{"remainingRequest": "E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\node_modules\\babel-loader\\lib\\index.js!E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\node_modules\\string-replace-loader\\index.js??ref--13-0!E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\src\\locales\\zh-TW.js", "dependencies": [{"path": "E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\src\\locales\\zh-TW.js", "mtime": 1754362736991}, {"path": "E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\node_modules\\string-replace-loader\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["E:/GitRepos/FusionNetProject/6849/frontend/application/sns/src/locales/zh-TW.js"], "names": ["locale", "lang", "common", "consumerError", "consumerSignatureError", "invalidUserOrPassword", "badGateway", "monitor20", "monitor21", "monitor22", "monitor23", "monitor24", "monitor25", "monitor26", "monitor27", "monitor28", "monitor29", "monitor30", "monitor31", "sensorTypeTemperature", "sensorTypeHeartRate", "sensorTypeBloodOxygen", "sensorTypeBloodPressure", "sensorTypeHelp", "sensorTypeDiaper", "sensorTypePosition", "sensorTypeMMWave", "confirmDeleteSelection", "confirmDelete", "appMainTitle", "systemTitle", "indoorRealTimePositioningSystem", "traditionalChinese", "simplifiedChinese", "english", "introduction", "people", "piece", "page", "import", "export", "exportExcel", "newlyIncreased", "search", "delete", "removePair", "list", "confirm", "cancel", "homePage", "reset", "edit", "upload", "storage", "total", "pen", "date", "time", "equipment", "noDataText", "inputKeyword", "keywordEvent", "pleaseSelect", "pleaseSearch", "chooseStartTime", "chooseEndTime", "exportFormatForExcel", "seeMoreEvents", "return", "all", "marked", "notMarked", "cancelled", "hasBeenIdentified", "deleteConfirm", "deleteItemConfirm", "prompt", "selectEventToDelete", "notUsePermission", "deleteSuccessful", "removePairSuccessful", "addSuccessful", "storageSuccessful", "exportSuccessful", "importSuccessful", "searchSuccessful", "modifySuccessful", "syncSuccessful", "exportFail", "searchFail", "backgroundManagement", "personalData", "personal", "operation", "changeThePassword", "logout", "pleaseEnterOriginalPassword", "pleaseEnterNewPassword", "pleaseEnterNewPasswordAgain", "passwordError", "tooManyResourcesError", "passwordErrorPrompt", "eventRecordErrorPrompt", "samePasswordPrompt", "twoNewPasswordsAreNotConsistent", "planeNoUploadCoordinatesError", "noInformationOnFloor", "selectMap", "floorPlan", "selectDate", "successful", "fillInEventRecord", "warning", "error", "invalidFormat", "contactSystemAdministrator", "modificationSuccessfulLoginAgain", "loginPeriodExpires", "selectedEventDoesNot", "networkProblemPleaseRefreshPage", "networkProblem", "passwordUnavailableError", "accessCodeUnavailableError", "storageUnavailableError", "accountAlreadyExists", "dataAlReadyExists", "deviceObjectExistsError", "objectDeviceExistsError", "accountNotFoundError", "resourceNotFoundError", "planeNotFoundError", "operationIsNotAllowed", "selectAtLeastOneAttribute", "itNotExist", "reorganizeThePage", "selectedEventState", "fillInIncidentRecord", "invalidRequestError", "badPasswordError", "badEmailError", "badPhoneError", "invalidAccessTaskError", "uneventful", "removeEvent", "event", "trajectory", "locateObject", "templateHelp", "objectsDisplayed", "serialNumber", "name", "category", "role", "group", "currentPosition", "latestPositioningTime", "modifiesAt", "byTheTime", "plane", "eventClassification", "sponsorName", "initiator", "eventLog", "eventState", "viewTheDayTrack", "viewCurrentLocation", "monitoring", "fenceMonitoring", "mmWaveMonitoring", "monitor01", "monitor02", "monitor03", "monitor04", "monitor05", "managePermissions", "accountManagement", "roleManagement", "firmwareUpdate", "positioningSettings", "planePosition", "baseStation", "anchor", "guard", "no", "dataManagement", "objectFeatures", "objectData", "deviceInformation", "eventDefinition", "guardSetting", "otaUpdate", "licenseManagement", "historyReport", "temporarilyNoData", "cameraSetting", "logRecording", "inventory", "systemManagement", "systemConfig", "systemSWVersion", "mmWave", "fence", "helpEvent", "enterTheNotice", "leaveTheNotice", "theNumberOfControl", "lowBatteryWarning", "stationAbnormalWarning", "stayTimeout", "regularRound", "leaveBed", "abnormal<PERSON>uard", "sensorDataDriven", "fallDetection", "stayTimeoutMMWave", "leaveBedMMWave", "getUp", "abnormalBreath", "wetUrine", "help", "untreated", "inTheProcessing", "hasLift", "personnel", "map", "eventName", "objectsName", "order", "personnelDistribution", "equipmentDistribution", "area", "subRegion", "factory", "building", "floor", "areaName", "factoryName", "buildingName", "floorName", "subRegionName", "geoCluster", "coordinate", "hasAttachment", "offline", "planeName", "originalCoordinates", "currentCoordinates", "hasChange", "mapHint", "archives", "pairingDevice", "inputCode", "inputName", "inputCodeError", "selectObject", "selectDeleteObject", "selectGroup", "onTheGround", "underground", "okToDoThis", "okToDeleteSelection", "whetherToReset", "selectTheObject", "noData", "limitDeleteRecords", "planeNotFound", "selectPlane", "chooseType", "uploadTime", "yes", "nay", "type", "document", "enabled", "notEnabled", "selectedItems", "planeDataExists", "notAllowedDelete", "badConfiguration", "fullScreen", "exitFullScreen", "noShortcuts", "login", "account", "password", "resetPassword", "accountRequiredVerification", "passwordRequiredVerification", "passwordPatternVerification", "pleaseResetAccountPassword", "inputNewPassword", "inputNewPasswordAgain", "send", "newPasswdRequiredVerification", "newPasswdRequiredVerificationAgain", "newPasswdPatternVerification", "newPasswd<PERSON><PERSON><PERSON>", "resetPasswordSuccessful", "inputEmail", "inputVcode", "emailRequiredVerification", "emailTypeVerification", "vcodeRequiredVerification", "vcodeError", "sendEmailSuccessful", "accountNotFoundPrompt", "email", "phone", "userEmailExistsVerification", "nameRequiredVerification", "nameTypeVerification", "phoneRequiredVerification", "phonePatternVerification", "userRoleRequiredVerification", "dashBoard", "device", "totalCount", "normal", "lowBattery", "lowBatteryEvent", "stationAbnormal", "more", "guardAbnormal", "managementShortcut", "course", "deviceName", "pairObject", "battery", "latestBatteryTime", "lowBatteryNotice", "stationName", "locationRegion", "connectionState", "startsAt", "abnormalNotice", "<PERSON><PERSON><PERSON>", "operationDate", "ipPosition", "operationRecord", "noPlaneInformation", "noCoordinateInformation", "regionNotUploadPlaneMap", "monitorManagerment", "loginSystem", "logoutSystem", "edit<PERSON><PERSON>", "applicationIntegration", "clickOnClosed", "clickOnA", "dragLeftRightToResizeTheWindow", "DeEventOperation", "DetermineUnselectedEvent", "searchMonitoringConditions", "mandatoryFieldArea", "unableToDisplayTheMap", "storeFailure", "storeSuccessful", "noPlaneRelatedConsultation", "noCoordinateConsultation", "noCoordinateOrPlaneRelatedConsultation", "anUnexaminedObject", "selectProjectToTerminated", "validatePlaneErrorInfo", "noFlatMapData", "keyword", "download", "recordPreview", "fileId", "cameraCode", "planeCode", "filename", "permission", "deleteAccountPrompt", "addSystemAccount", "inputAccount", "inputPassword", "inputPhone", "selectRole", "accountTypeVerification", "userAccountExistsVerification", "accountName", "instantEventInspection", "localizingObjectInspection", "noInstantEvent", "immediateEvent", "enter", "leave", "numberControl", "abnormalStation", "selectMonitoringConditionPrompt", "pleaseSelectCategory", "pleaseSelectRole", "pleaseSelectGroup", "pleaseSelectRegion", "categoryRequiredVerification", "roleRequiredVerification", "groupRequiredVerification", "regionRequiredVerification", "addLocationObject", "deleteMonitoringConditionPrompt", "selectTypeRoleGroup", "roleCode", "<PERSON><PERSON><PERSON>", "systemRoleCode", "systemRoleName", "selectDeleteCodes", "userRoleNotFound", "userConflicts", "view", "addRole", "enterRoleCode", "enterRoleName", "permissionSetting", "codeRuleValidate", "nameRuleValidate", "permissionRuleValidate", "moduleNotFound", "userRoleCodeExists", "categoryCode", "categoryName", "icon", "groupCode", "groupName", "pleaseSelectDeleteCategory", "pleaseSelectDeleteRole", "pleaseSelectDeleteGroup", "objectTypeNotFoundVerification", "objectRoleNotFoundVerification", "objectGroupNotFoundVerification", "objectConflictsVerification", "eventConflictsVerification", "monitorConflicts", "addLocationAttribute", "pleaseSelectIcon", "pleaseSelectLocationAttribute", "locationAttributeRequiredVerification", "codeRequiredVerification", "codeTypeVerification", "iconRequiredVerification", "objectCodeExistsVerification", "code", "excelExampleDownload", "pleaseSelectUploadImportFiles", "uploadFiles", "fileFormatForxls", "systemReadsPens", "uploadFileForExcel", "pleaseSelectUploadFiles", "pleaseSelectALocationAttribute", "correctInformation", "errorInformation", "errorCode", "locationAttributeName", "stationConfiguration", "sid", "IP", "systemVersion", "appVersion", "isAlive", "correctImportedError", "planeLayerAtLeastError", "unableCoordinatePositioning", "selectCorrespondingClass", "XCoordinate", "YCoordinate", "flatArea", "inputSid", "inputSidCode", "lastConnectionTime", "fieldInformation", "selectTheArea", "selectTheUploadedFile", "addBaseStation", "baseStationIncorrectDuplicatedError", "sidCodeLengthError", "nameLengthError", "addCamera", "prioritySeq", "cameraId", "cameraNo", "cameraName", "fieldFilterToolTip", "deviceMAC", "locationObject", "locationTime", "residualBattery", "pm25", "tvoc", "temperature", "humidity", "pm25GetTime", "tvocGetTime", "temperatureGetTime", "humidityGetTime", "heartRate", "latestHeartRateTime", "sbp", "latestSbpTime", "dbp", "latestDbpTime", "softwareVersion", "deviceNotFoundVerification", "pleaseSelectDeleteDevice", "pleaseSelectRemovePairItem", "pleaseConfirmOperationCorrectness", "addDeviceInformation", "inputDeviceMAC", "inputDeviceName", "selectPairObject", "pidRequiredVerification", "pidTypeVerification", "objectNotFoundVerification", "objectDeviceExistsVerification", "devicePidExistsVerification", "currentPairDevice", "anchorConfiguration", "anchorMac", "anchorName", "inputAnchorMac", "inputPIDCode", "addAnchor", "anchorMacIncorrectDuplicatedError", "anchorMacLengthError", "guardConfiguration", "region", "detectMode", "enableOrClose", "synchronousState", "enable", "close", "synchronizing", "synchronized", "dataSynchronizationUpdate", "guardNotFoundVerification", "pleaseSelectDeleteGuard", "pleaseSelectPlaneMap", "pleaseSelectCorrespondingClass", "addG<PERSON>", "basicInformation", "pleaseSelectPairObject", "pleaseSelectDetectMode", "bodyTemp", "bodySize", "bedWidth", "<PERSON><PERSON><PERSON><PERSON>", "ceilingHeight", "stopAlarmTime", "bathroomLength", "bathroomWidth", "intervalTime", "ipSetting", "rawDataCollectionSetting", "ipCameraEnableOrClose", "ipCameraCircleFrequency", "logUploadFrequency", "logPackingSize", "bodyTempRequiredVerification", "bodySizeRequiredVerification", "bedWidthRequiredVerification", "bedLengthRequiredVerification", "ceilingHeightRequiredVerification", "stopAlarmTimeRequiredVerification", "bathroomLengthRequiredVerification", "bathroomWidthRequiredVerification", "intervalTimeRequiredVerification", "bodyTempTypeVerification", "bodySizeTypeVerification", "bedWidthTypeVerification", "bedLengthTypeVerification", "ceilingHeightTypeVerification", "stopAlarmTimeTypeVerification", "bathroomLengthTypeVerification", "bathroomWidthTypeVerification", "intervalTimeTypeVerification", "objectGuardExistsVerification", "guardCodeExistsVerification", "columnMustNumber", "columnMustPositiveInteger", "leastSocketServerIp", "enableFusionGuardMustItem", "xCoordinate", "yCoordinate", "basicInformationMustCompleteForEnableGuard", "currentPairObject", "planeNotFoundVerification", "needPortField", "objectNotFound", "objectTypeNotFound", "objectRoleNotFound", "objectGroupNotFound", "deviceNotFound", "guardNotFound", "deviceObjectExists", "guardObjectExists", "sitePlanConfig", "addSitePlanConfig", "updateFileMap", "updateOneFileMap", "flatPositionError", "flatFactoryError", "flatBuildingError", "flatFloorError", "flatRegionError", "flatCode", "flatName", "flatCodeError", "flatNameError", "selectTheDeletedObject", "delSelectItemsError", "stationConflictsError", "eventConflictsError", "planeConflictsError", "anchorConflicts", "positioningPlanePosition", "selectCorrespondingLevelError", "readjustStationAnchorPoint", "dragNoticeError", "savePromptingError", "deletePlanesChildPositionError", "isPoptip", "establishOrder", "establishAtLeastOneFactoryAreaFirst", "establishAtLeastOneBuildingAreaFirst", "establishAtLeastOneFloorAreaFirst", "establishAtLeastOneSubRegionAreaFirst", "selectAndFillInFloor", "floorInputCaveat", "fieldInformationCaveat", "zoomRatioMustTheSame", "validatePlaneCodeError", "planeCodeTypeError", "planeCodeTypeLengthError", "selectFool", "validateFloorError", "planeNameError", "planeNameLengthError", "addPlaneError", "itemHasNotSetCoordinate", "floorPlanPreview", "singleEvent", "planeEvent", "eventCode", "eventTemplate", "addEventDefinition", "selectTemplate", "enterEventCode", "enterEventName", "enterEventNameNoStar", "selectEventCategory", "selectEventCategoryNoStar", "enterValue", "selectStartTime", "selectEndTime", "selectSponsorType", "selectSponsorRole", "selectSponsorGroup", "selectParticipantType", "selectParticipantRole", "selectParticipantGroup", "selectNotifierType", "selectNotifierRole", "selectNotifierGroup", "selectNotifierAccount", "enterNotifierMsg", "selectDeleteEvent", "threshold", "below", "over", "stayOver", "batteryPercentage", "<PERSON><PERSON><PERSON><PERSON>", "min<PERSON><PERSON><PERSON>", "intervals", "between", "sensorDataDrivenData", "it", "ite", "gt", "gte", "dataDrivenRuleSource", "dataDrivenRuleComp", "enterSource", "enterComp", "dataDrivenRuleRepeat", "dataDrivenRuleCount", "dataDrivenRuleDuration", "dataDrivenRuleDurationSec", "validatePlaneCode", "validateCategory", "validateSponsor", "validateParticipant", "enterEventCodeValidate", "eventCodeValidate", "enterEventNameValidate", "eventNameValidate", "templateValidate", "enterThresholdValidate", "thresholdValidatePattern", "thresholdValidateMax", "msgValidate", "badInterval", "sponsorObjectTypeNotFound", "sponsorObjectRoleNotFound", "sponsorObjectGroupNotFound", "participantObjectTypeNotFound", "participantObjectRoleNotFound", "participantObjectGroupNotFound", "thresholdNotFound", "notifierObjectTypeNotFound", "notifierObjectRoleNotFound", "notifierObjectGroupNotFound", "notifierUserNotFound", "eventCodeExists", "warningCondition", "controlTime", "startTime", "entTime", "eventCategory", "participantType", "participantRole", "participantGroup", "sponsorType", "sponsorRole", "sponsorGroup", "notifierType", "notifierRole", "notifierGroup", "notifierUser", "notifierMsg", "addControlTime", "planeSetting", "sponsorTypeSetting", "sponsorRoleSetting", "sponsorGroupSetting", "participantTypeSetting", "participantRoleSetting", "participantGroupSetting", "notifierTypeSetting", "notifierRoleSetting", "notifierGroupSetting", "notifierUserSetting", "thresholdValidate", "searchEventTemplate", "searchSelectTemplate", "eventNotFound", "taskConflicts", "uploadRecord", "scheduleList", "fileName", "uploadOta", "station", "stationApk", "wristband", "button", "tag", "fromVersion", "toVersion", "description", "activeName", "nordicVersion", "bootloaderVersion", "stVersion", "nordicFileName", "bootloaderFileName", "stFileName", "uploadFile", "fileNameFormatReference", "uploadNordicFile", "uploadbootLoaderFile", "uploadStFile", "inputDescription", "setUpdateScheduling", "queryUpdateResults", "back", "completedQuantity", "unfinishedQuantity", "updateTime", "updateObject", "originalVersion", "newVersion", "currentVersion", "toUpdateVersion", "errorMessage", "schedulingName", "targetPlane", "count", "actionSuccess", "actionUnsuccess", "schedulingEnable", "updateType", "updateRole", "updateGroup", "noEnable", "pleaseSelectDeleteStation", "pleaseSelectDeleteStationApk", "pleaseSelectDeleteAnchor", "pleaseSelectDeleteWristband", "pleaseSelectDeleteButton", "pleaseSelectDeleteTag", "otaScheduleNotFoundVerification", "updateCycle", "sunday", "monday", "tuesday", "wednesday", "thursday", "friday", "saturday", "setUpdateSchedulingSuccessful", "inputSchedulingName", "inputSchedulingCode", "pleaseSelectStartsAt", "pleaseSelectEndsAt", "pleaseSelectUpdateType", "pleaseSelectUpdateRole", "pleaseSelectUpdateGroup", "pleaseSelectTargetPlane", "pleaseSelectNotifierUserAccounts", "targetPlaneRequiredVerification", "startsAtRequiredVerification", "typeRequiredVerification", "selectTargetPlane<PERSON>ength", "targetPlaneNotFoundVerification", "updateTypeRoleGroupVerification", "targetObjectTypeNotFoundVerification", "targetObjectRoleNotFoundVerification", "targetObjectGroupNotFoundVerification", "notifierUserNotFoundVerification", "otaScheduleCodeExistsVerification", "recursiveVersionFound", "badFilename", "badCycleVerification", "updateDate", "pleaseInputSchedulingName", "queryUpdateResultsSuccessful", "updateTimeRequiredVerification", "uploadSuccess", "licenseRequest", "enterCustomerId", "enterSerialNum", "selectService", "useRegCode", "enterRegCode", "enterCustomerIdHint", "enterSerialNumHint", "selectServiceHint", "customerIdRuleValidate", "serialNumRuleValidate", "copy", "serviceNotFound", "customerNotFound", "enterRegCodeHint", "regCodeRuleValidate", "regSuccessful", "badRegistrationCode", "customerName", "totalService", "validStation", "validDevice", "expiredTime", "status", "id", "serviceSerial", "serviceName", "serviceIp", "licenseTime", "validServiceCount", "invalidServiceCount", "slash", "copyRequestCode", "noError", "licenseExpired", "licenseExpiredSoon", "action", "eventPlane", "sponsorObjects", "startAndEndTime", "treating", "treated", "note", "exportEvent", "camera", "editCamera", "cameraIp", "streamUrl", "cameraVideoPath", "taskVideoPath", "enterCameraCode", "enterCameraName", "enterCameraIp", "enterStreamUrl", "enterCameraVideoPath", "enterTaskVideoPath", "cameraIpPosition", "cameraConnStatus", "addLogRecording", "chooseDevice", "choosePlane", "chooseAction", "chooseValidTime", "oneHour", "threeHour", "twelfthHour", "oneDay", "threeDay", "fiveDay", "oneWeek", "enterDescription", "devicesName", "devicesID", "devicesPID", "reportUrl", "logUrl", "message", "finishesAt", "expiresAt", "waiting", "logCollecting", "logCollected", "reportCreating", "logFileCreating", "finished", "failed", "canceled", "enterNameValidate", "eventNameValidateLength", "logNotFound", "waitForInitialization", "requestUnavailable", "isExport", "logNo", "readyRecord", "recording", "readyExport", "exportingLog", "exportingFile", "recorded", "recordFail", "stopRecord", "pleaseEnter", "pleaseUpload", "devicesPosition", "latestPositionTime", "hasCounted", "reCounted", "refresh", "latestCountingTime", "addIvnentory", "addTask", "index", "task", "iconCode", "enterNumber", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "objectCode", "enterPurchaseDate", "uploadPhoto", "serial", "inputValidateLength20", "inputValidateLength32", "inputValidateLength64", "keeperUserNotFound", "managerUserNotFound", "iconNotFound", "categoryNotFound", "objectInventoryExists", "inventoryCodeExists", "detail", "taskDetail", "createsAt", "eolAt", "active", "repairing", "calibrating", "servicing", "eol", "objectName", "position", "positionXY", "images", "taskNumber", "taskDescription", "ownerUser", "countedCount", "uncountedCount", "inProgress", "key", "value", "defaultValue", "min", "max", "max<PERSON><PERSON><PERSON>", "unit", "schemaGroupAPI", "schemaGroupLogging", "schemaGroupHelp", "schemaGroupUser", "schemaGroupEnvironment", "schemaGroupPositioning", "schemaGroupConnection", "schemaGroupWeb", "descReserveJournalDuration", "descStationOfflinePeriod", "descDeviceOfflinePeriod", "descDeviceOtaRssiThreshold", "descGatewayOfflinePeriod", "descSmtpEnable", "descSmtpHost", "descSmtpPort", "descSmtpUsername", "descSmtpPassword", "descHelpTaskDelayPeriod", "descHelpTurnOffDeviceTimer", "descHelpTurnOffDeviceRetryCount", "descHelpTaskMinRssiThreshold", "descUserRegisterCodeExpirePeriod", "descUserRegisterCodeLength", "descApiAccessTokenExpirePeriod", "descApiRefreshTokenExpirePeriod", "descApiDefaultSearchActive", "descApiDefaultMaxSize", "descReportCachePeriod", "descTrackingKalmanFilterEnable", "descTrackingKalmanFilterMeterRangePerSecond", "descTrackingKalmanFilterAccuracy", "descLocationKalmanFilterEnable", "descLocationKalmanProcessNoise", "descLocationKalmanMeasurementNoise", "descPositionPersistencePeriod", "descPositionHistoryPersistencePeriod", "descPositioningAlgorithmV2", "descPositioningPeroid", "descPositionMinRssiThreshold", "descTrackingRegionTotalCount", "descTrackingRegionMatchCount", "descMonitorRefreshSecond", "descStayTimeoutDefaultInterval", "descHistoryMatchCount", "ipAddr", "ipError", "version", "checkAt", "apps", "autoTreated", "posParameterCode", "deviceType", "displayStations", "monitor", "object", "configSetting", "selectDelete", "add", "cancelAdd", "cancelEdit", "saveAdd", "saveEdit", "picEdit", "p<PERSON><PERSON><PERSON><PERSON>", "picUpload", "picConfirm", "planeEdit", "planeSet", "picBackgroundUpload", "iconPicEdit", "iconPicUpload", "objectDeviceConfirm", "record", "recordPlaceholder", "eventAlarm", "eventAlarmWindow", "eventStatus", "eventClean", "eventCleanConfirm", "eventStartTime", "emergencyEvent", "deviceMac", "occupy", "selectDeleteError", "abnormalDevice", "second", "deviceConnectionAbnormal", "deviceConnectionAbnormalLatestEvent", "positionX", "positionY", "m20", "selectMonitorPlane", "selectMonitorObject", "m21", "eFence", "confirmEdit", "fall", "positionStation", "allPositionStation", "enterAndExit", "exit", "m01", "addSpecialStatus", "<PERSON><PERSON><PERSON>", "timeoutSetting", "specialStatus", "baby", "lossSignal", "wetUrineTimeout", "noPatient", "hasPatient", "babyCare", "removeSpecialStatus", "removeSpecialStatusConfirm", "babyMove", "unknownStation", "specialEvent", "babyRegion", "diaperNormal", "babyTagLowBattery", "motherTagLowBattery", "monitorOutside", "emptyBed", "enterSetting", "entering", "eventId", "eventNo", "alertCondition", "less", "minsSignal", "addMon", "objectAttr", "addBaby", "resetDiaperComfortable", "mon", "baby2", "propertySetting", "objectSetting", "stationSetting", "stationSelect", "monRoom", "babyRoom", "controlArea", "warningArea", "normalArea", "objectName2", "objectType", "addBed", "bed", "toilet", "addRegion", "regionType", "showRegion", "addSubRegion", "m03", "overtemperature", "wristbandLowBattery", "bodyOvertemperature", "negativePressureIsolationCenter", "nowTemperature", "nowHeartRate", "nowBloodOxygen", "nowSbp", "nowDbp", "temperaturePicAndNumerical", "temperatureColor", "remind", "bodyTemperatureWarm", "bodyTemperatureDetect", "heartRateDetect", "bloodOxygenDetect", "sbpDetect", "dbpDetect", "greater", "greaterThanOrEqual", "lessThanOrEqual", "degree", "timesPerMin", "negativePressureIsolationRoom", "editConfirm", "bodyPhysicalDataSetting", "continuousMonitoringDuration", "nonContinuousMonitoringAbnormalCritical", "continuousMonitoring", "nonContinuousMonitoring", "times", "alert", "abnormal", "critical", "bloodOxygen", "bloodPressure", "bodyTemperature", "backgroundColor", "deviceLowBattery", "lowTemperature", "overTemperature", "lowHeartRate", "overHeartRate", "lowBloodOxygen", "overBloodPressure", "lowBloodPressure", "overBloodPressureSbp", "lowBloodPressureSbp", "overBloodPressureDbp", "lowBloodPressureDbp", "sensorType", "current", "history", "lying", "sit", "natureCall", "now", "breathe", "breathe5MinsRecord", "bpm", "mins", "correct", "notCorrect", "empty", "breathDetect", "stopDetect", "eventCondition", "connectStatus", "connecting", "nowObject", "allObject", "beforeIndex", "afterIndex", "searchCondition", "eventEdit", "sponsorObjectType", "preFall", "m02", "stationID", "employeeNo", "employeeName", "phoneNo", "modifyTime", "enterControlArea", "notice", "install", "remove", "leaveControlArea", "enableAirwatch", "m05", "m27", "temperatureWarm", "temperatureDetect", "overHumidity", "lowHumidity", "humidityDetect", "nowHumidity", "airPM25", "nowAirPM25", "airPM25Detect", "overAirPM25", "lowAirPM25", "airAQI", "nowAQI", "airAQIDetect", "overAirAQI", "lowAirAQI", "m28", "m29", "wetUrineEvent", "cardTemperature", "card<PERSON><PERSON><PERSON>", "cardHelp", "diaperWarning", "diaper<PERSON><PERSON><PERSON>", "eventHelp", "m30", "cardMMWaveBreathe", "eventMMWaveFallDetection", "eventMMWaveStayTimeout", "eventMMWaveLeaveBed", "eventMMWaveGetUp", "bathroomFall", "room", "bathroom", "<PERSON><PERSON><PERSON>", "noMan", "m31", "location", "lossMonitor", "wetUrineMonitor"], "mappings": "AAAA;AACA,OAAO,IAAMA,MAAM,GAAG;AACpBC,EAAAA,IAAI,EAAE;AACJ;AACAC,IAAAA,MAAM,EAAE;AACL;AACAC,MAAAA,aAAa,EAAE,oBAFV;AAGLC,MAAAA,sBAAsB,EAAE,kBAHnB;AAILC,MAAAA,qBAAqB,EAAE,SAJlB;AAKLC,MAAAA,UAAU,EAAE,eALP;AAQLC,MAAAA,SAAS,EAAE,MARN;AASLC,MAAAA,SAAS,EAAE,MATN;AAULC,MAAAA,SAAS,EAAE,MAVN;AAWLC,MAAAA,SAAS,EAAE,MAXN;AAYLC,MAAAA,SAAS,EAAE,MAZN;AAYa;AAClBC,MAAAA,SAAS,EAAE,MAbN;AAcLC,MAAAA,SAAS,EAAE,MAdN;AAeLC,MAAAA,SAAS,EAAE,QAfN;AAee;AACpBC,MAAAA,SAAS,EAAE,MAhBN;AAiBLC,MAAAA,SAAS,EAAE,MAjBN;AAkBLC,MAAAA,SAAS,EAAE,MAlBN;AAmBLC,MAAAA,SAAS,EAAE,MAnBN;AAsBLC,MAAAA,qBAAqB,EAAC,IAtBjB;AAuBLC,MAAAA,mBAAmB,EAAC,IAvBf;AAwBLC,MAAAA,qBAAqB,EAAC,IAxBjB;AAyBLC,MAAAA,uBAAuB,EAAC,IAzBnB;AA0BLC,MAAAA,cAAc,EAAC,IA1BV;AA2BLC,MAAAA,gBAAgB,EAAC,IA3BZ;AA4BLC,MAAAA,kBAAkB,EAAC,IA5Bd;AA8BLC,MAAAA,gBAAgB,EAAC,KA9BZ;AAgCL;AACAC,MAAAA,sBAAsB,EAAC,SAjClB;AAkCLC,MAAAA,aAAa,EAAC,MAlCT;AAmCL;AACDC,MAAAA,YAAY,EAAC,SApCP;AAqCNC,MAAAA,WAAW,EAAC,YArCN;AAsCNC,MAAAA,+BAA+B,EAAE,YAtC3B;AAuCNC,MAAAA,kBAAkB,EAAE,MAvCd;AAwCNC,MAAAA,iBAAiB,EAAE,MAxCb;AAyCNC,MAAAA,OAAO,EAAE,IAzCH;AA0CNC,MAAAA,YAAY,EAAE,IA1CR;AA2CNC,MAAAA,MAAM,EAAE,GA3CF;AA4CNC,MAAAA,KAAK,EAAE,GA5CD;AA6CN;AACAC,MAAAA,IAAI,EAAE,GA9CA;AA+CNC,MAAAA,MAAM,EAAE,IA/CF;AAgDNC,MAAAA,MAAM,EAAE,IAhDF;AAiDNC,MAAAA,WAAW,EAAE,YAjDP;AAkDNC,MAAAA,cAAc,EAAE,IAlDV;AAmDNC,MAAAA,MAAM,EAAE,IAnDF;AAoDNC,MAAAA,MAAM,EAAE,IApDF;AAqDNC,MAAAA,UAAU,EAAE,MArDN;AAsDNC,MAAAA,IAAI,EAAE,IAtDA;AAuDNC,MAAAA,OAAO,EAAE,IAvDH;AAwDNC,MAAAA,MAAM,EAAE,IAxDF;AAyDNC,MAAAA,QAAQ,EAAE,IAzDJ;AA0DNC,MAAAA,KAAK,EAAE,IA1DD;AA2DNC,MAAAA,IAAI,EAAE,IA3DA;AA4DNC,MAAAA,MAAM,EAAE,IA5DF;AA6DNC,MAAAA,OAAO,EAAE,IA7DH;AA8DNC,MAAAA,KAAK,EAAE,GA9DD;AA+DNC,MAAAA,GAAG,EAAE,GA/DC;AAgENC,MAAAA,IAAI,EAAE,IAhEA;AAiENC,MAAAA,IAAI,EAAE,IAjEA;AAkENC,MAAAA,SAAS,EAAE,IAlEL;AAmENC,MAAAA,UAAU,EAAE,MAnEN;AAoENC,MAAAA,YAAY,EAAE,QApER;AAqENC,MAAAA,YAAY,EAAE,SArER;AAsENC,MAAAA,YAAY,EAAE,KAtER;AAuENC,MAAAA,YAAY,EAAE,OAvER;AAwENC,MAAAA,eAAe,EAAE,SAxEX;AAyENC,MAAAA,aAAa,EAAE,SAzET;AA0ENC,MAAAA,oBAAoB,EAAE,YA1EhB;AA2ENC,MAAAA,aAAa,EAAE,OA3ET;AA4ENC,MAAAA,MAAM,EAAE,IA5EF;AA6ENC,MAAAA,GAAG,EAAE,IA7EC;AA8ENC,MAAAA,MAAM,EAAE,KA9EF;AA+ENC,MAAAA,SAAS,EAAE,KA/EL;AAgFNC,MAAAA,SAAS,EAAE,KAhFL;AAiFNC,MAAAA,iBAAiB,EAAE,KAjFb;AAkFNC,MAAAA,aAAa,EAAE,MAlFT;AAmFNC,MAAAA,iBAAiB,EAAE,YAnFb;AAoFN;AACAC,MAAAA,MAAM,EAAE,IArFF;AAsFNC,MAAAA,mBAAmB,EAAE,WAtFf;AAuFNC,MAAAA,gBAAgB,EAAE,gBAvFZ;AAwFNC,MAAAA,gBAAgB,EAAE,OAxFZ;AAyFNC,MAAAA,oBAAoB,EAAE,SAzFhB;AA0FNC,MAAAA,aAAa,EAAE,OA1FT;AA2FNC,MAAAA,iBAAiB,EAAE,OA3Fb;AA4FNC,MAAAA,gBAAgB,EAAE,OA5FZ;AA6FNC,MAAAA,gBAAgB,EAAE,OA7FZ;AA8FNC,MAAAA,gBAAgB,EAAE,OA9FZ;AA+FNC,MAAAA,gBAAgB,EAAE,OA/FZ;AAgGNC,MAAAA,cAAc,EAAE,OAhGV;AAiGNC,MAAAA,UAAU,EAAE,WAjGN;AAkGNC,MAAAA,UAAU,EAAE,WAlGN;AAmGN;AACAC,MAAAA,oBAAoB,EAAE,MApGhB;AAqGNC,MAAAA,YAAY,EAAE,OArGR;AAsGNC,MAAAA,QAAQ,EAAE,IAtGJ;AAuGNC,MAAAA,SAAS,EAAE,IAvGL;AAwGNC,MAAAA,iBAAiB,EAAE,MAxGb;AAyGNC,MAAAA,MAAM,EAAE,IAzGF;AA0GNC,MAAAA,2BAA2B,EAAE,QA1GvB;AA2GNC,MAAAA,sBAAsB,EAAE,QA3GlB;AA4GNC,MAAAA,2BAA2B,EAAE,UA5GvB;AA6GNC,MAAAA,aAAa,EAAE,gBA7GT;AA8GNC,MAAAA,qBAAqB,EAAE,sBA9GjB;AA+GNC,MAAAA,mBAAmB,EAAE,2BA/Gf;AAgHNC,MAAAA,sBAAsB,EAAE,oBAhHlB;AAiHNC,MAAAA,kBAAkB,EAAE,kBAjHd;AAkHNC,MAAAA,+BAA+B,EAAE,cAlH3B;AAmHNC,MAAAA,6BAA6B,EAAE,qBAnHzB;AAoHNC,MAAAA,oBAAoB,EAAE,eApHhB;AAqHNC,MAAAA,SAAS,EAAE,QArHL;AAsHNC,MAAAA,SAAS,EAAE,KAtHL;AAuHNC,MAAAA,UAAU,EAAE,OAvHN;AAwHNC,MAAAA,UAAU,EAAE,IAxHN;AAyHNC,MAAAA,iBAAiB,EAAE,SAzHb;AA0HNC,MAAAA,OAAO,EAAE,IA1HH;AA2HNC,MAAAA,KAAK,EAAE,IA3HD;AA4HNC,MAAAA,aAAa,EAAE,OA5HT;AA6HNC,MAAAA,0BAA0B,EAAE,iBA7HtB;AA8HNC,MAAAA,gCAAgC,EAAE,aA9H5B;AA+HNC,MAAAA,kBAAkB,EAAE,iBA/Hd;AAgINC,MAAAA,oBAAoB,EAAE,mBAhIhB;AAiINC,MAAAA,+BAA+B,EAAE,eAjI3B;AAkINC,MAAAA,cAAc,EAAE,sBAlIV;AAmINC,MAAAA,wBAAwB,EAAE,8BAnIpB;AAoINC,MAAAA,0BAA0B,EAAE,+BApItB;AAqINC,MAAAA,uBAAuB,EAAE,yBArInB;AAsINC,MAAAA,oBAAoB,EAAE,QAtIhB;AAuINC,MAAAA,iBAAiB,EAAE,QAvIb;AAwINC,MAAAA,uBAAuB,EAAE,uBAxInB;AAyINC,MAAAA,uBAAuB,EAAE,uBAzInB;AA0INC,MAAAA,oBAAoB,EAAE,SA1IhB;AA2INC,MAAAA,qBAAqB,EAAE,OA3IjB;AA4INC,MAAAA,kBAAkB,EAAE,mBA5Id;AA6INC,MAAAA,qBAAqB,EAAE,mBA7IjB;AA8INC,MAAAA,yBAAyB,EAAE,YA9IrB;AA+INC,MAAAA,UAAU,EAAE,WA/IN;AAgJNC,MAAAA,iBAAiB,EAAE,UAhJb;AAiJNC,MAAAA,kBAAkB,EAAE,UAjJd;AAkJNC,MAAAA,oBAAoB,EAAE,SAlJhB;AAmJNC,MAAAA,mBAAmB,EAAE,eAnJf;AAoJNC,MAAAA,gBAAgB,EAAE,8BApJZ;AAqJNC,MAAAA,aAAa,EAAE,iBArJT;AAsJNC,MAAAA,aAAa,EAAE,SAtJT;AAuJNC,MAAAA,sBAAsB,EAAE,qBAvJlB;AAwJNC,MAAAA,UAAU,EAAE,SAxJN;AAyJNC,MAAAA,WAAW,EAAE,MAzJP;AA0JNC,MAAAA,KAAK,EAAE,IA1JD;AA2JNC,MAAAA,UAAU,EAAE,IA3JN;AA4JNC,MAAAA,YAAY,EAAE,MA5JR;AA6JNC,MAAAA,YAAY,EAAE,MA7JR;AA8JN;AACAC,MAAAA,gBAAgB,EAAE,MA/JZ;AAgKNC,MAAAA,YAAY,EAAE,IAhKR;AAiKNC,MAAAA,IAAI,EAAE,IAjKA;AAkKNC,MAAAA,QAAQ,EAAE,IAlKJ;AAmKNC,MAAAA,IAAI,EAAE,IAnKA;AAoKNC,MAAAA,KAAK,EAAE,IApKD;AAqKNC,MAAAA,eAAe,EAAE,MArKX;AAsKNC,MAAAA,qBAAqB,EAAE,QAtKjB;AAuKNC,MAAAA,UAAU,EAAE,QAvKN;AAwKNC,MAAAA,SAAS,EAAE,MAxKL;AAyKNC,MAAAA,KAAK,EAAE,IAzKD;AA0KNC,MAAAA,mBAAmB,EAAE,IA1Kf;AA2KNC,MAAAA,WAAW,EAAE,OA3KP;AA4KNC,MAAAA,SAAS,EAAE,UA5KL;AA6KNC,MAAAA,QAAQ,EAAE,IA7KJ;AA8KNC,MAAAA,UAAU,EAAE,IA9KN;AA+KNC,MAAAA,eAAe,EAAE,QA/KX;AAgLNC,MAAAA,mBAAmB,EAAE,QAhLf;AAiLN;AACAC,MAAAA,UAAU,EAAE,MAlLN;AAmLNC,MAAAA,eAAe,EAAE,MAnLX;AAoLNC,MAAAA,gBAAgB,EAAE,MApLZ;AAqLNC,MAAAA,SAAS,EAAE,MArLL;AAsLNC,MAAAA,SAAS,EAAE,MAtLL;AAuLNC,MAAAA,SAAS,EAAE,MAvLL;AAwLNC,MAAAA,SAAS,EAAE,MAxLL;AAyLNC,MAAAA,SAAS,EAAE,MAzLL;AA2LNC,MAAAA,iBAAiB,EAAE,MA3Lb;AA4LNC,MAAAA,iBAAiB,EAAE,QA5Lb;AA6LNC,MAAAA,cAAc,EAAE,QA7LV;AA8LNC,MAAAA,cAAc,EAAE,MA9LV;AA+LNC,MAAAA,mBAAmB,EAAE,MA/Lf;AAgMNC,MAAAA,aAAa,EAAE,QAhMT;AAiMNC,MAAAA,WAAW,EAAE,QAjMP;AAkMNC,MAAAA,MAAM,EAAE,MAlMF;AAmMNC,MAAAA,KAAK,EAAE,OAnMD;AAoMNC,MAAAA,EAAE,EAAE,GApME;AAqMNC,MAAAA,cAAc,EAAE,MArMV;AAsMNC,MAAAA,cAAc,EAAE,QAtMV;AAuMNC,MAAAA,UAAU,EAAE,QAvMN;AAwMNC,MAAAA,iBAAiB,EAAE,QAxMb;AAyMNC,MAAAA,eAAe,EAAE,QAzMX;AA0MNC,MAAAA,YAAY,EAAE,SA1MR;AA2MNC,MAAAA,SAAS,EAAE,MA3ML;AA4MNC,MAAAA,iBAAiB,EAAE,MA5Mb;AA6MNC,MAAAA,aAAa,EAAE,MA7MT;AA8MNC,MAAAA,iBAAiB,EAAE,MA9Mb;AA+MNC,MAAAA,aAAa,EAAE,OA/MT;AAgNNC,MAAAA,YAAY,EAAE,QAhNR;AAiNNC,MAAAA,SAAS,EAAE,QAjNL;AAkNNC,MAAAA,gBAAgB,EAAE,MAlNZ;AAmNNC,MAAAA,YAAY,EAAE,QAnNR;AAoNNC,MAAAA,eAAe,EAAE,QApNX;AAqNNC,MAAAA,MAAM,EAAE,KArNF;AAsNNC,MAAAA,KAAK,EAAE,MAtND;AAuNN;AACAC,MAAAA,SAAS,EAAE,MAxNL;AAyNNC,MAAAA,cAAc,EAAE,MAzNV;AA0NNC,MAAAA,cAAc,EAAE,MA1NV;AA2NNC,MAAAA,kBAAkB,EAAE,MA3Nd;AA4NNC,MAAAA,iBAAiB,EAAE,OA5Nb;AA6NNC,MAAAA,sBAAsB,EAAE,QA7NlB;AA8NNC,MAAAA,WAAW,EAAE,QA9NP;AA+NNC,MAAAA,YAAY,EAAE,MA/NR;AAgONC,MAAAA,QAAQ,EAAE,MAhOJ;AAiONC,MAAAA,aAAa,EAAE,WAjOT;AAkONC,MAAAA,gBAAgB,EAAE,QAlOZ;AAmONC,MAAAA,aAAa,EAAE,UAnOT;AAoONC,MAAAA,iBAAiB,EAAE,YApOb;AAqONC,MAAAA,cAAc,EAAE,UArOV;AAsONC,MAAAA,KAAK,EAAE,UAtOD;AAuONC,MAAAA,cAAc,EAAE,YAvOV;AAwONC,MAAAA,QAAQ,EAAE,MAxOJ;AAyON;AACAC,MAAAA,IAAI,EAAE,IA1OA;AA2ONC,MAAAA,SAAS,EAAE,KA3OL;AA4ONC,MAAAA,eAAe,EAAE,KA5OX;AA6ONC,MAAAA,OAAO,EAAE,KA7OH;AA8ONC,MAAAA,SAAS,EAAE,IA9OL;AA+ONC,MAAAA,GAAG,EAAE,IA/OC;AAgPNC,MAAAA,SAAS,EAAE,MAhPL;AAiPNC,MAAAA,WAAW,EAAE,MAjPP;AAkPNC,MAAAA,KAAK,EAAE,IAlPD;AAmPNC,MAAAA,qBAAqB,EAAE,MAnPjB;AAoPNC,MAAAA,qBAAqB,EAAE,MApPjB;AAqPNC,MAAAA,IAAI,EAAE,IArPA;AAsPNC,MAAAA,SAAS,EAAE,KAtPL;AAuPNC,MAAAA,OAAO,EAAE,IAvPH;AAwPNC,MAAAA,QAAQ,EAAE,KAxPJ;AAyPNC,MAAAA,KAAK,EAAE,IAzPD;AA0PNC,MAAAA,QAAQ,EAAE,MA1PJ;AA2PNC,MAAAA,WAAW,EAAE,MA3PP;AA4PNC,MAAAA,YAAY,EAAE,OA5PR;AA6PNC,MAAAA,SAAS,EAAE,MA7PL;AA8PNC,MAAAA,aAAa,EAAE,OA9PT;AA+PNC,MAAAA,UAAU,EAAE,MA/PN;AA+Pc;AACpBC,MAAAA,UAAU,EAAE,IAhQN;AAiQNC,MAAAA,aAAa,EAAE,KAjQT;AAkQNC,MAAAA,OAAO,EAAE,IAlQH;AAmQNC,MAAAA,SAAS,EAAE,OAnQL;AAoQNC,MAAAA,mBAAmB,EAAE,MApQf;AAqQNC,MAAAA,kBAAkB,EAAE,MArQd;AAsQNC,MAAAA,SAAS,EAAE,KAtQL;AAuQNC,MAAAA,OAAO,EAAE,cAvQH;AAwQNC,MAAAA,QAAQ,EAAE,IAxQJ;AAyQNC,MAAAA,aAAa,EAAE,MAzQT;AA0QNC,MAAAA,SAAS,EAAE,OA1QL;AA2QNC,MAAAA,SAAS,EAAE,OA3QL;AA4QNC,MAAAA,cAAc,EAAE,YA5QV;AA6QNC,MAAAA,YAAY,EAAE,SA7QR;AA8QNC,MAAAA,kBAAkB,EAAE,YA9Qd;AA+QNC,MAAAA,WAAW,EAAE,OA/QP;AAgRNC,MAAAA,WAAW,EAAE,IAhRP;AAiRNC,MAAAA,WAAW,EAAE,IAjRP;AAkRNC,MAAAA,UAAU,EAAE,UAlRN;AAmRNC,MAAAA,mBAAmB,EAAE,WAnRf;AAoRNC,MAAAA,cAAc,EAAE,OApRV;AAqRNC,MAAAA,eAAe,EAAE,QArRX;AAsRNC,MAAAA,MAAM,EAAE,MAtRF;AAuRNC,MAAAA,kBAAkB,EAAE,mBAvRd;AAwRNC,MAAAA,aAAa,EAAE,mBAxRT;AAyRNC,MAAAA,WAAW,EAAE,OAzRP;AA0RNC,MAAAA,UAAU,EAAE,OA1RN;AA2RNC,MAAAA,UAAU,EAAE,MA3RN;AA4RNC,MAAAA,GAAG,EAAE,GA5RC;AA6RNC,MAAAA,GAAG,EAAE,GA7RC;AA8RNC,MAAAA,IAAI,EAAE,IA9RA;AA+RNC,MAAAA,QAAQ,EAAE,IA/RJ;AAgSNC,MAAAA,OAAO,EAAE,IAhSH;AAiSNC,MAAAA,UAAU,EAAE,KAjSN;AAkSNC,MAAAA,aAAa,EAAE,OAlST;AAmSNC,MAAAA,eAAe,EAAE,QAnSX;AAoSNC,MAAAA,gBAAgB,EAAE,OApSZ;AAqSNC,MAAAA,gBAAgB,EAAE,SArSZ;AAsSNC,MAAAA,UAAU,EAAE,KAtSN;AAuSNC,MAAAA,cAAc,EAAE,OAvSV;AAwSNC,MAAAA,WAAW,EAAE;AAxSP,KAFJ;AA4SJ;AACAC,IAAAA,KAAK,EAAE;AACLC,MAAAA,OAAO,EAAE,IADJ;AAELC,MAAAA,QAAQ,EAAE,IAFL;AAGLC,MAAAA,aAAa,EAAE,MAHV;AAILH,MAAAA,KAAK,EAAE,IAJF;AAKLI,MAAAA,2BAA2B,EAAE,QALxB;AAMLC,MAAAA,4BAA4B,EAAE,QANzB;AAOLC,MAAAA,2BAA2B,EAAE;AAPxB,KA7SH;AAsTJ;AACAH,IAAAA,aAAa,EAAE;AACbA,MAAAA,aAAa,EAAE,MADF;AAEbI,MAAAA,0BAA0B,EAAE,WAFf;AAGbC,MAAAA,gBAAgB,EAAE,QAHL;AAIbC,MAAAA,qBAAqB,EAAE,UAJV;AAKbC,MAAAA,IAAI,EAAE,IALO;AAMbC,MAAAA,6BAA6B,EAAE,SANlB;AAObC,MAAAA,kCAAkC,EAAE,WAPvB;AAQbC,MAAAA,4BAA4B,EAAE,2BARjB;AASbC,MAAAA,eAAe,EAAE,cATJ;AAUbC,MAAAA,uBAAuB,EAAE,eAVZ;AAWbC,MAAAA,UAAU,EAAE,WAXC;AAYbC,MAAAA,UAAU,EAAE,SAZC;AAabC,MAAAA,yBAAyB,EAAE,UAbd;AAcbC,MAAAA,qBAAqB,EAAE,iBAdV;AAebC,MAAAA,yBAAyB,EAAE,WAfd;AAgBbC,MAAAA,UAAU,EAAE,WAhBC;AAiBbC,MAAAA,mBAAmB,EAAE,kBAjBR;AAkBbC,MAAAA,qBAAqB,EAAE;AAlBV,KAvTX;AA2UJrN,IAAAA,QAAQ,EAAE;AACR+L,MAAAA,OAAO,EAAE,IADD;AAERrI,MAAAA,IAAI,EAAE,IAFE;AAGR4J,MAAAA,KAAK,EAAE,MAHC;AAIRC,MAAAA,KAAK,EAAE,IAJC;AAKR3J,MAAAA,IAAI,EAAE,IALE;AAMR4J,MAAAA,2BAA2B,EAAE,sBANrB;AAORtB,MAAAA,2BAA2B,EAAE,QAPrB;AAQRuB,MAAAA,wBAAwB,EAAE,QARlB;AASRC,MAAAA,oBAAoB,EAAE,iBATd;AAURV,MAAAA,yBAAyB,EAAE,UAVnB;AAWRC,MAAAA,qBAAqB,EAAE,iBAXf;AAYRU,MAAAA,yBAAyB,EAAE,QAZnB;AAaRC,MAAAA,wBAAwB,EAAE,SAblB;AAcRC,MAAAA,4BAA4B,EAAE;AAdtB,KA3UN;AA2VJ;AACAC,IAAAA,SAAS,EAAE;AACTC,MAAAA,MAAM,EAAE,IADC;AAETC,MAAAA,UAAU,EAAE,IAFH;AAGTC,MAAAA,MAAM,EAAE,IAHC;AAITC,MAAAA,UAAU,EAAE,KAJH;AAKTC,MAAAA,eAAe,EAAE,OALR;AAMT3I,MAAAA,WAAW,EAAE,IANJ;AAOT+D,MAAAA,OAAO,EAAE,IAPA;AAQT5D,MAAAA,EAAE,EAAE,GARK;AASTyI,MAAAA,eAAe,EAAE,MATR;AAUTC,MAAAA,IAAI,EAAE,IAVG;AAWTC,MAAAA,aAAa,EAAE,SAXN;AAYTC,MAAAA,kBAAkB,EAAE,MAZX;AAaTC,MAAAA,MAAM,EAAE,IAbC;AAcTC,MAAAA,UAAU,EAAE,MAdH;AAeTC,MAAAA,UAAU,EAAE,MAfH;AAgBTC,MAAAA,OAAO,EAAE,IAhBA;AAiBTC,MAAAA,iBAAiB,EAAE,QAjBV;AAkBTC,MAAAA,gBAAgB,EAAE,OAlBT;AAmBTC,MAAAA,WAAW,EAAE,MAnBJ;AAoBTC,MAAAA,cAAc,EAAE,MApBP;AAqBTC,MAAAA,eAAe,EAAE,MArBR;AAsBTC,MAAAA,QAAQ,EAAE,MAtBD;AAuBTC,MAAAA,cAAc,EAAE,MAvBP;AAwBTC,MAAAA,SAAS,EAAE,SAxBF;AAyBTC,MAAAA,aAAa,EAAE,MAzBN;AA0BTC,MAAAA,UAAU,EAAE,MA1BH;AA2BTC,MAAAA,eAAe,EAAE,MA3BR;AA4BTC,MAAAA,kBAAkB,EAAE,WA5BX;AA6BTC,MAAAA,uBAAuB,EAAE,WA7BhB;AA8BTC,MAAAA,uBAAuB,EAAE,qBA9BhB;AA+BTC,MAAAA,kBAAkB,EAAE,MA/BX;AAgCTC,MAAAA,WAAW,EAAE,MAhCJ;AAiCTC,MAAAA,YAAY,EAAE,MAjCL;AAkCTC,MAAAA,SAAS,EAAE,QAlCF;AAmCTC,MAAAA,sBAAsB,EAAE;AAnCf,KA5VP;AAiYJ;AACApL,IAAAA,UAAU,EAAE;AACVqL,MAAAA,aAAa,EAAE,MADL;AAEVC,MAAAA,QAAQ,EAAE,MAFA;AAGVC,MAAAA,8BAA8B,EAAE,aAHtB;AAIVC,MAAAA,gBAAgB,EAAE,QAJR;AAKVC,MAAAA,wBAAwB,EAAE,UALhB;AAMVC,MAAAA,0BAA0B,EAAE,SANlB;AAOVC,MAAAA,kBAAkB,EAAE,cAPV;AAQVC,MAAAA,qBAAqB,EAAE,oBARb;AASVC,MAAAA,YAAY,EAAE,MATJ;AAUVC,MAAAA,eAAe,EAAE,MAVP;AAWVC,MAAAA,0BAA0B,EAAE,UAXlB;AAYVC,MAAAA,wBAAwB,EAAE,UAZhB;AAaVC,MAAAA,sCAAsC,EAAE,aAb9B;AAcVC,MAAAA,kBAAkB,EAAE,UAdV;AAeVC,MAAAA,yBAAyB,EAAE,aAfjB;AAgBVC,MAAAA,sBAAsB,EAAE,4BAhBd;AAiBVC,MAAAA,aAAa,EAAE,SAjBL;AAkBVC,MAAAA,OAAO,EAAE,KAlBC;AAmBVC,MAAAA,QAAQ,EAAE,IAnBA;AAoBVC,MAAAA,aAAa,EAAE,OApBL;AAqBVC,MAAAA,MAAM,EAAE,MArBE;AAsBVC,MAAAA,UAAU,EAAE,OAtBF;AAuBVC,MAAAA,SAAS,EAAE,IAvBD;AAwBVC,MAAAA,QAAQ,EAAE;AAxBA,KAlYR;AA4ZJ;AACAnM,IAAAA,iBAAiB,EAAE;AACjB4G,MAAAA,OAAO,EAAE,IADQ;AAEjBrI,MAAAA,IAAI,EAAE,IAFW;AAGjB4J,MAAAA,KAAK,EAAE,MAHU;AAIjBC,MAAAA,KAAK,EAAE,IAJU;AAKjB3J,MAAAA,IAAI,EAAE,IALW;AAMjBI,MAAAA,UAAU,EAAE,QANK;AAOjBuN,MAAAA,UAAU,EAAE,IAPK;AAQjBC,MAAAA,mBAAmB,EAAE,YARJ;AASjBnE,MAAAA,qBAAqB,EAAE,mBATN;AAUjBoE,MAAAA,gBAAgB,EAAE,QAVD;AAWjBC,MAAAA,YAAY,EAAE,SAXG;AAYjBC,MAAAA,aAAa,EAAE,SAZE;AAajB3H,MAAAA,SAAS,EAAE,SAbM;AAcjB8C,MAAAA,UAAU,EAAE,WAdK;AAejB8E,MAAAA,UAAU,EAAE,SAfK;AAgBjBC,MAAAA,UAAU,EAAE,WAhBK;AAiBjB3F,MAAAA,2BAA2B,EAAE,QAjBZ;AAkBjB4F,MAAAA,uBAAuB,EAAE,iBAlBR;AAmBjBC,MAAAA,6BAA6B,EAAE,kBAnBd;AAoBjB5F,MAAAA,4BAA4B,EAAE,QApBb;AAqBjBC,MAAAA,2BAA2B,EAAE,SArBZ;AAsBjBqB,MAAAA,wBAAwB,EAAE,QAtBT;AAuBjBC,MAAAA,oBAAoB,EAAE,iBAvBL;AAwBjBV,MAAAA,yBAAyB,EAAE,UAxBV;AAyBjBC,MAAAA,qBAAqB,EAAE,iBAzBN;AA0BjBO,MAAAA,2BAA2B,EAAE,sBA1BZ;AA2BjBG,MAAAA,yBAAyB,EAAE,QA3BV;AA4BjBC,MAAAA,wBAAwB,EAAE,SA5BT;AA6BjBC,MAAAA,4BAA4B,EAAE,UA7Bb;AA8BjBmE,MAAAA,WAAW,EAAE,MA9BI;AA+BjBC,MAAAA,sBAAsB,EAAE,QA/BP;AAgCjBC,MAAAA,0BAA0B,EAAE,QAhCX;AAiCjBC,MAAAA,cAAc,EAAE,QAjCC;AAkCjBC,MAAAA,cAAc,EAAE,MAlCC;AAmCjBrK,MAAAA,IAAI,EAAE,MAnCW;AAoCjBsK,MAAAA,KAAK,EAAE,MApCU;AAqCjBC,MAAAA,KAAK,EAAE,MArCU;AAsCjBC,MAAAA,aAAa,EAAE,MAtCE;AAuCjBrE,MAAAA,UAAU,EAAE,OAvCK;AAwCjBsE,MAAAA,eAAe,EAAE,QAxCA;AAyCjBlL,MAAAA,QAAQ,EAAE,MAzCO;AA0CjBF,MAAAA,WAAW,EAAE,QA1CI;AA2CjBC,MAAAA,YAAY,EAAE,MA3CG;AA4CjBG,MAAAA,gBAAgB,EAAE,QA5CD;AA6CjBiL,MAAAA,+BAA+B,EAAE,iBA7ChB;AA8CjBC,MAAAA,oBAAoB,EAAE,OA9CL;AA+CjBC,MAAAA,gBAAgB,EAAE,OA/CD;AAgDjBC,MAAAA,iBAAiB,EAAE,OAhDF;AAiDjBC,MAAAA,kBAAkB,EAAE,OAjDH;AAkDjBC,MAAAA,4BAA4B,EAAE,QAlDb;AAmDjBC,MAAAA,wBAAwB,EAAE,QAnDT;AAoDjBC,MAAAA,yBAAyB,EAAE,QApDV;AAqDjBC,MAAAA,0BAA0B,EAAE,QArDX;AAsDjBC,MAAAA,iBAAiB,EAAE,QAtDF;AAuDjBC,MAAAA,+BAA+B,EAAE,aAvDhB;AAwDjBC,MAAAA,mBAAmB,EAAE;AAxDJ,KA7Zf;AAudJ;AACAhO,IAAAA,cAAc,EAAE;AACdiO,MAAAA,QAAQ,EAAE,MADI;AAEdC,MAAAA,QAAQ,EAAE,MAFI;AAGdC,MAAAA,cAAc,EAAE,QAHF;AAIdC,MAAAA,cAAc,EAAE,QAJF;AAKdC,MAAAA,iBAAiB,EAAE,cALL;AAMdC,MAAAA,gBAAgB,EAAE,qBANJ;AAOdC,MAAAA,aAAa,EAAE,sBAPD;AAQdC,MAAAA,IAAI,EAAE,IARQ;AASdC,MAAAA,OAAO,EAAE,QATK;AAUdC,MAAAA,aAAa,EAAE,WAVD;AAWdC,MAAAA,aAAa,EAAE,WAXD;AAYdC,MAAAA,iBAAiB,EAAE,MAZL;AAadC,MAAAA,gBAAgB,EAAE,wBAbJ;AAcdC,MAAAA,gBAAgB,EAAE,wBAdJ;AAedC,MAAAA,sBAAsB,EAAE,cAfV;AAgBdC,MAAAA,cAAc,EAAE,qBAhBF;AAiBdC,MAAAA,kBAAkB,EAAE;AAjBN,KAxdZ;AA2eJ;AACAxO,IAAAA,cAAc,EAAE;AACdlC,MAAAA,QAAQ,EAAE,IADI;AAEdC,MAAAA,IAAI,EAAE,IAFQ;AAGdC,MAAAA,KAAK,EAAE,IAHO;AAIdyQ,MAAAA,YAAY,EAAE,MAJA;AAKdC,MAAAA,YAAY,EAAE,MALA;AAMdC,MAAAA,IAAI,EAAE,IANQ;AAOdnB,MAAAA,QAAQ,EAAE,MAPI;AAQdC,MAAAA,QAAQ,EAAE,MARI;AASdmB,MAAAA,SAAS,EAAE,MATG;AAUdC,MAAAA,SAAS,EAAE,MAVG;AAWd1Q,MAAAA,UAAU,EAAE,QAXE;AAYd2Q,MAAAA,0BAA0B,EAAE,WAZd;AAadC,MAAAA,sBAAsB,EAAE,WAbV;AAcdC,MAAAA,uBAAuB,EAAE,WAdX;AAedC,MAAAA,8BAA8B,EAAE,mBAflB;AAgBdC,MAAAA,8BAA8B,EAAE,mBAhBlB;AAiBdC,MAAAA,+BAA+B,EAAE,mBAjBnB;AAkBdC,MAAAA,2BAA2B,EAAE,sBAlBf;AAmBdC,MAAAA,0BAA0B,EAAE,sBAnBd;AAoBdC,MAAAA,gBAAgB,EAAE,wBApBJ;AAqBdC,MAAAA,oBAAoB,EAAE,QArBR;AAsBdC,MAAAA,gBAAgB,EAAE,OAtBJ;AAuBdC,MAAAA,6BAA6B,EAAE,WAvBjB;AAwBdvL,MAAAA,SAAS,EAAE,SAxBG;AAyBdC,MAAAA,SAAS,EAAE,SAzBG;AA0BduL,MAAAA,qCAAqC,EAAE,UA1BzB;AA2BdC,MAAAA,wBAAwB,EAAE,QA3BZ;AA4BdC,MAAAA,oBAAoB,EAAE,iBA5BR;AA6BdhI,MAAAA,wBAAwB,EAAE,QA7BZ;AA8BdC,MAAAA,oBAAoB,EAAE,iBA9BR;AA+BdgI,MAAAA,wBAAwB,EAAE,QA/BZ;AAgCdC,MAAAA,4BAA4B,EAAE,kBAhChB;AAiCdC,MAAAA,IAAI,EAAE,IAjCQ;AAkCdlS,MAAAA,IAAI,EAAE,IAlCQ;AAmCdmS,MAAAA,oBAAoB,EAAE,YAnCR;AAoCdC,MAAAA,6BAA6B,EAAE,aApCjB;AAqCdC,MAAAA,WAAW,EAAE,QArCC;AAsCdC,MAAAA,gBAAgB,EAAE,aAtCJ;AAuCdC,MAAAA,eAAe,EAAE,cAvCH;AAwCdC,MAAAA,kBAAkB,EAAE,eAxCN;AAyCdC,MAAAA,uBAAuB,EAAE,UAzCX;AA0CdC,MAAAA,8BAA8B,EAAE,YA1ClB;AA2CdC,MAAAA,kBAAkB,EAAE,SA3CN;AA4CdC,MAAAA,gBAAgB,EAAE,OA5CJ;AA6CdC,MAAAA,SAAS,EAAE,QA7CG;AA8CdC,MAAAA,qBAAqB,EAAE;AA9CT,KA5eZ;AA4hBJ;AACAhR,IAAAA,WAAW,EAAE;AACXiR,MAAAA,oBAAoB,EAAE,MADX;AAEXC,MAAAA,GAAG,EAAE,MAFM;AAGXhT,MAAAA,IAAI,EAAE,MAHK;AAIXiT,MAAAA,EAAE,EAAE,MAJO;AAKXnN,MAAAA,SAAS,EAAE,MALA;AAMXoN,MAAAA,aAAa,EAAE,MANJ;AAOXC,MAAAA,UAAU,EAAE,OAPD;AAQXC,MAAAA,OAAO,EAAE,MARE;AASX9S,MAAAA,UAAU,EAAE,QATD;AAUX+S,MAAAA,oBAAoB,EAAE,SAVX;AAWXT,MAAAA,gBAAgB,EAAE,OAXP;AAYXC,MAAAA,SAAS,EAAE,OAZA;AAaXhR,MAAAA,aAAa,EAAE,MAbJ;AAcXyR,MAAAA,sBAAsB,EAAE,qBAdb;AAeXC,MAAAA,2BAA2B,EAAE,oBAflB;AAgBXC,MAAAA,wBAAwB,EAAE,WAhBf;AAiBXC,MAAAA,WAAW,EAAE,KAjBF;AAkBXC,MAAAA,WAAW,EAAE,KAlBF;AAmBXC,MAAAA,QAAQ,EAAE,MAnBC;AAoBXC,MAAAA,QAAQ,EAAE,SApBC;AAqBXtN,MAAAA,SAAS,EAAE,OArBA;AAsBXuN,MAAAA,YAAY,EAAE,UAtBH;AAuBXC,MAAAA,kBAAkB,EAAE,QAvBT;AAwBXC,MAAAA,gBAAgB,EAAE,MAxBP;AAyBXC,MAAAA,aAAa,EAAE,SAzBJ;AA0BXC,MAAAA,qBAAqB,EAAE,UA1BZ;AA2BXC,MAAAA,cAAc,EAAE,MA3BL;AA4BXC,MAAAA,mCAAmC,EAAE,cA5B1B;AA6BXC,MAAAA,kBAAkB,EAAE,iBA7BT;AA8BXC,MAAAA,eAAe,EAAE,cA9BN;AA+BXC,MAAAA,SAAS,EAAE,OA/BA;AAgCXC,MAAAA,WAAW,EAAE,MAhCF;AAiCXC,MAAAA,QAAQ,EAAE,OAjCC;AAkCXC,MAAAA,QAAQ,EAAE,OAlCC;AAmCXC,MAAAA,UAAU,EAAE;AAnCD,KA7hBT;AAkkBJ;AACArS,IAAAA,iBAAiB,EAAE;AACjBsS,MAAAA,kBAAkB,EAAE,kBADH;AAEjBC,MAAAA,SAAS,EAAE,OAFM;AAGjB7J,MAAAA,UAAU,EAAE,MAHK;AAIjB8J,MAAAA,cAAc,EAAE,MAJC;AAKjB7J,MAAAA,UAAU,EAAE,MALK;AAMjBK,MAAAA,cAAc,EAAE,MANC;AAOjByJ,MAAAA,YAAY,EAAE,MAPG;AAQjBC,MAAAA,eAAe,EAAE,MARA;AASjBC,MAAAA,IAAI,EAAE,SATW;AAUjBC,MAAAA,IAAI,EAAE,QAVW;AAWjBC,MAAAA,WAAW,EAAE,IAXI;AAYjBC,MAAAA,QAAQ,EAAE,IAZO;AAajBC,MAAAA,WAAW,EAAE,aAbI;AAcjBC,MAAAA,WAAW,EAAE,YAdI;AAejBC,MAAAA,kBAAkB,EAAE,QAfH;AAgBjBC,MAAAA,eAAe,EAAE,QAhBA;AAiBjBC,MAAAA,SAAS,EAAE,IAjBM;AAkBjBC,MAAAA,mBAAmB,EAAE,QAlBJ;AAmBjBC,MAAAA,GAAG,EAAE,KAnBY;AAoBjBC,MAAAA,aAAa,EAAE,SApBE;AAqBjBC,MAAAA,GAAG,EAAE,KArBY;AAsBjBC,MAAAA,aAAa,EAAE,SAtBE;AAuBjBvK,MAAAA,eAAe,EAAE,MAvBA;AAwBjBJ,MAAAA,iBAAiB,EAAE,QAxBF;AAyBjB4K,MAAAA,eAAe,EAAE,MAzBA;AA0BjBxV,MAAAA,UAAU,EAAE,QA1BK;AA2BjByV,MAAAA,0BAA0B,EAAE,mBA3BX;AA4BjBC,MAAAA,wBAAwB,EAAE,YA5BT;AA6BjBC,MAAAA,0BAA0B,EAAE,cA7BX;AA8BjBC,MAAAA,iCAAiC,EAAE,cA9BlB;AA+BjBnK,MAAAA,uBAAuB,EAAE,qBA/BR;AAgCjBF,MAAAA,kBAAkB,EAAE,WAhCH;AAiCjBC,MAAAA,uBAAuB,EAAE,WAjCR;AAkCjBqK,MAAAA,oBAAoB,EAAE,QAlCL;AAmCjBC,MAAAA,cAAc,EAAE,YAnCC;AAoCjBC,MAAAA,eAAe,EAAE,WApCA;AAqCjBC,MAAAA,gBAAgB,EAAE,SArCD;AAsCjBC,MAAAA,uBAAuB,EAAE,WAtCR;AAuCjBC,MAAAA,mBAAmB,EAAE,oBAvCJ;AAwCjBzM,MAAAA,wBAAwB,EAAE,UAxCT;AAyCjBC,MAAAA,oBAAoB,EAAE,mBAzCL;AA0CjByM,MAAAA,0BAA0B,EAAE,mBA1CX;AA2CjBC,MAAAA,8BAA8B,EAAE,uBA3Cf;AA4CjBC,MAAAA,2BAA2B,EAAE,wBA5CZ;AA6CjBxE,MAAAA,oBAAoB,EAAE,YA7CL;AA8CjBC,MAAAA,6BAA6B,EAAE,aA9Cd;AA+CjBC,MAAAA,WAAW,EAAE,QA/CI;AAgDjBC,MAAAA,gBAAgB,EAAE,aAhDD;AAiDjBC,MAAAA,eAAe,EAAE,cAjDA;AAkDjBC,MAAAA,kBAAkB,EAAE,eAlDH;AAmDjBC,MAAAA,uBAAuB,EAAE,UAnDR;AAoDjBE,MAAAA,kBAAkB,EAAE,SApDH;AAqDjBC,MAAAA,gBAAgB,EAAE,OArDD;AAsDjBC,MAAAA,SAAS,EAAE,QAtDM;AAuDjB+D,MAAAA,iBAAiB,EAAE;AAvDF,KAnkBf;AA4nBJ;AACA7U,IAAAA,MAAM,EAAE;AACN8U,MAAAA,mBAAmB,EAAE,MADf;AAENC,MAAAA,SAAS,EAAE,OAFL;AAGNC,MAAAA,UAAU,EAAE,MAHN;AAINC,MAAAA,cAAc,EAAE,UAJV;AAKNC,MAAAA,YAAY,EAAE,UALR;AAMNC,MAAAA,SAAS,EAAE,MANL;AAONC,MAAAA,iCAAiC,EAAE,eAP7B;AAQNC,MAAAA,oBAAoB,EAAE;AARhB,KA7nBJ;AAuoBJ;AACApV,IAAAA,KAAK,EAAE;AACLqV,MAAAA,kBAAkB,EAAE,SADf;AAELnB,MAAAA,iCAAiC,EAAE,cAF9B;AAGLD,MAAAA,0BAA0B,EAAE,cAHvB;AAIL/D,MAAAA,IAAI,EAAE,IAJD;AAKLzG,MAAAA,SAAS,EAAE,SALN;AAMLT,MAAAA,UAAU,EAAE,MANP;AAOLsM,MAAAA,MAAM,EAAE,IAPH;AAQLC,MAAAA,UAAU,EAAE,MARP;AASLC,MAAAA,aAAa,EAAE,OATV;AAULlM,MAAAA,eAAe,EAAE,MAVZ;AAWLmM,MAAAA,gBAAgB,EAAE,MAXb;AAYLnX,MAAAA,UAAU,EAAE,QAZP;AAaLsD,MAAAA,QAAQ,EAAE,MAbL;AAcLF,MAAAA,WAAW,EAAE,MAdR;AAeLC,MAAAA,YAAY,EAAE,MAfT;AAgBL+T,MAAAA,MAAM,EAAE,IAhBH;AAiBLC,MAAAA,KAAK,EAAE,IAjBF;AAkBLpN,MAAAA,MAAM,EAAE,IAlBH;AAmBL1E,MAAAA,OAAO,EAAE,IAnBJ;AAoBL+R,MAAAA,aAAa,EAAE,KApBV;AAqBLC,MAAAA,YAAY,EAAE,KArBT;AAsBLC,MAAAA,yBAAyB,EAAE,gBAtBtB;AAuBLC,MAAAA,yBAAyB,EAAE,sBAvBtB;AAwBLC,MAAAA,uBAAuB,EAAE,eAxBpB;AAyBLC,MAAAA,oBAAoB,EAAE,QAzBjB;AA0BL5e,MAAAA,MAAM,EAAE,IA1BH;AA2BLia,MAAAA,sBAAsB,EAAE,qBA3BnB;AA4BLnW,MAAAA,6BAA6B,EAAE,qBA5B1B;AA6BL+a,MAAAA,8BAA8B,EAAE,YA7B3B;AA8BL/F,MAAAA,oBAAoB,EAAE,YA9BjB;AA+BLC,MAAAA,6BAA6B,EAAE,aA/B1B;AAgCLC,MAAAA,WAAW,EAAE,QAhCR;AAiCLC,MAAAA,gBAAgB,EAAE,aAjCb;AAkCLC,MAAAA,eAAe,EAAE,cAlCZ;AAmCLC,MAAAA,kBAAkB,EAAE,eAnCf;AAoCLC,MAAAA,uBAAuB,EAAE,UApCpB;AAqCLE,MAAAA,kBAAkB,EAAE,SArCf;AAsCLC,MAAAA,gBAAgB,EAAE,OAtCb;AAuCLC,MAAAA,SAAS,EAAE,QAvCN;AAwCLsF,MAAAA,QAAQ,EAAE,SAxCL;AAyCLC,MAAAA,gBAAgB,EAAE,MAzCb;AA0CL/R,MAAAA,SAAS,EAAE,SA1CN;AA2CLC,MAAAA,SAAS,EAAE,SA3CN;AA4CL+R,MAAAA,sBAAsB,EAAE,SA5CnB;AA6CLlJ,MAAAA,kBAAkB,EAAE,OA7Cf;AA8CLmJ,MAAAA,sBAAsB,EAAE,SA9CnB;AA+CLC,MAAAA,QAAQ,EAAE,MA/CL;AAgDLC,MAAAA,QAAQ,EAAE,UAhDL;AAiDLC,MAAAA,QAAQ,EAAE,OAjDL;AAkDLC,MAAAA,SAAS,EAAE,OAlDN;AAmDLC,MAAAA,aAAa,EAAE,UAnDV;AAoDLC,MAAAA,aAAa,EAAE,WApDV;AAqDLC,MAAAA,cAAc,EAAE,QArDX;AAsDLC,MAAAA,aAAa,EAAE,QAtDV;AAuDLC,MAAAA,YAAY,EAAE,aAvDT;AAwDLC,MAAAA,SAAS,EAAE,MAxDN;AAyDLC,MAAAA,wBAAwB,EAAE,cAzDrB;AA0DLC,MAAAA,qBAAqB,EAAE,gBA1DlB;AA2DLC,MAAAA,uBAAuB,EAAE,mBA3DpB;AA4DLC,MAAAA,kBAAkB,EAAE,cA5Df;AA6DLC,MAAAA,cAAc,EAAE,aA7DX;AA8DLvH,MAAAA,wBAAwB,EAAE,QA9DrB;AA+DLC,MAAAA,oBAAoB,EAAE,iBA/DjB;AAgELhI,MAAAA,wBAAwB,EAAE,QAhErB;AAiELC,MAAAA,oBAAoB,EAAE,iBAjEjB;AAkELsP,MAAAA,4BAA4B,EAAE,UAlEzB;AAmELC,MAAAA,4BAA4B,EAAE,cAnEzB;AAoELC,MAAAA,4BAA4B,EAAE,SApEzB;AAqELC,MAAAA,6BAA6B,EAAE,SArE1B;AAsELC,MAAAA,iCAAiC,EAAE,WAtE9B;AAuELC,MAAAA,iCAAiC,EAAE,YAvE9B;AAwELC,MAAAA,kCAAkC,EAAE,UAxE/B;AAyELC,MAAAA,iCAAiC,EAAE,UAzE9B;AA0ELC,MAAAA,gCAAgC,EAAE,cA1E7B;AA2ELC,MAAAA,wBAAwB,EAAE,eA3ErB;AA4ELC,MAAAA,wBAAwB,EAAE,oBA5ErB;AA6ELC,MAAAA,wBAAwB,EAAE,cA7ErB;AA8ELC,MAAAA,yBAAyB,EAAE,cA9EtB;AA+ELC,MAAAA,6BAA6B,EAAE,gBA/E1B;AAgFLC,MAAAA,6BAA6B,EAAE,oBAhF1B;AAiFLC,MAAAA,8BAA8B,EAAE,eAjF3B;AAkFLC,MAAAA,6BAA6B,EAAE,eAlF1B;AAmFLC,MAAAA,4BAA4B,EAAE,sBAnFzB;AAoFL9D,MAAAA,0BAA0B,EAAE,mBApFvB;AAqFL+D,MAAAA,6BAA6B,EAAE,uBArF1B;AAsFLC,MAAAA,2BAA2B,EAAE,kBAtFxB;AAuFLC,MAAAA,gBAAgB,EAAE,WAvFb;AAwFLC,MAAAA,yBAAyB,EAAE,YAxFtB;AAyFLC,MAAAA,mBAAmB,EAAE,yBAzFhB;AA0FLC,MAAAA,yBAAyB,EAAE,uBA1FtB;AA2FLC,MAAAA,WAAW,EAAE,KA3FR;AA4FLC,MAAAA,WAAW,EAAE,KA5FR;AA6FLC,MAAAA,0CAA0C,EAAE,uBA7FvC;AA8FLC,MAAAA,iBAAiB,EAAE,SA9Fd;AA+FLC,MAAAA,yBAAyB,EAAE,mBA/FtB;AAgGLC,MAAAA,aAAa,EAAE;AAhGV,KAxoBH;AA0uBJ;AACA/Y,IAAAA,UAAU,EAAE;AACVJ,MAAAA,KAAK,EAAE,SADG;AAEVoZ,MAAAA,cAAc,EAAE,aAFN;AAGVC,MAAAA,kBAAkB,EAAE,WAHV;AAIVC,MAAAA,kBAAkB,EAAE,WAJV;AAKVC,MAAAA,mBAAmB,EAAE,WALX;AAMVC,MAAAA,cAAc,EAAE,aANN;AAOVC,MAAAA,aAAa,EAAE,gBAPL;AAQVC,MAAAA,kBAAkB,EAAE,oBARV;AASVC,MAAAA,iBAAiB,EAAE;AATT,KA3uBR;AAsvBJ;AACA9Z,IAAAA,aAAa,EAAE;AACb+Z,MAAAA,cAAc,EAAE,MADH;AAEbC,MAAAA,iBAAiB,EAAE,QAFN;AAGbC,MAAAA,aAAa,EAAE,QAHF;AAIbC,MAAAA,gBAAgB,EAAE,UAJL;AAKbC,MAAAA,iBAAiB,EAAE,aALN;AAMbC,MAAAA,gBAAgB,EAAE,WANL;AAObC,MAAAA,iBAAiB,EAAE,YAPN;AAQbC,MAAAA,cAAc,EAAE,WARH;AASbC,MAAAA,eAAe,EAAE,WATJ;AAUbC,MAAAA,QAAQ,EAAE,OAVG;AAWbC,MAAAA,QAAQ,EAAE,OAXG;AAYbC,MAAAA,aAAa,EAAE,WAZF;AAabC,MAAAA,aAAa,EAAE,WAbF;AAcbC,MAAAA,sBAAsB,EAAE,UAdX;AAebC,MAAAA,mBAAmB,EAAE,iCAfR;AAgBbC,MAAAA,qBAAqB,EAAE,oBAhBV;AAiBbC,MAAAA,mBAAmB,EAAE,sBAjBR;AAkBbC,MAAAA,mBAAmB,EAAE,oBAlBR;AAmBbC,MAAAA,eAAe,EAAE,oBAnBJ;AAoBbrL,MAAAA,gBAAgB,EAAE,uBApBL;AAqBbsL,MAAAA,wBAAwB,EAAE,QArBb;AAsBbC,MAAAA,6BAA6B,EAAE,YAtBlB;AAuBbC,MAAAA,0BAA0B,EAAE,yBAvBf;AAwBbC,MAAAA,eAAe,EAAE,6BAxBJ;AAyBbC,MAAAA,kBAAkB,EAAE,kCAzBP;AA0BbC,MAAAA,8BAA8B,EAAE,oBA1BnB;AA2BbC,MAAAA,QAAQ,EAAE,YA3BG;AA4BbC,MAAAA,cAAc,EAAE,uBA5BH;AA6BbC,MAAAA,mCAAmC,EAAE,WA7BxB;AA8BbC,MAAAA,oCAAoC,EAAE,YA9BzB;AA+BbC,MAAAA,iCAAiC,EAAE,WA/BtB;AAgCbC,MAAAA,qCAAqC,EAAE,WAhC1B;AAiCbC,MAAAA,oBAAoB,EAAE,UAjCT;AAkCbC,MAAAA,gBAAgB,EAAE,aAlCL;AAmCbC,MAAAA,sBAAsB,EAAE,SAnCX;AAoCbC,MAAAA,oBAAoB,EAAE,eApCT;AAqCbC,MAAAA,sBAAsB,EAAE,UArCX;AAsCbC,MAAAA,kBAAkB,EAAE,eAtCP;AAuCbC,MAAAA,wBAAwB,EAAE,uBAvCb;AAwCbC,MAAAA,UAAU,EAAE,OAxCC;AAyCbC,MAAAA,kBAAkB,EAAE,qBAzCP;AA0CbC,MAAAA,cAAc,EAAE,UA1CH;AA2CbC,MAAAA,oBAAoB,EAAE,uBA3CT;AA4CbC,MAAAA,aAAa,EAAE,mBA5CF;AA6CbC,MAAAA,uBAAuB,EAAE,WA7CZ;AA8CbC,MAAAA,gBAAgB,EAAE;AA9CL,KAvvBX;AAuyBJ;AACAlc,IAAAA,eAAe,EAAE;AACfmc,MAAAA,WAAW,EAAE,MADE;AAEfC,MAAAA,UAAU,EAAE,MAFG;AAGfC,MAAAA,SAAS,EAAE,MAHI;AAIfC,MAAAA,aAAa,EAAE,OAJA;AAKfC,MAAAA,kBAAkB,EAAE,QALL;AAMfC,MAAAA,cAAc,EAAE,SAND;AAOfC,MAAAA,cAAc,EAAE,WAPD;AAQfC,MAAAA,cAAc,EAAE,WARD;AASfC,MAAAA,oBAAoB,EAAE,SATP;AAUfC,MAAAA,mBAAmB,EAAE,WAVN;AAWfC,MAAAA,yBAAyB,EAAE,SAXZ;AAYf/X,MAAAA,WAAW,EAAE,SAZE;AAafgY,MAAAA,UAAU,EAAE,OAbG;AAcfC,MAAAA,eAAe,EAAE,SAdF;AAefC,MAAAA,aAAa,EAAE,SAfA;AAgBfC,MAAAA,iBAAiB,EAAE,UAhBJ;AAiBfC,MAAAA,iBAAiB,EAAE,UAjBJ;AAkBfC,MAAAA,kBAAkB,EAAE,UAlBL;AAmBfC,MAAAA,qBAAqB,EAAE,UAnBR;AAoBfC,MAAAA,qBAAqB,EAAE,UApBR;AAqBfC,MAAAA,sBAAsB,EAAE,UArBT;AAsBfC,MAAAA,kBAAkB,EAAE,SAtBL;AAuBfC,MAAAA,kBAAkB,EAAE,SAvBL;AAwBfC,MAAAA,mBAAmB,EAAE,SAxBN;AAyBfC,MAAAA,qBAAqB,EAAE,SAzBR;AA0BfC,MAAAA,gBAAgB,EAAE,SA1BH;AA2BfC,MAAAA,iBAAiB,EAAE,YA3BJ;AA4BfC,MAAAA,SAAS,EAAE,QA5BI;AA6BfC,MAAAA,KAAK,EAAE,IA7BQ;AA8BfC,MAAAA,IAAI,EAAE,IA9BS;AA+BfC,MAAAA,QAAQ,EAAE,MA/BK;AAgCfC,MAAAA,iBAAiB,EAAE,MAhCJ;AAiCfC,MAAAA,QAAQ,EAAE,SAjCK;AAkCfC,MAAAA,SAAS,EAAE,YAlCI;AAmCfC,MAAAA,SAAS,EAAE,QAnCI;AAoCfC,MAAAA,OAAO,EAAE,GApCM;AAqCf7c,MAAAA,gBAAgB,EAAE,UArCH;AAsCf8c,MAAAA,oBAAoB,EAAE,IAtCP;AAuCfC,MAAAA,EAAE,EAAE,GAvCW;AAwCfC,MAAAA,GAAG,EAAE,IAxCU;AAyCfC,MAAAA,EAAE,EAAE,GAzCW;AA0CfC,MAAAA,GAAG,EAAE,IA1CU;AA2CfC,MAAAA,oBAAoB,EAAE,IA3CP;AA4CfC,MAAAA,kBAAkB,EAAE,IA5CL;AA6CfC,MAAAA,WAAW,EAAE,SA7CE;AA8CfC,MAAAA,SAAS,EAAE,OA9CI;AA+CfC,MAAAA,oBAAoB,EAAE,MA/CP;AAgDfC,MAAAA,mBAAmB,EAAE,GAhDN;AAiDfC,MAAAA,sBAAsB,EAAE,IAjDT;AAkDfC,MAAAA,yBAAyB,EAAE,GAlDZ;AAmDfC,MAAAA,iBAAiB,EAAE,0BAnDJ;AAoDfC,MAAAA,gBAAgB,EAAE,UApDH;AAqDfC,MAAAA,eAAe,EAAE,mBArDF;AAsDfC,MAAAA,mBAAmB,EAAE,mBAtDN;AAuDfC,MAAAA,sBAAsB,EAAE,UAvDT;AAwDfC,MAAAA,iBAAiB,EAAE,sBAxDJ;AAyDfC,MAAAA,sBAAsB,EAAE,UAzDT;AA0DfC,MAAAA,iBAAiB,EAAE,sBA1DJ;AA2DfC,MAAAA,gBAAgB,EAAE,QA3DH;AA4DfC,MAAAA,sBAAsB,EAAE,UA5DT;AA6DfC,MAAAA,wBAAwB,EAAE,iBA7DX;AA8DfC,MAAAA,oBAAoB,EAAE,iBA9DP;AA+DfC,MAAAA,WAAW,EAAE,uBA/DE;AAgEfC,MAAAA,WAAW,EAAE,wBAhEE;AAiEfC,MAAAA,yBAAyB,EAAE,sBAjEZ;AAkEfC,MAAAA,yBAAyB,EAAE,sBAlEZ;AAmEfC,MAAAA,0BAA0B,EAAE,sBAnEb;AAoEfC,MAAAA,6BAA6B,EAAE,sBApEhB;AAqEfC,MAAAA,6BAA6B,EAAE,sBArEhB;AAsEfC,MAAAA,8BAA8B,EAAE,sBAtEjB;AAuEfC,MAAAA,iBAAiB,EAAE,qBAvEJ;AAwEfC,MAAAA,0BAA0B,EAAE,qBAxEb;AAyEfC,MAAAA,0BAA0B,EAAE,qBAzEb;AA0EfC,MAAAA,2BAA2B,EAAE,qBA1Ed;AA2EfC,MAAAA,oBAAoB,EAAE,qBA3EP;AA4EfC,MAAAA,eAAe,EAAE,sBA5EF;AA6EfC,MAAAA,gBAAgB,EAAE,MA7EH;AA8EfC,MAAAA,WAAW,EAAE,MA9EE;AA+EfC,MAAAA,SAAS,EAAE,MA/EI;AAgFfC,MAAAA,OAAO,EAAE,MAhFM;AAiFfC,MAAAA,aAAa,EAAE,MAjFA;AAkFf5V,MAAAA,SAAS,EAAE,MAlFI;AAmFf6V,MAAAA,eAAe,EAAE,OAnFF;AAoFfC,MAAAA,eAAe,EAAE,OApFF;AAqFfC,MAAAA,gBAAgB,EAAE,OArFH;AAsFfC,MAAAA,WAAW,EAAE,OAtFE;AAuFfC,MAAAA,WAAW,EAAE,OAvFE;AAwFfC,MAAAA,YAAY,EAAE,OAxFC;AAyFfC,MAAAA,YAAY,EAAE,MAzFC;AA0FfC,MAAAA,YAAY,EAAE,MA1FC;AA2FfC,MAAAA,aAAa,EAAE,MA3FA;AA4FfC,MAAAA,YAAY,EAAE,MA5FC;AA6FfC,MAAAA,WAAW,EAAE,MA7FE;AA8FfC,MAAAA,cAAc,EAAE,cA9FD;AA+FfC,MAAAA,YAAY,EAAE,MA/FC;AAgGfC,MAAAA,kBAAkB,EAAE,SAhGL;AAiGfC,MAAAA,kBAAkB,EAAE,SAjGL;AAkGfC,MAAAA,mBAAmB,EAAE,SAlGN;AAmGfC,MAAAA,sBAAsB,EAAE,SAnGT;AAoGfC,MAAAA,sBAAsB,EAAE,SApGT;AAqGfC,MAAAA,uBAAuB,EAAE,SArGV;AAsGfC,MAAAA,mBAAmB,EAAE,QAtGN;AAuGfC,MAAAA,mBAAmB,EAAE,QAvGN;AAwGfC,MAAAA,oBAAoB,EAAE,QAxGP;AAyGfC,MAAAA,mBAAmB,EAAE,QAzGN;AA0GfC,MAAAA,iBAAiB,EAAE,mBA1GJ;AA2GfC,MAAAA,mBAAmB,EAAE,MA3GN;AA4GfC,MAAAA,oBAAoB,EAAE,OA5GP;AA6GfC,MAAAA,aAAa,EAAE,qBA7GA;AA8GfC,MAAAA,aAAa,EAAE,qBA9GA;AA+Gf9f,MAAAA,QAAQ,EAAE;AA/GK,KAxyBb;AAy5BJ;AACA7C,IAAAA,SAAS,EAAE;AACT4iB,MAAAA,YAAY,EAAE,MADL;AAETC,MAAAA,YAAY,EAAE,MAFL;AAGTC,MAAAA,QAAQ,EAAE,MAHD;AAITtlB,MAAAA,IAAI,EAAE,MAJG;AAKTsH,MAAAA,UAAU,EAAE,MALH;AAMTie,MAAAA,SAAS,EAAE,QANF;AAOTC,MAAAA,OAAO,EAAE,QAPA;AAQTC,MAAAA,UAAU,EAAE,SARH;AAST1jB,MAAAA,MAAM,EAAE,IATC;AAUT2jB,MAAAA,SAAS,EAAE,IAVF;AAWTC,MAAAA,MAAM,EAAE,IAXC;AAYTC,MAAAA,GAAG,EAAE,MAZI;AAaTC,MAAAA,WAAW,EAAE,KAbJ;AAcTC,MAAAA,SAAS,EAAE,KAdF;AAeTC,MAAAA,WAAW,EAAE,IAfJ;AAgBTC,MAAAA,UAAU,EAAE,MAhBH;AAiBT9T,MAAAA,IAAI,EAAE,IAjBG;AAkBT+T,MAAAA,aAAa,EAAE,UAlBN;AAmBTC,MAAAA,iBAAiB,EAAE,cAnBV;AAoBTC,MAAAA,SAAS,EAAE,MApBF;AAqBTC,MAAAA,cAAc,EAAE,YArBP;AAsBTC,MAAAA,kBAAkB,EAAE,gBAtBX;AAuBTC,MAAAA,UAAU,EAAE,QAvBH;AAwBTC,MAAAA,UAAU,EAAE,OAxBH;AAyBTC,MAAAA,uBAAuB,EAAE,UAzBhB;AA0BTC,MAAAA,gBAAgB,EAAE,aA1BT;AA2BTC,MAAAA,oBAAoB,EAAE,iBA3Bb;AA4BTC,MAAAA,YAAY,EAAE,SA5BL;AA6BTC,MAAAA,gBAAgB,EAAE,SA7BT;AA8BTC,MAAAA,mBAAmB,EAAE,QA9BZ;AA+BTC,MAAAA,kBAAkB,EAAE,QA/BX;AAgCTC,MAAAA,IAAI,EAAE,IAhCG;AAiCTC,MAAAA,iBAAiB,EAAE,OAjCV;AAkCTC,MAAAA,kBAAkB,EAAE,OAlCX;AAmCTC,MAAAA,UAAU,EAAE,MAnCH;AAoCTnc,MAAAA,UAAU,EAAE,MApCH;AAqCToc,MAAAA,YAAY,EAAE,MArCL;AAsCTC,MAAAA,eAAe,EAAE,MAtCR;AAuCTC,MAAAA,UAAU,EAAE,OAvCH;AAwCTC,MAAAA,cAAc,EAAE,MAxCP;AAyCTC,MAAAA,eAAe,EAAE,OAzCR;AA0CTC,MAAAA,YAAY,EAAE,QA1CL;AA2CTC,MAAAA,cAAc,EAAE,MA3CP;AA4CTC,MAAAA,WAAW,EAAE,IA5CJ;AA6CTC,MAAAA,KAAK,EAAE,OA7CE;AA8CTC,MAAAA,aAAa,EAAE,OA9CN;AA+CTC,MAAAA,eAAe,EAAE,OA/CR;AAgDTC,MAAAA,gBAAgB,EAAE,MAhDT;AAiDTC,MAAAA,UAAU,EAAE,MAjDH;AAkDTC,MAAAA,UAAU,EAAE,MAlDH;AAmDTC,MAAAA,WAAW,EAAE,MAnDJ;AAoDTvQ,MAAAA,MAAM,EAAE,IApDC;AAqDTC,MAAAA,KAAK,EAAE,IArDE;AAsDTuQ,MAAAA,QAAQ,EAAE,KAtDD;AAuDTC,MAAAA,yBAAyB,EAAE,iBAvDlB;AAwDTC,MAAAA,4BAA4B,EAAE,kBAxDrB;AAyDTC,MAAAA,wBAAwB,EAAE,aAzDjB;AA0DTC,MAAAA,2BAA2B,EAAE,aA1DpB;AA2DTC,MAAAA,wBAAwB,EAAE,aA3DjB;AA4DTC,MAAAA,qBAAqB,EAAE,eA5Dd;AA6DTC,MAAAA,+BAA+B,EAAE,mBA7DxB;AA8DTC,MAAAA,WAAW,EAAE,MA9DJ;AA+DTC,MAAAA,MAAM,EAAE,KA/DC;AAgETC,MAAAA,MAAM,EAAE,KAhEC;AAiETC,MAAAA,OAAO,EAAE,KAjEA;AAkETC,MAAAA,SAAS,EAAE,KAlEF;AAmETC,MAAAA,QAAQ,EAAE,KAnED;AAoETC,MAAAA,MAAM,EAAE,KApEC;AAqETC,MAAAA,QAAQ,EAAE,KArED;AAsETC,MAAAA,6BAA6B,EAAE,WAtEtB;AAuETC,MAAAA,mBAAmB,EAAE,WAvEZ;AAwETC,MAAAA,mBAAmB,EAAE,WAxEZ;AAyETC,MAAAA,oBAAoB,EAAE,SAzEb;AA0ETC,MAAAA,kBAAkB,EAAE,SA1EX;AA2ETta,MAAAA,oBAAoB,EAAE,OA3Eb;AA4ETua,MAAAA,sBAAsB,EAAE,SA5Ef;AA6ETC,MAAAA,sBAAsB,EAAE,SA7Ef;AA8ETC,MAAAA,uBAAuB,EAAE,SA9EhB;AA+ETC,MAAAA,uBAAuB,EAAE,OA/EhB;AAgFTC,MAAAA,gCAAgC,EAAE,SAhFzB;AAiFTC,MAAAA,+BAA+B,EAAE,QAjFxB;AAkFT7f,MAAAA,wBAAwB,EAAE,UAlFjB;AAmFTC,MAAAA,oBAAoB,EAAE,mBAnFb;AAoFT8H,MAAAA,wBAAwB,EAAE,UApFjB;AAqFTC,MAAAA,oBAAoB,EAAE,mBArFb;AAsFT8X,MAAAA,4BAA4B,EAAE,UAtFrB;AAuFTC,MAAAA,wBAAwB,EAAE,QAvFjB;AAwFTC,MAAAA,uBAAuB,EAAE,qBAxFhB;AAyFTC,MAAAA,+BAA+B,EAAE,mBAzFxB;AA0FTC,MAAAA,+BAA+B,EAAE,mBA1FxB;AA2FTC,MAAAA,oCAAoC,EAAE,qBA3F7B;AA4FTC,MAAAA,oCAAoC,EAAE,qBA5F7B;AA6FTC,MAAAA,qCAAqC,EAAE,qBA7F9B;AA8FTC,MAAAA,gCAAgC,EAAE,qBA9FzB;AA+FTC,MAAAA,iCAAiC,EAAE,sBA/F1B;AAgGTC,MAAAA,qBAAqB,EAAE,uBAhGd;AAiGTC,MAAAA,WAAW,EAAE,6BAjGJ;AAkGTC,MAAAA,oBAAoB,EAAE,SAlGb;AAmGTC,MAAAA,UAAU,EAAE,QAnGH;AAoGTC,MAAAA,yBAAyB,EAAE,SApGlB;AAqGTC,MAAAA,4BAA4B,EAAE,WArGrB;AAsGTC,MAAAA,8BAA8B,EAAE,UAtGvB;AAuGTC,MAAAA,aAAa,EAAE;AAvGN,KA15BP;AAmgCJ;AACAroB,IAAAA,iBAAiB,EAAE;AACjBsoB,MAAAA,cAAc,EAAE,QADC;AAEjBC,MAAAA,eAAe,EAAE,WAFA;AAGjBC,MAAAA,cAAc,EAAE,SAHC;AAIjBC,MAAAA,aAAa,EAAE,SAJE;AAKjBC,MAAAA,UAAU,EAAE,OALK;AAMjBC,MAAAA,YAAY,EAAE,UANG;AAOjBlb,MAAAA,IAAI,EAAE,IAPW;AAQjBmb,MAAAA,mBAAmB,EAAE,SARJ;AASjBC,MAAAA,kBAAkB,EAAE,OATH;AAUjBC,MAAAA,iBAAiB,EAAE,SAVF;AAWjBC,MAAAA,sBAAsB,EAAE,sBAXP;AAYjBC,MAAAA,qBAAqB,EAAE,qBAZN;AAajBC,MAAAA,IAAI,EAAE,IAbW;AAcjBC,MAAAA,eAAe,EAAE,qBAdA;AAejBC,MAAAA,gBAAgB,EAAE,mBAfD;AAgBjBC,MAAAA,gBAAgB,EAAE,QAhBD;AAiBjBC,MAAAA,mBAAmB,EAAE,uBAjBJ;AAkBjBC,MAAAA,aAAa,EAAE,UAlBE;AAmBjBC,MAAAA,mBAAmB,EAAE,eAnBJ;AAoBjBC,MAAAA,YAAY,EAAE,MApBG;AAqBjBC,MAAAA,YAAY,EAAE,QArBG;AAsBjBC,MAAAA,YAAY,EAAE,QAtBG;AAuBjBC,MAAAA,WAAW,EAAE,QAvBI;AAwBjBC,MAAAA,WAAW,EAAE,OAxBI;AAyBjBC,MAAAA,MAAM,EAAE,MAzBS;AA0BjBC,MAAAA,EAAE,EAAE,IA1Ba;AA2BjBC,MAAAA,aAAa,EAAE,QA3BE;AA4BjBC,MAAAA,WAAW,EAAE,QA5BI;AA6BjBC,MAAAA,SAAS,EAAE,MA7BM;AA8BjBC,MAAAA,WAAW,EAAE,MA9BI;AA+BjBC,MAAAA,iBAAiB,EAAE,IA/BF;AAgCjBC,MAAAA,mBAAmB,EAAE,IAhCJ;AAiCjBC,MAAAA,KAAK,EAAE,KAjCU;AAkCjBC,MAAAA,eAAe,EAAE,SAlCA;AAmCjBC,MAAAA,OAAO,EAAE,SAnCQ;AAoCjBC,MAAAA,cAAc,EAAE,QApCC;AAqCjBC,MAAAA,kBAAkB,EAAE;AArCH,KApgCf;AA2iCJ;AACAxqB,IAAAA,aAAa,EAAE;AACbkc,MAAAA,aAAa,EAAE,MADF;AAEbrT,MAAAA,QAAQ,EAAE,MAFG;AAGb4hB,MAAAA,MAAM,EAAE,MAHK;AAIbxoB,MAAAA,SAAS,EAAE,MAJE;AAKbyoB,MAAAA,UAAU,EAAE,QALC;AAMbC,MAAAA,cAAc,EAAE,OANH;AAObtH,MAAAA,WAAW,EAAE,MAPA;AAQbuH,MAAAA,eAAe,EAAE,aARJ;AASbhpB,MAAAA,SAAS,EAAE,KATE;AAUbipB,MAAAA,QAAQ,EAAE,KAVG;AAWbC,MAAAA,OAAO,EAAE,KAXI;AAYbC,MAAAA,IAAI,EAAE,kBAZO;AAabC,MAAAA,WAAW,EAAE;AAbA,KA5iCX;AA2jCJ;AACAC,IAAAA,MAAM,EAAE;AACNrZ,MAAAA,SAAS,EAAE,OADL;AAENsZ,MAAAA,UAAU,EAAE,OAFN;AAGNlgB,MAAAA,UAAU,EAAE,OAHN;AAINgH,MAAAA,UAAU,EAAE,OAJN;AAKNmZ,MAAAA,QAAQ,EAAE,OALJ;AAMNC,MAAAA,SAAS,EAAE,SANL;AAONC,MAAAA,eAAe,EAAE,WAPX;AAQNC,MAAAA,aAAa,EAAE,UART;AASNC,MAAAA,eAAe,EAAE,UATX;AAUNC,MAAAA,eAAe,EAAE,UAVX;AAWNC,MAAAA,aAAa,EAAE,UAXT;AAYNC,MAAAA,cAAc,EAAE,YAZV;AAaNC,MAAAA,oBAAoB,EAAE,cAbhB;AAcNC,MAAAA,kBAAkB,EAAE,aAdd;AAeN7Z,MAAAA,QAAQ,EAAE,OAfJ;AAgBN8Z,MAAAA,gBAAgB,EAAE,SAhBZ;AAiBNC,MAAAA,gBAAgB,EAAE;AAjBZ,KA5jCJ;AA+kCJ;AACA3rB,IAAAA,YAAY,EAAE;AACZ4rB,MAAAA,eAAe,EAAE,UADL;AAEZzuB,MAAAA,IAAI,EAAE,MAFM;AAGZ0uB,MAAAA,YAAY,EAAE,OAHF;AAIZC,MAAAA,WAAW,EAAE,OAJD;AAKZC,MAAAA,YAAY,EAAE,OALF;AAMZC,MAAAA,eAAe,EAAE,SANL;AAOZC,MAAAA,OAAO,EAAE,KAPG;AAQZC,MAAAA,SAAS,EAAE,KARC;AASZC,MAAAA,WAAW,EAAE,MATD;AAUZC,MAAAA,MAAM,EAAE,IAVI;AAWZC,MAAAA,QAAQ,EAAE,IAXE;AAYZC,MAAAA,OAAO,EAAE,IAZG;AAaZC,MAAAA,OAAO,EAAE,IAbG;AAcZC,MAAAA,gBAAgB,EAAE,OAdN;AAeZC,MAAAA,WAAW,EAAE,MAfD;AAgBZC,MAAAA,SAAS,EAAE,MAhBC;AAiBZC,MAAAA,UAAU,EAAE,OAjBA;AAkBZC,MAAAA,SAAS,EAAE,QAlBC;AAmBZC,MAAAA,MAAM,EAAE,MAnBI;AAoBZpD,MAAAA,MAAM,EAAE,IApBI;AAqBZ9rB,MAAAA,KAAK,EAAE,IArBK;AAsBZmvB,MAAAA,OAAO,EAAE,IAtBG;AAuBZ5J,MAAAA,WAAW,EAAE,IAvBD;AAwBZxa,MAAAA,QAAQ,EAAE,MAxBE;AAyBZqkB,MAAAA,UAAU,EAAE,MAzBA;AA0BZC,MAAAA,SAAS,EAAE,MA1BC;AA2BZvvB,MAAAA,UAAU,EAAE,QA3BA;AA4BZ6sB,MAAAA,MAAM,EAAE,IA5BI;AA6BZ2C,MAAAA,OAAO,EAAE,OA7BG;AA8BZC,MAAAA,aAAa,EAAE,KA9BH;AA+BZC,MAAAA,YAAY,EAAE,MA/BF;AAgCZC,MAAAA,cAAc,EAAE,UAhCJ;AAiCZC,MAAAA,eAAe,EAAE,SAjCL;AAkCZC,MAAAA,QAAQ,EAAE,KAlCE;AAmCZC,MAAAA,MAAM,EAAE,MAnCI;AAoCZC,MAAAA,QAAQ,EAAE,MApCE;AAqCZC,MAAAA,iBAAiB,EAAE,SArCP;AAsCZC,MAAAA,uBAAuB,EAAE,qBAtCb;AAuCZlO,MAAAA,WAAW,EAAE,oBAvCD;AAwCZtM,MAAAA,0BAA0B,EAAE,mBAxChB;AAyCZnX,MAAAA,kBAAkB,EAAE,mBAzCR;AA0CZ4xB,MAAAA,WAAW,EAAE,mBA1CD;AA2CZC,MAAAA,qBAAqB,EAAE,sBA3CX;AA4CZC,MAAAA,kBAAkB,EAAE,eA5CR;AA6CZC,MAAAA,QAAQ,EAAE,QA7CE;AA8CZC,MAAAA,KAAK,EAAE,MA9CK;AA+CZrpB,MAAAA,GAAG,EAAE,GA/CO;AAgDZtF,MAAAA,EAAE,EAAE,GAhDQ;AAiDZ4uB,MAAAA,WAAW,EAAE,OAjDD;AAkDZC,MAAAA,SAAS,EAAE,KAlDC;AAmDZC,MAAAA,WAAW,EAAE,SAnDD;AAoDZC,MAAAA,YAAY,EAAE,UApDF;AAqDZC,MAAAA,aAAa,EAAE,SArDH;AAsDZC,MAAAA,QAAQ,EAAE,KAtDE;AAuDZC,MAAAA,UAAU,EAAE,MAvDA;AAwDZz3B,MAAAA,MAAM,EAAE,KAxDI;AAyDZ03B,MAAAA,UAAU,EAAE;AAzDA,KAhlCV;AA2oCJ;AACAtuB,IAAAA,SAAS,EAAE;AACTuuB,MAAAA,WAAW,EAAE,KADJ;AAET72B,MAAAA,YAAY,EAAE,KAFL;AAGT82B,MAAAA,YAAY,EAAE,IAHL;AAITjrB,MAAAA,SAAS,EAAE,IAJF;AAKTipB,MAAAA,WAAW,EAAE,MALJ;AAMTC,MAAAA,SAAS,EAAE,MANF;AAOTC,MAAAA,UAAU,EAAE,OAPH;AAQT+B,MAAAA,eAAe,EAAE,MARR;AAST9d,MAAAA,WAAW,EAAE,KATJ;AAUTC,MAAAA,WAAW,EAAE,KAVJ;AAWT9T,MAAAA,YAAY,EAAE,MAXL;AAYTyL,MAAAA,cAAc,EAAE,MAZP;AAaTmmB,MAAAA,kBAAkB,EAAE,QAbX;AAcTC,MAAAA,UAAU,EAAE,MAdH;AAeTC,MAAAA,SAAS,EAAE,MAfF;AAgBTC,MAAAA,OAAO,EAAE,MAhBA;AAiBTC,MAAAA,kBAAkB,EAAE,QAjBX;AAkBTtxB,MAAAA,UAAU,EAAE,QAlBH;AAmBT6sB,MAAAA,MAAM,EAAE,IAnBC;AAoBT0E,MAAAA,YAAY,EAAE,MApBL;AAqBTC,MAAAA,OAAO,EAAE,QArBA;AAsBTC,MAAAA,KAAK,EAAE,MAtBE;AAuBTC,MAAAA,IAAI,EAAE,MAvBG;AAwBTjM,MAAAA,WAAW,EAAE,MAxBJ;AAyBTnV,MAAAA,YAAY,EAAE,MAzBL;AA0BTqhB,MAAAA,QAAQ,EAAE,MA1BD;AA2BTC,MAAAA,WAAW,EAAE,MA3BJ;AA4BTC,MAAAA,YAAY,EAAE,KA5BL;AA6BTC,MAAAA,aAAa,EAAE,KA7BN;AA8BTC,MAAAA,UAAU,EAAE,IA9BH;AA+BTC,MAAAA,iBAAiB,EAAE,MA/BV;AAgCTC,MAAAA,WAAW,EAAE,MAhCJ;AAiCTC,MAAAA,MAAM,EAAE,MAjCC;AAkCTC,MAAAA,qBAAqB,EAAE,iBAlCd;AAmCTC,MAAAA,qBAAqB,EAAE,iBAnCd;AAoCTC,MAAAA,qBAAqB,EAAE,iBApCd;AAqCTC,MAAAA,kBAAkB,EAAE,YArCX;AAsCTC,MAAAA,mBAAmB,EAAE,YAtCZ;AAuCTC,MAAAA,YAAY,EAAE,WAvCL;AAwCTC,MAAAA,gBAAgB,EAAE,aAxCT;AAyCT3X,MAAAA,cAAc,EAAE,WAzCP;AA0CT4X,MAAAA,qBAAqB,EAAE,YA1Cd;AA2CTC,MAAAA,mBAAmB,EAAE,UA3CZ;AA4CTC,MAAAA,MAAM,EAAE,MA5CC;AA6CTC,MAAAA,UAAU,EAAE,MA7CH;AA8CT7G,MAAAA,MAAM,EAAE,IA9CC;AA+CT8G,MAAAA,SAAS,EAAE,MA/CF;AAgDTC,MAAAA,KAAK,EAAE,UAhDE;AAiDTC,MAAAA,MAAM,EAAE,QAjDC;AAkDTC,MAAAA,SAAS,EAAE,WAlDF;AAmDTC,MAAAA,WAAW,EAAE,aAnDJ;AAoDTC,MAAAA,SAAS,EAAE,WApDF;AAqDTC,MAAAA,GAAG,EAAE,KArDI;AAsDTC,MAAAA,UAAU,EAAE,QAtDH;AAuDTC,MAAAA,QAAQ,EAAE,MAvDD;AAwDTC,MAAAA,UAAU,EAAE,MAxDH;AAyDTC,MAAAA,MAAM,EAAE,MAzDC;AA0DTC,MAAAA,UAAU,EAAE,MA1DH;AA2DTC,MAAAA,eAAe,EAAE,MA3DR;AA4DTC,MAAAA,SAAS,EAAE,KA5DF;AA6DTrE,MAAAA,UAAU,EAAE,MA7DH;AA8DTsE,MAAAA,YAAY,EAAE,KA9DL;AA+DTC,MAAAA,cAAc,EAAE,KA/DP;AAgETC,MAAAA,UAAU,EAAE,KAhEH;AAiETjE,MAAAA,QAAQ,EAAE;AAjED,KA5oCP;AA+sCJ;AACAntB,IAAAA,YAAY,EAAE;AACZ7C,MAAAA,KAAK,EAAE,IADK;AAEZk0B,MAAAA,GAAG,EAAE,MAFO;AAGZC,MAAAA,KAAK,EAAE,KAHK;AAIZC,MAAAA,YAAY,EAAE,KAJF;AAKZC,MAAAA,GAAG,EAAE,KALO;AAMZC,MAAAA,GAAG,EAAE,KANO;AAOZC,MAAAA,SAAS,EAAE,MAPC;AAQZC,MAAAA,IAAI,EAAE,IARM;AASZ5O,MAAAA,WAAW,EAAE,MATD;AAUZ6O,MAAAA,cAAc,EAAE,OAVJ;AAWZC,MAAAA,kBAAkB,EAAE,IAXR;AAYZC,MAAAA,eAAe,EAAE,IAZL;AAaZC,MAAAA,eAAe,EAAE,MAbL;AAcZC,MAAAA,sBAAsB,EAAE,MAdZ;AAeZC,MAAAA,sBAAsB,EAAE,IAfZ;AAgBZC,MAAAA,qBAAqB,EAAE,MAhBX;AAiBZC,MAAAA,cAAc,EAAE,MAjBJ;AAkBZC,MAAAA,0BAA0B,EAAE,6BAlBhB;AAmBZC,MAAAA,wBAAwB,EAAE,oCAnBd;AAoBZC,MAAAA,uBAAuB,EAAE,oCApBb;AAqBZC,MAAAA,0BAA0B,EAAE,aArBhB;AAsBZC,MAAAA,wBAAwB,EAAE,iCAtBd;AAuBZC,MAAAA,cAAc,EAAE,eAvBJ;AAwBZC,MAAAA,YAAY,EAAE,aAxBF;AAyBZC,MAAAA,YAAY,EAAE,eAzBF;AA0BZC,MAAAA,gBAAgB,EAAE,YA1BN;AA2BZC,MAAAA,gBAAgB,EAAE,YA3BN;AA4BZC,MAAAA,uBAAuB,EAAE,YA5Bb;AA6BZC,MAAAA,0BAA0B,EAAE,UA7BhB;AA8BZC,MAAAA,+BAA+B,EAAE,UA9BrB;AA+BZC,MAAAA,4BAA4B,EAAE,YA/BlB;AAgCZC,MAAAA,gCAAgC,EAAE,aAhCtB;AAiCZC,MAAAA,0BAA0B,EAAE,WAjChB;AAkCZC,MAAAA,8BAA8B,EAAE,aAlCpB;AAmCZC,MAAAA,+BAA+B,EAAE,aAnCrB;AAoCZC,MAAAA,0BAA0B,EAAE,qCApChB;AAqCZC,MAAAA,qBAAqB,EAAE,qBArCX;AAsCZC,MAAAA,qBAAqB,EAAE,UAtCX;AAuCZC,MAAAA,8BAA8B,EAAE,wBAvCpB;AAwCZC,MAAAA,2CAA2C,EAAE,wBAxCjC;AAyCZC,MAAAA,gCAAgC,EAAE,sBAzCtB;AA0CZC,MAAAA,8BAA8B,EAAE,sBA1CpB;AA2CZC,MAAAA,8BAA8B,EAAE,2BA3CpB;AA4CZC,MAAAA,kCAAkC,EAAE,+BA5CxB;AA6CZC,MAAAA,6BAA6B,EAAE,WA7CnB;AA8CZC,MAAAA,oCAAoC,EAAE,eA9C1B;AA+CZC,MAAAA,0BAA0B,EAAE,OA/ChB;AAgDZC,MAAAA,qBAAqB,EAAE,MAhDX;AAiDZC,MAAAA,4BAA4B,EAAE,UAjDlB;AAkDZC,MAAAA,4BAA4B,EAAE,iBAlDlB;AAmDZC,MAAAA,4BAA4B,EAAE,iBAnDlB;AAoDZC,MAAAA,wBAAwB,EAAE,UApDd;AAqDZC,MAAAA,8BAA8B,EAAE,gBArDpB;AAsDZC,MAAAA,qBAAqB,EAAE,WAtDX;AAuDZ93B,MAAAA,KAAK,EAAE;AAvDK,KAhtCV;AAywCJ;AACA+3B,IAAAA,MAAM,EAAE;AACNC,MAAAA,OAAO,EAAE;AADH,KA1wCJ;AA6wCJ;AACAz0B,IAAAA,eAAe,EAAE;AACfjD,MAAAA,IAAI,EAAE,MADS;AAEf23B,MAAAA,OAAO,EAAE,IAFM;AAGfrL,MAAAA,MAAM,EAAE,IAHO;AAIfsL,MAAAA,OAAO,EAAE;AAJM,KA9wCb;AAoxCJ;AACAC,IAAAA,IAAI,EAAE;AACJjhC,MAAAA,MAAM,EAAE;AACNkhC,QAAAA,WAAW,EAAC,MADN;AAENC,QAAAA,gBAAgB,EAAC,OAFX;AAGNC,QAAAA,UAAU,EAAE,UAHN;AAINC,QAAAA,eAAe,EAAC,MAJV;AAKNC,QAAAA,OAAO,EAAE,MALH;AAMNC,QAAAA,MAAM,EAAE,MANF;AAON/T,QAAAA,YAAY,EAAE,MAPR;AAQNgU,QAAAA,aAAa,EAAE,MART;AASN3+B,QAAAA,OAAO,EAAE,IATH;AAUNC,QAAAA,MAAM,EAAE,IAVF;AAWNjB,QAAAA,+BAA+B,EAAE,UAX3B;AAYN4R,QAAAA,MAAM,EAAE,MAZF;AAaNguB,QAAAA,YAAY,EAAE,SAbR;AAcNC,QAAAA,GAAG,EAAE,IAdC;AAeNz+B,QAAAA,IAAI,EAAE,IAfA;AAgBNP,QAAAA,MAAM,EAAE,IAhBF;AAiBNi/B,QAAAA,SAAS,EAAE,MAjBL;AAkBNC,QAAAA,UAAU,EAAE,MAlBN;AAmBNC,QAAAA,OAAO,EAAE,MAnBH;AAoBNC,QAAAA,QAAQ,EAAE,MApBJ;AAqBN1lB,QAAAA,GAAG,EAAE,OArBC;AAsBNxS,QAAAA,KAAK,EAAE,MAtBD;AAuBN4S,QAAAA,OAAO,EAAE,MAvBH;AAwBNulB,QAAAA,OAAO,EAAE,QAxBH;AAyBNC,QAAAA,SAAS,EAAE,MAzBL;AA0BNC,QAAAA,SAAS,EAAE,MA1BL;AA2BNC,QAAAA,UAAU,EAAE,UA3BN;AA4BNC,QAAAA,SAAS,EAAE,QA5BL;AA6BNC,QAAAA,QAAQ,EAAE,QA7BJ;AA8BNC,QAAAA,mBAAmB,EAAE,OA9Bf;AA+BNC,QAAAA,WAAW,EAAE,UA/BP;AAgCNC,QAAAA,aAAa,EAAE,UAhCT;AAiCNC,QAAAA,mBAAmB,EAAE,gBAjCf;AAkCN9M,QAAAA,MAAM,EAAE,IAlCF;AAmCN+M,QAAAA,MAAM,EAAE,IAnCF;AAoCNC,QAAAA,iBAAiB,EAAE,UApCb;AAqCNC,QAAAA,UAAU,EAAE,OArCN;AAsCNC,QAAAA,gBAAgB,EAAE,QAtCZ;AAuCNC,QAAAA,WAAW,EAAE,MAvCP;AAwCNC,QAAAA,UAAU,EAAE,QAxCN;AAyCNC,QAAAA,iBAAiB,EAAE,SAzCb;AA0CNC,QAAAA,cAAc,EAAE,UA1CV;AA2CNn6B,QAAAA,WAAW,EAAE,IA3CP;AA4CNo6B,QAAAA,cAAc,EAAE,QA5CV;AA6CNlG,QAAAA,UAAU,EAAE,QA7CN;AA8CNmG,QAAAA,SAAS,EAAE,SA9CL;AA+CNC,QAAAA,MAAM,EAAE,IA/CF;AAgDNr6B,QAAAA,KAAK,EAAE,IAhDD;AAiDN5G,QAAAA,MAAM,EAAE,GAjDF;AAkDNkhC,QAAAA,iBAAiB,EAAE,SAlDb;AAmDNC,QAAAA,cAAc,EAAE,MAnDV;AAoDNC,QAAAA,MAAM,EAAE,IApDF;AAqDNC,QAAAA,wBAAwB,EAAE,SArDpB;AAsDNC,QAAAA,mCAAmC,EAAE,UAtD/B;AAuDNC,QAAAA,SAAS,EAAE,MAvDL;AAwDNC,QAAAA,SAAS,EAAE;AAxDL,OADJ;AA2DJC,MAAAA,GAAG,EAAE;AACHC,QAAAA,kBAAkB,EAAC,MADhB;AAEHC,QAAAA,mBAAmB,EAAE;AAFlB,OA3DD;AA+DJC,MAAAA,GAAG,EAAE;AACHxP,QAAAA,aAAa,EAAC;AADX,OA/DD;AAkEJyP,MAAAA,MAAM,EAAE;AACNvgC,QAAAA,SAAS,EAAE,IADL;AAEN4F,QAAAA,IAAI,EAAE,MAFA;AAGNyH,QAAAA,IAAI,EAAE,MAHA;AAIN4C,QAAAA,MAAM,EAAE,SAJF;AAKNuwB,QAAAA,WAAW,EAAE,OALP;AAMN9hC,QAAAA,MAAM,EAAE,GANF;AAON+hC,QAAAA,IAAI,EAAE,IAPA;AAQNtwB,QAAAA,MAAM,EAAE,IARF;AASNuwB,QAAAA,eAAe,EAAE,QATX;AAUNp7B,QAAAA,KAAK,EAAE,IAVD;AAWNq7B,QAAAA,kBAAkB,EAAE,QAXd;AAYNC,QAAAA,YAAY,EAAE,OAZR;AAaNrsB,QAAAA,KAAK,EAAE,IAbD;AAcNssB,QAAAA,IAAI,EAAE;AAdA,OAlEJ;AAkFJC,MAAAA,GAAG,EAAE;AACH9gC,QAAAA,SAAS,EAAE,IADR;AAEH+gC,QAAAA,gBAAgB,EAAE,QAFf;AAGHC,QAAAA,QAAQ,EAAE,OAHP;AAIH9jB,QAAAA,MAAM,EAAE,IAJL;AAKHyO,QAAAA,WAAW,EAAE,IALV;AAMHsV,QAAAA,cAAc,EAAE,MANb;AAOHlhC,QAAAA,IAAI,EAAE,IAPH;AAQHuJ,QAAAA,WAAW,EAAE,MARV;AASH43B,QAAAA,aAAa,EAAE,MATZ;AAUHC,QAAAA,IAAI,EAAE,KAVH;AAWH1sB,QAAAA,aAAa,EAAE,MAXZ;AAYHF,QAAAA,KAAK,EAAE,OAZJ;AAaHnE,QAAAA,UAAU,EAAE,OAbT;AAeHpG,QAAAA,QAAQ,EAAE,KAfP;AAgBHo3B,QAAAA,UAAU,EAAE,MAhBT;AAiBHC,QAAAA,eAAe,EAAE,MAjBd;AAkBHC,QAAAA,SAAS,EAAE,OAlBR;AAmBHC,QAAAA,UAAU,EAAE,MAnBT;AAoBHC,QAAAA,QAAQ,EAAE,OApBP;AAqBHC,QAAAA,mBAAmB,EAAE,QArBlB;AAsBHC,QAAAA,0BAA0B,EAAE,WAtBzB;AAuBHC,QAAAA,QAAQ,EAAE,IAvBP;AAwBHC,QAAAA,cAAc,EAAE,MAxBb;AAyBHC,QAAAA,YAAY,EAAE,MAzBX;AA0BHC,QAAAA,UAAU,EAAE,MA1BT;AA2BHC,QAAAA,YAAY,EAAE,MA3BX;AA6BHC,QAAAA,iBAAiB,EAAE,UA7BhB;AA8BHC,QAAAA,mBAAmB,EAAE,SA9BlB;AA+BHC,QAAAA,cAAc,EAAE,MA/Bb;AAgCHC,QAAAA,QAAQ,EAAE,IAhCP;AAiCHC,QAAAA,YAAY,EAAE,MAjCX;AAkCHC,QAAAA,QAAQ,EAAE,KAlCP;AAmCHC,QAAAA,OAAO,EAAE,MAnCN;AAoCHC,QAAAA,OAAO,EAAE,MApCN;AAqCHh4B,QAAAA,SAAS,EAAE,MArCR;AAsCHi4B,QAAAA,cAAc,EAAE,MAtCb;AAuCHC,QAAAA,IAAI,EAAE,IAvCH;AAwCH5xB,QAAAA,OAAO,EAAE,IAxCN;AAyCHoV,QAAAA,IAAI,EAAE,IAzCH;AA0CHyc,QAAAA,UAAU,EAAE,OA1CT;AA2CH5C,QAAAA,MAAM,EAAE,IA3CL;AA4CH6C,QAAAA,MAAM,EAAE,MA5CL;AA6CHC,QAAAA,UAAU,EAAE,QA7CT;AA8CHC,QAAAA,OAAO,EAAE,MA9CN;AA+CHC,QAAAA,sBAAsB,EAAE,QA/CrB;AAgDHC,QAAAA,GAAG,EAAE,IAhDF;AAiDHC,QAAAA,KAAK,EAAE,IAjDJ;AAkDHC,QAAAA,eAAe,EAAE,MAlDd;AAmDHC,QAAAA,aAAa,EAAE,MAnDZ;AAoDHC,QAAAA,cAAc,EAAE,MApDb;AAqDHC,QAAAA,aAAa,EAAE,MArDZ;AAsDHC,QAAAA,OAAO,EAAE,OAtDN;AAuDHC,QAAAA,QAAQ,EAAE,MAvDP;AAwDHC,QAAAA,WAAW,EAAE,QAxDV;AAyDHC,QAAAA,WAAW,EAAE,KAzDV;AA0DHC,QAAAA,UAAU,EAAE,KA1DT;AA2DHC,QAAAA,WAAW,EAAE,MA3DV;AA4DHC,QAAAA,UAAU,EAAE,MA5DT;AA6DHC,QAAAA,MAAM,EAAE,KA7DL;AA8DHC,QAAAA,GAAG,EAAE,GA9DF;AA+DHC,QAAAA,MAAM,EAAE,IA/DL;AAgEHC,QAAAA,SAAS,EAAE,MAhER;AAiEHC,QAAAA,UAAU,EAAE,MAjET;AAkEHC,QAAAA,UAAU,EAAE,MAlET;AAmEHC,QAAAA,YAAY,EAAE;AAnEX,OAlFD;AAwJJC,MAAAA,GAAG,EAAE;AACHnkC,QAAAA,SAAS,EAAE,IADR;AAEHokC,QAAAA,eAAe,EAAE,MAFd;AAGHC,QAAAA,mBAAmB,EAAE,OAHlB;AAIH/C,QAAAA,SAAS,EAAE,KAJR;AAKHlxB,QAAAA,UAAU,EAAE,OALT;AAMHk0B,QAAAA,mBAAmB,EAAE,MANlB;AAOHC,QAAAA,+BAA+B,EAAE,QAP9B;AAQH/C,QAAAA,QAAQ,EAAE,OARP;AASHgD,QAAAA,cAAc,EAAE,MATb;AAUHC,QAAAA,YAAY,EAAE,MAVX;AAWHC,QAAAA,cAAc,EAAE,MAXb;AAYHC,QAAAA,MAAM,EAAE,OAZL;AAaHC,QAAAA,MAAM,EAAE,OAbL;AAcHvK,QAAAA,GAAG,EAAE,KAdF;AAeHvf,QAAAA,WAAW,EAAE,IAfV;AAgBH+pB,QAAAA,0BAA0B,EAAE,SAhBzB;AAiBHC,QAAAA,gBAAgB,EAAE,WAjBf;AAkBH30B,QAAAA,MAAM,EAAE,IAlBL;AAmBH40B,QAAAA,MAAM,EAAE,IAnBL;AAoBHC,QAAAA,mBAAmB,EAAE,MApBlB;AAqBHC,QAAAA,qBAAqB,EAAE,MArBpB;AAsBHC,QAAAA,eAAe,EAAE,MAtBd;AAuBHC,QAAAA,iBAAiB,EAAE,MAvBhB;AAwBHC,QAAAA,SAAS,EAAE,QAxBR;AAyBHC,QAAAA,SAAS,EAAE,QAzBR;AA0BHpf,QAAAA,IAAI,EAAE,IA1BH;AA2BHqc,QAAAA,OAAO,EAAE,MA3BN;AA4BHC,QAAAA,OAAO,EAAE,MA5BN;AA6BHh4B,QAAAA,SAAS,EAAE,MA7BR;AA8BHi4B,QAAAA,cAAc,EAAE,MA9Bb;AA+BH3xB,QAAAA,OAAO,EAAE,IA/BN;AAgCHyK,QAAAA,GAAG,EAAE,KAhCF;AAiCHE,QAAAA,GAAG,EAAE,KAjCF;AAkCH8pB,QAAAA,OAAO,EAAE,IAlCN;AAmCHC,QAAAA,kBAAkB,EAAE,MAnCjB;AAoCH9C,QAAAA,IAAI,EAAE,IApCH;AAqCH+C,QAAAA,eAAe,EAAE,MArCd;AAsCHC,QAAAA,MAAM,EAAE,GAtCL;AAuCHC,QAAAA,WAAW,EAAE,KAvCV;AAwCHxC,QAAAA,aAAa,EAAE,MAxCZ;AAyCHyC,QAAAA,6BAA6B,EAAE,QAzC5B;AA0CHpM,QAAAA,UAAU,EAAE,MA1CT;AA2CHoK,QAAAA,UAAU,EAAE,MA3CT;AA4CHC,QAAAA,MAAM,EAAE,KA5CL;AA6CHC,QAAAA,GAAG,EAAE,GA7CF;AA8CHC,QAAAA,MAAM,EAAE,IA9CL;AA+CHC,QAAAA,SAAS,EAAE,MA/CR;AAgDHC,QAAAA,UAAU,EAAE,MAhDT;AAiDHC,QAAAA,UAAU,EAAE,MAjDT;AAkDHC,QAAAA,YAAY,EAAE,OAlDX;AAmDH0B,QAAAA,WAAW,EAAE,MAnDV;AAoDHC,QAAAA,uBAAuB,EAAE,UApDtB;AAqDHC,QAAAA,4BAA4B,EAAE,WArD3B;AAsDHC,QAAAA,uCAAuC,EAAE,kBAtDtC;AAuDHC,QAAAA,oBAAoB,EAAE,MAvDnB;AAwDHC,QAAAA,uBAAuB,EAAE,OAxDtB;AAyDHC,QAAAA,KAAK,EAAE,GAzDJ;AA0DHC,QAAAA,KAAK,EAAE,MA1DJ;AA2DHC,QAAAA,QAAQ,EAAE,IA3DP;AA4DHC,QAAAA,QAAQ,EAAE,IA5DP;AA6DHC,QAAAA,WAAW,EAAE,IA7DV;AA8DHC,QAAAA,aAAa,EAAE,IA9DZ;AA+DHnrB,QAAAA,SAAS,EAAE,IA/DR;AAgEHorB,QAAAA,eAAe,EAAE,IAhEd;AAiEHC,QAAAA,eAAe,EAAE,KAjEd;AAkEHC,QAAAA,gBAAgB,EAAE,OAlEf;AAmEHC,QAAAA,cAAc,EAAE,MAnEb;AAoEHC,QAAAA,eAAe,EAAE,MApEd;AAqEHC,QAAAA,YAAY,EAAE,MArEX;AAsEHC,QAAAA,aAAa,EAAE,MAtEZ;AAuEHC,QAAAA,cAAc,EAAE,MAvEb;AAwEHC,QAAAA,iBAAiB,EAAE,MAxEhB;AAyEHC,QAAAA,gBAAgB,EAAE,MAzEf;AA0EHC,QAAAA,oBAAoB,EAAE,OA1EnB;AA2EHC,QAAAA,mBAAmB,EAAE,OA3ElB;AA4EHC,QAAAA,oBAAoB,EAAE,OA5EnB;AA6EHC,QAAAA,mBAAmB,EAAE,OA7ElB;AA8EHC,QAAAA,UAAU,EAAE,UA9ET;AA+EHC,QAAAA,OAAO,EAAE;AA/EN,OAxJD;AAyOJz+B,MAAAA,MAAM,EAAE;AACN9I,QAAAA,SAAS,EAAE,IADL;AAENwnC,QAAAA,OAAO,EAAE,MAFH;AAGN/G,QAAAA,IAAI,EAAE,IAHA;AAINgH,QAAAA,KAAK,EAAE,IAJD;AAKNC,QAAAA,GAAG,EAAE,IALC;AAMNC,QAAAA,UAAU,EAAE,IANN;AAONC,QAAAA,GAAG,EAAE,IAPC;AAQN1V,QAAAA,MAAM,EAAE,IARF;AASN2V,QAAAA,OAAO,EAAE,IATH;AAUN5I,QAAAA,MAAM,EAAE,IAVF;AAWN9uB,QAAAA,MAAM,EAAE,IAXF;AAYN23B,QAAAA,kBAAkB,EAAE,SAZd;AAaNC,QAAAA,GAAG,EAAE,OAbC;AAcN1N,QAAAA,GAAG,EAAE,KAdC;AAeN4G,QAAAA,cAAc,EAAE,MAfV;AAgBN+G,QAAAA,IAAI,EAAE,IAhBA;AAiBN1+B,QAAAA,WAAW,EAAE,MAjBP;AAkBN2+B,QAAAA,OAAO,EAAE,IAlBH;AAmBNC,QAAAA,UAAU,EAAE,IAnBN;AAoBNn+B,QAAAA,cAAc,EAAE,MApBV;AAqBNP,QAAAA,QAAQ,EAAE,IArBJ;AAsBNM,QAAAA,KAAK,EAAE,IAtBD;AAuBNq4B,QAAAA,QAAQ,EAAE,IAvBJ;AAwBNv7B,QAAAA,UAAU,EAAE,KAxBN;AAyBNuhC,QAAAA,KAAK,EAAE,IAzBD;AA0BNxI,QAAAA,MAAM,EAAE,KA1BF;AA2BNyC,QAAAA,YAAY,EAAE,MA3BR;AA4BNC,QAAAA,QAAQ,EAAE,KA5BJ;AA6BN9tB,QAAAA,KAAK,EAAE,IA7BD;AA8BN6zB,QAAAA,YAAY,EAAE,MA9BR;AA+BNC,QAAAA,UAAU,EAAE,MA/BN;AAgCN/F,QAAAA,OAAO,EAAE,MAhCH;AAiCNC,QAAAA,OAAO,EAAE,MAjCH;AAkCNh4B,QAAAA,SAAS,EAAE,MAlCL;AAmCNi4B,QAAAA,cAAc,EAAE,MAnCV;AAoCNn1B,QAAAA,IAAI,EAAE,MApCA;AAqCN4C,QAAAA,MAAM,EAAE,SArCF;AAsCN9C,QAAAA,GAAG,EAAE,GAtCC;AAuCNtF,QAAAA,EAAE,EAAE,GAvCE;AAwCN+6B,QAAAA,UAAU,EAAE,QAxCN;AAyCN0F,QAAAA,cAAc,EAAE,MAzCV;AA0CNC,QAAAA,aAAa,EAAE,UA1CT;AA2CNC,QAAAA,UAAU,EAAE,KA3CN;AA4CNC,QAAAA,SAAS,EAAE,QA5CL;AA6CNC,QAAAA,SAAS,EAAE,QA7CL;AA8CNC,QAAAA,WAAW,EAAE,QA9CP;AA+CNC,QAAAA,UAAU,EAAE,QA/CN;AAgDNC,QAAAA,eAAe,EAAE,MAhDX;AAiDNC,QAAAA,SAAS,EAAE,MAjDL;AAkDNrpC,QAAAA,IAAI,EAAE,IAlDA;AAmDN2+B,QAAAA,UAAU,EAAE,MAnDN;AAoDNE,QAAAA,QAAQ,EAAE,MApDJ;AAqDNyK,QAAAA,iBAAiB,EAAE,OArDb;AAsDNC,QAAAA,OAAO,EAAE;AAtDH,OAzOJ;AAkSJC,MAAAA,GAAG,EAAE;AACHjpC,QAAAA,SAAS,EAAE,IADR;AAEHkpC,QAAAA,SAAS,EAAE,OAFR;AAGHtjC,QAAAA,IAAI,EAAE,MAHH;AAIHyH,QAAAA,IAAI,EAAE,MAJH;AAKH4C,QAAAA,MAAM,EAAE,SALL;AAMHuwB,QAAAA,WAAW,EAAE,OANV;AAOH9hC,QAAAA,MAAM,EAAE,GAPL;AAQH+hC,QAAAA,IAAI,EAAE,IARH;AASHtwB,QAAAA,MAAM,EAAE,IATL;AAUHuwB,QAAAA,eAAe,EAAE,QAVd;AAWHp7B,QAAAA,KAAK,EAAE,IAXJ;AAYHq7B,QAAAA,kBAAkB,EAAE,QAZjB;AAaHC,QAAAA,YAAY,EAAE,OAbX;AAcHrsB,QAAAA,KAAK,EAAE,IAdJ;AAeHssB,QAAAA,IAAI,EAAE,IAfH;AAgBHsI,QAAAA,UAAU,EAAE,IAhBT;AAiBHC,QAAAA,YAAY,EAAE,IAjBX;AAkBHC,QAAAA,OAAO,EAAE,MAlBN;AAmBH/rB,QAAAA,MAAM,EAAE,IAnBL;AAoBHC,QAAAA,KAAK,EAAE,IApBJ;AAqBH+rB,QAAAA,UAAU,EAAE,MArBT;AAsBHt9B,QAAAA,aAAa,EAAE,MAtBZ;AAuBHu3B,QAAAA,WAAW,EAAE,KAvBV;AAwBHE,QAAAA,UAAU,EAAE,MAxBT;AAyBH8F,QAAAA,gBAAgB,EAAE,OAzBf;AA0BHC,QAAAA,MAAM,EAAE,IA1BL;AA2BHC,QAAAA,OAAO,EAAE,IA3BN;AA4BHpqC,QAAAA,OAAO,EAAE,IA5BN;AA6BHqqC,QAAAA,MAAM,EAAE,IA7BL;AA8BHC,QAAAA,gBAAgB,EAAE,OA9Bf;AA+BHC,QAAAA,cAAc,EAAE;AA/Bb,OAlSD;AAmUJC,MAAAA,GAAG,EAAE;AACH7pC,QAAAA,SAAS,EAAE,IADR;AAEH+gC,QAAAA,gBAAgB,EAAE,QAFf;AAGHC,QAAAA,QAAQ,EAAE,MAHP;AAIH9jB,QAAAA,MAAM,EAAE,IAJL;AAKHyO,QAAAA,WAAW,EAAE,IALV;AAMHsV,QAAAA,cAAc,EAAE,MANb;AAOHlhC,QAAAA,IAAI,EAAE,IAPH;AAQHuJ,QAAAA,WAAW,EAAE,MARV;AASH43B,QAAAA,aAAa,EAAE,MATZ;AAUHC,QAAAA,IAAI,EAAE,IAVH;AAWH1sB,QAAAA,aAAa,EAAE,MAXZ;AAYHF,QAAAA,KAAK,EAAE,OAZJ;AAaHnE,QAAAA,UAAU,EAAE,OAbT;AAeHpG,QAAAA,QAAQ,EAAE,KAfP;AAgBHo3B,QAAAA,UAAU,EAAE,MAhBT;AAiBHC,QAAAA,eAAe,EAAE,MAjBd;AAkBHC,QAAAA,SAAS,EAAE,MAlBR;AAmBHC,QAAAA,UAAU,EAAE,IAnBT;AAoBHC,QAAAA,QAAQ,EAAE,MApBP;AAqBHC,QAAAA,mBAAmB,EAAE,QArBlB;AAsBHC,QAAAA,0BAA0B,EAAE,WAtBzB;AAuBHC,QAAAA,QAAQ,EAAE,IAvBP;AAwBHC,QAAAA,cAAc,EAAE,MAxBb;AAyBHC,QAAAA,YAAY,EAAE,MAzBX;AA0BHC,QAAAA,UAAU,EAAE,MA1BT;AA2BHC,QAAAA,YAAY,EAAE,MA3BX;AA6BHC,QAAAA,iBAAiB,EAAE,KA7BhB;AA8BHC,QAAAA,mBAAmB,EAAE,SA9BlB;AA+BHC,QAAAA,cAAc,EAAE,MA/Bb;AAgCHC,QAAAA,QAAQ,EAAE,IAhCP;AAiCHC,QAAAA,YAAY,EAAE,MAjCX;AAkCHC,QAAAA,QAAQ,EAAE,KAlCP;AAmCHC,QAAAA,OAAO,EAAE,MAnCN;AAoCHC,QAAAA,OAAO,EAAE,MApCN;AAqCHh4B,QAAAA,SAAS,EAAE,MArCR;AAsCHi4B,QAAAA,cAAc,EAAE,MAtCb;AAuCHC,QAAAA,IAAI,EAAE,IAvCH;AAwCH5xB,QAAAA,OAAO,EAAE,IAxCN;AAyCHoV,QAAAA,IAAI,EAAE,IAzCH;AA0CHyc,QAAAA,UAAU,EAAE,OA1CT;AA2CH5C,QAAAA,MAAM,EAAE,IA3CL;AA4CH6C,QAAAA,MAAM,EAAE,MA5CL;AA6CHC,QAAAA,UAAU,EAAE,QA7CT;AA8CHC,QAAAA,OAAO,EAAE,MA9CN;AA+CHC,QAAAA,sBAAsB,EAAE,QA/CrB;AAgDHC,QAAAA,GAAG,EAAE,IAhDF;AAiDHC,QAAAA,KAAK,EAAE,IAjDJ;AAkDHC,QAAAA,eAAe,EAAE,MAlDd;AAmDHC,QAAAA,aAAa,EAAE,MAnDZ;AAoDHC,QAAAA,cAAc,EAAE,MApDb;AAqDHC,QAAAA,aAAa,EAAE,MArDZ;AAsDHC,QAAAA,OAAO,EAAE,OAtDN;AAuDHC,QAAAA,QAAQ,EAAE,MAvDP;AAwDHC,QAAAA,WAAW,EAAE,QAxDV;AAyDHC,QAAAA,WAAW,EAAE,KAzDV;AA0DHC,QAAAA,UAAU,EAAE,KA1DT;AA2DHC,QAAAA,WAAW,EAAE,MA3DV;AA4DHC,QAAAA,UAAU,EAAE,MA5DT;AA6DHC,QAAAA,MAAM,EAAE,KA7DL;AA8DHC,QAAAA,GAAG,EAAE,GA9DF;AA+DHC,QAAAA,MAAM,EAAE,IA/DL;AAgEHC,QAAAA,SAAS,EAAE,MAhER;AAiEHC,QAAAA,UAAU,EAAE,MAjET;AAkEHC,QAAAA,UAAU,EAAE,MAlET;AAmEHC,QAAAA,YAAY,EAAE;AAnEX,OAnUD;AAyYJ4F,MAAAA,GAAG,EAAE;AACHhvB,QAAAA,WAAW,EAAC,IADT;AAEH0pB,QAAAA,cAAc,EAAE,MAFb;AAGHK,QAAAA,0BAA0B,EAAE,SAHzB;AAIHC,QAAAA,gBAAgB,EAAE,WAJf;AAKHiF,QAAAA,eAAe,EAAE,MALd;AAMHC,QAAAA,iBAAiB,EAAE,MANhB;AAOHpD,QAAAA,eAAe,EAAE,MAPd;AAQHD,QAAAA,cAAc,EAAE,MARb;AAUH5rB,QAAAA,QAAQ,EAAC,IAVN;AAWHkvB,QAAAA,YAAY,EAAE,MAXX;AAYHC,QAAAA,WAAW,EAAE,MAZV;AAaHC,QAAAA,cAAc,EAAE,MAbb;AAcHC,QAAAA,WAAW,EAAE,MAdV;AAgBHC,QAAAA,OAAO,EAAC,OAhBL;AAiBHC,QAAAA,UAAU,EAAE,SAjBT;AAkBHC,QAAAA,aAAa,EAAE,SAlBZ;AAmBHC,QAAAA,WAAW,EAAE,SAnBV;AAoBHC,QAAAA,UAAU,EAAE,SApBT;AAsBHC,QAAAA,MAAM,EAAC,KAtBJ;AAuBHC,QAAAA,MAAM,EAAE,OAvBL;AAwBHC,QAAAA,YAAY,EAAE,OAxBX;AAyBHC,QAAAA,UAAU,EAAE,OAzBT;AA0BHC,QAAAA,SAAS,EAAE,OA1BR;AAgCH9qC,QAAAA,SAAS,EAAE,IAhCR;AAiCHqkC,QAAAA,mBAAmB,EAAE,OAjClB;AAkCH/C,QAAAA,SAAS,EAAE,KAlCR;AAmCHlxB,QAAAA,UAAU,EAAE,OAnCT;AAqCHm0B,QAAAA,+BAA+B,EAAE,QArC9B;AAsCH/C,QAAAA,QAAQ,EAAE,OAtCP;AAuCHmD,QAAAA,MAAM,EAAE,OAvCL;AAwCHC,QAAAA,MAAM,EAAE,OAxCL;AAyCHvK,QAAAA,GAAG,EAAE,KAzCF;AA2CHlqB,QAAAA,MAAM,EAAE,IA3CL;AA4CH40B,QAAAA,MAAM,EAAE,IA5CL;AA6CHK,QAAAA,SAAS,EAAE,QA7CR;AA8CHC,QAAAA,SAAS,EAAE,QA9CR;AA+CHpf,QAAAA,IAAI,EAAE,IA/CH;AAgDHqc,QAAAA,OAAO,EAAE,MAhDN;AAiDHC,QAAAA,OAAO,EAAE,MAjDN;AAkDHh4B,QAAAA,SAAS,EAAE,MAlDR;AAmDHi4B,QAAAA,cAAc,EAAE,MAnDb;AAoDH3xB,QAAAA,OAAO,EAAE,IApDN;AAqDHyK,QAAAA,GAAG,EAAE,KArDF;AAsDHE,QAAAA,GAAG,EAAE,KAtDF;AAuDH8pB,QAAAA,OAAO,EAAE,IAvDN;AAwDHC,QAAAA,kBAAkB,EAAE,MAxDjB;AAyDH9C,QAAAA,IAAI,EAAE,IAzDH;AA0DH+C,QAAAA,eAAe,EAAE,MA1Dd;AA2DHC,QAAAA,MAAM,EAAE,GA3DL;AA4DHC,QAAAA,WAAW,EAAE,KA5DV;AA6DHxC,QAAAA,aAAa,EAAE,MA7DZ;AA8DHyC,QAAAA,6BAA6B,EAAE,QA9D5B;AA+DHpM,QAAAA,UAAU,EAAE,MA/DT;AAgEHoK,QAAAA,UAAU,EAAE,MAhET;AAiEHC,QAAAA,MAAM,EAAE,KAjEL;AAkEHC,QAAAA,GAAG,EAAE,GAlEF;AAmEHC,QAAAA,MAAM,EAAE,IAnEL;AAoEHC,QAAAA,SAAS,EAAE,MApER;AAqEHC,QAAAA,UAAU,EAAE,MArET;AAsEHC,QAAAA,UAAU,EAAE,MAtET;AAuEHC,QAAAA,YAAY,EAAE,OAvEX;AAwEH0B,QAAAA,WAAW,EAAE,MAxEV;AAyEHC,QAAAA,uBAAuB,EAAE,UAzEtB;AA0EHC,QAAAA,4BAA4B,EAAE,WA1E3B;AA2EHC,QAAAA,uCAAuC,EAAE,kBA3EtC;AA4EHC,QAAAA,oBAAoB,EAAE,MA5EnB;AA6EHC,QAAAA,uBAAuB,EAAE,OA7EtB;AA8EHC,QAAAA,KAAK,EAAE,GA9EJ;AA+EHC,QAAAA,KAAK,EAAE,MA/EJ;AAgFHC,QAAAA,QAAQ,EAAE,IAhFP;AAiFHC,QAAAA,QAAQ,EAAE,IAjFP;AAkFHE,QAAAA,aAAa,EAAE,IAlFZ;AAmFHE,QAAAA,eAAe,EAAE,KAnFd;AAoFHC,QAAAA,gBAAgB,EAAE,OApFf;AAqFHM,QAAAA,iBAAiB,EAAE,MArFhB;AAsFHC,QAAAA,gBAAgB,EAAE,MAtFf;AAuFHC,QAAAA,oBAAoB,EAAE,OAvFnB;AAwFHC,QAAAA,mBAAmB,EAAE,OAxFlB;AAyFHC,QAAAA,oBAAoB,EAAE,OAzFnB;AA0FHC,QAAAA,mBAAmB,EAAE,OA1FlB;AA2FHC,QAAAA,UAAU,EAAE,UA3FT;AA4FHC,QAAAA,OAAO,EAAE;AA5FN,OAzYD;AAueJwD,MAAAA,GAAG,EAAE;AACH/qC,QAAAA,SAAS,EAAE,IADR;AAEHkpC,QAAAA,SAAS,EAAE,OAFR;AAGHtjC,QAAAA,IAAI,EAAE,MAHH;AAIHyH,QAAAA,IAAI,EAAE,MAJH;AAKH4C,QAAAA,MAAM,EAAE,SALL;AAMHuwB,QAAAA,WAAW,EAAE,OANV;AAOH9hC,QAAAA,MAAM,EAAE,GAPL;AAQH+hC,QAAAA,IAAI,EAAE,IARH;AASHtwB,QAAAA,MAAM,EAAE,IATL;AAUHuwB,QAAAA,eAAe,EAAE,QAVd;AAWHp7B,QAAAA,KAAK,EAAE,IAXJ;AAYHq7B,QAAAA,kBAAkB,EAAE,QAZjB;AAaHC,QAAAA,YAAY,EAAE,OAbX;AAcHrsB,QAAAA,KAAK,EAAE,IAdJ;AAeHssB,QAAAA,IAAI,EAAE,IAfH;AAgBHsI,QAAAA,UAAU,EAAE,IAhBT;AAiBHC,QAAAA,YAAY,EAAE,IAjBX;AAkBHC,QAAAA,OAAO,EAAE,MAlBN;AAmBH/rB,QAAAA,MAAM,EAAE,IAnBL;AAoBHC,QAAAA,KAAK,EAAE,IApBJ;AAqBH+rB,QAAAA,UAAU,EAAE,MArBT;AAsBHt9B,QAAAA,aAAa,EAAE,MAtBZ;AAuBHu3B,QAAAA,WAAW,EAAE,KAvBV;AAwBHE,QAAAA,UAAU,EAAE,MAxBT;AAyBH8F,QAAAA,gBAAgB,EAAE,OAzBf;AA0BHC,QAAAA,MAAM,EAAE,IA1BL;AA2BHC,QAAAA,OAAO,EAAE,IA3BN;AA4BHpqC,QAAAA,OAAO,EAAE,IA5BN;AA6BHqqC,QAAAA,MAAM,EAAE,IA7BL;AA8BHC,QAAAA,gBAAgB,EAAE,OA9Bf;AA+BHC,QAAAA,cAAc,EAAE;AA/Bb,OAveD;AAwgBJoB,MAAAA,GAAG,EAAE;AACHhiC,QAAAA,SAAS,EAAC,MADP;AAEHiiC,QAAAA,aAAa,EAAC,MAFX;AAIHC,QAAAA,eAAe,EAAE,IAJd;AAKHC,QAAAA,UAAU,EAAE,MALT;AAMHC,QAAAA,QAAQ,EAAE,IANP;AAOHrJ,QAAAA,YAAY,EAAE,IAPX;AAQHsJ,QAAAA,aAAa,EAAE,KARZ;AASHC,QAAAA,WAAW,EAAE,MATV;AAWHC,QAAAA,SAAS,EAAE,MAXR;AAYHhC,QAAAA,gBAAgB,EAAC,OAZd;AAaHI,QAAAA,gBAAgB,EAAC,OAbd;AAcHlG,QAAAA,UAAU,EAAC,MAdR;AAgBHF,QAAAA,WAAW,EAAC,KAhBT;AAmBHzoB,QAAAA,WAAW,EAAC,IAnBT;AAoBH0pB,QAAAA,cAAc,EAAE,MApBb;AAqBHK,QAAAA,0BAA0B,EAAE,SArBzB;AAsBHC,QAAAA,gBAAgB,EAAE,WAtBf;AAuBHiF,QAAAA,eAAe,EAAE,MAvBd;AAwBHC,QAAAA,iBAAiB,EAAE,MAxBhB;AAyBHpD,QAAAA,eAAe,EAAE,MAzBd;AA0BHD,QAAAA,cAAc,EAAE,MA1Bb;AA4BH3mC,QAAAA,SAAS,EAAE,IA5BR;AA6BH+gC,QAAAA,gBAAgB,EAAE,QA7Bf;AA8BHC,QAAAA,QAAQ,EAAE,MA9BP;AA+BH9jB,QAAAA,MAAM,EAAE,IA/BL;AAgCHyO,QAAAA,WAAW,EAAE,IAhCV;AAiCHsV,QAAAA,cAAc,EAAE,MAjCb;AAkCHlhC,QAAAA,IAAI,EAAE,IAlCH;AAmCHuJ,QAAAA,WAAW,EAAE,MAnCV;AAoCH43B,QAAAA,aAAa,EAAE,MApCZ;AAqCHC,QAAAA,IAAI,EAAE,IArCH;AAsCH1sB,QAAAA,aAAa,EAAE,MAtCZ;AAuCHF,QAAAA,KAAK,EAAE,OAvCJ;AAwCHnE,QAAAA,UAAU,EAAE,OAxCT;AA2CHgxB,QAAAA,UAAU,EAAE,MA3CT;AA4CHE,QAAAA,SAAS,EAAE,MA5CR;AA6CHC,QAAAA,UAAU,EAAE,IA7CT;AA8CHC,QAAAA,QAAQ,EAAE,MA9CP;AA+CHC,QAAAA,mBAAmB,EAAE,QA/ClB;AAgDHC,QAAAA,0BAA0B,EAAE,WAhDzB;AAiDHC,QAAAA,QAAQ,EAAE,IAjDP;AAkDHC,QAAAA,cAAc,EAAE,MAlDb;AAmDHC,QAAAA,YAAY,EAAE,MAnDX;AAoDHC,QAAAA,UAAU,EAAE,MApDT;AAuDHE,QAAAA,iBAAiB,EAAE,KAvDhB;AAwDHC,QAAAA,mBAAmB,EAAE,SAxDlB;AAyDHC,QAAAA,cAAc,EAAE,MAzDb;AA0DHC,QAAAA,QAAQ,EAAE,IA1DP;AA2DHC,QAAAA,YAAY,EAAE,MA3DX;AA4DHC,QAAAA,QAAQ,EAAE,KA5DP;AA6DHC,QAAAA,OAAO,EAAE,MA7DN;AA8DHC,QAAAA,OAAO,EAAE,MA9DN;AA+DHh4B,QAAAA,SAAS,EAAE,MA/DR;AAgEHi4B,QAAAA,cAAc,EAAE,MAhEb;AAiEHC,QAAAA,IAAI,EAAE,IAjEH;AAkEH5xB,QAAAA,OAAO,EAAE,IAlEN;AAmEHoV,QAAAA,IAAI,EAAE,IAnEH;AAoEHyc,QAAAA,UAAU,EAAE,OApET;AAqEH5C,QAAAA,MAAM,EAAE,IArEL;AAsEH6C,QAAAA,MAAM,EAAE,MAtEL;AAuEHC,QAAAA,UAAU,EAAE,QAvET;AAwEHC,QAAAA,OAAO,EAAE,MAxEN;AAyEHC,QAAAA,sBAAsB,EAAE,QAzErB;AA0EHC,QAAAA,GAAG,EAAE,IA1EF;AA2EHC,QAAAA,KAAK,EAAE,IA3EJ;AA4EHC,QAAAA,eAAe,EAAE,MA5Ed;AA6EHC,QAAAA,aAAa,EAAE,MA7EZ;AA8EHC,QAAAA,cAAc,EAAE,MA9Eb;AA+EHC,QAAAA,aAAa,EAAE,MA/EZ;AAgFHC,QAAAA,OAAO,EAAE,OAhFN;AAiFHC,QAAAA,QAAQ,EAAE,MAjFP;AAkFH;AACAE,QAAAA,WAAW,EAAE,KAnFV;AAqFHE,QAAAA,WAAW,EAAE,MArFV;AAsFHC,QAAAA,UAAU,EAAE,MAtFT;AAuFHC,QAAAA,MAAM,EAAE,KAvFL;AAwFHC,QAAAA,GAAG,EAAE,GAxFF;AAyFHC,QAAAA,MAAM,EAAE,IAzFL;AA0FHC,QAAAA,SAAS,EAAE,MA1FR;AA2FHC,QAAAA,UAAU,EAAE,MA3FT;AA4FHC,QAAAA,UAAU,EAAE,MA5FT;AA6FHC,QAAAA,YAAY,EAAE;AA7FX,OAxgBD;AAwmBJsH,MAAAA,GAAG,EAAE;AACHxiC,QAAAA,SAAS,EAAC,MADP;AAEHiiC,QAAAA,aAAa,EAAC,MAFX;AAIHC,QAAAA,eAAe,EAAE,IAJd;AAKHC,QAAAA,UAAU,EAAE,MALT;AAMHC,QAAAA,QAAQ,EAAE,IANP;AAQHK,QAAAA,iBAAiB,EAAE,MARhB;AAWHC,QAAAA,wBAAwB,EAAE,IAXvB;AAYHC,QAAAA,sBAAsB,EAAE,MAZrB;AAaHC,QAAAA,mBAAmB,EAAE,IAblB;AAcHC,QAAAA,gBAAgB,EAAE,IAdf;AAiBH9J,QAAAA,YAAY,EAAE,IAjBX;AAkBHsJ,QAAAA,aAAa,EAAE,KAlBZ;AAmBHC,QAAAA,WAAW,EAAE,MAnBV;AAqBHC,QAAAA,SAAS,EAAE,MArBR;AAsBHhC,QAAAA,gBAAgB,EAAC,OAtBd;AAuBHI,QAAAA,gBAAgB,EAAC,OAvBd;AAwBHlG,QAAAA,UAAU,EAAC,MAxBR;AAyBHF,QAAAA,WAAW,EAAC,KAzBT;AA4BHzoB,QAAAA,WAAW,EAAC,IA5BT;AA6BH0pB,QAAAA,cAAc,EAAE,MA7Bb;AA8BHK,QAAAA,0BAA0B,EAAE,SA9BzB;AA+BHC,QAAAA,gBAAgB,EAAE,WA/Bf;AAgCHiF,QAAAA,eAAe,EAAE,MAhCd;AAiCHC,QAAAA,iBAAiB,EAAE,MAjChB;AAkCHpD,QAAAA,eAAe,EAAE,MAlCd;AAmCHD,QAAAA,cAAc,EAAE,MAnCb;AAqCH3mC,QAAAA,SAAS,EAAE,IArCR;AAsCH+gC,QAAAA,gBAAgB,EAAE,QAtCf;AAuCHC,QAAAA,QAAQ,EAAE,MAvCP;AAwCH9jB,QAAAA,MAAM,EAAE,IAxCL;AAyCHyO,QAAAA,WAAW,EAAE,IAzCV;AA0CHsV,QAAAA,cAAc,EAAE,MA1Cb;AA2CHlhC,QAAAA,IAAI,EAAE,IA3CH;AA4CHuJ,QAAAA,WAAW,EAAE,MA5CV;AA6CHwiC,QAAAA,YAAY,EAAE,MA7CX;AA8CH5K,QAAAA,aAAa,EAAE,MA9CZ;AA+CHC,QAAAA,IAAI,EAAE,IA/CH;AAgDH1sB,QAAAA,aAAa,EAAE,MAhDZ;AAiDHF,QAAAA,KAAK,EAAE,OAjDJ;AAkDHnE,QAAAA,UAAU,EAAE,OAlDT;AAqDHgxB,QAAAA,UAAU,EAAE,MArDT;AAsDHE,QAAAA,SAAS,EAAE,MAtDR;AAuDHC,QAAAA,UAAU,EAAE,IAvDT;AAwDHC,QAAAA,QAAQ,EAAE,MAxDP;AAyDHC,QAAAA,mBAAmB,EAAE,QAzDlB;AA0DHC,QAAAA,0BAA0B,EAAE,WA1DzB;AA2DHC,QAAAA,QAAQ,EAAE,IA3DP;AA4DHC,QAAAA,cAAc,EAAE,MA5Db;AA6DHC,QAAAA,YAAY,EAAE,MA7DX;AA8DHC,QAAAA,UAAU,EAAE,MA9DT;AAiEHE,QAAAA,iBAAiB,EAAE,KAjEhB;AAkEHC,QAAAA,mBAAmB,EAAE,SAlElB;AAmEHC,QAAAA,cAAc,EAAE,MAnEb;AAoEHC,QAAAA,QAAQ,EAAE,IApEP;AAqEHC,QAAAA,YAAY,EAAE,MArEX;AAsEHC,QAAAA,QAAQ,EAAE,KAtEP;AAuEHC,QAAAA,OAAO,EAAE,MAvEN;AAwEHC,QAAAA,OAAO,EAAE,MAxEN;AAyEHh4B,QAAAA,SAAS,EAAE,MAzER;AA0EHi4B,QAAAA,cAAc,EAAE,MA1Eb;AA2EHC,QAAAA,IAAI,EAAE,IA3EH;AA4EH5xB,QAAAA,OAAO,EAAE,IA5EN;AA6EHoV,QAAAA,IAAI,EAAE,IA7EH;AA8EHyc,QAAAA,UAAU,EAAE,OA9ET;AA+EH5C,QAAAA,MAAM,EAAE,IA/EL;AAgFH6C,QAAAA,MAAM,EAAE,MAhFL;AAiFHC,QAAAA,UAAU,EAAE,QAjFT;AAkFHC,QAAAA,OAAO,EAAE,MAlFN;AAmFHC,QAAAA,sBAAsB,EAAE,QAnFrB;AAoFHC,QAAAA,GAAG,EAAE,IApFF;AAqFHC,QAAAA,KAAK,EAAE,IArFJ;AAsFHC,QAAAA,eAAe,EAAE,MAtFd;AAuFHC,QAAAA,aAAa,EAAE,MAvFZ;AAwFHC,QAAAA,cAAc,EAAE,MAxFb;AAyFHC,QAAAA,aAAa,EAAE,MAzFZ;AA0FHC,QAAAA,OAAO,EAAE,OA1FN;AA2FHC,QAAAA,QAAQ,EAAE,MA3FP;AA4FH;AACAE,QAAAA,WAAW,EAAE,KA7FV;AA+FHE,QAAAA,WAAW,EAAE,MA/FV;AAgGHC,QAAAA,UAAU,EAAE,MAhGT;AAiGHC,QAAAA,MAAM,EAAE,KAjGL;AAkGHC,QAAAA,GAAG,EAAE,GAlGF;AAmGHkI,QAAAA,IAAI,EAAE,IAnGH;AAoGHC,QAAAA,QAAQ,EAAE,IApGP;AAsGHjI,QAAAA,SAAS,EAAE,MAtGR;AAuGHC,QAAAA,UAAU,EAAE,MAvGT;AAwGHC,QAAAA,UAAU,EAAE,MAxGT;AAyGHC,QAAAA,YAAY,EAAE,OAzGX;AA2GH+H,QAAAA,OAAO,EAAE,IA3GN;AA4GHC,QAAAA,KAAK,EAAE,IA5GJ;AA6GH7D,QAAAA,UAAU,EAAE;AA7GT,OAxmBD;AAwtBJ8D,MAAAA,GAAG,EAAE;AACHC,QAAAA,QAAQ,EAAC,IADN;AAEHC,QAAAA,WAAW,EAAC,MAFT;AAGHC,QAAAA,eAAe,EAAE,MAHd;AAMHtsC,QAAAA,SAAS,EAAE,IANR;AAOH+gC,QAAAA,gBAAgB,EAAE,QAPf;AAQHC,QAAAA,QAAQ,EAAE,OARP;AASH9jB,QAAAA,MAAM,EAAE,IATL;AAUHyO,QAAAA,WAAW,EAAE,IAVV;AAWHsV,QAAAA,cAAc,EAAE,MAXb;AAYHlhC,QAAAA,IAAI,EAAE,IAZH;AAaHuJ,QAAAA,WAAW,EAAE,MAbV;AAcH43B,QAAAA,aAAa,EAAE,MAdZ;AAeHC,QAAAA,IAAI,EAAE,KAfH;AAgBH1sB,QAAAA,aAAa,EAAE,MAhBZ;AAiBHF,QAAAA,KAAK,EAAE,OAjBJ;AAkBHnE,QAAAA,UAAU,EAAE,OAlBT;AAoBHpG,QAAAA,QAAQ,EAAE,KApBP;AAqBHo3B,QAAAA,UAAU,EAAE,MArBT;AAsBHC,QAAAA,eAAe,EAAE,MAtBd;AAuBHC,QAAAA,SAAS,EAAE,OAvBR;AAwBHC,QAAAA,UAAU,EAAE,MAxBT;AAyBHC,QAAAA,QAAQ,EAAE,OAzBP;AA0BHC,QAAAA,mBAAmB,EAAE,QA1BlB;AA2BHC,QAAAA,0BAA0B,EAAE,WA3BzB;AA4BHC,QAAAA,QAAQ,EAAE,IA5BP;AA6BHC,QAAAA,cAAc,EAAE,MA7Bb;AA8BHC,QAAAA,YAAY,EAAE,MA9BX;AA+BHC,QAAAA,UAAU,EAAE,MA/BT;AAgCHC,QAAAA,YAAY,EAAE,MAhCX;AAkCHC,QAAAA,iBAAiB,EAAE,UAlChB;AAmCHC,QAAAA,mBAAmB,EAAE,SAnClB;AAoCHC,QAAAA,cAAc,EAAE,MApCb;AAqCHC,QAAAA,QAAQ,EAAE,IArCP;AAsCHC,QAAAA,YAAY,EAAE,MAtCX;AAuCHC,QAAAA,QAAQ,EAAE,KAvCP;AAwCHC,QAAAA,OAAO,EAAE,MAxCN;AAyCHC,QAAAA,OAAO,EAAE,MAzCN;AA0CHh4B,QAAAA,SAAS,EAAE,MA1CR;AA2CHi4B,QAAAA,cAAc,EAAE,MA3Cb;AA4CHC,QAAAA,IAAI,EAAE,IA5CH;AA6CH5xB,QAAAA,OAAO,EAAE,IA7CN;AA8CHoV,QAAAA,IAAI,EAAE,IA9CH;AA+CHyc,QAAAA,UAAU,EAAE,OA/CT;AAgDH5C,QAAAA,MAAM,EAAE,IAhDL;AAiDH6C,QAAAA,MAAM,EAAE,MAjDL;AAkDHC,QAAAA,UAAU,EAAE,QAlDT;AAmDHC,QAAAA,OAAO,EAAE,MAnDN;AAoDHC,QAAAA,sBAAsB,EAAE,QApDrB;AAqDHC,QAAAA,GAAG,EAAE,IArDF;AAsDHC,QAAAA,KAAK,EAAE,IAtDJ;AAuDHC,QAAAA,eAAe,EAAE,MAvDd;AAwDHC,QAAAA,aAAa,EAAE,MAxDZ;AAyDHC,QAAAA,cAAc,EAAE,MAzDb;AA0DHC,QAAAA,aAAa,EAAE,MA1DZ;AA2DHC,QAAAA,OAAO,EAAE,OA3DN;AA4DHC,QAAAA,QAAQ,EAAE,MA5DP;AA6DHC,QAAAA,WAAW,EAAE,QA7DV;AA8DHC,QAAAA,WAAW,EAAE,KA9DV;AA+DHC,QAAAA,UAAU,EAAE,KA/DT;AAgEHC,QAAAA,WAAW,EAAE,MAhEV;AAiEHC,QAAAA,UAAU,EAAE,MAjET;AAkEHC,QAAAA,MAAM,EAAE,KAlEL;AAmEHC,QAAAA,GAAG,EAAE,GAnEF;AAoEHC,QAAAA,MAAM,EAAE,IApEL;AAqEHC,QAAAA,SAAS,EAAE,MArER;AAsEHC,QAAAA,UAAU,EAAE,MAtET;AAuEHC,QAAAA,UAAU,EAAE,MAvET;AAwEHC,QAAAA,YAAY,EAAE;AAxEX;AAxtBD;AArxCF;AADc,CAAf", "sourcesContent": ["//export default {\r\nexport const locale = {\r\n  lang: {\r\n    // 公共\r\n    common: {\r\n       //============== getway ========\r\n       consumerError: '不允許授權此操作，請聯絡系統管理員。',\r\n       consumerSignatureError: '簽名驗證失敗，請聯絡系統管理員。',\r\n       invalidUserOrPassword: '帳號或密碼錯誤',\r\n       badGateway: 'service error', \r\n       \r\n     \r\n       monitor20: '軌跡監控',\r\n       monitor21: '事件監控',\r\n       monitor22: '母嬰同室',\r\n       monitor23: '長照中心',    \r\n       monitor24: '生理監控',//'生理偵測',\r\n       monitor25: '病房監控',  \r\n       monitor26: '管制監控',         \r\n       monitor27: '室內環境品質',//'環境偵測',  \r\n       monitor28: '走失防止',\r\n       monitor29: '住民照護',\r\n       monitor30: '病患安全',\r\n       monitor31: '母嬰同室',\r\n\r\n       \r\n       sensorTypeTemperature:'體溫',\r\n       sensorTypeHeartRate:'脈博',\r\n       sensorTypeBloodOxygen:'血氧',\r\n       sensorTypeBloodPressure:'血壓',\r\n       sensorTypeHelp:'求助',\r\n       sensorTypeDiaper:'尿濕',\r\n       sensorTypePosition:'定位',\r\n       \r\n       sensorTypeMMWave:'毫米波',\r\n       \r\n       //==============\r\n       confirmDeleteSelection:'刪除勾選選項?',\r\n       confirmDelete:'刪除確認',\r\n       //==============\r\n      appMainTitle:'智匯網護理支援',\r\n      systemTitle:'智匯網護理支援標準版',\r\n      indoorRealTimePositioningSystem: '智匯網護理支援標準版',\r\n      traditionalChinese: '繁體中文',\r\n      simplifiedChinese: '簡體中文',\r\n      english: '英文',\r\n      introduction: '簡介',\r\n      people: '人',\r\n      piece: '件',\r\n      // 公共\r\n      page: '頁',\r\n      import: '匯入',\r\n      export: '匯出',\r\n      exportExcel: '匯出格式為Excel',\r\n      newlyIncreased: '新增',\r\n      search: '搜尋',\r\n      delete: '刪除',\r\n      removePair: '解除配對',\r\n      list: '列表',\r\n      confirm: '確定',\r\n      cancel: '取消',\r\n      homePage: '首頁',\r\n      reset: '重置',\r\n      edit: '編輯',\r\n      upload: '上傳',\r\n      storage: '儲存',\r\n      total: '共',\r\n      pen: '筆',\r\n      date: '日期',\r\n      time: '時間',\r\n      equipment: '設備',\r\n      noDataText: '暫無數據',\r\n      inputKeyword: '請輸入關鍵字',\r\n      keywordEvent: '請輸入事件記錄',\r\n      pleaseSelect: '請選擇',\r\n      pleaseSearch: '搜尋...',\r\n      chooseStartTime: '請選擇起始日期',\r\n      chooseEndTime: '請選擇結束日期',\r\n      exportFormatForExcel: '匯出格式爲Excel',\r\n      seeMoreEvents: '看更多事件',\r\n      return: '返回',\r\n      all: '全部',\r\n      marked: '已標記',\r\n      notMarked: '未標記',\r\n      cancelled: '已取消',\r\n      hasBeenIdentified: '已確定',\r\n      deleteConfirm: '刪除確認',\r\n      deleteItemConfirm: '是否確認刪除此項目？',\r\n      // 提示\r\n      prompt: '提示',\r\n      selectEventToDelete: '請選擇要刪除的事件',\r\n      notUsePermission: '您沒有使用此功能所需的權限。',\r\n      deleteSuccessful: '刪除成功。',\r\n      removePairSuccessful: '解除配對成功。',\r\n      addSuccessful: '新增成功。',\r\n      storageSuccessful: '儲存成功。',\r\n      exportSuccessful: '匯出成功。',\r\n      importSuccessful: '匯入成功。',\r\n      searchSuccessful: '搜尋成功。',\r\n      modifySuccessful: '修改成功。',\r\n      syncSuccessful: '同步成功。',\r\n      exportFail: '匯出欄位驗證失敗。',\r\n      searchFail: '搜尋欄位驗證失敗。',\r\n      // header\r\n      backgroundManagement: '後台管理',\r\n      personalData: '的個人資料',\r\n      personal: '個人',\r\n      operation: '操作',\r\n      changeThePassword: '變更密碼',\r\n      logout: '登出',\r\n      pleaseEnterOriginalPassword: '請輸入原密碼',\r\n      pleaseEnterNewPassword: '請輸入新密碼',\r\n      pleaseEnterNewPasswordAgain: '請再次輸入新密碼',\r\n      passwordError: '原密碼輸入錯誤，請再次確認。',\r\n      tooManyResourcesError: '抱歉，匯出筆數超過限制，請調整查詢條件。',\r\n      passwordErrorPrompt: '新密碼長度為8-15字元，至少須含英文字母及數字。',\r\n      eventRecordErrorPrompt: '事件記錄字數長度必須介於0和256。',\r\n      samePasswordPrompt: '新密碼與原密碼相同，請重新設定。',\r\n      twoNewPasswordsAreNotConsistent: '兩次填入的新密碼不一致。',\r\n      planeNoUploadCoordinatesError: '此平面沒有上傳地圖，無法進行座標設定。',\r\n      noInformationOnFloor: '此樓層目前沒有資料可顯示。',\r\n      selectMap: '請選擇平面圖',\r\n      floorPlan: '平面圖',\r\n      selectDate: '請選擇日期',\r\n      successful: '成功',\r\n      fillInEventRecord: '請填入事件記錄',\r\n      warning: '警告',\r\n      error: '錯誤',\r\n      invalidFormat: '格式錯誤。',\r\n      contactSystemAdministrator: '系統忙碌中，請聯絡系統管理員。',\r\n      modificationSuccessfulLoginAgain: '修改成功，請重新登錄。',\r\n      loginPeriodExpires: '登入有效期限已過，請重新登入。',\r\n      selectedEventDoesNot: '選擇的事件不存在，請重新整理頁面。',\r\n      networkProblemPleaseRefreshPage: '網路問題，請重新整理頁面。',\r\n      networkProblem: '網路連線緩慢，請確認網路狀態或稍後再試。',\r\n      passwordUnavailableError: '目前郵件伺服器異常，無法送帳號通知信件給您，請稍後再試。',\r\n      accessCodeUnavailableError: '目前郵件伺服器異常，無法送重設密碼的信件給您，請稍後再試。',\r\n      storageUnavailableError: '圖片上傳失敗，請確認格式或是檔案大小是否正確。',\r\n      accountAlreadyExists: '帳號已經存在',\r\n      dataAlReadyExists: '資料已經存在',\r\n      deviceObjectExistsError: '選擇的裝置已經被綁定使用，請試試其他裝置。',\r\n      objectDeviceExistsError: '選擇的物件已經被綁定使用，請試試其他物件。',\r\n      accountNotFoundError: '帳號或密碼錯誤',\r\n      resourceNotFoundError: '資料不存在',\r\n      planeNotFoundError: '選擇的區域不存在，請重新整理頁面。',\r\n      operationIsNotAllowed: '不允許此項操作，請聯絡系統管理員。',\r\n      selectAtLeastOneAttribute: '請選擇至少一種屬性。',\r\n      itNotExist: '選擇的事件不存在。',\r\n      reorganizeThePage: '請重新整理頁面。',\r\n      selectedEventState: '請選擇事件狀態。',\r\n      fillInIncidentRecord: '請填寫事件記錄',\r\n      invalidRequestError: '參數錯誤或上傳文件格式錯誤',\r\n      badPasswordError: '密碼長度須介於8-15字元之間，至少須含英文字母及數字。',\r\n      badEmailError: '電子郵件必須符合電子郵件格式。',\r\n      badPhoneError: '電話格式錯誤。',\r\n      invalidAccessTaskError: '此任務無效或已過期，請重新執行此操作。',\r\n      uneventful: '解除事件成功。',\r\n      removeEvent: '解除事件',\r\n      event: '事件',\r\n      trajectory: '軌跡',\r\n      locateObject: '定位對象',\r\n      templateHelp: '求救模板',\r\n      // table\r\n      objectsDisplayed: '物件圖示',\r\n      serialNumber: '編號',\r\n      name: '名稱',\r\n      category: '類別',\r\n      role: '角色',\r\n      group: '群組',\r\n      currentPosition: '目前位置',\r\n      latestPositioningTime: '最新定位時間',\r\n      modifiesAt: '最近更新時間',\r\n      byTheTime: '發起時間',\r\n      plane: '位置',\r\n      eventClassification: '分類',\r\n      sponsorName: '發起者名稱',\r\n      initiator: '發起者類別/角色',\r\n      eventLog: '記錄',\r\n      eventState: '狀態',\r\n      viewTheDayTrack: '檢視當日軌跡',\r\n      viewCurrentLocation: '檢視目前位置',\r\n      // menu\r\n      monitoring: '即時監控',\r\n      fenceMonitoring: '軌跡監控',\r\n      mmWaveMonitoring: '病房監控',\r\n      monitor01: '母嬰同室',\r\n      monitor02: '管制監控',\r\n      monitor03: '生理偵測',\r\n      monitor04: '感染監控',\r\n      monitor05: '長照中心',\r\n\r\n      managePermissions: '管理權限',\r\n      accountManagement: '系統帳號設定',\r\n      roleManagement: '系統角色設定',\r\n      firmwareUpdate: '韌體更新',\r\n      positioningSettings: '場域管理',\r\n      planePosition: '平面圖資設定',\r\n      baseStation: '無線基站設定',\r\n      anchor: '錨點設定',\r\n      guard: 'Guard',\r\n      no: '無',\r\n      dataManagement: '資料管理',\r\n      objectFeatures: '定位屬性設定',\r\n      objectData: '定位對象設定',\r\n      deviceInformation: '裝置資料設定',\r\n      eventDefinition: '事件定義設定',\r\n      guardSetting: 'Guard設定',\r\n      otaUpdate: '韌體更新',\r\n      licenseManagement: '授權管理',\r\n      historyReport: '歷史報表',\r\n      temporarilyNoData: '暫無數據',\r\n      cameraSetting: '攝影機設定',\r\n      logRecording: '裝置定位日誌',\r\n      inventory: '資產盤點系統',\r\n      systemManagement: '系統管理',\r\n      systemConfig: '系統參數設定',\r\n      systemSWVersion: '系統軟體版本',\r\n      mmWave: '毫米波',\r\n      fence: '電子圍籬',\r\n      // event\r\n      helpEvent: '求救警示',\r\n      enterTheNotice: '進入通知',\r\n      leaveTheNotice: '離開通知',\r\n      theNumberOfControl: '人數管制',\r\n      lowBatteryWarning: '低電量警示',\r\n      stationAbnormalWarning: '基站異常警示',\r\n      stayTimeout: '停留逾時警示',\r\n      regularRound: '巡查提醒',\r\n      leaveBed: '離床警示',\r\n      abnormalGuard: 'Guard異常警示',\r\n      sensorDataDriven: '數據自訂事件',\r\n      fallDetection: '跌倒警示_毫米波',\r\n      stayTimeoutMMWave: '停留逾時警示_毫米波',\r\n      leaveBedMMWave: '離床提醒_毫米波',\r\n      getUp: '起身提醒_毫米波',\r\n      abnormalBreath: '呼吸異常警示_毫米波',\r\n      wetUrine: '尿濕提醒',\r\n      // other\r\n      help: '求救',\r\n      untreated: '未處理',\r\n      inTheProcessing: '處理中',\r\n      hasLift: '已解除',\r\n      personnel: '人員',\r\n      map: '地圖',\r\n      eventName: '事件名稱',\r\n      objectsName: '物件名稱',\r\n      order: '順序',\r\n      personnelDistribution: '人員分佈',\r\n      equipmentDistribution: '設備分佈',\r\n      area: '區域',\r\n      subRegion: '子區域',\r\n      factory: '廠區',\r\n      building: '建築物',\r\n      floor: '樓層',\r\n      areaName: '區域名稱',\r\n      factoryName: '廠區名稱',\r\n      buildingName: '建築物名稱',\r\n      floorName: '樓層名稱',\r\n      subRegionName: '子區域名稱',\r\n      geoCluster: '地理群集', // Yujin\r\n      coordinate: '座標',\r\n      hasAttachment: '已連線',\r\n      offline: '離線',\r\n      planeName: '平面圖名稱',\r\n      originalCoordinates: '原始座標',\r\n      currentCoordinates: '目前座標',\r\n      hasChange: '已更動',\r\n      mapHint: '無地圖，請進行平面圖搜索',\r\n      archives: '檔案',\r\n      pairingDevice: '配對裝置',\r\n      inputCode: '請輸入編號',\r\n      inputName: '請輸入名稱',\r\n      inputCodeError: '輸入的編號有誤或重複',\r\n      selectObject: '請選擇定位對象',\r\n      selectDeleteObject: '請選擇刪除的定位對象',\r\n      selectGroup: '請選擇群組',\r\n      onTheGround: '地上',\r\n      underground: '地下',\r\n      okToDoThis: '確定執行此操作？',\r\n      okToDeleteSelection: '確定刪除選擇項目？',\r\n      whetherToReset: '是否重置?',\r\n      selectTheObject: '請選擇物件。',\r\n      noData: '暫無資料',\r\n      limitDeleteRecords: '批次刪除的筆數限制最多為100筆。',\r\n      planeNotFound: '選擇的區域不存在，請重新整理頁面。',\r\n      selectPlane: '請選擇區域',\r\n      chooseType: '請選擇類型',\r\n      uploadTime: '上傳時間',\r\n      yes: '是',\r\n      nay: '否',\r\n      type: '類型',\r\n      document: '文檔',\r\n      enabled: '啓用',\r\n      notEnabled: '不啓用',\r\n      selectedItems: '選擇的項目',\r\n      planeDataExists: '存在平面資料',\r\n      notAllowedDelete: '不允許刪除',\r\n      badConfiguration: '參數格式不正確',\r\n      fullScreen: '全螢幕',\r\n      exitFullScreen: '退出全螢幕',\r\n      noShortcuts: '暫無捷徑'\r\n    },\r\n    // 登錄\r\n    login: {\r\n      account: '帳號',\r\n      password: '密碼',\r\n      resetPassword: '重置密碼',\r\n      login: '登入',\r\n      accountRequiredVerification: '請填入帳號。',\r\n      passwordRequiredVerification: '請填入密碼。',\r\n      passwordPatternVerification: '密碼格式錯誤。'\r\n    },\r\n    // resetPassword\r\n    resetPassword: {\r\n      resetPassword: '重置密碼',\r\n      pleaseResetAccountPassword: '請重設您的帳號密碼',\r\n      inputNewPassword: '請輸入新密碼',\r\n      inputNewPasswordAgain: '請再次輸入新密碼',\r\n      send: '送出',\r\n      newPasswdRequiredVerification: '請填入新密碼。',\r\n      newPasswdRequiredVerificationAgain: '請再次填入新密碼。',\r\n      newPasswdPatternVerification: '新密碼長度為8-15字元，至少須含英文字母及數字。',\r\n      newPasswdDiffer: '兩次填入的新密碼不一致。',\r\n      resetPasswordSuccessful: '重置密碼成功，請重新登入。',\r\n      inputEmail: '請輸入您的電子郵件',\r\n      inputVcode: '輸入圖片驗證碼',\r\n      emailRequiredVerification: '請填入電子郵件。',\r\n      emailTypeVerification: '電子郵件必須符合電子郵件格式。',\r\n      vcodeRequiredVerification: '請填入圖片驗證碼。',\r\n      vcodeError: '圖片驗證碼不正確。',\r\n      sendEmailSuccessful: '我們已經寄送重置密碼的信件給您！',\r\n      accountNotFoundPrompt: '選擇的帳號不存在，請重新整理頁面。'\r\n    },\r\n    personal: {\r\n      account: '帳號',\r\n      name: '姓名',\r\n      email: '電子郵件',\r\n      phone: '電話',\r\n      role: '角色',\r\n      userEmailExistsVerification: '電子郵件已經被使用，請試試其他電子郵件。',\r\n      accountRequiredVerification: '請填入帳號。',\r\n      nameRequiredVerification: '請填入姓名。',\r\n      nameTypeVerification: '姓名字數長度必須介於0和64。',\r\n      emailRequiredVerification: '請填入電子郵件。',\r\n      emailTypeVerification: '電子郵件必須符合電子郵件格式。',\r\n      phoneRequiredVerification: '請填入電話。',\r\n      phonePatternVerification: '電話格式錯誤。',\r\n      userRoleRequiredVerification: '請選擇系統角色。'\r\n    },\r\n    // dashBoard\r\n    dashBoard: {\r\n      device: '裝置',\r\n      totalCount: '總數',\r\n      normal: '正常',\r\n      lowBattery: '低電量',\r\n      lowBatteryEvent: '低電量事件',\r\n      baseStation: '基站',\r\n      offline: '離線',\r\n      no: '無',\r\n      stationAbnormal: '基站異常',\r\n      more: '更多',\r\n      guardAbnormal: 'Guard異常',\r\n      managementShortcut: '管理捷徑',\r\n      course: '歷程',\r\n      deviceName: '裝置名稱',\r\n      pairObject: '配對物件',\r\n      battery: '電量',\r\n      latestBatteryTime: '電量回報時間',\r\n      lowBatteryNotice: '低電量通知',\r\n      stationName: '基站名稱',\r\n      locationRegion: '所在區域',\r\n      connectionState: '連線狀態',\r\n      startsAt: '發起時間',\r\n      abnormalNotice: '異常通知',\r\n      guardName: 'Guard名稱',\r\n      operationDate: '操作日期',\r\n      ipPosition: 'IP位置',\r\n      operationRecord: '操作記錄',\r\n      noPlaneInformation: '暫無平面相關訊息。',\r\n      noCoordinateInformation: '暫無座標相關訊息。',\r\n      regionNotUploadPlaneMap: '選擇區域沒有上傳平面圖，無法顯示地圖。',\r\n      monitorManagerment: '監控管理',\r\n      loginSystem: '登入系統',\r\n      logoutSystem: '退出系統',\r\n      editPlane: '編輯平面位置',\r\n      applicationIntegration: '應用集成'\r\n    },\r\n    // 即時監控\r\n    monitoring: {\r\n      clickOnClosed: '點選閉合',\r\n      clickOnA: '點選展開',\r\n      dragLeftRightToResizeTheWindow: '左右拖拉可調整視窗大小',\r\n      DeEventOperation: '解除事件操作',\r\n      DetermineUnselectedEvent: '確定解除選擇事件',\r\n      searchMonitoringConditions: '請搜尋監控條件',\r\n      mandatoryFieldArea: '區域、物件屬性為必選欄位',\r\n      unableToDisplayTheMap: '選擇區域沒有上傳平面圖，無法顯示地圖',\r\n      storeFailure: '儲存失敗',\r\n      storeSuccessful: '儲存成功',\r\n      noPlaneRelatedConsultation: '暫無平面相關資訊',\r\n      noCoordinateConsultation: '暫無座標相關資訊',\r\n      noCoordinateOrPlaneRelatedConsultation: '暫無座標或平面相關資訊',\r\n      anUnexaminedObject: '暫無可查看的物件',\r\n      selectProjectToTerminated: '請選擇要解除事件的項目',\r\n      validatePlaneErrorInfo: '區域階層請選擇 \"廠區\" 或 \"樓層\" 或 \"區域\"',\r\n      noFlatMapData: '無平面地圖資料',\r\n      keyword: '關鍵字',\r\n      download: '下載',\r\n      recordPreview: '錄影檔預覽',\r\n      fileId: '檔案編號',\r\n      cameraCode: '攝影機編號',\r\n      planeCode: '位置',\r\n      filename: '檔案名稱'\r\n    },\r\n    // 系統帳號設定\r\n    accountManagement: {\r\n      account: '帳號',\r\n      name: '姓名',\r\n      email: '電子郵件',\r\n      phone: '電話',\r\n      role: '角色',\r\n      modifiesAt: '最近更新時間',\r\n      permission: '權限',\r\n      deleteAccountPrompt: '請選擇要刪除的帳號。',\r\n      accountNotFoundPrompt: '選擇的帳號不存在，請重新整理頁面。',\r\n      addSystemAccount: '新增系統帳號',\r\n      inputAccount: '請輸入帳號 *',\r\n      inputPassword: '請輸入密碼 *',\r\n      inputName: '請輸入姓名 *',\r\n      inputEmail: '請輸入電子郵件 *',\r\n      inputPhone: '請輸入電話 *',\r\n      selectRole: '請選擇系統角色 *',\r\n      accountRequiredVerification: '請填入帳號。',\r\n      accountTypeVerification: '帳號字數長度必須介於0和20。',\r\n      userAccountExistsVerification: '帳號已經被使用，請試試其他帳號。',\r\n      passwordRequiredVerification: '請填入密碼。',\r\n      passwordPatternVerification: '密碼格式錯誤。',\r\n      nameRequiredVerification: '請填入姓名。',\r\n      nameTypeVerification: '姓名字數長度必須介於0和64。',\r\n      emailRequiredVerification: '請填入電子郵件。',\r\n      emailTypeVerification: '電子郵件必須符合電子郵件格式。',\r\n      userEmailExistsVerification: '電子郵件已經被使用，請試試其他電子郵件。',\r\n      phoneRequiredVerification: '請填入電話。',\r\n      phonePatternVerification: '電話格式錯誤。',\r\n      userRoleRequiredVerification: '請選擇系統角色。',\r\n      accountName: '帳號名稱',\r\n      instantEventInspection: '即時事件檢視',\r\n      localizingObjectInspection: '定位對象檢視',\r\n      noInstantEvent: '暫無即時事件',\r\n      immediateEvent: '即時事件',\r\n      help: '求救警示',\r\n      enter: '進入通知',\r\n      leave: '離開通知',\r\n      numberControl: '人數管制',\r\n      lowBattery: '低電量警示',\r\n      abnormalStation: '基站異常警示',\r\n      leaveBed: '離床警示',\r\n      stayTimeout: '停留逾時警示',\r\n      regularRound: '巡查提醒',\r\n      sensorDataDriven: '數據自訂事件',\r\n      selectMonitoringConditionPrompt: '至少選擇一項監控條件，可複選！',\r\n      pleaseSelectCategory: '請選擇類別',\r\n      pleaseSelectRole: '請選擇角色',\r\n      pleaseSelectGroup: '請選擇群組',\r\n      pleaseSelectRegion: '請選擇區域',\r\n      categoryRequiredVerification: '請選擇類別。',\r\n      roleRequiredVerification: '請選擇角色。',\r\n      groupRequiredVerification: '請選擇群組。',\r\n      regionRequiredVerification: '請選擇區域。',\r\n      addLocationObject: '新增定位對象',\r\n      deleteMonitoringConditionPrompt: '至少要有一項監控條件。',\r\n      selectTypeRoleGroup: '類別/角色/群組，請擇一輸入。'\r\n    },\r\n    // 系統角色設定\r\n    roleManagement: {\r\n      roleCode: '角色編號',\r\n      roleName: '角色名稱',\r\n      systemRoleCode: '系統角色編號',\r\n      systemRoleName: '系統角色名稱',\r\n      selectDeleteCodes: '請勾選要刪除的系統角色。',\r\n      userRoleNotFound: '選擇的系統角色不存在，請重新整理頁面。',\r\n      userConflicts: '選擇的項目存在系統帳號資料，不允許刪除。',\r\n      view: '顯示',\r\n      addRole: '新增系統角色',\r\n      enterRoleCode: '請填入系統角色編號',\r\n      enterRoleName: '請填入系統角色名稱',\r\n      permissionSetting: '權限設定',\r\n      codeRuleValidate: '系統角色編號字數長度必須介於 0 和 20。',\r\n      nameRuleValidate: '系統角色名稱字數長度必須介於 0 和 64。',\r\n      permissionRuleValidate: '請至少選擇一個權限設定。',\r\n      moduleNotFound: '選擇的權限設定不存在，請重新整理頁面。',\r\n      userRoleCodeExists: '系統角色編號已經被使用，請試試其他系統角色編號。'\r\n    },\r\n    // 定位屬性設定\r\n    objectFeatures: {\r\n      category: '類別',\r\n      role: '角色',\r\n      group: '群組',\r\n      categoryCode: '類別編號',\r\n      categoryName: '類別名稱',\r\n      icon: '圖示',\r\n      roleCode: '角色編號',\r\n      roleName: '角色名稱',\r\n      groupCode: '群組編號',\r\n      groupName: '群組名稱',\r\n      modifiesAt: '最近更新時間',\r\n      pleaseSelectDeleteCategory: '請選擇要刪除的類別',\r\n      pleaseSelectDeleteRole: '請選擇要刪除的角色',\r\n      pleaseSelectDeleteGroup: '請選擇要刪除的群組',\r\n      objectTypeNotFoundVerification: '選擇的類別不存在，請重新整理頁面。',\r\n      objectRoleNotFoundVerification: '選擇的角色不存在，請重新整理頁面。',\r\n      objectGroupNotFoundVerification: '選擇的群組不存在，請重新整理頁面。',\r\n      objectConflictsVerification: '選擇的項目存在定位對象資料，不允許刪除。',\r\n      eventConflictsVerification: '選擇的項目存在事件定義資料，不允許刪除。',\r\n      monitorConflicts: '選擇的項目存在帳號權限設定資料，不允許刪除。',\r\n      addLocationAttribute: '新增定位屬性',\r\n      pleaseSelectIcon: '請選擇圖示',\r\n      pleaseSelectLocationAttribute: '請選擇定位屬性 *',\r\n      inputCode: '請輸入編號 *',\r\n      inputName: '請輸入名稱 *',\r\n      locationAttributeRequiredVerification: '請選擇定位屬性。',\r\n      codeRequiredVerification: '請填入編號。',\r\n      codeTypeVerification: '編號字數長度必須介於0和20。',\r\n      nameRequiredVerification: '請填入名稱。',\r\n      nameTypeVerification: '名稱字數長度必須介於0和64。',\r\n      iconRequiredVerification: '請選擇圖示。',\r\n      objectCodeExistsVerification: '編號已經被使用，請試試其他編號。',\r\n      code: '編號',\r\n      name: '名稱',\r\n      excelExampleDownload: 'Excel範例檔下載',\r\n      pleaseSelectUploadImportFiles: '請選擇要上傳匯入的檔案',\r\n      uploadFiles: '待上傳檔案：',\r\n      fileFormatForxls: '檔案格式必須爲.xls',\r\n      systemReadsPens: '(系統最多讀取200筆)',\r\n      uploadFileForExcel: '上傳檔案必須為Excel。',\r\n      pleaseSelectUploadFiles: '請選擇上傳檔案。',\r\n      pleaseSelectALocationAttribute: '請選擇一項定位屬性。',\r\n      correctInformation: '正確匯入資料有',\r\n      errorInformation: '錯誤資料有',\r\n      errorCode: '錯誤編號有：',\r\n      locationAttributeName: '定位屬性名稱'\r\n    },\r\n    // 無線基站設定\r\n    baseStation: {\r\n      stationConfiguration: '基站配置',\r\n      sid: '基站序號',\r\n      name: '基站名稱',\r\n      IP: 'IP位置',\r\n      planeName: '所在區域',\r\n      systemVersion: '系統版本',\r\n      appVersion: 'APK版本',\r\n      isAlive: '連線狀態',\r\n      modifiesAt: '系統更新時間',\r\n      correctImportedError: '正確匯入資料有',\r\n      errorInformation: '錯誤資料有',\r\n      errorCode: '錯誤編號有',\r\n      planePosition: '平面位置',\r\n      planeLayerAtLeastError: '平面圖層至少選擇至 樓層/區域/子區域',\r\n      unableCoordinatePositioning: '此平面沒有上傳地圖，無法進行座標設定',\r\n      selectCorrespondingClass: '請先選擇對應的階層',\r\n      XCoordinate: 'X座標',\r\n      YCoordinate: 'Y座標',\r\n      flatArea: '平面地區',\r\n      inputSid: '請輸入基站序號',\r\n      inputName: '請輸入名稱',\r\n      inputSidCode: '請輸入SID編號',\r\n      lastConnectionTime: '最後連線時間',\r\n      fieldInformation: '欄位資料',\r\n      selectTheArea: '請選擇所在區域',\r\n      selectTheUploadedFile: '請選擇上傳的檔案',\r\n      addBaseStation: '新增基站',\r\n      baseStationIncorrectDuplicatedError: '輸入的基站序號有誤或重複',\r\n      sidCodeLengthError: 'SID編號長度必須介於0和20',\r\n      nameLengthError: '名稱長度必須介於0和20',\r\n      addCamera: '新增攝影機',\r\n      prioritySeq: '優先順序',\r\n      cameraId: '攝影機ID',\r\n      cameraNo: '攝影機編號',\r\n      cameraName: '攝影機名稱'\r\n    },\r\n    // 裝置資料設定\r\n    deviceInformation: {\r\n      fieldFilterToolTip: '下拉框中沒有被選中的列會被隱藏掉',\r\n      deviceMAC: '裝置MAC',\r\n      deviceName: '裝置名稱',\r\n      locationObject: '定位對象',\r\n      pairObject: '配對物件',\r\n      locationRegion: '所在區域',\r\n      locationTime: '定位時間',\r\n      residualBattery: '剩餘電量',\r\n      pm25: 'PM2.5濃度',\r\n      tvoc: 'TVOC濃度',\r\n      temperature: '溫度',\r\n      humidity: '濕度',\r\n      pm25GetTime: 'PM2.5濃度回報時間',\r\n      tvocGetTime: 'TVOC濃度回報時間',\r\n      temperatureGetTime: '溫度回報時間',\r\n      humidityGetTime: '濕度回報時間',\r\n      heartRate: '心律',\r\n      latestHeartRateTime: '心律回報時間',\r\n      sbp: '收縮壓',\r\n      latestSbpTime: '收縮壓回報時間',\r\n      dbp: '舒張壓',\r\n      latestDbpTime: '舒張壓回報時間',\r\n      connectionState: '連線狀態',\r\n      latestBatteryTime: '電量回報時間',\r\n      softwareVersion: '軟體版本',\r\n      modifiesAt: '最近更新時間',\r\n      deviceNotFoundVerification: '選擇的裝置不存在，請重新整理頁面。',\r\n      pleaseSelectDeleteDevice: '請選擇要刪除的裝置。',\r\n      pleaseSelectRemovePairItem: '請選擇要解除配對的項目。',\r\n      pleaseConfirmOperationCorrectness: '請確認項目操作是否正確。',\r\n      regionNotUploadPlaneMap: '選擇區域沒有上傳平面圖，無法顯示地圖。',\r\n      noPlaneInformation: '暫無平面相關訊息。',\r\n      noCoordinateInformation: '暫無座標相關訊息。',\r\n      addDeviceInformation: '新增裝置資料',\r\n      inputDeviceMAC: '請輸入裝置MAC *',\r\n      inputDeviceName: '請輸入裝置名稱 *',\r\n      selectPairObject: '請選擇配對物件',\r\n      pidRequiredVerification: '請填入裝置MAC。',\r\n      pidTypeVerification: '裝置MAC字數長度必須介於0和20。',\r\n      nameRequiredVerification: '請填入裝置名稱。',\r\n      nameTypeVerification: '裝置名稱字數長度必須介於0和32。',\r\n      objectNotFoundVerification: '選擇的物件不存在，請重新整理頁面。',\r\n      objectDeviceExistsVerification: '選取的物件已經被綁定使用，請試試其他物件。',\r\n      devicePidExistsVerification: '裝置MAC已經被使用，請試試其他裝置MAC。',\r\n      excelExampleDownload: 'Excel範例檔下載',\r\n      pleaseSelectUploadImportFiles: '請選擇要上傳匯入的檔案',\r\n      uploadFiles: '待上傳檔案：',\r\n      fileFormatForxls: '檔案格式必須爲.xls',\r\n      systemReadsPens: '(系統最多讀取200筆)',\r\n      uploadFileForExcel: '上傳檔案必須為Excel。',\r\n      pleaseSelectUploadFiles: '請選擇上傳檔案。',\r\n      correctInformation: '正確匯入資料有',\r\n      errorInformation: '錯誤資料有',\r\n      errorCode: '錯誤編號有：',\r\n      currentPairDevice: '目前配對裝置：'\r\n    },\r\n    // 錨地設定\r\n    anchor: {\r\n      anchorConfiguration: '錨點配置',\r\n      anchorMac: '錨點Mac',\r\n      anchorName: '錨點名稱',\r\n      inputAnchorMac: '請輸入錨點Mac',\r\n      inputPIDCode: '請輸入PID編號',\r\n      addAnchor: '新增錨點',\r\n      anchorMacIncorrectDuplicatedError: '輸入的PID編號有誤或重複',\r\n      anchorMacLengthError: '錨點Mac長度必須介於0和20'\r\n    },\r\n    // Guard\r\n    guard: {\r\n      guardConfiguration: 'Guard配置',\r\n      pleaseConfirmOperationCorrectness: '請確認項目操作是否正確。',\r\n      pleaseSelectRemovePairItem: '請選擇要解除配對的項目。',\r\n      code: '序號',\r\n      guardName: 'Guard名稱',\r\n      pairObject: '配對對象',\r\n      region: '區域',\r\n      detectMode: '偵測模式',\r\n      enableOrClose: '啟用／關閉',\r\n      connectionState: '連線狀態',\r\n      synchronousState: '同步狀態',\r\n      modifiesAt: '最近更新時間',\r\n      leaveBed: '離床偵測',\r\n      stayTimeout: '逾時偵測',\r\n      regularRound: '巡察提醒',\r\n      enable: '啟用',\r\n      close: '關閉',\r\n      normal: '正常',\r\n      offline: '離線',\r\n      synchronizing: '同步中',\r\n      synchronized: '已同步',\r\n      dataSynchronizationUpdate: '資料同步更新中，請稍後再試。',\r\n      guardNotFoundVerification: '選擇的Guard不存在，請重新整理頁面。',\r\n      pleaseSelectDeleteGuard: '請選擇要刪除的Guard。',\r\n      pleaseSelectPlaneMap: '請選擇平面圖',\r\n      search: '搜索',\r\n      planeLayerAtLeastError: '平面圖層至少選擇至 樓層/區域/子區域',\r\n      planeNoUploadCoordinatesError: '此平面沒有上傳地圖，無法進行座標設定。',\r\n      pleaseSelectCorrespondingClass: '請先選擇對應的階層。',\r\n      excelExampleDownload: 'Excel範例檔下載',\r\n      pleaseSelectUploadImportFiles: '請選擇要上傳匯入的檔案',\r\n      uploadFiles: '待上傳檔案：',\r\n      fileFormatForxls: '檔案格式必須爲.xls',\r\n      systemReadsPens: '(系統最多讀取200筆)',\r\n      uploadFileForExcel: '上傳檔案必須為Excel。',\r\n      pleaseSelectUploadFiles: '請選擇上傳檔案。',\r\n      correctInformation: '正確匯入資料有',\r\n      errorInformation: '錯誤資料有',\r\n      errorCode: '錯誤編號有：',\r\n      addGuard: '新增Guard',\r\n      basicInformation: '基本資料',\r\n      inputCode: '請輸入序號 *',\r\n      inputName: '請輸入名稱 *',\r\n      pleaseSelectPairObject: '請選擇配對對象',\r\n      pleaseSelectRegion: '請選擇區域',\r\n      pleaseSelectDetectMode: '請選擇偵測模式',\r\n      bodyTemp: '溫差閥值',\r\n      bodySize: '人／物體大小閥值',\r\n      bedWidth: '床寬(m)',\r\n      bedLength: '床長(m)',\r\n      ceilingHeight: '天花板高度(m)',\r\n      stopAlarmTime: '停留報警時間(s)',\r\n      bathroomLength: '浴室長(m)',\r\n      bathroomWidth: '浴室寬(m)',\r\n      intervalTime: '每次巡視時間間隔(s)',\r\n      ipSetting: 'IP設定',\r\n      rawDataCollectionSetting: 'Raw Data收集設定',\r\n      ipCameraEnableOrClose: 'IP Camera啟用／關閉',\r\n      ipCameraCircleFrequency: 'IP Camera取圓頻率(ms)',\r\n      logUploadFrequency: 'log上傳頻率(sec)',\r\n      logPackingSize: 'log打包大小(KB)',\r\n      codeRequiredVerification: '請填入序號。',\r\n      codeTypeVerification: '序號字數長度必須介於0和20。',\r\n      nameRequiredVerification: '請填入名稱。',\r\n      nameTypeVerification: '名稱字數長度必須介於0和64。',\r\n      bodyTempRequiredVerification: '請填入溫差閥值。',\r\n      bodySizeRequiredVerification: '請填入人／物體大小閥值。',\r\n      bedWidthRequiredVerification: '請填入床寬度。',\r\n      bedLengthRequiredVerification: '請填入床長度。',\r\n      ceilingHeightRequiredVerification: '請填入天花板高度。',\r\n      stopAlarmTimeRequiredVerification: '請填入停留報警時間。',\r\n      bathroomLengthRequiredVerification: '請填入浴室長度。',\r\n      bathroomWidthRequiredVerification: '請填入浴室寬度。',\r\n      intervalTimeRequiredVerification: '請填入每次巡視時間間隔。',\r\n      bodyTempTypeVerification: '溫差閥值必須介於0和15。',\r\n      bodySizeTypeVerification: '人／物體大小閥值必須介於0和225。',\r\n      bedWidthTypeVerification: '床寬度必須介於0和15。',\r\n      bedLengthTypeVerification: '床長度必須介於0和15。',\r\n      ceilingHeightTypeVerification: '天花板高度必須介於0和15。',\r\n      stopAlarmTimeTypeVerification: '停留報警時間必須介於0和65535。',\r\n      bathroomLengthTypeVerification: '浴室長度必須介於0和15。',\r\n      bathroomWidthTypeVerification: '浴室寬度必須介於0和15。',\r\n      intervalTimeTypeVerification: '每次巡視時間間隔必須介於0和65535。',\r\n      objectNotFoundVerification: '選擇的對象不存在，請重新整理頁面。',\r\n      objectGuardExistsVerification: '選取的對象已經被綁定使用，請試試其他對象。',\r\n      guardCodeExistsVerification: '序號已經被使用，請試試其他序號。',\r\n      columnMustNumber: '該欄位必須是數字。',\r\n      columnMustPositiveInteger: '該欄位必須是正整數。',\r\n      leastSocketServerIp: '至少要有一項Socket Server IP。',\r\n      enableFusionGuardMustItem: '為啟用Fusion Guard的必填項目！',\r\n      xCoordinate: 'X座標',\r\n      yCoordinate: 'Y座標',\r\n      basicInformationMustCompleteForEnableGuard: '基本資料必須填寫完整，才能啟用Guard！',\r\n      currentPairObject: '目前配對對象：',\r\n      planeNotFoundVerification: '選擇的區域不存在，請重新整理頁面。',\r\n      needPortField: '格式錯誤，需要帶有port欄位。'\r\n    },\r\n    // 定位對象設定\r\n    objectData: {\r\n      guard: '配對Guard',\r\n      objectNotFound: '選擇的定位對象不存在。',\r\n      objectTypeNotFound: '選擇的類別不存在。',\r\n      objectRoleNotFound: '選擇的角色不存在。',\r\n      objectGroupNotFound: '選擇的群組不存在。',\r\n      deviceNotFound: '選擇的配對裝置不存在。',\r\n      guardNotFound: '選擇的配對Guard不存在。',\r\n      deviceObjectExists: '選擇的配對裝置已經綁定其他定位對象。',\r\n      guardObjectExists: '選擇的配對Guard已經綁定其他定位對象。'\r\n    },\r\n    // 平面資圖配置\r\n    planePosition: {\r\n      sitePlanConfig: '平面配置',\r\n      addSitePlanConfig: '新增平面位置',\r\n      updateFileMap: '請上傳平面圖',\r\n      updateOneFileMap: '請上傳一張平面圖',\r\n      flatPositionError: '請選擇要建立的平面位置',\r\n      flatFactoryError: '請選擇所屬對應廠區',\r\n      flatBuildingError: '請選擇所屬對應建築物',\r\n      flatFloorError: '請選擇所屬對應樓層',\r\n      flatRegionError: '請選擇所屬對應區域',\r\n      flatCode: '平面圖編號',\r\n      flatName: '平面圖名稱',\r\n      flatCodeError: '平面圖編號不能為空',\r\n      flatNameError: '平面圖名稱不能為空',\r\n      selectTheDeletedObject: '請選擇刪除的物件',\r\n      delSelectItemsError: '這個平面已經有綁定基站，要刪除這個平面之前要先刪除綁定的基站。',\r\n      stationConflictsError: '選擇的項目存在基站資料，不允許刪除。',\r\n      eventConflictsError: '選擇的項目存在事件定義資料，不允許刪除。',\r\n      planeConflictsError: '選擇的項目存在平面資料，不允許刪除。',\r\n      anchorConflicts: '選擇的項目存在錨點資料，不允許刪除。',\r\n      monitorConflicts: '選擇的項目存在使用者監控資料，不允許刪除。',\r\n      positioningPlanePosition: '定位平面位置',\r\n      selectCorrespondingLevelError: '請選擇所屬對應的階層',\r\n      readjustStationAnchorPoint: '平面地區已更動，需要重新調整基站和錨點的配置。',\r\n      dragNoticeError: '請注意，平面位置更動後，需要重新調整基站和錨點的配置。',\r\n      savePromptingError: '執行此動作後，您將需要重新調整基站和錨點的配置，請確認是否執行？',\r\n      deletePlanesChildPositionError: '請注意，已經移除平面上的基站和錨點。',\r\n      isPoptip: '您確定要重置此項目?',\r\n      establishOrder: '建立順序為廠區>建築物>樓層>區域>子區域',\r\n      establishAtLeastOneFactoryAreaFirst: '請先至少建立一廠區',\r\n      establishAtLeastOneBuildingAreaFirst: '請先至少建立一建築物',\r\n      establishAtLeastOneFloorAreaFirst: '請先至少建立一樓層',\r\n      establishAtLeastOneSubRegionAreaFirst: '請先至少建立一區域',\r\n      selectAndFillInFloor: '請選擇並填入樓層',\r\n      floorInputCaveat: '請填入1-99任一數字',\r\n      fieldInformationCaveat: '請填寫欄位資料',\r\n      zoomRatioMustTheSame: '圖檔放大縮小的倍率必須一致',\r\n      validatePlaneCodeError: '請填入平面圖編號',\r\n      planeCodeTypeError: '輸入的平面圖編號有誤或重複',\r\n      planeCodeTypeLengthError: '平面圖編號字數長度必須介於 0 和 20。',\r\n      selectFool: '請選擇樓層',\r\n      validateFloorError: '選擇的樓層已存在，請試試建立其他樓層。',\r\n      planeNameError: '請填入平面圖名稱',\r\n      planeNameLengthError: '平面圖名稱字數長度必須介於 0 和 64。',\r\n      addPlaneError: '新增平面圖成功，但無法上載平面圖。',\r\n      itemHasNotSetCoordinate: '此項目尚未設定座標',\r\n      floorPlanPreview: '平面圖預覽'\r\n    },\r\n    // 事件定義設定\r\n    eventDefinition: {\r\n      singleEvent: '獨立事件',\r\n      planeEvent: '區域事件',\r\n      eventCode: '事件編號',\r\n      eventTemplate: '模板名稱 ',\r\n      addEventDefinition: '新增事件定義',\r\n      selectTemplate: '請選擇模板 *',\r\n      enterEventCode: '請輸入事件編號 *',\r\n      enterEventName: '請輸入事件名稱 *',\r\n      enterEventNameNoStar: '請輸入事件名稱',\r\n      selectEventCategory: '請選擇事件類別 *',\r\n      selectEventCategoryNoStar: '請選擇事件類別',\r\n      selectPlane: '請選擇區域 *',\r\n      enterValue: '請輸入數值',\r\n      selectStartTime: '請選擇起始時間',\r\n      selectEndTime: '請選擇結束時間',\r\n      selectSponsorType: '請選擇發起者屬性',\r\n      selectSponsorRole: '請選擇發起者角色',\r\n      selectSponsorGroup: '請選擇發起者群組',\r\n      selectParticipantType: '請選擇參與者屬性',\r\n      selectParticipantRole: '請選擇參與者角色',\r\n      selectParticipantGroup: '請選擇參與者群組',\r\n      selectNotifierType: '請選擇通知屬性',\r\n      selectNotifierRole: '請選擇通知角色',\r\n      selectNotifierGroup: '請選擇通知群組',\r\n      selectNotifierAccount: '請選擇通知帳號',\r\n      enterNotifierMsg: '請輸入通知訊息',\r\n      selectDeleteEvent: '請勾選要刪除的事件。',\r\n      threshold: '警示條件設定',\r\n      below: '低於',\r\n      over: '超過',\r\n      stayOver: '停留超過',\r\n      batteryPercentage: '% 電量',\r\n      minAlert: '分鐘，進行警示',\r\n      minRemind: '分鐘未巡查，進行提醒',\r\n      intervals: '管制時間設定',\r\n      between: '到',\r\n      sensorDataDriven: '數據自訂事件設定',\r\n      sensorDataDrivenData: '數據',\r\n      it: '<',\r\n      ite: '<=',\r\n      gt: '>',\r\n      gte: '>=',\r\n      dataDrivenRuleSource: '來源',\r\n      dataDrivenRuleComp: '條件',\r\n      enterSource: '請選擇數據來源',\r\n      enterComp: '請選擇條件',\r\n      dataDrivenRuleRepeat: '重複出現',\r\n      dataDrivenRuleCount: '次',\r\n      dataDrivenRuleDuration: '持續',\r\n      dataDrivenRuleDurationSec: '秒',\r\n      validatePlaneCode: '區域階層至少請選擇至 樓層 / 區域 / 子區域',\r\n      validateCategory: '請選擇事件類別。',\r\n      validateSponsor: '發起者屬性/角色/群組，請擇一輸入',\r\n      validateParticipant: '參與者屬性/角色/群組，請擇一輸入',\r\n      enterEventCodeValidate: '請填入事件編號。',\r\n      eventCodeValidate: '事件編號字數長度必須介於 0 和 20。',\r\n      enterEventNameValidate: '請填入事件名稱。',\r\n      eventNameValidate: '事件名稱字數長度必須介於 0 和 64。',\r\n      templateValidate: '請選擇模板。',\r\n      enterThresholdValidate: '請填入警示條件。',\r\n      thresholdValidatePattern: '警示條件必須為數字(正整數)。',\r\n      thresholdValidateMax: '警示條件不能大於：20 字元。',\r\n      msgValidate: '通知訊息字數長度必須介於 0 和 256。',\r\n      badInterval: '管制時間必須符合以下格式：00:00:00。',\r\n      sponsorObjectTypeNotFound: '選擇的發起者屬性不存在，請重新整理頁面。',\r\n      sponsorObjectRoleNotFound: '選擇的發起者角色不存在，請重新整理頁面。',\r\n      sponsorObjectGroupNotFound: '選擇的發起者群組不存在，請重新整理頁面。',\r\n      participantObjectTypeNotFound: '選擇的參與者屬性不存在，請重新整理頁面。',\r\n      participantObjectRoleNotFound: '選擇的參與者角色不存在，請重新整理頁面。',\r\n      participantObjectGroupNotFound: '選擇的參與者群組不存在，請重新整理頁面。',\r\n      thresholdNotFound: '選擇的警示條件不存在，請重新整理頁面。',\r\n      notifierObjectTypeNotFound: '選擇的通知屬性不存在，請重新整理頁面。',\r\n      notifierObjectRoleNotFound: '選擇的通知角色不存在，請重新整理頁面。',\r\n      notifierObjectGroupNotFound: '選擇的通知群組不存在，請重新整理頁面。',\r\n      notifierUserNotFound: '選擇的通知帳號不存在，請重新整理頁面。',\r\n      eventCodeExists: '事件編號已經被使用，請試試其他事件編號。',\r\n      warningCondition: '警示條件',\r\n      controlTime: '管制時間',\r\n      startTime: '起始時間',\r\n      entTime: '結束時間',\r\n      eventCategory: '事件類別',\r\n      planeCode: '區域編號',\r\n      participantType: '參與者屬性',\r\n      participantRole: '參與者角色',\r\n      participantGroup: '參與者群組',\r\n      sponsorType: '發起者屬性',\r\n      sponsorRole: '發起者角色',\r\n      sponsorGroup: '發起者群組',\r\n      notifierType: '通知屬性',\r\n      notifierRole: '通知角色',\r\n      notifierGroup: '通知群組',\r\n      notifierUser: '通知帳號',\r\n      notifierMsg: '通知訊息',\r\n      addControlTime: '請點選 + 新增管制時間',\r\n      planeSetting: '區域設定',\r\n      sponsorTypeSetting: '發起者屬性設定',\r\n      sponsorRoleSetting: '發起者角色設定',\r\n      sponsorGroupSetting: '發起者群組設定',\r\n      participantTypeSetting: '參與者屬性設定',\r\n      participantRoleSetting: '參與者角色設定',\r\n      participantGroupSetting: '參與者群組設定',\r\n      notifierTypeSetting: '通知屬性設定',\r\n      notifierRoleSetting: '通知角色設定',\r\n      notifierGroupSetting: '通知群組設定',\r\n      notifierUserSetting: '通知帳號設定',\r\n      thresholdValidate: '警示條件必須為數字且長度為：20。',\r\n      searchEventTemplate: '事件模板',\r\n      searchSelectTemplate: '請選擇模板',\r\n      eventNotFound: '選擇的事件定義不存在，請重新整理頁面。',\r\n      taskConflicts: '求救事件尚未解除，無法刪除此事件定義。',\r\n      areaName: '區域名稱'\r\n    },\r\n    // 韌體更新\r\n    otaUpdate: {\r\n      uploadRecord: '上傳紀錄',\r\n      scheduleList: '排程清單',\r\n      fileName: '檔案名稱',\r\n      name: '韌體更新',\r\n      uploadTime: '上傳時間',\r\n      uploadOta: '上傳韌體檔案',\r\n      station: '基站(系統)',\r\n      stationApk: '基站(APK)',\r\n      anchor: '錨點',\r\n      wristband: '手環',\r\n      button: '按鈕',\r\n      tag: '定位設備',\r\n      fromVersion: '舊版本',\r\n      toVersion: '新版本',\r\n      description: '描述',\r\n      activeName: '是否啟用',\r\n      code: '版號',\r\n      nordicVersion: 'nordic版號',\r\n      bootloaderVersion: 'Bootloader版號',\r\n      stVersion: 'st版號',\r\n      nordicFileName: 'nordic檔案名稱',\r\n      bootloaderFileName: 'bootloader檔案名稱',\r\n      stFileName: 'st檔案名稱',\r\n      uploadFile: '請上傳檔案',\r\n      fileNameFormatReference: '檔案名稱格式參考',\r\n      uploadNordicFile: '請上傳nordic檔案',\r\n      uploadbootLoaderFile: '請上傳bootloader檔案',\r\n      uploadStFile: '請上傳st檔案',\r\n      inputDescription: '請輸入檔案說明',\r\n      setUpdateScheduling: '建立更新排程',\r\n      queryUpdateResults: '查詢更新結果',\r\n      back: '返回',\r\n      completedQuantity: '已完成數量',\r\n      unfinishedQuantity: '未完成數量',\r\n      updateTime: '更新時間',\r\n      deviceName: '裝置名稱',\r\n      updateObject: '更新對象',\r\n      originalVersion: '原始版本',\r\n      newVersion: '更新後版本',\r\n      currentVersion: '目前版本',\r\n      toUpdateVersion: '應更新版本',\r\n      errorMessage: '更新失敗原因',\r\n      schedulingName: '排程名稱',\r\n      targetPlane: '區域',\r\n      count: '需更新數量',\r\n      actionSuccess: '已更新數量',\r\n      actionUnsuccess: '未更新數量',\r\n      schedulingEnable: '排程啟用',\r\n      updateType: '更新類別',\r\n      updateRole: '更新角色',\r\n      updateGroup: '更新群組',\r\n      enable: '啟用',\r\n      close: '關閉',\r\n      noEnable: '不啟用',\r\n      pleaseSelectDeleteStation: '請選擇要刪除的基站(系統)排程',\r\n      pleaseSelectDeleteStationApk: '請選擇要刪除的基站(APK)排程',\r\n      pleaseSelectDeleteAnchor: '請選擇要刪除的錨點排程',\r\n      pleaseSelectDeleteWristband: '請選擇要刪除的手環排程',\r\n      pleaseSelectDeleteButton: '請選擇要刪除的按鈕排程',\r\n      pleaseSelectDeleteTag: '請選擇要刪除的定位設備排程',\r\n      otaScheduleNotFoundVerification: '選擇的排程不存在，請重新整理頁面。',\r\n      updateCycle: '更新週期',\r\n      sunday: '星期日',\r\n      monday: '星期一',\r\n      tuesday: '星期二',\r\n      wednesday: '星期三',\r\n      thursday: '星期四',\r\n      friday: '星期五',\r\n      saturday: '星期六',\r\n      setUpdateSchedulingSuccessful: '建立更新排程成功。',\r\n      inputSchedulingName: '請輸入排程名稱 *',\r\n      inputSchedulingCode: '請輸入排程代號 *',\r\n      pleaseSelectStartsAt: '請選擇開始時間',\r\n      pleaseSelectEndsAt: '請選擇結束時間',\r\n      pleaseSelectCategory: '請選擇類別',\r\n      pleaseSelectUpdateType: '請選擇更新類別',\r\n      pleaseSelectUpdateRole: '請選擇更新角色',\r\n      pleaseSelectUpdateGroup: '請選擇更新群組',\r\n      pleaseSelectTargetPlane: '請選擇區域',\r\n      pleaseSelectNotifierUserAccounts: '請選擇更新帳號',\r\n      targetPlaneRequiredVerification: '請選擇區域。',\r\n      nameRequiredVerification: '請填入排程名稱。',\r\n      nameTypeVerification: '排程名稱字數長度必須介於0和64。',\r\n      codeRequiredVerification: '請填入排程代號。',\r\n      codeTypeVerification: '排程代號字數長度必須介於0和20。',\r\n      startsAtRequiredVerification: '請選擇開始時間。',\r\n      typeRequiredVerification: '請選擇類別。',\r\n      selectTargetPlaneLength: '區域階層至少選擇至 樓層/區域/子區域',\r\n      targetPlaneNotFoundVerification: '選擇的區域不存在，請重新整理頁面。',\r\n      updateTypeRoleGroupVerification: '更新類別/角色/群組，請擇一輸入。',\r\n      targetObjectTypeNotFoundVerification: '選擇的更新類別不存在，請重新整理頁面。',\r\n      targetObjectRoleNotFoundVerification: '選擇的更新角色不存在，請重新整理頁面。',\r\n      targetObjectGroupNotFoundVerification: '選擇的更新群組不存在，請重新整理頁面。',\r\n      notifierUserNotFoundVerification: '選擇的更新帳號不存在，請重新整理頁面。',\r\n      otaScheduleCodeExistsVerification: '排程代號已經被使用，請試試其他排程代號。',\r\n      recursiveVersionFound: '檔案上傳失敗，請確認上傳檔案版本是否正確。',\r\n      badFilename: '檔案上傳失敗，檔案名稱格式不正確，請確認後再上傳檔案。',\r\n      badCycleVerification: '週期格式錯誤。',\r\n      updateDate: '更新起訖日期',\r\n      pleaseInputSchedulingName: '請輸入排程名稱',\r\n      queryUpdateResultsSuccessful: '查詢更新結果成功。',\r\n      updateTimeRequiredVerification: '請選擇更新時間。',\r\n      uploadSuccess: '檔案上傳成功。'\r\n    },\r\n    // 授權管理\r\n    licenseManagement: {\r\n      licenseRequest: '產生授權請求',\r\n      enterCustomerId: '* 請輸入客戶帳號',\r\n      enterSerialNum: '* 請輸入序號',\r\n      selectService: '請選擇授權主機',\r\n      useRegCode: '套用註冊碼',\r\n      enterRegCode: '* 請輸入註冊碼',\r\n      view: '查看',\r\n      enterCustomerIdHint: '請輸入客戶帳號',\r\n      enterSerialNumHint: '請輸入序號',\r\n      selectServiceHint: '請選擇授權主機',\r\n      customerIdRuleValidate: '客戶帳號字數長度必須介於 0 和 64。',\r\n      serialNumRuleValidate: '序號字數長度必須介於 0 和 256。',\r\n      copy: '複製',\r\n      serviceNotFound: '選擇的授權主機不存在，請重新整理頁面。',\r\n      customerNotFound: '輸入的客戶帳號不存在，請重新輸入。',\r\n      enterRegCodeHint: '請輸入註冊碼',\r\n      regCodeRuleValidate: '註冊碼字數長度必須介於 0 和 2048。',\r\n      regSuccessful: '套用註冊碼成功。',\r\n      badRegistrationCode: '註冊碼不正確，請重新輸入。',\r\n      customerName: '客戶名稱',\r\n      totalService: '授權主機數量',\r\n      validStation: '授權基站數量',\r\n      validDevice: '授權裝置數量',\r\n      expiredTime: '授權到期日',\r\n      status: '授權狀態',\r\n      id: '編號',\r\n      serviceSerial: '授權主機序號',\r\n      serviceName: '授權主機名稱',\r\n      serviceIp: '主機IP',\r\n      licenseTime: '啟用時間',\r\n      validServiceCount: '有效',\r\n      invalidServiceCount: '無效',\r\n      slash: ' / ',\r\n      copyRequestCode: '複製授權請求碼',\r\n      noError: '主機授權無異常',\r\n      licenseExpired: '主機授權失效',\r\n      licenseExpiredSoon: '主機授權即將逾期'\r\n    },\r\n    // 歷史報表\r\n    historyReport: {\r\n      eventTemplate: '事件分類',\r\n      startsAt: '發起時間',\r\n      action: '事件狀態',\r\n      eventName: '事件名稱',\r\n      eventPlane: '事件發生區域',\r\n      sponsorObjects: '事件發起者',\r\n      description: '事件紀錄',\r\n      startAndEndTime: '請選擇事件發生起訖日期',\r\n      untreated: '未解除',\r\n      treating: '解除中',\r\n      treated: '已解除',\r\n      note: '(若不選擇日期 則匯出最近7天)',\r\n      exportEvent: '匯出事件'\r\n    },\r\n    // 攝影機設定\r\n    camera: {\r\n      addCamera: '新增攝影機',\r\n      editCamera: '編輯攝影機',\r\n      cameraCode: '攝影機編號',\r\n      cameraName: '攝影機名稱',\r\n      cameraIp: '攝影機IP',\r\n      streamUrl: '攝影機串流路徑',\r\n      cameraVideoPath: '攝影機錄影存檔路徑',\r\n      taskVideoPath: '事件錄影存檔路徑',\r\n      enterCameraCode: '請輸入攝影機編號',\r\n      enterCameraName: '請輸入攝影機名稱',\r\n      enterCameraIp: '請輸入攝影機IP',\r\n      enterStreamUrl: '請輸入攝影機串流路徑',\r\n      enterCameraVideoPath: '請輸入攝影機錄影存檔路徑',\r\n      enterTaskVideoPath: '請輸入事件錄影存檔路徑',\r\n      cameraNo: '攝影機編號',\r\n      cameraIpPosition: '攝影機IP位址',\r\n      cameraConnStatus: '連線狀態'\r\n    },\r\n    // Log錄製\r\n    logRecording: {\r\n      addLogRecording: '新增裝置定位日誌',\r\n      name: '日誌名稱',\r\n      chooseDevice: '請選擇裝置',\r\n      choosePlane: '請選擇區域',\r\n      chooseAction: '請選擇狀態',\r\n      chooseValidTime: '請選擇有效時間',\r\n      oneHour: '一小時',\r\n      threeHour: '三小時',\r\n      twelfthHour: '十二小時',\r\n      oneDay: '一天',\r\n      threeDay: '三天',\r\n      fiveDay: '五天',\r\n      oneWeek: '一週',\r\n      enterDescription: '請輸入描述',\r\n      devicesName: '裝置名稱',\r\n      devicesID: '裝置ID',\r\n      devicesPID: '裝置MAC',\r\n      reportUrl: '日誌分析地址',\r\n      logUrl: '日誌地址',\r\n      status: '狀態',\r\n      plane: '區域',\r\n      message: '訊息',\r\n      description: '描述',\r\n      startsAt: '開始時間',\r\n      finishesAt: '結束時間',\r\n      expiresAt: '過期時間',\r\n      modifiesAt: '最近更新時間',\r\n      action: '操作',\r\n      waiting: '準備錄製中',\r\n      logCollecting: '錄製中',\r\n      logCollected: '停止錄製',\r\n      reportCreating: '正在產生分析報表',\r\n      logFileCreating: '正在產生日誌檔',\r\n      finished: '已錄製',\r\n      failed: '錄製失敗',\r\n      canceled: '取消錄製',\r\n      enterNameValidate: '請填入日誌名稱',\r\n      eventNameValidateLength: '日誌名稱字數長度必須介於 0 和 32',\r\n      msgValidate: '描述字數長度必須介於 0 和 512',\r\n      deviceNotFoundVerification: '選擇的裝置不存在，請重新整理頁面。',\r\n      planeNotFoundError: '選擇的區域不存在，請重新整理頁面。',\r\n      logNotFound: '選擇的日誌不存在，請重新整理頁面。',\r\n      waitForInitialization: '日誌初始化中，請5秒鐘後再點擊停止錄製。',\r\n      requestUnavailable: '請求失敗，請重新整理頁面。',\r\n      isExport: '是否產生報表',\r\n      logNo: '日誌編號',\r\n      yes: '是',\r\n      no: '否',\r\n      readyRecord: '準備錄製中',\r\n      recording: '錄製中',\r\n      readyExport: '準備產生日誌中',\r\n      exportingLog: '正在產生分析報表',\r\n      exportingFile: '正在產生日誌檔',\r\n      recorded: '已錄製',\r\n      recordFail: '錄製失敗',\r\n      cancel: '已取消',\r\n      stopRecord: '停止錄製'\r\n    },\r\n    // 資產盤點系統\r\n    inventory: {\r\n      pleaseEnter: '請輸入',\r\n      pleaseSelect: '請選擇',\r\n      pleaseUpload: '上傳',\r\n      inputCode: '編號',\r\n      devicesName: '裝置名稱',\r\n      devicesID: '裝置ID',\r\n      devicesPID: '裝置MAC',\r\n      devicesPosition: '裝置位置',\r\n      XCoordinate: 'X座標',\r\n      YCoordinate: 'Y座標',\r\n      locateObject: '定位對象',\r\n      locationRegion: '所在區域',\r\n      latestPositionTime: '最近定位時間',\r\n      hasCounted: '盤點狀態',\r\n      reCounted: '重新盤點',\r\n      refresh: '重新整理',\r\n      latestCountingTime: '最近盤點時間',\r\n      modifiesAt: '最近更新時間',\r\n      action: '操作',\r\n      addIvnentory: '新增資產',\r\n      addTask: '新增盤點任務',\r\n      index: '資產狀態',\r\n      task: '任務清單',\r\n      description: '資產描述',\r\n      categoryCode: '資產類型',\r\n      iconCode: '資產圖示',\r\n      enterNumber: '資產編號',\r\n      chooseKeeper: '保管人',\r\n      chooseManager: '管理人',\r\n      objectCode: '物件',\r\n      enterPurchaseDate: '購買日期',\r\n      uploadPhoto: '上傳圖片',\r\n      serial: '資產序號',\r\n      inputValidateLength20: '字數長度必須介於 1 和 20',\r\n      inputValidateLength32: '字數長度必須介於 1 和 32',\r\n      inputValidateLength64: '字數長度必須介於 1 和 64',\r\n      keeperUserNotFound: '保管人不存在於系統中',\r\n      managerUserNotFound: '管理人不存在於系統中',\r\n      iconNotFound: '圖示不存在於系統中',\r\n      categoryNotFound: '資產類型不存在於系統中',\r\n      objectNotFound: '物件不存在於系統中',\r\n      objectInventoryExists: '已存在相同的物件資產',\r\n      inventoryCodeExists: '已存在相同的編號',\r\n      detail: '資產詳情',\r\n      taskDetail: '任務詳情',\r\n      status: '狀態',\r\n      createsAt: '創建時間',\r\n      eolAt: '產品壽命結束時間',\r\n      active: 'Active',\r\n      repairing: 'Repairing',\r\n      calibrating: 'Calibrating',\r\n      servicing: 'Servicing',\r\n      eol: 'EOL',\r\n      objectName: '定位對象名稱',\r\n      position: '所在區域',\r\n      positionXY: '定位座標',\r\n      images: '資產圖片',\r\n      taskNumber: '任務號碼',\r\n      taskDescription: '任務描述',\r\n      ownerUser: '盤點人',\r\n      finishesAt: '結束時間',\r\n      countedCount: '已盤點',\r\n      uncountedCount: '未盤點',\r\n      inProgress: '進行中',\r\n      finished: '已結束'\r\n    },\r\n    // 系統參數\r\n    systemConfig: {\r\n      group: '群組',\r\n      key: '參數名稱',\r\n      value: '參數值',\r\n      defaultValue: '預設值',\r\n      min: '最小值',\r\n      max: '最大值',\r\n      maxLength: '最大長度',\r\n      unit: '單位',\r\n      description: '參數說明',\r\n      schemaGroupAPI: 'API接口',\r\n      schemaGroupLogging: '日誌',\r\n      schemaGroupHelp: '求救',\r\n      schemaGroupUser: '系統帳號',\r\n      schemaGroupEnvironment: '系統環境',\r\n      schemaGroupPositioning: '定位',\r\n      schemaGroupConnection: '連線管理',\r\n      schemaGroupWeb: '前端網頁',\r\n      descReserveJournalDuration: '日誌保留天數 (i.e. DBA_CleanData)',\r\n      descStationOfflinePeriod: '基站離線判斷閥值: 超過此閥值未收到scan result則判定離線',\r\n      descDeviceOfflinePeriod: '裝置離線判斷閥值: 超過此閥值未收到scan result則判定離線',\r\n      descDeviceOtaRssiThreshold: 'OTA裝置最低訊號強度',\r\n      descGatewayOfflinePeriod: 'Gateway離線判斷閥值: 超過此閥值未收到???則判定離線',\r\n      descSmtpEnable: '是否啟用外部SMTP伺服器',\r\n      descSmtpHost: '外部SMTP伺服器地址',\r\n      descSmtpPort: '外部SMTP伺服器Port',\r\n      descSmtpUsername: '外部SMTP連線帳號',\r\n      descSmtpPassword: '外部SMTP連線密碼',\r\n      descHelpTaskDelayPeriod: '求救任務延遲開始間隔',\r\n      descHelpTurnOffDeviceTimer: '求救關燈排程間隔',\r\n      descHelpTurnOffDeviceRetryCount: '求救關燈重試次數',\r\n      descHelpTaskMinRssiThreshold: '求救任務延遲開始間隔',\r\n      descUserRegisterCodeExpirePeriod: '用戶註冊驗證碼有效間隔',\r\n      descUserRegisterCodeLength: '用戶註冊驗證碼長度',\r\n      descApiAccessTokenExpirePeriod: 'API訪問令牌有效間隔',\r\n      descApiRefreshTokenExpirePeriod: 'API更新令牌有效間隔',\r\n      descApiDefaultSearchActive: 'API預設查詢參數: 查詢結果只含 active 為 true 的資料',\r\n      descApiDefaultMaxSize: 'API預設查詢參數: 查詢結果最大筆數',\r\n      descReportCachePeriod: '裝置訊號取樣間隔',\r\n      descTrackingKalmanFilterEnable: '是否啟用定位結東KF(減緩定位座標飄移距離)',\r\n      descTrackingKalmanFilterMeterRangePerSecond: '提供定位結果KF參數: 每秒移動距離(公尺)',\r\n      descTrackingKalmanFilterAccuracy: '提供定位結果KF參數: Accuracy',\r\n      descLocationKalmanFilterEnable: '是否啟用裝置訊號KF(平滑裝置訊號強度)',\r\n      descLocationKalmanProcessNoise: '提供裝置訊號KF參數: Process Noise',\r\n      descLocationKalmanMeasurementNoise: '提供裝置訊號KF參數: Measurement Noise',\r\n      descPositionPersistencePeriod: '定位結果持久化間隔',\r\n      descPositionHistoryPersistencePeriod: '定位歷史記錄結果持久化間隔',\r\n      descPositioningAlgorithmV2: '定位演算法',\r\n      descPositioningPeroid: '定位週期',\r\n      descPositionMinRssiThreshold: '定位訊號過濾閾值',\r\n      descTrackingRegionTotalCount: '定位結果區域判定參數: 樣本數',\r\n      descTrackingRegionMatchCount: '定位結果區域判定參數: 命中數',\r\n      descMonitorRefreshSecond: '監控資料更新週期',\r\n      descStayTimeoutDefaultInterval: '停留逾時事件預設逾時時間間隔',\r\n      descHistoryMatchCount: '方向一致的向量個數',\r\n      event: '事件'\r\n    },\r\n    // 系統管條理 - ipAddr\r\n    ipAddr: {\r\n      ipError: '請輸入正確的IP地址'\r\n    },\r\n    // 系統版本\r\n    systemSWVersion: {\r\n      name: '模組名稱',\r\n      version: '版本',\r\n      status: '狀態',\r\n      checkAt: '回報時間'\r\n    },\r\n    // apps\r\n    apps: {\r\n      common: {\r\n        autoTreated:'自動解除',\r\n        posParameterCode:'定位演算法',\r\n        deviceType: '配對裝置Type',\r\n        displayStations:'顯示基站',\r\n        monitor: '監控平面',\r\n        object: '定位對象',\r\n        planeSetting: '平面設定',\r\n        configSetting: '參數設定',\r\n        confirm: '確認',\r\n        cancel: '取消',\r\n        indoorRealTimePositioningSystem: '室內即時定位系統',\r\n        device: '配對裝置',\r\n        selectDelete: '刪除選取的項目',\r\n        add: '新增',\r\n        edit: '編輯',\r\n        delete: '刪除',\r\n        cancelAdd: '取消新增',\r\n        cancelEdit: '取消編輯',\r\n        saveAdd: '儲存新增',\r\n        saveEdit: '儲存編輯',\r\n        sid: '基站SID',\r\n        plane: '所在區域',\r\n        isAlive: '連線狀態',\r\n        picEdit: '背景圖檔編輯',\r\n        picRemove: '移除圖檔',\r\n        picUpload: '上傳圖檔',\r\n        picConfirm: '確定刪除背景圖?',\r\n        planeEdit: '編輯監控平面',\r\n        planeSet: '配置監控平面',\r\n        picBackgroundUpload: '上傳背景圖',\r\n        iconPicEdit: 'Icon圖檔編輯',\r\n        iconPicUpload: '上傳Icon圖檔',\r\n        objectDeviceConfirm: '裝置已配對,是否解綁定位對象',\r\n        status: '狀況',\r\n        record: '紀錄',\r\n        recordPlaceholder: '請輸入紀錄...',\r\n        eventAlarm: '事件警報音',\r\n        eventAlarmWindow: '事件通知視窗',\r\n        eventStatus: '狀況處理',\r\n        eventClean: '清除所有事件',\r\n        eventCleanConfirm: '確定清除事件?',\r\n        eventStartTime: '事件紀錄起始時間',\r\n        removeEvent: '解除',\r\n        emergencyEvent: '緊急事件通知',\r\n        objectName: '定位對象名稱',\r\n        deviceMac: '配對裝置MAC',\r\n        occupy: '入住',\r\n        event: '事件',\r\n        people: '人',\r\n        selectDeleteError: '請選取刪除項目',\r\n        abnormalDevice: '裝置異常',\r\n        second: '秒鐘',\r\n        deviceConnectionAbnormal: '裝置連線異常!',\r\n        deviceConnectionAbnormalLatestEvent: '連線異常前狀態：',\r\n        positionX: '坐標軸X',\r\n        positionY: \"坐標軸Y\"\r\n      },\r\n      m20: {\r\n        selectMonitorPlane:'偵測平面',\r\n        selectMonitorObject: '偵測對像',        \r\n      },\r\n      m21: {\r\n        selectService:'發起服務'\r\n      },\r\n      eFence: {\r\n        equipment: '設備',\r\n        name: '定位對象',\r\n        type: '定位類別',\r\n        device: '配對裝置MAC',\r\n        confirmEdit: '裝置已配對',\r\n        people: '人',\r\n        fall: '跌倒',\r\n        normal: '正常',\r\n        positionStation: '目前定位基站',\r\n        event: '事件',\r\n        allPositionStation: '全部定位基站',\r\n        enterAndExit: '進入/離開',\r\n        enter: '進入',\r\n        exit: '離開'\r\n      },\r\n      m01: {\r\n        equipment: '設備',\r\n        addSpecialStatus: '新增特殊狀況',\r\n        babyName: '新生兒名稱',\r\n        region: '地點',\r\n        description: '原因',\r\n        timeoutSetting: '逾時設定',\r\n        time: '分鐘',\r\n        stayTimeout: '停留逾時',\r\n        specialStatus: '特殊狀況',\r\n        baby: '新生兒',\r\n        numberControl: '母嬰同室',\r\n        enter: '進入警示區',\r\n        lowBattery: '低電量警示',\r\n      \r\n        wetUrine: '尿濕中',\r\n        lossSignal: '嬰兒走失',\r\n        wetUrineTimeout: '尿濕逾時',\r\n        noPatient: '無產婦入住',\r\n        hasPatient: '產婦入住',\r\n        babyCare: '新生兒照護',\r\n        removeSpecialStatus: '解除特殊狀況',\r\n        removeSpecialStatusConfirm: '確定解除特殊狀況?',\r\n        babyMove: '移動',\r\n        unknownStation: '未知基站',\r\n        specialEvent: '特殊事件',\r\n        babyRegion: '嬰兒位置',\r\n        diaperNormal: '尿布正常',\r\n\r\n        babyTagLowBattery: '嬰兒Tag低電量',\r\n        motherTagLowBattery: '母親手環低電量',\r\n        monitorOutside: '管制區外',\r\n        emptyBed: '空床',\r\n        enterSetting: '入住設定',\r\n        entering: '入住中',\r\n        eventId: '事件ID',\r\n        eventNo: '事件編號',\r\n        eventName: '事件名稱',\r\n        alertCondition: '警示條件',\r\n        less: '低於',\r\n        battery: '電量',\r\n        over: '超過',\r\n        minsSignal: '分鐘沒訊號',\r\n        second: '秒鐘',\r\n        addMon: '新增母親',\r\n        objectAttr: '定位對象屬性',\r\n        addBaby: '新增嬰兒',\r\n        resetDiaperComfortable: '重置尿布濕度',\r\n        mon: '母親',\r\n        baby2: '嬰兒',\r\n        propertySetting: '屬性設定',\r\n        objectSetting: '物件配置',\r\n        stationSetting: '基站配置',\r\n        stationSelect: '選擇基站',\r\n        monRoom: '婦產科病房',\r\n        babyRoom: '嬰兒中心',\r\n        controlArea: '管制區出入口',\r\n        warningArea: '警示區',\r\n        normalArea: '一般區',\r\n        objectName2: '物件名稱',\r\n        objectType: '物件屬性',\r\n        addBed: '新增床',\r\n        bed: '床',\r\n        toilet: '浴廁',\r\n        addRegion: '新增區域',\r\n        regionType: '區域類別',\r\n        showRegion: '顯示區域',\r\n        addSubRegion: '新增子區域'\r\n\r\n      },\r\n      m03: {\r\n        equipment: '設備',\r\n        overtemperature: '體溫過高',\r\n        wristbandLowBattery: '手環低電量',\r\n        noPatient: '無入住',\r\n        lowBattery: '低電量警示',\r\n        bodyOvertemperature: '體溫過高',\r\n        negativePressureIsolationCenter: '負壓隔離中心',\r\n        babyCare: '新生兒照護',\r\n        nowTemperature: '現在體溫',\r\n        nowHeartRate: '現在脈搏',\r\n        nowBloodOxygen: '現在血氧',\r\n        nowSbp: '現在收縮壓',\r\n        nowDbp: '現在舒張壓',\r\n        max: '最大值',\r\n        temperature: '體溫',\r\n        temperaturePicAndNumerical: '體溫圖例與數值',\r\n        temperatureColor: '體溫區間與顏色設定',\r\n        normal: '正常',\r\n        remind: '提醒',\r\n        bodyTemperatureWarm: '體溫稍高',\r\n        bodyTemperatureDetect: '體溫偵測',\r\n        heartRateDetect: '脈搏偵測',\r\n        bloodOxygenDetect: '血氧偵測',\r\n        sbpDetect: '血壓過高偵測',\r\n        dbpDetect: '血壓過低偵測',\r\n        over: '過高',\r\n        eventId: '事件ID',\r\n        eventNo: '事件編號',\r\n        eventName: '事件名稱',\r\n        alertCondition: '警示條件',\r\n        battery: '電量',\r\n        sbp: '收縮壓',\r\n        dbp: '舒張壓',\r\n        greater: '大於',\r\n        greaterThanOrEqual: '大於等於',\r\n        less: '低於',\r\n        lessThanOrEqual: '小於等於',\r\n        degree: '度',\r\n        timesPerMin: '次/分',\r\n        objectSetting: '物件配置',\r\n        negativePressureIsolationRoom: '負壓隔離病房',\r\n        objectName: '物件名稱',\r\n        objectType: '物件屬性',\r\n        addBed: '新增床',\r\n        bed: '床',\r\n        toilet: '浴廁',\r\n        addRegion: '新增區域',\r\n        regionType: '區域類別',\r\n        showRegion: '顯示區域',\r\n        addSubRegion: '新增子區域',\r\n        editConfirm: '修改確認',\r\n        bodyPhysicalDataSetting: '生理訊號監測設定',\r\n        continuousMonitoringDuration: '連續監測 持續時間',\r\n        nonContinuousMonitoringAbnormalCritical: '非連續監測 監測數值異常/危急達',\r\n        continuousMonitoring: '連續監測',\r\n        nonContinuousMonitoring: '非連續監測',\r\n        times: '次',\r\n        alert: '回報系統',\r\n        abnormal: '異常',\r\n        critical: '危急',\r\n        bloodOxygen: '血氧',\r\n        bloodPressure: '血壓',\r\n        heartRate: '脈搏',\r\n        bodyTemperature: '體溫',\r\n        backgroundColor: '背景色',\r\n        deviceLowBattery: '裝置低電量',\r\n        lowTemperature: '體溫過低',\r\n        overTemperature: '體溫過高',\r\n        lowHeartRate: '脈搏過低',\r\n        overHeartRate: '脈搏過高',\r\n        lowBloodOxygen: '血氧過低',\r\n        overBloodPressure: '血壓過高',\r\n        lowBloodPressure: '血壓過低',\r\n        overBloodPressureSbp: '收縮壓過高',\r\n        lowBloodPressureSbp: '收縮壓過低',\r\n        overBloodPressureDbp: '舒張壓過高',\r\n        lowBloodPressureDbp: '舒張壓過低',\r\n        sensorType: '配對裝置感測類別',\r\n        current: '現在'\r\n      },\r\n      mmWave: {\r\n        equipment: '設備',\r\n        history: '歷史紀錄',\r\n        fall: '跌倒',\r\n        lying: '臥躺',\r\n        sit: '坐著',\r\n        natureCall: '如廁',\r\n        now: '現在',\r\n        status: '狀況',\r\n        breathe: '呼吸',\r\n        record: '紀錄',\r\n        normal: '正常',\r\n        breathe5MinsRecord: '呼吸5分鐘趨勢',\r\n        bpm: '現在BPM',\r\n        max: '最大值',\r\n        timeoutSetting: '逾時設定',\r\n        mins: '分鐘',\r\n        stayTimeout: '停留逾時',\r\n        correct: '正確',\r\n        notCorrect: '誤判',\r\n        abnormalBreath: '呼吸異常',\r\n        leaveBed: '離床',\r\n        getUp: '起身',\r\n        emptyBed: '空床',\r\n        monitoring: '監測中',\r\n        empty: '閒置',\r\n        occupy: '使用中',\r\n        enterSetting: '入住設定',\r\n        entering: '入住中',\r\n        enter: '入住',\r\n        breathDetect: '呼吸偵測',\r\n        stopDetect: '停止偵測',\r\n        eventId: '事件ID',\r\n        eventNo: '事件編號',\r\n        eventName: '事件名稱',\r\n        alertCondition: '警示條件',\r\n        type: '定位類別',\r\n        device: '配對裝置MAC',\r\n        yes: '是',\r\n        no: '否',\r\n        objectAttr: '定位對象屬性',\r\n        eventCondition: '事件條件',\r\n        connectStatus: '配對裝置連線狀態',\r\n        connecting: '連線中',\r\n        nowObject: '目前定位對象',\r\n        allObject: '全部定位對象',\r\n        beforeIndex: '拖動前的索引',\r\n        afterIndex: '拖動後的索引',\r\n        searchCondition: '查詢條件',\r\n        eventEdit: '事件編輯',\r\n        edit: '編輯',\r\n        cancelEdit: '取消編輯',\r\n        saveEdit: '儲存編輯',\r\n        sponsorObjectType: '發起者屬性',\r\n        preFall: '跌倒偵測'\r\n\r\n      },\r\n      m02: {\r\n        equipment: '設備',\r\n        stationID: '基站SID',\r\n        name: '定位對象',\r\n        type: '定位類別',\r\n        device: '配對裝置MAC',\r\n        confirmEdit: '裝置已配對',\r\n        people: '人',\r\n        fall: '跌倒',\r\n        normal: '正常',\r\n        positionStation: '目前定位基站',\r\n        event: '事件',\r\n        allPositionStation: '全部定位基站',\r\n        enterAndExit: '進入/離開',\r\n        enter: '進入',\r\n        exit: '離開',\r\n        employeeNo: '工號',\r\n        employeeName: '姓名',\r\n        phoneNo: '手機序號',\r\n        enable: '啟用',\r\n        close: '關閉',\r\n        modifyTime: '修改時間',\r\n        pairingDevice: '配對裝置',\r\n        controlArea: '管制區',\r\n        normalArea: '非管制區',\r\n        enterControlArea: '進入管制區',\r\n        notice: '通知',\r\n        install: '安裝',\r\n        confirm: '確認',\r\n        remove: '移除',\r\n        leaveControlArea: '離開管制區',\r\n        enableAirwatch: '啟用Airwatch'\r\n      },\r\n      m05: {\r\n        equipment: '設備',\r\n        addSpecialStatus: '新增特殊狀況',\r\n        babyName: '老人名稱',\r\n        region: '地點',\r\n        description: '原因',\r\n        timeoutSetting: '逾時設定',\r\n        time: '分鐘',\r\n        stayTimeout: '停留逾時',\r\n        specialStatus: '特殊狀況',\r\n        baby: '老人',\r\n        numberControl: '長照中心',\r\n        enter: '進入警示區',\r\n        lowBattery: '低電量警示',\r\n\r\n        wetUrine: '尿濕中',\r\n        lossSignal: '老人走失',\r\n        wetUrineTimeout: '尿濕逾時',\r\n        noPatient: '無人入住', \r\n        hasPatient: '入住', \r\n        babyCare: '長照中心',\r\n        removeSpecialStatus: '解除特殊狀況',\r\n        removeSpecialStatusConfirm: '確定解除特殊狀況?',\r\n        babyMove: '移動',\r\n        unknownStation: '未知基站',\r\n        specialEvent: '特殊事件',\r\n        babyRegion: '老人位置',\r\n        diaperNormal: '尿布正常',\r\n\r\n        babyTagLowBattery: '低電量',\r\n        motherTagLowBattery: '母親手環低電量',\r\n        monitorOutside: '管制區外',\r\n        emptyBed: '空床',\r\n        enterSetting: '入住設定',\r\n        entering: '入住中',\r\n        eventId: '事件ID',\r\n        eventNo: '事件編號',\r\n        eventName: '事件名稱',\r\n        alertCondition: '警示條件',\r\n        less: '低於',\r\n        battery: '電量',\r\n        over: '超過',\r\n        minsSignal: '分鐘沒訊號',\r\n        second: '秒鐘',\r\n        addMon: '新增母親',\r\n        objectAttr: '定位對象屬性',\r\n        addBaby: '新增老人',\r\n        resetDiaperComfortable: '重置尿布濕度',\r\n        mon: '母親',\r\n        baby2: '嬰兒',\r\n        propertySetting: '屬性設定',\r\n        objectSetting: '物件配置',\r\n        stationSetting: '基站配置',\r\n        stationSelect: '選擇基站',\r\n        monRoom: '婦產科病房',\r\n        babyRoom: '老人中心',\r\n        controlArea: '管制區出入口',\r\n        warningArea: '警示區',\r\n        normalArea: '一般區',\r\n        objectName2: '物件名稱',\r\n        objectType: '物件屬性',\r\n        addBed: '新增床',\r\n        bed: '床',\r\n        toilet: '浴廁',\r\n        addRegion: '新增區域',\r\n        regionType: '區域類別',\r\n        showRegion: '顯示區域',\r\n        addSubRegion: '新增子區域'\r\n\r\n      },\r\n      m27: {        \r\n        temperature:'溫度',       \r\n        nowTemperature: '現在溫度',        \r\n        temperaturePicAndNumerical: '溫度圖例與數值',\r\n        temperatureColor: '溫度區間與顏色設定',               \r\n        temperatureWarm: '溫度稍高',\r\n        temperatureDetect: '溫度偵測',\r\n        overTemperature: '溫度過高',\r\n        lowTemperature: '溫度過低',        \r\n\r\n        humidity:'濕度',\r\n        overHumidity: '濕度過高',        \r\n        lowHumidity: '濕度過低',\r\n        humidityDetect: '濕度偵測',\r\n        nowHumidity: '現在濕度',\r\n        \r\n        airPM25:'PM2.5',\r\n        nowAirPM25: '現在PM2.5',\r\n        airPM25Detect: 'PM2.5偵測',        \r\n        overAirPM25: 'PM2.5過高',\r\n        lowAirPM25: 'PM2.5過高',\r\n        \r\n        airAQI:'AQI',\r\n        nowAQI: '現在AQI',\r\n        airAQIDetect: 'AQI偵測',        \r\n        overAirAQI: 'AQI過高',\r\n        lowAirAQI: 'AQI過高',\r\n        \r\n        \r\n\r\n\r\n\r\n        equipment: '設備',\r\n        wristbandLowBattery: '手環低電量',\r\n        noPatient: '無入住',\r\n        lowBattery: '低電量警示',\r\n        \r\n        negativePressureIsolationCenter: '環境品質偵測',\r\n        babyCare: '新生兒照護',        \r\n        nowSbp: '現在收縮壓',\r\n        nowDbp: '現在舒張壓',\r\n        max: '最大值',\r\n\r\n        normal: '正常',\r\n        remind: '提醒',\r\n        sbpDetect: '血壓過高偵測',\r\n        dbpDetect: '血壓過低偵測',\r\n        over: '過高',\r\n        eventId: '事件ID',\r\n        eventNo: '事件編號',\r\n        eventName: '事件名稱',\r\n        alertCondition: '警示條件',\r\n        battery: '電量',\r\n        sbp: '收縮壓',\r\n        dbp: '舒張壓',\r\n        greater: '大於',\r\n        greaterThanOrEqual: '大於等於',\r\n        less: '低於',\r\n        lessThanOrEqual: '小於等於',\r\n        degree: '度',\r\n        timesPerMin: '次/分',\r\n        objectSetting: '物件配置',\r\n        negativePressureIsolationRoom: '負壓隔離病房',\r\n        objectName: '物件名稱',\r\n        objectType: '物件屬性',\r\n        addBed: '新增床',\r\n        bed: '床',\r\n        toilet: '浴廁',\r\n        addRegion: '新增區域',\r\n        regionType: '區域類別',\r\n        showRegion: '顯示區域',\r\n        addSubRegion: '新增子區域',\r\n        editConfirm: '修改確認',\r\n        bodyPhysicalDataSetting: '生理訊號監測設定',\r\n        continuousMonitoringDuration: '連續監測 持續時間',\r\n        nonContinuousMonitoringAbnormalCritical: '非連續監測 監測數值異常/危急達',\r\n        continuousMonitoring: '連續監測',\r\n        nonContinuousMonitoring: '非連續監測',\r\n        times: '次',\r\n        alert: '回報系統',\r\n        abnormal: '異常',\r\n        critical: '危急',\r\n        bloodPressure: '血壓',                \r\n        backgroundColor: '背景色',\r\n        deviceLowBattery: '裝置低電量',\r\n        overBloodPressure: '血壓過高',\r\n        lowBloodPressure: '血壓過低',\r\n        overBloodPressureSbp: '收縮壓過高',\r\n        lowBloodPressureSbp: '收縮壓過低',\r\n        overBloodPressureDbp: '舒張壓過高',\r\n        lowBloodPressureDbp: '舒張壓過低',\r\n        sensorType: '配對裝置感測類別',\r\n        current: '現在'\r\n      },\r\n      m28: {\r\n        equipment: '設備',\r\n        stationID: '基站SID',\r\n        name: '定位對象',\r\n        type: '定位類別',\r\n        device: '配對裝置MAC',\r\n        confirmEdit: '裝置已配對',\r\n        people: '人',\r\n        fall: '跌倒',\r\n        normal: '正常',\r\n        positionStation: '目前定位基站',\r\n        event: '事件',\r\n        allPositionStation: '全部定位基站',\r\n        enterAndExit: '進入/離開',\r\n        enter: '進入',\r\n        exit: '離開',\r\n        employeeNo: '工號',\r\n        employeeName: '姓名',\r\n        phoneNo: '手機序號',\r\n        enable: '啟用',\r\n        close: '關閉',\r\n        modifyTime: '修改時間',\r\n        pairingDevice: '配對裝置',\r\n        controlArea: '管制區',\r\n        normalArea: '非管制區',\r\n        enterControlArea: '進入管制區',\r\n        notice: '通知',\r\n        install: '安裝',\r\n        confirm: '確認',\r\n        remove: '移除',\r\n        leaveControlArea: '離開管制區',\r\n        enableAirwatch: '啟用Airwatch'\r\n      },\r\n      m29: {\r\n        helpEvent:'求救事件',\r\n        wetUrineEvent:'尿溼提醒',\r\n\r\n        cardTemperature: '體溫',        \r\n        cardDiaper: '尿布偵測',\r\n        cardHelp: '求助',\r\n        diaperNormal: '正常',\r\n        diaperWarning: '尿濕中',\r\n        diaperAlert: '尿濕逾時',\r\n\r\n        eventHelp: '人員求助',\r\n        enterControlArea:'進入管制區',\r\n        leaveControlArea:'離開管制區',\r\n        normalArea:'非管制區',\r\n\r\n        controlArea:'管制區',\r\n\r\n        \r\n        temperature:'溫度',       \r\n        nowTemperature: '現在溫度',        \r\n        temperaturePicAndNumerical: '溫度圖例與數值',\r\n        temperatureColor: '溫度區間與顏色設定',               \r\n        temperatureWarm: '溫度稍高',\r\n        temperatureDetect: '溫度偵測',\r\n        overTemperature: '溫度過高',        \r\n        lowTemperature: '溫度過低',       \r\n\r\n        equipment: '設備',\r\n        addSpecialStatus: '新增特殊狀況',\r\n        babyName: '老人名稱',\r\n        region: '地點',\r\n        description: '原因',\r\n        timeoutSetting: '逾時設定',\r\n        time: '分鐘',\r\n        stayTimeout: '停留逾時',\r\n        specialStatus: '特殊狀況',\r\n        baby: '老人',\r\n        numberControl: '長照中心',\r\n        enter: '進入警示區',\r\n        lowBattery: '低電量警示',\r\n\r\n        \r\n        lossSignal: '老人走失',        \r\n        noPatient: '無人入住', \r\n        hasPatient: '入住', \r\n        babyCare: '長照中心',\r\n        removeSpecialStatus: '解除特殊狀況',\r\n        removeSpecialStatusConfirm: '確定解除特殊狀況?',\r\n        babyMove: '移動',\r\n        unknownStation: '未知基站',\r\n        specialEvent: '特殊事件',\r\n        babyRegion: '老人位置',\r\n        \r\n\r\n        babyTagLowBattery: '低電量',\r\n        motherTagLowBattery: '母親手環低電量',\r\n        monitorOutside: '管制區外',\r\n        emptyBed: '空床',\r\n        enterSetting: '入住設定',\r\n        entering: '入住中',\r\n        eventId: '事件ID',\r\n        eventNo: '事件編號',\r\n        eventName: '事件名稱',\r\n        alertCondition: '警示條件',\r\n        less: '低於',\r\n        battery: '電量',\r\n        over: '超過',\r\n        minsSignal: '分鐘沒訊號',\r\n        second: '秒鐘',\r\n        addMon: '新增母親',\r\n        objectAttr: '定位對象屬性',\r\n        addBaby: '新增老人',\r\n        resetDiaperComfortable: '重置尿布濕度',\r\n        mon: '母親',\r\n        baby2: '嬰兒',\r\n        propertySetting: '屬性設定',\r\n        objectSetting: '物件配置',\r\n        stationSetting: '基站配置',\r\n        stationSelect: '選擇基站',\r\n        monRoom: '婦產科病房',\r\n        babyRoom: '老人中心',\r\n        //controlArea: '管制區出入口',\r\n        warningArea: '警示區',\r\n        \r\n        objectName2: '物件名稱',\r\n        objectType: '物件屬性',\r\n        addBed: '新增床',\r\n        bed: '床',\r\n        toilet: '浴廁',\r\n        addRegion: '新增區域',\r\n        regionType: '區域類別',\r\n        showRegion: '顯示區域',\r\n        addSubRegion: '新增子區域'\r\n      },\r\n\r\n      m30: {\r\n        helpEvent:'求救事件',\r\n        wetUrineEvent:'尿溼提醒',\r\n\r\n        cardTemperature: '體溫',        \r\n        cardDiaper: '尿布偵測',\r\n        cardHelp: '求助',\r\n\r\n        cardMMWaveBreathe: '呼吸偵測',\r\n\r\n        \r\n        eventMMWaveFallDetection: '跌倒',\r\n        eventMMWaveStayTimeout: '停留逾時',\r\n        eventMMWaveLeaveBed: '離床',\r\n        eventMMWaveGetUp: '起身',\r\n      \r\n        \r\n        diaperNormal: '正常',\r\n        diaperWarning: '尿濕中',\r\n        diaperAlert: '尿濕逾時',\r\n\r\n        eventHelp: '人員求助',\r\n        enterControlArea:'進入管制區',\r\n        leaveControlArea:'離開管制區',\r\n        normalArea:'非管制區',\r\n        controlArea:'管制區',\r\n\r\n        \r\n        temperature:'溫度',       \r\n        nowTemperature: '現在溫度',        \r\n        temperaturePicAndNumerical: '溫度圖例與數值',\r\n        temperatureColor: '溫度區間與顏色設定',               \r\n        temperatureWarm: '溫度稍高',\r\n        temperatureDetect: '溫度偵測',\r\n        overTemperature: '溫度過高',        \r\n        lowTemperature: '溫度過低',       \r\n\r\n        equipment: '設備',\r\n        addSpecialStatus: '新增特殊狀況',\r\n        babyName: '老人名稱',\r\n        region: '地點',\r\n        description: '原因',\r\n        timeoutSetting: '逾時設定',\r\n        time: '分鐘',\r\n        stayTimeout: '停留逾時',\r\n        bathroomFall: '浴廁跌倒',\r\n        specialStatus: '特殊狀況',\r\n        baby: '老人',\r\n        numberControl: '長照中心',\r\n        enter: '進入警示區',\r\n        lowBattery: '低電量警示',\r\n\r\n        \r\n        lossSignal: '老人走失',        \r\n        noPatient: '無人入住', \r\n        hasPatient: '入住', \r\n        babyCare: '長照中心',\r\n        removeSpecialStatus: '解除特殊狀況',\r\n        removeSpecialStatusConfirm: '確定解除特殊狀況?',\r\n        babyMove: '移動',\r\n        unknownStation: '未知基站',\r\n        specialEvent: '特殊事件',\r\n        babyRegion: '老人位置',\r\n        \r\n\r\n        babyTagLowBattery: '低電量',\r\n        motherTagLowBattery: '母親手環低電量',\r\n        monitorOutside: '管制區外',\r\n        emptyBed: '空床',\r\n        enterSetting: '入住設定',\r\n        entering: '入住中',\r\n        eventId: '事件ID',\r\n        eventNo: '事件編號',\r\n        eventName: '事件名稱',\r\n        alertCondition: '警示條件',\r\n        less: '低於',\r\n        battery: '電量',\r\n        over: '超過',\r\n        minsSignal: '分鐘沒訊號',\r\n        second: '秒鐘',\r\n        addMon: '新增母親',\r\n        objectAttr: '定位對象屬性',\r\n        addBaby: '新增老人',\r\n        resetDiaperComfortable: '重置尿布濕度',\r\n        mon: '母親',\r\n        baby2: '嬰兒',\r\n        propertySetting: '屬性設定',\r\n        objectSetting: '物件配置',\r\n        stationSetting: '基站配置',\r\n        stationSelect: '選擇基站',\r\n        monRoom: '婦產科病房',\r\n        babyRoom: '老人中心',\r\n        //controlArea: '管制區出入口',\r\n        warningArea: '警示區',\r\n        \r\n        objectName2: '物件名稱',\r\n        objectType: '物件屬性',\r\n        addBed: '新增床',\r\n        bed: '床',\r\n        room: '房間',\r\n        bathroom: '浴廁',\r\n\r\n        addRegion: '新增區域',\r\n        regionType: '區域類別',\r\n        showRegion: '顯示區域',\r\n        addSubRegion: '新增子區域',\r\n\r\n        haveMan: '有人',\r\n        noMan: '無人',\r\n        stopDetect: '停止偵測'\r\n\r\n      },\r\n      m31: {\r\n        location:'位置',\r\n        lossMonitor:'走失偵測',\r\n        wetUrineMonitor: '尿布偵測',\r\n\r\n\r\n        equipment: '設備',\r\n        addSpecialStatus: '新增特殊狀況',\r\n        babyName: '新生兒名稱',\r\n        region: '地點',\r\n        description: '原因',\r\n        timeoutSetting: '逾時設定',\r\n        time: '分鐘',\r\n        stayTimeout: '停留逾時',\r\n        specialStatus: '特殊狀況',\r\n        baby: '新生兒',\r\n        numberControl: '母嬰同室',\r\n        enter: '進入警示區',\r\n        lowBattery: '低電量警示',\r\n      \r\n        wetUrine: '尿濕中',\r\n        lossSignal: '嬰兒走失',\r\n        wetUrineTimeout: '尿濕逾時',\r\n        noPatient: '無產婦入住',\r\n        hasPatient: '產婦入住',\r\n        babyCare: '新生兒照護',\r\n        removeSpecialStatus: '解除特殊狀況',\r\n        removeSpecialStatusConfirm: '確定解除特殊狀況?',\r\n        babyMove: '移動',\r\n        unknownStation: '未知基站',\r\n        specialEvent: '特殊事件',\r\n        babyRegion: '嬰兒位置',\r\n        diaperNormal: '尿布正常',\r\n\r\n        babyTagLowBattery: '嬰兒Tag低電量',\r\n        motherTagLowBattery: '母親手環低電量',\r\n        monitorOutside: '管制區外',\r\n        emptyBed: '空床',\r\n        enterSetting: '入住設定',\r\n        entering: '入住中',\r\n        eventId: '事件ID',\r\n        eventNo: '事件編號',\r\n        eventName: '事件名稱',\r\n        alertCondition: '警示條件',\r\n        less: '低於',\r\n        battery: '電量',\r\n        over: '超過',\r\n        minsSignal: '分鐘沒訊號',\r\n        second: '秒鐘',\r\n        addMon: '新增母親',\r\n        objectAttr: '定位對象屬性',\r\n        addBaby: '新增嬰兒',\r\n        resetDiaperComfortable: '重置尿布濕度',\r\n        mon: '母親',\r\n        baby2: '嬰兒',\r\n        propertySetting: '屬性設定',\r\n        objectSetting: '物件配置',\r\n        stationSetting: '基站配置',\r\n        stationSelect: '選擇基站',\r\n        monRoom: '婦產科病房',\r\n        babyRoom: '嬰兒中心',\r\n        controlArea: '管制區出入口',\r\n        warningArea: '警示區',\r\n        normalArea: '一般區',\r\n        objectName2: '物件名稱',\r\n        objectType: '物件屬性',\r\n        addBed: '新增床',\r\n        bed: '床',\r\n        toilet: '浴廁',\r\n        addRegion: '新增區域',\r\n        regionType: '區域類別',\r\n        showRegion: '顯示區域',\r\n        addSubRegion: '新增子區域'\r\n\r\n      },\r\n      \r\n            \r\n    }\r\n  }\r\n}\r\n"]}]}