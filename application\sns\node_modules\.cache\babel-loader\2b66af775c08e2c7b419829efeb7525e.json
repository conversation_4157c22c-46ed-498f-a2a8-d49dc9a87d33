{"remainingRequest": "E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\node_modules\\babel-loader\\lib\\index.js!E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\node_modules\\string-replace-loader\\index.js??ref--13-0!E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\src\\components\\administrative\\common\\stationsSelectModal.vue?vue&type=script&lang=js&", "dependencies": [{"path": "E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\src\\components\\administrative\\common\\stationsSelectModal.vue", "mtime": 1754362736960}, {"path": "E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\node_modules\\string-replace-loader\\index.js", "mtime": 499162500000}, {"path": "E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["stationsSelectModal.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqHA,OAAA,MAAA,MAAA,iBAAA;AAEA,eAAA;AACA,EAAA,KAAA,EAAA,CAAA,eAAA,EAAA,WAAA,CADA;AAEA,EAAA,IAFA,kBAEA;AAAA;;AACA,WAAA;AACA,MAAA,aAAA,EAAA,CACA;AACA,QAAA,KAAA,EAAA,KADA;AAEA,QAAA,IAAA,EAAA,KAAA,EAAA,CAAA,qBAAA;AAFA,OADA,EAKA;AACA,QAAA,KAAA,EAAA,GADA;AAEA,QAAA,IAAA,EAAA,KAAA,EAAA,CAAA,qBAAA;AAFA,OALA,EASA;AACA,QAAA,KAAA,EAAA,GADA;AAEA,QAAA,IAAA,EAAA,KAAA,EAAA,CAAA,qBAAA;AAFA,OATA,EAaA;AACA,QAAA,KAAA,EAAA,GADA;AAEA,QAAA,IAAA,EAAA,KAAA,EAAA,CAAA,qBAAA;AAFA,OAbA,CADA;AAmBA,MAAA,UAAA,EAAA,KAAA,EAAA,CAAA,qBAAA,CAnBA;AAoBA,MAAA,YAAA,EAAA,EApBA;AAqBA,MAAA,eAAA,EAAA,EArBA;AAsBA,MAAA,iBAAA,EAAA,KAtBA;AAuBA,MAAA,SAAA,EAAA,EAvBA;AAwBA,MAAA,eAAA,EAAA,KAxBA;AA0BA,MAAA,YAAA,EAAA,KA1BA;AA2BA,MAAA,SAAA,EAAA,EA3BA;AA4BA,MAAA,UAAA,EAAA,EA5BA;AA6BA,MAAA,eAAA,EAAA,EA7BA;AA8BA,MAAA,cAAA,EAAA,CACA;AACA,QAAA,IAAA,EAAA,WADA;AAEA,QAAA,KAAA,EAAA,QAFA;AAGA,QAAA,KAAA,EAAA;AAHA,OADA,EAMA;AACA,QAAA,KAAA,EAAA,KAAA,EAAA,CAAA,qBAAA,CADA;AAEA,QAAA,GAAA,EAAA,KAFA;AAGA,QAAA,QAAA,EAAA,IAHA;AAIA,QAAA,KAAA,EAAA,OAJA;AAKA,QAAA,MAAA,EAAA,gBAAA,CAAA,EAAA,MAAA,EAAA;AACA,iBAAA,CAAA,CAAA,MAAA,EAAA,MAAA,CAAA,GAAA,CAAA,GAAA,CAAA;AACA,SAPA;AAQA,QAAA,QAAA,EAAA;AARA,OANA,EAgBA;AACA,QAAA,KAAA,EAAA,KAAA,EAAA,CAAA,qBAAA,CADA;AAEA,QAAA,GAAA,EAAA,MAFA;AAGA,QAAA,KAAA,EAAA,OAHA;AAIA,QAAA,QAAA,EAAA,IAJA;AAKA,QAAA,MAAA,EAAA,gBAAA,CAAA,EAAA,MAAA,EAAA;AACA,iBAAA,CAAA,CAAA,MAAA,EAAA,MAAA,CAAA,GAAA,CAAA,IAAA,CAAA;AACA;AAPA,OAhBA,EAyBA;AACA,QAAA,KAAA,EAAA,KAAA,EAAA,CAAA,qBAAA,CADA;AAEA,QAAA,GAAA,EAAA,MAFA;AAGA,QAAA,QAAA,EAAA,IAHA;AAIA,QAAA,MAAA,EAAA,gBAAA,CAAA,EAAA,MAAA,EAAA;AACA,cAAA,QAAA,GAAA,MAAA,CAAA,eAAA,CAAA,IAAA,CAAA,UAAA,EAAA;AAAA,mBAAA,EAAA,CAAA,IAAA,IAAA,MAAA,CAAA,GAAA,CAAA,IAAA;AAAA,WAAA,IAAA,MAAA,CAAA,eAAA,CAAA,IAAA,CAAA,UAAA,EAAA;AAAA,mBAAA,EAAA,CAAA,IAAA,IAAA,MAAA,CAAA,GAAA,CAAA,IAAA;AAAA,WAAA,EAAA,IAAA,GAAA,MAAA,CAAA,GAAA,CAAA,IAAA;AACA,iBAAA,CAAA,CAAA,MAAA,EAAA,QAAA,CAAA;AACA;AAPA,OAzBA,EAkCA;AACA;AACA,QAAA,KAAA,EAAA,KAAA,EAAA,CAAA,qBAAA,CAFA;AAGA,QAAA,GAAA,EAAA,aAHA;AAIA,QAAA,QAAA,EAAA,IAJA;AAKA,QAAA,MAAA,EAAA,gBAAA,CAAA,EAAA,MAAA,EAAA;AACA,iBAAA,CAAA,CACA,MADA,EAEA,MAAA,CAAA,GAAA,CAAA,WAAA,GACA,MAAA,CAAA,aAAA,CAAA,IAAA,CACA,UAAA,IAAA;AAAA,mBAAA,IAAA,CAAA,KAAA,KAAA,MAAA,CAAA,GAAA,CAAA,WAAA,CAAA,QAAA,EAAA;AAAA,WADA,IAGA,MAAA,CAAA,aAAA,CAAA,IAAA,CACA,UAAA,IAAA;AAAA,mBAAA,IAAA,CAAA,KAAA,KAAA,MAAA,CAAA,GAAA,CAAA,WAAA,CAAA,QAAA,EAAA;AAAA,WADA,EAEA,IALA,GAMA,EAPA,GAQA,EAVA,CAAA;AAYA;AAlBA,OAlCA,EAsDA;AACA,QAAA,KAAA,EAAA,KAAA,EAAA,CAAA,qBAAA,CADA;AAEA,QAAA,GAAA,EAAA,eAFA;AAGA,QAAA,QAAA,EAAA,IAHA;AAIA,QAAA,KAAA,EAAA,OAJA;AAKA,QAAA,MAAA,EAAA,gBAAA,CAAA,EAAA,MAAA,EAAA;AACA,iBAAA,CAAA,CACA,MADA,EAEA,MAAA,CAAA,GAAA,CAAA,UAAA,GAAA,MAAA,CAAA,GAAA,CAAA,UAAA,CAAA,EAAA,GAAA,EAFA,CAAA,CADA,CAKA;AACA;AAXA,OAtDA,CAmEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAlFA;AA9BA,KAAA;AAmHA,GAtHA;AAuHA,EAAA,OAvHA,qBAuHA;AACA,SAAA,YAAA,GAAA,IAAA;AACA,SAAA,kBAAA;AACA,SAAA,YAAA;AACA,SAAA,WAAA;AACA,GA5HA;AA6HA,EAAA,OAAA,EAAA;AACA;;;;;;;;;;;;;;;;;;;AAqBA,IAAA,kBAtBA,gCAsBA;AAAA;;AACA,UAAA,KAAA,GAAA,IAAA,CADA,CAEA;;;AACA,UAAA,MAAA,GAAA,CACA;AACA;AACA;AAHA,OAAA;AAKA,WAAA,QAAA,CAAA,mBAAA,CAAA,IAAA,CAAA,MAAA,EAAA,IAAA,CAAA,UAAA,IAAA,EAAA;AACA,QAAA,OAAA,CAAA,GAAA,CAAA,UAAA,IAAA;AACA,QAAA,KAAA,CAAA,eAAA,GAAA,IAAA;;AACA,QAAA,KAAA,CAAA,eAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,UAAA,IAAA,CAAA,IAAA,GAAA,MAAA,CAAA,EAAA,CAAA,kBAAA,IAAA,CAAA,KAAA,CAAA,MAAA,CAAA;AACA,SAFA;AAGA,OANA;AAOA,KArCA;AAsCA,IAAA,YAtCA,0BAsCA;AACA,UAAA,KAAA,GAAA,IAAA,CADA,CAEA;;;AACA,UAAA,MAAA,GAAA;AACA,QAAA,MAAA,EAAA,IADA,CAEA;;AAFA,OAAA;AAIA,WAAA,QAAA,CAAA,SAAA,CAAA,IAAA,CAAA,MAAA,EAAA,IAAA,CAAA,UAAA,IAAA,EAAA;AACA,QAAA,OAAA,CAAA,GAAA,CAAA,IAAA;AACA,QAAA,KAAA,CAAA,SAAA,GAAA,IAAA,CAAA,OAAA;AACA,OAHA;AAIA,KAjDA;AAmDA,IAAA,WAnDA,yBAmDA;AAAA;;AACA,UAAA,KAAA,GAAA,IAAA;;AACA,UAAA,MAAA,GACA,yDAAA,KAAA,SADA;;AAEA,UAAA,KAAA,SAAA,EAAA;AACA,QAAA,MAAA,IAAA,oBAAA,KAAA,SAAA,GAAA,GAAA;AACA;;AAEA,UAAA,KAAA,UAAA,EAAA;AACA,QAAA,MAAA,IAAA,qBAAA,KAAA,UAAA,GAAA,GAAA;AACA;;AAEA,UAAA,KAAA,eAAA,KAAA,KAAA,EAAA;AACA,QAAA,MAAA,IAAA,yBAAA,KAAA,eAAA;AACA;;AACA,UAAA,KAAA,iBAAA,KAAA,KAAA,EAAA;AACA,QAAA,MAAA,IAAA,qBAAA,KAAA,iBAAA,GAAA,GAAA;AACA;;AAEA,UAAA,MAAA,GAAA;AACA,QAAA,WAAA,EAAA,IADA;AAEA,QAAA,MAAA,EAAA,MAFA,CAGA;AACA;;AAJA,OAAA,CAnBA,CAyBA;AACA;AACA;;AAEA,WAAA,QAAA,CAAA,WAAA,CAAA,IAAA,CAAA,MAAA,EAAA,IAAA,CAAA,UAAA,KAAA,EAAA;AACA,QAAA,OAAA,CAAA,GAAA,CAAA,KAAA;;AACA,YAAA,KAAA,CAAA,KAAA,GAAA,CAAA,EAAA;AACA,cAAA,MAAA,CAAA,aAAA,EAAA;AACA,gBAAA,gBAAA,GAAA,MAAA,CAAA,aAAA,CAAA,GAAA,CAAA,UAAA,CAAA,EAAA;AACA,qBAAA,CAAA,CAAA,GAAA;AACA,aAFA,CAAA;;AAGA,YAAA,KAAA,CAAA,eAAA,GAAA,KAAA,CAAA,OAAA,CAAA,MAAA,CACA,UAAA,CAAA;AAAA,qBAAA,CAAA,gBAAA,CAAA,QAAA,CAAA,CAAA,CAAA,GAAA,CAAA;AAAA,aADA,CAAA;AAGA,WAPA,MAOA;AACA,YAAA,KAAA,CAAA,eAAA,GAAA,KAAA,CAAA,OAAA;AACA;AAEA;;;;;;;;;;;AAWA,SAvBA,MAuBA;AACA,UAAA,KAAA,CAAA,eAAA,GAAA,EAAA;AACA;AACA,OA5BA;AA6BA,KA7GA;AA8GA,IAAA,cAAA,EAAA,0BAAA;AACA,UAAA,KAAA,KAAA,CAAA,aAAA,CAAA,YAAA,GAAA,MAAA,KAAA,CAAA,EAAA;AACA,aAAA,OAAA,CAAA,OAAA,CAAA;AACA,UAAA,KAAA,EAAA,IADA;AAEA,UAAA,IAAA,EAAA,SAFA;AAGA,UAAA,QAAA,EAAA,MAAA,CAAA;AAHA,SAAA;AAKA,OANA,MAMA;AACA,YAAA,OAAA,GAAA,KAAA,KAAA,CAAA,aAAA,CAAA,YAAA,EAAA;AACA,aAAA,KAAA,CAAA,mBAAA,EAAA,OAAA;AACA,aAAA,YAAA,GAAA,KAAA;AACA;AACA,KA1HA;AA2HA,IAAA,OA3HA,qBA2HA;AACA,WAAA,KAAA,CAAA,mBAAA;AACA,WAAA,YAAA,GAAA,KAAA;AACA,KA9HA;AA+HA,IAAA,YA/HA,wBA+HA,SA/HA,EA+HA;AACA,WAAA,SAAA,GAAA,SAAA;AACA,UAAA,WAAA,GAAA,EAAA;AACA,MAAA,SAAA,CAAA,OAAA,CAAA,UAAA,OAAA,EAAA,KAAA,EAAA;AACA,QAAA,WAAA,CAAA,IAAA,CAAA,OAAA,CAAA,GAAA;AACA,OAFA,EAEA,IAFA;AAGA,WAAA,YAAA,GAAA,WAAA;AACA;AAtIA;AA7HA,CAAA", "sourcesContent": ["<template>\r\n  <div>\r\n    <Modal\r\n      v-model=\"openStations\"\r\n      @on-cancel=\"onClose\"\r\n      :title=\"$t('LocaleString.L00222')\"\r\n      :mask-closable=\"false\"\r\n      width=\"850\"\r\n    >\r\n      <div>\r\n        <Form>\r\n          <Row>\r\n            <i-Col span=\"3\">\r\n              <div style=\"padding-top: 5px\">\r\n                {{ $t(\"LocaleString.L00047\") }}\r\n              </div>\r\n            </i-Col>\r\n            <i-Col span=\"6\">\r\n              <FormItem class=\"add-form-item\">\r\n                <Select\r\n                  v-model=\"searchStationType\"\r\n                  :transfer=\"true\"\r\n                  :placeholder=\"$t('LocaleString.D00001')\"\r\n                >\r\n                  <Option value=\"all\">{{ $t(\"LocaleString.D00002\") }}</Option>\r\n                  <Option\r\n                    v-for=\"item in stationTypeList\"\r\n                    :value=\"item.type\"\r\n                    :key=\"item.type\"\r\n                    >{{ item.name }}</Option\r\n                  >\r\n                </Select>\r\n              </FormItem>\r\n            </i-Col>\r\n            <i-Col span=\"3\">\r\n              <div style=\"padding-left: 10px; padding-top: 5px\">\r\n                {{ $t(\"LocaleString.L00345\") }}\r\n              </div>\r\n            </i-Col>\r\n            <i-Col span=\"6\">\r\n              <FormItem class=\"add-form-item\">\r\n                <Select\r\n                  v-model=\"searchBluetooth\"\r\n                  :transfer=\"true\"\r\n                  :placeholder=\"$t('LocaleString.D00001')\"\r\n                >\r\n                  <Option\r\n                    v-for=\"item in bluetoothList\"\r\n                    :value=\"item.value\"\r\n                    :key=\"item.value\"\r\n                    >{{ item.name }}</Option\r\n                  >\r\n                </Select>\r\n              </FormItem>\r\n            </i-Col>\r\n\r\n            <i-Col offset=\"1\" span=\"1\">\r\n              <Button type=\"primary\" @click=\"getStations\">{{\r\n                $t(\"LocaleString.B00006\")\r\n              }}</Button>\r\n            </i-Col>\r\n\r\n            <Divider style=\"visibility: hidden; margin: 0px 0px\" />\r\n\r\n            <i-Col span=\"3\">\r\n              <div style=\"margin-top: 5px\">{{ $t(\"LocaleString.L00037\") }}</div>\r\n            </i-Col>\r\n            <i-Col span=\"6\">\r\n              <FormItem class=\"add-form-item\">\r\n                <i-Input\r\n                  clearable\r\n                  v-model=\"searchSid\"\r\n                  :placeholder=\"$t('LocaleString.L00037')\"\r\n                ></i-Input>\r\n              </FormItem>\r\n            </i-Col>\r\n            <i-Col span=\"3\">\r\n              <div style=\"padding-left: 10px; padding-top: 5px\">\r\n                {{ $t(\"LocaleString.L00012\") }}\r\n              </div>\r\n            </i-Col>\r\n            <i-Col span=\"6\">\r\n              <FormItem class=\"add-form-item\">\r\n                <i-Input\r\n                  clearable\r\n                  v-model=\"searchName\"\r\n                  :placeholder=\"$t('LocaleString.L00012')\"\r\n                ></i-Input>\r\n              </FormItem>\r\n            </i-Col>\r\n          </Row>\r\n\r\n          <Table\r\n            height=\"300\"\r\n            ref=\"tableStations\"\r\n            :columns=\"columnStations\"\r\n            :data=\"newDataStations\"\r\n            @on-selection-change=\"selectChange\"\r\n            :no-data-text=\"noDataText\"\r\n          ></Table>\r\n        </Form>\r\n      </div>\r\n      <div slot=\"footer\" style=\"text-align: center\">\r\n        <Button\r\n          type=\"primary\"\r\n          :disabled=\"responseData.length == 0 ? true : false\"\r\n          @click=\"passSelectData()\"\r\n          >{{ $t(\"LocaleString.B00014\") }}</Button\r\n        >\r\n\r\n        <Button @click=\"onClose\">{{ $t(\"LocaleString.B00015\") }}</Button>\r\n      </div>\r\n    </Modal>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport Config from \"@/common/config\";\r\n\r\nexport default {\r\n  props: [\"existStations\", \"planeCode\"],\r\n  data() {\r\n    return {\r\n      bluetoothList: [\r\n        {\r\n          value: \"all\",\r\n          name: this.$t(\"LocaleString.D00002\"),\r\n        },\r\n        {\r\n          value: \"0\",\r\n          name: this.$t(\"LocaleString.D00279\"),\r\n        },\r\n        {\r\n          value: \"1\",\r\n          name: this.$t(\"LocaleString.D00280\"),\r\n        },\r\n        {\r\n          value: \"2\",\r\n          name: this.$t(\"LocaleString.D00281\"),\r\n        },\r\n      ],\r\n      noDataText: this.$t(\"LocaleString.L00081\"),\r\n      responseData: [],\r\n      stationTypeList: [],\r\n      searchStationType: \"all\",\r\n      planeList: [],\r\n      searchBluetooth: \"all\",\r\n\r\n      openStations: false,\r\n      searchSid: \"\",\r\n      searchName: \"\",\r\n      newDataStations: [],\r\n      columnStations: [\r\n        {\r\n          type: \"selection\",\r\n          align: \"center\",\r\n          width: \"50px\",\r\n        },\r\n        {\r\n          title: this.$t(\"LocaleString.L00258\"),\r\n          key: \"sid\",\r\n          sortable: true,\r\n          width: \"180px\",\r\n          render: (h, params) => {\r\n            return h(\"span\", params.row.sid);\r\n          },\r\n          sortType: \"asc\",\r\n        },\r\n        {\r\n          title: this.$t(\"LocaleString.L00012\"),\r\n          key: \"name\",\r\n          width: \"180px\",\r\n          sortable: true,\r\n          render: (h, params) => {\r\n            return h(\"span\", params.row.name);\r\n          },\r\n        },\r\n        {\r\n          title: this.$t(\"LocaleString.L00047\"),\r\n          key: \"type\",\r\n          sortable: true,\r\n          render: (h, params) => {\r\n            let typeName = this.stationTypeList.some(el=> el.type == params.row.type) ? this.stationTypeList.find(el=> el.type == params.row.type).name : params.row.type;\r\n            return h(\"span\", typeName);\r\n          },\r\n        },\r\n        {\r\n          // title: this.$t(\"LocaleString.L00258\"),\r\n          title: this.$t(\"LocaleString.L00346\"),\r\n          key: \"scanSetting\",\r\n          sortable: true,\r\n          render: (h, params) => {\r\n            return h(\r\n              \"span\",\r\n              params.row.scanSetting\r\n                ? this.bluetoothList.some(\r\n                    (item) => item.value === params.row.scanSetting.toString()\r\n                  )\r\n                  ? this.bluetoothList.find(\r\n                      (item) => item.value === params.row.scanSetting.toString()\r\n                    ).name\r\n                  : \"\"\r\n                : \"\"\r\n            );\r\n          },\r\n        },\r\n        {\r\n          title: this.$t(\"LocaleString.L00045\"),\r\n          key: \"connection.ip\",\r\n          sortable: true,\r\n          width: \"140px\",\r\n          render: (h, params) => {\r\n            return h(\r\n              \"span\",\r\n              params.row.connection ? params.row.connection.ip : \"\"\r\n            );\r\n            // return h('span', params.row.connection.ip)\r\n          },\r\n        },\r\n        // {\r\n        //   title: this.$t(\"LocaleString.L00046\"),\r\n        //   key: \"plane.name\",\r\n        //   sortable: true,\r\n        //   render: (h, params) => {\r\n        //     return h(\"span\", params.row.plane ? params.row.plane.name : \"\");\r\n        //   },\r\n        // },\r\n        // {\r\n        //     'title': this.$t('LocaleString.L00047'),\r\n        //     'key': 'type',\r\n        //     'sortable': true,\r\n        //     render: (h, params) => {\r\n        //         return h('span', params.row.type ? params.row.type : '')\r\n        //     },\r\n        // }\r\n      ],\r\n    };\r\n  },\r\n  mounted() {\r\n    this.openStations = true;\r\n    this.getStationTypeList();\r\n    this.getPlaneList();\r\n    this.getStations();\r\n  },\r\n  methods: {\r\n    /*\r\n        getStationsMenu(callBack) {\r\n            let params = {\r\n                active: true,\r\n                //hasObject:true\r\n            }\r\n            this.$service.getStationsMenu.send(params).then((datas) => {\r\n               // console.log('this.$service.getDevicesMenu')\r\n               // console.log(datas)\r\n\r\n                if(datas && datas.length>0){\r\n                    let hasObjectDevices=datas.map(x=>{return x.pid})\r\n                    //C0:26:DE:00:10:E1\r\n                    callBack(hasObjectDevices)\r\n                } else {\r\n                  callBack([])\r\n                }\r\n            })\r\n        },\r\n\r\n        */\r\n    getStationTypeList() {\r\n      let _this = this;\r\n      // let str = 'active eq true '\r\n      let params = {\r\n        // search: str,\r\n        // inlinecount: true,\r\n        // sort: _this.sort,\r\n      };\r\n      this.$service.getStationsTypeList.send(params).then((data) => {\r\n        console.log(\"data:\" + data);\r\n        _this.stationTypeList = data;\r\n        _this.stationTypeList.forEach(item=>{\r\n          item.name = this.$t(\"LocaleString.\" +item.langs.nameId);\r\n        })\r\n      });\r\n    },\r\n    getPlaneList() {\r\n      let _this = this;\r\n      // let str = 'active eq true '\r\n      let params = {\r\n        active: true,\r\n        //hasObject:true\r\n      };\r\n      this.$service.getPlanes.send(params).then((data) => {\r\n        console.log(data);\r\n        _this.planeList = data.results;\r\n      });\r\n    },\r\n\r\n    getStations() {\r\n      let _this = this;\r\n      let search =\r\n        \"active eq true and enable eq true and plane.code eq \" + this.planeCode;\r\n      if (this.searchSid) {\r\n        search += \" and sid like '\" + this.searchSid + \"'\";\r\n      }\r\n\r\n      if (this.searchName) {\r\n        search += \" and name like '\" + this.searchName + \"'\";\r\n      }\r\n\r\n      if (this.searchBluetooth !== \"all\") {\r\n        search += \" and scanSetting eq \" + this.searchBluetooth;\r\n      }\r\n      if (this.searchStationType !== \"all\") {\r\n        search += \" and type like '\" + this.searchStationType + \"'\";\r\n      }\r\n\r\n      let params = {\r\n        inlinecount: true,\r\n        search: search,\r\n        // page: current,\r\n        // size: pageSize\r\n      };\r\n      //if(this.searchIP) {\r\n      //    params.ipRange= '\\'' +this.searchIP +'\\''\r\n      //}\r\n\r\n      this.$service.getStations.send(params).then((datas) => {\r\n        console.log(datas);\r\n        if (datas.count > 0) {\r\n          if (this.existStations) {\r\n            let existStationSids = this.existStations.map((x) => {\r\n              return x.sid;\r\n            });\r\n            _this.newDataStations = datas.results.filter(\r\n              (x) => !existStationSids.includes(x.sid)\r\n            );\r\n          } else {\r\n            _this.newDataStations = datas.results;\r\n          }\r\n\r\n          /*\r\n                    this.getDevicesMenu((hasObjectDevices)=>{\r\n                        if(hasObjectDevices.length>0) {\r\n                            console.log('hasObjectDevices.toString()')\r\n                            console.log(hasObjectDevices)\r\n                            console.log(_this.newDataStations)\r\n\r\n                            _this.newDataStations = _this.newDataStations.filter(x=>!hasObjectDevices.includes(x.pid))\r\n                        }\r\n                    })\r\n                    */\r\n        } else {\r\n          _this.newDataStations = [];\r\n        }\r\n      });\r\n    },\r\n    passSelectData: function () {\r\n      if (this.$refs.tableStations.getSelection().length === 0) {\r\n        this.$Notice.warning({\r\n          title: \"提醒\",\r\n          desc: \"請選取加入項目\",\r\n          duration: Config.WARNING_DURATION,\r\n        });\r\n      } else {\r\n        let selData = this.$refs.tableStations.getSelection();\r\n        this.$emit(\"closeStationModal\", selData);\r\n        this.openStations = false;\r\n      }\r\n    },\r\n    onClose() {\r\n      this.$emit(\"closeStationModal\");\r\n      this.openStations = false;\r\n    },\r\n    selectChange(selection) {\r\n      this.selection = selection;\r\n      let accountsArr = [];\r\n      selection.forEach(function (element, index) {\r\n        accountsArr.push(element.sid);\r\n      }, this);\r\n      this.responseData = accountsArr;\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n/deep/ .ivu-btn-primary {\r\n  color: #fff;\r\n  background-color: #31babb;\r\n  border-color: #31babb;\r\n}\r\n\r\n/deep/ .ivu-btn-primary:hover {\r\n  color: #fff;\r\n  border-color: #31babb;\r\n}\r\n\r\n/deep/ .ivu-btn-primary.disabled,\r\n.ivu-btn-primary.disabled.active,\r\n.ivu-btn-primary.disabled:active,\r\n.ivu-btn-primary.disabled:focus,\r\n.ivu-btn-primary.disabled:hover,\r\n.ivu-btn-primary[disabled],\r\n.ivu-btn-primary[disabled].active,\r\n.ivu-btn-primary[disabled]:active,\r\n.ivu-btn-primary[disabled]:focus,\r\n.ivu-btn-primary[disabled]:hover,\r\nfieldset[disabled] .ivu-btn-primary,\r\nfieldset[disabled] .ivu-btn-primary.active,\r\nfieldset[disabled] .ivu-btn-primary:active,\r\nfieldset[disabled] .ivu-btn-primary:focus,\r\nfieldset[disabled] .ivu-btn-primary:hover {\r\n  color: #c5c8ce;\r\n  background-color: #f7f7f7;\r\n  border-color: #dcdee2;\r\n}\r\n\r\nbutton {\r\n  width: 150px;\r\n}\r\n</style>\r\n"], "sourceRoot": "src/components/administrative/common"}]}