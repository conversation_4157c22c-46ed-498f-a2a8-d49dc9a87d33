{"remainingRequest": "E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\src\\components\\administrative\\systemManagement\\about.vue?vue&type=script&lang=js&", "dependencies": [{"path": "E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\src\\components\\administrative\\systemManagement\\about.vue", "mtime": 1754362736974}, {"path": "E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\node_modules\\string-replace-loader\\index.js", "mtime": 499162500000}, {"path": "E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgQ29uZmlnIGZyb20gIkAvY29tbW9uL2NvbmZpZyI7DQpsZXQgbW9tZW50ID0gcmVxdWlyZSgibW9tZW50Iik7DQpleHBvcnQgZGVmYXVsdCB7DQogIGRhdGEoKSB7DQogICAgcmV0dXJuIHsNCiAgICAgIGlzU2hvdzogZmFsc2UsDQogICAgICBzeXN0ZW1JbmZvOiBbXSwNCiAgICAgIG5vRGF0YVRleHQ6IHRoaXMuJHQoIkxvY2FsZVN0cmluZy5MMDAwODEiKSwNCiAgICAgIGNvbHVtbnMxOiBbDQogICAgICAgIHsNCiAgICAgICAgICB0aXRsZTogdGhpcy4kdCgiTG9jYWxlU3RyaW5nLkwzMDA1MyIpLA0KICAgICAgICAgIGtleTogIm5hbWUiLA0KICAgICAgICAgIHdpZHRoOiAiMzgwcHgiLA0KICAgICAgICAgIHNvcnRhYmxlOiB0cnVlLA0KICAgICAgICB9LA0KICAgICAgICB7DQogICAgICAgICAgdGl0bGU6IHRoaXMuJHQoIkxvY2FsZVN0cmluZy5MMDAwNzEiKSwNCiAgICAgICAgICBrZXk6ICJ2ZXJzaW9uIiwNCiAgICAgICAgICBzb3J0YWJsZTogdHJ1ZSwNCiAgICAgICAgfSwNCiAgICAgIF0sDQogICAgfTsNCiAgfSwNCiAgbW91bnRlZCgpIHsNCiAgICB0aGlzLmlzU2hvdyA9IHRydWU7DQogICAgdGhpcy5nZXRTeXN0ZW1BYm91dCgpOw0KICAgIHRoaXMuZ2V0TGluY2Vuc2UoKTsNCiAgICAvL3RoaXMubm9ybWFsaXplSGVpZ2h0KCk7DQogIH0sDQogIG1ldGhvZHM6IHsNCiAgICBub3JtYWxpemVIZWlnaHQoKSB7DQogICAgICBzZXRUaW1lb3V0KA0KICAgICAgICBmdW5jdGlvbiAoKSB7DQogICAgICAgICAgY29uc3QgbW9kYWwgPSBkb2N1bWVudC5xdWVyeVNlbGVjdG9yKCIuaXZ1LW1vZGFsLWJvZHkgLmNvbnRlbnQiKTsNCiAgICAgICAgICBpZiAobW9kYWwpIHsNCiAgICAgICAgICAgIG1vZGFsLnN0eWxlLmhlaWdodCA9IHdpbmRvdy5pbm5lckhlaWdodCAtIDIwMCArICJweCI7DQogICAgICAgICAgfQ0KICAgICAgICB9LmJpbmQodGhpcyksDQogICAgICAgIDUwDQogICAgICApOw0KICAgIH0sDQogICAgZHluYW1pY1NvcnQocHJvcGVydHkpIHsNCiAgICAgIHZhciBzb3J0T3JkZXIgPSAxOw0KICAgICAgaWYgKHByb3BlcnR5WzBdID09PSAiLSIpIHsNCiAgICAgICAgc29ydE9yZGVyID0gLTE7DQogICAgICAgIHByb3BlcnR5ID0gcHJvcGVydHkuc3Vic3RyKDEpOw0KICAgICAgfQ0KICAgICAgcmV0dXJuIGZ1bmN0aW9uIChhLCBiKSB7DQogICAgICAgIHZhciByZXN1bHQgPQ0KICAgICAgICAgIGFbcHJvcGVydHldIDwgYltwcm9wZXJ0eV0gPyAtMSA6IGFbcHJvcGVydHldID4gYltwcm9wZXJ0eV0gPyAxIDogMDsNCiAgICAgICAgcmV0dXJuIHJlc3VsdCAqIHNvcnRPcmRlcjsNCiAgICAgIH07DQogICAgfSwNCiAgICByZWZyZXNoQnRuKCkgew0KICAgICAgdGhpcy5nZXRTeXN0ZW1BYm91dCgpOw0KICAgIH0sDQogICAgZ2V0U3lzdGVtQWJvdXQoKSB7DQogICAgICBsZXQgX3RoaXMgPSB0aGlzOw0KICAgICAgX3RoaXMuc3lzdGVtSW5mby5wdXNoKHsNCiAgICAgICAgbmFtZTogdGhpcy4kdCgiTG9jYWxlU3RyaW5nLlMwMDAwMiIpLA0KICAgICAgICB2ZXJzaW9uOiBDb25maWcuV0VCX1ZFUlNJT04sDQogICAgICB9KTsNCiAgICB9LA0KICAgIGdldExpbmNlbnNlKGN1cnJlbnQsIHBhZ2VTaXplKSB7DQogICAgICBsZXQgX3RoaXMgPSB0aGlzDQogICAgICBsZXQgc3RyID0gJ2FjdGl2ZSBlcSB0cnVlJw0KICAgICAgbGV0IHBhcmFtcyA9IHsNCiAgICAgICAgICBzZWFyY2g6IHN0ciwNCiAgICAgICAgICBpbmxpbmVjb3VudDogdHJ1ZSwNCiAgICAgICAgICAvLyBzb3J0OiBfdGhpcy5zb3J0LA0KICAgICAgICAgIC8vIHBhZ2U6IGN1cnJlbnQsDQogICAgICAgICAgLy8gc2l6ZTogcGFnZVNpemUNCiAgICAgIH0NCiAgICAgIC8vIHBhcmFtcyA9IG5ldyBVUkxTZWFyY2hQYXJhbXMocGFyYW1zKQ0KICAgICAgLy8gdGhpcy4kc2VydmljZS5nZXRVc2Vycy5yZXF1ZXN0Q29tbW9uKF90aGlzLnF1ZXJ5U3RyLCBjdXJyZW50LCBwYWdlU2l6ZSkNCiAgICAgIHRoaXMuJHNlcnZpY2UuZ2V0U2VydmljZUxpY2Vuc2Uuc2VuZChwYXJhbXMpLnRoZW4oKGRhdGEpID0+IHsNCiAgICAgICAgaWYgKGRhdGEuY291bnQgPiAwKSB7IA0KICAgICAgICAgICBfdGhpcy5zeXN0ZW1JbmZvLnB1c2goew0KICAgICAgICAgICAgbmFtZTogdGhpcy4kdCgiTG9jYWxlU3RyaW5nLkwwMDEwMyIpLA0KICAgICAgICAgICAgdmVyc2lvbjogZGF0YS5yZXN1bHRzLnNvbWUoaXRlbT0+aXRlbS5jb2RlID09ICJMb3NzU2lnbmFsIiAmJiBpdGVtLmxpY2Vuc2UuZXhwaXJlc0F0KT8gIGRhdGEucmVzdWx0cy5maW5kKGl0ZW09Pml0ZW0uY29kZSA9PSAiTG9zc1NpZ25hbCIpLmxpY2Vuc2UuZXhwaXJlc0F0IDogIi0iLA0KICAgICAgICAgIH0pOw0KICAgICAgICB9DQogICAgICB9KTsNCiAgICB9LA0KICAgIGNvbXB1dGVkRGF0ZSh2YWwpIHsNCiAgICAgIGlmICh2YWwpIHsNCiAgICAgICAgcmV0dXJuIG1vbWVudCh2YWwpLmZvcm1hdCgiWVlZWS1NTS1ERCIpOw0KICAgICAgfQ0KICAgICAgcmV0dXJuICIiOw0KICAgIH0sDQogICAgY29tcHV0ZWREYXRlVGltZSh2YWwpIHsNCiAgICAgIGlmICh2YWwpIHsNCiAgICAgICAgcmV0dXJuIG1vbWVudCh2YWwpLmZvcm1hdCgiWVlZWS1NTS1ERCBISDptbTpzcyIpOw0KICAgICAgfQ0KICAgICAgcmV0dXJuICIiOw0KICAgIH0sDQogICAgY2FuY2VsKCkgew0KICAgICAgdGhpcy5pc1Nob3cgPSBmYWxzZTsNCiAgICAgIHNldFRpbWVvdXQoKCkgPT4gew0KICAgICAgICB0aGlzLiRlbWl0KCJjbG9zZUFib3V0Iik7DQogICAgICB9LCA1MDApOw0KICAgIH0sDQogIH0sDQp9Ow0K"}, {"version": 3, "sources": ["about.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAoCA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "about.vue", "sourceRoot": "src/components/administrative/systemManagement", "sourcesContent": ["<template>\r\n  <div class=\"personal-data\">\r\n    <Modal\r\n      class=\"about-data-modal\"\r\n      :mask-closable=\"false\"\r\n      v-model=\"isShow\"\r\n      class-name=\"vertical-center-modal\"\r\n      @on-cancel=\"cancel\"\r\n      width=\"850\"\r\n    >\r\n      <a slot=\"close\">\r\n        <Icon type=\"md-close\" color=\"#ADADAD\" size=\"24\"></Icon>\r\n      </a>\r\n      <div slot=\"header\" class=\"header\">\r\n        <i class=\"left\"></i>\r\n        <span class=\"title\"></span>\r\n          <span class=\"title\">{{ $t(\"LocaleString.L30052\") }}</span>\r\n        </span>\r\n        <!--\r\n        <Button class=\"es-btn\" type=\"primary\" icon=\"ios-refresh\" @click=\"refreshBtn\">Refresh</Button>\r\n        -->\r\n      </div>\r\n      <div class=\"content\">\r\n        <Table\r\n          class=\"about-table\"\r\n          :columns=\"columns1\"\r\n          :data=\"systemInfo\"\r\n          :no-data-text=\"noDataText\"\r\n        ></Table>\r\n      </div>\r\n      <div slot=\"footer\"></div>\r\n    </Modal>\r\n  </div>\r\n</template>\r\n\r\n<script type='es6'>\r\nimport Config from \"@/common/config\";\r\nlet moment = require(\"moment\");\r\nexport default {\r\n  data() {\r\n    return {\r\n      isShow: false,\r\n      systemInfo: [],\r\n      noDataText: this.$t(\"LocaleString.L00081\"),\r\n      columns1: [\r\n        {\r\n          title: this.$t(\"LocaleString.L30053\"),\r\n          key: \"name\",\r\n          width: \"380px\",\r\n          sortable: true,\r\n        },\r\n        {\r\n          title: this.$t(\"LocaleString.L00071\"),\r\n          key: \"version\",\r\n          sortable: true,\r\n        },\r\n      ],\r\n    };\r\n  },\r\n  mounted() {\r\n    this.isShow = true;\r\n    this.getSystemAbout();\r\n    this.getLincense();\r\n    //this.normalizeHeight();\r\n  },\r\n  methods: {\r\n    normalizeHeight() {\r\n      setTimeout(\r\n        function () {\r\n          const modal = document.querySelector(\".ivu-modal-body .content\");\r\n          if (modal) {\r\n            modal.style.height = window.innerHeight - 200 + \"px\";\r\n          }\r\n        }.bind(this),\r\n        50\r\n      );\r\n    },\r\n    dynamicSort(property) {\r\n      var sortOrder = 1;\r\n      if (property[0] === \"-\") {\r\n        sortOrder = -1;\r\n        property = property.substr(1);\r\n      }\r\n      return function (a, b) {\r\n        var result =\r\n          a[property] < b[property] ? -1 : a[property] > b[property] ? 1 : 0;\r\n        return result * sortOrder;\r\n      };\r\n    },\r\n    refreshBtn() {\r\n      this.getSystemAbout();\r\n    },\r\n    getSystemAbout() {\r\n      let _this = this;\r\n      _this.systemInfo.push({\r\n        name: this.$t(\"LocaleString.S00002\"),\r\n        version: Config.WEB_VERSION,\r\n      });\r\n    },\r\n    getLincense(current, pageSize) {\r\n      let _this = this\r\n      let str = 'active eq true'\r\n      let params = {\r\n          search: str,\r\n          inlinecount: true,\r\n          // sort: _this.sort,\r\n          // page: current,\r\n          // size: pageSize\r\n      }\r\n      // params = new URLSearchParams(params)\r\n      // this.$service.getUsers.requestCommon(_this.queryStr, current, pageSize)\r\n      this.$service.getServiceLicense.send(params).then((data) => {\r\n        if (data.count > 0) { \r\n           _this.systemInfo.push({\r\n            name: this.$t(\"LocaleString.L00103\"),\r\n            version: data.results.some(item=>item.code == \"LossSignal\" && item.license.expiresAt)?  data.results.find(item=>item.code == \"LossSignal\").license.expiresAt : \"-\",\r\n          });\r\n        }\r\n      });\r\n    },\r\n    computedDate(val) {\r\n      if (val) {\r\n        return moment(val).format(\"YYYY-MM-DD\");\r\n      }\r\n      return \"\";\r\n    },\r\n    computedDateTime(val) {\r\n      if (val) {\r\n        return moment(val).format(\"YYYY-MM-DD HH:mm:ss\");\r\n      }\r\n      return \"\";\r\n    },\r\n    cancel() {\r\n      this.isShow = false;\r\n      setTimeout(() => {\r\n        this.$emit(\"closeAbout\");\r\n      }, 500);\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n.about-data-modal {\r\n  // .vertical-center-modal {\r\n  //   display: flex;\r\n  //   align-items: center;\r\n  //   justify-content: center;\r\n  //   .ivu-modal {\r\n  //     top: 0;\r\n  //   }\r\n  // }\r\n  .ivu-modal-close {\r\n    top: 15px;\r\n    right: 20px;\r\n  }\r\n  .content {\r\n    padding: 4px 14px;\r\n    overflow-y: auto;\r\n    //height: 700px;\r\n    //height: 400px;\r\n  }\r\n\r\n  @media only screen and (min-width: 1200px) {\r\n    .ivu-modal-close {\r\n      top: 18px;\r\n    }\r\n  }\r\n  .ivu-modal-header {\r\n    padding: 15px 20px;\r\n    border-bottom: 1px solid #f7f7f7;\r\n    .header {\r\n      overflow: hidden;\r\n      .left {\r\n        float: left;\r\n        width: 6px;\r\n        height: 24px;\r\n        margin-right: 8px;\r\n        background-color: #0ac7c0;\r\n        border-radius: 3px;\r\n      }\r\n      .title {\r\n        float: left;\r\n        height: 24px;\r\n        line-height: 24px;\r\n        font-size: 14px;\r\n        font-weight: bold;\r\n        color: #999;\r\n        span {\r\n          color: #333;\r\n        }\r\n      }\r\n      .es-btn {\r\n        float: right;\r\n        margin-right: 35px;\r\n      }\r\n      @media only screen and (min-width: 1200px) {\r\n        .left {\r\n          height: 30px;\r\n        }\r\n        .title {\r\n          height: 30px;\r\n          line-height: 30px;\r\n          font-size: 18px;\r\n        }\r\n      }\r\n    }\r\n  }\r\n  .ivu-modal-body {\r\n    .content {\r\n      padding: 4px 14px;\r\n      overflow-y: auto;\r\n      .row {\r\n        margin-bottom: 20px;\r\n        .item {\r\n          padding: 15px;\r\n          border: 1px solid #f7f7f7;\r\n          border-radius: 4px;\r\n          label {\r\n            color: #999;\r\n            font-weight: bold;\r\n          }\r\n          p {\r\n            margin-top: 10px;\r\n            color: #333;\r\n          }\r\n        }\r\n      }\r\n      .form-item {\r\n        padding: 15px 15px 22px;\r\n        margin-bottom: 16px;\r\n        border-radius: 4px;\r\n        background-color: #f7f7f7;\r\n      }\r\n    }\r\n  }\r\n  .ivu-modal-footer {\r\n    border-top: none;\r\n    padding: 0;\r\n  }\r\n}\r\n</style>\r\n"]}]}