{"remainingRequest": "E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\node_modules\\babel-loader\\lib\\index.js!E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\node_modules\\string-replace-loader\\index.js??ref--13-0!E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\src\\components\\administrative\\common\\AdminHeader.vue?vue&type=script&lang=js&", "dependencies": [{"path": "E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\src\\components\\administrative\\common\\AdminHeader.vue", "mtime": 1754362736906}, {"path": "E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\node_modules\\string-replace-loader\\index.js", "mtime": 499162500000}, {"path": "E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMuYXJyYXkuZm9yLWVhY2giOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy9lcy5hcnJheS5zcGxpY2UiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy9lcy5mdW5jdGlvbi5uYW1lIjsKaW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvd2ViLmRvbS1jb2xsZWN0aW9ucy5mb3ItZWFjaCI7CmltcG9ydCBfb2JqZWN0U3ByZWFkIGZyb20gIkU6L0dpdFJlcG9zL0Z1c2lvbk5ldFByb2plY3QvNjg0OS9mcm9udGVuZC9hcHBsaWNhdGlvbi9zbnMvbm9kZV9tb2R1bGVzL0BiYWJlbC9ydW50aW1lL2hlbHBlcnMvZXNtL29iamVjdFNwcmVhZDIiOwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLyBpbXBvcnQgc25zTG9nbyBmcm9tICJAL2Fzc2V0cy9pbWFnZXMvbG9nb19zbnMucG5nIjsKaW1wb3J0IHNuc0xvZ28gZnJvbSAiQC9hc3NldHMvaW1hZ2VzL0ZVU0lPTl9MT0dPX0Z1c2lvbkNhcmUuc3ZnIjsgLy9pbXBvcnQgc25zTG9nbyBmcm9tICJAL2Fzc2V0cy9pbWFnZXMvRlVTSU9OX0xPR09fRnVzaW9uQ2FyZS5wbmciOwoKaW1wb3J0IEFib3V0IGZyb20gIkAvY29tcG9uZW50cy9hZG1pbmlzdHJhdGl2ZS9zeXN0ZW1NYW5hZ2VtZW50L2Fib3V0LnZ1ZSI7CmltcG9ydCBTeXN0ZW1Db25maWcgZnJvbSAiQC9jb21wb25lbnRzL2FkbWluaXN0cmF0aXZlL3N5c3RlbU1hbmFnZW1lbnQvc3lzdGVtQ29uZmlnLnZ1ZSI7CmltcG9ydCBLTUNvbmZpZyBmcm9tICJAL2NvbXBvbmVudHMvYWRtaW5pc3RyYXRpdmUvc3lzdGVtTWFuYWdlbWVudC9rbUNvbmZpZy52dWUiOwppbXBvcnQgQ29uZmlnIGZyb20gIkAvY29tbW9uL2NvbmZpZyI7IC8vIGltcG9ydCBXZWJTdG9yYWdlIGZyb20gJ0Avc3RvcmUvd2ViLXN0b3JhZ2UnCi8vIGltcG9ydCBzaWRlclRyaWdnZXIgZnJvbSAnLi9zaWRlci10cmlnZ2VyJwovLyBpbXBvcnQgY3VzdG9tQnJlYWRDcnVtYiBmcm9tICcuL2N1c3RvbS1icmVhZC1jcnVtYicKCmltcG9ydCBDb21tb25TZXJ2aWNlIGZyb20gIkAvY29tcG9uZW50cy9hZG1pbmlzdHJhdGl2ZS9hcHBzL2NvbW1vblNlcnZpY2UiOwppbXBvcnQgeyBtYXBHZXR0ZXJzIH0gZnJvbSAidnVleCI7CmltcG9ydCB7IGxvY2FsZSBhcyBlblVTIH0gZnJvbSAiQC9sb2NhbGVzL2VuLVVTIjsKaW1wb3J0IHsgbG9jYWxlIGFzIHpoQ04gfSBmcm9tICJAL2xvY2FsZXMvemgtQ04iOwppbXBvcnQgeyBsb2NhbGUgYXMgemhUVyB9IGZyb20gIkAvbG9jYWxlcy96aC1UVyI7CmV4cG9ydCBkZWZhdWx0IHsKICBuYW1lOiAiQWRtaW5IZWFkZXIiLAogIHByb3BzOiBbImNvbGxhcHNlZCIsICJpc1Nob3dSaWdodFNpZGVyIiwgImlzT3BlbiJdLAogIGRhdGE6IGZ1bmN0aW9uIGRhdGEoKSB7CiAgICB2YXIgX3RoaXMyID0gdGhpczsKCiAgICB2YXIgdmFsaWRhdGVPbGRQYXNzID0gZnVuY3Rpb24gdmFsaWRhdGVPbGRQYXNzKHJ1bGUsIHZhbHVlLCBjYWxsYmFjaykgewogICAgICBpZiAodmFsdWUgPT09ICIiKSB7CiAgICAgICAgY2FsbGJhY2sobmV3IEVycm9yKF90aGlzMi4kdCgiTG9jYWxlU3RyaW5nLkwwMDAwMSIsIHsKICAgICAgICAgIDA6IF90aGlzMi4kdCgiTG9jYWxlU3RyaW5nLkwwMDAwOCIpCiAgICAgICAgfSkpKTsKICAgICAgfSBlbHNlIGlmIChfdGhpczIuZXJyb3JUeXBlID09PSAxKSB7CiAgICAgICAgY2FsbGJhY2sobmV3IEVycm9yKF90aGlzMi4kdCgiTG9jYWxlU3RyaW5nLkUwMDAwOCIpKSk7CiAgICAgIH0gZWxzZSB7CiAgICAgICAgY2FsbGJhY2soKTsKICAgICAgfQoKICAgICAgX3RoaXMyLmVycm9yVHlwZSA9IDA7CiAgICB9OwoKICAgIHZhciB2YWxpZGF0ZU5ld1Bhc3MgPSBmdW5jdGlvbiB2YWxpZGF0ZU5ld1Bhc3MocnVsZSwgdmFsdWUsIGNhbGxiYWNrKSB7CiAgICAgIHZhciByZWcgPSAvXig/IVtcZF0rJCkoPyFbYS16QS1aXSskKSg/IVt+YEAjJCVeJipcLV89K3w/KCk8PltcXXt9IiwuOychXSskKVtcZGEtekEtWn5gQCMkJV4mKlwtXz0rfD8oKTw+W1xde30iLC47JyFdezgsMTV9JC87CgogICAgICBpZiAodmFsdWUgPT09ICIiKSB7CiAgICAgICAgY2FsbGJhY2sobmV3IEVycm9yKF90aGlzMi4kdCgiTG9jYWxlU3RyaW5nLkwwMDAwMSIsIHsKICAgICAgICAgIDA6IF90aGlzMi4kdCgiTG9jYWxlU3RyaW5nLkwwMDAwOSIpCiAgICAgICAgfSkpKTsKICAgICAgfSBlbHNlIGlmICghcmVnLnRlc3QodmFsdWUpKSB7CiAgICAgICAgY2FsbGJhY2sobmV3IEVycm9yKF90aGlzMi4kdCgiTG9jYWxlU3RyaW5nLlcwMDAyOCIpKSk7CiAgICAgIH0gZWxzZSBpZiAoX3RoaXMyLmVycm9yVHlwZSA9PT0gMikgewogICAgICAgIGNhbGxiYWNrKG5ldyBFcnJvcihfdGhpczIuJHQoIkxvY2FsZVN0cmluZy5XMDAwMjgiKSkpOwogICAgICB9IGVsc2UgaWYgKF90aGlzMi5mb3JtQ2hhbmdlUGFzc3dvcmQubmV3UGFzc3dkID09PSBfdGhpczIuZm9ybUNoYW5nZVBhc3N3b3JkLm9sZFBhc3N3ZCkgewogICAgICAgIGNhbGxiYWNrKG5ldyBFcnJvcihfdGhpczIuJHQoIkxvY2FsZVN0cmluZy5NMDAwMTUiKSkpOwogICAgICB9IGVsc2UgewogICAgICAgIGlmIChfdGhpczIuZm9ybUNoYW5nZVBhc3N3b3JkLm5ld1Bhc3N3ZENoZWNrICE9PSAiIikgewogICAgICAgICAgLy8g5a+556ysM+S4quWvhueggeahhuWNleeLrOmqjOivgQogICAgICAgICAgX3RoaXMyLiRyZWZzLmZvcm1DaGFuZ2VQYXNzd29yZC52YWxpZGF0ZUZpZWxkKCJuZXdQYXNzd2RDaGVjayIpOwogICAgICAgIH0KCiAgICAgICAgY2FsbGJhY2soKTsKICAgICAgfQoKICAgICAgX3RoaXMyLmVycm9yVHlwZSA9IDA7CiAgICB9OwoKICAgIHZhciB2YWxpZGF0ZU5ld1Bhc3NDaGVjayA9IGZ1bmN0aW9uIHZhbGlkYXRlTmV3UGFzc0NoZWNrKHJ1bGUsIHZhbHVlLCBjYWxsYmFjaykgewogICAgICBpZiAodmFsdWUgPT09ICIiKSB7CiAgICAgICAgY2FsbGJhY2sobmV3IEVycm9yKF90aGlzMi4kdCgiTG9jYWxlU3RyaW5nLkwwMDAwMSIsIHsKICAgICAgICAgIDA6IF90aGlzMi4kdCgiTG9jYWxlU3RyaW5nLkwwMDAxMCIpCiAgICAgICAgfSkpKTsKICAgICAgfSBlbHNlIGlmICh2YWx1ZSAhPT0gX3RoaXMyLmZvcm1DaGFuZ2VQYXNzd29yZC5uZXdQYXNzd2QpIHsKICAgICAgICBjYWxsYmFjayhuZXcgRXJyb3IoX3RoaXMyLiR0KCJMb2NhbGVTdHJpbmcuVzAwMDA3IikpKTsKICAgICAgfSBlbHNlIHsKICAgICAgICBjYWxsYmFjaygpOwogICAgICB9CiAgICB9OwoKICAgIHJldHVybiB7CiAgICAgIHNuc0xvZ286IHNuc0xvZ28sCiAgICAgIGZpbGVVUkw6IG51bGwsCiAgICAgIGxhbmdMaXN0OiBbXSwKICAgICAgc3ViQ29udHJvbDogIiIsCiAgICAgIGZvcm1DaGFuZ2VQYXNzd29yZDogewogICAgICAgIG9sZFBhc3N3ZDogIiIsCiAgICAgICAgbmV3UGFzc3dkOiAiIiwKICAgICAgICBuZXdQYXNzd2RDaGVjazogIiIKICAgICAgfSwKICAgICAgd2ViVmVyc2lvbjogQ29uZmlnLldFQl9WRVJTSU9OLAogICAgICB1c2VyTmFtZTogbG9jYWxTdG9yYWdlLmdldEl0ZW0oInNuc19sb2dpblVzZXIiKSwKICAgICAgbGFuZ0NoYW5nZTogbG9jYWxTdG9yYWdlLmdldEl0ZW0oInNuc19sYW5nIiksCiAgICAgIGJyZWFkQ3J1bWJzOiBbXSwKICAgICAgc2VsQXBwVGFiOiAiTW9uaXRvciIsCiAgICAgIGlzU2hvd0VkaXRQYXNzd29yZE1vZGFsOiBmYWxzZSwKICAgICAgbG9hZGluZzogdHJ1ZSwKICAgICAgcmVzZXRGb3JtSXRlbTogewogICAgICAgIHBhc3N3b3JkOiAiIgogICAgICB9LAogICAgICBzaG93QWJvdXQ6IGZhbHNlLAogICAgICBzaG93U3lzdGVtQ29uZmlnOiBmYWxzZSwKICAgICAgc2hvd0tNQ29uZmlnOiBmYWxzZSwKICAgICAgYXBwc0VkaXQ6IHRydWUsCiAgICAgIHJ1bGVDaGFuZ2VQYXNzd29yZDogewogICAgICAgIG9sZFBhc3N3ZDogW3sKICAgICAgICAgIHZhbGlkYXRvcjogdmFsaWRhdGVPbGRQYXNzLAogICAgICAgICAgdHJpZ2dlcjogInN1Ym1pdCIKICAgICAgICB9XSwKICAgICAgICBuZXdQYXNzd2Q6IFt7CiAgICAgICAgICB2YWxpZGF0b3I6IHZhbGlkYXRlTmV3UGFzcywKICAgICAgICAgIHRyaWdnZXI6ICJzdWJtaXQiCiAgICAgICAgfV0sCiAgICAgICAgbmV3UGFzc3dkQ2hlY2s6IFt7CiAgICAgICAgICB2YWxpZGF0b3I6IHZhbGlkYXRlTmV3UGFzc0NoZWNrLAogICAgICAgICAgdHJpZ2dlcjogInN1Ym1pdCIKICAgICAgICB9XQogICAgICB9LAogICAgICBhcHBDb2RlOiBsb2NhbFN0b3JhZ2UuZ2V0SXRlbSgiYXBwbGljYXRpb25Db2RlIikKICAgIH07CiAgfSwKICBjb21wb25lbnRzOiB7CiAgICAvLyBzaWRlclRyaWdnZXIgLy8sCiAgICAvLyBjdXN0b21CcmVhZENydW1iCiAgICBBYm91dDogQWJvdXQsCiAgICBTeXN0ZW1Db25maWc6IFN5c3RlbUNvbmZpZywKICAgIEtNQ29uZmlnOiBLTUNvbmZpZwogIH0sCiAgY29tcHV0ZWQ6IF9vYmplY3RTcHJlYWQoX29iamVjdFNwcmVhZCh7fSwgbWFwR2V0dGVycyhbImJyZWFkQ3J1bWJMaXN0IiwgInBvY0NvbmZpZ0NoYW5nZWQiLCAiYXBwVGFiIiwgImxvZ291dEFjdGlvbiJdKSksIHt9LCB7CiAgICAvLyBsYW5nTGlzdCgpIHsKICAgIC8vICAgcmV0dXJuIFsKICAgIC8vICAgICB7CiAgICAvLyAgICAgICB2YWx1ZTogInpoLVRXIiwKICAgIC8vICAgICAgIGxhYmVsOiB0aGlzLiR0KCJMb2NhbGVTdHJpbmcuRDEwMDAxIiksCiAgICAvLyAgICAgfSwKICAgIC8vICAgICB7CiAgICAvLyAgICAgICB2YWx1ZTogImVuLVVTIiwKICAgIC8vICAgICAgIGxhYmVsOiB0aGlzLiR0KCJMb2NhbGVTdHJpbmcuRDEwMDAyIiksCiAgICAvLyAgICAgfSwKICAgIC8vICAgICB7CiAgICAvLyAgICAgICB2YWx1ZTogInpoLUNOIiwKICAgIC8vICAgICAgIGxhYmVsOiB0aGlzLiR0KCJMb2NhbGVTdHJpbmcuRDEwMDAzIiksCiAgICAvLyAgICAgfSwKICAgIC8vICAgXTsKICAgIC8vIH0sCiAgICBjdXJyZW50Um91dGVOYW1lOiBmdW5jdGlvbiBjdXJyZW50Um91dGVOYW1lKCkgewogICAgICByZXR1cm4gdGhpcy4kcm91dGUubmFtZTsKICAgIH0KICAgIC8qDQogICAgICBicmVhZENydW1iTGlzdCAoKSB7DQogICAgICAgIHJldHVybiB0aGlzLiRzdG9yZS5zdGF0ZS5hcHAuYnJlYWRDcnVtYkxpc3QNCiAgICAgIH0NCiAgICAgICovCgogIH0pLAogIHdhdGNoOiB7CiAgICAkcm91dGU6ICJyb3V0aW5nQ2hhbmdlIiwKICAgIGJyZWFkQ3J1bWJMaXN0OiBmdW5jdGlvbiBicmVhZENydW1iTGlzdCh2YWwpIHsKICAgICAgLy8gICB0aGlzLmJyZWFkQ3J1bWI9dmFsLnRvU3RyaW5nKCkKICAgICAgdGhpcy5icmVhZENydW1icyA9IHZhbDsKICAgIH0sCiAgICBwb2NDb25maWdDaGFuZ2VkOiBmdW5jdGlvbiBwb2NDb25maWdDaGFuZ2VkKHZhbCkgewogICAgICBjb25zb2xlLmxvZygicG9jQ29uZmlnQ2hhbmdlZCIpOwogICAgICBjb25zb2xlLmxvZyh2YWwpOwogICAgICB0aGlzLmdldEFwcHNFZGl0YWJsZSgpOwogICAgICB0aGlzLmdldExvZ28oKTsKICAgIH0sCiAgICBhcHBUYWI6IGZ1bmN0aW9uIGFwcFRhYih2YWwpIHsKICAgICAgc3dpdGNoICh2YWwpIHsKICAgICAgICBjYXNlICJQbGFuZVNldHRpbmciOgogICAgICAgICAgdGhpcy5zdWJDb250cm9sID0gIiA+IOWwjeixoeeuoeeQhiI7CiAgICAgICAgICBicmVhazsKCiAgICAgICAgY2FzZSAiT2JqZWN0U2V0dGluZyI6CiAgICAgICAgICB0aGlzLnN1YkNvbnRyb2wgPSAiID4g5ZyW6Z2i566h55CGIjsKICAgICAgICAgIGJyZWFrOwoKICAgICAgICBjYXNlICJUaGlyZFBhcnR5U2V0dGluZyI6CiAgICAgICAgICB0aGlzLnN1YkNvbnRyb2wgPSAiID4gIiArIHRoaXMuJHQoIkxvY2FsZVN0cmluZy5GMzAwMTIiKTsKICAgICAgICAgIGJyZWFrOwoKICAgICAgICBkZWZhdWx0OgogICAgICAgICAgdGhpcy5zdWJDb250cm9sID0gIiI7CiAgICAgICAgICBicmVhazsKICAgICAgfQogICAgfSwKICAgIGxhbmdDaGFuZ2U6IGZ1bmN0aW9uIGxhbmdDaGFuZ2UoKSB7CiAgICAgIHRoaXMuZ2V0TG9jYWxlcygpOwogICAgfSwKICAgIGxvZ291dEFjdGlvbjogZnVuY3Rpb24gbG9nb3V0QWN0aW9uKCkgewogICAgICB0aGlzLmxvZ291dCgpOwogICAgfQogIH0sCiAgY3JlYXRlZDogZnVuY3Rpb24gY3JlYXRlZCgpIHsKICAgIHRoaXMuZ2V0TG9jYWxlcygpOwoKICAgIGlmIChsb2NhbFN0b3JhZ2UuZ2V0SXRlbSgic25zX3N5c3RlbVRpdGxlIikgIT0gbnVsbCkgewogICAgICBkb2N1bWVudC50aXRsZSA9IGxvY2FsU3RvcmFnZS5nZXRJdGVtKCJzbnNfc3lzdGVtVGl0bGUiKTsKICAgIH0gZWxzZSB7CiAgICAgIGRvY3VtZW50LnRpdGxlID0gdGhpcy4kdCgiTG9jYWxlU3RyaW5nLlMwMDAwMiIpOwogICAgfQoKICAgIHRoaXMuZmlsZVVSTCA9IGxvY2FsU3RvcmFnZS5nZXRJdGVtKCJzbnNfbG9nb1VSTCIpID8gbG9jYWxTdG9yYWdlLmdldEl0ZW0oInNuc19sb2dvVVJMIikgOiBudWxsOwogICAgdGhpcy5nZXRMb2dvKCk7CiAgfSwKICBtb3VudGVkOiBmdW5jdGlvbiBtb3VudGVkKCkgewogICAgdGhpcy5nZXRBcHBzRWRpdGFibGUoKTsKICAgIHRoaXMucm91dGluZ0NoYW5nZSgpOwogIH0sCiAgbWV0aG9kczogewogICAgZ2V0TG9nbzogZnVuY3Rpb24gZ2V0TG9nbygpIHsKICAgICAgdmFyIF90aGlzMyA9IHRoaXM7CgogICAgICB2YXIgcG9jR3JvdXBQYXJhbXMgPSB7CiAgICAgICAgaW5saW5lY291bnQ6IHRydWUsCiAgICAgICAgc2VhcmNoOiAiYWN0aXZlIGVxIHRydWUgYW5kIGNhdGVnb3J5IGVxIEBTTlNATG9nbyIKICAgICAgfTsKICAgICAgdGhpcy4kc2VydmljZS5nZXRQT0NHcm91cHNCeUNhdGVnb3J5LnNlbmQocG9jR3JvdXBQYXJhbXMpLnRoZW4oZnVuY3Rpb24gKHBvY1JlcykgewogICAgICAgIGlmIChwb2NSZXMuY291bnQgPiAwICYmIHBvY1Jlcy5yZXN1bHRzWzBdLmZpbGVzICYmIHBvY1Jlcy5yZXN1bHRzWzBdLmZpbGVzLmxlbmd0aCA+IDApIHsKICAgICAgICAgIF90aGlzMy5maWxlVVJMID0gcG9jUmVzLnJlc3VsdHNbMF0uZmlsZXNbMF0udXJsOwogICAgICAgICAgbG9jYWxTdG9yYWdlLnNldEl0ZW0oInNuc19sb2dvVVJMIiwgX3RoaXMzLmZpbGVVUkwpOwogICAgICAgIH0gZWxzZSB7CiAgICAgICAgICBfdGhpczMuZmlsZVVSTCA9IG51bGw7CiAgICAgICAgfQogICAgICB9KTsKICAgIH0sCiAgICBnZXRMb2NhbGVzOiBmdW5jdGlvbiBnZXRMb2NhbGVzKCkgewogICAgICB2YXIgX3RoaXM0ID0gdGhpczsKCiAgICAgIGNvbnNvbGUubG9nKCJsYW5nOiIgKyB0aGlzLmxhbmd1YWdlKTsKCiAgICAgIHZhciBfdGhpcyA9IHRoaXM7CgogICAgICB2YXIgc3RyID0gIiI7CiAgICAgIHZhciBwYXJhbXMgPSB7CiAgICAgICAgc2VhcmNoOiBzdHIsCiAgICAgICAgaW5saW5lY291bnQ6IHRydWUKICAgICAgfTsgLy8gdGhpcy4kc2VydmljZS5nZXRVc2Vycy5yZXF1ZXN0Q29tbW9uKF90aGlzLnF1ZXJ5U3RyLCBjdXJyZW50LCBwYWdlU2l6ZSkKCiAgICAgIHRoaXMuJHNlcnZpY2UuZ2V0TG9jYWxlcy5zZW5kKHBhcmFtcykudGhlbihmdW5jdGlvbiAoZGF0YSkgewogICAgICAgIF90aGlzLnJlc3VsdHMgPSBkYXRhLnJlc3VsdHM7IC8vIGNvbnNvbGUubG9nKGRhdGEpCgogICAgICAgIF90aGlzLmxhbmdMaXN0LnNwbGljZSgwKTsKCiAgICAgICAgdmFyIG9iaiA9IHt9OwogICAgICAgIGRhdGEucmVzdWx0cy5mb3JFYWNoKGZ1bmN0aW9uIChlbGVtZW50KSB7CiAgICAgICAgICBvYmogPSB7CiAgICAgICAgICAgIHZhbHVlOiBlbGVtZW50LmxvY2FsZSwKICAgICAgICAgICAgbGFiZWw6IF90aGlzNC4kdCgiTG9jYWxlU3RyaW5nLiIgKyBlbGVtZW50LnN0cmluZ0lkKQogICAgICAgICAgfTsKCiAgICAgICAgICBfdGhpcy5sYW5nTGlzdC5wdXNoKG9iaik7CiAgICAgICAgfSk7CiAgICAgICAgY29uc29sZS5sb2coInp6ejoiICsgSlNPTi5zdHJpbmdpZnkoX3RoaXMubGFuZ0xpc3QpKTsKICAgICAgfSk7CiAgICB9LAogICAgLy8gcmVsb2FkUGFnZSgpIHsKICAgIC8vICAgbG9jYXRpb24ucmVsb2FkKHRydWUpOwogICAgLy8gfSwKICAgIGNsb3NlTW9kYWw6IGZ1bmN0aW9uIGNsb3NlTW9kYWwoKSB7CiAgICAgIHRoaXMuaXNTaG93RWRpdFBhc3N3b3JkTW9kYWwgPSBmYWxzZTsKICAgICAgdGhpcy5mb3JtQ2hhbmdlUGFzc3dvcmQgPSB7CiAgICAgICAgb2xkUGFzc3dkOiAiIiwKICAgICAgICBuZXdQYXNzd2Q6ICIiLAogICAgICAgIG5ld1Bhc3N3ZENoZWNrOiAiIgogICAgICB9OwogICAgfSwKICAgIGhhbmRsZUNsaWNrOiBmdW5jdGlvbiBoYW5kbGVDbGljayh2YWwpIHsKICAgICAgc3dpdGNoICh2YWwpIHsKICAgICAgICBjYXNlICJsb2dvdXQiOgogICAgICAgICAgdGhpcy5sb2dvdXQoKTsKICAgICAgICAgIGJyZWFrOwoKICAgICAgICBjYXNlICJzaG93RWRpdFBhc3N3b3JkTW9kYWwiOgogICAgICAgICAgdGhpcy5pc1Nob3dFZGl0UGFzc3dvcmRNb2RhbCA9IHRydWU7CiAgICAgICAgICBicmVhazsKICAgICAgfQogICAgfSwKICAgIGxvZ291dDogZnVuY3Rpb24gbG9nb3V0KCkgewogICAgICB2YXIgYXBwbGljYXRpb25Db2RlID0gbG9jYWxTdG9yYWdlLmdldEl0ZW0oInNuc19hcHBsaWNhdGlvbkNvZGUiKSB8fCAiIjsKICAgICAgbG9jYWxTdG9yYWdlLnJlbW92ZUl0ZW0oInNuc19hY2Nlc3NUb2tlbiIgKyBhcHBsaWNhdGlvbkNvZGUpOwogICAgICBsb2NhbFN0b3JhZ2UucmVtb3ZlSXRlbSgic25zX3JlZnJlc2hUb2tlbiIgKyBhcHBsaWNhdGlvbkNvZGUpOwogICAgICBsb2NhbFN0b3JhZ2UucmVtb3ZlSXRlbSgic25zX2xvZ2luVXNlciIpOwogICAgICBsb2NhbFN0b3JhZ2UucmVtb3ZlSXRlbSgic25zX3Blcm1pc3Npb24iKTsKICAgICAgbG9jYWxTdG9yYWdlLnJlbW92ZUl0ZW0oInNuc19tb2R1bGVDb2RlcyIpOwogICAgICBsb2NhbFN0b3JhZ2UucmVtb3ZlSXRlbSgic25zX20yRmlsdGVyIik7CiAgICAgIGxvY2FsU3RvcmFnZS5yZW1vdmVJdGVtKCJzbnNfbTNGaWx0ZXIiKTsKICAgICAgbG9jYWxTdG9yYWdlLnJlbW92ZUl0ZW0oInNuc19tNEZpbHRlciIpOwogICAgICBsb2NhbFN0b3JhZ2UucmVtb3ZlSXRlbSgic25zX204RmlsdGVyIik7CiAgICAgIGxvY2FsU3RvcmFnZS5yZW1vdmVJdGVtKCJzbnNfbTlGaWx0ZXIiKTsgLy8gdGhpcy4kTm90aWNlLnN1Y2Nlc3MoewogICAgICAvLyAgIHRpdGxlOiB0aGlzLiR0KCdsYW5nLmNvbW1vbi5zdWNjZXNzZnVsJyksCiAgICAgIC8vICAgZGVzYzogdGhpcy4kdCgnbGFuZy5jb21tb24ubG9nb3V0JykgKyB0aGlzLiR0KCdsYW5nLmNvbW1vbi5zdWNjZXNzZnVsJyksCiAgICAgIC8vICAgZHVyYXRpb246IENvbmZpZy5TVUNDRVNTX0RVUkFUSU9OCiAgICAgIC8vIH0pCgogICAgICB0aGlzLiRzdG9yZS5zdGF0ZS5sb2dnZWRJbiA9IGZhbHNlOwogICAgICB0aGlzLiRyb3V0ZXIucHVzaCgiL3VzZXIvbG9naW4iKTsKICAgIH0sCiAgICBjaGFuZ2VQYXNzd29yZEhhbmRsZVN1Ym1pdDogZnVuY3Rpb24gY2hhbmdlUGFzc3dvcmRIYW5kbGVTdWJtaXQobmFtZSkgewogICAgICB2YXIgX3RoaXM1ID0gdGhpczsKCiAgICAgIHRoaXMuJHJlZnNbbmFtZV0udmFsaWRhdGUoZnVuY3Rpb24gKHZhbGlkKSB7CiAgICAgICAgaWYgKHZhbGlkKSB7CiAgICAgICAgICB2YXIgZGF0YSA9IHsKICAgICAgICAgICAgb2xkUGFzc3dvcmQ6IF90aGlzNS5mb3JtQ2hhbmdlUGFzc3dvcmQub2xkUGFzc3dkLAogICAgICAgICAgICBuZXdQYXNzd29yZDogX3RoaXM1LmZvcm1DaGFuZ2VQYXNzd29yZC5uZXdQYXNzd2QKICAgICAgICAgIH07CgogICAgICAgICAgX3RoaXM1LiRzZXJ2aWNlLnBhdGNoQ3VycmVudFVzZXJQYXNzd29yZC5zZW5kKGRhdGEsIG51bGwsIHsKICAgICAgICAgICAgcmVxdWVzdElEOiBfdGhpczUuY3JlYXRlUmVxdWVzdElEKCksCiAgICAgICAgICAgIHJlcXVlc3RGdW5jdGlvbjogIkNoYW5nZVB3ZCIsCiAgICAgICAgICAgIHJlcXVlc3RBY3Rpb246ICJVcGRhdGUiCiAgICAgICAgICB9KS50aGVuKGZ1bmN0aW9uIChkYXRhKSB7CiAgICAgICAgICAgIHNldFRpbWVvdXQoZnVuY3Rpb24gKCkgewogICAgICAgICAgICAgIF90aGlzNS5sb2FkaW5nID0gZmFsc2U7CgogICAgICAgICAgICAgIF90aGlzNS4kbmV4dFRpY2soZnVuY3Rpb24gKCkgewogICAgICAgICAgICAgICAgX3RoaXM1LmxvYWRpbmcgPSB0cnVlOwogICAgICAgICAgICAgIH0pOwogICAgICAgICAgICB9LCAyMDAwKTsKCiAgICAgICAgICAgIGlmIChkYXRhWzBdKSB7Ly8gZm9yIChsZXQgaSBpbiBkYXRhWzBdLmVycm9ycykgewogICAgICAgICAgICAgIC8vIHN3aXRjaCAoZGF0YVswXS5lcnJvcnNbaV0uZXJyb3IpIHsKICAgICAgICAgICAgICAvLyAgICAgY2FzZSAncGFzc3dvcmROb3RNYXRjaCc6CiAgICAgICAgICAgICAgLy8gICAgIHRoaXMuZXJyb3JUeXBlID0gMQogICAgICAgICAgICAgIC8vICAgICB0aGlzLiRyZWZzLmZvcm1DaGFuZ2VQYXNzd29yZC52YWxpZGF0ZUZpZWxkKCdvbGRQYXNzd2QnKQogICAgICAgICAgICAgIC8vICAgICBicmVhawogICAgICAgICAgICAgIC8vICAgICBjYXNlICdiYWRQYXNzd29yZCc6CiAgICAgICAgICAgICAgLy8gICAgIHRoaXMuZXJyb3JUeXBlID0gMgogICAgICAgICAgICAgIC8vICAgICB0aGlzLiRyZWZzLmZvcm1DaGFuZ2VQYXNzd29yZC52YWxpZGF0ZUZpZWxkKCduZXdQYXNzd2QnKQogICAgICAgICAgICAgIC8vICAgICBicmVhawogICAgICAgICAgICAgIC8vICAgICBjYXNlICdzYW1lUGFzc3dvcmQnOgogICAgICAgICAgICAgIC8vICAgICB0aGlzLmVycm9yVHlwZSA9IDMKICAgICAgICAgICAgICAvLyAgICAgdGhpcy4kcmVmcy5mb3JtQ2hhbmdlUGFzc3dvcmQudmFsaWRhdGVGaWVsZCgnbmV3UGFzc3dkJykKICAgICAgICAgICAgICAvLyAgICAgYnJlYWsKICAgICAgICAgICAgICAvLyAgICAgZGVmYXVsdDoKICAgICAgICAgICAgICAvLyAgICAgbGV0IF90aGlzID0gdGhpcwogICAgICAgICAgICAgIC8vICAgICB0aGlzLiROb3RpY2UuZXJyb3IoewogICAgICAgICAgICAgIC8vICAgICAgICAgdGl0bGU6IF90aGlzLiR0KCdMb2NhbGVTdHJpbmcuRTAwMDM3JyksCiAgICAgICAgICAgICAgLy8gICAgICAgICBkZXNjOiBfdGhpcy4kdCgnbGFuZy5jb21tb24uY29udGFjdFN5c3RlbUFkbWluaXN0cmF0b3InKSwKICAgICAgICAgICAgICAvLyAgICAgICAgIGR1cmF0aW9uOiBDb25maWcuZXJyb3JEdXJhdGlvbgogICAgICAgICAgICAgIC8vICAgICB9KQogICAgICAgICAgICAgIC8vICAgICBicmVhawogICAgICAgICAgICAgIC8vIH0KICAgICAgICAgICAgICAvLyB9CiAgICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgICAgLy8gdGhpcy5jaGFuZ2VQYXNzd29yZE1vZGFsID0gZmFsc2UKICAgICAgICAgICAgICB2YXIgX3RoaXMgPSBfdGhpczU7IC8vIHRoaXMuJHN0b3JlLmRpc3BhdGNoKCdkZWxUb2tlbicpCiAgICAgICAgICAgICAgLy8gbG9jYWxTdG9yYWdlLnJlbW92ZUl0ZW0oJ21vZHVsZUNvZGVzJykKCiAgICAgICAgICAgICAgX3RoaXM1LiROb3RpY2Uuc3VjY2Vzcyh7CiAgICAgICAgICAgICAgICB0aXRsZTogX3RoaXM1LiR0KCJMb2NhbGVTdHJpbmcuTTAwMDA0IiksCiAgICAgICAgICAgICAgICBkZXNjOiBfdGhpcy4kdCgiTG9jYWxlU3RyaW5nLk0wMDAwNSIpLAogICAgICAgICAgICAgICAgZHVyYXRpb246IENvbmZpZy5zdWNjZXNzRHVyYXRpb24KICAgICAgICAgICAgICB9KTsgLy8gdGhpcy4kcmVmc1tuYW1lXS5yZXNldEZpZWxkcygpCgoKICAgICAgICAgICAgICBfdGhpczUubG9nb3V0KCk7CiAgICAgICAgICAgIH0KICAgICAgICAgIH0pOwogICAgICAgIH0KICAgICAgfSk7CiAgICB9LAogICAgLy8g6K+t6KiA5YiH5o2iCiAgICBjaGFuZ2VMYW5nOiBmdW5jdGlvbiBjaGFuZ2VMYW5nKGRhdGEpIHsKICAgICAgLy8gdGhpcy4kaTE4bi5sb2NhbGUgPSBkYXRhOwogICAgICBsb2NhbFN0b3JhZ2Uuc2V0SXRlbSgic25zX2xhbmciLCBkYXRhKTsKICAgICAgdGhpcy4kc3RvcmUuY29tbWl0KCJzZXRMYW5nQ2hhbmdlIiwgZGF0YSk7CgogICAgICBpZiAobG9jYWxTdG9yYWdlLmdldEl0ZW0oInNuc19zeXN0ZW1UaXRsZSIpICE9IG51bGwpIHsKICAgICAgICBkb2N1bWVudC50aXRsZSA9IGxvY2FsU3RvcmFnZS5nZXRJdGVtKCJzbnNfc3lzdGVtVGl0bGUiKTsKICAgICAgfSBlbHNlIHsKICAgICAgICBkb2N1bWVudC50aXRsZSA9IHRoaXMuJHQoIkxvY2FsZVN0cmluZy5TMDAwMDIiKTsKICAgICAgfQoKICAgICAgdGhpcy5yb3V0aW5nQ2hhbmdlKCk7CiAgICAgIGxvY2F0aW9uLnJlbG9hZCgpOwogICAgfSwKICAgIGdldEFwcHNFZGl0YWJsZTogZnVuY3Rpb24gZ2V0QXBwc0VkaXRhYmxlKHZhbCkgewogICAgICB2YXIgX3RoaXM2ID0gdGhpczsKCiAgICAgIHZhciBsb2dpblVzZXJOYW1lID0gbG9jYWxTdG9yYWdlLmdldEl0ZW0oInNuc19sb2dpblVzZXIiKTsKICAgICAgdmFyIGFwcEVkaXRBY2NvdW50S2V5ID0gImFwcHNFZGl0LSIgKyBsb2dpblVzZXJOYW1lOwogICAgICBDb21tb25TZXJ2aWNlLmdldFBPQ1Byb3BlcnRpZXMoInBvY0NvbmZpZyIsIGFwcEVkaXRBY2NvdW50S2V5LCBmdW5jdGlvbiAodmFsdWUpIHsKICAgICAgICBpZiAodmFsdWUgPT09ICIiKSB7CiAgICAgICAgICBfdGhpczYuYXBwc0VkaXQgPSB0cnVlOwogICAgICAgIH0gZWxzZSBpZiAodmFsdWUgPT09ICJ0cnVlIikgewogICAgICAgICAgX3RoaXM2LmFwcHNFZGl0ID0gdHJ1ZTsKICAgICAgICB9IGVsc2UgewogICAgICAgICAgX3RoaXM2LmFwcHNFZGl0ID0gZmFsc2U7CiAgICAgICAgfQogICAgICB9KTsKICAgIH0sCiAgICBvbkFib3V0OiBmdW5jdGlvbiBvbkFib3V0KCkgewogICAgICB0aGlzLnNob3dBYm91dCA9IHRydWU7CiAgICB9LAogICAgY2xvc2VBYm91dDogZnVuY3Rpb24gY2xvc2VBYm91dCgpIHsKICAgICAgdGhpcy5zaG93QWJvdXQgPSBmYWxzZTsKICAgIH0sCiAgICBvblN5c3RlbUNvbmZpZzogZnVuY3Rpb24gb25TeXN0ZW1Db25maWcoKSB7CiAgICAgIHRoaXMuc2hvd1N5c3RlbUNvbmZpZyA9IHRydWU7CiAgICB9LAogICAgb25LTUNvbmZpZzogZnVuY3Rpb24gb25LTUNvbmZpZygpIHsKICAgICAgdGhpcy5zaG93S01Db25maWcgPSB0cnVlOwogICAgfSwKICAgIG9uUmVzZXRQYXNzd29yZDogZnVuY3Rpb24gb25SZXNldFBhc3N3b3JkKCkgewogICAgICB0aGlzLmlzU2hvd0VkaXRQYXNzd29yZE1vZGFsID0gdHJ1ZTsKICAgIH0sCiAgICBjbG9zZUNoYW5nZVBhc3N3b3JkOiBmdW5jdGlvbiBjbG9zZUNoYW5nZVBhc3N3b3JkKCkgewogICAgICB0aGlzLmlzU2hvd0VkaXRQYXNzd29yZE1vZGFsID0gZmFsc2U7CiAgICB9LAogICAgY2xvc2VTeXN0ZW1Db25maWc6IGZ1bmN0aW9uIGNsb3NlU3lzdGVtQ29uZmlnKCkgewogICAgICB0aGlzLnNob3dTeXN0ZW1Db25maWcgPSBmYWxzZTsKICAgICAgdGhpcy5nZXRMb2dvKCk7CiAgICB9LAogICAgY2xvc2VLTUNvbmZpZzogZnVuY3Rpb24gY2xvc2VLTUNvbmZpZygpIHsKICAgICAgdGhpcy5zaG93S01Db25maWcgPSBmYWxzZTsKICAgIH0sCiAgICBvbkFwcFRhYjogZnVuY3Rpb24gb25BcHBUYWIodmFsKSB7CiAgICAgIHZhciBfdGhpczcgPSB0aGlzOwoKICAgICAgdGhpcy5zZWxBcHBUYWIgPSB2YWw7CiAgICAgIHRoaXMuJHN0b3JlLmNvbW1pdCgic2V0QXBwVGFiIiwgIiIpOwogICAgICB0aGlzLiRuZXh0VGljayhmdW5jdGlvbiAoKSB7CiAgICAgICAgX3RoaXM3LiRzdG9yZS5jb21taXQoInNldEFwcFRhYiIsIF90aGlzNy5zZWxBcHBUYWIpOwogICAgICB9KTsKICAgIH0sCiAgICByb3V0aW5nQ2hhbmdlOiBmdW5jdGlvbiByb3V0aW5nQ2hhbmdlKCkgewogICAgICBjb25zb2xlLmxvZygidGhpcy5jdXJyZW50Um91dGVOYW1lOiIgKyB0aGlzLmN1cnJlbnRSb3V0ZU5hbWUpOwoKICAgICAgaWYgKHRoaXMuY3VycmVudFJvdXRlTmFtZSAmJiB0aGlzLmN1cnJlbnRSb3V0ZU5hbWUgIT09ICJkYXNoQm9hcmQiKSB7Ly90aGlzLnNlbEFwcFRhYj0nTW9uaXRvcicKICAgICAgICAvL3RoaXMuJHN0b3JlLmNvbW1pdCgnc2V0QXBwVGFiJywgdGhpcy5zZWxBcHBUYWIpCiAgICAgIH0gZWxzZSB7CiAgICAgICAgdGhpcy5zZWxBcHBUYWIgPSAiTW9uaXRvciI7CiAgICAgIH0KCiAgICAgIHRoaXMuc3ViQ29udHJvbCA9ICIiOwoKICAgICAgc3dpdGNoICh0aGlzLmN1cnJlbnRSb3V0ZU5hbWUpIHsKICAgICAgICBjYXNlICJhcHBzU05TTTEiOgogICAgICAgICAgdGhpcy5zdWJDb250cm9sID0gdGhpcy4kdCgiTG9jYWxlU3RyaW5nLkYzMDAwMSIpOwogICAgICAgICAgYnJlYWs7CgogICAgICAgIGNhc2UgImFwcHNTTlNNMiI6CiAgICAgICAgICB0aGlzLnN1YkNvbnRyb2wgPSB0aGlzLiR0KCJMb2NhbGVTdHJpbmcuRjMwMDAyIik7CiAgICAgICAgICBicmVhazsKCiAgICAgICAgY2FzZSAiYXBwc1NOU00zIjoKICAgICAgICAgIHRoaXMuc3ViQ29udHJvbCA9IHRoaXMuJHQoIkxvY2FsZVN0cmluZy5GMzAwMDMiKTsKICAgICAgICAgIGJyZWFrOwoKICAgICAgICBjYXNlICJhcHBzU05TTTQiOgogICAgICAgICAgdGhpcy5zdWJDb250cm9sID0gdGhpcy4kdCgiTG9jYWxlU3RyaW5nLkYzMDAwNCIpOwogICAgICAgICAgYnJlYWs7CgogICAgICAgIGNhc2UgImFwcHNTTlNNNSI6CiAgICAgICAgICB0aGlzLnN1YkNvbnRyb2wgPSB0aGlzLiR0KCJMb2NhbGVTdHJpbmcuRjMwMDA1Iik7CiAgICAgICAgICBicmVhazsKCiAgICAgICAgY2FzZSAiYXBwc1NOU002IjoKICAgICAgICAgIHRoaXMuc3ViQ29udHJvbCA9IHRoaXMuJHQoIkxvY2FsZVN0cmluZy5GMzAwMDYiKTsKICAgICAgICAgIGJyZWFrOwoKICAgICAgICBjYXNlICJhcHBzU05TTTciOgogICAgICAgICAgdGhpcy5zdWJDb250cm9sID0gIui7jOi3oeebo+aOpyI7CiAgICAgICAgICBicmVhazsKCiAgICAgICAgY2FzZSAiYXBwc1NOU004IjoKICAgICAgICAgIHRoaXMuc3ViQ29udHJvbCA9IHRoaXMuJHQoIkxvY2FsZVN0cmluZy5GMzAwMTYiKTsKICAgICAgICAgIGJyZWFrOwoKICAgICAgICBjYXNlICJhcHBzU05TTTkiOgogICAgICAgICAgdGhpcy5zdWJDb250cm9sID0gdGhpcy4kdCgiTG9jYWxlU3RyaW5nLkYzMDAxNSIpOwogICAgICAgICAgYnJlYWs7CgogICAgICAgIGNhc2UgImFwcHNTTlNQbGFuZVNldHRpbmciOgogICAgICAgICAgdGhpcy5zdWJDb250cm9sID0gdGhpcy4kdCgiTG9jYWxlU3RyaW5nLkYzMDAxMCIpOwogICAgICAgICAgYnJlYWs7CgogICAgICAgIGNhc2UgImFwcHNTTlNPYmplY3RTZXR0aW5nIjoKICAgICAgICAgIHRoaXMuc3ViQ29udHJvbCA9IHRoaXMuJHQoIkxvY2FsZVN0cmluZy5GMDAwMTgiKTsKICAgICAgICAgIGJyZWFrOwoKICAgICAgICBjYXNlICJhcHBzU05TVGhpcmRQYXJ0eVNldHRpbmciOgogICAgICAgICAgdGhpcy5zdWJDb250cm9sID0gdGhpcy4kdCgiTG9jYWxlU3RyaW5nLkYzMDAxMiIpOwogICAgICAgICAgYnJlYWs7CgogICAgICAgIGNhc2UgImFwcHNTTlNBY2Nlc3NMb2dnaW5nIjoKICAgICAgICAgIHRoaXMuc3ViQ29udHJvbCA9IHRoaXMuJHQoIkxvY2FsZVN0cmluZy5GMzAwMTciKTsKICAgICAgICAgIGJyZWFrOwoKICAgICAgICBkZWZhdWx0OgogICAgICAgICAgdGhpcy5zdWJDb250cm9sID0gdGhpcy4kdCgiTG9jYWxlU3RyaW5nLkYzMDAwMSIpOwogICAgICAgICAgYnJlYWs7CiAgICAgIH0KICAgIH0sCiAgICBnb0hvbWU6IGZ1bmN0aW9uIGdvSG9tZSgpIHsKICAgICAgdmFyIHRhYkl0ZW0gPSBudWxsOyAvLyBpZih0aGlzLmN1cnJlbnRSb3V0ZU5hbWUgJiYgdGhpcy5jdXJyZW50Um91dGVOYW1lIT09J2Rhc2hCb2FyZCcpIHsKCiAgICAgIHRhYkl0ZW0gPSBsb2NhbFN0b3JhZ2UuZ2V0SXRlbSgic25zX2RlZmF1bHRUYWIiKTsKCiAgICAgIGlmICghdGFiSXRlbSkgewogICAgICAgIHRhYkl0ZW0gPSAibTEiOwogICAgICB9CgogICAgICB0aGlzLiRyb3V0ZXIucHVzaCh7CiAgICAgICAgcGF0aDogZGVjb2RlVVJJQ29tcG9uZW50KCIvYWRtaW5pc3RyYXRpdmUvYXBwcy9zbnMvIiArIHRhYkl0ZW0pIC8vIOWwjumggeiHs+mgkOioremggemdogoKICAgICAgfSk7IC8vIH0KICAgICAgLy8gaWYgKHRoaXMuY3VycmVudFJvdXRlTmFtZSAmJiB0aGlzLmN1cnJlbnRSb3V0ZU5hbWUgIT09ICJhcHBzU05TTTEiKSB7CiAgICAgIC8vICAgdGhpcy4kcm91dGVyLnB1c2goeyBuYW1lOiAiYXBwc1NOU00xIiB9KTsKICAgICAgLy8gfSBlbHNlIHsKICAgICAgLy8gICBsb2NhdGlvbi5yZWxvYWQoKTsKICAgICAgLy8gfQogICAgfSwKICAgIGhhbmRsZUNvbGxwYXNlZENoYW5nZTogZnVuY3Rpb24gaGFuZGxlQ29sbHBhc2VkQ2hhbmdlKHN0YXRlKSB7CiAgICAgIHRoaXMuJGVtaXQoIm9uLWNvbGwtY2hhbmdlIiwgc3RhdGUpOwogICAgfSwKICAgIG9uQ2hhbmdlU2lkZXI6IGZ1bmN0aW9uIG9uQ2hhbmdlU2lkZXIoKSB7CiAgICAgIHRoaXMuJGVtaXQoIm9uQ2hhbmdlU2lkZXIiLCB0aGlzLmlzT3Blbik7CiAgICB9LAogICAgb25QZXJzb25EYXRhOiBmdW5jdGlvbiBvblBlcnNvbkRhdGEoKSB7CiAgICAgIHRoaXMuJHJvdXRlci5wdXNoKHsKICAgICAgICBwYXRoOiAiL2FkbWluaXN0cmF0aXZlL3VzZXJBY2NvdW50IgogICAgICB9KTsKICAgIH0sCiAgICBvbkNoYW5nZVBhc3N3b3JkOiBmdW5jdGlvbiBvbkNoYW5nZVBhc3N3b3JkKCkgewogICAgICB0aGlzLiRyb3V0ZXIucHVzaCh7CiAgICAgICAgcGF0aDogIi9hZG1pbmlzdHJhdGl2ZS91c2VyQWNjb3VudCIsCiAgICAgICAgcXVlcnk6IHsKICAgICAgICAgIGFjdGlvbjogInBhc3N3b3JkIgogICAgICAgIH0KICAgICAgfSk7CiAgICB9LAogICAgb25Mb2dvdXQ6IGZ1bmN0aW9uIG9uTG9nb3V0KCkgewogICAgICB2YXIgYXBwbGljYXRpb25Db2RlID0gbG9jYWxTdG9yYWdlLmdldEl0ZW0oInNuc19hcHBsaWNhdGlvbkNvZGUiKSB8fCAiIjsKICAgICAgbG9jYWxTdG9yYWdlLnJlbW92ZUl0ZW0oInNuc19hY2Nlc3NUb2tlbiIgKyBhcHBsaWNhdGlvbkNvZGUpOwogICAgICBsb2NhbFN0b3JhZ2UucmVtb3ZlSXRlbSgic25zX3JlZnJlc2hUb2tlbiIgKyBhcHBsaWNhdGlvbkNvZGUpOwogICAgICBsb2NhbFN0b3JhZ2UucmVtb3ZlSXRlbSgic25zX2xvZ2luVXNlciIpOwogICAgICBsb2NhbFN0b3JhZ2UucmVtb3ZlSXRlbSgic25zX3Blcm1pc3Npb24iKTsKICAgICAgbG9jYWxTdG9yYWdlLnJlbW92ZUl0ZW0oInNuc19tb2R1bGVDb2RlcyIpOwogICAgICBsb2NhbFN0b3JhZ2UucmVtb3ZlSXRlbSgic25zX2lkZW50aXR5Iik7CiAgICAgIGxvY2FsU3RvcmFnZS5yZW1vdmVJdGVtKCJzbnNfbTJGaWx0ZXIiKTsKICAgICAgbG9jYWxTdG9yYWdlLnJlbW92ZUl0ZW0oInNuc19tM0ZpbHRlciIpOwogICAgICBsb2NhbFN0b3JhZ2UucmVtb3ZlSXRlbSgic25zX200RmlsdGVyIik7CiAgICAgIGxvY2FsU3RvcmFnZS5yZW1vdmVJdGVtKCJzbnNfbThGaWx0ZXIiKTsKICAgICAgbG9jYWxTdG9yYWdlLnJlbW92ZUl0ZW0oInNuc19tOUZpbHRlciIpOyAvLyB0aGlzLiROb3RpY2Uuc3VjY2Vzcyh7CiAgICAgIC8vICAgdGl0bGU6IHRoaXMuJHQoJ2xhbmcuY29tbW9uLnN1Y2Nlc3NmdWwnKSwKICAgICAgLy8gICBkZXNjOiB0aGlzLiR0KCdsYW5nLmNvbW1vbi5sb2dvdXQnKSArIHRoaXMuJHQoJ2xhbmcuY29tbW9uLnN1Y2Nlc3NmdWwnKSwKICAgICAgLy8gICBkdXJhdGlvbjogQ29uZmlnLlNVQ0NFU1NfRFVSQVRJT04KICAgICAgLy8gfSkKCiAgICAgIHRoaXMuJHJvdXRlci5wdXNoKCIvdXNlci9sb2dpbiIpOwogICAgfQogIH0KfTs="}, {"version": 3, "sources": ["AdminHeader.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6NA;AACA,OAAA,OAAA,MAAA,4CAAA,C,CACA;;AACA,OAAA,KAAA,MAAA,wDAAA;AACA,OAAA,YAAA,MAAA,+DAAA;AACA,OAAA,QAAA,MAAA,2DAAA;AAEA,OAAA,MAAA,MAAA,iBAAA,C,CACA;AACA;AACA;;AACA,OAAA,aAAA,MAAA,gDAAA;AAEA,SAAA,UAAA,QAAA,MAAA;AAEA,SAAA,MAAA,IAAA,IAAA,QAAA,iBAAA;AACA,SAAA,MAAA,IAAA,IAAA,QAAA,iBAAA;AACA,SAAA,MAAA,IAAA,IAAA,QAAA,iBAAA;AAEA,eAAA;AACA,EAAA,IAAA,EAAA,aADA;AAEA,EAAA,KAAA,EAAA,CAAA,WAAA,EAAA,kBAAA,EAAA,QAAA,CAFA;AAGA,EAAA,IAHA,kBAGA;AAAA;;AACA,QAAA,eAAA,GAAA,SAAA,eAAA,CAAA,IAAA,EAAA,KAAA,EAAA,QAAA,EAAA;AACA,UAAA,KAAA,KAAA,EAAA,EAAA;AACA,QAAA,QAAA,CACA,IAAA,KAAA,CACA,MAAA,CAAA,EAAA,CAAA,qBAAA,EAAA;AACA,aAAA,MAAA,CAAA,EAAA,CAAA,qBAAA;AADA,SAAA,CADA,CADA,CAAA;AAOA,OARA,MAQA,IAAA,MAAA,CAAA,SAAA,KAAA,CAAA,EAAA;AACA,QAAA,QAAA,CAAA,IAAA,KAAA,CAAA,MAAA,CAAA,EAAA,CAAA,qBAAA,CAAA,CAAA,CAAA;AACA,OAFA,MAEA;AACA,QAAA,QAAA;AACA;;AACA,MAAA,MAAA,CAAA,SAAA,GAAA,CAAA;AACA,KAfA;;AAgBA,QAAA,eAAA,GAAA,SAAA,eAAA,CAAA,IAAA,EAAA,KAAA,EAAA,QAAA,EAAA;AACA,UAAA,GAAA,GAAA,kHAAA;;AACA,UAAA,KAAA,KAAA,EAAA,EAAA;AACA,QAAA,QAAA,CACA,IAAA,KAAA,CACA,MAAA,CAAA,EAAA,CAAA,qBAAA,EAAA;AACA,aAAA,MAAA,CAAA,EAAA,CAAA,qBAAA;AADA,SAAA,CADA,CADA,CAAA;AAOA,OARA,MAQA,IAAA,CAAA,GAAA,CAAA,IAAA,CAAA,KAAA,CAAA,EAAA;AACA,QAAA,QAAA,CAAA,IAAA,KAAA,CAAA,MAAA,CAAA,EAAA,CAAA,qBAAA,CAAA,CAAA,CAAA;AACA,OAFA,MAEA,IAAA,MAAA,CAAA,SAAA,KAAA,CAAA,EAAA;AACA,QAAA,QAAA,CAAA,IAAA,KAAA,CAAA,MAAA,CAAA,EAAA,CAAA,qBAAA,CAAA,CAAA,CAAA;AACA,OAFA,MAEA,IACA,MAAA,CAAA,kBAAA,CAAA,SAAA,KAAA,MAAA,CAAA,kBAAA,CAAA,SADA,EAEA;AACA,QAAA,QAAA,CAAA,IAAA,KAAA,CAAA,MAAA,CAAA,EAAA,CAAA,qBAAA,CAAA,CAAA,CAAA;AACA,OAJA,MAIA;AACA,YAAA,MAAA,CAAA,kBAAA,CAAA,cAAA,KAAA,EAAA,EAAA;AACA;AACA,UAAA,MAAA,CAAA,KAAA,CAAA,kBAAA,CAAA,aAAA,CAAA,gBAAA;AACA;;AACA,QAAA,QAAA;AACA;;AACA,MAAA,MAAA,CAAA,SAAA,GAAA,CAAA;AACA,KA1BA;;AA2BA,QAAA,oBAAA,GAAA,SAAA,oBAAA,CAAA,IAAA,EAAA,KAAA,EAAA,QAAA,EAAA;AACA,UAAA,KAAA,KAAA,EAAA,EAAA;AACA,QAAA,QAAA,CACA,IAAA,KAAA,CACA,MAAA,CAAA,EAAA,CAAA,qBAAA,EAAA;AACA,aAAA,MAAA,CAAA,EAAA,CAAA,qBAAA;AADA,SAAA,CADA,CADA,CAAA;AAOA,OARA,MAQA,IAAA,KAAA,KAAA,MAAA,CAAA,kBAAA,CAAA,SAAA,EAAA;AACA,QAAA,QAAA,CAAA,IAAA,KAAA,CAAA,MAAA,CAAA,EAAA,CAAA,qBAAA,CAAA,CAAA,CAAA;AACA,OAFA,MAEA;AACA,QAAA,QAAA;AACA;AACA,KAdA;;AAgBA,WAAA;AACA,MAAA,OAAA,EAAA,OADA;AAEA,MAAA,OAAA,EAAA,IAFA;AAGA,MAAA,QAAA,EAAA,EAHA;AAIA,MAAA,UAAA,EAAA,EAJA;AAKA,MAAA,kBAAA,EAAA;AACA,QAAA,SAAA,EAAA,EADA;AAEA,QAAA,SAAA,EAAA,EAFA;AAGA,QAAA,cAAA,EAAA;AAHA,OALA;AAUA,MAAA,UAAA,EAAA,MAAA,CAAA,WAVA;AAWA,MAAA,QAAA,EAAA,YAAA,CAAA,OAAA,CAAA,eAAA,CAXA;AAYA,MAAA,UAAA,EAAA,YAAA,CAAA,OAAA,CAAA,UAAA,CAZA;AAaA,MAAA,WAAA,EAAA,EAbA;AAcA,MAAA,SAAA,EAAA,SAdA;AAeA,MAAA,uBAAA,EAAA,KAfA;AAgBA,MAAA,OAAA,EAAA,IAhBA;AAiBA,MAAA,aAAA,EAAA;AACA,QAAA,QAAA,EAAA;AADA,OAjBA;AAoBA,MAAA,SAAA,EAAA,KApBA;AAqBA,MAAA,gBAAA,EAAA,KArBA;AAsBA,MAAA,YAAA,EAAA,KAtBA;AAuBA,MAAA,QAAA,EAAA,IAvBA;AAwBA,MAAA,kBAAA,EAAA;AACA,QAAA,SAAA,EAAA,CACA;AACA,UAAA,SAAA,EAAA,eADA;AAEA,UAAA,OAAA,EAAA;AAFA,SADA,CADA;AAOA,QAAA,SAAA,EAAA,CACA;AACA,UAAA,SAAA,EAAA,eADA;AAEA,UAAA,OAAA,EAAA;AAFA,SADA,CAPA;AAaA,QAAA,cAAA,EAAA,CACA;AACA,UAAA,SAAA,EAAA,oBADA;AAEA,UAAA,OAAA,EAAA;AAFA,SADA;AAbA,OAxBA;AA4CA,MAAA,OAAA,EAAA,YAAA,CAAA,OAAA,CAAA,iBAAA;AA5CA,KAAA;AA8CA,GA7GA;AA8GA,EAAA,UAAA,EAAA;AACA;AACA;AACA,IAAA,KAAA,EAAA,KAHA;AAIA,IAAA,YAAA,EAAA,YAJA;AAKA,IAAA,QAAA,EAAA;AALA,GA9GA;AAsHA,EAAA,QAAA,kCACA,UAAA,CAAA,CACA,gBADA,EAEA,kBAFA,EAGA,QAHA,EAIA,cAJA,CAAA,CADA;AAOA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAA,gBAvBA,8BAuBA;AACA,aAAA,KAAA,MAAA,CAAA,IAAA;AACA;AACA;;;;;;AA1BA,IAtHA;AAuJA,EAAA,KAAA,EAAA;AACA,IAAA,MAAA,EAAA,eADA;AAEA,IAAA,cAFA,0BAEA,GAFA,EAEA;AACA;AACA,WAAA,WAAA,GAAA,GAAA;AACA,KALA;AAMA,IAAA,gBANA,4BAMA,GANA,EAMA;AACA,MAAA,OAAA,CAAA,GAAA,CAAA,kBAAA;AACA,MAAA,OAAA,CAAA,GAAA,CAAA,GAAA;AACA,WAAA,eAAA;AACA,WAAA,OAAA;AACA,KAXA;AAYA,IAAA,MAZA,kBAYA,GAZA,EAYA;AACA,cAAA,GAAA;AACA,aAAA,cAAA;AACA,eAAA,UAAA,GAAA,SAAA;AACA;;AACA,aAAA,eAAA;AACA,eAAA,UAAA,GAAA,SAAA;AACA;;AACA,aAAA,mBAAA;AACA,eAAA,UAAA,GAAA,QAAA,KAAA,EAAA,CAAA,qBAAA,CAAA;AACA;;AACA;AACA,eAAA,UAAA,GAAA,EAAA;AACA;AAZA;AAcA,KA3BA;AA4BA,IAAA,UA5BA,wBA4BA;AACA,WAAA,UAAA;AACA,KA9BA;AA+BA,IAAA,YA/BA,0BA+BA;AACA,WAAA,MAAA;AACA;AAjCA,GAvJA;AA0LA,EAAA,OA1LA,qBA0LA;AACA,SAAA,UAAA;;AACA,QAAA,YAAA,CAAA,OAAA,CAAA,iBAAA,KAAA,IAAA,EAAA;AACA,MAAA,QAAA,CAAA,KAAA,GAAA,YAAA,CAAA,OAAA,CAAA,iBAAA,CAAA;AACA,KAFA,MAEA;AACA,MAAA,QAAA,CAAA,KAAA,GAAA,KAAA,EAAA,CAAA,qBAAA,CAAA;AACA;;AACA,SAAA,OAAA,GAAA,YAAA,CAAA,OAAA,CAAA,aAAA,IACA,YAAA,CAAA,OAAA,CAAA,aAAA,CADA,GAEA,IAFA;AAGA,SAAA,OAAA;AACA,GArMA;AAsMA,EAAA,OAtMA,qBAsMA;AACA,SAAA,eAAA;AACA,SAAA,aAAA;AACA,GAzMA;AA0MA,EAAA,OAAA,EAAA;AACA,IAAA,OADA,qBACA;AAAA;;AACA,UAAA,cAAA,GAAA;AACA,QAAA,WAAA,EAAA,IADA;AAEA,QAAA,MAAA,EAAA;AAFA,OAAA;AAIA,WAAA,QAAA,CAAA,sBAAA,CAAA,IAAA,CAAA,cAAA,EAAA,IAAA,CAAA,UAAA,MAAA,EAAA;AACA,YACA,MAAA,CAAA,KAAA,GAAA,CAAA,IACA,MAAA,CAAA,OAAA,CAAA,CAAA,EAAA,KADA,IAEA,MAAA,CAAA,OAAA,CAAA,CAAA,EAAA,KAAA,CAAA,MAAA,GAAA,CAHA,EAIA;AACA,UAAA,MAAA,CAAA,OAAA,GAAA,MAAA,CAAA,OAAA,CAAA,CAAA,EAAA,KAAA,CAAA,CAAA,EAAA,GAAA;AACA,UAAA,YAAA,CAAA,OAAA,CAAA,aAAA,EAAA,MAAA,CAAA,OAAA;AACA,SAPA,MAOA;AACA,UAAA,MAAA,CAAA,OAAA,GAAA,IAAA;AACA;AACA,OAXA;AAYA,KAlBA;AAmBA,IAAA,UAnBA,wBAmBA;AAAA;;AACA,MAAA,OAAA,CAAA,GAAA,CAAA,UAAA,KAAA,QAAA;;AACA,UAAA,KAAA,GAAA,IAAA;;AAEA,UAAA,GAAA,GAAA,EAAA;AACA,UAAA,MAAA,GAAA;AACA,QAAA,MAAA,EAAA,GADA;AAEA,QAAA,WAAA,EAAA;AAFA,OAAA,CALA,CASA;;AACA,WAAA,QAAA,CAAA,UAAA,CAAA,IAAA,CAAA,MAAA,EAAA,IAAA,CAAA,UAAA,IAAA,EAAA;AACA,QAAA,KAAA,CAAA,OAAA,GAAA,IAAA,CAAA,OAAA,CADA,CAEA;;AACA,QAAA,KAAA,CAAA,QAAA,CAAA,MAAA,CAAA,CAAA;;AACA,YAAA,GAAA,GAAA,EAAA;AACA,QAAA,IAAA,CAAA,OAAA,CAAA,OAAA,CAAA,UAAA,OAAA,EAAA;AACA,UAAA,GAAA,GAAA;AACA,YAAA,KAAA,EAAA,OAAA,CAAA,MADA;AAEA,YAAA,KAAA,EAAA,MAAA,CAAA,EAAA,CAAA,kBAAA,OAAA,CAAA,QAAA;AAFA,WAAA;;AAIA,UAAA,KAAA,CAAA,QAAA,CAAA,IAAA,CAAA,GAAA;AACA,SANA;AAQA,QAAA,OAAA,CAAA,GAAA,CAAA,SAAA,IAAA,CAAA,SAAA,CAAA,KAAA,CAAA,QAAA,CAAA;AACA,OAdA;AAeA,KA5CA;AA6CA;AACA;AACA;AACA,IAAA,UAhDA,wBAgDA;AACA,WAAA,uBAAA,GAAA,KAAA;AACA,WAAA,kBAAA,GAAA;AACA,QAAA,SAAA,EAAA,EADA;AAEA,QAAA,SAAA,EAAA,EAFA;AAGA,QAAA,cAAA,EAAA;AAHA,OAAA;AAKA,KAvDA;AAwDA,IAAA,WAxDA,uBAwDA,GAxDA,EAwDA;AACA,cAAA,GAAA;AACA,aAAA,QAAA;AACA,eAAA,MAAA;AACA;;AACA,aAAA,uBAAA;AACA,eAAA,uBAAA,GAAA,IAAA;AACA;AANA;AAQA,KAjEA;AAkEA,IAAA,MAlEA,oBAkEA;AACA,UAAA,eAAA,GAAA,YAAA,CAAA,OAAA,CAAA,qBAAA,KAAA,EAAA;AACA,MAAA,YAAA,CAAA,UAAA,CAAA,oBAAA,eAAA;AACA,MAAA,YAAA,CAAA,UAAA,CAAA,qBAAA,eAAA;AAEA,MAAA,YAAA,CAAA,UAAA,CAAA,eAAA;AACA,MAAA,YAAA,CAAA,UAAA,CAAA,gBAAA;AACA,MAAA,YAAA,CAAA,UAAA,CAAA,iBAAA;AACA,MAAA,YAAA,CAAA,UAAA,CAAA,cAAA;AACA,MAAA,YAAA,CAAA,UAAA,CAAA,cAAA;AACA,MAAA,YAAA,CAAA,UAAA,CAAA,cAAA;AACA,MAAA,YAAA,CAAA,UAAA,CAAA,cAAA;AACA,MAAA,YAAA,CAAA,UAAA,CAAA,cAAA,EAZA,CAcA;AACA;AACA;AACA;AACA;;AACA,WAAA,MAAA,CAAA,KAAA,CAAA,QAAA,GAAA,KAAA;AACA,WAAA,OAAA,CAAA,IAAA,CAAA,aAAA;AACA,KAvFA;AAwFA,IAAA,0BAxFA,sCAwFA,IAxFA,EAwFA;AAAA;;AACA,WAAA,KAAA,CAAA,IAAA,EAAA,QAAA,CAAA,UAAA,KAAA,EAAA;AACA,YAAA,KAAA,EAAA;AACA,cAAA,IAAA,GAAA;AACA,YAAA,WAAA,EAAA,MAAA,CAAA,kBAAA,CAAA,SADA;AAEA,YAAA,WAAA,EAAA,MAAA,CAAA,kBAAA,CAAA;AAFA,WAAA;;AAIA,UAAA,MAAA,CAAA,QAAA,CAAA,wBAAA,CACA,IADA,CACA,IADA,EACA,IADA,EACA;AACA,YAAA,SAAA,EAAA,MAAA,CAAA,eAAA,EADA;AAEA,YAAA,eAAA,EAAA,WAFA;AAGA,YAAA,aAAA,EAAA;AAHA,WADA,EAMA,IANA,CAMA,UAAA,IAAA,EAAA;AACA,YAAA,UAAA,CAAA,YAAA;AACA,cAAA,MAAA,CAAA,OAAA,GAAA,KAAA;;AACA,cAAA,MAAA,CAAA,SAAA,CAAA,YAAA;AACA,gBAAA,MAAA,CAAA,OAAA,GAAA,IAAA;AACA,eAFA;AAGA,aALA,EAKA,IALA,CAAA;;AAOA,gBAAA,IAAA,CAAA,CAAA,CAAA,EAAA,CACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAzBA,MAyBA;AACA;AACA,kBAAA,KAAA,GAAA,MAAA,CAFA,CAGA;AACA;;AACA,cAAA,MAAA,CAAA,OAAA,CAAA,OAAA,CAAA;AACA,gBAAA,KAAA,EAAA,MAAA,CAAA,EAAA,CAAA,qBAAA,CADA;AAEA,gBAAA,IAAA,EAAA,KAAA,CAAA,EAAA,CAAA,qBAAA,CAFA;AAGA,gBAAA,QAAA,EAAA,MAAA,CAAA;AAHA,eAAA,EALA,CAUA;;;AACA,cAAA,MAAA,CAAA,MAAA;AACA;AACA,WApDA;AAqDA;AACA,OA5DA;AA6DA,KAtJA;AAwJA;AACA,IAAA,UAzJA,sBAyJA,IAzJA,EAyJA;AACA;AACA,MAAA,YAAA,CAAA,OAAA,CAAA,UAAA,EAAA,IAAA;AACA,WAAA,MAAA,CAAA,MAAA,CAAA,eAAA,EAAA,IAAA;;AACA,UAAA,YAAA,CAAA,OAAA,CAAA,iBAAA,KAAA,IAAA,EAAA;AACA,QAAA,QAAA,CAAA,KAAA,GAAA,YAAA,CAAA,OAAA,CAAA,iBAAA,CAAA;AACA,OAFA,MAEA;AACA,QAAA,QAAA,CAAA,KAAA,GAAA,KAAA,EAAA,CAAA,qBAAA,CAAA;AACA;;AACA,WAAA,aAAA;AACA,MAAA,QAAA,CAAA,MAAA;AACA,KApKA;AAqKA,IAAA,eArKA,2BAqKA,GArKA,EAqKA;AAAA;;AACA,UAAA,aAAA,GAAA,YAAA,CAAA,OAAA,CAAA,eAAA,CAAA;AACA,UAAA,iBAAA,GAAA,cAAA,aAAA;AAEA,MAAA,aAAA,CAAA,gBAAA,CAAA,WAAA,EAAA,iBAAA,EAAA,UAAA,KAAA,EAAA;AACA,YAAA,KAAA,KAAA,EAAA,EAAA;AACA,UAAA,MAAA,CAAA,QAAA,GAAA,IAAA;AACA,SAFA,MAEA,IAAA,KAAA,KAAA,MAAA,EAAA;AACA,UAAA,MAAA,CAAA,QAAA,GAAA,IAAA;AACA,SAFA,MAEA;AACA,UAAA,MAAA,CAAA,QAAA,GAAA,KAAA;AACA;AACA,OARA;AASA,KAlLA;AAmLA,IAAA,OAnLA,qBAmLA;AACA,WAAA,SAAA,GAAA,IAAA;AACA,KArLA;AAsLA,IAAA,UAtLA,wBAsLA;AACA,WAAA,SAAA,GAAA,KAAA;AACA,KAxLA;AAyLA,IAAA,cAzLA,4BAyLA;AACA,WAAA,gBAAA,GAAA,IAAA;AACA,KA3LA;AA4LA,IAAA,UA5LA,wBA4LA;AACA,WAAA,YAAA,GAAA,IAAA;AACA,KA9LA;AA+LA,IAAA,eA/LA,6BA+LA;AACA,WAAA,uBAAA,GAAA,IAAA;AACA,KAjMA;AAkMA,IAAA,mBAlMA,iCAkMA;AACA,WAAA,uBAAA,GAAA,KAAA;AACA,KApMA;AAsMA,IAAA,iBAtMA,+BAsMA;AACA,WAAA,gBAAA,GAAA,KAAA;AACA,WAAA,OAAA;AACA,KAzMA;AA0MA,IAAA,aA1MA,2BA0MA;AACA,WAAA,YAAA,GAAA,KAAA;AACA,KA5MA;AA6MA,IAAA,QA7MA,oBA6MA,GA7MA,EA6MA;AAAA;;AACA,WAAA,SAAA,GAAA,GAAA;AACA,WAAA,MAAA,CAAA,MAAA,CAAA,WAAA,EAAA,EAAA;AACA,WAAA,SAAA,CAAA,YAAA;AACA,QAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,WAAA,EAAA,MAAA,CAAA,SAAA;AACA,OAFA;AAGA,KAnNA;AAoNA,IAAA,aApNA,2BAoNA;AACA,MAAA,OAAA,CAAA,GAAA,CAAA,2BAAA,KAAA,gBAAA;;AACA,UAAA,KAAA,gBAAA,IAAA,KAAA,gBAAA,KAAA,WAAA,EAAA,CACA;AACA;AACA,OAHA,MAGA;AACA,aAAA,SAAA,GAAA,SAAA;AACA;;AACA,WAAA,UAAA,GAAA,EAAA;;AACA,cAAA,KAAA,gBAAA;AACA,aAAA,WAAA;AACA,eAAA,UAAA,GAAA,KAAA,EAAA,CAAA,qBAAA,CAAA;AACA;;AACA,aAAA,WAAA;AACA,eAAA,UAAA,GAAA,KAAA,EAAA,CAAA,qBAAA,CAAA;AACA;;AACA,aAAA,WAAA;AACA,eAAA,UAAA,GAAA,KAAA,EAAA,CAAA,qBAAA,CAAA;AACA;;AACA,aAAA,WAAA;AACA,eAAA,UAAA,GAAA,KAAA,EAAA,CAAA,qBAAA,CAAA;AACA;;AACA,aAAA,WAAA;AACA,eAAA,UAAA,GAAA,KAAA,EAAA,CAAA,qBAAA,CAAA;AACA;;AACA,aAAA,WAAA;AACA,eAAA,UAAA,GAAA,KAAA,EAAA,CAAA,qBAAA,CAAA;AACA;;AACA,aAAA,WAAA;AACA,eAAA,UAAA,GAAA,MAAA;AACA;;AACA,aAAA,WAAA;AACA,eAAA,UAAA,GAAA,KAAA,EAAA,CAAA,qBAAA,CAAA;AACA;;AACA,aAAA,WAAA;AACA,eAAA,UAAA,GAAA,KAAA,EAAA,CAAA,qBAAA,CAAA;AACA;;AACA,aAAA,qBAAA;AACA,eAAA,UAAA,GAAA,KAAA,EAAA,CAAA,qBAAA,CAAA;AACA;;AACA,aAAA,sBAAA;AACA,eAAA,UAAA,GAAA,KAAA,EAAA,CAAA,qBAAA,CAAA;AACA;;AACA,aAAA,0BAAA;AACA,eAAA,UAAA,GAAA,KAAA,EAAA,CAAA,qBAAA,CAAA;AACA;;AACA,aAAA,sBAAA;AACA,eAAA,UAAA,GAAA,KAAA,EAAA,CAAA,qBAAA,CAAA;AACA;;AACA;AACA,eAAA,UAAA,GAAA,KAAA,EAAA,CAAA,qBAAA,CAAA;AACA;AA1CA;AA4CA,KAzQA;AA0QA,IAAA,MA1QA,oBA0QA;AACA,UAAA,OAAA,GAAA,IAAA,CADA,CAEA;;AACA,MAAA,OAAA,GAAA,YAAA,CAAA,OAAA,CAAA,gBAAA,CAAA;;AACA,UAAA,CAAA,OAAA,EAAA;AACA,QAAA,OAAA,GAAA,IAAA;AACA;;AACA,WAAA,OAAA,CAAA,IAAA,CAAA;AACA,QAAA,IAAA,EAAA,kBAAA,CAAA,8BAAA,OAAA,CADA,CACA;;AADA,OAAA,EAPA,CAUA;AAEA;AACA;AACA;AACA;AACA;AACA,KA3RA;AA6RA,IAAA,qBA7RA,iCA6RA,KA7RA,EA6RA;AACA,WAAA,KAAA,CAAA,gBAAA,EAAA,KAAA;AACA,KA/RA;AAgSA,IAAA,aAhSA,2BAgSA;AACA,WAAA,KAAA,CAAA,eAAA,EAAA,KAAA,MAAA;AACA,KAlSA;AAoSA,IAAA,YApSA,0BAoSA;AACA,WAAA,OAAA,CAAA,IAAA,CAAA;AAAA,QAAA,IAAA,EAAA;AAAA,OAAA;AACA,KAtSA;AAuSA,IAAA,gBAvSA,8BAuSA;AACA,WAAA,OAAA,CAAA,IAAA,CAAA;AACA,QAAA,IAAA,EAAA,6BADA;AAEA,QAAA,KAAA,EAAA;AAAA,UAAA,MAAA,EAAA;AAAA;AAFA,OAAA;AAIA,KA5SA;AA6SA,IAAA,QA7SA,sBA6SA;AACA,UAAA,eAAA,GAAA,YAAA,CAAA,OAAA,CAAA,qBAAA,KAAA,EAAA;AACA,MAAA,YAAA,CAAA,UAAA,CAAA,oBAAA,eAAA;AACA,MAAA,YAAA,CAAA,UAAA,CAAA,qBAAA,eAAA;AAEA,MAAA,YAAA,CAAA,UAAA,CAAA,eAAA;AACA,MAAA,YAAA,CAAA,UAAA,CAAA,gBAAA;AACA,MAAA,YAAA,CAAA,UAAA,CAAA,iBAAA;AACA,MAAA,YAAA,CAAA,UAAA,CAAA,cAAA;AACA,MAAA,YAAA,CAAA,UAAA,CAAA,cAAA;AACA,MAAA,YAAA,CAAA,UAAA,CAAA,cAAA;AACA,MAAA,YAAA,CAAA,UAAA,CAAA,cAAA;AACA,MAAA,YAAA,CAAA,UAAA,CAAA,cAAA;AACA,MAAA,YAAA,CAAA,UAAA,CAAA,cAAA,EAbA,CAeA;AACA;AACA;AACA;AACA;;AACA,WAAA,OAAA,CAAA,IAAA,CAAA,aAAA;AACA;AAlUA;AA1MA,CAAA", "sourcesContent": ["<template>\r\n  <Header\r\n    class=\"my-header\"\r\n    style=\"\r\n      height: 56px;\r\n      background: #1f3d50;\r\n      boxshadow: 0 2px 3px 2px rgba(0, 0, 0, 0.1);\r\n    \"\r\n  >\r\n    <!-- #4C4C4C background-color:#274e67;-->\r\n    <Row>\r\n      <Col span=\"20\">\r\n        <div class=\"left-header\">\r\n          <Row>\r\n            <!--\r\n          <Col span=\"4\">\r\n          <sider-trigger :collapsed=\"collapsed\" icon=\"md-menu\" @on-change=\"handleCollpasedChange\"></sider-trigger>\r\n          </Col>\r\n            -->\r\n            <Col span=\"24\">\r\n              <div v-for=\"(item, index) in breadCrumbs\" :key=\"index\">\r\n                <span v-if=\"index === 0\">\r\n                  <!--\r\n                    <a href=\"/administrative/dashBoard\">\r\n                      <Icon type=\"md-home\" style=\"color:white;float:left;margin-top:15px;\" />\r\n                      <span style=\"margin-left:5px;color: #CFF7F5;\">{{item}}</span>\r\n                    </a>\r\n                  -->\r\n                  <span v-if=\"currentRouteName && currentRouteName !== 'dashBoard'\">\r\n                    <a @click=\"goHome()\">\r\n                      <img\r\n                        :src=\"fileURL != null ? fileURL : snsLogo\"\r\n                        style=\"float: left; height: 40px; margin-top: 10px\"\r\n                      />\r\n                      <!-- <Icon\r\n                        type=\"md-home\"\r\n                        style=\"color: white; float: left; margin-top: 15px\"\r\n                      />-->\r\n                      <span style=\"margin-left: 5px; color: #cff7f5\">\r\n                        {{\r\n                        item\r\n                        }}\r\n                      </span>\r\n                    </a>\r\n                  </span>\r\n                  <span v-else>\r\n                    <img\r\n                      :src=\"fileURL != null ? fileURL : snsLogo\"\r\n                      style=\"float: left; height: 40px; margin-top: 10px\"\r\n                    />\r\n                    <span style=\"margin-left: 5px; color: #cff7f5\">\r\n                      {{\r\n                      item\r\n                      }}\r\n                    </span>\r\n                  </span>\r\n                </span>\r\n                <span v-else>{{ subControl }}</span>\r\n                <span v-if=\"index < breadCrumbs.length - 1\">></span>\r\n              </div>\r\n            </Col>\r\n          </Row>\r\n        </div>\r\n      </Col>\r\n\r\n      <!-- <Col span=10>\r\n    <div>\r\n        <div style='visibility:hidden'> menu </div>\r\n    </div>\r\n     <div v-if=\"breadCrumbs.length>1 && this.appsEdit\r\n       && (currentRouteName!=='appsM20' && currentRouteName!=='appsM21')\" class=\"center-header\">\r\n\r\n      <Button type=\"text\" @click=\"onAppTab('Monitor')\"\r\n          :class=\"['appTab',this.selAppTab==='Monitor'?'appTabActive':'appTabNormal']\">\r\n       {{$t('lang.apps.common.monitor')}}\r\n      </Button>\r\n\r\n      <Button type=\"text\" @click=\"onAppTab('PlaneSetting')\"\r\n          :class=\"['appTab',this.selAppTab==='PlaneSetting'?'appTabActive':'appTabNormal']\">\r\n       {{$t('lang.apps.common.planeSetting')}}\r\n      </Button>\r\n      <Button type=\"text\" @click=\"onAppTab('ObjectSetting')\"\r\n          :class=\"['appTab',this.selAppTab==='ObjectSetting'?'appTabActive':'appTabNormal']\">\r\n       {{$t('lang.apps.common.object')}}\r\n      </Button>\r\n\r\n    </div>\r\n    <div v-else>\r\n        <div style='visibility:hidden'> menu </div>\r\n    </div>\r\n      </Col>-->\r\n      <!-- <Col span=\"1\">\r\n        <Button @click=\"reloadPage\">reload</Button>\r\n      </Col>-->\r\n      <Col span=\"4\">\r\n        <div class=\"right-header-item\">\r\n          <Dropdown placement=\"bottom-end\">\r\n            <!--trigger=\"click\" -->\r\n            <a href=\"javascript:;\">\r\n              <i class=\"ivu-icon ivu-icon-md-person\"></i>\r\n              <span>{{ userName }}</span>\r\n            </a>\r\n            <DropdownMenu slot=\"list\">\r\n              <!--\r\n          <DropdownItem divided\t @click.native=\"onPersonData\">{{$t(\"lang.common.personalData\")}}</DropdownItem>\r\n          <DropdownItem divided\t @click.native=\"onChangePassword\">{{$t(\"lang.common.changePassword\")}}</DropdownItem>\r\n              -->\r\n\r\n              <DropdownItem\r\n                divided\r\n                @click.native=\"onAbout\"\r\n                class=\"leftItem\"\r\n              >{{ $t(\"LocaleString.F30008\") }}</DropdownItem>\r\n\r\n              <DropdownItem\r\n                divided\r\n                @click.native=\"onSystemConfig\"\r\n                class=\"leftItem\"\r\n              >{{ $t(\"LocaleString.F30009\") }}</DropdownItem>\r\n\r\n              <!-- <DropdownItem divided @click.native=\"onKMConfig\" class=\"leftItem\">\r\n                {{ $t(\"LocaleString.F30014\") }}\r\n              </DropdownItem>-->\r\n\r\n              <DropdownItem\r\n                divided\r\n                @click.native=\"onResetPassword\"\r\n                class=\"leftItem\"\r\n              >{{ $t(\"LocaleString.B00039\") }}</DropdownItem>\r\n\r\n              <DropdownItem divided @click.native=\"onLogout\" class=\"logout\">\r\n                <span class=\"logout-text\">{{ $t(\"LocaleString.B00038\") }}</span>\r\n                <span class=\"logout-icon\">\r\n                  <Icon type=\"md-exit\" />\r\n                </span>\r\n              </DropdownItem>\r\n              <Select\r\n                :disabled=\"false\"\r\n                class=\"Xright-header-select\"\r\n                placement=\"top\"\r\n                v-model=\"langChange\"\r\n                @on-change=\"changeLang\"\r\n                @on-select=\"getLocales\"\r\n              >\r\n                <Option\r\n                  v-for=\"item in langList\"\r\n                  :value=\"item.value\"\r\n                  :key=\"item.value\"\r\n                >{{ item.label }}</Option>\r\n              </Select>\r\n            </DropdownMenu>\r\n          </Dropdown>\r\n\r\n          <About v-if=\"showAbout\" @closeAbout=\"closeAbout\"></About>\r\n\r\n          <SystemConfig v-if=\"showSystemConfig\" @closeSystemConfig=\"closeSystemConfig\"></SystemConfig>\r\n\r\n          <KMConfig v-if=\"showKMConfig\" @closeKMConfig=\"closeKMConfig\"></KMConfig>\r\n\r\n          <div class=\"right-header-action\" v-show=\"isShowRightSider\" @click=\"onChangeSider()\">\r\n            <span class=\"arrow\" :class=\"{ 'left-arrow': !isOpen, 'right-arrow': isOpen }\"></span>\r\n          </div>\r\n        </div>\r\n      </Col>\r\n    </Row>\r\n    <Modal\r\n      :title=\"$t('LocaleString.B00039')\"\r\n      width=\"600px\"\r\n      v-model=\"isShowEditPasswordModal\"\r\n      class-name=\"vertical-center-modal\"\r\n      :closable=\"false\"\r\n      :mask-closable=\"false\"\r\n    >\r\n      <Form\r\n        ref=\"formChangePassword\"\r\n        :model=\"formChangePassword\"\r\n        :rules=\"ruleChangePassword\"\r\n        label-position=\"top\"\r\n      >\r\n        <div class=\"modal-form-content\">\r\n          <Form-item prop=\"oldPasswd\" :label=\"$t('LocaleString.L00008')\">\r\n            <i-Input\r\n              type=\"password\"\r\n              v-model=\"formChangePassword.oldPasswd\"\r\n              :placeholder=\"$t('LocaleString.L00001', { 0: $t('LocaleString.L00008') })\r\n              \"\r\n            ></i-Input>\r\n          </Form-item>\r\n          <Form-item prop=\"newPasswd\" :label=\"$t('LocaleString.L00009')\">\r\n            <i-Input\r\n              type=\"password\"\r\n              v-model=\"formChangePassword.newPasswd\"\r\n              :placeholder=\"$t('LocaleString.L00001', { 0: $t('LocaleString.L00009') })\r\n              \"\r\n            ></i-Input>\r\n          </Form-item>\r\n          <Form-item prop=\"newPasswdCheck\" :label=\"$t('LocaleString.L00010')\">\r\n            <i-Input\r\n              type=\"password\"\r\n              v-model=\"formChangePassword.newPasswdCheck\"\r\n              :placeholder=\"$t('LocaleString.L00010')\"\r\n            ></i-Input>\r\n          </Form-item>\r\n        </div>\r\n      </Form>\r\n      <div slot=\"footer\" style=\"text-align: center\">\r\n        <Button @click=\"closeModal\" type=\"primary\">\r\n          {{\r\n          $t(\"LocaleString.B00015\")\r\n          }}\r\n        </Button>\r\n        <Button @click=\"changePasswordHandleSubmit('formChangePassword')\" type=\"primary\">\r\n          {{ $t(\"LocaleString.B00003\")\r\n          }}\r\n        </Button>\r\n      </div>\r\n    </Modal>\r\n  </Header>\r\n</template>\r\n\r\n<script type=\"es6\">\r\n// import snsLogo from \"@/assets/images/logo_sns.png\";\r\nimport snsLogo from \"@/assets/images/FUSION_LOGO_FusionCare.svg\";\r\n//import snsLogo from \"@/assets/images/FUSION_LOGO_FusionCare.png\";\r\nimport About from \"@/components/administrative/systemManagement/about.vue\";\r\nimport SystemConfig from \"@/components/administrative/systemManagement/systemConfig.vue\";\r\nimport KMConfig from \"@/components/administrative/systemManagement/kmConfig.vue\";\r\n\r\nimport Config from \"@/common/config\";\r\n// import WebStorage from '@/store/web-storage'\r\n// import siderTrigger from './sider-trigger'\r\n// import customBreadCrumb from './custom-bread-crumb'\r\nimport CommonService from \"@/components/administrative/apps/commonService\";\r\n\r\nimport { mapGetters } from \"vuex\";\r\n\r\nimport { locale as enUS } from \"@/locales/en-US\";\r\nimport { locale as zhCN } from \"@/locales/zh-CN\";\r\nimport { locale as zhTW } from \"@/locales/zh-TW\";\r\n\r\nexport default {\r\n  name: \"AdminHeader\",\r\n  props: [\"collapsed\", \"isShowRightSider\", \"isOpen\"],\r\n  data() {\r\n    const validateOldPass = (rule, value, callback) => {\r\n      if (value === \"\") {\r\n        callback(\r\n          new Error(\r\n            this.$t(\"LocaleString.L00001\", {\r\n              0: this.$t(\"LocaleString.L00008\")\r\n            })\r\n          )\r\n        );\r\n      } else if (this.errorType === 1) {\r\n        callback(new Error(this.$t(\"LocaleString.E00008\")));\r\n      } else {\r\n        callback();\r\n      }\r\n      this.errorType = 0;\r\n    };\r\n    const validateNewPass = (rule, value, callback) => {\r\n      let reg = /^(?![\\d]+$)(?![a-zA-Z]+$)(?![~`@#$%^&*\\-_=+|?()<>[\\]{}\",.;'!]+$)[\\da-zA-Z~`@#$%^&*\\-_=+|?()<>[\\]{}\",.;'!]{8,15}$/;\r\n      if (value === \"\") {\r\n        callback(\r\n          new Error(\r\n            this.$t(\"LocaleString.L00001\", {\r\n              0: this.$t(\"LocaleString.L00009\")\r\n            })\r\n          )\r\n        );\r\n      } else if (!reg.test(value)) {\r\n        callback(new Error(this.$t(\"LocaleString.W00028\")));\r\n      } else if (this.errorType === 2) {\r\n        callback(new Error(this.$t(\"LocaleString.W00028\")));\r\n      } else if (\r\n        this.formChangePassword.newPasswd === this.formChangePassword.oldPasswd\r\n      ) {\r\n        callback(new Error(this.$t(\"LocaleString.M00015\")));\r\n      } else {\r\n        if (this.formChangePassword.newPasswdCheck !== \"\") {\r\n          // 对第3个密码框单独验证\r\n          this.$refs.formChangePassword.validateField(\"newPasswdCheck\");\r\n        }\r\n        callback();\r\n      }\r\n      this.errorType = 0;\r\n    };\r\n    const validateNewPassCheck = (rule, value, callback) => {\r\n      if (value === \"\") {\r\n        callback(\r\n          new Error(\r\n            this.$t(\"LocaleString.L00001\", {\r\n              0: this.$t(\"LocaleString.L00010\")\r\n            })\r\n          )\r\n        );\r\n      } else if (value !== this.formChangePassword.newPasswd) {\r\n        callback(new Error(this.$t(\"LocaleString.W00007\")));\r\n      } else {\r\n        callback();\r\n      }\r\n    };\r\n\r\n    return {\r\n      snsLogo: snsLogo,\r\n      fileURL: null,\r\n      langList: [],\r\n      subControl: \"\",\r\n      formChangePassword: {\r\n        oldPasswd: \"\",\r\n        newPasswd: \"\",\r\n        newPasswdCheck: \"\"\r\n      },\r\n      webVersion: Config.WEB_VERSION,\r\n      userName: localStorage.getItem(\"sns_loginUser\"),\r\n      langChange: localStorage.getItem(\"sns_lang\"),\r\n      breadCrumbs: [],\r\n      selAppTab: \"Monitor\",\r\n      isShowEditPasswordModal: false,\r\n      loading: true,\r\n      resetFormItem: {\r\n        password: \"\"\r\n      },\r\n      showAbout: false,\r\n      showSystemConfig: false,\r\n      showKMConfig: false,\r\n      appsEdit: true,\r\n      ruleChangePassword: {\r\n        oldPasswd: [\r\n          {\r\n            validator: validateOldPass,\r\n            trigger: \"submit\"\r\n          }\r\n        ],\r\n        newPasswd: [\r\n          {\r\n            validator: validateNewPass,\r\n            trigger: \"submit\"\r\n          }\r\n        ],\r\n        newPasswdCheck: [\r\n          {\r\n            validator: validateNewPassCheck,\r\n            trigger: \"submit\"\r\n          }\r\n        ]\r\n      },\r\n      appCode: localStorage.getItem(\"applicationCode\")\r\n    };\r\n  },\r\n  components: {\r\n    // siderTrigger //,\r\n    // customBreadCrumb\r\n    About,\r\n    SystemConfig,\r\n    KMConfig\r\n  },\r\n\r\n  computed: {\r\n    ...mapGetters([\r\n      \"breadCrumbList\",\r\n      \"pocConfigChanged\",\r\n      \"appTab\",\r\n      \"logoutAction\"\r\n    ]),\r\n    // langList() {\r\n    //   return [\r\n    //     {\r\n    //       value: \"zh-TW\",\r\n    //       label: this.$t(\"LocaleString.D10001\"),\r\n    //     },\r\n    //     {\r\n    //       value: \"en-US\",\r\n    //       label: this.$t(\"LocaleString.D10002\"),\r\n    //     },\r\n    //     {\r\n    //       value: \"zh-CN\",\r\n    //       label: this.$t(\"LocaleString.D10003\"),\r\n    //     },\r\n    //   ];\r\n    // },\r\n    currentRouteName() {\r\n      return this.$route.name;\r\n    }\r\n    /*\r\n      breadCrumbList () {\r\n        return this.$store.state.app.breadCrumbList\r\n      }\r\n      */\r\n  },\r\n\r\n  watch: {\r\n    $route: \"routingChange\",\r\n    breadCrumbList(val) {\r\n      //   this.breadCrumb=val.toString()\r\n      this.breadCrumbs = val;\r\n    },\r\n    pocConfigChanged(val) {\r\n      console.log(\"pocConfigChanged\");\r\n      console.log(val);\r\n      this.getAppsEditable();\r\n      this.getLogo();\r\n    },\r\n    appTab(val) {\r\n      switch (val) {\r\n        case \"PlaneSetting\":\r\n          this.subControl = \" > 對象管理\";\r\n          break;\r\n        case \"ObjectSetting\":\r\n          this.subControl = \" > 圖面管理\";\r\n          break;\r\n        case \"ThirdPartySetting\":\r\n          this.subControl = \" > \" + this.$t(\"LocaleString.F30012\");\r\n          break;\r\n        default:\r\n          this.subControl = \"\";\r\n          break;\r\n      }\r\n    },\r\n    langChange() {\r\n      this.getLocales();\r\n    },\r\n    logoutAction() {\r\n      this.logout();\r\n    }\r\n  },\r\n  created() {\r\n    this.getLocales();\r\n    if (localStorage.getItem(\"sns_systemTitle\") != null) {\r\n      document.title = localStorage.getItem(\"sns_systemTitle\");\r\n    } else {\r\n      document.title = this.$t(\"LocaleString.S00002\");\r\n    }\r\n    this.fileURL = localStorage.getItem(\"sns_logoURL\")\r\n      ? localStorage.getItem(\"sns_logoURL\")\r\n      : null;\r\n    this.getLogo();\r\n  },\r\n  mounted() {\r\n    this.getAppsEditable();\r\n    this.routingChange();\r\n  },\r\n  methods: {\r\n    getLogo() {\r\n      let pocGroupParams = {\r\n        inlinecount: true,\r\n        search: \"active eq true and category eq @SNS@Logo\"\r\n      };\r\n      this.$service.getPOCGroupsByCategory.send(pocGroupParams).then(pocRes => {\r\n        if (\r\n          pocRes.count > 0 &&\r\n          pocRes.results[0].files &&\r\n          pocRes.results[0].files.length > 0\r\n        ) {\r\n          this.fileURL = pocRes.results[0].files[0].url;\r\n          localStorage.setItem(\"sns_logoURL\", this.fileURL);\r\n        } else {\r\n          this.fileURL = null;\r\n        }\r\n      });\r\n    },\r\n    getLocales() {\r\n      console.log(\"lang:\" + this.language);\r\n      let _this = this;\r\n\r\n      let str = \"\";\r\n      let params = {\r\n        search: str,\r\n        inlinecount: true\r\n      };\r\n      // this.$service.getUsers.requestCommon(_this.queryStr, current, pageSize)\r\n      this.$service.getLocales.send(params).then(data => {\r\n        _this.results = data.results;\r\n        // console.log(data)\r\n        _this.langList.splice(0);\r\n        let obj = {};\r\n        data.results.forEach(element => {\r\n          obj = {\r\n            value: element.locale,\r\n            label: this.$t(\"LocaleString.\" + element.stringId)\r\n          };\r\n          _this.langList.push(obj);\r\n        });\r\n\r\n        console.log(\"zzz:\" + JSON.stringify(_this.langList));\r\n      });\r\n    },\r\n    // reloadPage() {\r\n    //   location.reload(true);\r\n    // },\r\n    closeModal() {\r\n      this.isShowEditPasswordModal = false;\r\n      this.formChangePassword = {\r\n        oldPasswd: \"\",\r\n        newPasswd: \"\",\r\n        newPasswdCheck: \"\"\r\n      };\r\n    },\r\n    handleClick(val) {\r\n      switch (val) {\r\n        case \"logout\":\r\n          this.logout();\r\n          break;\r\n        case \"showEditPasswordModal\":\r\n          this.isShowEditPasswordModal = true;\r\n          break;\r\n      }\r\n    },\r\n    logout() {\r\n      let applicationCode = localStorage.getItem(\"sns_applicationCode\") || \"\";\r\n      localStorage.removeItem(\"sns_accessToken\" + applicationCode);\r\n      localStorage.removeItem(\"sns_refreshToken\" + applicationCode);\r\n\r\n      localStorage.removeItem(\"sns_loginUser\");\r\n      localStorage.removeItem(\"sns_permission\");\r\n      localStorage.removeItem(\"sns_moduleCodes\");\r\n      localStorage.removeItem(\"sns_m2Filter\");\r\n      localStorage.removeItem(\"sns_m3Filter\");\r\n      localStorage.removeItem(\"sns_m4Filter\");\r\n      localStorage.removeItem(\"sns_m8Filter\");\r\n      localStorage.removeItem(\"sns_m9Filter\");\r\n\r\n      // this.$Notice.success({\r\n      //   title: this.$t('lang.common.successful'),\r\n      //   desc: this.$t('lang.common.logout') + this.$t('lang.common.successful'),\r\n      //   duration: Config.SUCCESS_DURATION\r\n      // })\r\n      this.$store.state.loggedIn = false;\r\n      this.$router.push(\"/user/login\");\r\n    },\r\n    changePasswordHandleSubmit(name) {\r\n      this.$refs[name].validate(valid => {\r\n        if (valid) {\r\n          let data = {\r\n            oldPassword: this.formChangePassword.oldPasswd,\r\n            newPassword: this.formChangePassword.newPasswd\r\n          };\r\n          this.$service.patchCurrentUserPassword\r\n            .send(data, null, {\r\n              requestID: this.createRequestID(),\r\n              requestFunction: \"ChangePwd\",\r\n              requestAction: \"Update\"\r\n            })\r\n            .then(data => {\r\n              setTimeout(() => {\r\n                this.loading = false;\r\n                this.$nextTick(() => {\r\n                  this.loading = true;\r\n                });\r\n              }, 2000);\r\n\r\n              if (data[0]) {\r\n                // for (let i in data[0].errors) {\r\n                // switch (data[0].errors[i].error) {\r\n                //     case 'passwordNotMatch':\r\n                //     this.errorType = 1\r\n                //     this.$refs.formChangePassword.validateField('oldPasswd')\r\n                //     break\r\n                //     case 'badPassword':\r\n                //     this.errorType = 2\r\n                //     this.$refs.formChangePassword.validateField('newPasswd')\r\n                //     break\r\n                //     case 'samePassword':\r\n                //     this.errorType = 3\r\n                //     this.$refs.formChangePassword.validateField('newPasswd')\r\n                //     break\r\n                //     default:\r\n                //     let _this = this\r\n                //     this.$Notice.error({\r\n                //         title: _this.$t('LocaleString.E00037'),\r\n                //         desc: _this.$t('lang.common.contactSystemAdministrator'),\r\n                //         duration: Config.errorDuration\r\n                //     })\r\n                //     break\r\n                // }\r\n                // }\r\n              } else {\r\n                // this.changePasswordModal = false\r\n                let _this = this;\r\n                // this.$store.dispatch('delToken')\r\n                // localStorage.removeItem('moduleCodes')\r\n                this.$Notice.success({\r\n                  title: this.$t(\"LocaleString.M00004\"),\r\n                  desc: _this.$t(\"LocaleString.M00005\"),\r\n                  duration: Config.successDuration\r\n                });\r\n                // this.$refs[name].resetFields()\r\n                this.logout();\r\n              }\r\n            });\r\n        }\r\n      });\r\n    },\r\n\r\n    // 语言切换\r\n    changeLang(data) {\r\n      // this.$i18n.locale = data;\r\n      localStorage.setItem(\"sns_lang\", data);\r\n      this.$store.commit(\"setLangChange\", data);\r\n      if (localStorage.getItem(\"sns_systemTitle\") != null) {\r\n        document.title = localStorage.getItem(\"sns_systemTitle\");\r\n      } else {\r\n        document.title = this.$t(\"LocaleString.S00002\");\r\n      }\r\n      this.routingChange();\r\n      location.reload();\r\n    },\r\n    getAppsEditable(val) {\r\n      let loginUserName = localStorage.getItem(\"sns_loginUser\");\r\n      let appEditAccountKey = \"appsEdit-\" + loginUserName;\r\n\r\n      CommonService.getPOCProperties(\"pocConfig\", appEditAccountKey, value => {\r\n        if (value === \"\") {\r\n          this.appsEdit = true;\r\n        } else if (value === \"true\") {\r\n          this.appsEdit = true;\r\n        } else {\r\n          this.appsEdit = false;\r\n        }\r\n      });\r\n    },\r\n    onAbout() {\r\n      this.showAbout = true;\r\n    },\r\n    closeAbout() {\r\n      this.showAbout = false;\r\n    },\r\n    onSystemConfig() {\r\n      this.showSystemConfig = true;\r\n    },\r\n    onKMConfig() {\r\n      this.showKMConfig = true;\r\n    },\r\n    onResetPassword() {\r\n      this.isShowEditPasswordModal = true;\r\n    },\r\n    closeChangePassword() {\r\n      this.isShowEditPasswordModal = false;\r\n    },\r\n\r\n    closeSystemConfig() {\r\n      this.showSystemConfig = false;\r\n      this.getLogo();\r\n    },\r\n    closeKMConfig() {\r\n      this.showKMConfig = false;\r\n    },\r\n    onAppTab(val) {\r\n      this.selAppTab = val;\r\n      this.$store.commit(\"setAppTab\", \"\");\r\n      this.$nextTick(() => {\r\n        this.$store.commit(\"setAppTab\", this.selAppTab);\r\n      });\r\n    },\r\n    routingChange() {\r\n      console.log(\"this.currentRouteName:\" + this.currentRouteName);\r\n      if (this.currentRouteName && this.currentRouteName !== \"dashBoard\") {\r\n        //this.selAppTab='Monitor'\r\n        //this.$store.commit('setAppTab', this.selAppTab)\r\n      } else {\r\n        this.selAppTab = \"Monitor\";\r\n      }\r\n      this.subControl = \"\";\r\n      switch (this.currentRouteName) {\r\n        case \"appsSNSM1\":\r\n          this.subControl = this.$t(\"LocaleString.F30001\");\r\n          break;\r\n        case \"appsSNSM2\":\r\n          this.subControl = this.$t(\"LocaleString.F30002\");\r\n          break;\r\n        case \"appsSNSM3\":\r\n          this.subControl = this.$t(\"LocaleString.F30003\");\r\n          break;\r\n        case \"appsSNSM4\":\r\n          this.subControl = this.$t(\"LocaleString.F30004\");\r\n          break;\r\n        case \"appsSNSM5\":\r\n          this.subControl = this.$t(\"LocaleString.F30005\");\r\n          break;\r\n        case \"appsSNSM6\":\r\n          this.subControl = this.$t(\"LocaleString.F30006\");\r\n          break;\r\n        case \"appsSNSM7\":\r\n          this.subControl = \"軌跡監控\";\r\n          break;\r\n        case \"appsSNSM8\":\r\n          this.subControl = this.$t(\"LocaleString.F30016\");\r\n          break;\r\n        case \"appsSNSM9\":\r\n          this.subControl = this.$t(\"LocaleString.F30015\");\r\n          break;\r\n        case \"appsSNSPlaneSetting\":\r\n          this.subControl = this.$t(\"LocaleString.F30010\");\r\n          break;\r\n        case \"appsSNSObjectSetting\":\r\n          this.subControl = this.$t(\"LocaleString.F00018\");\r\n          break;\r\n        case \"appsSNSThirdPartySetting\":\r\n          this.subControl = this.$t(\"LocaleString.F30012\");\r\n          break;\r\n        case \"appsSNSAccessLogging\":\r\n          this.subControl = this.$t(\"LocaleString.F30017\");\r\n          break;\r\n        default:\r\n          this.subControl = this.$t(\"LocaleString.F30001\");\r\n          break;\r\n      }\r\n    },\r\n    goHome() {\r\n      let tabItem = null;\r\n      // if(this.currentRouteName && this.currentRouteName!=='dashBoard') {\r\n      tabItem = localStorage.getItem(\"sns_defaultTab\");\r\n      if (!tabItem) {\r\n        tabItem = \"m1\";\r\n      }\r\n      this.$router.push({\r\n        path: decodeURIComponent(\"/administrative/apps/sns/\" + tabItem) // 導頁至預設頁面\r\n      });\r\n      // }\r\n\r\n      // if (this.currentRouteName && this.currentRouteName !== \"appsSNSM1\") {\r\n      //   this.$router.push({ name: \"appsSNSM1\" });\r\n      // } else {\r\n      //   location.reload();\r\n      // }\r\n    },\r\n\r\n    handleCollpasedChange(state) {\r\n      this.$emit(\"on-coll-change\", state);\r\n    },\r\n    onChangeSider() {\r\n      this.$emit(\"onChangeSider\", this.isOpen);\r\n    },\r\n\r\n    onPersonData() {\r\n      this.$router.push({ path: \"/administrative/userAccount\" });\r\n    },\r\n    onChangePassword() {\r\n      this.$router.push({\r\n        path: \"/administrative/userAccount\",\r\n        query: { action: \"password\" }\r\n      });\r\n    },\r\n    onLogout() {\r\n      let applicationCode = localStorage.getItem(\"sns_applicationCode\") || \"\";\r\n      localStorage.removeItem(\"sns_accessToken\" + applicationCode);\r\n      localStorage.removeItem(\"sns_refreshToken\" + applicationCode);\r\n\r\n      localStorage.removeItem(\"sns_loginUser\");\r\n      localStorage.removeItem(\"sns_permission\");\r\n      localStorage.removeItem(\"sns_moduleCodes\");\r\n      localStorage.removeItem(\"sns_identity\");\r\n      localStorage.removeItem(\"sns_m2Filter\");\r\n      localStorage.removeItem(\"sns_m3Filter\");\r\n      localStorage.removeItem(\"sns_m4Filter\");\r\n      localStorage.removeItem(\"sns_m8Filter\");\r\n      localStorage.removeItem(\"sns_m9Filter\");\r\n\r\n      // this.$Notice.success({\r\n      //   title: this.$t('lang.common.successful'),\r\n      //   desc: this.$t('lang.common.logout') + this.$t('lang.common.successful'),\r\n      //   duration: Config.SUCCESS_DURATION\r\n      // })\r\n      this.$router.push(\"/user/login\");\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style lang=\"less\">\r\n.my-header {\r\n  padding-left: 5px !important;\r\n  padding-right: 5px !important;\r\n\r\n  .left-header {\r\n    float: left;\r\n    color: white;\r\n    height: 56px;\r\n    //margin:0 auto;\r\n    font-size: 18px !important;\r\n    font-weight: bold;\r\n    width: 100%;\r\n\r\n    span {\r\n      float: left;\r\n      margin-left: 5px;\r\n      overflow: hidden;\r\n      white-space: nowrap;\r\n      text-overflow: ellipsis;\r\n      font-size: 32px;\r\n    }\r\n  }\r\n\r\n  .center-header {\r\n    .appTab {\r\n      border-radius: 10px 10px 0px 0px;\r\n      font-size: 20px;\r\n      // width:120px;\r\n      height: 40px;\r\n      margin-left: 5px;\r\n      margin-top: 10px;\r\n    }\r\n\r\n    .appTabNormal {\r\n      background: #dddddd;\r\n    }\r\n\r\n    .appTabActive {\r\n      background: white;\r\n    }\r\n  }\r\n\r\n  .right-header-item {\r\n    float: right;\r\n\r\n    .ivu-dropdown-rel {\r\n      margin-right: 30px;\r\n      text-align: center;\r\n\r\n      a {\r\n        color: #ffffff;\r\n      }\r\n\r\n      i {\r\n        font-size: 25px;\r\n        padding-right: 10px;\r\n      }\r\n\r\n      height: 56px;\r\n      line-height: 56px;\r\n    }\r\n\r\n    .leftItem {\r\n      text-align: left;\r\n    }\r\n\r\n    .logout {\r\n      display: flex;\r\n\r\n      .logout-text {\r\n        flex: 1;\r\n        margin-left: 30px;\r\n      }\r\n    }\r\n\r\n    :hover {\r\n      color: #49accf;\r\n    }\r\n  }\r\n\r\n  .right-header {\r\n    float: right;\r\n    margin-right: 5px;\r\n    margin-top: 3px;\r\n\r\n    //margin-right: -10px;\r\n    //background-color: red;\r\n    &-select {\r\n      width: 100px;\r\n      align-self: center;\r\n      float: left;\r\n      margin-top: 14px;\r\n      margin-right: 32px;\r\n\r\n      .ivu-select-selection {\r\n        height: 28px;\r\n      }\r\n    }\r\n\r\n    &-user {\r\n      .user-name {\r\n        display: flex;\r\n\r\n        i {\r\n          line-height: 56px;\r\n        }\r\n\r\n        span {\r\n          display: block;\r\n          overflow: hidden;\r\n          white-space: nowrap;\r\n          text-overflow: ellipsis;\r\n          text-align: left;\r\n        }\r\n      }\r\n\r\n      float: left;\r\n      font-size: 16px;\r\n      padding-right: 10px;\r\n\r\n      .ivu-dropdown-rel {\r\n        text-align: center;\r\n\r\n        a {\r\n          color: #ffffff;\r\n        }\r\n\r\n        i {\r\n          font-size: 25px;\r\n          padding-right: 10px;\r\n        }\r\n\r\n        height: 56px;\r\n        line-height: 56px;\r\n      }\r\n\r\n      .logout {\r\n        display: flex;\r\n\r\n        .logout-text {\r\n          flex: 1;\r\n        }\r\n      }\r\n\r\n      :hover {\r\n        color: #49accf;\r\n      }\r\n    }\r\n\r\n    &-action {\r\n      background: #1f3c50;\r\n      height: 56px;\r\n      width: 56px;\r\n      float: left;\r\n\r\n      .arrow {\r\n        margin-top: 18px;\r\n        margin-left: 18px;\r\n        width: 20px;\r\n        height: 20px;\r\n        display: block;\r\n        background-size: 100%;\r\n      }\r\n\r\n      .left-arrow {\r\n        background: url(\"~@/assets/images/icon_left-arrow.svg\") no-repeat center;\r\n      }\r\n\r\n      .right-arrow {\r\n        background: url(\"~@/assets/images/icon_right-arrow.svg\") no-repeat\r\n          center;\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n<style lang=\"scss\" scoped>\r\n/deep/ .ivu-btn:hover {\r\n  color: #31babb;\r\n  // background-color: #31babb;\r\n  border-color: #31babb;\r\n}\r\n\r\n/deep/ .ivu-btn-group-large > .ivu-btn {\r\n  height: 40px;\r\n  padding: 0 15px;\r\n  font-size: 16px;\r\n  border-radius: 10px;\r\n}\r\n\r\n/deep/ .ivu-btn-primary {\r\n  color: #fff;\r\n  background-color: #31babb;\r\n  border-color: #31babb;\r\n}\r\n\r\n/deep/ .ivu-btn-primary:hover {\r\n  color: #fff !important;\r\n  border-color: #31babb;\r\n}\r\n\r\n/deep/ .ivu-select-dropdown {\r\n  z-index: 9999999900;\r\n}\r\n</style>\r\n"], "sourceRoot": "src/components/administrative/common"}]}