{"remainingRequest": "E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\src\\components\\administrative\\apps\\sns\\planeSetting\\sitePlan\\dragContainNS2.vue?vue&type=script&lang=js&", "dependencies": [{"path": "E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\src\\components\\administrative\\apps\\sns\\planeSetting\\sitePlan\\dragContainNS2.vue", "mtime": 1754362736892}, {"path": "E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\node_modules\\string-replace-loader\\index.js", "mtime": 499162500000}, {"path": "E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgZHJhZ0RpdiBmcm9tICdAL2NvbXBvbmVudHMvYWRtaW5pc3RyYXRpdmUvY29tbW9uL2RyYWdEaXYnDQppbXBvcnQgY3Jvc3MgZnJvbSAnQC9jb21wb25lbnRzL2FkbWluaXN0cmF0aXZlL2NvbW1vbi9pbWFnZXMvY3Jvc3MucG5nJw0KaW1wb3J0IHJlc2V0IGZyb20gJ0AvY29tcG9uZW50cy9hZG1pbmlzdHJhdGl2ZS9jb21tb24vaW1hZ2VzL3Jlc2V0LnBuZycNCg0KZXhwb3J0IGRlZmF1bHQgew0KICAgIHByb3BzOiBbJ2NoaWxkRHJhZycsICdib3gnLCAnbmFtZScsICdpc0RyYWdnYWJsZVJlZ2lvbiddLA0KICAgIGRhdGEoKSB7DQogICAgICAgIC8vIHNldFRpbWVvdXQoKCkgPT4gew0KICAgICAgICAvLyAgIGxldCBvQm94ID0gZG9jdW1lbnQuZ2V0RWxlbWVudEJ5SWQodGhpcy5fcHJvcHMuYm94KQ0KICAgICAgICAvLyAgIGNvbnNvbGUubG9nKCd0aGlzLl9wcm9wcy5ib3g6JytKU09OLnN0cmluZ2lmeSh0aGlzLl9wcm9wcy5ib3gpKQ0KICAgICAgICAvLyAgIGNvbnNvbGUubG9nKCdvQm94LmNsaWVudFdpZHRoOicrSlNPTi5zdHJpbmdpZnkob0JveC5jbGllbnRXaWR0aCkpDQogICAgICAgIC8vICAgY29uc29sZS5sb2coJ29Cb3guY2xpZW50SGVpZ2h0OicrSlNPTi5zdHJpbmdpZnkob0JveC5jbGllbnRIZWlnaHQpKQ0KICAgICAgICAvLyAgIGlmICghb0JveCkgew0KICAgICAgICAvLyAgICAgcmV0dXJuDQogICAgICAgIC8vICAgfQ0KICAgICAgICAvLyAgIGxldCB3aWR0aCA9IG9Cb3guY2xpZW50V2lkdGgNCiAgICAgICAgLy8gICBsZXQgaGVpZ2h0ID0gb0JveC5jbGllbnRIZWlnaHQNCiAgICAgICAgLy8gICB0aGlzLmhlaWdodCA9IGhlaWdodCAvIHdpZHRoDQogICAgICAgIC8vIH0sIDMwMCkNCg0KICAgICAgICByZXR1cm4gew0KICAgICAgICAgICAgcG9zaXRpb246IG51bGwsDQogICAgICAgICAgICBoZWlnaHQ6IDAsDQogICAgICAgICAgICBzdHlsZU9iamVjdDogew0KICAgICAgICAgICAgICAgIHdpZHRoOiAodGhpcy5fcHJvcHMuY2hpbGREcmFnLmNoaWxkLmNvb3JkaW5hdGVzLmxvd1JpZ2h0LnggKiAxMDAgLSB0aGlzLl9wcm9wcy5jaGlsZERyYWcuY2hpbGQuY29vcmRpbmF0ZXMudXBwZXJMZWZ0LnggKiAxMDApICsgJyUnLA0KICAgICAgICAgICAgICAgIGhlaWdodDogMCwNCiAgICAgICAgICAgICAgICBsZWZ0OiB0aGlzLl9wcm9wcy5jaGlsZERyYWcuY2hpbGQuY29vcmRpbmF0ZXMudXBwZXJMZWZ0LnggKiAxMDAgKyAnJScsDQogICAgICAgICAgICAgICAgLy93aWR0aDonMTAlJywNCiAgICAgICAgICAgICAgICAvL2hlaWdodDogJzEwJScsDQogICAgICAgICAgICAgICAgLy9sZWZ0OiAnMTAlJywNCiAgICAgICAgICAgICAgICB0b3A6IDAsDQogICAgICAgICAgICAgICAgJ3otaW5kZXgnOiAxLA0KICAgICAgICAgICAgICAgIGRpc3BsYXk6IHRoaXMuX3Byb3BzLmNoaWxkRHJhZy5pc1Nob3cgPyAnJyA6ICdub25lJw0KICAgICAgICAgICAgfSwNCiAgICAgICAgICAgIGNyb3NzOiBjcm9zcywNCiAgICAgICAgICAgIHJlc2V0OiByZXNldCwNCiAgICAgICAgICAgIHN0eWxlQ29sb3I6IHsNCiAgICAgICAgICAgICAgYmFja2dyb3VuZENvbG9yOiAnJw0KICAgICAgICAgICAgfQ0KICAgICAgICB9DQogICAgfSwNCiAgICB3YXRjaDogew0KICAgICAgICBpc0RyYWdnYWJsZVJlZ2lvbigpIHsNCiAgICAgICAgICAgICAgdGhpcy5pbml0KCkgICAgICAgICANCiAgICAgICAgfQ0KICAgIH0sDQoNCiAgICBtb3VudGVkKCkgew0KICAgICAgICB0aGlzLnN0eWxlT2JqZWN0LnRvcCA9ICc1JScNCiAgICAgICAgc2V0VGltZW91dCgoKSA9PiB7DQoNCiAgICAgICAgICAgIHRoaXMuaW5pdCgpDQoNCiAgICAgICAgfSwgNTAwKQ0KICAgIH0sDQogICAgbWV0aG9kczogew0KICAgICAgICBpbml0KCkgew0KICAgICAgICAgICAgbGV0IG9Cb3ggPSBkb2N1bWVudC5nZXRFbGVtZW50QnlJZCh0aGlzLl9wcm9wcy5ib3gpDQogICAgICAgICAgICAvLyBjb25zb2xlLmxvZygndGhpcy5fcHJvcHMuYm94OicgKyBKU09OLnN0cmluZ2lmeSh0aGlzLl9wcm9wcy5ib3gpKQ0KICAgICAgICAgICAgLy8gY29uc29sZS5sb2coJ29Cb3guY2xpZW50V2lkdGg6JyArIEpTT04uc3RyaW5naWZ5KG9Cb3guY2xpZW50V2lkdGgpKQ0KICAgICAgICAgICAgLy8gY29uc29sZS5sb2coJ29Cb3guY2xpZW50SGVpZ2h0OicgKyBKU09OLnN0cmluZ2lmeShvQm94LmNsaWVudEhlaWdodCkpDQogICAgICAgICAgICBpZiAoIW9Cb3gpIHsNCiAgICAgICAgICAgICAgICByZXR1cm4NCiAgICAgICAgICAgIH0NCiAgICAgICAgICAgIGxldCB3aWR0aCA9IG9Cb3guY2xpZW50V2lkdGgNCiAgICAgICAgICAgIGxldCBoZWlnaHQgPSBvQm94LmNsaWVudEhlaWdodA0KICAgICAgICAgICAgdGhpcy5oZWlnaHQgPSBoZWlnaHQgLyB3aWR0aA0KDQogICAgICAgICAgICAvLyBjb25zb2xlLmxvZygnQUFBQScgKyBKU09OLnN0cmluZ2lmeSh0aGlzLl9wcm9wcykpDQogICAgICAgICAgICBpZiAodGhpcy5pc0RyYWdnYWJsZVJlZ2lvbikgew0KICAgICAgICAgICAgICAgIGRyYWdEaXYodGhpcy5fcHJvcHMuYm94LCB0aGlzLl9wcm9wcy5jaGlsZERyYWcuY2hpbGQuZElELCB0aGlzLl9wcm9wcy5jaGlsZERyYWcuaWQsIHRoaXMuX3Byb3BzLmNoaWxkRHJhZy5jaGlsZC56SUQsIHRoaXMuZHJhZ1ZhbHVlKQ0KICAgICAgICAgICAgfQ0KICAgICAgICAgICAgdGhpcy5zdHlsZU9iamVjdC5oZWlnaHQgPSAoKHRoaXMuX3Byb3BzLmNoaWxkRHJhZy5jaGlsZC5jb29yZGluYXRlcy5sb3dSaWdodC55IC0gdGhpcy5fcHJvcHMuY2hpbGREcmFnLmNoaWxkLmNvb3JkaW5hdGVzLnVwcGVyTGVmdC55KSAvIHRoaXMuaGVpZ2h0KSAqIDEwMCArICclJw0KICAgICAgICAgICAgdGhpcy5zdHlsZU9iamVjdC50b3AgPSAodGhpcy5fcHJvcHMuY2hpbGREcmFnLmNoaWxkLmNvb3JkaW5hdGVzLnVwcGVyTGVmdC55IC8gdGhpcy5oZWlnaHQpICogMTAwICsgJyUnDQogICAgICAgICAgICB0aGlzLnN0eWxlQ29sb3IuYmFja2dyb3VuZENvbG9yID0gdGhpcy5fcHJvcHMuY2hpbGREcmFnLmNvbG9yDQogICAgICAgIH0sDQogICAgICAgIGRyYWdWYWx1ZSh2YWx1ZSkgew0KICAgICAgICAgICAgLy8gdGhpcy5wb3NpdGlvbj12YWx1ZSAvLyBkZWJ1Zw0KDQogICAgICAgICAgICBpZiAodGhpcy5jaGlsZERyYWcuaXNFbnRpdHkpIHsNCiAgICAgICAgICAgICAgICB2YWx1ZS5lbnRpdHlOb2RlSWQgPSB0aGlzLmNoaWxkRHJhZy5lbnRpdHlOb2RlSWQNCiAgICAgICAgICAgICAgICB2YWx1ZS5jb2RlID0gdGhpcy5jaGlsZERyYWcuY29kZQ0KICAgICAgICAgICAgfQ0KDQogICAgICAgICAgICB2YWx1ZS5sb3dSaWdodC55ID0gdmFsdWUubG93UmlnaHQueSAqIHRoaXMuaGVpZ2h0DQogICAgICAgICAgICB2YWx1ZS51cHBlckxlZnQueSA9IHZhbHVlLnVwcGVyTGVmdC55ICogdGhpcy5oZWlnaHQNCiAgICAgICAgICAgIHRoaXMuJGVtaXQoJ2RyYWdDb250YWluZXInLCB2YWx1ZSkNCiAgICAgICAgfSwNCiAgICAgICAgLy8g6YeN572uDQogICAgICAgIGl0ZW1DaGFuZ2UoaXRlbSkgew0KICAgICAgICAgICAgZG9jdW1lbnQuZ2V0RWxlbWVudEJ5SWQoaXRlbS5pZCkuc3R5bGUud2lkdGggPSAoaXRlbS5jaGlsZC5vbGRDb29yZGluYXRlcy5sb3dSaWdodC54ICogMTAwIC0gaXRlbS5jaGlsZC5vbGRDb29yZGluYXRlcy51cHBlckxlZnQueCAqIDEwMCkgKyAnJScNCiAgICAgICAgICAgIGRvY3VtZW50LmdldEVsZW1lbnRCeUlkKGl0ZW0uaWQpLnN0eWxlLmhlaWdodCA9ICgoaXRlbS5jaGlsZC5vbGRDb29yZGluYXRlcy5sb3dSaWdodC55IC0gaXRlbS5jaGlsZC5vbGRDb29yZGluYXRlcy51cHBlckxlZnQueSkgLyB0aGlzLmhlaWdodCkgKiAxMDAgKyAnJScNCiAgICAgICAgICAgIGRvY3VtZW50LmdldEVsZW1lbnRCeUlkKGl0ZW0uaWQpLnN0eWxlLmxlZnQgPSBpdGVtLmNoaWxkLm9sZENvb3JkaW5hdGVzLnVwcGVyTGVmdC54ICogMTAwICsgJyUnDQogICAgICAgICAgICBkb2N1bWVudC5nZXRFbGVtZW50QnlJZChpdGVtLmlkKS5zdHlsZS50b3AgPSAoaXRlbS5jaGlsZC5vbGRDb29yZGluYXRlcy51cHBlckxlZnQueSAvIHRoaXMuaGVpZ2h0KSAqIDEwMCArICclJw0KICAgICAgICB9LA0KICAgICAgICAvLyDph43nva7mk43kvZwNCiAgICAgICAgcmVzZXRJdGVtKGl0ZW0pIHsNCiAgICAgICAgICAgIHRoaXMuJGVtaXQoJ29rUmVzZXQnLCBpdGVtKQ0KICAgICAgICB9LA0KICAgICAgICAvLyDpgInkuK0NCiAgICAgICAgcGl0Y2hVcChpdGVtKSB7DQogICAgICAgICAgICBpZiAoYXJndW1lbnRzWzFdICE9PSAwKSB7DQogICAgICAgICAgICAgICAgZG9jdW1lbnQuZ2V0RWxlbWVudEJ5SWQoYXJndW1lbnRzWzFdKS5zdHlsZS5vcGFjaXR5ID0gMC43DQogICAgICAgICAgICB9DQogICAgICAgICAgICBkb2N1bWVudC5nZXRFbGVtZW50QnlJZChpdGVtLmNoaWxkLmRJRCkuc3R5bGUub3BhY2l0eSA9IDAuOTUNCiAgICAgICAgfQ0KICAgIH0NCn0NCg=="}, {"version": 3, "sources": ["dragContainNS2.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAsFA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "dragContainNS2.vue", "sourceRoot": "src/components/administrative/apps/sns/planeSetting/sitePlan", "sourcesContent": ["<style lang=\"scss\">\r\n.dragContain {\r\n    width: 20px;\r\n    height: 20px;\r\n    position: absolute\r\n}\r\n\r\n.drag {\r\n    opacity: 0.7;\r\n    width: 100%;\r\n    height: 100%;\r\n    border: 1px solid #bbcabf;\r\n    box-shadow: 0 3px 6px rgba(0, 0, 0, .2);\r\n    word-break: break-all;\r\n    overflow: hidden;\r\n    background-color: #e3c1c1;\r\n    text-align: center;\r\n    font-size: 16px;\r\n}\r\n\r\n.circleBase {\r\n    opacity: 0.7;\r\n    border-radius: 50%;\r\n    width: 100%;\r\n    height: 100%;\r\n    border: 1px solid #bbcabf;\r\n    box-shadow: 0 3px 6px rgba(0, 0, 0, .2);\r\n    word-break: break-all;\r\n    overflow: hidden;\r\n    text-align: center;\r\n    background: #00afff !important;\r\n    color: white;\r\n    display: flex;\r\n    align-items: center;\r\n}\r\n\r\n.dragZoom {\r\n    position: absolute;\r\n    width: 10px;\r\n    height: 10px;\r\n    background-color: #c5a2a2;\r\n    right: 0;\r\n    bottom: 0;\r\n}\r\n\r\n.cross {\r\n    position: absolute;\r\n    height: 40px;\r\n    width: 40px;\r\n    top: -20px;\r\n    left: -20px;\r\n}\r\n\r\n.reset {\r\n    height: 24px;\r\n    width: 24px;\r\n    cursor: pointer;\r\n}\r\n\r\n.ivu-poptip-confirm {\r\n    position: absolute;\r\n    top: -12px;\r\n    right: -12px;\r\n}\r\n</style>\r\n<template>\r\n<div :id=\"childDrag.id\" class=\"animated dragContain zoomIn\" v-bind:style=\"styleObject\">\r\n    <div :id=\"childDrag.child.dID\" :class=\"[childDrag.isEntity?'circleBase':'drag']\" v-bind:style=\"styleColor\">\r\n        <p style=\"position: absolute;width: 100%;\">{{childDrag.name}}</p>\r\n    </div>\r\n    <div :id=\"childDrag.child.zID\" class=\"dragZoom\"></div>\r\n    <img class=\"cross\" :src=\"cross\" />\r\n    <!--\r\n    <Poptip\r\n      placement=\"bottom-end\"\r\n      confirm\r\n      @on-ok=\"resetItem(childDrag)\"\r\n      :title=\"$t('lang.common.whetherToReset')\">\r\n      <img class=\"reset\" :src=\"reset\" :title=\"$t('lang.common.reset')\"/>\r\n    </Poptip>\r\n    -->\r\n\r\n</div>\r\n</template>\r\n\r\n<script>\r\nimport dragDiv from '@/components/administrative/common/dragDiv'\r\nimport cross from '@/components/administrative/common/images/cross.png'\r\nimport reset from '@/components/administrative/common/images/reset.png'\r\n\r\nexport default {\r\n    props: ['childDrag', 'box', 'name', 'isDraggableRegion'],\r\n    data() {\r\n        // setTimeout(() => {\r\n        //   let oBox = document.getElementById(this._props.box)\r\n        //   console.log('this._props.box:'+JSON.stringify(this._props.box))\r\n        //   console.log('oBox.clientWidth:'+JSON.stringify(oBox.clientWidth))\r\n        //   console.log('oBox.clientHeight:'+JSON.stringify(oBox.clientHeight))\r\n        //   if (!oBox) {\r\n        //     return\r\n        //   }\r\n        //   let width = oBox.clientWidth\r\n        //   let height = oBox.clientHeight\r\n        //   this.height = height / width\r\n        // }, 300)\r\n\r\n        return {\r\n            position: null,\r\n            height: 0,\r\n            styleObject: {\r\n                width: (this._props.childDrag.child.coordinates.lowRight.x * 100 - this._props.childDrag.child.coordinates.upperLeft.x * 100) + '%',\r\n                height: 0,\r\n                left: this._props.childDrag.child.coordinates.upperLeft.x * 100 + '%',\r\n                //width:'10%',\r\n                //height: '10%',\r\n                //left: '10%',\r\n                top: 0,\r\n                'z-index': 1,\r\n                display: this._props.childDrag.isShow ? '' : 'none'\r\n            },\r\n            cross: cross,\r\n            reset: reset,\r\n            styleColor: {\r\n              backgroundColor: ''\r\n            }\r\n        }\r\n    },\r\n    watch: {\r\n        isDraggableRegion() {\r\n              this.init()         \r\n        }\r\n    },\r\n\r\n    mounted() {\r\n        this.styleObject.top = '5%'\r\n        setTimeout(() => {\r\n\r\n            this.init()\r\n\r\n        }, 500)\r\n    },\r\n    methods: {\r\n        init() {\r\n            let oBox = document.getElementById(this._props.box)\r\n            // console.log('this._props.box:' + JSON.stringify(this._props.box))\r\n            // console.log('oBox.clientWidth:' + JSON.stringify(oBox.clientWidth))\r\n            // console.log('oBox.clientHeight:' + JSON.stringify(oBox.clientHeight))\r\n            if (!oBox) {\r\n                return\r\n            }\r\n            let width = oBox.clientWidth\r\n            let height = oBox.clientHeight\r\n            this.height = height / width\r\n\r\n            // console.log('AAAA' + JSON.stringify(this._props))\r\n            if (this.isDraggableRegion) {\r\n                dragDiv(this._props.box, this._props.childDrag.child.dID, this._props.childDrag.id, this._props.childDrag.child.zID, this.dragValue)\r\n            }\r\n            this.styleObject.height = ((this._props.childDrag.child.coordinates.lowRight.y - this._props.childDrag.child.coordinates.upperLeft.y) / this.height) * 100 + '%'\r\n            this.styleObject.top = (this._props.childDrag.child.coordinates.upperLeft.y / this.height) * 100 + '%'\r\n            this.styleColor.backgroundColor = this._props.childDrag.color\r\n        },\r\n        dragValue(value) {\r\n            // this.position=value // debug\r\n\r\n            if (this.childDrag.isEntity) {\r\n                value.entityNodeId = this.childDrag.entityNodeId\r\n                value.code = this.childDrag.code\r\n            }\r\n\r\n            value.lowRight.y = value.lowRight.y * this.height\r\n            value.upperLeft.y = value.upperLeft.y * this.height\r\n            this.$emit('dragContainer', value)\r\n        },\r\n        // 重置\r\n        itemChange(item) {\r\n            document.getElementById(item.id).style.width = (item.child.oldCoordinates.lowRight.x * 100 - item.child.oldCoordinates.upperLeft.x * 100) + '%'\r\n            document.getElementById(item.id).style.height = ((item.child.oldCoordinates.lowRight.y - item.child.oldCoordinates.upperLeft.y) / this.height) * 100 + '%'\r\n            document.getElementById(item.id).style.left = item.child.oldCoordinates.upperLeft.x * 100 + '%'\r\n            document.getElementById(item.id).style.top = (item.child.oldCoordinates.upperLeft.y / this.height) * 100 + '%'\r\n        },\r\n        // 重置操作\r\n        resetItem(item) {\r\n            this.$emit('okReset', item)\r\n        },\r\n        // 选中\r\n        pitchUp(item) {\r\n            if (arguments[1] !== 0) {\r\n                document.getElementById(arguments[1]).style.opacity = 0.7\r\n            }\r\n            document.getElementById(item.child.dID).style.opacity = 0.95\r\n        }\r\n    }\r\n}\r\n</script>\r\n"]}]}