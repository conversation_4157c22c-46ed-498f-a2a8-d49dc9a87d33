{"remainingRequest": "E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\src\\components\\administrative\\apps\\sns\\monitoring\\m6\\trajectories\\searchModal.vue?vue&type=script&lang=js&", "dependencies": [{"path": "E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\src\\components\\administrative\\apps\\sns\\monitoring\\m6\\trajectories\\searchModal.vue", "mtime": 1754362736682}, {"path": "E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\node_modules\\string-replace-loader\\index.js", "mtime": 499162500000}, {"path": "E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgcmVzZXRJY29uIGZyb20gIkAvYXNzZXRzL2ltYWdlcy9pY19yZXNldC5zdmciOw0KaW1wb3J0IHNlYXJjaEljb24gZnJvbSAiQC9hc3NldHMvaW1hZ2VzL2ljX3NlYXJjaC5zdmciOw0KaW1wb3J0IENvbmZpZyBmcm9tICJAL2NvbW1vbi9jb25maWciOw0KbGV0IG1vbWVudCA9IHJlcXVpcmUoIm1vbWVudCIpOw0KDQpleHBvcnQgZGVmYXVsdCB7DQogIHByb3BzOiBbInNlcnZpY2VDb2RlTWVudSIsICJzdGF0aW9uTWVudSIsICJvYmplY3RNZW51IiwgIm9wZW5TZWFyY2hTdGF0dXMiXSwNCiAgZGF0YSgpIHsNCiAgICBjb25zdCB2YWxpZGF0ZU9iamVjdE5hbWUgPSAocnVsZSwgdmFsdWUsIGNhbGxiYWNrKSA9PiB7DQogICAgICBsZXQgX3RoaXMgPSB0aGlzOw0KICAgICAgaWYgKHZhbHVlLmxlbmd0aCA9PSAwKSB7DQogICAgICAgIGNhbGxiYWNrKA0KICAgICAgICAgIG5ldyBFcnJvcigNCiAgICAgICAgICAgIHRoaXMuJHQoIkxvY2FsZVN0cmluZy5NMDAwMTAiLCB7DQogICAgICAgICAgICAgIDA6IHRoaXMuJHQoIkxvY2FsZVN0cmluZy5MMDAwOTIiKSwNCiAgICAgICAgICAgIH0pDQogICAgICAgICAgKQ0KICAgICAgICApOw0KICAgICAgfQ0KICAgICAgZWxzZSBpZiAodmFsdWUuaW5jbHVkZXMoImFsbCIpICYmIF90aGlzLnNlYXJjaEZvcm1WYWxpZGF0ZS5zdGF0aW9uTmFtZS5pbmNsdWRlcygiYWxsIikpIHsNCiAgICAgICAgY2FsbGJhY2sobmV3IEVycm9yKA0KICAgICAgICAgIHRoaXMuJHQoIkxvY2FsZVN0cmluZy5XMzAwMTQiKQ0KICAgICAgICApKTsNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIGNhbGxiYWNrKCkNCiAgICAgIH0NCiAgICB9Ow0KDQogICAgY29uc3QgdmFsaWRhdGVTdGF0aW9uTmFtZSA9IChydWxlLCB2YWx1ZSwgY2FsbGJhY2spID0+IHsNCiAgICAgIGxldCBfdGhpcyA9IHRoaXM7DQogICAgICBpZiAodmFsdWUubGVuZ3RoID09IDApIHsNCiAgICAgICAgY2FsbGJhY2soDQogICAgICAgICAgbmV3IEVycm9yKA0KICAgICAgICAgICAgdGhpcy4kdCgiTG9jYWxlU3RyaW5nLk0wMDAxMCIsIHsNCiAgICAgICAgICAgICAgMDogdGhpcy4kdCgiTG9jYWxlU3RyaW5nLkwwMDE2MSIpLA0KICAgICAgICAgICAgfSkNCiAgICAgICAgICApDQogICAgICAgICk7DQogICAgICB9IGVsc2UgaWYgKHZhbHVlLmluY2x1ZGVzKCJhbGwiKSAmJiBfdGhpcy5zZWFyY2hGb3JtVmFsaWRhdGUub2JqZWN0TmFtZS5pbmNsdWRlcygiYWxsIikpIHsNCiAgICAgICAgY2FsbGJhY2sobmV3IEVycm9yKA0KICAgICAgICAgIHRoaXMuJHQoIkxvY2FsZVN0cmluZy5XMzAwMTQiKQ0KICAgICAgICApKTsNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIGNhbGxiYWNrKCkNCiAgICAgIH0NCiAgICB9Ow0KICAgIHJldHVybiB7DQogICAgICBzZWFyY2hGaXJzdFRpbWU6IHRydWUsDQogICAgICBkaWFibGVkU3RhdGlvbk5hbWVBbGw6IGZhbHNlLA0KICAgICAgZGlhYmxlZE9iamVjdE5hbWVBbGw6IGZhbHNlLA0KICAgICAgc2VhcmNoU3RhcnREYXRlOiAiIiwNCiAgICAgIHNlYXJjaEVuZERhdGU6ICIiLA0KICAgICAgbW9kYWxTZWFyY2g6IGZhbHNlLA0KICAgICAgcmVzZXRJY29uOiByZXNldEljb24sDQogICAgICBzZWFyY2hJY29uOiBzZWFyY2hJY29uLA0KICAgICAgZGVmYXVsdFNlbGVjdGlvbjogW10sDQogICAgICBub3RGb3VuZFRleHQ6IHRoaXMuJHQoIkxvY2FsZVN0cmluZy5MMDAwODEiKSwNCiAgICAgIHJvd0hlaWdodDogInJvdy1oZWlnaHQiLA0KICAgICAgc2VhcmNoRm9ybVZhbGlkYXRlOiB7DQogICAgICAgIG9iamVjdE5hbWU6IFsnYWxsJ10sDQogICAgICAgIHN0YXJ0c0F0OiB0aGlzLmdldFN0YXJ0c0F0RGVmYXVsdCgpLA0KICAgICAgICBmaW5pc2hlc0F0OiB0aGlzLmdldEZpbmlzaGVzQXREZWZhdWx0KCksDQogICAgICAgIHN0YXRpb25OYW1lOiBbJ2FsbCddLA0KICAgICAgICBub1NpZ25hbFJlY29yZDogZmFsc2UsDQogICAgICB9LA0KICAgICAgc2VhcmNoRm9ybVZhbGlkYXRlU2VhcmNoOiBudWxsLA0KICAgICAgZGF0ZU9wdGlvbnM6IHsNCiAgICAgICAgZGlzYWJsZWREYXRlKGRhdGUpIHsNCiAgICAgICAgICBsZXQgc3RhcnRzQXQgPSBuZXcgRGF0ZSgNCiAgICAgICAgICAgIERhdGUubm93KCkgLSAxMDAwICogNjAgKiA2MCAqIDI0ICogQ29uZmlnLlNFQVJDSF9FTkFCTEVfREFURVMNCiAgICAgICAgICApOw0KICAgICAgICAgIHN0YXJ0c0F0LnNldEhvdXJzKDAsIDAsIDAsIDApOw0KICAgICAgICAgIGxldCBmaW5pc2hlc0F0ID0gbmV3IERhdGUoRGF0ZS5ub3coKSArIDEwMDAgKiA2MCAqIDYwICogMjQpOw0KICAgICAgICAgIGZpbmlzaGVzQXQuc2V0SG91cnMoMCwgMCwgMCwgMCk7DQoNCiAgICAgICAgICByZXR1cm4gZGF0ZSAmJiAoZGF0ZSA8IHN0YXJ0c0F0IHx8IGRhdGUgPiBmaW5pc2hlc0F0KTsNCiAgICAgICAgfSwNCiAgICAgIH0sDQogICAgICBzZWFyY2hSdWxlVmFsaWRhdGU6DQogICAgICB7DQogICAgICAgIHN0YXJ0c0F0OiBbDQogICAgICAgICAgew0KICAgICAgICAgICAgcmVxdWlyZWQ6IHRydWUsDQogICAgICAgICAgICBtZXNzYWdlOiB0aGlzLiR0KCJMb2NhbGVTdHJpbmcuTTAwMDEwIiwgew0KICAgICAgICAgICAgICAwOiB0aGlzLiR0KCJMb2NhbGVTdHJpbmcuTDAwMTQxIiksDQogICAgICAgICAgICB9KSwNCiAgICAgICAgICB9LA0KICAgICAgICAgIHsNCiAgICAgICAgICAgIHR5cGU6ICJkYXRlIiwNCiAgICAgICAgICAgIHZhbGlkYXRvcjogKHJ1bGUsIHZhbHVlKSA9Pg0KICAgICAgICAgICAgICAhdmFsdWUgfHwNCiAgICAgICAgICAgICAgIXRoaXMuc2VhcmNoRm9ybVZhbGlkYXRlLmZpbmlzaGVzQXQgfHwNCiAgICAgICAgICAgICAgdmFsdWUgPD0gdGhpcy5zZWFyY2hGb3JtVmFsaWRhdGUuZmluaXNoZXNBdCwNCiAgICAgICAgICAgIG1lc3NhZ2U6IHRoaXMuJHQoIkxvY2FsZVN0cmluZy5XMDAwMzYiKSwNCiAgICAgICAgICB9LA0KICAgICAgICBdLA0KICAgICAgICBmaW5pc2hlc0F0OiBbDQogICAgICAgICAgew0KICAgICAgICAgICAgcmVxdWlyZWQ6IHRydWUsDQogICAgICAgICAgICBtZXNzYWdlOiB0aGlzLiR0KCJMb2NhbGVTdHJpbmcuTTAwMDEwIiwgew0KICAgICAgICAgICAgICAwOiB0aGlzLiR0KCJMb2NhbGVTdHJpbmcuTDAwMTQyIiksDQogICAgICAgICAgICB9KSwNCiAgICAgICAgICB9LA0KICAgICAgICAgIHsNCiAgICAgICAgICAgIHR5cGU6ICJkYXRlIiwNCiAgICAgICAgICAgIHZhbGlkYXRvcjogKHJ1bGUsIHZhbHVlKSA9Pg0KICAgICAgICAgICAgICAhdmFsdWUgfHwNCiAgICAgICAgICAgICAgIXRoaXMuc2VhcmNoRm9ybVZhbGlkYXRlLnN0YXJ0c0F0IHx8DQogICAgICAgICAgICAgIHZhbHVlID49IHRoaXMuc2VhcmNoRm9ybVZhbGlkYXRlLnN0YXJ0c0F0LA0KICAgICAgICAgICAgbWVzc2FnZTogdGhpcy4kdCgiTG9jYWxlU3RyaW5nLlcwMDAzNiIpLA0KICAgICAgICAgIH0sDQogICAgICAgIF0sDQogICAgICAgIG9iamVjdE5hbWU6IFsNCiAgICAgICAgICB7IHZhbGlkYXRvcjogdmFsaWRhdGVPYmplY3ROYW1lLCB0cmlnZ2VyOiAnYmx1cicgfQ0KICAgICAgICBdLA0KICAgICAgICBzdGF0aW9uTmFtZTogWw0KICAgICAgICAgIHsgdmFsaWRhdG9yOiB2YWxpZGF0ZVN0YXRpb25OYW1lLCB0cmlnZ2VyOiAnYmx1cicgfQ0KICAgICAgICBdLA0KDQogICAgICB9DQogICAgfTsNCiAgfSwNCiAgY29tcHV0ZWQ6IHsNCg0KICAgIG9wZW5Nb2RhbCgpIHsNCiAgICAgIGlmICh0aGlzLm9wZW5TZWFyY2hTdGF0dXMpIHsNCiAgICAgICAgdGhpcy5vcGVuU2VhcmNoT2JqZWN0KCk7DQogICAgICB9DQogICAgICByZXR1cm4gbnVsbDsNCiAgICB9DQogIH0sDQogIGNyZWF0ZWQoKSB7DQogICAgdGhpcy5zZWFyY2hGb3JtVmFsaWRhdGVTZWFyY2ggPSBKU09OLnBhcnNlKA0KICAgICAgSlNPTi5zdHJpbmdpZnkodGhpcy5zZWFyY2hGb3JtVmFsaWRhdGUpDQogICAgKTsNCiAgfSwNCiAgYXN5bmMgbW91bnRlZCgpIHsNCiAgfSwNCiAgbWV0aG9kczogew0KICAgIGNoYW5nZVN0YXRpb24oZGF0YSkgew0KICAgICAgaWYgKHRoaXMuc3RhdGlvbk1lbnUpIHsNCiAgICAgICAgaWYgKHRoaXMuc2VhcmNoRm9ybVZhbGlkYXRlLnN0YXRpb25OYW1lLmluY2x1ZGVzKCdhbGwnKSkgew0KICAgICAgICAgIHRoaXMuc3RhdGlvbk1lbnUuZm9yRWFjaChpdGVtID0+IHsgaXRlbS5kaXNhYmxlZCA9IHRydWUgfSk7DQogICAgICAgICAgdGhpcy5zZWFyY2hGb3JtVmFsaWRhdGUuc3RhdGlvbk5hbWUgPSBbJ2FsbCddOw0KICAgICAgICB9DQogICAgICAgIGVsc2Ugew0KICAgICAgICAgIHRoaXMuc3RhdGlvbk1lbnUuZm9yRWFjaChpdGVtID0+IHsgaXRlbS5kaXNhYmxlZCA9IGZhbHNlIH0pOw0KICAgICAgICAgIGlmIChkYXRhLmxlbmd0aCA+IDIwKSB7DQogICAgICAgICAgICBkYXRhLnBvcCgpOw0KICAgICAgICAgIH0NCiAgICAgICAgfQ0KICAgICAgfQ0KICAgIH0sDQoNCiAgICBjaGFuZ2VPYmplY3QoZGF0YSkgew0KICAgICAgaWYgKHRoaXMub2JqZWN0TWVudSkgew0KICAgICAgICBpZiAodGhpcy5zZWFyY2hGb3JtVmFsaWRhdGUub2JqZWN0TmFtZS5pbmNsdWRlcygnYWxsJykpIHsNCiAgICAgICAgICB0aGlzLm9iamVjdE1lbnUuZm9yRWFjaChpdGVtID0+IHsgaXRlbS5kaXNhYmxlZCA9IHRydWUgfSk7DQogICAgICAgICAgdGhpcy5zZWFyY2hGb3JtVmFsaWRhdGUub2JqZWN0TmFtZSA9IFsnYWxsJ107DQogICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgdGhpcy5vYmplY3RNZW51LmZvckVhY2goaXRlbSA9PiB7IGl0ZW0uZGlzYWJsZWQgPSBmYWxzZSB9KTsNCiAgICAgICAgICBpZiAoZGF0YS5sZW5ndGggPiAyMCkgew0KICAgICAgICAgICAgZGF0YS5wb3AoKTsNCiAgICAgICAgICB9DQogICAgICAgIH0NCiAgICAgIH0NCiAgICB9LA0KDQogICAgcGFyc2VUaW1lKGRheSkgew0KICAgICAgbGV0IHRpbWVkaWZmZXJlbmNlID0gKG5ldyBEYXRlKCkuZ2V0VGltZXpvbmVPZmZzZXQoKSAvIDYwKSAqIC0xOw0KICAgICAgbGV0IHRtcE9mZnNldERheSA9IG1vbWVudChkYXksICJZWVlZLU1NLUREIEhIOm1tOnNzIikNCiAgICAgICAgLmFkZCh0aW1lZGlmZmVyZW5jZSwgImhvdXJzIikNCiAgICAgICAgLmZvcm1hdCgiWVlZWS1NTS1ERCBISDptbTpzcyIpOw0KICAgICAgcmV0dXJuIHRtcE9mZnNldERheTsNCiAgICB9LA0KICAgIHRyYW5zbGF0ZUNvbmRpdGlvbihpdGVtLCB0eXBlKSB7DQogICAgICBpZiAoaXRlbSA9PSB1bmRlZmluZWQpIHsNCiAgICAgICAgcmV0dXJuICcnOw0KICAgICAgfQ0KICAgICAgaWYgKGl0ZW0gPT0gImFsbCIpIHsNCiAgICAgICAgcmV0dXJuIHRoaXMuJHQoIkxvY2FsZVN0cmluZy5EMDAwMDIiKTsNCiAgICAgIH0NCg0KICAgICAgbGV0IHRyYW5zbGF0ZU5hbWUgPSAiIjsNCiAgICAgIHN3aXRjaCAodHlwZSkgew0KICAgICAgICBjYXNlICJub1NpZ25hbFJlY29yZCI6DQogICAgICAgICAgdHJhbnNsYXRlTmFtZSA9IGl0ZW0gPyB0aGlzLiR0KCJMb2NhbGVTdHJpbmcuRDMwMDI5IikgOiB0aGlzLiR0KCJMb2NhbGVTdHJpbmcuRDMwMDMwIik7DQogICAgICAgICAgYnJlYWs7DQogICAgICAgIGNhc2UgIm9iamVjdE5hbWUiOg0KICAgICAgICAgIHRyYW5zbGF0ZU5hbWUgPSBpdGVtLmxlbmd0aCA+IDEgPyB0aGlzLm9iamVjdE1lbnUuZmluZChpID0+IGkuY29kZSA9PSBpdGVtWzBdKS5uYW1lICsgIiAuLi4gIiA6IHRoaXMub2JqZWN0TWVudS5maW5kKGkgPT4gaS5jb2RlID09IGl0ZW0pLm5hbWU7DQogICAgICAgICAgYnJlYWs7DQogICAgICAgIGNhc2UgInN0YXRpb25OYW1lIjoNCiAgICAgICAgICB0cmFuc2xhdGVOYW1lID0gaXRlbS5sZW5ndGggPiAxID8gdGhpcy5zdGF0aW9uTWVudS5maW5kKGkgPT4gaS5zaWQgPT0gaXRlbVswXSkubmFtZSArICIgLi4uICIgOiB0aGlzLnN0YXRpb25NZW51LmZpbmQoaSA9PiBpLnNpZCA9PSBpdGVtKS5uYW1lOw0KICAgICAgICAgIGJyZWFrOw0KDQogICAgICB9DQogICAgICByZXR1cm4gdHJhbnNsYXRlTmFtZTsNCiAgICB9LA0KICAgIGhhbmRsZVJlc2V0KG5hbWUpIHsNCiAgICAgIHRoaXMuJHJlZnNbbmFtZV0ucmVzZXRGaWVsZHMoKTsNCiAgICAgIHRoaXMuc2VhcmNoRm9ybVZhbGlkYXRlU2VhcmNoID0gdGhpcy5zZWFyY2hGb3JtVmFsaWRhdGU7DQogICAgICB0aGlzLnNlYXJjaEhhbmRsZVN1Ym1pdCgic2VhcmNoRm9ybVZhbGlkYXRlIik7DQogICAgfSwNCiAgICBjYW5jZWxNb2RhbCgpIHsNCiAgICAgIHRoaXMuJHJlZnNbInNlYXJjaEZvcm1WYWxpZGF0ZSJdLnJlc2V0RmllbGRzKCk7DQogICAgICBpZiAoIXRoaXMuc2VhcmNoRmlyc3RUaW1lKSB7DQogICAgICAgIGxldCBuZXdTdGFydHNBdCA9IG5ldyBEYXRlKHRoaXMuc2VhcmNoRm9ybVZhbGlkYXRlU2VhcmNoLnN0YXJ0ZWRUaW1lKTsNCiAgICAgICAgbGV0IG5ld0ZpbmlzaHNBdCA9IG5ldyBEYXRlKHRoaXMuc2VhcmNoRm9ybVZhbGlkYXRlU2VhcmNoLmVuZGVkVGltZSk7DQogICAgICAgIHRoaXMuc2VhcmNoRm9ybVZhbGlkYXRlID0gSlNPTi5wYXJzZSgNCiAgICAgICAgICBKU09OLnN0cmluZ2lmeSh0aGlzLnNlYXJjaEZvcm1WYWxpZGF0ZVNlYXJjaCkNCiAgICAgICAgKTsNCiAgICAgICAgdGhpcy5zZWFyY2hGb3JtVmFsaWRhdGUuc3RhcnRlZFRpbWUgPSBuZXdTdGFydHNBdDsNCiAgICAgICAgdGhpcy5zZWFyY2hGb3JtVmFsaWRhdGUuZW5kZWRUaW1lID0gbmV3RmluaXNoc0F0Ow0KICAgICAgfQ0KDQogICAgICB0aGlzLm1vZGFsU2VhcmNoID0gZmFsc2U7DQogICAgICB0aGlzLiRlbWl0KCJjbG9zZVNlYXJjaE1vZGFsIik7DQogICAgfSwNCiAgICBvcGVuU2VhcmNoT2JqZWN0KCkgew0KICAgICAgdGhpcy5tb2RhbFNlYXJjaCA9IHRydWU7DQogICAgfSwNCg0KICAgIGdldFN0YXJ0c0F0RGVmYXVsdCgpIHsNCiAgICAgIGxldCByZXN1bHQgPSBuZXcgRGF0ZSgNCiAgICAgICAgRGF0ZS5ub3coKSAtDQogICAgICAgIDEwMDAgKiAzNjAwICogMjQgKiAoQ29uZmlnLlNFQVJDSF9QT1NUR1JFU1FMX0RBVEFfREFURVMgLSAxKQ0KICAgICAgKTsNCiAgICAgIHJlc3VsdC5zZXRIb3VycygwLCAwLCAwLCAwKTsNCiAgICAgIHJldHVybiByZXN1bHQ7DQogICAgfSwNCiAgICBnZXRGaW5pc2hlc0F0RGVmYXVsdCgpIHsNCiAgICAgIGxldCByZXN1bHQgPSBuZXcgRGF0ZShEYXRlLm5vdygpICsgMTAwMCAqIDM2MDAgKiAyNCk7DQogICAgICByZXN1bHQuc2V0SG91cnMoMCwgMCwgMCwgMCk7DQogICAgICByZXR1cm4gcmVzdWx0Ow0KICAgIH0sDQogICAgc3RhcnRzQXRDaGFuZ2UoKSB7DQogICAgICB0aGlzLiRyZWZzWyJzZWFyY2hGb3JtVmFsaWRhdGUiXS52YWxpZGF0ZUZpZWxkKCJmaW5pc2hlc0F0Iik7DQogICAgfSwNCiAgICBmaW5pc2hlc0F0Q2hhbmdlKCkgew0KICAgICAgdGhpcy4kcmVmc1sic2VhcmNoRm9ybVZhbGlkYXRlIl0udmFsaWRhdGVGaWVsZCgic3RhcnRzQXQiKTsNCiAgICB9LA0KICAgIHNlYXJjaEhhbmRsZVN1Ym1pdChuYW1lKSB7DQogICAgICB0aGlzLiRyZWZzW25hbWVdLnZhbGlkYXRlKCh2YWxpZCkgPT4gew0KICAgICAgICBpZiAodmFsaWQpIHsNCiAgICAgICAgICB0aGlzLnNlYXJjaEZvcm1WYWxpZGF0ZVNlYXJjaCA9IEpTT04ucGFyc2UoDQogICAgICAgICAgICBKU09OLnN0cmluZ2lmeSh0aGlzLnNlYXJjaEZvcm1WYWxpZGF0ZSkNCiAgICAgICAgICApOw0KICAgICAgICAgIHRoaXMuc2VhcmNoRmlyc3RUaW1lID0gZmFsc2UNCiAgICAgICAgICB0aGlzLiRlbWl0KCJzZWFyY2hSZXF1ZXN0Iiwgew0KICAgICAgICAgICAgaXNVc2VyU3VibWl0OiB0cnVlLA0KICAgICAgICAgICAgc2VhcmNoUGFyYW1zOiB0aGlzLmdldFNlYXJjaFBhcmFtcygpLA0KICAgICAgICAgIH0pOw0KICAgICAgICAgIHRoaXMubW9kYWxTZWFyY2ggPSBmYWxzZTsNCg0KICAgICAgICB9DQogICAgICB9KTsNCiAgICB9LA0KICAgIGdldFNlYXJjaFBhcmFtcygpIHsNCiAgICAgIGxldCBzdHIgPSAiIjsNCg0KICAgICAgaWYgKHRoaXMuc2VhcmNoRm9ybVZhbGlkYXRlLnN0YXJ0c0F0ICE9PSAiIiAmJiB0aGlzLnNlYXJjaEZvcm1WYWxpZGF0ZS5maW5pc2hlc0F0ICE9PSAiIikgew0KICAgICAgICBsZXQgdGltZWRpZmZlcmVuY2VTdGFydERheSA9DQogICAgICAgICAgKG5ldyBEYXRlKCkuZ2V0VGltZXpvbmVPZmZzZXQoKSAvIDYwKSAqIC0xIC0gODsNCiAgICAgICAgbGV0IHRtcE9mZnNldFN0YXJ0RGF5ID0gbW9tZW50KA0KICAgICAgICAgIHRoaXMuc2VhcmNoRm9ybVZhbGlkYXRlLnN0YXJ0c0F0LA0KICAgICAgICAgICJZWVlZLU1NLUREIEhIOm1tOnNzIg0KICAgICAgICApDQogICAgICAgICAgLmFkZCh0aW1lZGlmZmVyZW5jZVN0YXJ0RGF5ICogLTEsICJob3VycyIpDQogICAgICAgICAgLmZvcm1hdCgiWVlZWS1NTS1ERCBISDptbTpzcyIpOw0KDQogICAgICAgIGxldCBzdGFydHNBdCA9IHRtcE9mZnNldFN0YXJ0RGF5Ow0KDQogICAgICAgIGxldCB0aW1lZGlmZmVyZW5jZWZpbmlzaERheSA9DQogICAgICAgICAgKG5ldyBEYXRlKCkuZ2V0VGltZXpvbmVPZmZzZXQoKSAvIDYwKSAqIC0xIC0gODsNCiAgICAgICAgbGV0IHRtcE9mZnNldEZpbmlzaERheSA9IG1vbWVudCgNCiAgICAgICAgICB0aGlzLnNlYXJjaEZvcm1WYWxpZGF0ZS5maW5pc2hlc0F0LA0KICAgICAgICAgICJZWVlZLU1NLUREIEhIOm1tOnNzIg0KICAgICAgICApDQogICAgICAgICAgLmFkZCh0aW1lZGlmZmVyZW5jZWZpbmlzaERheSAqIC0xLCAiaG91cnMiKQ0KICAgICAgICAgIC5mb3JtYXQoIllZWVktTU0tREQgSEg6bW06c3MiKTsNCiAgICAgICAgbGV0IGZpbmlzaGVzQXQgPSBtb21lbnQodG1wT2Zmc2V0RmluaXNoRGF5KS5mb3JtYXQoDQogICAgICAgICAgIllZWVktTU0tREQgSEg6bW06c3MiDQogICAgICAgICk7DQoNCiAgICAgICAgc3RyICs9ICJwb3NpdGlvblRpbWUgYmV0d2VlbiAnIiArIHN0YXJ0c0F0ICsgIiwiICsgZmluaXNoZXNBdCArICInICI7DQogICAgICAgIHRoaXMuc2VhcmNoU3RhcnREYXRlID0gc3RhcnRzQXQ7DQogICAgICAgIHRoaXMuc2VhcmNoRW5kRGF0ZSA9IGZpbmlzaGVzQXQ7DQogICAgICB9DQoNCiAgICAgIGlmICh0aGlzLnNlYXJjaEZvcm1WYWxpZGF0ZS5vYmplY3ROYW1lLmxlbmd0aCA+IDAgJiYgIXRoaXMuc2VhcmNoRm9ybVZhbGlkYXRlLm9iamVjdE5hbWUuaW5jbHVkZXMoImFsbCIpKSB7DQogICAgICAgIHN0ciArPQ0KICAgICAgICAgICIgYW5kIG9iamVjdC5jb2RlIGluICIgKw0KICAgICAgICAgIHRoaXMuc2VhcmNoRm9ybVZhbGlkYXRlLm9iamVjdE5hbWUuam9pbigiLCIpICsNCiAgICAgICAgICAiICI7DQogICAgICB9DQogICAgICBpZiAodGhpcy5zZWFyY2hGb3JtVmFsaWRhdGUuc3RhdGlvbk5hbWUubGVuZ3RoID4gMCAmJiAhdGhpcy5zZWFyY2hGb3JtVmFsaWRhdGUuc3RhdGlvbk5hbWUuaW5jbHVkZXMoImFsbCIpKSB7DQogICAgICAgIHN0ciArPQ0KICAgICAgICAgICIgYW5kIHRvU3RhdGlvbi5zaWQgaW4gIiArDQogICAgICAgICAgdGhpcy5zZWFyY2hGb3JtVmFsaWRhdGUuc3RhdGlvbk5hbWUuam9pbigiLCIpICsNCiAgICAgICAgICAiICI7DQogICAgICB9DQoNCiAgICAgIGlmICghdGhpcy5zZWFyY2hGb3JtVmFsaWRhdGUubm9TaWduYWxSZWNvcmQpIHsNCiAgICAgICAgc3RyICs9DQogICAgICAgICAgIiBhbmQgdG9TdGF0aW9uLnNpZCBuZSBvZmZsaW5lIjsNCiAgICAgIH0NCiAgICAgIHJldHVybiB7IHNlYXJjaDogc3RyIH07DQogICAgfSwNCiAgICBleHBvcnRIYW5kbGVTdWJtaXQoKSB7DQogICAgICB0aGlzLiRlbWl0KCJleHBvcnRDb25maXJtIik7DQogICAgfSwNCiAgfSwNCn07DQo="}, {"version": 3, "sources": ["searchModal.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAwHA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "searchModal.vue", "sourceRoot": "src/components/administrative/apps/sns/monitoring/m6/trajectories", "sourcesContent": ["<template>\r\n  <div>\r\n    <Row style=\"margin: 15px 0px\">\r\n      {{ openModal }}\r\n      <Col span=\"24\" style=\"text-align: left\" v-if=\"!searchFirstTime\">\r\n      <span style=\"padding-left: 5px\" v-if=\"searchFormValidateSearch.objectName != '' ||\r\n        searchFormValidateSearch.startsAt != '' ||\r\n        searchFormValidateSearch.finishesAt != '' ||\r\n        searchFormValidateSearch.stationName != ''\r\n        \">{{ $t(\"LocaleString.L00262\") }}&nbsp;:</span>\r\n      <span style=\"padding-left: 10px; font-weight: bolder\" v-if=\"searchFormValidateSearch.startsAt != ''\">{{\r\n        $t(\"LocaleString.L00141\") + '(' + parseTime(searchFormValidateSearch.startsAt) + ') ' }}</span>\r\n      <span style=\"padding-left: 10px; font-weight: bolder\" v-if=\"searchFormValidateSearch.finishesAt != ''\">{{\r\n        $t(\"LocaleString.L00142\") + '(' + parseTime(searchFormValidateSearch.finishesAt) + ') ' }}</span>\r\n      <span style=\"padding-left: 10px; font-weight: bolder\" v-if=\"searchFormValidateSearch.objectName != ''\">\r\n        <Tooltip v-if=\"searchFormValidateSearch.objectName.length > 1\"> {{\r\n        $t(\"LocaleString.L00092\") + '(' +\r\n        translateCondition(searchFormValidateSearch.objectName, 'objectName') + ') ' }}\r\n          <div slot=\"content\">\r\n            <p v-for=\"(item, index) in searchFormValidateSearch.objectName\" v-bind:key=\"index\">\r\n              {{ \"• \" + objectMenu.find(i => i.code == item).name }}\r\n            </p>\r\n          </div>\r\n        </Tooltip>\r\n        <span v-else>{{\r\n        $t(\"LocaleString.L00092\") + '(' + translateCondition(searchFormValidateSearch.objectName, 'objectName') +\r\n        ')' }}</span>\r\n      </span>\r\n      <span style=\"padding-left: 10px; font-weight: bolder\" v-if=\"searchFormValidateSearch.stationName != ''\">\r\n        <Tooltip v-if=\"searchFormValidateSearch.stationName.length > 1\">{{\r\n        $t(\"LocaleString.L00161\") + '(' + translateCondition(searchFormValidateSearch.stationName, 'stationName') +\r\n        ')' }}\r\n          <div slot=\"content\">\r\n            <p v-for=\"(item, index) in searchFormValidateSearch.stationName\" v-bind:key=\"index\">\r\n              {{ \"• \" + stationMenu.find(i => i.sid == item).name }}\r\n            </p>\r\n          </div>\r\n        </Tooltip>\r\n        <span v-else>{{\r\n        $t(\"LocaleString.L00161\") + '(' + translateCondition(searchFormValidateSearch.stationName, 'stationName') +\r\n        ')' }}</span>\r\n      </span>\r\n      <span style=\"padding-left: 10px; font-weight: bolder\">{{\r\n        $t(\"LocaleString.L30170\") + '(' + translateCondition(searchFormValidateSearch.noSignalRecord, 'noSignalRecord')\r\n        +\r\n        ')' }}</span>\r\n      <!-- <Button ghost shape=\"circle\" style=\"width: 20px; margin-left: 10px\" @click=\"handleReset('searchFormValidate')\"\r\n        v-if=\"searchFormValidateSearch.objectName != '' ||\r\n        searchFormValidateSearch.stationName != '' ||\r\n        searchFormValidateSearch.startsAt != '' ||\r\n        searchFormValidateSearch.finishesAt != ''\r\n        \"><img :src=\"resetIcon\" style=\"width: 30.4px; margin-left: -14.8px\" /></Button> -->\r\n      </Col>\r\n      <Col span=\"3\" :offset=\"!searchFirstTime ? 0 : 21\" style=\"text-align: right\">\r\n      <!-- <Button ghost shape=\"circle\" style=\"width: 20px\" @click=\"openSearchObject\"><img :src=\"searchIcon\"\r\n          style=\"width: 30.4px; margin-left: -14.8px\" /></Button>\r\n      <Button type=\"success\" icon=\"ios-download-outline\" @click=\"exportHandleSubmit()\">{{ $t(\"LocaleString.B00009\")\r\n        }}</Button> -->\r\n      </Col>\r\n    </Row>\r\n    <Modal v-model=\"modalSearch\" :closable=\"false\" :mask-closable=\"false\">\r\n      <Form ref=\"searchFormValidate\" :model=\"searchFormValidate\" :rules=\"searchRuleValidate\" label-position=\"top\">\r\n        <Row :class=\"rowHeight\" :gutter=\"16\">\r\n          <Col span=\"12\">\r\n          <FormItem class=\"search-form-item\" :label=\"$t('LocaleString.L00141')\" prop=\"startsAt\">\r\n            <DatePicker type=\"datetime\" v-model.trim=\"searchFormValidate.startsAt\" :placeholder=\"$t('LocaleString.M00010', {\r\n        0: $t('LocaleString.L00141'),\r\n      })\r\n        \" :transfer=\"true\" @on-change=\"startsAtChange()\"></DatePicker>\r\n          </FormItem>\r\n          </Col>\r\n          <Col span=\"12\">\r\n          <FormItem class=\"search-form-item\" :label=\"$t('LocaleString.L00142')\" prop=\"finishesAt\">\r\n            <DatePicker type=\"datetime\" v-model.trim=\"searchFormValidate.finishesAt\" :placeholder=\"$t('LocaleString.M00010', {\r\n        0: $t('LocaleString.L00142'),\r\n      })\r\n        \" :transfer=\"true\" @on-change=\"finishesAtChange()\"></DatePicker>\r\n          </FormItem>\r\n          </Col>\r\n          <Col span=\"12\">\r\n          <FormItem class=\"search-form-item\" :label=\"$t('LocaleString.L00092')\" prop=\"objectName\">\r\n            <Select :placeholder=\"$t('LocaleString.D00001')\" v-model=\"searchFormValidate.objectName\" :transfer=\"true\"\r\n              filterable multiple :not-found-text=\"notFoundText\" @on-change=\"changeObject\" :max-tag-count=\"1\">\r\n              <Option value=\"all\" :disabled=\"diabledObjectNameAll\">{{ $t(\"LocaleString.D00002\") }}</Option>\r\n              <Option v-for=\"item in objectMenu\" :value=\"item.code\" :key=\"item.code\" :disabled=\"item.disabled\">{{\r\n        item.name }}</Option>\r\n            </Select>\r\n          </FormItem>\r\n          </Col>\r\n          <Col span=\"12\">\r\n          <FormItem class=\"search-form-item\" :label=\"$t('LocaleString.L00161')\" prop=\"stationName\">\r\n            <Select :placeholder=\"$t('LocaleString.D00001')\" v-model=\"searchFormValidate.stationName\" :transfer=\"true\"\r\n              filterable multiple :not-found-text=\"notFoundText\" @on-change=\"changeStation\" :max-tag-count=\"1\">\r\n              <Option value=\"all\" :disabled=\"diabledStationNameAll\">{{ $t(\"LocaleString.D00002\") }}</Option>\r\n              <Option v-for=\"item in stationMenu\" :value=\"item.sid\" :key=\"item.sid\" :disabled=\"item.disabled\">{{\r\n        item.name }}</Option>\r\n            </Select>\r\n          </FormItem>\r\n          </Col>\r\n          <Col span=\"12\">\r\n          <FormItem class=\"search-form-item\" :label=\"$t('LocaleString.L30170')\" prop=\"noSignalRecord\">\r\n            <div>\r\n              <i-switch v-model=\"searchFormValidate.noSignalRecord\" style=\"margin-left: 10px\" />\r\n            </div>\r\n          </FormItem>\r\n          </Col>\r\n        </Row>\r\n      </Form>\r\n      <div slot=\"footer\">\r\n        <div class=\"search-submit\">\r\n          <Button type=\"success\" icon=\"ios-search\" @click=\"searchHandleSubmit('searchFormValidate')\">{{\r\n        $t(\"LocaleString.B20017\") }}</Button>\r\n          <Button @click=\"cancelModal\">{{ $t(\"LocaleString.B00015\") }}</Button>\r\n        </div>\r\n      </div>\r\n    </Modal>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport resetIcon from \"@/assets/images/ic_reset.svg\";\r\nimport searchIcon from \"@/assets/images/ic_search.svg\";\r\nimport Config from \"@/common/config\";\r\nlet moment = require(\"moment\");\r\n\r\nexport default {\r\n  props: [\"serviceCodeMenu\", \"stationMenu\", \"objectMenu\", \"openSearchStatus\"],\r\n  data() {\r\n    const validateObjectName = (rule, value, callback) => {\r\n      let _this = this;\r\n      if (value.length == 0) {\r\n        callback(\r\n          new Error(\r\n            this.$t(\"LocaleString.M00010\", {\r\n              0: this.$t(\"LocaleString.L00092\"),\r\n            })\r\n          )\r\n        );\r\n      }\r\n      else if (value.includes(\"all\") && _this.searchFormValidate.stationName.includes(\"all\")) {\r\n        callback(new Error(\r\n          this.$t(\"LocaleString.W30014\")\r\n        ));\r\n      } else {\r\n        callback()\r\n      }\r\n    };\r\n\r\n    const validateStationName = (rule, value, callback) => {\r\n      let _this = this;\r\n      if (value.length == 0) {\r\n        callback(\r\n          new Error(\r\n            this.$t(\"LocaleString.M00010\", {\r\n              0: this.$t(\"LocaleString.L00161\"),\r\n            })\r\n          )\r\n        );\r\n      } else if (value.includes(\"all\") && _this.searchFormValidate.objectName.includes(\"all\")) {\r\n        callback(new Error(\r\n          this.$t(\"LocaleString.W30014\")\r\n        ));\r\n      } else {\r\n        callback()\r\n      }\r\n    };\r\n    return {\r\n      searchFirstTime: true,\r\n      diabledStationNameAll: false,\r\n      diabledObjectNameAll: false,\r\n      searchStartDate: \"\",\r\n      searchEndDate: \"\",\r\n      modalSearch: false,\r\n      resetIcon: resetIcon,\r\n      searchIcon: searchIcon,\r\n      defaultSelection: [],\r\n      notFoundText: this.$t(\"LocaleString.L00081\"),\r\n      rowHeight: \"row-height\",\r\n      searchFormValidate: {\r\n        objectName: ['all'],\r\n        startsAt: this.getStartsAtDefault(),\r\n        finishesAt: this.getFinishesAtDefault(),\r\n        stationName: ['all'],\r\n        noSignalRecord: false,\r\n      },\r\n      searchFormValidateSearch: null,\r\n      dateOptions: {\r\n        disabledDate(date) {\r\n          let startsAt = new Date(\r\n            Date.now() - 1000 * 60 * 60 * 24 * Config.SEARCH_ENABLE_DATES\r\n          );\r\n          startsAt.setHours(0, 0, 0, 0);\r\n          let finishesAt = new Date(Date.now() + 1000 * 60 * 60 * 24);\r\n          finishesAt.setHours(0, 0, 0, 0);\r\n\r\n          return date && (date < startsAt || date > finishesAt);\r\n        },\r\n      },\r\n      searchRuleValidate:\r\n      {\r\n        startsAt: [\r\n          {\r\n            required: true,\r\n            message: this.$t(\"LocaleString.M00010\", {\r\n              0: this.$t(\"LocaleString.L00141\"),\r\n            }),\r\n          },\r\n          {\r\n            type: \"date\",\r\n            validator: (rule, value) =>\r\n              !value ||\r\n              !this.searchFormValidate.finishesAt ||\r\n              value <= this.searchFormValidate.finishesAt,\r\n            message: this.$t(\"LocaleString.W00036\"),\r\n          },\r\n        ],\r\n        finishesAt: [\r\n          {\r\n            required: true,\r\n            message: this.$t(\"LocaleString.M00010\", {\r\n              0: this.$t(\"LocaleString.L00142\"),\r\n            }),\r\n          },\r\n          {\r\n            type: \"date\",\r\n            validator: (rule, value) =>\r\n              !value ||\r\n              !this.searchFormValidate.startsAt ||\r\n              value >= this.searchFormValidate.startsAt,\r\n            message: this.$t(\"LocaleString.W00036\"),\r\n          },\r\n        ],\r\n        objectName: [\r\n          { validator: validateObjectName, trigger: 'blur' }\r\n        ],\r\n        stationName: [\r\n          { validator: validateStationName, trigger: 'blur' }\r\n        ],\r\n\r\n      }\r\n    };\r\n  },\r\n  computed: {\r\n\r\n    openModal() {\r\n      if (this.openSearchStatus) {\r\n        this.openSearchObject();\r\n      }\r\n      return null;\r\n    }\r\n  },\r\n  created() {\r\n    this.searchFormValidateSearch = JSON.parse(\r\n      JSON.stringify(this.searchFormValidate)\r\n    );\r\n  },\r\n  async mounted() {\r\n  },\r\n  methods: {\r\n    changeStation(data) {\r\n      if (this.stationMenu) {\r\n        if (this.searchFormValidate.stationName.includes('all')) {\r\n          this.stationMenu.forEach(item => { item.disabled = true });\r\n          this.searchFormValidate.stationName = ['all'];\r\n        }\r\n        else {\r\n          this.stationMenu.forEach(item => { item.disabled = false });\r\n          if (data.length > 20) {\r\n            data.pop();\r\n          }\r\n        }\r\n      }\r\n    },\r\n\r\n    changeObject(data) {\r\n      if (this.objectMenu) {\r\n        if (this.searchFormValidate.objectName.includes('all')) {\r\n          this.objectMenu.forEach(item => { item.disabled = true });\r\n          this.searchFormValidate.objectName = ['all'];\r\n        } else {\r\n          this.objectMenu.forEach(item => { item.disabled = false });\r\n          if (data.length > 20) {\r\n            data.pop();\r\n          }\r\n        }\r\n      }\r\n    },\r\n\r\n    parseTime(day) {\r\n      let timedifference = (new Date().getTimezoneOffset() / 60) * -1;\r\n      let tmpOffsetDay = moment(day, \"YYYY-MM-DD HH:mm:ss\")\r\n        .add(timedifference, \"hours\")\r\n        .format(\"YYYY-MM-DD HH:mm:ss\");\r\n      return tmpOffsetDay;\r\n    },\r\n    translateCondition(item, type) {\r\n      if (item == undefined) {\r\n        return '';\r\n      }\r\n      if (item == \"all\") {\r\n        return this.$t(\"LocaleString.D00002\");\r\n      }\r\n\r\n      let translateName = \"\";\r\n      switch (type) {\r\n        case \"noSignalRecord\":\r\n          translateName = item ? this.$t(\"LocaleString.D30029\") : this.$t(\"LocaleString.D30030\");\r\n          break;\r\n        case \"objectName\":\r\n          translateName = item.length > 1 ? this.objectMenu.find(i => i.code == item[0]).name + \" ... \" : this.objectMenu.find(i => i.code == item).name;\r\n          break;\r\n        case \"stationName\":\r\n          translateName = item.length > 1 ? this.stationMenu.find(i => i.sid == item[0]).name + \" ... \" : this.stationMenu.find(i => i.sid == item).name;\r\n          break;\r\n\r\n      }\r\n      return translateName;\r\n    },\r\n    handleReset(name) {\r\n      this.$refs[name].resetFields();\r\n      this.searchFormValidateSearch = this.searchFormValidate;\r\n      this.searchHandleSubmit(\"searchFormValidate\");\r\n    },\r\n    cancelModal() {\r\n      this.$refs[\"searchFormValidate\"].resetFields();\r\n      if (!this.searchFirstTime) {\r\n        let newStartsAt = new Date(this.searchFormValidateSearch.startedTime);\r\n        let newFinishsAt = new Date(this.searchFormValidateSearch.endedTime);\r\n        this.searchFormValidate = JSON.parse(\r\n          JSON.stringify(this.searchFormValidateSearch)\r\n        );\r\n        this.searchFormValidate.startedTime = newStartsAt;\r\n        this.searchFormValidate.endedTime = newFinishsAt;\r\n      }\r\n\r\n      this.modalSearch = false;\r\n      this.$emit(\"closeSearchModal\");\r\n    },\r\n    openSearchObject() {\r\n      this.modalSearch = true;\r\n    },\r\n\r\n    getStartsAtDefault() {\r\n      let result = new Date(\r\n        Date.now() -\r\n        1000 * 3600 * 24 * (Config.SEARCH_POSTGRESQL_DATA_DATES - 1)\r\n      );\r\n      result.setHours(0, 0, 0, 0);\r\n      return result;\r\n    },\r\n    getFinishesAtDefault() {\r\n      let result = new Date(Date.now() + 1000 * 3600 * 24);\r\n      result.setHours(0, 0, 0, 0);\r\n      return result;\r\n    },\r\n    startsAtChange() {\r\n      this.$refs[\"searchFormValidate\"].validateField(\"finishesAt\");\r\n    },\r\n    finishesAtChange() {\r\n      this.$refs[\"searchFormValidate\"].validateField(\"startsAt\");\r\n    },\r\n    searchHandleSubmit(name) {\r\n      this.$refs[name].validate((valid) => {\r\n        if (valid) {\r\n          this.searchFormValidateSearch = JSON.parse(\r\n            JSON.stringify(this.searchFormValidate)\r\n          );\r\n          this.searchFirstTime = false\r\n          this.$emit(\"searchRequest\", {\r\n            isUserSubmit: true,\r\n            searchParams: this.getSearchParams(),\r\n          });\r\n          this.modalSearch = false;\r\n\r\n        }\r\n      });\r\n    },\r\n    getSearchParams() {\r\n      let str = \"\";\r\n\r\n      if (this.searchFormValidate.startsAt !== \"\" && this.searchFormValidate.finishesAt !== \"\") {\r\n        let timedifferenceStartDay =\r\n          (new Date().getTimezoneOffset() / 60) * -1 - 8;\r\n        let tmpOffsetStartDay = moment(\r\n          this.searchFormValidate.startsAt,\r\n          \"YYYY-MM-DD HH:mm:ss\"\r\n        )\r\n          .add(timedifferenceStartDay * -1, \"hours\")\r\n          .format(\"YYYY-MM-DD HH:mm:ss\");\r\n\r\n        let startsAt = tmpOffsetStartDay;\r\n\r\n        let timedifferencefinishDay =\r\n          (new Date().getTimezoneOffset() / 60) * -1 - 8;\r\n        let tmpOffsetFinishDay = moment(\r\n          this.searchFormValidate.finishesAt,\r\n          \"YYYY-MM-DD HH:mm:ss\"\r\n        )\r\n          .add(timedifferencefinishDay * -1, \"hours\")\r\n          .format(\"YYYY-MM-DD HH:mm:ss\");\r\n        let finishesAt = moment(tmpOffsetFinishDay).format(\r\n          \"YYYY-MM-DD HH:mm:ss\"\r\n        );\r\n\r\n        str += \"positionTime between '\" + startsAt + \",\" + finishesAt + \"' \";\r\n        this.searchStartDate = startsAt;\r\n        this.searchEndDate = finishesAt;\r\n      }\r\n\r\n      if (this.searchFormValidate.objectName.length > 0 && !this.searchFormValidate.objectName.includes(\"all\")) {\r\n        str +=\r\n          \" and object.code in \" +\r\n          this.searchFormValidate.objectName.join(\",\") +\r\n          \" \";\r\n      }\r\n      if (this.searchFormValidate.stationName.length > 0 && !this.searchFormValidate.stationName.includes(\"all\")) {\r\n        str +=\r\n          \" and toStation.sid in \" +\r\n          this.searchFormValidate.stationName.join(\",\") +\r\n          \" \";\r\n      }\r\n\r\n      if (!this.searchFormValidate.noSignalRecord) {\r\n        str +=\r\n          \" and toStation.sid ne offline\";\r\n      }\r\n      return { search: str };\r\n    },\r\n    exportHandleSubmit() {\r\n      this.$emit(\"exportConfirm\");\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n/deep/ .ivu-btn-success {\r\n  color: #fff;\r\n  background-color: #31babb;\r\n  border-color: #31babb;\r\n}\r\n\r\n/deep/ .ivu-btn-success:hover {\r\n  color: #fff !important;\r\n  border-color: #31babb;\r\n}\r\n</style>\r\n"]}]}