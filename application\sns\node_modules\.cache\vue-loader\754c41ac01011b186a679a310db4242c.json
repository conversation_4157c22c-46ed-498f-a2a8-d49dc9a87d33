{"remainingRequest": "E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\src\\components\\administrative\\systemManagement\\systemConfig.vue?vue&type=template&id=6afce1dd&scoped=true&", "dependencies": [{"path": "E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\src\\components\\administrative\\systemManagement\\systemConfig.vue", "mtime": 1754362736978}, {"path": "E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}