{"remainingRequest": "E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\src\\components\\administrative\\apps\\sns\\planeSetting\\sitePlan\\pointDrag.vue?vue&type=template&id=04ac45ed&scoped=true&", "dependencies": [{"path": "E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\src\\components\\administrative\\apps\\sns\\planeSetting\\sitePlan\\pointDrag.vue", "mtime": 1754362736894}, {"path": "E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CjxkaXYKICA6aWQ9Iml0ZW0uaWQiCiAgY2xhc3M9ImFuaW1hdGVkIGRyYWdDb250YWluUG9pbnQgem9vbUluIgogIDpjbGFzcz0ieyBkcmFnQ29udGFpblBvaW50SGVpZ2h0OiBwb2ludFR5cGUgPT09ICdndWFyZCcgfSIKICA6c3R5bGU9InBvaW50U3R5bGUiCj4KICA8VG9vbHRpcCA6Y29udGVudD0iaXRlbS5uYW1lIiBwbGFjZW1lbnQ9ImJvdHRvbSIgOnRyYW5zZmVyPSJ0cnVlIj4KICAgIDxkaXYgY2xhc3M9InN0YXRpb25UZXh0Ij4xPC9kaXY+CiAgPC9Ub29sdGlwPgo8L2Rpdj4K"}, null]}