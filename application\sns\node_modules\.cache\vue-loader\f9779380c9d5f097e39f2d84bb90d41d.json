{"remainingRequest": "E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\src\\components\\administrative\\systemManagement\\systemConfig.vue?vue&type=style&index=0&lang=scss&", "dependencies": [{"path": "E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\src\\components\\administrative\\systemManagement\\systemConfig.vue", "mtime": 1754362736978}, {"path": "E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCg0KLmFib3V0LWRhdGEtbW9kYWwgew0KICAudmVydGljYWwtY2VudGVyLW1vZGFsIHsNCiAgICBkaXNwbGF5OiBmbGV4Ow0KICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQogICAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7DQoNCiAgICAuaXZ1LW1vZGFsIHsNCiAgICAgIHRvcDogMDsNCiAgICB9DQogIH0NCg0KICAuaXZ1LW1vZGFsLWNsb3NlIHsNCiAgICB0b3A6IDE1cHg7DQogICAgcmlnaHQ6IDIwcHg7DQogIH0NCg0KICAuY29udGVudCB7DQogICAgcGFkZGluZzogNHB4IDE0cHg7DQogICAgb3ZlcmZsb3cteTogYXV0bzsNCiAgICAvL2hlaWdodDogNzAwcHg7DQogICAgLy9oZWlnaHQ6IDQwMHB4Ow0KICB9DQoNCiAgQG1lZGlhIG9ubHkgc2NyZWVuIGFuZCAobWluLXdpZHRoOiAxMjAwcHgpIHsNCiAgICAuaXZ1LW1vZGFsLWNsb3NlIHsNCiAgICAgIHRvcDogMThweDsNCiAgICB9DQogIH0NCg0KICAuaXZ1LW1vZGFsLWhlYWRlciB7DQogICAgcGFkZGluZzogMTVweCAyMHB4Ow0KICAgIGJvcmRlci1ib3R0b206IDFweCBzb2xpZCAjZjdmN2Y3Ow0KDQogICAgLmhlYWRlciB7DQogICAgICBvdmVyZmxvdzogaGlkZGVuOw0KDQogICAgICAubGVmdCB7DQogICAgICAgIGZsb2F0OiBsZWZ0Ow0KICAgICAgICB3aWR0aDogNnB4Ow0KICAgICAgICBoZWlnaHQ6IDI0cHg7DQogICAgICAgIG1hcmdpbi1yaWdodDogOHB4Ow0KICAgICAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjMGFjN2MwOw0KICAgICAgICBib3JkZXItcmFkaXVzOiAzcHg7DQogICAgICB9DQoNCiAgICAgIC50aXRsZSB7DQogICAgICAgIGZsb2F0OiBsZWZ0Ow0KICAgICAgICBoZWlnaHQ6IDI0cHg7DQogICAgICAgIGxpbmUtaGVpZ2h0OiAyNHB4Ow0KICAgICAgICBmb250LXNpemU6IDE0cHg7DQogICAgICAgIGZvbnQtd2VpZ2h0OiBib2xkOw0KICAgICAgICBjb2xvcjogIzk5OTsNCg0KICAgICAgICBzcGFuIHsNCiAgICAgICAgICBjb2xvcjogIzMzMzsNCiAgICAgICAgfQ0KICAgICAgfQ0KDQogICAgICAuZXMtYnRuIHsNCiAgICAgICAgZmxvYXQ6IHJpZ2h0Ow0KICAgICAgICBtYXJnaW4tcmlnaHQ6IDM1cHg7DQogICAgICB9DQoNCiAgICAgIEBtZWRpYSBvbmx5IHNjcmVlbiBhbmQgKG1pbi13aWR0aDogMTIwMHB4KSB7DQogICAgICAgIC5sZWZ0IHsNCiAgICAgICAgICBoZWlnaHQ6IDMwcHg7DQogICAgICAgIH0NCg0KICAgICAgICAudGl0bGUgew0KICAgICAgICAgIGhlaWdodDogMzBweDsNCiAgICAgICAgICBsaW5lLWhlaWdodDogMzBweDsNCiAgICAgICAgICBmb250LXNpemU6IDE4cHg7DQogICAgICAgIH0NCiAgICAgIH0NCiAgICB9DQogIH0NCg0KICAuaXZ1LW1vZGFsLWJvZHkgew0KICAgIC5jb250ZW50IHsNCiAgICAgIHBhZGRpbmc6IDRweCAxNHB4Ow0KICAgICAgb3ZlcmZsb3cteTogYXV0bzsNCg0KICAgICAgLnJvdyB7DQogICAgICAgIG1hcmdpbi1ib3R0b206IDIwcHg7DQoNCiAgICAgICAgLml0ZW0gew0KICAgICAgICAgIHBhZGRpbmc6IDE1cHg7DQogICAgICAgICAgYm9yZGVyOiAxcHggc29saWQgI2Y3ZjdmNzsNCiAgICAgICAgICBib3JkZXItcmFkaXVzOiA0cHg7DQoNCiAgICAgICAgICBsYWJlbCB7DQogICAgICAgICAgICBjb2xvcjogIzk5OTsNCiAgICAgICAgICAgIGZvbnQtd2VpZ2h0OiBib2xkOw0KICAgICAgICAgIH0NCg0KICAgICAgICAgIHAgew0KICAgICAgICAgICAgbWFyZ2luLXRvcDogMTBweDsNCiAgICAgICAgICAgIGNvbG9yOiAjMzMzOw0KICAgICAgICAgIH0NCiAgICAgICAgfQ0KICAgICAgfQ0KDQogICAgICAuZm9ybS1pdGVtIHsNCiAgICAgICAgcGFkZGluZzogMTVweCAxNXB4IDIycHg7DQogICAgICAgIG1hcmdpbi1ib3R0b206IDE2cHg7DQogICAgICAgIGJvcmRlci1yYWRpdXM6IDRweDsNCiAgICAgICAgYmFja2dyb3VuZC1jb2xvcjogI2Y3ZjdmNzsNCiAgICAgIH0NCiAgICB9DQogIH0NCg0KICAuaXZ1LW1vZGFsLWZvb3RlciB7DQogICAgaGVpZ2h0OiA1MHB4Ow0KICAgIGJvcmRlci10b3A6IG5vbmU7DQogICAgcGFkZGluZzogMDsNCiAgfQ0KfQ0K"}, {"version": 3, "sources": ["systemConfig.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA61IA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA", "file": "systemConfig.vue", "sourceRoot": "src/components/administrative/systemManagement", "sourcesContent": ["<template>\r\n  <div class=\"personal-data\">\r\n    <Modal\r\n      class=\"about-data-modal\"\r\n      :closable=\"false\"\r\n      :mask-closable=\"false\"\r\n      v-model=\"isShow\"\r\n      class-name=\"vertical-center-modal\"\r\n      :width=\"modalWidth\"\r\n    >\r\n      <!-- <a slot=\"close\">\r\n        <Icon type=\"md-close\" color=\"#ADADAD\" size=\"24\"></Icon>\r\n      </a>-->\r\n      <div slot=\"header\" class=\"header\">\r\n        <div style=\"position:absolute;\">\r\n          <div :style=\"tabButtonWidth\">\r\n            <div style=\"float: right;margin-top:5px\">\r\n              <Button v-show=\"!isEdit\" type=\"primary\" size=\"small\" class=\"font-small\" @click=\"edit\">\r\n                {{\r\n                $t(\"LocaleString.B20014\") }}\r\n              </Button>\r\n              <Button v-show=\"isEdit\" size=\"small\" class=\"font-small\" @click=\"cancelEdit\">\r\n                {{ $t(\"LocaleString.B00015\")\r\n                }}\r\n              </Button>\r\n              <Button\r\n                v-show=\"isEdit\"\r\n                type=\"primary\"\r\n                size=\"small\"\r\n                class=\"font-small\"\r\n                :loading=\"submitLoading\"\r\n                @click=\"editHandleSubmitNew('update')\"\r\n              >{{ $t(\"LocaleString.B00012\") }}</Button>\r\n            </div>\r\n            <div style=\"clear: both\"></div>\r\n          </div>\r\n        </div>\r\n        <!-- <i class=\"left\"></i> -->\r\n        <span class=\"title\">{{ $t(\"LocaleString.L30054\") }}</span>\r\n      </div>\r\n      <div class=\"content\">\r\n        <Tabs :animated=\"false\" :value=\"currentTab\" @on-click=\"changeTab\">\r\n          <TabPane :label=\"$t('LocaleString.F00037')\" name=\"system\">\r\n            <Table :columns=\"columns\" :data=\"data\" :height=\"modalInnerHeight\">\r\n              <template slot-scope=\"{ row, index }\" slot=\"item\">\r\n                <!-- <Input type=\"text\" v-model=\"row.name\" v-if=\"isEdit\" /> -->\r\n                <div v-if=\"row.section == 10\">\r\n                  <span v-if=\"configData.eventAuthClose == true\">\r\n                    {{\r\n                    row.item\r\n                    }}\r\n                  </span>\r\n                </div>\r\n                <div v-if=\"row.section != 10\">\r\n                  <span>\r\n                    {{ row.item }}\r\n                    <img\r\n                      v-if=\"row.section == 6\"\r\n                      :src=\"InfoIcon\"\r\n                      style=\"vertical-align: middle; cursor: pointer\"\r\n                      @click=\"tipModal = true\"\r\n                    />\r\n                    <img\r\n                      v-if=\"row.section == 7\"\r\n                      :src=\"InfoIcon\"\r\n                      style=\"vertical-align: middle; cursor: pointer\"\r\n                      @click=\"tip2Modal = true\"\r\n                    />\r\n                    <img\r\n                      v-if=\"row.section == 11\"\r\n                      :src=\"InfoIcon\"\r\n                      style=\"vertical-align: middle; cursor: pointer\"\r\n                      @click=\"tip3Modal = true\"\r\n                    />\r\n                    <img\r\n                      v-if=\"row.section == 14\"\r\n                      :src=\"InfoIcon\"\r\n                      style=\"vertical-align: middle; cursor: pointer\"\r\n                      @click=\"tip4Modal = true\"\r\n                    />\r\n                    <img\r\n                      v-if=\"row.section == 16\"\r\n                      :src=\"InfoIcon\"\r\n                      style=\"vertical-align: middle; cursor: pointer\"\r\n                      @click=\"tip5Modal = true\"\r\n                    />\r\n                  </span>\r\n                </div>\r\n              </template>\r\n              <template slot-scope=\"{ row, index }\" slot=\"value\">\r\n                <div v-if=\"row.section == 23\">\r\n                  <Select\r\n                    :placeholder=\"$t('LocaleString.D00001')\"\r\n                    v-model=\"configData.license\"\r\n                    v-if=\"isEdit\"\r\n                    multiple\r\n                    transfer\r\n                  >\r\n                    <Option\r\n                      v-for=\"item in serviceCodeList\"\r\n                      :value=\"item.key\"\r\n                      :key=\"item.key\"\r\n                    >{{ item.value }}</Option>\r\n                  </Select>\r\n                  <span v-else>{{ row.value }}</span>\r\n                </div>\r\n                <div v-if=\"row.section == 0\">\r\n                  <Input\r\n                    maxlength=\"25\"\r\n                    show-word-limit\r\n                    v-if=\"isEdit && getEditPermission()\"\r\n                    v-model=\"configData.systemName\"\r\n                  ></Input>\r\n                  <span v-else>{{ row.value }}</span>\r\n                </div>\r\n                <div v-if=\"row.section == 17\">\r\n                  <div\r\n                    v-show=\"isEdit && getEditPermission()\"\r\n                    class=\"toggle-sound paused\"\r\n                    style=\"display: inline-block\"\r\n                  >\r\n                    <Button\r\n                      ghost\r\n                      shape=\"circle\"\r\n                      style=\"width: 20px; margin-left: 10px\"\r\n                      v-if=\"showCleanIcon\"\r\n                      @click=\"restImage\"\r\n                    >\r\n                      <img :src=\"resetIcon\" style=\"width: 30.4px; margin-left: -14.8px\" />\r\n                    </Button>\r\n                  </div>\r\n                  <Upload\r\n                    v-if=\"isEdit && getEditPermission()\"\r\n                    :before-upload=\"handleUploadImage\"\r\n                    action\r\n                    style=\"display: inline-block\"\r\n                  >\r\n                    <span>\r\n                      <Icon class=\"musicIcon\" type=\"md-cloud-upload\" size=\"30\" />\r\n                    </span>\r\n                  </Upload>\r\n                  <img\r\n                    v-if=\"isEdit && getEditPermission()\"\r\n                    id=\"preview\"\r\n                    :src=\"imgFileURL ? imgFileURL : ''\"\r\n                  />\r\n                  <span v-if=\"!isEdit\">\r\n                    <img :src=\"imgFileURL\" />\r\n                  </span>\r\n                </div>\r\n                <div v-if=\"row.section == 31\">\r\n                  <InputNumber\r\n                    :precision=\"0\"\r\n                    :min=\"0\"\r\n                    :max=\"300\"\r\n                    step=\"1\"\r\n                    v-if=\"isEdit\"\r\n                    v-model=\"configData.logoutTime\"\r\n                  ></InputNumber>\r\n                  <span v-else>{{ row.value }}</span>\r\n                </div>\r\n                <div v-if=\"row.section == 18\">\r\n                  <i-switch\r\n                    @on-change=\"switchMultiPlanes\"\r\n                    v-model=\"configData.multiPlanes\"\r\n                    v-if=\"isEdit\"\r\n                  />\r\n                  <span style=\"margin-right:25px\" v-else>{{ row.viewMultiPlanes }}</span>\r\n                  <Select\r\n                    style=\"margin-left:5px; width:770px\"\r\n                    :placeholder=\"$t('LocaleString.D00001')\"\r\n                    v-model=\"configData.selectedPlanes\"\r\n                    v-if=\"isEdit && configData.multiPlanes\"\r\n                    multiple\r\n                    :max-tag-count=\"8\"\r\n                    transfer\r\n                    @on-change=\"changeMultiPlanes\"\r\n                  >\r\n                    <Option\r\n                      v-for=\"item in planeList\"\r\n                      :value=\"item.value\"\r\n                      :key=\"item.value\"\r\n                    >{{ item.label }}</Option>\r\n                  </Select>\r\n                  <span v-else>\r\n                    <span\r\n                      v-if=\"!isEdit && row.valuePlaneSelectList !== ''\"\r\n                    >{{ row.valuePlaneSelectList }}</span>\r\n                  </span>\r\n                </div>\r\n                <div v-if=\"row.section == 30\">\r\n                  <i-switch v-model=\"configData.stuckPlane\" v-if=\"isEdit\" />\r\n                  <span style=\"margin-right:25px\" v-else>{{ row.viewStuckPlane }}</span>\r\n                </div>\r\n                <div v-if=\"row.section == 1\">\r\n                  <Select\r\n                    :placeholder=\"$t('LocaleString.D00001')\"\r\n                    v-model=\"configData.tabList\"\r\n                    v-if=\"isEdit\"\r\n                    multiple\r\n                    transfer\r\n                    @on-change=\"changeDefaultTabList\"\r\n                  >\r\n                    <Option\r\n                      v-for=\"item in showingTab\"\r\n                      :value=\"item.value\"\r\n                      :key=\"item.value\"\r\n                    >{{ item.label }}</Option>\r\n                  </Select>\r\n                  <span v-else>{{ row.value }}</span>\r\n                </div>\r\n                <div v-if=\"row.section == 2\">\r\n                  <i-switch v-model=\"configData.sound\" v-if=\"isEdit\" />\r\n                  <div\r\n                    v-show=\"isEdit\"\r\n                    @click=\"play()\"\r\n                    class=\"toggle-sound paused\"\r\n                    style=\"display: inline-block\"\r\n                  >\r\n                    <span v-if=\"isPlay\">\r\n                      <Icon class=\"musicIcon\" type=\"md-pause\" size=\"30\" />\r\n                    </span>\r\n                    <span v-if=\"!isPlay\">\r\n                      <Icon class=\"musicIcon\" type=\"md-play\" size=\"30\" />\r\n                    </span>\r\n                  </div>\r\n                  <Upload\r\n                    v-if=\"isEdit\"\r\n                    :before-upload=\"handleUpload\"\r\n                    action\r\n                    style=\"display: inline-block\"\r\n                  >\r\n                    <span>\r\n                      <Icon class=\"musicIcon\" type=\"md-cloud-upload\" size=\"30\" />\r\n                    </span>\r\n                  </Upload>\r\n                  <audio\r\n                    ref=\"audio\"\r\n                    :src=\"fileURL != null ? fileURL : alertSound\"\r\n                    @ended=\"ended\"\r\n                    preload\r\n                    id=\"audio\"\r\n                    muted\r\n                  ></audio>\r\n\r\n                  <span v-if=\"!isEdit\">{{ row.value }}</span>\r\n\r\n                  <div\r\n                    v-show=\"!isEdit && configData.sound\"\r\n                    @click=\"play()\"\r\n                    class=\"toggle-sound paused\"\r\n                    style=\"display: inline-block\"\r\n                  >\r\n                    <span v-if=\"isPlay\">\r\n                      <Icon class=\"musicIcon\" type=\"md-pause\" size=\"30\" />\r\n                    </span>\r\n                    <span v-if=\"!isPlay\">\r\n                      <Icon class=\"musicIcon\" type=\"md-play\" size=\"30\" />\r\n                    </span>\r\n                  </div>\r\n                  <span style=\"margin-left:10px\" v-if=\"!isEdit && configData.sound\">\r\n                    <span>{{$t(\"LocaleString.L30203\")}}</span>\r\n                    {{ row.valueTime }}\r\n                    <span>{{$t(\"LocaleString.L30204\")}}</span>\r\n                  </span>\r\n                  <span style=\"margin-left:10px\" v-if=\"isEdit && configData.sound\">\r\n                    <span>{{$t(\"LocaleString.L30203\")}}</span>\r\n                    <InputNumber\r\n                      style=\"width:60px\"\r\n                      :min=\"0\"\r\n                      :max=\"20\"\r\n                      v-model=\"configData.repeatSound\"\r\n                      :placeholder=\"$t('LocaleString.L00001', { 0: $t('LocaleString.L30027') })\r\n      \"\r\n                    ></InputNumber>\r\n                    <span>{{$t(\"LocaleString.L30204\")}}(0~20)</span>\r\n                  </span>\r\n                </div>\r\n                <div v-if=\"row.section == 3\">\r\n                  <Select\r\n                    :placeholder=\"$t('LocaleString.D00001')\"\r\n                    v-model=\"configData.m2List\"\r\n                    v-if=\"isEdit\"\r\n                    multiple\r\n                    transfer\r\n                  >\r\n                    <Option\r\n                      v-for=\"item in showingType\"\r\n                      v-if=\"item.value != 'wetUrine'\"\r\n                      :value=\"item.value\"\r\n                      :key=\"item.value\"\r\n                    >{{ item.label }}</Option>\r\n                  </Select>\r\n                  <span v-else>{{ row.value }}</span>\r\n                </div>\r\n                <div v-if=\"row.section == 4\">\r\n                  <!-- <i-Input v-if=\"isEdit\" v-model=\"configData.iconSize\" placeholder=\"ex: 40*40\"></i-Input> -->\r\n                  <Select\r\n                    :placeholder=\"$t('LocaleString.D00001')\"\r\n                    v-model=\"configData.iconSize\"\r\n                    v-if=\"isEdit\"\r\n                    transfer\r\n                  >\r\n                    <Option\r\n                      v-for=\"item in iconSizeList\"\r\n                      :value=\"item.value\"\r\n                      :key=\"item.value\"\r\n                    >{{ item.label }}</Option>\r\n                  </Select>\r\n                  <span v-else>{{ iconSizeTitle }}</span>\r\n                </div>\r\n                <div v-if=\"row.section == 5\">\r\n                  <Select\r\n                    :placeholder=\"$t('LocaleString.D00001')\"\r\n                    v-model=\"configData.defaultTab\"\r\n                    v-if=\"isEdit\"\r\n                    transfer\r\n                  >\r\n                    <Option\r\n                      v-for=\"item in defaultTabList\"\r\n                      :value=\"item.value\"\r\n                      :key=\"item.value\"\r\n                    >{{ item.label }}</Option>\r\n                  </Select>\r\n                  <span v-else>{{ defaultTabTitle }}</span>\r\n                </div>\r\n                <div v-if=\"row.section == 6\">\r\n                  <Select\r\n                    :placeholder=\"$t('LocaleString.D00001')\"\r\n                    v-model=\"configData.updateSeconds\"\r\n                    v-if=\"isEdit\"\r\n                    transfer\r\n                  >\r\n                    <Option\r\n                      v-for=\"item in defaultUpdateSeconds\"\r\n                      :value=\"item.value\"\r\n                      :key=\"item.value\"\r\n                    >{{ item.label }}</Option>\r\n                  </Select>\r\n                  <!-- <i-Input\r\n\r\n                v-if=\"isEdit\"\r\n                v-model=\"configData.updateSeconds\"\r\n                placeholder=\"30\"\r\n                  ></i-Input>-->\r\n                  <span v-else>{{ row.value }}</span>\r\n                </div>\r\n                <div v-if=\"row.section == 7\">\r\n                  <InputNumber\r\n                    :min=\"0\"\r\n                    v-if=\"isEdit\"\r\n                    v-model=\"configData.grayTime\"\r\n                    :placeholder=\"$t('LocaleString.L00001', { 0: $t('LocaleString.L30027') })\r\n      \"\r\n                  ></InputNumber>\r\n                  <span v-else>{{ row.value }}</span>\r\n                </div>\r\n                <div v-if=\"row.section == 8\">\r\n                  <i-switch v-model=\"configData.m2Line\" v-if=\"isEdit\" />\r\n                  <span v-else>{{ row.value }}</span>\r\n                </div>\r\n                <div v-if=\"row.section == 11\">\r\n                  <Select\r\n                    :placeholder=\"$t('LocaleString.D00001')\"\r\n                    v-model=\"configData.influxRange\"\r\n                    v-if=\"isEdit\"\r\n                    transfer\r\n                  >\r\n                    <Option\r\n                      v-for=\"item in defaultInfluxRange\"\r\n                      :value=\"item.value\"\r\n                      :key=\"item.value\"\r\n                    >{{ item.label }}</Option>\r\n                  </Select>\r\n                  <span v-else>{{ row.value }}</span>\r\n                </div>\r\n                <div v-if=\"row.section == 12\">\r\n                  <InputNumber\r\n                    :precision=\"0\"\r\n                    :formatter=\"(value) => `${Number(value.toFixed(2))}`\"\r\n                    :min=\"1\"\r\n                    :max=\"60\"\r\n                    v-if=\"isEdit\"\r\n                    v-model=\"configData.lossSignal\"\r\n                    :placeholder=\"$t('LocaleString.L00001', { 0: $t('LocaleString.L30027') })\r\n      \"\r\n                  ></InputNumber>\r\n                  <Select\r\n                    :placeholder=\"$t('LocaleString.D00001')\"\r\n                    multiple\r\n                    :max-tag-count=\"6\"\r\n                    style=\"margin-left: 5px; width: 736px\"\r\n                    v-model=\"configData.lossSignalDevices\"\r\n                    v-if=\"isEdit\"\r\n                    transfer\r\n                  >\r\n                    <Option\r\n                      v-for=\"item in deviceListLossSignal\"\r\n                      :value=\"item.key\"\r\n                      :key=\"item.key\"\r\n                    >{{ item.value }}</Option>\r\n                  </Select>\r\n                  <div v-else>\r\n                    <Row>\r\n                      <Col span=\"3\">{{ row.value }}</Col>\r\n                      <Col span=\"21\" v-if=\"row.valueDevices !== ''\">\r\n                        <span>{{ row.valueDevices }}</span>\r\n                      </Col>\r\n                    </Row>\r\n                  </div>\r\n                </div>\r\n                <div v-if=\"row.section == 22\">\r\n                  <InputNumber\r\n                    :precision=\"0\"\r\n                    :formatter=\"(value) => `${Number(value.toFixed(2))}`\"\r\n                    :min=\"1\"\r\n                    :max=\"300\"\r\n                    v-if=\"isEdit\"\r\n                    v-model=\"configData.lossSignal2\"\r\n                    :placeholder=\"$t('LocaleString.L00001', { 0: $t('LocaleString.L30027') })\r\n      \"\r\n                  ></InputNumber>\r\n                  <Select\r\n                    :placeholder=\"$t('LocaleString.D00001')\"\r\n                    multiple\r\n                    :max-tag-count=\"6\"\r\n                    style=\"margin-left: 5px; width: 736px\"\r\n                    v-model=\"configData.lossSignalDevices2\"\r\n                    v-if=\"isEdit\"\r\n                    transfer\r\n                  >\r\n                    <Option\r\n                      v-for=\"item in deviceListLossSignal2\"\r\n                      :value=\"item.key\"\r\n                      :key=\"item.key\"\r\n                    >{{ item.value }}</Option>\r\n                  </Select>\r\n                  <div v-else>\r\n                    <Row>\r\n                      <Col span=\"3\">{{ row.value }}</Col>\r\n                      <Col span=\"21\" v-if=\"row.valueDevices !== ''\">\r\n                        <span>{{ row.valueDevices }}</span>\r\n                      </Col>\r\n                    </Row>\r\n                  </div>\r\n                </div>\r\n                <div v-if=\"row.section == 13\">\r\n                  <InputNumber\r\n                    :precision=\"0\"\r\n                    :formatter=\"(value) => `${Number(value.toFixed(2))}`\"\r\n                    :min=\"1\"\r\n                    :max=\"60\"\r\n                    v-if=\"isEdit\"\r\n                    v-model=\"configData.lowBattery\"\r\n                    :placeholder=\"$t('LocaleString.L00001', { 0: '%' })\"\r\n                  ></InputNumber>\r\n                  <Select\r\n                    :placeholder=\"$t('LocaleString.D00001')\"\r\n                    style=\"margin-left: 5px; width: 736px\"\r\n                    :max-tag-count=\"6\"\r\n                    multiple\r\n                    v-model=\"configData.lowBatteryDevices\"\r\n                    v-if=\"isEdit\"\r\n                    transfer\r\n                  >\r\n                    <Option\r\n                      v-for=\"item in deviceListLowBattery\"\r\n                      :value=\"item.key\"\r\n                      :key=\"item.key\"\r\n                    >{{ item.value }}</Option>\r\n                  </Select>\r\n                  <div v-else>\r\n                    <Row>\r\n                      <Col span=\"3\">{{ row.value }}</Col>\r\n                      <Col span=\"21\" v-if=\"row.valueDevices !== ''\">\r\n                        <span>{{ row.valueDevices }}</span>\r\n                      </Col>\r\n                    </Row>\r\n                  </div>\r\n                </div>\r\n                <div v-if=\"row.section == 15\">\r\n                  <Select\r\n                    :placeholder=\"$t('LocaleString.D00001')\"\r\n                    :max-tag-count=\"8\"\r\n                    multiple\r\n                    v-model=\"configData.abnormalDevices\"\r\n                    v-if=\"isEdit\"\r\n                    transfer\r\n                  >\r\n                    <Option\r\n                      v-for=\"item in deviceListAbnormalDevice\"\r\n                      :value=\"item.key\"\r\n                      :key=\"item.key\"\r\n                    >{{ item.value }}</Option>\r\n                  </Select>\r\n                  <div v-else>\r\n                    <Row>\r\n                      <Col span=\"3\">{{ row.value }}</Col>\r\n                      <Col span=\"21\" v-if=\"row.valueDevices !== ''\">\r\n                        <span>{{ row.valueDevices }}</span>\r\n                      </Col>\r\n                    </Row>\r\n                  </div>\r\n                </div>\r\n                <div v-if=\"row.section == 20\">\r\n                  <Select\r\n                    :placeholder=\"$t('LocaleString.D00001')\"\r\n                    v-model=\"configData.defaultPageSize\"\r\n                    v-if=\"isEdit\"\r\n                    transfer\r\n                  >\r\n                    <Option\r\n                      v-for=\"item in pageSizeList\"\r\n                      :value=\"item.value\"\r\n                      :key=\"item.value\"\r\n                    >{{ item.label }}</Option>\r\n                  </Select>\r\n                  <span v-else>{{ defaultPageSizeTitle }}</span>\r\n                </div>\r\n                <div v-if=\"row.section == 21\">\r\n                  <Select\r\n                    :placeholder=\"$t('LocaleString.D00001')\"\r\n                    v-model=\"configData.m5ObjectType\"\r\n                    v-if=\"isEdit\"\r\n                    transfer\r\n                  >\r\n                    <Option\r\n                      v-for=\"item in m5ObjectTypeList\"\r\n                      :value=\"item.value\"\r\n                      :key=\"item.value\"\r\n                    >{{ item.label }}</Option>\r\n                  </Select>\r\n                  <span v-else>{{ m5ObjectTypeTitle }}</span>\r\n                </div>\r\n                <div v-if=\"row.section == 9\">\r\n                  <i-switch v-model=\"configData.eventAuthClose\" v-if=\"isEdit\" />\r\n                  <span v-else>{{ row.value }}</span>\r\n                </div>\r\n                <div v-if=\"row.section == 10\">\r\n                  <div v-if=\"configData.eventAuthClose == true\">\r\n                    <InputNumber\r\n                      :precision=\"0\"\r\n                      :formatter=\"(value) => `${Number(value.toFixed(2))}`\"\r\n                      :min=\"1\"\r\n                      :max=\"7\"\r\n                      v-if=\"isEdit\"\r\n                      v-model=\"configData.eventKeepDays\"\r\n                      :placeholder=\"$t('LocaleString.L00001', {\r\n      0: $t('LocaleString.L20150'),\r\n    })\r\n      \"\r\n                    ></InputNumber>\r\n\r\n                    <span v-else>{{ row.value }}</span>\r\n                  </div>\r\n                </div>\r\n                <div v-if=\"row.section == 14\">\r\n                  <Select\r\n                    :placeholder=\"$t('LocaleString.D00001')\"\r\n                    v-model=\"configData.skipEvent\"\r\n                    v-if=\"isEdit\"\r\n                    multiple\r\n                    :max-tag-count=\"10\"\r\n                    transfer\r\n                  >\r\n                    <Option\r\n                      v-for=\"item in eventServiceCodeList\"\r\n                      :value=\"item.key\"\r\n                      :key=\"item.key\"\r\n                    >{{ item.value }}</Option>\r\n                  </Select>\r\n                  <span v-else>\r\n                    <span v-if=\"row.valueSkipEvent !== ''\">{{ row.valueSkipEvent }}</span>\r\n                  </span>\r\n                </div>\r\n                <div v-if=\"row.section == 16\">\r\n                  <Select\r\n                    :placeholder=\"$t('LocaleString.D00001')\"\r\n                    v-model=\"configData.deviceSelectList\"\r\n                    v-if=\"isEdit\"\r\n                    multiple\r\n                    :max-tag-count=\"10\"\r\n                    transfer\r\n                    @on-open-change=\"openDeviceSelection\"\r\n                    @on-change=\"changeDeviceDefaultSelectList\"\r\n                  >\r\n                    <Option\r\n                      v-for=\"item in deviceDefaultSelection\"\r\n                      :value=\"item.key\"\r\n                      :key=\"item.key\"\r\n                    >{{ item.value }}</Option>\r\n                  </Select>\r\n                  <span v-else>\r\n                    <span v-if=\"row.valueDeviceSelectList !== ''\">{{ row.valueDeviceSelectList }}</span>\r\n                  </span>\r\n                </div>\r\n                <div v-if=\"row.section == 19\">\r\n                  <Select\r\n                    :placeholder=\"$t('LocaleString.D00001')\"\r\n                    v-model=\"configData.m1LongPress\"\r\n                    v-if=\"isEdit\"\r\n                    multiple\r\n                    :max-tag-count=\"8\"\r\n                    transfer\r\n                  >\r\n                    <Option\r\n                      v-for=\"item in deviceDefaultSelection.filter((item) => item.key.includes('fusion-km'))\"\r\n                      :value=\"item.key\"\r\n                      :key=\"item.key\"\r\n                    >{{ item.value }}</Option>\r\n                  </Select>\r\n                  <span v-else>\r\n                    <span v-if=\"row.valueM1LongPressList !== ''\">{{ row.valueM1LongPressList }}</span>\r\n                  </span>\r\n                </div>\r\n                <div v-if=\"row.section == 24\">\r\n                  <i-switch v-model=\"configData.showLeaveBed\" v-if=\"isEdit\" />\r\n                  <span v-else>{{ row.value }}</span>\r\n                </div>\r\n                <div v-if=\"row.section == 25\">\r\n                  <InputNumber\r\n                    :precision=\"0\"\r\n                    :formatter=\"(value) => `${Number(value.toFixed(2))}`\"\r\n                    :min=\"2\"\r\n                    :max=\"600\"\r\n                    v-if=\"isEdit\"\r\n                    v-model=\"configData.m2Statistic\"\r\n                    :placeholder=\"$t('LocaleString.L00001', {\r\n      0: $t('LocaleString.L20150'),\r\n    })\r\n      \"\r\n                  ></InputNumber>\r\n                  <span v-else>{{ row.value }}</span>\r\n                </div>\r\n                <div v-if=\"row.section == 26\">\r\n                  <InputNumber\r\n                    :precision=\"0\"\r\n                    :formatter=\"(value) => `${Number(value.toFixed(2))}`\"\r\n                    :min=\"2\"\r\n                    :max=\"600\"\r\n                    v-if=\"isEdit\"\r\n                    v-model=\"configData.m3Statistic\"\r\n                    :placeholder=\"$t('LocaleString.L00001', {\r\n      0: $t('LocaleString.L20150'),\r\n    })\r\n      \"\r\n                  ></InputNumber>\r\n                  <span v-else>{{ row.value }}</span>\r\n                </div>\r\n                <div v-if=\"row.section == 27\">\r\n                  <InputNumber\r\n                    :precision=\"0\"\r\n                    :formatter=\"(value) => `${Number(value.toFixed(2))}`\"\r\n                    :min=\"2\"\r\n                    :max=\"600\"\r\n                    v-if=\"isEdit\"\r\n                    v-model=\"configData.m4Statistic\"\r\n                    :placeholder=\"$t('LocaleString.L00001', {\r\n      0: $t('LocaleString.L20150'),\r\n    })\r\n      \"\r\n                  ></InputNumber>\r\n                  <span v-else>{{ row.value }}</span>\r\n                </div>\r\n                <div v-if=\"row.section == 28\">\r\n                  <InputNumber\r\n                    :precision=\"0\"\r\n                    :formatter=\"(value) => `${Number(value.toFixed(2))}`\"\r\n                    :min=\"2\"\r\n                    :max=\"600\"\r\n                    v-if=\"isEdit\"\r\n                    v-model=\"configData.m8Statistic\"\r\n                    :placeholder=\"$t('LocaleString.L00001', {\r\n      0: $t('LocaleString.L20150'),\r\n    })\r\n      \"\r\n                  ></InputNumber>\r\n                  <span v-else>{{ row.value }}</span>\r\n                </div>\r\n                <div v-if=\"row.section == 29\">\r\n                  <InputNumber\r\n                    :precision=\"0\"\r\n                    :formatter=\"(value) => `${Number(value.toFixed(2))}`\"\r\n                    :min=\"2\"\r\n                    :max=\"600\"\r\n                    v-if=\"isEdit\"\r\n                    v-model=\"configData.m9Statistic\"\r\n                    :placeholder=\"$t('LocaleString.L00001', {\r\n      0: $t('LocaleString.L20150'),\r\n    })\r\n      \"\r\n                  ></InputNumber>\r\n                  <span v-else>{{ row.value }}</span>\r\n                </div>\r\n                <div v-if=\"row.section == 32\">\r\n                  <ColorPicker v-model=\"configData.stateColor\" v-if=\"isEdit\" />\r\n                  <span v-else>\r\n                    <div\r\n                      :style=\"{height:'20px',width:'20px',borderRadius:'2px' , backgroundColor: configData.stateColor}\"\r\n                    ></div>\r\n                  </span>\r\n                </div>\r\n              </template>\r\n            </Table>\r\n\r\n            <Row style=\"margin-top:10px\" v-if=\"isAdmin\">\r\n              <Col\r\n                span=\"4\"\r\n                style=\"padding-left:10px;line-height:32px\"\r\n              >{{ $t('LocaleString.L30155') }}</Col>\r\n              <Col span=\"20\">\r\n                <Input\r\n                  v-model=\"loginAccount\"\r\n                  :placeholder=\"$t('LocaleString.L00001', { 0: $t('LocaleString.L00002') })\r\n      \"\r\n                  style=\"width:200px;margin-right:10px\"\r\n                />\r\n                <Input\r\n                  type=\"password\"\r\n                  v-model=\"loginPassword\"\r\n                  autocomplete=\"new-password\"\r\n                  :placeholder=\"$t('LocaleString.L00001', { 0: $t('LocaleString.L00003') })\r\n      \"\r\n                  style=\"width:200px;margin-right:10px\"\r\n                />\r\n                <Button type=\"text\" @click=\"generateToken\" style=\"width:30px\">\r\n                  <img :src=\"generateIcon\" style=\"margin-left:-15px;width: 30px;;margin-right:5px\" />\r\n                </Button>\r\n                <Input\r\n                  type=\"password\"\r\n                  id=\"generateTokenData\"\r\n                  v-model=\"token\"\r\n                  autocomplete=\"new-password\"\r\n                  style=\"width:420px;margin-right:10px\"\r\n                />\r\n                <Button type=\"text\" @click=\"copyToken\" style=\"width:30px\">\r\n                  <img :src=\"copyIcon\" style=\"margin-left:-15px;width: 30px;\" />\r\n                </Button>\r\n              </Col>\r\n            </Row>\r\n          </TabPane>\r\n          <TabPane :label=\"$t('LocaleString.L30107')\" name=\"km\">\r\n            <Table\r\n              v-if=\"!isEdit\"\r\n              class=\"config-table\"\r\n              :columns=\"kmColumnsList\"\r\n              :data=\"kmConfigList\"\r\n              :no-data-text=\"noDataText\"\r\n            >\r\n              <template slot-scope=\"{ row, index }\" slot=\"type\">\r\n                <span>{{ row.type }}</span>\r\n              </template>\r\n            </Table>\r\n            <Table\r\n              v-if=\"isEdit\"\r\n              class=\"config-table\"\r\n              :columns=\"kmColumnsEdit\"\r\n              :data=\"kmConfigListEdit\"\r\n              :no-data-text=\"noDataText\"\r\n            >\r\n              <template slot-scope=\"{ row, index }\" slot=\"type\">\r\n                <span>{{ getObjectType(row.type) }}</span>\r\n              </template>\r\n              <template slot-scope=\"{ row, index }\" slot=\"longPress_cht\">\r\n                <Input\r\n                  type=\"text\"\r\n                  v-model=\"row.longPress_cht\"\r\n                  maxlength=\"20\"\r\n                  @on-change=\"onEditDataChange('longPress_cht', row, index)\"\r\n                />\r\n              </template>\r\n              <template slot-scope=\"{ row, index }\" slot=\"longPress_en\">\r\n                <Input\r\n                  type=\"text\"\r\n                  v-model=\"row.longPress_en\"\r\n                  maxlength=\"20\"\r\n                  @on-change=\"onEditDataChange('longPress_en', row, index)\"\r\n                />\r\n              </template>\r\n              <template slot-scope=\"{ row, index }\" slot=\"shortClick_cht\">\r\n                <Input\r\n                  type=\"text\"\r\n                  v-model=\"row.shortClick_cht\"\r\n                  maxlength=\"20\"\r\n                  @on-change=\"onEditDataChange('shortClick_cht', row, index)\"\r\n                />\r\n              </template>\r\n              <template slot-scope=\"{ row, index }\" slot=\"shortClick_en\">\r\n                <Input\r\n                  type=\"text\"\r\n                  v-model=\"row.shortClick_en\"\r\n                  maxlength=\"20\"\r\n                  @on-change=\"onEditDataChange('shortClick_en', row, index)\"\r\n                />\r\n              </template>\r\n              <template slot-scope=\"{ row, index }\" slot=\"doubleClick_cht\">\r\n                <Input\r\n                  type=\"text\"\r\n                  v-model=\"row.doubleClick_cht\"\r\n                  maxlength=\"20\"\r\n                  @on-change=\"onEditDataChange('doubleClick_cht', row, index)\"\r\n                />\r\n              </template>\r\n              <template slot-scope=\"{ row, index }\" slot=\"doubleClick_en\">\r\n                <Input\r\n                  type=\"text\"\r\n                  v-model=\"row.doubleClick_en\"\r\n                  maxlength=\"20\"\r\n                  @on-change=\"onEditDataChange('doubleClick_en', row, index)\"\r\n                />\r\n              </template>\r\n            </Table>\r\n          </TabPane>\r\n          <TabPane :label=\"$t('LocaleString.L30139')\" name=\"camera\" v-if=\"isAdmin\">\r\n            <Table\r\n              class=\"config-table\"\r\n              :columns=\"cameraColumnsList\"\r\n              :data=\"cameraConfigList\"\r\n              :no-data-text=\"noDataText\"\r\n            >\r\n              <template slot-scope=\"{ row, index }\" slot=\"capture\">\r\n                <i-switch\r\n                  v-if=\"isAdmin && isEdit\"\r\n                  v-model=\"row.capture\"\r\n                  @on-change=\"onCameraDataChange('capture', row, index)\"\r\n                />\r\n                <span v-if=\"!isEdit || !isAdmin\">\r\n                  {{ row.capture ? $t('LocaleString.D00003') : $t('LocaleString.D00004')\r\n                  }}\r\n                </span>\r\n              </template>\r\n              <template slot-scope=\"{ row, index }\" slot=\"prefixMinute\">\r\n                <InputNumber\r\n                  style=\"width:50px\"\r\n                  v-if=\"row.capture && isAdmin && isEdit\"\r\n                  v-model=\"row.prefixMinute\"\r\n                  :precision=\"0\"\r\n                  :min=\"0\"\r\n                  :max=\"15\"\r\n                  @on-change=\"onCameraDataChange('prefixMinute', row, index)\"\r\n                />\r\n                <span v-if=\"(!isEdit && row.capture) || !isAdmin\">{{ row.prefixMinute }}</span>\r\n              </template>\r\n              <template slot-scope=\"{ row, index }\" slot=\"suffixMinute\">\r\n                <InputNumber\r\n                  style=\"width:50px\"\r\n                  v-if=\"row.capture && isAdmin && isEdit\"\r\n                  v-model=\"row.suffixMinute\"\r\n                  :precision=\"0\"\r\n                  :min=\"0\"\r\n                  :max=\"15\"\r\n                  @on-change=\"onCameraDataChange('suffixMinute', row, index)\"\r\n                />\r\n                <span v-if=\"(!isEdit && row.capture) || !isAdmin\">{{ row.suffixMinute }}</span>\r\n              </template>\r\n              <template slot-scope=\"{ row, index }\" slot=\"backupDirectory\">\r\n                <Input\r\n                  v-model=\"row.backupDirectory\"\r\n                  @on-change=\"onCameraDataChange('backupDirectory', row, index)\"\r\n                  v-if=\"row.capture && isAdmin && isEdit\"\r\n                />\r\n                <span v-if=\"(!isEdit && row.capture) || !isAdmin\">{{ row.backupDirectory }}</span>\r\n              </template>\r\n              <template slot-scope=\"{ row, index }\" slot=\"account\">\r\n                <Input\r\n                  v-if=\"row.capture && isAdmin && isEdit\"\r\n                  v-model=\"row.account\"\r\n                  @on-change=\"onCameraDataChange('account', row, index)\"\r\n                />\r\n                <span v-if=\"(!isEdit && row.capture) || !isAdmin\">{{ row.account }}</span>\r\n              </template>\r\n              <template slot-scope=\"{ row, index }\" slot=\"password\">\r\n                <Input\r\n                  type=\"password\"\r\n                  v-model=\"row.password\"\r\n                  autocomplete=\"new-password\"\r\n                  @on-change=\"onCameraDataChange('password', row, index)\"\r\n                  v-if=\"row.capture && isAdmin && isEdit\"\r\n                />\r\n                <span v-if=\"(!isEdit && row.capture) || !isAdmin\">{{ row.password }}</span>\r\n              </template>\r\n              <template slot-scope=\"{ row, index }\" slot=\"keepDays\">\r\n                <InputNumber\r\n                  style=\"width:70px\"\r\n                  v-if=\"row.capture && isAdmin && isEdit\"\r\n                  v-model=\"row.keepDays\"\r\n                  :precision=\"0\"\r\n                  :min=\"0\"\r\n                  @on-change=\"onCameraDataChange('keepDays', row, index)\"\r\n                />\r\n                <span v-if=\"(!isEdit && row.capture) || !isAdmin\">{{ row.keepDays }}</span>\r\n              </template>\r\n              <template slot-scope=\"{ row, index }\" slot=\"positionCapture\">\r\n                <InputNumber\r\n                  v-if=\"row.capture && isAdmin && isEdit\"\r\n                  :precision=\"0\"\r\n                  :min=\"0\"\r\n                  :max=\"15\"\r\n                  v-model=\"row.positionCapture\"\r\n                  @on-change=\"onCameraDataChange('positionCapture', row, index)\"\r\n                />\r\n                <span v-if=\"(!isEdit && row.capture) || !isAdmin\">{{ row.positionCapture }}</span>\r\n              </template>\r\n              <template slot-scope=\"{ row, index }\" slot=\"action\">\r\n                <Button\r\n                  @click=\"testCameraConnection(row)\"\r\n                  type=\"primary\"\r\n                  class=\"font-small\"\r\n                  v-if=\"row.capture && isAdmin && isEdit\"\r\n                >\r\n                  {{\r\n                  $t(\"LocaleString.B30010\") }}\r\n                </Button>\r\n              </template>\r\n            </Table>\r\n          </TabPane>\r\n          <TabPane :label=\"$t('LocaleString.L30177')\" name=\"statisticLine\">\r\n            <Table\r\n              class=\"config-table\"\r\n              :columns=\"statisticLineColumnsList\"\r\n              :data=\"statisticLineConfigList\"\r\n              :no-data-text=\"noDataText\"\r\n            >\r\n              <template slot-scope=\"{ row }\" slot=\"type\">\r\n                <span>{{ row.type }}</span>\r\n              </template>\r\n              <template slot-scope=\"{ row,index }\" slot=\"interval\">\r\n                <Select\r\n                  :placeholder=\"$t('LocaleString.D00001')\"\r\n                  v-model=\"row.interval\"\r\n                  v-if=\"isEdit\"\r\n                  transfer\r\n                  @on-change=\"onStatisticLineDataChange('interval', row, index)\"\r\n                >\r\n                  <Option v-for=\"item in 10\" :value=\"item\" :key=\"item\">{{ item }}</Option>\r\n                </Select>\r\n                <span v-else>{{ row.interval }}</span>\r\n              </template>\r\n              <template slot-scope=\"{ row,index }\" slot=\"dataType\">\r\n                <Select\r\n                  :placeholder=\"$t('LocaleString.D00001')\"\r\n                  v-model=\"row.dataType\"\r\n                  v-if=\"isEdit\"\r\n                  transfer\r\n                  @on-change=\"onStatisticLineDataChange('dataType', row, index)\"\r\n                >\r\n                  <Option\r\n                    v-for=\"item in dataTypeList\"\r\n                    :value=\"item.key\"\r\n                    :key=\"item.key\"\r\n                  >{{ item.value }}</Option>\r\n                </Select>\r\n                <span v-else>{{ dataTypeList.find(item=> item.key == row.dataType).value }}</span>\r\n              </template>\r\n              <template slot-scope=\"{ row, index }\" slot=\"warningLine\">\r\n                <i-switch\r\n                  v-if=\"isEdit\"\r\n                  v-model=\"row.warningLine\"\r\n                  @on-change=\"onStatisticLineDataChange('warningLine', row, index)\"\r\n                />\r\n                <span\r\n                  v-else\r\n                >{{ row.warningLine == true ? $t('LocaleString.D30029') : $t('LocaleString.D30030') }}</span>\r\n              </template>\r\n            </Table>\r\n          </TabPane>\r\n          <TabPane :label=\"$t('LocaleString.L30194')\" name=\"thirdParty\" v-if=\"isAdmin\">\r\n            <Table\r\n              class=\"config-table\"\r\n              :columns=\"columns\"\r\n              :data=\"thirdPartyConfigList\"\r\n              :no-data-text=\"noDataText\"\r\n            >\r\n              <template slot-scope=\"{ row }\" slot=\"item\">\r\n                <span>{{ row.item }}</span>\r\n              </template>\r\n              <template slot-scope=\"{ row,index }\" slot=\"value\">\r\n                <div v-if=\"row.section == 1\">\r\n                  <i-switch\r\n                    @on-change=\"switchloginThirdParty\"\r\n                    v-model=\"thirdPartyData.loginThirdParty\"\r\n                    v-if=\"isEdit\"\r\n                  />\r\n                  <span\r\n                    style=\"margin-right:25px\"\r\n                    v-else\r\n                  >{{ viewLoginThirdParty == \"\" ? $t(\"LocaleString.B20010\") :viewLoginThirdParty }}</span>\r\n                </div>\r\n                <div v-if=\"row.section == 2\">\r\n                  <Input v-if=\"isEdit\" v-model=\"thirdPartyData.url\"></Input>\r\n                  <span v-else>{{ row.value }}</span>\r\n                </div>\r\n                <div v-if=\"row.section == 3\">\r\n                  <Input v-if=\"isEdit\" v-model=\"thirdPartyData.userName\"></Input>\r\n                  <span v-else>{{ row.value }}</span>\r\n                </div>\r\n                <div v-if=\"row.section == 4\">\r\n                  <Input v-if=\"isEdit\" v-model=\"thirdPartyData.secret\"></Input>\r\n                  <span v-else>{{ row.value }}</span>\r\n                </div>\r\n              </template>\r\n            </Table>\r\n            <Button\r\n              v-if=\"thirdPartyData.loginThirdParty\"\r\n              style=\"margin-top:5px\"\r\n              @click=\"testConnection\"\r\n            >{{ $t(\"LocaleString.B30010\")}}</Button>\r\n          </TabPane>\r\n        </Tabs>\r\n      </div>\r\n      <div slot=\"footer\" style=\"text-align: center; margin-top: 10px\">\r\n        <Button type=\"primary\" class=\"font-small\" @click=\"cancel\" v-show=\"!isEdit\">\r\n          {{ $t(\"LocaleString.B00044\")\r\n          }}\r\n        </Button>\r\n      </div>\r\n    </Modal>\r\n    <Modal\r\n      v-model=\"tipModal\"\r\n      :title=\"$t('LocaleString.L30061')\"\r\n      :mask-closable=\"false\"\r\n      :closable=\"false\"\r\n    >\r\n      <div class=\"message\">\r\n        <div>\r\n          <p>{{ $t(\"LocaleString.L30067\") }}</p>\r\n          <p>\r\n            <B>{{ $t(\"LocaleString.L30106\") }}</B>\r\n          </p>\r\n          <p>\r\n            <B>{{ $t(\"LocaleString.L30068\") }}</B>\r\n          </p>\r\n          <br />\r\n          <p>{{ $t(\"LocaleString.L30066\") }}</p>\r\n        </div>\r\n      </div>\r\n      <div slot=\"footer\" style=\"text-align: center; margin-top: 5px\">\r\n        <Button type=\"primary\" @click=\"tipModal = false\">\r\n          {{\r\n          $t(\"LocaleString.B00044\")\r\n          }}\r\n        </Button>\r\n      </div>\r\n    </Modal>\r\n    <Modal\r\n      v-model=\"tip2Modal\"\r\n      :title=\"$t('LocaleString.L30062')\"\r\n      :mask-closable=\"false\"\r\n      :closable=\"false\"\r\n    >\r\n      <div class=\"message\">\r\n        <div>\r\n          <p>{{ $t(\"LocaleString.L30063\") }}</p>\r\n          <p>\r\n            <B>{{ $t(\"LocaleString.L30064\") }}</B>\r\n          </p>\r\n          <p>\r\n            <B>{{ $t(\"LocaleString.L30065\") }}</B>\r\n          </p>\r\n          <br />\r\n          <p>{{ $t(\"LocaleString.L30066\") }}</p>\r\n        </div>\r\n      </div>\r\n      <div slot=\"footer\" style=\"text-align: center; margin-top: 5px\">\r\n        <Button type=\"primary\" @click=\"tip2Modal = false\">\r\n          {{\r\n          $t(\"LocaleString.B00044\")\r\n          }}\r\n        </Button>\r\n      </div>\r\n    </Modal>\r\n    <Modal\r\n      v-model=\"tip3Modal\"\r\n      :title=\"$t('LocaleString.L30101')\"\r\n      :mask-closable=\"false\"\r\n      :closable=\"false\"\r\n    >\r\n      <div class=\"message\">\r\n        <div>\r\n          <p>\r\n            <B>{{ $t(\"LocaleString.L30102\") }}</B>\r\n          </p>\r\n          <br />\r\n          <p>{{ $t(\"LocaleString.L30066\") }}</p>\r\n        </div>\r\n      </div>\r\n      <div slot=\"footer\" style=\"text-align: center; margin-top: 5px\">\r\n        <Button type=\"primary\" @click=\"tip3Modal = false\">\r\n          {{\r\n          $t(\"LocaleString.B00044\")\r\n          }}\r\n        </Button>\r\n      </div>\r\n    </Modal>\r\n    <Modal\r\n      v-model=\"tip4Modal\"\r\n      :title=\"$t('LocaleString.L30116')\"\r\n      :mask-closable=\"false\"\r\n      :closable=\"false\"\r\n    >\r\n      <div class=\"message\">\r\n        <div>\r\n          <p>{{ $t(\"LocaleString.L30117\") }}</p>\r\n        </div>\r\n      </div>\r\n      <div slot=\"footer\" style=\"text-align: center; margin-top: 5px\">\r\n        <Button type=\"primary\" @click=\"tip4Modal = false\">\r\n          {{\r\n          $t(\"LocaleString.B00044\")\r\n          }}\r\n        </Button>\r\n      </div>\r\n    </Modal>\r\n    <Modal\r\n      v-model=\"tip5Modal\"\r\n      :title=\"$t('LocaleString.L30129')\"\r\n      :mask-closable=\"false\"\r\n      :closable=\"false\"\r\n    >\r\n      <div class=\"message\">\r\n        <div>\r\n          <p>{{ $t(\"LocaleString.L30130\") }}</p>\r\n        </div>\r\n      </div>\r\n      <div slot=\"footer\" style=\"text-align: center; margin-top: 5px\">\r\n        <Button type=\"primary\" @click=\"tip5Modal = false\">\r\n          {{\r\n          $t(\"LocaleString.B00044\")\r\n          }}\r\n        </Button>\r\n      </div>\r\n    </Modal>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport resetIcon from \"@/assets/images/ic_reset.svg\";\r\nimport copyIcon from \"@/assets/images/ic_copy.png\";\r\nimport generateIcon from \"@/assets/images/ic_generate.png\";\r\nimport Config from \"@/common/config\";\r\nimport CommonService from \"@/components/administrative/apps/commonService\";\r\nimport InfoIcon from \"@/components/administrative/common/images/pointPlane/icon-ic-info.svg\";\r\nconst alertSound = require(\"@/assets/beep.mp3\"); // require the sound\r\nlet Base64 = require(\"js-base64\");\r\nlet moment = require(\"moment\");\r\nexport default {\r\n  data() {\r\n    return {\r\n      thirdPartyData: {\r\n        loginThirdParty: false,\r\n        url: \"\",\r\n        userName: \"\",\r\n        secret: \"\"\r\n      },\r\n      dataTypeList: [\r\n        { key: \"MEDIAN\", value: this.$t(\"LocaleString.L30178\") },\r\n        { key: \"MEAN\", value: this.$t(\"LocaleString.L30179\") },\r\n        { key: \"LAST\", value: this.$t(\"LocaleString.L30180\") }\r\n      ],\r\n      serviceCodeList: [],\r\n      serviceNameList: [],\r\n      copyIcon: copyIcon,\r\n      generateIcon: generateIcon,\r\n      loginAccount: \"\",\r\n      loginPassword: \"\",\r\n      token: \"\",\r\n      isAdmin: false,\r\n      openDeviceSelect: false,\r\n      description: \"\",\r\n      tabButtonWidth: {\r\n        width: \"1150px\"\r\n      },\r\n      modalWidth: 1200,\r\n      modalInnerHeight: window.innerHeight - 300,\r\n      currentTab: \"system\",\r\n      showCleanIcon: false,\r\n      resetIcon: resetIcon,\r\n      imgURL: \"\",\r\n      oriTab: \"\",\r\n      eventServiceCodeList: [],\r\n      serviceCodeLocaleStringsList: [],\r\n      submitLoading: false,\r\n      allDeviceTypeMenu: null,\r\n      deviceListLossSignal: [],\r\n      deviceListLossSignal2: [],\r\n      deviceListLowBattery: [],\r\n      deviceListAbnormalDevice: [],\r\n      alertSound: alertSound,\r\n      imgFile: null,\r\n      imgFileURL: null,\r\n      imgFileOriURL: null,\r\n      fileURL: null,\r\n      fileOriURL: null,\r\n      tempURL: null,\r\n      file: null,\r\n      isPlay: false,\r\n      tipModal: false,\r\n      tip2Modal: false,\r\n      tip3Modal: false,\r\n      tip4Modal: false,\r\n      tip5Modal: false,\r\n      InfoIcon: InfoIcon,\r\n      iconSizeTitle: \"\",\r\n      m5ObjectTypeTitle: \"\",\r\n      defaultPageSizeTitle: \"\",\r\n      defaultTabTitle: \"\",\r\n      origConfigData: {},\r\n      origThirdPartyData: {},\r\n      configData: {\r\n        defaultPageSize: \"\",\r\n        m5ObjectType: \"all\",\r\n        m1LongPress: [\"fusion-km7\"],\r\n        multiPlanes: false,\r\n        stuckPlane: false,\r\n        selectedPlanes: [],\r\n        planeList: [],\r\n        systemName: \"\",\r\n        tabList: [\"m1\", \"m2\", \"m3\", \"m4\", \"m5\", \"m6\", \"m8\"],\r\n        soundURL: null,\r\n        sound: false,\r\n        repeatSound: 0,\r\n        m2List: [\r\n          \"temperature\",\r\n          \"heartRate\",\r\n          \"bloodOxygen\",\r\n          \"bloodPressure\",\r\n          \"step\"\r\n        ],\r\n        iconSize: \"40\",\r\n        defaultTab: \"m1\",\r\n        updateSeconds: \"30\",\r\n        grayTime: 15,\r\n        m2Line: false,\r\n        eventAuthClose: false,\r\n        eventKeepDays: \"2\",\r\n        influxRange: 8,\r\n        lossSignal: 1,\r\n        lossSignal2: 60,\r\n        lowBattery: 30,\r\n        lossSignalDevices: null,\r\n        lossSignalDevices2: [],\r\n        lowBatteryDevices: null,\r\n        abnormalDevices: null,\r\n        skipEvent: [],\r\n        deviceSelectList: [],\r\n        license: [],\r\n        showLeaveBed: false,\r\n        m2Statistic: 10,\r\n        m3Statistic: 10,\r\n        m4Statistic: 60,\r\n        m8Statistic: 60,\r\n        m9Statistic: 60,\r\n        stateColor: \"#000000\",\r\n        logoutTime: 0\r\n      },\r\n      viewTab: \"\",\r\n      viewLicense: \"\",\r\n      viewPlaySound: \"\",\r\n      viewM2Line: \"\",\r\n      viewM2List: \"\",\r\n      viewEventAuthClose: \"\",\r\n      viewEventKeepDays: \"\",\r\n      viewLogo: \"\",\r\n      viewMultiPlanes: \"\",\r\n      viewStuckPlane: \"\",\r\n      viewLoginThirdParty: \"\",\r\n      isEdit: false,\r\n      isShow: false,\r\n\r\n      configList: [],\r\n      configListToSave: [],\r\n      noDataText: this.$t(\"LocaleString.L00081\"),\r\n      kmObjectTypeList: [\r\n        {\r\n          type: \"equipment\",\r\n          name: this.$t(\"LocaleString.D00008\")\r\n        },\r\n        {\r\n          type: \"people\",\r\n          name: this.$t(\"LocaleString.D00007\")\r\n        },\r\n        {\r\n          type: \"space\",\r\n          name: this.$t(\"LocaleString.D00009\")\r\n        },\r\n        {\r\n          type: \"other\",\r\n          name: this.$t(\"LocaleString.D00010\")\r\n        }\r\n      ],\r\n      cameraTypeList: [\r\n        {\r\n          type: \"help\",\r\n          name: this.$t(\"LocaleString.D00030\")\r\n        },\r\n        {\r\n          type: \"enter\",\r\n          name: this.$t(\"LocaleString.D00029\")\r\n        },\r\n        {\r\n          type: \"leave\",\r\n          name: this.$t(\"LocaleString.D00031\")\r\n        },\r\n        {\r\n          type: \"fall\",\r\n          name: this.$t(\"LocaleString.D00035\")\r\n        }\r\n      ],\r\n      cameraColumnsList: [\r\n        {\r\n          title: this.$t(\"LocaleString.L00047\"),\r\n          slot: \"type\",\r\n          width: 90,\r\n          render: (h, params) => {\r\n            return h(\"span\", this.getCameraType(params.row.type));\r\n          }\r\n        },\r\n        {\r\n          title: this.$t(\"LocaleString.L30140\"),\r\n          slot: \"capture\",\r\n          width: 70\r\n        },\r\n        {\r\n          title: this.$t(\"LocaleString.L30141\"),\r\n          slot: \"prefixMinute\",\r\n          width: 85\r\n        },\r\n        {\r\n          title: this.$t(\"LocaleString.L30142\"),\r\n          slot: \"suffixMinute\",\r\n          width: 85\r\n        },\r\n        {\r\n          title: this.$t(\"LocaleString.L30143\"),\r\n          slot: \"backupDirectory\"\r\n        },\r\n        {\r\n          title: this.$t(\"LocaleString.L30144\"),\r\n          slot: \"account\",\r\n          width: 110\r\n        },\r\n        {\r\n          title: this.$t(\"LocaleString.L00003\"),\r\n          slot: \"password\",\r\n          width: 110\r\n        },\r\n        {\r\n          title: this.$t(\"LocaleString.L30145\"),\r\n          slot: \"keepDays\",\r\n          width: 90\r\n        },\r\n        {\r\n          title: this.$t(\"LocaleString.L30146\"),\r\n          slot: \"positionCapture\",\r\n          width: 120\r\n        },\r\n        {\r\n          title: \" \",\r\n          slot: \"action\",\r\n          width: 110\r\n        }\r\n      ],\r\n      statisticLineColumnsList: [\r\n        {\r\n          title: this.$t(\"LocaleString.L30057\"),\r\n          slot: \"type\"\r\n        },\r\n        {\r\n          title: this.$t(\"LocaleString.L30150\"),\r\n          slot: \"interval\"\r\n        },\r\n        {\r\n          title: this.$t(\"LocaleString.L00157\"),\r\n          slot: \"dataType\"\r\n        },\r\n        {\r\n          title: this.$t(\"LocaleString.L30151\"),\r\n          slot: \"warningLine\"\r\n        }\r\n      ],\r\n      defaultStatisticLineConfigList: [\r\n        {\r\n          type: this.$t(\"LocaleString.F30002\"),\r\n          interval: 1,\r\n          dataType: \"MEDIAN\",\r\n          warningLine: true,\r\n          entry: \"m2\"\r\n        },\r\n        {\r\n          type: this.$t(\"LocaleString.F30003\"),\r\n          interval: 1,\r\n          dataType: \"MEDIAN\",\r\n          warningLine: true,\r\n          entry: \"m3\"\r\n        },\r\n        {\r\n          type: this.$t(\"LocaleString.F30004\"),\r\n          interval: 1,\r\n          dataType: \"MEDIAN\",\r\n          warningLine: true,\r\n          entry: \"m4\"\r\n        },\r\n        {\r\n          type: this.$t(\"LocaleString.F30016\"),\r\n          interval: 1,\r\n          dataType: \"MEDIAN\",\r\n          warningLine: true,\r\n          entry: \"m8\"\r\n        }\r\n      ],\r\n      statisticLineConfigList: [],\r\n      thirdPartyConfigList: [\r\n        {\r\n          item: this.$t(\"LocaleString.L30195\"),\r\n          section: 1,\r\n          value: false\r\n        },\r\n        {\r\n          item: this.$t(\"LocaleString.L30196\"),\r\n          section: 2,\r\n          value: \"\"\r\n        },\r\n        {\r\n          item: \"UserName\",\r\n          section: 3,\r\n          value: \"\"\r\n        },\r\n        {\r\n          item: \"CredentialSecret\",\r\n          section: 4,\r\n          value: \"\"\r\n        }\r\n      ],\r\n      defaultCameraConfigList: [\r\n        {\r\n          type: \"help\",\r\n          capture: false,\r\n          prefixMinute: 3,\r\n          suffixMinute: 3,\r\n          backupDirectory: \"\",\r\n          account: \"\",\r\n          password: \"\",\r\n          keepDays: 90,\r\n          positionCapture: 3\r\n        },\r\n        {\r\n          type: \"enter\",\r\n          capture: false,\r\n          prefixMinute: 3,\r\n          suffixMinute: 3,\r\n          backupDirectory: \"\",\r\n          account: \"\",\r\n          password: \"\",\r\n          keepDays: 90,\r\n          positionCapture: 3\r\n        },\r\n        {\r\n          type: \"leave\",\r\n          capture: false,\r\n          prefixMinute: 3,\r\n          suffixMinute: 3,\r\n          backupDirectory: \"\",\r\n          account: \"\",\r\n          password: \"\",\r\n          keepDays: 90,\r\n          positionCapture: 3\r\n        },\r\n        {\r\n          type: \"fall\",\r\n          capture: false,\r\n          prefixMinute: 3,\r\n          suffixMinute: 3,\r\n          backupDirectory: \"\",\r\n          account: \"\",\r\n          password: \"\",\r\n          keepDays: 90,\r\n          positionCapture: 3\r\n        }\r\n      ],\r\n      cameraConfigList: [],\r\n      kmColumnsList: [\r\n        {\r\n          title: this.$t(\"LocaleString.L00047\"),\r\n          key: \"type\",\r\n          width: 100,\r\n          render: (h, params) => {\r\n            return h(\"span\", this.getObjectType(params.row.type));\r\n          }\r\n        },\r\n        {\r\n          title: this.$t(\"LocaleString.L30108\"),\r\n          key: \"longPress_cht\"\r\n        },\r\n        {\r\n          title: this.$t(\"LocaleString.L30109\"),\r\n          key: \"longPress_en\"\r\n        },\r\n        {\r\n          title: this.$t(\"LocaleString.L30110\"),\r\n          key: \"shortClick_cht\"\r\n        },\r\n        {\r\n          title: this.$t(\"LocaleString.L30111\"),\r\n          key: \"shortClick_en\"\r\n        },\r\n        {\r\n          title: this.$t(\"LocaleString.L30112\"),\r\n          key: \"doubleClick_cht\"\r\n        },\r\n        {\r\n          title: this.$t(\"LocaleString.L30113\"),\r\n          key: \"doubleClick_en\"\r\n        }\r\n      ],\r\n      kmColumnsEdit: [\r\n        {\r\n          title: this.$t(\"LocaleString.L00047\"),\r\n          slot: \"type\",\r\n          width: 100,\r\n          render: (h, params) => {\r\n            return h(\"span\", this.getObjectType(params.row.type));\r\n          }\r\n        },\r\n        {\r\n          title: this.$t(\"LocaleString.L30108\"),\r\n          slot: \"longPress_cht\"\r\n        },\r\n        {\r\n          title: this.$t(\"LocaleString.L30109\"),\r\n          slot: \"longPress_en\"\r\n        },\r\n        {\r\n          title: this.$t(\"LocaleString.L30110\"),\r\n          slot: \"shortClick_cht\"\r\n        },\r\n        {\r\n          title: this.$t(\"LocaleString.L30111\"),\r\n          slot: \"shortClick_en\"\r\n        },\r\n        {\r\n          title: this.$t(\"LocaleString.L30112\"),\r\n          slot: \"doubleClick_cht\"\r\n        },\r\n        {\r\n          title: this.$t(\"LocaleString.L30113\"),\r\n          slot: \"doubleClick_en\"\r\n        }\r\n      ],\r\n      kmConfigList: [\r\n        {\r\n          type: \"equipment\",\r\n          longPress_cht: \"\",\r\n          longPress_en: \"\",\r\n          shortClick_cht: \"\",\r\n          shortClick_en: \"\",\r\n          doubleClick_cht: \"\",\r\n          doubleClick_en: \"\"\r\n        },\r\n        {\r\n          type: \"people\",\r\n          longPress_cht: \"\",\r\n          longPress_en: \"\",\r\n          shortClick_cht: \"\",\r\n          shortClick_en: \"\",\r\n          doubleClick_cht: \"\",\r\n          doubleClick_en: \"\"\r\n        },\r\n        {\r\n          type: \"space\",\r\n          longPress_cht: \"\",\r\n          longPress_en: \"\",\r\n          shortClick_cht: \"\",\r\n          shortClick_en: \"\",\r\n          doubleClick_cht: \"\",\r\n          doubleClick_en: \"\"\r\n        },\r\n        {\r\n          type: \"other\",\r\n          longPress_cht: \"\",\r\n          longPress_en: \"\",\r\n          shortClick_cht: \"\",\r\n          shortClick_en: \"\",\r\n          doubleClick_cht: \"\",\r\n          doubleClick_en: \"\"\r\n        }\r\n      ],\r\n      kmConfigListEdit: [\r\n        {\r\n          type: \"equipment\",\r\n          longPress_cht: \"\",\r\n          longPress_en: \"\",\r\n          shortClick_cht: \"\",\r\n          shortClick_en: \"\",\r\n          doubleClick_cht: \"\",\r\n          doubleClick_en: \"\"\r\n        },\r\n        {\r\n          type: \"people\",\r\n          longPress_cht: \"\",\r\n          longPress_en: \"\",\r\n          shortClick_cht: \"\",\r\n          shortClick_en: \"\",\r\n          doubleClick_cht: \"\",\r\n          doubleClick_en: \"\"\r\n        },\r\n        {\r\n          type: \"space\",\r\n          longPress_cht: \"\",\r\n          longPress_en: \"\",\r\n          shortClick_cht: \"\",\r\n          shortClick_en: \"\",\r\n          doubleClick_cht: \"\",\r\n          doubleClick_en: \"\"\r\n        },\r\n        {\r\n          type: \"other\",\r\n          longPress_cht: \"\",\r\n          longPress_en: \"\",\r\n          shortClick_cht: \"\",\r\n          shortClick_en: \"\",\r\n          doubleClick_cht: \"\",\r\n          doubleClick_en: \"\"\r\n        }\r\n      ],\r\n      columns: [\r\n        {\r\n          title: this.$t(\"LocaleString.L00183\"),\r\n          slot: \"item\"\r\n          // width: 360\r\n        },\r\n        {\r\n          title: this.$t(\"LocaleString.L00184\"),\r\n          slot: \"value\"\r\n        }\r\n      ],\r\n      dataSection0: [\r\n        {\r\n          item: this.$t(\"LocaleString.F00004\"),\r\n          section: 23,\r\n          value: \"\"\r\n        }\r\n      ],\r\n      dataSection01: [\r\n        {\r\n          item: this.$t(\"LocaleString.L30118\"),\r\n          section: 0,\r\n          value: \"\"\r\n        },\r\n        {\r\n          item: this.$t(\"LocaleString.L30134\"),\r\n          section: 17,\r\n          value: \"\"\r\n        },\r\n        {\r\n          item: this.$t(\"LocaleString.L00446\"),\r\n          section: 31,\r\n          value: \"\"\r\n        }\r\n      ],\r\n      data: [\r\n        {\r\n          item: this.$t(\"LocaleString.L30138\"),\r\n          section: 18,\r\n          value: \"\"\r\n        },\r\n        {\r\n          item: this.$t(\"LocaleString.L30188\"),\r\n          section: 30,\r\n          value: \"\"\r\n        },\r\n        {\r\n          item: this.$t(\"LocaleString.L30057\"),\r\n          section: 1,\r\n          value: \"\"\r\n        },\r\n        {\r\n          item: this.$t(\"LocaleString.L30055\"),\r\n          section: 2,\r\n          value: \"\"\r\n        },\r\n        {\r\n          item: this.$t(\"LocaleString.L30056\"),\r\n          section: 3,\r\n          value: \"\"\r\n        },\r\n        {\r\n          item: this.$t(\"LocaleString.L30058\"),\r\n          section: 4,\r\n          value: \"\"\r\n        },\r\n        {\r\n          item: this.$t(\"LocaleString.L30060\"),\r\n          section: 5,\r\n          value: \"\"\r\n        },\r\n        {\r\n          item: this.$t(\"LocaleString.L30061\"),\r\n          section: 6,\r\n          value: \"\"\r\n        },\r\n        {\r\n          item: this.$t(\"LocaleString.L30062\"),\r\n          section: 7,\r\n          value: 1\r\n        },\r\n        {\r\n          item: this.$t(\"LocaleString.L30085\"),\r\n          section: 8,\r\n          value: \"\"\r\n        },\r\n        {\r\n          item: this.$t(\"LocaleString.L30101\"),\r\n          section: 11,\r\n          value: \"\"\r\n        },\r\n        {\r\n          item: this.$t(\"LocaleString.L30103\"),\r\n          section: 12,\r\n          value: \"\",\r\n          valueDevices: []\r\n        },\r\n        {\r\n          item: this.$t(\"LocaleString.L30156\"),\r\n          section: 22,\r\n          value: \"\",\r\n          valueDevices: []\r\n        },\r\n        {\r\n          item: this.$t(\"LocaleString.L30104\"),\r\n          section: 13,\r\n          value: \"\",\r\n          valueDevices: []\r\n        },\r\n        {\r\n          item: this.$t(\"LocaleString.L30115\"),\r\n          section: 15,\r\n          value: \"\",\r\n          valueDevices: []\r\n        },\r\n        {\r\n          item: this.$t(\"LocaleString.L30116\"),\r\n          section: 14,\r\n          value: \"\",\r\n          valueSkipEvent: []\r\n        },\r\n        {\r\n          item: this.$t(\"LocaleString.L30129\"),\r\n          section: 16,\r\n          value: \"\",\r\n          valueDeviceSelectList: []\r\n        },\r\n        {\r\n          item: this.$t(\"LocaleString.L30136\"),\r\n          section: 19,\r\n          value: \"\",\r\n          valueM1LongPressList: []\r\n        },\r\n        {\r\n          item: this.$t(\"LocaleString.L30159\"),\r\n          section: 24,\r\n          value: \"\"\r\n        },\r\n        {\r\n          item: this.$t(\"LocaleString.L30153\"),\r\n          section: 20,\r\n          value: \"\"\r\n        },\r\n        {\r\n          item: this.$t(\"LocaleString.L30154\"),\r\n          section: 21,\r\n          value: \"\"\r\n        },\r\n        {\r\n          item: this.$t(\"LocaleString.L30182\"),\r\n          section: 25,\r\n          value: \"\"\r\n        },\r\n\r\n        {\r\n          item: this.$t(\"LocaleString.L30183\"),\r\n          section: 26,\r\n          value: \"\"\r\n        },\r\n        {\r\n          item: this.$t(\"LocaleString.L30184\"),\r\n          section: 27,\r\n          value: \"\"\r\n        },\r\n\r\n        {\r\n          item: this.$t(\"LocaleString.L30185\"),\r\n          section: 28,\r\n          value: \"\"\r\n        },\r\n\r\n        {\r\n          item: this.$t(\"LocaleString.L30186\"),\r\n          section: 29,\r\n          value: \"\"\r\n        },\r\n        {\r\n          item: this.$t(\"LocaleString.L30214\"),\r\n          section: 32,\r\n          value: \"\"\r\n        },\r\n        {\r\n          item: this.$t(\"LocaleString.L30082\"),\r\n          section: 9,\r\n          value: \"\"\r\n        },\r\n        {\r\n          item: this.$t(\"LocaleString.L30083\"),\r\n          section: 10,\r\n          value: \"\"\r\n        }\r\n      ],\r\n      iconSizeList: [\r\n        {\r\n          label: \"32*32\",\r\n          value: \"32\"\r\n        },\r\n        {\r\n          label: \"40*40\",\r\n          value: \"40\"\r\n        }\r\n      ],\r\n      m5ObjectTypeList: [\r\n        {\r\n          label: this.$t(\"LocaleString.D00002\"),\r\n          value: \"all\"\r\n        },\r\n        {\r\n          label: this.$t(\"LocaleString.D00007\"),\r\n          value: \"people\"\r\n        },\r\n        {\r\n          label: this.$t(\"LocaleString.D00008\"),\r\n          value: \"equipment\"\r\n        },\r\n        {\r\n          label: this.$t(\"LocaleString.D00009\"),\r\n          value: \"space\"\r\n        },\r\n        {\r\n          label: this.$t(\"LocaleString.D00010\"),\r\n          value: \"other\"\r\n        }\r\n      ],\r\n      pageSizeList: [\r\n        {\r\n          label: \"10/\" + this.$t(\"LocaleString.L00020\"),\r\n          value: \"10\"\r\n        },\r\n        {\r\n          label: \"25/\" + this.$t(\"LocaleString.L00020\"),\r\n          value: \"25\"\r\n        },\r\n        {\r\n          label: \"50/\" + this.$t(\"LocaleString.L00020\"),\r\n          value: \"50\"\r\n        },\r\n        {\r\n          label: \"100/\" + this.$t(\"LocaleString.L00020\"),\r\n          value: \"100\"\r\n        }\r\n      ],\r\n      showingTab: [\r\n        {\r\n          label: this.$t(\"LocaleString.D30031\"),\r\n          value: \"m1\"\r\n        },\r\n        {\r\n          label: this.$t(\"LocaleString.D30032\"),\r\n          value: \"m2\"\r\n        },\r\n        {\r\n          label: this.$t(\"LocaleString.D30033\"),\r\n          value: \"m3\"\r\n        },\r\n        {\r\n          label: this.$t(\"LocaleString.D30034\"),\r\n          value: \"m4\"\r\n        },\r\n        {\r\n          label: this.$t(\"LocaleString.D30035\"),\r\n          value: \"m5\"\r\n        },\r\n        {\r\n          label: this.$t(\"LocaleString.D30036\"),\r\n          value: \"m6\"\r\n        },\r\n        {\r\n          label: this.$t(\"LocaleString.F30016\"),\r\n          value: \"m8\"\r\n        },\r\n        {\r\n          label: this.$t(\"LocaleString.D30044\"),\r\n          value: \"m9\"\r\n        }\r\n        // {\r\n        //   label: \"軌跡監控\",\r\n        //   value: \"m7\",\r\n        // },\r\n      ],\r\n      showingType: [\r\n        {\r\n          label: this.$t(\"LocaleString.L30059\"),\r\n          value: \"temperature\"\r\n        },\r\n        {\r\n          label: this.$t(\"LocaleString.L30010\"),\r\n          value: \"heartRate\"\r\n        },\r\n        {\r\n          label: this.$t(\"LocaleString.L30013\"),\r\n          value: \"bloodOxygen\"\r\n        },\r\n        {\r\n          label: this.$t(\"LocaleString.L30037\"),\r\n          value: \"bloodPressure\"\r\n        },\r\n        {\r\n          label: this.$t(\"LocaleString.L30208\"),\r\n          value: \"step\"\r\n        },\r\n        {\r\n          label: this.$t(\"LocaleString.L30105\"),\r\n          value: \"breathe\"\r\n        }\r\n      ],\r\n      defaultTabList: [],\r\n      defaultUpdateSeconds: [\r\n        {\r\n          label: \"0\",\r\n          value: \"0\"\r\n        },\r\n        {\r\n          label: \"30\",\r\n          value: \"30\"\r\n        },\r\n        {\r\n          label: \"60\",\r\n          value: \"60\"\r\n        },\r\n        {\r\n          label: \"120\",\r\n          value: \"120\"\r\n        }\r\n      ],\r\n      defaultInfluxRange: [\r\n        {\r\n          label: \"8\",\r\n          value: 8\r\n        },\r\n        {\r\n          label: \"12\",\r\n          value: 12\r\n        },\r\n        {\r\n          label: \"24\",\r\n          value: 24\r\n        },\r\n        {\r\n          label: \"48\",\r\n          value: 48\r\n        },\r\n        {\r\n          label: \"72\",\r\n          value: 72\r\n        }\r\n      ]\r\n    };\r\n  },\r\n  created() {},\r\n  async mounted() {\r\n    console.log(\"load systemConfig\");\r\n    await this.getEventLocaleStringList();\r\n    await this.getServiceCodeMenu();\r\n    this.getDeviceTypeMenu();\r\n    this.loadKMData();\r\n    this.loadCameraData();\r\n    this.loadStatisticLineData();\r\n    this.isShow = true;\r\n    let identity = localStorage.getItem(\"sns_identity\");\r\n    this.isAdmin = identity == \"true\" ? true : false;\r\n\r\n    let permission = localStorage.getItem(\"sns_permission\");\r\n    if (permission.includes(\"[sns]-[1]-[view]\") || identity === \"true\") {\r\n      this.data = [...this.dataSection01, ...this.data];\r\n    }\r\n    if (identity === \"true\") {\r\n      this.data = [...this.dataSection0, ...this.data];\r\n    }\r\n    this.cameraConfigList = this.defaultCameraConfigList;\r\n    this.statisticLineConfigList = this.defaultStatisticLineConfigList;\r\n    window.addEventListener(\"resize\", this.resizeHeight);\r\n    //this.getSystemConfig();\r\n    // this.normalizeHeight();\r\n  },\r\n  beforeDestroy() {\r\n    window.removeEventListener(\"resize\", this.resizeHeight);\r\n  },\r\n  methods: {\r\n    async testConnection() {\r\n      let errorMsg = [\r\n        this.$t(\"LocaleString.L30196\"),\r\n        \"UserName\",\r\n        \"CredentialSecret\"\r\n      ];\r\n      if (\r\n        this.thirdPartyData.url == \"\" ||\r\n        this.thirdPartyData.userName == \"\" ||\r\n        this.thirdPartyData.secret == \"\"\r\n      ) {\r\n        this.$Notice.error({\r\n          title: this.$t(\"LocaleString.E00037\"),\r\n          desc: this.$t(\"LocaleString.W00038\", { 0: errorMsg.join(\",\") }),\r\n          duration: Config.WARNING_DURATION\r\n        });\r\n        return;\r\n      }\r\n      this.$serviceThirdParty.testConnection.requestCommon(\r\n        this.thirdPartyData.url\r\n      );\r\n      let result = await this.$serviceThirdParty.testConnection.send(\r\n        null,\r\n        null,\r\n        {\r\n          userName: this.thirdPartyData.userName,\r\n          secret: this.thirdPartyData.secret\r\n        }\r\n      );\r\n      if (result) {\r\n        this.$Notice.success({\r\n          title: this.$t(\"LocaleString.M00004\"),\r\n          desc:\r\n            this.$t(\"LocaleString.D00063\") +\r\n            \" \" +\r\n            this.$t(\"LocaleString.D00167\"),\r\n          duration: Config.SUCCESS_DURATION\r\n        });\r\n      }\r\n    },\r\n    getEditPermission() {\r\n      let isAllow = false;\r\n\r\n      let permission = localStorage.getItem(\"sns_permission\");\r\n      if (permission.includes(\"[sns]-[1]-[edit]\") || this.isAdmin) {\r\n        isAllow = true;\r\n      }\r\n      return isAllow;\r\n    },\r\n\r\n    switchloginThirdParty(entry) {\r\n      if (!entry) {\r\n        this.thirdPartyData.url = \"\";\r\n        this.thirdPartyData.userName = \"\";\r\n        this.thirdPartyData.secret = \"\";\r\n      }\r\n    },\r\n    resizeHeight() {\r\n      this.modalInnerHeight =\r\n        this.currentTab == \"system\" ? window.innerHeight - 300 : 250;\r\n    },\r\n    async getServiceCodeMenu() {\r\n      let configParams = {\r\n        // hasLicense: true,\r\n        category: \"event\"\r\n      };\r\n      let tmperviceCodeList = await this.$service.getServices.send(\r\n        configParams\r\n      );\r\n      this.serviceCodeList = tmperviceCodeList.results;\r\n      this.serviceCodeList.forEach(item => {\r\n        let transName = this.serviceCodeLocaleStringsList.some(\r\n          data => data.key == item.code\r\n        )\r\n          ? this.serviceCodeLocaleStringsList.find(\r\n              data => data.key == item.code\r\n            ).value\r\n          : item.code;\r\n\r\n        item.key = item.code;\r\n        item.value = transName;\r\n      });\r\n    },\r\n    copyToken() {\r\n      navigator.clipboard.writeText(this.token);\r\n      this.$Notice.success({\r\n        title: this.$t(\"LocaleString.M00004\"),\r\n        desc: this.$t(\"LocaleString.M30006\"),\r\n        duration: Config.SUCCESS_DURATION\r\n      });\r\n    },\r\n    async generateToken() {\r\n      let postData = {\r\n        account: this.loginAccount,\r\n        password: this.loginPassword\r\n      };\r\n\r\n      let valid = await this.$service.getAuthTokenNoload.send(postData);\r\n      if (valid) {\r\n        let res = await this.$service.generateToken.send(postData);\r\n        this.token = location.origin + \"/sns/#/user/login?direct=\" + res;\r\n\r\n        this.$Notice.success({\r\n          title: this.$t(\"LocaleString.M00004\"),\r\n          desc: this.$t(\"LocaleString.M30007\"),\r\n          duration: Config.SUCCESS_DURATION\r\n        });\r\n      }\r\n    },\r\n    async testCameraConnection(row) {\r\n      let errorMessage = \"\";\r\n      if (row.backupDirectory == \"\") {\r\n        errorMessage += this.$t(\"LocaleString.W00038\", {\r\n          0: this.$t(\"LocaleString.L30143\")\r\n        });\r\n      }\r\n      if (row.account == \"\") {\r\n        errorMessage += this.$t(\"LocaleString.W00038\", {\r\n          0: this.$t(\"LocaleString.L30144\")\r\n        });\r\n      }\r\n      if (row.password == \"\") {\r\n        errorMessage += this.$t(\"LocaleString.W00038\", {\r\n          0: this.$t(\"LocaleString.L00003\")\r\n        });\r\n      }\r\n      if (errorMessage != \"\") {\r\n        this.$Notice.warning({\r\n          title: this.$t(\"LocaleString.W00005\"),\r\n          desc: errorMessage,\r\n          duration: Config.WARNING_DURATION\r\n        });\r\n        return false;\r\n      }\r\n\r\n      let params = {\r\n        filepath: row.backupDirectory,\r\n        username: row.account,\r\n        userAuth: Base64.encode(row.password)\r\n      };\r\n\r\n      let connetResult = await this.$service.testCameraConnection.send(params);\r\n      if (connetResult == \"\") {\r\n        this.$Notice.success({\r\n          title: this.$t(\"LocaleString.M00004\"), //成功\r\n          desc:\r\n            this.$t(\"LocaleString.B30010\") +\r\n            \" \" +\r\n            this.$t(\"LocaleString.M00004\"),\r\n          duration: Config.WARNING_DURATION\r\n        });\r\n      }\r\n    },\r\n    async onCameraDataChange(type, row, index) {\r\n      switch (type) {\r\n        case \"capture\":\r\n          this.cameraConfigList[index].capture = row.capture;\r\n          break;\r\n        case \"prefixMinute\":\r\n          this.cameraConfigList[index].prefixMinute = row.prefixMinute;\r\n          break;\r\n        case \"suffixMinute\":\r\n          this.cameraConfigList[index].suffixMinute = row.suffixMinute;\r\n          break;\r\n        case \"backupDirectory\":\r\n          this.cameraConfigList[index].backupDirectory = row.backupDirectory;\r\n          break;\r\n        case \"account\":\r\n          this.cameraConfigList[index].account = row.account;\r\n          break;\r\n        case \"password\":\r\n          this.cameraConfigList[index].password = row.password;\r\n          break;\r\n        case \"keepDays\":\r\n          this.cameraConfigList[index].keepDays = row.keepDays;\r\n          break;\r\n        case \"positionCapture\":\r\n          this.cameraConfigList[index].positionCapture = row.positionCapture;\r\n          break;\r\n      }\r\n      // console.log(this.cameraConfigList);\r\n    },\r\n\r\n    async onStatisticLineDataChange(type, row, index) {\r\n      switch (type) {\r\n        case \"interval\":\r\n          this.statisticLineConfigList[index].interval = row.interval;\r\n          break;\r\n        case \"dataType\":\r\n          this.statisticLineConfigList[index].dataType = row.dataType;\r\n          break;\r\n        case \"warningLine\":\r\n          this.statisticLineConfigList[index].warningLine = row.warningLine;\r\n          break;\r\n      }\r\n      // console.log(this.cameraConfigList);\r\n    },\r\n    /**\r\n      when change device default select List action\r\n      1. default is all\r\n      2. when disable all, auto insert device type which exist from console\r\n      3. when diable a device type which is included in DefaultDeviceTypeList(from API) , it will show alert dialog and reinsert to selection\r\n     **/\r\n    openDeviceSelection(data) {\r\n      this.openDeviceSelect = data;\r\n    },\r\n    changeDeviceDefaultSelectList(item) {\r\n      if (this.openDeviceSelect) {\r\n        if (item.filter(data => data == \"all\").length > 0) {\r\n          this.configData.deviceSelectList = [\"all\"];\r\n        } else {\r\n          this.$service.getDefaultDeviceTypeList\r\n            .send(null)\r\n            .then(defaultList => {\r\n              let defaultSelectList = defaultList.deviceList.split(\",\");\r\n              if (item.length == 0) {\r\n                this.configData.deviceSelectList = defaultSelectList;\r\n              } else {\r\n                defaultSelectList.forEach(item => {\r\n                  if (!this.configData.deviceSelectList.includes(item)) {\r\n                    let deviceName = \"\";\r\n                    if (\r\n                      this.deviceDefaultSelection.some(data =>\r\n                        item.includes(data.key)\r\n                      )\r\n                    ) {\r\n                      deviceName = this.deviceDefaultSelection.find(data =>\r\n                        item.includes(data.key)\r\n                      ).value;\r\n                      this.$Notice.error({\r\n                        title: this.$t(\"LocaleString.E00037\"),\r\n                        desc: this.$t(\"LocaleString.W30011\", { 0: deviceName }),\r\n                        duration: Config.errorDuration\r\n                      });\r\n                      this.configData.deviceSelectList.push(item);\r\n                    }\r\n                  }\r\n                });\r\n              }\r\n            });\r\n        }\r\n      }\r\n    },\r\n    async getEventLocaleStringList() {\r\n      let params = {\r\n        search: \"\"\r\n      };\r\n      let tmp = [];\r\n      let res = await this.$service.getServiceCodeLocaleStrings.send(params);\r\n      res.results.forEach(r => {\r\n        tmp.push({\r\n          key: r.code,\r\n          value: this.$t(\"LocaleString.\" + r.langs.nameId)\r\n        });\r\n      });\r\n      this.serviceCodeLocaleStringsList = JSON.parse(JSON.stringify(tmp));\r\n    },\r\n    changeMultiPlanes(val) {\r\n      this.configData.selectedPlanes = val;\r\n    },\r\n    switchMultiPlanes(val) {\r\n      if (!val) {\r\n        this.configData.selectedPlanes = [];\r\n      }\r\n    },\r\n    changeTab(e) {\r\n      this.currentTab = e;\r\n      this.resizeHeight();\r\n    },\r\n    transEventName(serviceCode) {\r\n      // console.log(\r\n      //   \"transEventName: \" + JSON.stringify(this.serviceCodeLocaleStringsList)\r\n      // );\r\n      let rtnString = \"\";\r\n      if (\r\n        this.serviceCodeLocaleStringsList &&\r\n        this.serviceCodeLocaleStringsList.length > 0\r\n      ) {\r\n        rtnString = this.serviceCodeLocaleStringsList.find(\r\n          item => item.key === serviceCode\r\n        ).value;\r\n      }\r\n      return rtnString;\r\n    },\r\n    getDeviceListLossSignalLowBatteryAbnormalDevice() {\r\n      let tempDeviceListLossSignal = [];\r\n      let tempDeviceListLossSignal2 = [];\r\n      let tempDeviceListLowBattery = [];\r\n      let tempDeviceListAbnormalDevice = [];\r\n      this.allDeviceTypeMenu.forEach(item => {\r\n        if (item.supportDataEvent) {\r\n          if (\r\n            item.supportDataEvent.some(i => i.serviceCode === \"LossSignal\") &&\r\n            item.isBleDevice\r\n          ) {\r\n            tempDeviceListLossSignal.push({\r\n              key: item.type,\r\n              value: this.$t(\"LocaleString.\" + item.langs.nameId)\r\n            });\r\n          }\r\n          if (\r\n            item.supportDataEvent.some(i => i.serviceCode === \"LossSignal\") &&\r\n            !item.isBleDevice\r\n          ) {\r\n            tempDeviceListLossSignal2.push({\r\n              key: item.type,\r\n              value: this.$t(\"LocaleString.\" + item.langs.nameId)\r\n            });\r\n          }\r\n          if (item.supportDataEvent.some(i => i.serviceCode === \"LowBattery\")) {\r\n            tempDeviceListLowBattery.push({\r\n              key: item.type,\r\n              value: this.$t(\"LocaleString.\" + item.langs.nameId)\r\n            });\r\n          }\r\n          if (\r\n            item.supportDataEvent.some(i => i.serviceCode === \"AbnormalDevice\")\r\n          ) {\r\n            tempDeviceListAbnormalDevice.push({\r\n              key: item.type,\r\n              value: this.$t(\"LocaleString.\" + item.langs.nameId)\r\n            });\r\n          }\r\n        }\r\n      });\r\n      this.deviceListLossSignal = Array.from(new Set(tempDeviceListLossSignal));\r\n      this.deviceListLossSignal2 = Array.from(\r\n        new Set(tempDeviceListLossSignal2)\r\n      );\r\n      this.deviceListLowBattery = Array.from(new Set(tempDeviceListLowBattery));\r\n      this.deviceListAbnormalDevice = Array.from(\r\n        new Set(tempDeviceListAbnormalDevice)\r\n      );\r\n    },\r\n    getEventServiceCodeList() {\r\n      let tmpEventServiceCodeList = [];\r\n      this.allDeviceTypeMenu.forEach(item => {\r\n        if (item.supportDataEvent) {\r\n          let scList = item.supportDataEvent.map(e => e.serviceCode);\r\n          tmpEventServiceCodeList = [...tmpEventServiceCodeList, ...scList];\r\n        }\r\n      });\r\n\r\n      tmpEventServiceCodeList = Array.from(new Set(tmpEventServiceCodeList));\r\n      tmpEventServiceCodeList.forEach(item => {\r\n        this.eventServiceCodeList.push({\r\n          key: item,\r\n          value: this.transEventName(item)\r\n        });\r\n      });\r\n    },\r\n    async getDeviceTypeMenu() {\r\n      let deviceTypesParams = new URLSearchParams();\r\n      deviceTypesParams.append(\"active\", true);\r\n      this.allDeviceTypeMenu = await this.$service.getDeviceTypesmenu.send(\r\n        deviceTypesParams\r\n      );\r\n      this.getDeviceSelectList();\r\n      this.getEventServiceCodeList();\r\n      this.getDeviceListLossSignalLowBatteryAbnormalDevice();\r\n      this.loadLogo();\r\n      this.loadSound();\r\n      this.loadConfig();\r\n      this.loadThirdParty();\r\n    },\r\n    getDeviceSelectList() {\r\n      this.deviceDefaultSelection = this.allDeviceTypeMenu.map(item => ({\r\n        key: item.type,\r\n        value: this.$t(\"LocaleString.\" + item.langs.nameId)\r\n      }));\r\n      this.deviceDefaultSelection = [\r\n        ...[{ key: \"all\", value: this.$t(\"LocaleString.D00002\") }],\r\n        ...this.deviceDefaultSelection\r\n      ];\r\n    },\r\n    ended() {\r\n      let audio = this.$refs.audio;\r\n      this.isPlay = false;\r\n      audio.pause();\r\n      audio.currentTime = 0;\r\n      document.querySelector(\".toggle-sound\").classList.add(\"paused\");\r\n    },\r\n    play(control) {\r\n      let audio = this.$refs.audio;\r\n      if (control != null || control != undefined) {\r\n        this.isPlay = false;\r\n        audio.pause();\r\n        audio.currentTime = 0;\r\n        document.querySelector(\".toggle-sound\").classList.add(\"paused\");\r\n        return;\r\n      }\r\n\r\n      if (\r\n        audio.paused &&\r\n        document.querySelector(\".toggle-sound\").classList.contains(\"paused\")\r\n      ) {\r\n        this.isPlay = true;\r\n        document.querySelector(\".toggle-sound\").classList.remove(\"paused\");\r\n        audio.play();\r\n      } else {\r\n        this.isPlay = false;\r\n        audio.pause();\r\n        audio.currentTime = 0;\r\n        document.querySelector(\".toggle-sound\").classList.add(\"paused\");\r\n      }\r\n    },\r\n    restImage() {\r\n      const preview = document.querySelector(\"#preview\");\r\n      preview.src = \"\";\r\n      this.showCleanIcon = false;\r\n      this.imgFileURL = null;\r\n    },\r\n\r\n    handleUploadImage(imgFile) {\r\n      let imgFileurl = null;\r\n      if (\r\n        imgFile.type.toLowerCase().includes(\"png\") ||\r\n        imgFile.type.toLowerCase().includes(\"jpg\") ||\r\n        imgFile.type.toLowerCase().includes(\"jpeg\")\r\n      ) {\r\n        const reader = new FileReader();\r\n        const preview = document.querySelector(\"#preview\");\r\n\r\n        reader.addEventListener(\r\n          \"load\",\r\n          function() {\r\n            preview.src = reader.result;\r\n          },\r\n          false\r\n        );\r\n\r\n        if (imgFile) {\r\n          reader.readAsDataURL(imgFile);\r\n        }\r\n        //\r\n        // if (window.createObjectURL != undefined) {\r\n        //   imgFileurl = window.createObjectURL(imgFile);\r\n        // } else if (window.URL != undefined) {\r\n        //   imgFileurl = window.URL.createObjectURL(imgFile);\r\n        // } else if (window.webkitURL != undefined) {\r\n        //   imgFileurl = window.webitURL.createObjectURL(imgFile);\r\n        // }\r\n        this.imgFile = imgFile;\r\n        this.imgFileURL = imgFileurl;\r\n        this.showCleanIcon = true;\r\n      } else {\r\n        this.$Notice.error({\r\n          title: this.$t(\"LocaleString.E00037\"),\r\n          desc: this.$t(\"LocaleString.E00040\", {\r\n            0: this.$t(\"LocaleString.L30134\")\r\n          }),\r\n          duration: Config.errorDuration\r\n        });\r\n      }\r\n    },\r\n    handleUpload(file) {\r\n      this.isPlay = false;\r\n      let audio = this.$refs.audio;\r\n      audio.pause();\r\n      document.querySelector(\".toggle-sound\").classList.add(\"paused\");\r\n\r\n      let audioElement = new FileReader();\r\n      audioElement.readAsDataURL(file);\r\n      audioElement.addEventListener(\"load\", () => {\r\n        if (!audioElement.result.includes(\"data:audio/\")) {\r\n          this.file = null;\r\n          this.fileURL = this.fileOriURL;\r\n          this.$Notice.error({\r\n            title: this.$t(\"LocaleString.E00037\"),\r\n            desc: this.$t(\"LocaleString.E00040\", {\r\n              0: this.$t(\"LocaleString.L30055\")\r\n            }),\r\n            duration: Config.errorDuration\r\n          });\r\n        } else {\r\n          this.file = file;\r\n          this.fileURL = audioElement.result;\r\n\r\n          setTimeout(() => {\r\n            if (audio.duration > 15) {\r\n              this.file = null;\r\n              this.fileURL = this.fileOriURL;\r\n              this.$Notice.error({\r\n                title: this.$t(\"LocaleString.E00037\"),\r\n                desc: this.$t(\"LocaleString.W30008\", { 0: \"15\" }),\r\n                duration: Config.errorDuration\r\n              });\r\n            } else {\r\n              this.file = file;\r\n              this.fileURL = audioElement.result;\r\n            }\r\n          }, 100);\r\n        }\r\n      });\r\n    },\r\n    changeDefaultTabList(item) {\r\n      // console.log('Sitem:'+JSON.stringify(item))\r\n      let arr = [];\r\n      item.forEach(element => {\r\n        let v = this.showingTab.find(t => t.value == element).label;\r\n        if (v) {\r\n          arr.push({\r\n            label: v,\r\n            value: element\r\n          });\r\n        }\r\n      });\r\n      this.defaultTabList = arr;\r\n    },\r\n    loadLogo() {\r\n      let pocGroupParams = {\r\n        inlinecount: true,\r\n        search: \"active eq true and category eq @SNS@Logo\"\r\n      };\r\n      this.$service.getPOCGroupsByCategory.send(pocGroupParams).then(pocRes => {\r\n        if (\r\n          pocRes.count > 0 &&\r\n          pocRes.results[0].files &&\r\n          pocRes.results[0].files.length > 0\r\n        ) {\r\n          this.imgFileURL = pocRes.results[0].files[0].url;\r\n          this.imgFileOriURL = pocRes.results[0].files[0].url;\r\n          this.showCleanIcon = true;\r\n        }\r\n      });\r\n    },\r\n    loadSound() {\r\n      let pocGroupParams = {\r\n        inlinecount: true,\r\n        search: \"active eq true and category eq @SNS@Sound\"\r\n      };\r\n      this.$service.getPOCGroupsByCategory.send(pocGroupParams).then(pocRes => {\r\n        if (\r\n          pocRes.count > 0 &&\r\n          pocRes.results[0].files &&\r\n          pocRes.results[0].files.length > 0\r\n        ) {\r\n          this.fileURL = pocRes.results[0].files[0].url;\r\n          this.fileOriURL = pocRes.results[0].files[0].url;\r\n        }\r\n      });\r\n    },\r\n    loadConfig() {\r\n      //let loginUserName = localStorage.getItem(\"sns_loginUser\");\r\n\r\n      let params = {\r\n        inlinecount: true,\r\n        search: \"category eq @SNS@Setting\"\r\n      };\r\n\r\n      let planeStr = \"active eq true and enable eq true\";\r\n      let planeParams = {\r\n        search: planeStr,\r\n        inlinecount: true,\r\n        // page: 0,\r\n        // size: 1,\r\n        sort: \"modifiesAt,desc\"\r\n        // active: true,\r\n      };\r\n\r\n      this.$service.getPlanes.send(planeParams).then(planeData => {\r\n        if (planeData.results.length > 0) {\r\n          this.planeList = planeData.results.map(item => ({\r\n            value: item.code,\r\n            label: item.name\r\n          }));\r\n        }\r\n        this.$service.getPOCProperties.send(params).then(res => {\r\n          if (res.count > 0 && res.results[0].properties.length > 0) {\r\n            res.results[0].properties.forEach(res => {\r\n              switch (res.key) {\r\n                case \"license\":\r\n                  this.configData.license =\r\n                    res.value != \"\" ? res.value.split(\",\") : [];\r\n                  break;\r\n                case \"systemName\":\r\n                  this.configData.systemName = res.value;\r\n                  break;\r\n                case \"tabList\":\r\n                  this.configData.tabList = res.value.split(\",\");\r\n                  let arr = [];\r\n                  this.configData.tabList.forEach(element => {\r\n                    let v = this.showingTab.find(t => t.value == element).label;\r\n                    if (v) {\r\n                      arr.push({\r\n                        label: v,\r\n                        value: element\r\n                      });\r\n                    }\r\n                  });\r\n                  this.defaultTabList = arr;\r\n                  break;\r\n                // case \"sound\":\r\n                //   break;\r\n                case \"m2List\":\r\n                  this.configData.m2List = res.value.split(\",\");\r\n                  break;\r\n                case \"iconSize\":\r\n                  this.configData.iconSize = res.value;\r\n                  break;\r\n                case \"defaultTab\":\r\n                  this.configData.defaultTab = res.value;\r\n                  this.oriTab = res.value;\r\n                  break;\r\n                case \"updateSeconds\":\r\n                  this.configData.updateSeconds = res.value;\r\n                  break;\r\n                case \"grayTime\":\r\n                  this.configData.grayTime = res.value * 1;\r\n                  break;\r\n                case \"EventAuthClose\":\r\n                  this.configData.eventAuthClose =\r\n                    res.value == \"true\" ? true : false;\r\n                  break;\r\n                case \"EventKeepDays\":\r\n                  this.configData.eventKeepDays = res.value * 1;\r\n                  break;\r\n                case \"InfluxRange\":\r\n                  this.configData.influxRange = res.value * 1;\r\n                  break;\r\n                case \"lossSignal\":\r\n                  this.configData.lossSignal = res.value * 1;\r\n                  break;\r\n                case \"lossSignal2\":\r\n                  this.configData.lossSignal2 = res.value * 1;\r\n                  break;\r\n                case \"lowBattery\":\r\n                  this.configData.lowBattery = res.value * 1;\r\n                  break;\r\n                case \"lossSignalDevices\":\r\n                  this.configData.lossSignalDevices = res.value.split(\",\");\r\n                  break;\r\n                case \"lossSignalDevices2\":\r\n                  this.configData.lossSignalDevices2 = res.value.split(\",\");\r\n                  break;\r\n                case \"lowBatteryDevices\":\r\n                  this.configData.lowBatteryDevices = res.value.split(\",\");\r\n                  break;\r\n                case \"abnormalDevices\":\r\n                  this.configData.abnormalDevices = res.value.split(\",\");\r\n                  break;\r\n                case \"skipEvent\":\r\n                  this.configData.skipEvent = res.value.split(\",\");\r\n                  break;\r\n                case \"deviceSelectList\":\r\n                  this.configData.deviceSelectList = res.value.split(\",\");\r\n                  break;\r\n                case \"multiPlanes\":\r\n                  this.configData.multiPlanes =\r\n                    res.value == \"true\" ? true : false;\r\n                  break;\r\n                case \"selectedPlanes\":\r\n                  this.configData.selectedPlanes = res.value.split(\",\");\r\n                  break;\r\n                case \"stuckPlane\":\r\n                  this.configData.stuckPlane =\r\n                    res.value == \"true\" ? true : false;\r\n                  break;\r\n                case \"m1LongPress\":\r\n                  this.configData.m1LongPress = res.value.split(\",\");\r\n                  break;\r\n                case \"defaultPageSize\":\r\n                  this.configData.defaultPageSize = res.value;\r\n                  break;\r\n                case \"m5ObjectType\":\r\n                  this.configData.m5ObjectType =\r\n                    res.value == \"\" ? \"all\" : res.value;\r\n                  break;\r\n                case \"showLeaveBed\":\r\n                  this.configData.showLeaveBed =\r\n                    res.value === \"true\" ? true : false;\r\n                  break;\r\n                case \"m2Statistic\":\r\n                  this.configData.m2Statistic = res.value * 1;\r\n                  break;\r\n                case \"m3Statistic\":\r\n                  this.configData.m3Statistic = res.value * 1;\r\n                  break;\r\n                case \"m4Statistic\":\r\n                  this.configData.m4Statistic = res.value * 1;\r\n                  break;\r\n                case \"m8Statistic\":\r\n                  this.configData.m8Statistic = res.value * 1;\r\n                  break;\r\n                case \"m9Statistic\":\r\n                  this.configData.m9Statistic = res.value * 1;\r\n                  break;\r\n                case \"stateColor\":\r\n                  this.configData.stateColor = res.value;\r\n                  break;\r\n                case \"repeatSound\":\r\n                  this.configData.repeatSound = res.value * 1;\r\n                  break;\r\n                case \"logoutTime\":\r\n                  this.configData.logoutTime = res.value * 1;\r\n                  break;\r\n              }\r\n            });\r\n            this.configData.sound =\r\n              localStorage.getItem(\"sns_alertSound\") == \"true\" ? true : false;\r\n            this.configData.m2Line =\r\n              localStorage.getItem(\"sns_m2Line\") == \"true\" ? true : false;\r\n            //\r\n\r\n            this.origConfigData = JSON.parse(JSON.stringify(this.configData));\r\n            this.updateValue(this.origConfigData);\r\n          } else {\r\n            tgus;\r\n            this.editHandleSubmitNew(\"new\");\r\n          }\r\n        });\r\n      });\r\n    },\r\n    loadThirdParty() {\r\n      //let loginUserName = localStorage.getItem(\"sns_loginUser\");\r\n\r\n      let params = {\r\n        inlinecount: true,\r\n        search: \"category eq @SNS@SettingThirdParty\"\r\n      };\r\n\r\n      this.$service.getPOCProperties.send(params).then(res => {\r\n        if (res.count > 0 && res.results[0].properties.length > 0) {\r\n          res.results[0].properties.forEach(res => {\r\n            switch (res.key) {\r\n              case \"loginThirdParty\":\r\n                this.thirdPartyData.loginThirdParty =\r\n                  res.value == \"true\" ? true : false;\r\n                break;\r\n              case \"url\":\r\n                this.thirdPartyData.url = res.value;\r\n                break;\r\n              case \"userName\":\r\n                this.thirdPartyData.userName = res.value;\r\n                break;\r\n              case \"secret\":\r\n                this.thirdPartyData.secret = res.value;\r\n                break;\r\n            }\r\n          });\r\n          this.origThirdPartyData = JSON.parse(\r\n            JSON.stringify(this.thirdPartyData)\r\n          );\r\n          this.updateThirdPartyValue(this.origThirdPartyData);\r\n        }\r\n      });\r\n    },\r\n    checkEditHandleSubmitNew() {\r\n      //\r\n      let errorDesc = \"\";\r\n      let tabList = this.configData.tabList;\r\n      let m2List = this.configData.m2List;\r\n      let defaultTab = this.configData.defaultTab;\r\n      let grayTime = this.configData.grayTime;\r\n      let eventAuthClose = this.configData.eventAuthClose;\r\n      let eventKeepDays = this.configData.eventKeepDays;\r\n      let lowBattery = this.configData.lowBattery;\r\n      let lossSignal = this.configData.lossSignal;\r\n      let lossSignal2 = this.configData.lossSignal2;\r\n      let lowBatteryDevices = this.configData.lowBatteryDevices;\r\n      let lossSignalDevices = this.configData.lossSignalDevices;\r\n      let lossSignalDevices2 = this.configData.lossSignalDevices2;\r\n      let abnormalDevices = this.configData.abnormalDevices;\r\n      let skipEvent = this.configData.skipEvent;\r\n      let deviceSelectList = this.configData.deviceSelectList;\r\n      let multiPlanes = this.configData.multiPlanes;\r\n      let selectedPlanes = this.configData.selectedPlanes;\r\n      let stuckPlane = this.configData.stuckPlane;\r\n      let m1LognPress = this.configData.m1LongPress;\r\n      let showLeaveBed = this.configData.showLeaveBed;\r\n      let m2Statistic = this.configData.m2Statistic;\r\n      let m3Statistic = this.configData.m3Statistic;\r\n      let m4Statistic = this.configData.m4Statistic;\r\n      let m8Statistic = this.configData.m8Statistic;\r\n      let m9Statistic = this.configData.m9Statistic;\r\n      let stateColor = this.configData.stateColor;\r\n      let logoutTime = this.configData.logoutTime;\r\n      // let iconSize = this.configData.iconSize\r\n      if (tabList.length < 1) {\r\n        errorDesc +=\r\n          this.$t(\"LocaleString.M00010\", {\r\n            0: this.$t(\"LocaleString.L30057\")\r\n          }) + \"<br>\";\r\n      }\r\n      if (m2List.length < 1) {\r\n        errorDesc +=\r\n          this.$t(\"LocaleString.M00010\", {\r\n            0: this.$t(\"LocaleString.L30056\")\r\n          }) + \"<br>\";\r\n      }\r\n      if (m2List.length > 4) {\r\n        errorDesc += this.$t(\"LocaleString.W30002\") + \"<br>\";\r\n      }\r\n      if (!defaultTab) {\r\n        errorDesc +=\r\n          this.$t(\"LocaleString.M00010\", {\r\n            0: this.$t(\"LocaleString.L30060\")\r\n          }) + \"<br>\";\r\n      }\r\n      if (grayTime == null) {\r\n        errorDesc +=\r\n          this.$t(\"LocaleString.L00001\", {\r\n            0: this.$t(\"LocaleString.L30062\")\r\n          }) + \"<br>\";\r\n      }\r\n      if (eventAuthClose == true) {\r\n        if (eventKeepDays == null) {\r\n          errorDesc +=\r\n            this.$t(\"LocaleString.L00001\", {\r\n              0: this.$t(\"LocaleString.L30083\")\r\n            }) + \"<br>\";\r\n        }\r\n      }\r\n      if (lowBattery == null) {\r\n        errorDesc +=\r\n          this.$t(\"LocaleString.L00001\", {\r\n            0: this.$t(\"LocaleString.L30104\")\r\n          }) + \"<br>\";\r\n      } else {\r\n        if (lowBattery < 10) {\r\n          errorDesc +=\r\n            this.$t(\"LocaleString.L30104\") +\r\n            this.$t(\"LocaleString.W30009\", {\r\n              0: 10,\r\n              1: 60\r\n            }) +\r\n            \"<br>\";\r\n        }\r\n      }\r\n      if (lossSignal == null) {\r\n        errorDesc +=\r\n          this.$t(\"LocaleString.L00001\", {\r\n            0: this.$t(\"LocaleString.L30103\")\r\n          }) + \"<br>\";\r\n      } else {\r\n        if (lossSignal < 1) {\r\n          errorDesc +=\r\n            this.$t(\"LocaleString.L30103\") +\r\n            this.$t(\"LocaleString.W30009\", {\r\n              0: 1,\r\n              1: 60\r\n            }) +\r\n            \"<br>\";\r\n        }\r\n      }\r\n      if (deviceSelectList.length < 1) {\r\n        errorDesc +=\r\n          this.$t(\"LocaleString.M00010\", {\r\n            0: this.$t(\"LocaleString.L30129\")\r\n          }) + \"<br>\";\r\n      }\r\n      if (\r\n        multiPlanes &&\r\n        (selectedPlanes.length < 1 ||\r\n          (selectedPlanes.length == 1 && selectedPlanes[0] == \"\"))\r\n      ) {\r\n        errorDesc +=\r\n          this.$t(\"LocaleString.M00010\", {\r\n            0: this.$t(\"LocaleString.L30138\")\r\n          }) + \"<br>\";\r\n      }\r\n\r\n      if (m2Statistic == null) {\r\n        errorDesc +=\r\n          this.$t(\"LocaleString.L00001\", {\r\n            0: this.$t(\"LocaleString.L30182\")\r\n          }) + \"<br>\";\r\n      }\r\n      if (m3Statistic == null) {\r\n        errorDesc +=\r\n          this.$t(\"LocaleString.L00001\", {\r\n            0: this.$t(\"LocaleString.L30183\")\r\n          }) + \"<br>\";\r\n      }\r\n      if (m4Statistic == null) {\r\n        errorDesc +=\r\n          this.$t(\"LocaleString.L00001\", {\r\n            0: this.$t(\"LocaleString.L30184\")\r\n          }) + \"<br>\";\r\n      }\r\n      if (m8Statistic == null) {\r\n        errorDesc +=\r\n          this.$t(\"LocaleString.L00001\", {\r\n            0: this.$t(\"LocaleString.L30185\")\r\n          }) + \"<br>\";\r\n      }\r\n      if (m9Statistic == null) {\r\n        errorDesc +=\r\n          this.$t(\"LocaleString.L00001\", {\r\n            0: this.$t(\"LocaleString.L30186\")\r\n          }) + \"<br>\";\r\n      }\r\n      if (stateColor == \"\") {\r\n        errorDesc += this.$t(\"LocaleString.W30019\") + \"<br>\";\r\n      }\r\n      if (logoutTime == null) {\r\n        errorDesc +=\r\n          this.$t(\"LocaleString.L00001\", {\r\n            0: this.$t(\"LocaleString.L00446\")\r\n          }) + \"<br>\";\r\n      }\r\n\r\n      this.cameraConfigList.forEach(item => {\r\n        if (\r\n          item.capture &&\r\n          (item.prefixMinute == \"\" ||\r\n            item.suffixMinute == \"\" ||\r\n            item.backupDirectory == \"\" ||\r\n            item.account == \"\" ||\r\n            item.password == \"\" ||\r\n            item.keepDays == \"\" ||\r\n            item.positionCapture == \"\")\r\n        ) {\r\n          let name = this.cameraTypeList.find(item2 => item.type == item2.type)\r\n            .name;\r\n          errorDesc +=\r\n            this.$t(\"LocaleString.L00001\", {\r\n              0: this.$t(\"LocaleString.L30139\") + \" - \" + name\r\n            }) + \"<br>\";\r\n        }\r\n      });\r\n      // if (iconSize == \"\") {\r\n      //     errorDesc += '請輸入平面圖標大小<br>'\r\n      // }else {\r\n      //   let re = /^[0-9]{1,}\\*[0-9]{1,}$/\r\n      //   if (!re.test(iconSize)) {\r\n      //     errorDesc += '平面圖標大小格式錯誤<br>'\r\n      //   }\r\n      // }\r\n      //\r\n      if (errorDesc) {\r\n        this.$Notice.error({\r\n          title: this.$t(\"LocaleString.E00037\"),\r\n          desc: errorDesc,\r\n          duration: Config.errorDuration\r\n        });\r\n        return false;\r\n      } else {\r\n        return true;\r\n      }\r\n    },\r\n    editHandleSubmitNew(type) {\r\n      let requestId = this.createRequestID();\r\n      if (type === \"update\") {\r\n        this.submitLoading = true;\r\n      }\r\n      //let loginUserName = localStorage.getItem(\"sns_loginUser\");\r\n      let newLowBatteryDevices = [];\r\n      let newLossignalDevices = [];\r\n      let newLossignalDevices2 = [];\r\n      let newAbnormalDevices = [];\r\n      let submitEventArr = [];\r\n      let putPropertyDatas = [];\r\n      let valid = this.checkEditHandleSubmitNew();\r\n      if (valid) {\r\n        if (type === \"new\") {\r\n          this.configData.lossSignalDevices = this.deviceListLossSignal.map(\r\n            item => item.key\r\n          );\r\n          newLossignalDevices = this.configData.lossSignalDevices;\r\n\r\n          this.configData.lossSignalDevices2 = this.deviceListLossSignal2.map(\r\n            item => item.key\r\n          );\r\n          newLossignalDevices2 = this.configData.lossSignalDevices2;\r\n\r\n          this.configData.lowBatteryDevices = this.deviceListLowBattery.map(\r\n            item => item.key\r\n          );\r\n          newLowBatteryDevices = this.configData.lowBatteryDevices;\r\n\r\n          this.configData.abnormalDevices = this.deviceListAbnormalDevice.map(\r\n            item => item.key\r\n          );\r\n          newAbnormalDevices = this.configData.abnormalDevices;\r\n        }\r\n\r\n        let putData = {\r\n          category: \"@SNS@Setting\",\r\n          properties: [\r\n            {\r\n              key: \"license\",\r\n              value: this.configData.license.join(\",\")\r\n            },\r\n            {\r\n              key: \"systemName\",\r\n              value: this.configData.systemName\r\n            },\r\n            {\r\n              key: \"tabList\",\r\n              value: this.configData.tabList.join(\",\")\r\n            },\r\n            // {\r\n            //   key: \"sound\",\r\n            //   value: this.configData.sound == true ? \"true\" : \"false\",\r\n            // },\r\n            {\r\n              key: \"m2List\",\r\n              value: this.configData.m2List.join(\",\")\r\n            },\r\n            {\r\n              key: \"iconSize\",\r\n              value: this.configData.iconSize\r\n            },\r\n            {\r\n              key: \"defaultTab\",\r\n              value: this.configData.defaultTab\r\n            },\r\n            {\r\n              key: \"updateSeconds\",\r\n              value: this.configData.updateSeconds\r\n            },\r\n            {\r\n              key: \"grayTime\",\r\n              value: this.configData.grayTime\r\n            },\r\n            {\r\n              key: \"EventAuthClose\",\r\n              value: this.configData.eventAuthClose\r\n            },\r\n            {\r\n              key: \"EventKeepDays\",\r\n              value: this.configData.eventKeepDays\r\n            },\r\n            {\r\n              key: \"InfluxRange\",\r\n              value: this.configData.influxRange\r\n            },\r\n            {\r\n              key: \"lossSignal\",\r\n              value: this.configData.lossSignal\r\n            },\r\n            {\r\n              key: \"lossSignal2\",\r\n              value: this.configData.lossSignal2\r\n            },\r\n            {\r\n              key: \"lowBattery\",\r\n              value: this.configData.lowBattery\r\n            },\r\n            {\r\n              key: \"lossSignalDevices\",\r\n              value: this.configData.lossSignalDevices.join(\",\")\r\n            },\r\n            {\r\n              key: \"lossSignalDevices2\",\r\n              value: this.configData.lossSignalDevices2.join(\",\")\r\n            },\r\n            {\r\n              key: \"lowBatteryDevices\",\r\n              value: this.configData.lowBatteryDevices.join(\",\")\r\n            },\r\n            {\r\n              key: \"abnormalDevices\",\r\n              value: this.configData.abnormalDevices.join(\",\")\r\n            },\r\n            {\r\n              key: \"skipEvent\",\r\n              value: this.configData.skipEvent.join(\",\")\r\n            },\r\n            {\r\n              key: \"deviceSelectList\",\r\n              value: this.configData.deviceSelectList.join(\",\")\r\n            },\r\n            {\r\n              key: \"selectedPlanes\",\r\n              value: this.configData.selectedPlanes.join(\",\")\r\n            },\r\n            {\r\n              key: \"multiPlanes\",\r\n              value: this.configData.multiPlanes\r\n            },\r\n            {\r\n              key: \"stuckPlane\",\r\n              value: this.configData.stuckPlane\r\n            },\r\n            {\r\n              key: \"m1LongPress\",\r\n              value: this.configData.m1LongPress.join(\",\")\r\n            },\r\n            {\r\n              key: \"defaultPageSize\",\r\n              value: this.configData.defaultPageSize\r\n            },\r\n            {\r\n              key: \"m5ObjectType\",\r\n              value: this.configData.m5ObjectType\r\n            },\r\n            {\r\n              key: \"showLeaveBed\",\r\n              value: this.configData.showLeaveBed\r\n            },\r\n            {\r\n              key: \"m2Statistic\",\r\n              value: this.configData.m2Statistic\r\n            },\r\n            {\r\n              key: \"m3Statistic\",\r\n              value: this.configData.m3Statistic\r\n            },\r\n            {\r\n              key: \"m4Statistic\",\r\n              value: this.configData.m4Statistic\r\n            },\r\n            {\r\n              key: \"m8Statistic\",\r\n              value: this.configData.m8Statistic\r\n            },\r\n            {\r\n              key: \"m9Statistic\",\r\n              value: this.configData.m9Statistic\r\n            },\r\n            {\r\n              key: \"stateColor\",\r\n              value: this.configData.stateColor\r\n            },\r\n            {\r\n              key: \"repeatSound\",\r\n              value: this.configData.repeatSound\r\n            },\r\n            {\r\n              key: \"logoutTime\",\r\n              value: this.configData.logoutTime\r\n            }\r\n          ]\r\n        };\r\n        if (this.configData.systemName == \"\") {\r\n          localStorage.removeItem(\"sns_systemTitle\");\r\n          document.title = this.$t(\"LocaleString.S00002\");\r\n        } else {\r\n          localStorage.setItem(\"sns_systemTitle\", this.configData.systemName);\r\n          document.title = this.configData.systemName;\r\n        }\r\n        localStorage.setItem(\"sns_defaultTab\", this.configData.defaultTab);\r\n        localStorage.setItem(\r\n          \"sns_alertSound\",\r\n          this.configData.sound == true ? \"true\" : \"false\"\r\n        );\r\n        localStorage.setItem(\r\n          \"sns_m2Line\",\r\n          this.configData.m2Line == true ? \"true\" : \"false\"\r\n        );\r\n        localStorage.setItem(\r\n          \"sns_stuckPlane\",\r\n          this.configData.stuckPlane == true ? \"true\" : \"false\"\r\n        );\r\n        localStorage.setItem(\"sns_pageSize\", this.configData.defaultPageSize);\r\n        localStorage.setItem(\"sns_logoutTime\", this.configData.logoutTime);\r\n        putPropertyDatas.push(putData);\r\n\r\n        let editPOCGroups = [\r\n          {\r\n            code: \"@SNS@Sound\",\r\n            name: \"@SNS@Sound\",\r\n            category: \"@SNS@Sound\"\r\n          },\r\n          {\r\n            code: \"@SNS@Logo\",\r\n            name: \"@SNS@Logo\",\r\n            category: \"@SNS@Logo\"\r\n          }\r\n        ];\r\n        let paramsEvent = {\r\n          inlinecount: true,\r\n          search:\r\n            \"active eq true and code in @SNS@LowBattery,@SNS@LossSignal,@SNS@LossSignal2,@SNS@AbnormalDevice\"\r\n        };\r\n\r\n        let objectParams = {\r\n          search: \"active eq true and foreignKeys.key1 eq @SNS@\"\r\n        };\r\n\r\n        /////// construct km data ///////\r\n        let putKMData = {\r\n          category: \"@SNS@SettingKM\",\r\n          properties: []\r\n        };\r\n        this.kmConfigListEdit.forEach(item => {\r\n          let keyValuePairs = [\r\n            {\r\n              key: item.type + \"_longPress_cht\",\r\n              value: item.longPress_cht\r\n            },\r\n            {\r\n              key: item.type + \"_longPress_en\",\r\n              value: item.longPress_en\r\n            },\r\n            {\r\n              key: item.type + \"_shortClick_cht\",\r\n              value: item.shortClick_cht\r\n            },\r\n            {\r\n              key: item.type + \"_shortClick_en\",\r\n              value: item.shortClick_en\r\n            },\r\n            {\r\n              key: item.type + \"_doubleClick_cht\",\r\n              value: item.doubleClick_cht\r\n            },\r\n            {\r\n              key: item.type + \"_doubleClick_en\",\r\n              value: item.doubleClick_en\r\n            }\r\n          ];\r\n          putKMData.properties = [...putKMData.properties, ...keyValuePairs];\r\n        });\r\n        putPropertyDatas.push(putKMData);\r\n        /////// construct km data ///////\r\n\r\n        /////// construct camera data ///////\r\n        let putCameraData = {\r\n          category: \"@SNS@SettingCamera\",\r\n          properties: []\r\n        };\r\n        this.cameraConfigList.forEach(item => {\r\n          let keyValuePairs = [\r\n            {\r\n              key: item.type + \"_capture\",\r\n              value: item.capture\r\n            },\r\n            {\r\n              key: item.type + \"_prefixMinute\",\r\n              value: item.prefixMinute\r\n            },\r\n            {\r\n              key: item.type + \"_suffixMinute\",\r\n              value: item.suffixMinute\r\n            },\r\n            {\r\n              key: item.type + \"_backupDirectory\",\r\n              value: item.backupDirectory\r\n            },\r\n            {\r\n              key: item.type + \"_account\",\r\n              value: item.account\r\n            },\r\n            {\r\n              key: item.type + \"_password\",\r\n              value: item.password\r\n            },\r\n            {\r\n              key: item.type + \"_keepDays\",\r\n              value: item.keepDays\r\n            },\r\n            {\r\n              key: item.type + \"_positionCapture\",\r\n              value: item.positionCapture\r\n            }\r\n          ];\r\n          putCameraData.properties = [\r\n            ...putCameraData.properties,\r\n            ...keyValuePairs\r\n          ];\r\n        });\r\n        putPropertyDatas.push(putCameraData);\r\n        /////// construct camera data ///////\r\n        /////// construct statistic Line data ///////\r\n        let putStatisticLineData = {\r\n          category: \"@SNS@SettingStatisticLine\",\r\n          properties: []\r\n        };\r\n        this.statisticLineConfigList.forEach(item => {\r\n          let keyValuePairs = [\r\n            {\r\n              key: item.entry + \"_interval\",\r\n              value: item.interval\r\n            },\r\n            {\r\n              key: item.entry + \"_dataType\",\r\n              value: item.dataType\r\n            },\r\n            {\r\n              key: item.entry + \"_warningLine\",\r\n              value: item.warningLine\r\n            }\r\n          ];\r\n          putStatisticLineData.properties = [\r\n            ...putStatisticLineData.properties,\r\n            ...keyValuePairs\r\n          ];\r\n        });\r\n        putPropertyDatas.push(putStatisticLineData);\r\n        /////// construct statistic Line data ///////\r\n        /////// construct thirdParty data ///////\r\n\r\n        if (this.thirdPartyData.loginThirdParty) {\r\n          let errorMsg = [\r\n            this.$t(\"LocaleString.L30196\"),\r\n            \"UserName\",\r\n            \"CredentialSecret\"\r\n          ];\r\n          if (\r\n            this.thirdPartyData.url == \"\" ||\r\n            this.thirdPartyData.userName == \"\" ||\r\n            this.thirdPartyData.secret == \"\"\r\n          ) {\r\n            this.$Notice.error({\r\n              title: this.$t(\"LocaleString.E00037\"),\r\n              desc: this.$t(\"LocaleString.W00038\", { 0: errorMsg.join(\", \") }),\r\n              duration: Config.WARNING_DURATION\r\n            });\r\n            this.submitLoading = false;\r\n            return;\r\n          }\r\n        }\r\n        let putThirdPartyData = {\r\n          category: \"@SNS@SettingThirdParty\",\r\n          properties: [\r\n            {\r\n              key: \"loginThirdParty\",\r\n              value: this.thirdPartyData.loginThirdParty\r\n            },\r\n            {\r\n              key: \"url\",\r\n              value: this.thirdPartyData.url\r\n            },\r\n            {\r\n              key: \"userName\",\r\n              value: this.thirdPartyData.userName\r\n            },\r\n            {\r\n              key: \"secret\",\r\n              value: this.thirdPartyData.secret\r\n            }\r\n          ]\r\n        };\r\n        putPropertyDatas.push(putThirdPartyData);\r\n        /////// construct thirdParty data ///////\r\n        let createEventArr = [];\r\n        let objectDevicesLossSignal = [];\r\n        let objectDevicesLossSignal2 = [];\r\n        let objectDevicesLowBattery = [];\r\n        this.$service.getObjectsNoLoad.send(objectParams).then(objRes => {\r\n          this.$service.getEventsNoLoad.send(paramsEvent).then(eventRes => {\r\n            this.$service.editPOCGroups\r\n              .send(editPOCGroups, null, {\r\n                requestID: requestId,\r\n                requestFunction: \"systemParameter\",\r\n                requestAction: \"Update\"\r\n              })\r\n              .then(editPOCRes => {\r\n                this.$service.editPOCProperties\r\n                  .send(putPropertyDatas, null, {\r\n                    requestID: requestId,\r\n                    requestFunction: \"systemParameter\",\r\n                    requestAction: \"Update\"\r\n                  })\r\n                  .then(propertyRes => {\r\n                    console.log(propertyRes);\r\n                    if (type != \"new\") {\r\n                      this.$Notice.success({\r\n                        title: this.$t(\"LocaleString.M00004\"),\r\n                        desc: this.$t(\"LocaleString.M00002\"),\r\n                        duration: Config.SUCCESS_DURATION\r\n                      });\r\n                    }\r\n\r\n                    localStorage.setItem(\r\n                      \"sns_iconSize\",\r\n                      this.configData.iconSize\r\n                    );\r\n                    localStorage.setItem(\r\n                      \"sns_updateSeconds\",\r\n                      this.configData.updateSeconds\r\n                    );\r\n\r\n                    this.isEdit = false;\r\n                    this.origConfigData = JSON.parse(\r\n                      JSON.stringify(this.configData)\r\n                    );\r\n                    this.updateValue(this.origConfigData);\r\n\r\n                    this.oriThirdPartyData = JSON.parse(\r\n                      JSON.stringify(this.thirdPartyData)\r\n                    );\r\n                    this.updateThirdPartyValue(this.oriThirdPartyData);\r\n\r\n                    if (eventRes.results.length > 0) {\r\n                      eventRes.results.forEach(currentEvent => {\r\n                        let submitArgument = currentEvent.arguments;\r\n                        submitArgument.forEach(item => {\r\n                          if (item.key == \"threshold\") {\r\n                            item.value = currentEvent.code.includes(\r\n                              \"LowBattery\"\r\n                            )\r\n                              ? this.configData.lowBattery.toString()\r\n                              : currentEvent.code.includes(\"LossSignal2\")\r\n                              ? this.configData.lossSignal2.toString()\r\n                              : this.configData.lossSignal.toString();\r\n                          }\r\n                        });\r\n                        let updateEvent = {\r\n                          code: currentEvent.code,\r\n                          arguments: submitArgument,\r\n                          // sponsorObject: {\r\n                          //   objectCodes: total_objCodes.length\r\n                          //     ? total_objCodes\r\n                          //     : [],\r\n                          // },\r\n                          sponsorDevice: {\r\n                            devicePids: currentEvent.code.includes(\"LowBattery\")\r\n                              ? this.getEventPids(\"LowBattery\", objRes)\r\n                              : currentEvent.code.includes(\"LossSignal2\")\r\n                              ? this.getEventPids(\"LossSignal2\", objRes)\r\n                              : currentEvent.code.includes(\"AbnormalDevice\")\r\n                              ? this.getEventPids(\"AbnormalDevice\", objRes)\r\n                              : this.getEventPids(\"LossSignal\", objRes),\r\n                            deviceTypes: currentEvent.code.includes(\r\n                              \"LowBattery\"\r\n                            )\r\n                              ? this.configData.lowBatteryDevices\r\n                              : currentEvent.code.includes(\"LossSignal2\")\r\n                              ? this.configData.lossSignalDevices2\r\n                              : currentEvent.code.includes(\"AbnormalDevice\")\r\n                              ? this.configData.abnormalDevices\r\n                              : this.configData.lossSignalDevices\r\n                          }\r\n                        };\r\n                        submitEventArr.push(updateEvent);\r\n                      });\r\n                      if (\r\n                        !eventRes.results.some(\r\n                          item => item.code == \"@SNS@LossSignal\"\r\n                        )\r\n                      ) {\r\n                        // create lossSignal event\r\n                        let lossSignalEvent = this.lossSignalCreateTemplate(\r\n                          newLossignalDevices\r\n                        );\r\n                        createEventArr.push(lossSignalEvent);\r\n                      }\r\n                      if (\r\n                        !eventRes.results.some(\r\n                          item => item.code == \"@SNS@LossSignal2\"\r\n                        )\r\n                      ) {\r\n                        // create lossSignal2 event\r\n                        let lossSignalEvent2 = this.lossSignalCreateTemplate2(\r\n                          newLossignalDevices2\r\n                        );\r\n                        createEventArr.push(lossSignalEvent2);\r\n                      }\r\n                      if (\r\n                        !eventRes.results.some(\r\n                          item => item.code == \"@SNS@LowBattery\"\r\n                        )\r\n                      ) {\r\n                        // create lowBattery event\r\n                        let lowBetteryEvent = this.lowBatteryCreateTemplate(\r\n                          newLowBatteryDevices\r\n                        );\r\n                        createEventArr.push(lowBetteryEvent);\r\n                      }\r\n                      if (\r\n                        !eventRes.results.some(\r\n                          item => item.code == \"@SNS@AbnormalDevice\"\r\n                        )\r\n                      ) {\r\n                        // create abnormalDevice event\r\n                        let abnormalDeviceEvent = this.abnormalDeviceCreateTemplate(\r\n                          newAbnormalDevices\r\n                        );\r\n                        createEventArr.push(abnormalDeviceEvent);\r\n                      }\r\n                      //edit lowBattery event\r\n                      this.$service.postNewEvent\r\n                        .send(createEventArr, null, {\r\n                          requestID: requestId,\r\n                          requestFunction: \"systemParameter\",\r\n                          requestAction: \"Update\"\r\n                        })\r\n                        .then(postRes => {\r\n                          this.$service.editEvent\r\n                            .send(submitEventArr, null, {\r\n                              requestID: requestId,\r\n                              requestFunction: \"systemParameter\",\r\n                              requestAction: \"Update\"\r\n                            })\r\n                            .then(patchRes => {\r\n                              this.fileUpload(type);\r\n                            });\r\n                        });\r\n                    } else {\r\n                      // create lowBattery event\r\n                      let lowBetteryEvent = this.lowBatteryCreateTemplate(\r\n                        newLowBatteryDevices\r\n                      );\r\n                      // create lossSignal event\r\n                      let lossSignalEvent = this.lossSignalCreateTemplate(\r\n                        newLossignalDevices\r\n                      );\r\n                      // create lossSignal event\r\n                      let lossSignalEvent2 = this.lossSignalCreateTemplate2(\r\n                        newLossignalDevices2\r\n                      );\r\n                      // create lossSignal event\r\n                      let abnormalDeviceEvent = this.abnormalDeviceCreateTemplate(\r\n                        newAbnormalDevices\r\n                      );\r\n\r\n                      createEventArr.push(lowBetteryEvent);\r\n                      createEventArr.push(lossSignalEvent);\r\n                      createEventArr.push(lossSignalEvent2);\r\n                      createEventArr.push(abnormalDeviceEvent);\r\n                      this.$service.postNewEvent\r\n                        .send(createEventArr, null, {\r\n                          requestID: requestId,\r\n                          requestFunction: \"systemParameter\",\r\n                          requestAction: \"Update\"\r\n                        })\r\n                        .then(editRes => {\r\n                          this.fileUpload(type);\r\n                        });\r\n                    }\r\n                  });\r\n              });\r\n          });\r\n        });\r\n      } else {\r\n        if (type === \"update\") {\r\n          this.submitLoading = false;\r\n        }\r\n      }\r\n    },\r\n    lossSignalCreateTemplate(deviceTypes) {\r\n      let event = {\r\n        code: \"@SNS@LossSignal\",\r\n        name:\r\n          this.$t(\"LocaleString.D00033\") + \"_\" + this.$t(\"LocaleString.L30158\"),\r\n        serviceCode: \"LossSignal\",\r\n        arguments: [\r\n          {\r\n            key: \"threshold\",\r\n            value: this.configData.lossSignal.toString()\r\n          },\r\n          { key: \"autoTreated\", value: true }\r\n        ],\r\n        sponsorDevice: {\r\n          deviceTypes: deviceTypes\r\n        }\r\n      };\r\n      return event;\r\n    },\r\n    lossSignalCreateTemplate2(deviceTypes) {\r\n      let event = {\r\n        code: \"@SNS@LossSignal2\",\r\n        name:\r\n          this.$t(\"LocaleString.D00033\") + \"_\" + this.$t(\"LocaleString.L30157\"),\r\n        serviceCode: \"LossSignal\",\r\n        arguments: [\r\n          {\r\n            key: \"threshold\",\r\n            value: this.configData.lossSignal2.toString()\r\n          },\r\n          { key: \"autoTreated\", value: true }\r\n        ],\r\n        sponsorDevice: {\r\n          deviceTypes: deviceTypes\r\n        }\r\n      };\r\n      return event;\r\n    },\r\n    lowBatteryCreateTemplate(deviceTypes) {\r\n      let event = {\r\n        code: \"@SNS@LowBattery\",\r\n        name: this.$t(\"LocaleString.D00034\"),\r\n        serviceCode: \"LowBattery\",\r\n        arguments: [\r\n          {\r\n            key: \"threshold\",\r\n            value: this.configData.lowBattery.toString()\r\n          },\r\n          { key: \"autoTreated\", value: true }\r\n        ],\r\n        sponsorDevice: {\r\n          deviceTypes: deviceTypes\r\n        }\r\n      };\r\n      return event;\r\n    },\r\n    abnormalDeviceCreateTemplate(deviceTypes) {\r\n      let event = {\r\n        code: \"@SNS@AbnormalDevice\",\r\n        name: this.$t(\"LocaleString.D00026\"),\r\n        serviceCode: \"AbnormalDevice\",\r\n        arguments: [{ key: \"autoTreated\", value: true }],\r\n        sponsorDevice: {\r\n          deviceTypes: deviceTypes\r\n        }\r\n      };\r\n      return event;\r\n    },\r\n    getEventPids(type, objectResponse) {\r\n      let pids = [];\r\n      if (objectResponse.results.length > 0) {\r\n        objectResponse.results.forEach(obj => {\r\n          if (obj.devices) {\r\n            obj.devices.forEach(d => {\r\n              if (\r\n                type === \"LossSignal\" &&\r\n                this.configData.lossSignalDevices.includes(d.type)\r\n              ) {\r\n                pids.push(d.pid);\r\n              }\r\n              if (\r\n                type === \"LossSignal2\" &&\r\n                this.configData.lossSignalDevices2.includes(d.type)\r\n              ) {\r\n                pids.push(d.pid);\r\n              }\r\n              if (\r\n                type === \"LowBattery\" &&\r\n                this.configData.lowBatteryDevices.includes(d.type)\r\n              ) {\r\n                pids.push(d.pid);\r\n              }\r\n              if (\r\n                type === \"AbnormalDevice\" &&\r\n                this.configData.abnormalDevices.includes(d.type)\r\n              ) {\r\n                pids.push(d.pid);\r\n              }\r\n            });\r\n          }\r\n        });\r\n      }\r\n      return pids;\r\n    },\r\n    async fileUpload(type) {\r\n      if (this.imgFile != null) {\r\n        let imgParams = [\"@SNS@Logo\"];\r\n        let imgData = {\r\n          image: this.imgFile\r\n        };\r\n        let res2 = await this.$service.editPOCGroupsImage.fileUpload(\r\n          imgData,\r\n          imgParams\r\n        );\r\n      } else {\r\n        if (!this.showCleanIcon && !this.imgFileURL) {\r\n          let params = \"@SNS@Logo\";\r\n          let res = await this.$service.deletePOCGroupsImage.send(params);\r\n          localStorage.removeItem(\"sns_logoURL\");\r\n        }\r\n      }\r\n      if (this.file != null) {\r\n        let data = {\r\n          image: this.file\r\n        };\r\n        let params = [\"@SNS@Sound\"];\r\n        let res = await this.$service.editPOCGroupsMusic.fileUpload(\r\n          data,\r\n          params\r\n        );\r\n      }\r\n      this.goDefaultPage(type);\r\n    },\r\n    goDefaultPage(type) {\r\n      if (this.oriTab != this.configData.defaultTab) {\r\n        //重導向\r\n        this.$router.push({\r\n          path: decodeURIComponent(\r\n            \"/administrative/apps/sns/\" + this.configData.defaultTab\r\n          ) // 導頁至預設頁面\r\n        });\r\n      }\r\n      setTimeout(() => {\r\n        this.$store.commit(\"setPOCConfigChanged\", moment().valueOf());\r\n        if (type != \"new\") {\r\n          this.submitLoading = false;\r\n          this.$emit(\"closeSystemConfig\");\r\n        }\r\n      }, 1100);\r\n    },\r\n    updateThirdPartyValue(data) {\r\n      this.thirdPartyConfigList.forEach(d => {\r\n        switch (d.section) {\r\n          case 1:\r\n            d.value = data.loginThirdParty;\r\n            this.viewLoginThirdParty =\r\n              data.loginThirdParty == true\r\n                ? this.$t(\"LocaleString.B20009\")\r\n                : this.$t(\"LocaleString.B20010\");\r\n            break;\r\n          case 2:\r\n            d.value = data.url;\r\n            break;\r\n          case 3:\r\n            d.value = data.userName;\r\n            break;\r\n          case 4:\r\n            d.value = data.secret;\r\n            break;\r\n        }\r\n      });\r\n\r\n      // d.viewStuckPlane =\r\n      //         data.stuckPlane == true\r\n      //           ? this.$t(\"LocaleString.B20009\")\r\n      //           : this.$t(\"LocaleString.B20010\");\r\n    },\r\n    updateValue(data) {\r\n      let viewM2ListArr = [];\r\n      let viewTabArr = [];\r\n      let viewLicenseArr = [];\r\n      data.m2List.forEach(d => {\r\n        let v = this.showingType.find(t => t.value == d).label;\r\n        viewM2ListArr.push(v);\r\n      });\r\n      this.viewM2List = viewM2ListArr.join(\", \");\r\n\r\n      data.tabList.forEach(d => {\r\n        let v = this.showingTab.find(t => t.value == d).label;\r\n        viewTabArr.push(v);\r\n      });\r\n\r\n      data.license.forEach(d => {\r\n        let v = this.serviceCodeList.find(t => t.key == d).value;\r\n        viewLicenseArr.push(v);\r\n      });\r\n\r\n      this.iconSizeTitle = this.iconSizeList.find(\r\n        t => t.value == data.iconSize\r\n      ).label;\r\n      this.defaultPageSizeTitle =\r\n        data.defaultPageSize == \"\"\r\n          ? \"\"\r\n          : this.pageSizeList.find(t => t.value == data.defaultPageSize).label;\r\n      this.m5ObjectTypeTitle =\r\n        data.m5ObjectType == \"\"\r\n          ? \"\"\r\n          : this.m5ObjectTypeList.find(t => t.value == data.m5ObjectType).label;\r\n      this.defaultTabTitle = this.showingTab.find(\r\n        t => t.value == data.defaultTab\r\n      ).label;\r\n\r\n      this.viewTab = viewTabArr.join(\", \");\r\n      this.viewLicense = viewLicenseArr.join(\", \");\r\n      this.viewPlaySound =\r\n        localStorage.getItem(\"sns_alertSound\") == \"true\"\r\n          ? this.$t(\"LocaleString.B20009\")\r\n          : this.$t(\"LocaleString.B20010\");\r\n      this.viewM2Line =\r\n        localStorage.getItem(\"sns_m2Line\") == \"true\"\r\n          ? this.$t(\"LocaleString.B20009\")\r\n          : this.$t(\"LocaleString.B20010\");\r\n      this.data.forEach(d => {\r\n        switch (d.section) {\r\n          case 0:\r\n            d.value = data.systemName;\r\n            break;\r\n          case 1:\r\n            d.value = this.viewTab;\r\n            break;\r\n          case 2:\r\n            d.value = this.viewPlaySound;\r\n            d.valueTime = data.repeatSound;\r\n            break;\r\n          case 3:\r\n            d.value = this.viewM2List;\r\n            break;\r\n          case 4:\r\n            d.value = data.iconSize;\r\n            break;\r\n          case 5:\r\n            d.value = data.defaultTab;\r\n            break;\r\n          case 6:\r\n            d.value = data.updateSeconds;\r\n            break;\r\n          case 7:\r\n            d.value = data.grayTime * 1;\r\n            break;\r\n          case 8:\r\n            d.value = this.viewM2Line;\r\n            break;\r\n          case 9:\r\n            d.value =\r\n              data.eventAuthClose == true\r\n                ? this.$t(\"LocaleString.B20009\")\r\n                : this.$t(\"LocaleString.B20010\");\r\n            d.boolean = data.eventAuthClose == true ? true : false;\r\n            break;\r\n          case 10:\r\n            d.value = data.eventKeepDays;\r\n            break;\r\n          case 11:\r\n            d.value = data.influxRange;\r\n            break;\r\n          case 12:\r\n            d.value = data.lossSignal;\r\n            d.valueDevices = this.transformName(\r\n              data.lossSignalDevices,\r\n              \"lossSignal\"\r\n            );\r\n            break;\r\n          case 22:\r\n            d.value = data.lossSignal2;\r\n            d.valueDevices = this.transformName(\r\n              data.lossSignalDevices2,\r\n              \"lossSignal2\"\r\n            );\r\n            break;\r\n          case 13:\r\n            d.value = data.lowBattery;\r\n            d.valueDevices = this.transformName(\r\n              data.lowBatteryDevices,\r\n              \"lowBattery\"\r\n            );\r\n            break;\r\n          case 14:\r\n            d.value = data.skipEvent;\r\n            d.valueSkipEvent = this.transformName(data.skipEvent, \"skipEvent\");\r\n            break;\r\n          case 15:\r\n            d.value = data.abnormalDevice;\r\n            d.valueDevices = this.transformName(\r\n              data.abnormalDevices,\r\n              \"abnormalDevice\"\r\n            );\r\n            break;\r\n          case 16:\r\n            d.value = data.deviceSelectList;\r\n            d.valueDeviceSelectList = this.transformName(\r\n              data.deviceSelectList,\r\n              \"deviceSelectList\"\r\n            );\r\n            break;\r\n          case 17:\r\n            d.value = this.viewLogo;\r\n            break;\r\n          case 18:\r\n            d.viewMultiPlanes =\r\n              data.multiPlanes == true\r\n                ? this.$t(\"LocaleString.B20009\")\r\n                : this.$t(\"LocaleString.B20010\");\r\n            d.multiPlanes = data.multiPlanes == true ? true : false;\r\n            d.value = data.selectedPlanes;\r\n            d.valuePlaneSelectList = this.transformPlaneName(\r\n              data.selectedPlanes\r\n            );\r\n            break;\r\n          case 19:\r\n            d.value = data.m1LongPress;\r\n            d.valueM1LongPressList = this.transformName(\r\n              data.m1LongPress,\r\n              \"m1LongPress\"\r\n            );\r\n            break;\r\n          case 20:\r\n            d.value = data.defaultPageSize;\r\n            break;\r\n          case 21:\r\n            d.value = data.m5ObjectType;\r\n            break;\r\n          case 23:\r\n            d.value = this.viewLicense;\r\n            break;\r\n          case 24:\r\n            d.value =\r\n              data.showLeaveBed == true\r\n                ? this.$t(\"LocaleString.B20009\")\r\n                : this.$t(\"LocaleString.B20010\");\r\n            break;\r\n          case 25:\r\n            d.value = data.m2Statistic;\r\n            break;\r\n          case 26:\r\n            d.value = data.m3Statistic;\r\n            break;\r\n          case 27:\r\n            d.value = data.m4Statistic;\r\n            break;\r\n          case 28:\r\n            d.value = data.m8Statistic;\r\n            break;\r\n          case 29:\r\n            d.value = data.m9Statistic;\r\n            break;\r\n          case 30:\r\n            d.viewStuckPlane =\r\n              data.stuckPlane == true\r\n                ? this.$t(\"LocaleString.B20009\")\r\n                : this.$t(\"LocaleString.B20010\");\r\n            d.stuckPlane = data.stuckPlane == true ? true : false;\r\n            break;\r\n          case 31:\r\n            d.value = data.logoutTime;\r\n            break;\r\n          case 32:\r\n            d.value = data.stateColor;\r\n            break;\r\n        }\r\n      });\r\n    },\r\n    transformName(deviceArray, type) {\r\n      let tempData = [];\r\n      let deviceType = \"\";\r\n      if (deviceArray && deviceArray.length > 0) {\r\n        if (deviceArray.length == 1 && deviceArray[0] === \"\") {\r\n          return \"\";\r\n        }\r\n        switch (type) {\r\n          case \"lossSignal\":\r\n            try {\r\n              deviceArray.forEach(item => {\r\n                deviceType = item;\r\n                let d = this.deviceListLossSignal.find(data => data.key == item)\r\n                  .value;\r\n                tempData.push(d);\r\n              });\r\n            } catch (e) {\r\n              tempData.push(deviceType);\r\n            }\r\n\r\n            break;\r\n          case \"lossSignal2\":\r\n            try {\r\n              deviceArray.forEach(item => {\r\n                deviceType = item;\r\n                let d = this.deviceListLossSignal2.find(\r\n                  data => data.key == item\r\n                ).value;\r\n                tempData.push(d);\r\n              });\r\n            } catch (e) {\r\n              tempData.push(deviceType);\r\n            }\r\n\r\n            break;\r\n          case \"lowBattery\":\r\n            try {\r\n              deviceArray.forEach(item => {\r\n                deviceType = item;\r\n                let d = this.deviceListLowBattery.find(data => data.key == item)\r\n                  .value;\r\n                tempData.push(d);\r\n              });\r\n            } catch (e) {\r\n              tempData.push(deviceType);\r\n            }\r\n            break;\r\n          case \"abnormalDevice\":\r\n            try {\r\n              deviceArray.forEach(item => {\r\n                deviceType = item;\r\n                let d = this.deviceListAbnormalDevice.find(\r\n                  data => data.key == item\r\n                ).value;\r\n                tempData.push(d);\r\n              });\r\n            } catch (e) {\r\n              tempData.push(deviceType);\r\n            }\r\n            break;\r\n          case \"skipEvent\":\r\n            deviceArray.forEach(item => {\r\n              let d = this.eventServiceCodeList.find(data => data.key == item)\r\n                .value;\r\n              tempData.push(d);\r\n            });\r\n            break;\r\n          case \"deviceSelectList\":\r\n            try {\r\n              deviceArray.forEach(item => {\r\n                deviceType = item;\r\n                let d = this.deviceDefaultSelection.find(\r\n                  data => data.key == item\r\n                ).value;\r\n                tempData.push(d);\r\n              });\r\n            } catch (e) {\r\n              tempData.push(deviceType);\r\n            }\r\n            break;\r\n          case \"m1LongPress\":\r\n            try {\r\n              deviceArray.forEach(item => {\r\n                deviceType = item;\r\n                let d = this.deviceDefaultSelection.find(\r\n                  data => data.key == item\r\n                ).value;\r\n                tempData.push(d);\r\n              });\r\n            } catch (e) {\r\n              tempData.push(deviceType);\r\n            }\r\n            break;\r\n        }\r\n        return tempData.join(\", \");\r\n      }\r\n    },\r\n    transformPlaneName(planeArray) {\r\n      let tempData = [];\r\n      if (planeArray.length == 1 && planeArray[0] === \"\") {\r\n        return \"\";\r\n      }\r\n      if (planeArray && planeArray.length > 0 && this.planeList.length > 0) {\r\n        planeArray.forEach(item => {\r\n          let d = this.planeList.find(data => data.value == item).label;\r\n          tempData.push(d);\r\n        });\r\n        return tempData.join(\", \");\r\n      } else {\r\n        return \"\";\r\n      }\r\n    },\r\n    loadKMData() {\r\n      let params = {\r\n        inlinecount: true,\r\n        search: \"category eq @SNS@SettingKM\"\r\n      };\r\n\r\n      this.$service.getPOCProperties.send(params).then(res => {\r\n        if (res.count > 0) {\r\n          res.results[0].properties.forEach(res => {\r\n            switch (res.key) {\r\n              case \"equipment_longPress_cht\":\r\n                this.kmConfigList[0].longPress_cht = res.value;\r\n                break;\r\n              case \"equipment_longPress_en\":\r\n                this.kmConfigList[0].longPress_en = res.value;\r\n                break;\r\n              case \"equipment_shortClick_cht\":\r\n                this.kmConfigList[0].shortClick_cht = res.value;\r\n                break;\r\n              case \"equipment_shortClick_en\":\r\n                this.kmConfigList[0].shortClick_en = res.value;\r\n                break;\r\n              case \"equipment_doubleClick_cht\":\r\n                this.kmConfigList[0].doubleClick_cht = res.value;\r\n                break;\r\n              case \"equipment_doubleClick_en\":\r\n                this.kmConfigList[0].doubleClick_en = res.value;\r\n                break;\r\n              case \"people_longPress_cht\":\r\n                this.kmConfigList[1].longPress_cht = res.value;\r\n                break;\r\n              case \"people_longPress_en\":\r\n                this.kmConfigList[1].longPress_en = res.value;\r\n                break;\r\n              case \"people_shortClick_cht\":\r\n                this.kmConfigList[1].shortClick_cht = res.value;\r\n                break;\r\n              case \"people_shortClick_en\":\r\n                this.kmConfigList[1].shortClick_en = res.value;\r\n                break;\r\n              case \"people_doubleClick_cht\":\r\n                this.kmConfigList[1].doubleClick_cht = res.value;\r\n                break;\r\n              case \"people_doubleClick_en\":\r\n                this.kmConfigList[1].doubleClick_en = res.value;\r\n                break;\r\n              case \"space_longPress_cht\":\r\n                this.kmConfigList[2].longPress_cht = res.value;\r\n                break;\r\n              case \"space_longPress_en\":\r\n                this.kmConfigList[2].longPress_en = res.value;\r\n                break;\r\n              case \"space_shortClick_cht\":\r\n                this.kmConfigList[2].shortClick_cht = res.value;\r\n                break;\r\n              case \"space_shortClick_en\":\r\n                this.kmConfigList[2].shortClick_en = res.value;\r\n                break;\r\n              case \"space_doubleClick_cht\":\r\n                this.kmConfigList[2].doubleClick_cht = res.value;\r\n                break;\r\n              case \"space_doubleClick_en\":\r\n                this.kmConfigList[2].doubleClick_en = res.value;\r\n                break;\r\n              case \"other_longPress_cht\":\r\n                this.kmConfigList[3].longPress_cht = res.value;\r\n                break;\r\n              case \"other_longPress_en\":\r\n                this.kmConfigList[3].longPress_en = res.value;\r\n                break;\r\n              case \"other_shortClick_cht\":\r\n                this.kmConfigList[3].shortClick_cht = res.value;\r\n                break;\r\n              case \"other_shortClick_en\":\r\n                this.kmConfigList[3].shortClick_en = res.value;\r\n                break;\r\n              case \"other_doubleClick_cht\":\r\n                this.kmConfigList[3].doubleClick_cht = res.value;\r\n                break;\r\n              case \"other_doubleClick_en\":\r\n                this.kmConfigList[3].doubleClick_en = res.value;\r\n                break;\r\n            }\r\n          });\r\n        }\r\n        this.kmConfigListEdit = JSON.parse(JSON.stringify(this.kmConfigList));\r\n      });\r\n    },\r\n    loadCameraData() {\r\n      let params = {\r\n        inlinecount: true,\r\n        search: \"category eq @SNS@SettingCamera\"\r\n      };\r\n\r\n      this.$service.getPOCProperties.send(params).then(res => {\r\n        if (res.count > 0) {\r\n          res.results[0].properties.forEach(res => {\r\n            switch (res.key) {\r\n              //help\r\n              case \"help_capture\":\r\n                this.cameraConfigList[0].capture =\r\n                  res.value == \"true\" ? true : false;\r\n                break;\r\n              case \"help_prefixMinute\":\r\n                this.cameraConfigList[0].prefixMinute = res.value * 1;\r\n                break;\r\n              case \"help_suffixMinute\":\r\n                this.cameraConfigList[0].suffixMinute = res.value * 1;\r\n                break;\r\n              case \"help_backupDirectory\":\r\n                this.cameraConfigList[0].backupDirectory = res.value;\r\n                break;\r\n              case \"help_account\":\r\n                this.cameraConfigList[0].account = res.value;\r\n                break;\r\n              case \"help_password\":\r\n                this.cameraConfigList[0].password = res.value;\r\n                break;\r\n              case \"help_keepDays\":\r\n                this.cameraConfigList[0].keepDays = res.value * 1;\r\n                break;\r\n              case \"help_positionCapture\":\r\n                this.cameraConfigList[0].positionCapture = res.value * 1;\r\n                break;\r\n\r\n              //Enter\r\n              case \"enter_capture\":\r\n                this.cameraConfigList[1].capture =\r\n                  res.value == \"true\" ? true : false;\r\n                break;\r\n              case \"enter_prefixMinute\":\r\n                this.cameraConfigList[1].prefixMinute = res.value * 1;\r\n                break;\r\n              case \"enter_suffixMinute\":\r\n                this.cameraConfigList[1].suffixMinute = res.value * 1;\r\n                break;\r\n              case \"enter_backupDirectory\":\r\n                this.cameraConfigList[1].backupDirectory = res.value;\r\n                break;\r\n              case \"enter_account\":\r\n                this.cameraConfigList[1].account = res.value;\r\n                break;\r\n              case \"enter_password\":\r\n                this.cameraConfigList[1].password = res.value;\r\n                break;\r\n              case \"enter_keepDays\":\r\n                this.cameraConfigList[1].keepDays = res.value * 1;\r\n                break;\r\n              case \"enter_positionCapture\":\r\n                this.cameraConfigList[1].positionCapture = res.value * 1;\r\n                break;\r\n\r\n              //Leave\r\n              case \"leave_capture\":\r\n                this.cameraConfigList[2].capture =\r\n                  res.value == \"true\" ? true : false;\r\n                break;\r\n              case \"leave_prefixMinute\":\r\n                this.cameraConfigList[2].prefixMinute = res.value * 1;\r\n                break;\r\n              case \"leave_suffixMinute\":\r\n                this.cameraConfigList[2].suffixMinute = res.value * 1;\r\n                break;\r\n              case \"leave_backupDirectory\":\r\n                this.cameraConfigList[2].backupDirectory = res.value;\r\n                break;\r\n              case \"leave_account\":\r\n                this.cameraConfigList[2].account = res.value;\r\n                break;\r\n              case \"leave_password\":\r\n                this.cameraConfigList[2].password = res.value;\r\n                break;\r\n              case \"leave_keepDays\":\r\n                this.cameraConfigList[2].keepDays = res.value * 1;\r\n                break;\r\n              case \"leave_positionCapture\":\r\n                this.cameraConfigList[2].positionCapture = res.value * 1;\r\n                break;\r\n\r\n              //fall\r\n              case \"fall_capture\":\r\n                this.cameraConfigList[3].capture =\r\n                  res.value == \"true\" ? true : false;\r\n                break;\r\n              case \"fall_prefixMinute\":\r\n                this.cameraConfigList[3].prefixMinute = res.value * 1;\r\n                break;\r\n              case \"fall_suffixMinute\":\r\n                this.cameraConfigList[3].suffixMinute = res.value * 1;\r\n                break;\r\n              case \"fall_backupDirectory\":\r\n                this.cameraConfigList[3].backupDirectory = res.value;\r\n                break;\r\n              case \"fall_account\":\r\n                this.cameraConfigList[3].account = res.value;\r\n                break;\r\n              case \"fall_password\":\r\n                this.cameraConfigList[3].password = res.value;\r\n                break;\r\n              case \"fall_keepDays\":\r\n                this.cameraConfigList[3].keepDays = res.value * 1;\r\n                break;\r\n              case \"fall_positionCapture\":\r\n                this.cameraConfigList[3].positionCapture = res.value * 1;\r\n                break;\r\n            }\r\n          });\r\n        }\r\n        this.kmConfigListEdit = JSON.parse(JSON.stringify(this.kmConfigList));\r\n      });\r\n    },\r\n    loadStatisticLineData() {\r\n      let params = {\r\n        inlinecount: true,\r\n        search: \"category eq @SNS@SettingStatisticLine\"\r\n      };\r\n      this.$service.getPOCProperties.send(params).then(resData => {\r\n        if (resData.count > 0) {\r\n          resData.results[0].properties.forEach(res => {\r\n            let key = res.key.split(\"_\")[0];\r\n            let data = res.key.split(\"_\")[1];\r\n            // if(req)\r\n            //     this.cameraConfigList[0].capture = res.value == \"true\" ? true : false;\r\n\r\n            this.statisticLineConfigList.forEach(item => {\r\n              if (item.entry == key) {\r\n                if (!isNaN(parseInt(res.value))) {\r\n                  item[data] = res.value * 1;\r\n                } else if (res.value == \"true\" || res == \"false\") {\r\n                  item[data] =\r\n                    res.value == \"true\"\r\n                      ? true\r\n                      : res.value == \"false\"\r\n                      ? false\r\n                      : false;\r\n                } else {\r\n                  item[data] = res.value;\r\n                }\r\n              }\r\n            });\r\n          });\r\n        }\r\n      });\r\n    },\r\n    getCameraType(type) {\r\n      if (type != undefined) {\r\n        let typeData = this.cameraTypeList.find(t => t.type == type).name;\r\n        return typeData;\r\n      } else {\r\n        return \"\";\r\n      }\r\n    },\r\n    getObjectType(type) {\r\n      if (type != undefined) {\r\n        let typeData = this.kmObjectTypeList.find(t => t.type == type).name;\r\n        return typeData;\r\n      } else {\r\n        return \"\";\r\n      }\r\n    },\r\n    onEditDataChange(type, row, index) {\r\n      switch (type) {\r\n        case \"longPress_cht\":\r\n          this.kmConfigListEdit[index].longPress_cht = row.longPress_cht;\r\n          break;\r\n        case \"longPress_en\":\r\n          this.kmConfigListEdit[index].longPress_en = row.longPress_en;\r\n          break;\r\n        case \"shortClick_cht\":\r\n          this.kmConfigListEdit[index].shortClick_cht = row.shortClick_cht;\r\n          break;\r\n        case \"shortClick_en\":\r\n          this.kmConfigListEdit[index].shortClick_en = row.shortClick_en;\r\n          break;\r\n        case \"doubleClick_cht\":\r\n          this.kmConfigListEdit[index].doubleClick_cht = row.doubleClick_cht;\r\n          break;\r\n        case \"doubleClick_en\":\r\n          this.kmConfigListEdit[index].doubleClick_en = row.doubleClick_en;\r\n          break;\r\n      }\r\n    },\r\n    normalizeHeight() {\r\n      setTimeout(\r\n        function() {\r\n          const modal = document.querySelector(\".ivu-modal-body .content\");\r\n          if (modal) {\r\n            modal.style.height = window.innerHeight - 400 + \"px\";\r\n          }\r\n          const modal2 = document.querySelector(\".ivu-modal-footer\");\r\n          if (modal2) {\r\n            modal2.style.height = \"30px\";\r\n          }\r\n        }.bind(this),\r\n        50\r\n      );\r\n    },\r\n    dynamicSort(property) {\r\n      var sortOrder = 1;\r\n      if (property[0] === \"-\") {\r\n        sortOrder = -1;\r\n        property = property.substr(1);\r\n      }\r\n      return function(a, b) {\r\n        var result =\r\n          a[property] < b[property] ? -1 : a[property] > b[property] ? 1 : 0;\r\n        return result * sortOrder;\r\n      };\r\n    },\r\n    edit() {\r\n      this.isEdit = true;\r\n      this.play(\"stop\");\r\n      this.loadLogo();\r\n      this.loadSound();\r\n      //this.getSystemConfig()\r\n    },\r\n    cancelEdit() {\r\n      this.play(\"stop\");\r\n      this.file = null;\r\n      this.fileURL = null;\r\n      this.isEdit = false;\r\n      this.configData = JSON.parse(JSON.stringify(this.origConfigData));\r\n      this.thirdPartyData = JSON.parse(JSON.stringify(this.origThirdPartyData));\r\n      this.cameraConfigList = JSON.parse(\r\n        JSON.stringify(this.defaultCameraConfigList)\r\n      );\r\n      this.updateValue(this.configData);\r\n      this.loadLogo();\r\n      this.loadSound();\r\n      this.loadKMData();\r\n      this.loadCameraData();\r\n      this.loadThirdParty();\r\n      //this.getSystemConfig();\r\n    },\r\n\r\n    // editHandleSubmit() {\r\n    //   console.log(\"editHandleSubmit\");\r\n    //   console.log(this.configList);\r\n    //   console.log(this.configListToSave);\r\n    //   console.log(\"LoginUser: \" + localStorage.getItem(\"sns_loginUser\"));\r\n\r\n    //   let putdata = [];\r\n    //   let properties = [];\r\n    //   let loginUserName = localStorage.getItem(\"sns_loginUser\");\r\n    //   let appEnableAccountKey = \"appsEnable-\" + loginUserName;\r\n    //   let appEditAccountKey = \"appsEdit-\" + loginUserName;\r\n    //   let appEnableForUser = \"\";\r\n    //   let appEditForUser = \"\";\r\n\r\n    //   this.configList.forEach((x) => {\r\n    //     if (x.key == appEnableAccountKey) appEnableForUser = x.value.toString();\r\n    //     else if (x.key == appEditAccountKey)\r\n    //       appEditForUser = x.value.toString();\r\n    //   });\r\n    //   this.configListToSave.forEach((y) => {\r\n    //     switch (y.schema.type) {\r\n    //       case \"string\":\r\n    //         properties.push({\r\n    //           key: y.key,\r\n    //           value: y.value,\r\n    //         });\r\n    //         break;\r\n    //       case \"select\":\r\n    //         if (y.key != appEnableAccountKey)\r\n    //           properties.push({\r\n    //             key: y.key,\r\n    //             value: y.value.toString(),\r\n    //           });\r\n    //         break;\r\n    //       case \"boolean\":\r\n    //         properties.push({\r\n    //           key: y.key,\r\n    //           value: y.value.toString(),\r\n    //         });\r\n    //         break;\r\n    //     }\r\n    //   });\r\n    //   if (loginUserName) {\r\n    //     properties.push({\r\n    //       key: appEnableAccountKey,\r\n    //       value: appEnableForUser,\r\n    //     });\r\n    //     properties.push({\r\n    //       key: appEditAccountKey,\r\n    //       value: appEditForUser,\r\n    //     });\r\n    //   }\r\n\r\n    //   putdata.push({\r\n    //     category: \"pocConfig\",\r\n    //     properties: properties,\r\n    //   });\r\n    //   console.log(\"properties=\");\r\n    //   console.log(putdata);\r\n\r\n    //   this.$service.editPOCProperties.send(putdata).then((res) => {\r\n    //     console.log(res);\r\n    //     this.$Notice.success({\r\n    //       title: this.$t(\"LocaleString.M00004\"),\r\n    //       desc: this.$t(\"LocaleString.M00002\"),\r\n    //       duration: Config.SUCCESS_DURATION,\r\n    //     });\r\n\r\n    //     this.$store.commit(\"setPOCConfigChanged\", moment().valueOf());\r\n\r\n    //     this.isShow = false;\r\n    //     this.$emit(\"closeSystemConfig\");\r\n    //   });\r\n    // },\r\n\r\n    // computedDate(val) {\r\n    //   if (val) {\r\n    //     return moment(val).format(\"YYYY-MM-DD\");\r\n    //   }\r\n    //   return \"\";\r\n    // },\r\n    // computedDateTime(val) {\r\n    //   if (val) {\r\n    //     return moment(val).format(\"YYYY-MM-DD HH:mm:ss\");\r\n    //   }\r\n    //   return \"\";\r\n    // },\r\n    cancel() {\r\n      this.isShow = false;\r\n      this.configData = this.origConfigData;\r\n      this.thirdPartyData = this.origThirdPartyData;\r\n      setTimeout(() => {\r\n        this.$emit(\"closeSystemConfig\");\r\n      }, 500);\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n.about-data-modal {\r\n  .vertical-center-modal {\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n\r\n    .ivu-modal {\r\n      top: 0;\r\n    }\r\n  }\r\n\r\n  .ivu-modal-close {\r\n    top: 15px;\r\n    right: 20px;\r\n  }\r\n\r\n  .content {\r\n    padding: 4px 14px;\r\n    overflow-y: auto;\r\n    //height: 700px;\r\n    //height: 400px;\r\n  }\r\n\r\n  @media only screen and (min-width: 1200px) {\r\n    .ivu-modal-close {\r\n      top: 18px;\r\n    }\r\n  }\r\n\r\n  .ivu-modal-header {\r\n    padding: 15px 20px;\r\n    border-bottom: 1px solid #f7f7f7;\r\n\r\n    .header {\r\n      overflow: hidden;\r\n\r\n      .left {\r\n        float: left;\r\n        width: 6px;\r\n        height: 24px;\r\n        margin-right: 8px;\r\n        background-color: #0ac7c0;\r\n        border-radius: 3px;\r\n      }\r\n\r\n      .title {\r\n        float: left;\r\n        height: 24px;\r\n        line-height: 24px;\r\n        font-size: 14px;\r\n        font-weight: bold;\r\n        color: #999;\r\n\r\n        span {\r\n          color: #333;\r\n        }\r\n      }\r\n\r\n      .es-btn {\r\n        float: right;\r\n        margin-right: 35px;\r\n      }\r\n\r\n      @media only screen and (min-width: 1200px) {\r\n        .left {\r\n          height: 30px;\r\n        }\r\n\r\n        .title {\r\n          height: 30px;\r\n          line-height: 30px;\r\n          font-size: 18px;\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .ivu-modal-body {\r\n    .content {\r\n      padding: 4px 14px;\r\n      overflow-y: auto;\r\n\r\n      .row {\r\n        margin-bottom: 20px;\r\n\r\n        .item {\r\n          padding: 15px;\r\n          border: 1px solid #f7f7f7;\r\n          border-radius: 4px;\r\n\r\n          label {\r\n            color: #999;\r\n            font-weight: bold;\r\n          }\r\n\r\n          p {\r\n            margin-top: 10px;\r\n            color: #333;\r\n          }\r\n        }\r\n      }\r\n\r\n      .form-item {\r\n        padding: 15px 15px 22px;\r\n        margin-bottom: 16px;\r\n        border-radius: 4px;\r\n        background-color: #f7f7f7;\r\n      }\r\n    }\r\n  }\r\n\r\n  .ivu-modal-footer {\r\n    height: 50px;\r\n    border-top: none;\r\n    padding: 0;\r\n  }\r\n}\r\n</style>\r\n\r\n<style lang=\"less\" scoped>\r\n/deep/ .ivu-btn-primary {\r\n  color: #fff;\r\n  background-color: #31babb;\r\n  border-color: #31babb;\r\n}\r\n\r\n/deep/ .ivu-btn-primary:hover {\r\n  color: #fff !important;\r\n  border-color: #31babb;\r\n}\r\n\r\n/deep/ .ivu-switch-checked {\r\n  background-color: #31babb;\r\n  border-color: #31babb;\r\n}\r\n\r\n.musicIcon {\r\n  vertical-align: middle;\r\n  margin-left: 5px;\r\n  color: #31babb;\r\n  cursor: pointer;\r\n}\r\n\r\n/deep/ .ivu-tag .ivu-icon-ios-close {\r\n  display: none;\r\n}\r\n\r\n/deep/ .ivu-select-multiple .ivu-tag span:not(.ivu-select-max-tag) {\r\n  margin-right: 0px;\r\n}\r\n</style>\r\n"]}]}