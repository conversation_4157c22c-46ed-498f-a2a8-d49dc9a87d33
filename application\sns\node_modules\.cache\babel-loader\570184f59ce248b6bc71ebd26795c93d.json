{"remainingRequest": "E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\node_modules\\babel-loader\\lib\\index.js!E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\node_modules\\string-replace-loader\\index.js??ref--13-0!E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\src\\components\\administrative\\apps\\sns\\planeSetting\\sitePlan\\pointDrag.vue?vue&type=script&lang=js&", "dependencies": [{"path": "E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\src\\components\\administrative\\apps\\sns\\planeSetting\\sitePlan\\pointDrag.vue", "mtime": 1754362736894}, {"path": "E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\node_modules\\string-replace-loader\\index.js", "mtime": 499162500000}, {"path": "E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["pointDrag.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8CA,OAAA,SAAA,MAAA,aAAA;AACA,OAAA,KAAA,MAAA,sEAAA;AACA,OAAA,QAAA,MAAA,uEAAA;AAEA,eAAA;AACA,EAAA,KAAA,EAAA,CACA,KADA,EAEA,UAFA,EAGA,MAHA,EAIA,aAJA,EAKA,WALA,EAMA,oBANA,EAOA,QAPA,EAQA,QARA,EASA,WATA,CADA;AAYA,EAAA,IAZA,kBAYA;AAAA;;AACA,IAAA,UAAA,CAAA,YAAA;AACA;AACA;AACA;AACA,MAAA,KAAA,CAAA,UAAA,GAAA;AACA,QAAA,UAAA,EAAA,SAAA,KAAA,GAAA,aADA;AAEA,QAAA,cAAA,EAAA,OAFA;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,QAAA,GAAA,EACA,KAAA,CAAA,IAAA,CAAA,WAAA,CAAA,CAAA,GAAA,KAAA,CAAA,MAAA,GACA,QAAA,CAAA,KAAA,CAAA,UAAA,CADA,GAEA,IAbA;AAcA,QAAA,IAAA,EACA,KAAA,CAAA,IAAA,CAAA,WAAA,CAAA,CAAA,GAAA,KAAA,CAAA,MAAA,GACA,QAAA,CAAA,KAAA,CAAA,SAAA,CAAA,GAAA,CADA,GAEA,CAFA,GAGA,IAlBA;AAkBA;AACA,QAAA,KAAA,EAAA,KAAA,CAAA,SAAA,GAAA,IAnBA;AAoBA,QAAA,MAAA,EAAA,KAAA,CAAA,UAAA,GAAA;AApBA,OAAA,CAJA,CA0BA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAvCA,EAuCA,GAvCA,CAAA;AAwCA,WAAA;AACA,MAAA,SAAA,EAAA,IADA;AAEA,MAAA,UAAA,EAAA,IAFA;AAGA,MAAA,UAAA,EAAA,EAHA,CAIA;;AAJA,KAAA;AAMA,GA3DA;AA4DA,EAAA,OA5DA,qBA4DA;AAAA;;AACA,QAAA,KAAA,SAAA,EAAA;AACA,MAAA,SAAA,CAAA,KAAA,MAAA,CAAA,GAAA,EAAA,KAAA,IAAA,CAAA,EAAA,EAAA,UAAA,KAAA,EAAA;AACA,QAAA,MAAA,CAAA,KAAA,CAAA,eAAA,EAAA,KAAA;AACA,OAFA,CAAA;AAGA,KALA,CAMA;;;AACA,QAAA,QAAA,GAAA,YAAA,CAAA,OAAA,CAAA,cAAA,CAAA;AACA,SAAA,SAAA,GAAA,QAAA;AACA,SAAA,UAAA,GAAA,QAAA,CATA,CAUA;AACA,GAvEA;AAwEA,EAAA,OAAA,EAAA;AACA,IAAA,gBADA,4BACA,IADA,EACA;AACA,UAAA,SAAA,CAAA,CAAA,CAAA,KAAA,CAAA,EAAA;AACA,QAAA,QAAA,CAAA,cAAA,CAAA,SAAA,CAAA,CAAA,CAAA,EAAA,KAAA,CAAA,UAAA,GACA,SAAA,KAAA,GAAA,2BADA;AAEA;;AACA,MAAA,QAAA,CAAA,cAAA,CAAA,IAAA,CAAA,EAAA,EAAA,KAAA,CAAA,UAAA,GACA,SAAA,QAAA,GAAA,2BADA;AAEA,KARA;AASA,IAAA,WATA,yBASA;AACA;AACA,UAAA,SAAA,GAAA,QAAA,CAAA,cAAA,CAAA,KAAA,IAAA,CAAA,EAAA,CAAA,CAFA,CAGA;;AACA,MAAA,SAAA,CAAA,WAAA,GAAA,YAAA,CAAA,CAAA;;AACA,MAAA,SAAA,CAAA,KAAA,CAAA,MAAA,GAAA,EAAA;AACA;AAfA,GAxEA;AAyFA,EAAA,KAAA,EAAA;AACA,IAAA,SAAA,EAAA;AACA,MAAA,OADA,qBACA;AAAA;;AACA,YAAA,KAAA,SAAA,EAAA;AACA,UAAA,SAAA,CAAA,KAAA,MAAA,CAAA,GAAA,EAAA,KAAA,IAAA,CAAA,EAAA,EAAA,UAAA,KAAA,EAAA;AACA,YAAA,MAAA,CAAA,KAAA,CAAA,eAAA,EAAA,KAAA;AACA,WAFA,CAAA;AAGA,SAJA,MAIA;AACA,eAAA,WAAA;AACA;AACA;AATA,KADA;AAYA,IAAA,IAAA,EAAA,cAAA,OAAA,EAAA,OAAA,EAAA;AACA,WAAA,IAAA,GAAA,OAAA,CADA,CAEA;;AAEA,MAAA,QAAA,CAAA,cAAA,CAAA,OAAA,CAAA,EAAA,EAAA,KAAA,CAAA,IAAA,GACA,KAAA,IAAA,CAAA,WAAA,CAAA,CAAA,GAAA,KAAA,MAAA,GACA,QAAA,CAAA,KAAA,SAAA,CAAA,GAAA,CADA,GAEA,CAFA,GAGA,IAJA;AAKA,MAAA,QAAA,CAAA,cAAA,CAAA,OAAA,CAAA,EAAA,EAAA,KAAA,CAAA,GAAA,GACA,KAAA,IAAA,CAAA,WAAA,CAAA,CAAA,GAAA,KAAA,MAAA,GACA,QAAA,CAAA,KAAA,UAAA,CADA,GAEA,IAHA,CATA,CAaA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA,KAnCA;AAoCA,IAAA,kBAAA,EAAA,4BAAA,OAAA,EAAA,OAAA,EAAA;AACA,UAAA,OAAA,EAAA;AACA,QAAA,QAAA,CAAA,cAAA,CAAA,OAAA,EAAA,KAAA,CAAA,UAAA,GACA,SAAA,KAAA,GAAA,2BADA;AAEA,QAAA,QAAA,CAAA,cAAA,CAAA,OAAA,EAAA,KAAA,CAAA,MAAA,GAAA,GAAA;AACA;;AACA,UAAA,OAAA,IAAA,KAAA,WAAA,EAAA;AACA,QAAA,QAAA,CAAA,cAAA,CAAA,OAAA,EAAA,KAAA,CAAA,UAAA,GACA,SAAA,QAAA,GAAA,2BADA;AAEA,QAAA,QAAA,CAAA,cAAA,CAAA,OAAA,EAAA,KAAA,CAAA,MAAA,GAAA,KAAA;AACA;AACA;AA/CA;AAzFA,CAAA", "sourcesContent": ["<style lang=\"scss\">\r\n.dragContainPoint {\r\n  // width: 27px;\r\n  // height: 38px;\r\n  position: absolute;\r\n  top: 0;\r\n  // opacity: 0.7;\r\n  word-break: break-all;\r\n  text-align: center;\r\n  font-size: 16px;\r\n  z-index: 9999;\r\n\r\n  .cross {\r\n    position: absolute;\r\n    height: 20px;\r\n    width: 20px;\r\n    top: -10px;\r\n    left: -10px;\r\n  }\r\n\r\n  .stationText {\r\n    // position: absolute;\r\n    width: 40px;\r\n    height: 40px;\r\n    color: transparent;\r\n  }\r\n}\r\n\r\n.dragContainPointHeight {\r\n  height: 37px !important;\r\n}\r\n</style>\r\n<template>\r\n  <div\r\n    :id=\"item.id\"\r\n    class=\"animated dragContainPoint zoomIn\"\r\n    :class=\"{ dragContainPointHeight: pointType === 'guard' }\"\r\n    :style=\"pointStyle\"\r\n  >\r\n    <Tooltip :content=\"item.name\" placement=\"bottom\" :transfer=\"true\">\r\n      <div class=\"stationText\">1</div>\r\n    </Tooltip>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport pointDrag from \"./pointDrag\";\r\nimport point from \"@/components/administrative/common/images/pointPlane/fill-gps_ok.svg\";\r\nimport pointRed from \"@/components/administrative/common/images/pointPlane/fill-gps_red.svg\";\r\n\r\nexport default {\r\n  props: [\r\n    \"box\",\r\n    \"pointImg\",\r\n    \"item\",\r\n    \"pointActive\",\r\n    \"pointType\",\r\n    \"currentActivePoint\",\r\n    \"pxRate\",\r\n    \"pyRate\",\r\n    \"draggable\",\r\n  ],\r\n  data() {\r\n    setTimeout(() => {\r\n      // console.log('this.item.coordinates.y:'+this.item.coordinates.y)\r\n      // console.log('this.pyRate:'+this.pyRate)\r\n      // console.log('iconHeight:'+this.iconHeight)\r\n      this.pointStyle = {\r\n        background: \"url(\" + point + \") no-repeat\",\r\n        backgroundSize: \"cover\",\r\n        // 圖: 寬=699px, 高=524px\r\n        // 實際公尺： 寬=152.1664， 高=114.1248\r\n        // 比例 :   699/152.1664=4.5936 px/m  , 152.1664/699=0.2176\r\n        // 畫面長寬＝ 實際公尺* 4.5936 (px)\r\n        // 實際座標公尺 = 畫面長寬 * 0.2176 (m)\r\n        // top: (this.item.coordinates.y * this.pxRate)  + 'px',\r\n        // left: (this.item.coordinates.x * this.pxRate) +  'px',\r\n        top:\r\n          this.item.coordinates.y * this.pyRate -\r\n          parseInt(this.iconHeight) +\r\n          \"px\",\r\n        left:\r\n          this.item.coordinates.x * this.pxRate -\r\n          parseInt(this.iconWidth) / 2 +\r\n          1 +\r\n          \"px\", // 7為svg左右留邊px\r\n        width: this.iconWidth + \"px\",\r\n        height: this.iconHeight + \"px\",\r\n      };\r\n      // this.cross = {\r\n      //     background: 'url(' + cross + ') no-repeat',\r\n      //     backgroundSize: 'cover',\r\n      // }\r\n      // if (document.body.clientWidth < 1200) {\r\n      //     this.pointStyle.width = '20px'\r\n      //     this.pointStyle.height = '20px'\r\n      //     this.pointStyle.fontSize = '14px'\r\n      //     // this.cross.height = '10px'\r\n      //     // this.cross.width = '10px'\r\n      //     // this.cross.top = '-5px'\r\n      //     // this.cross.left = '-5px'\r\n      // }\r\n    }, 300);\r\n    return {\r\n      iconWidth: \"40\",\r\n      iconHeight: \"40\",\r\n      pointStyle: {},\r\n      // cross: {}\r\n    };\r\n  },\r\n  mounted() {\r\n    if (this.draggable) {\r\n      pointDrag(this._props.box, this.item.id, (value) => {\r\n        this.$emit(\"pointPosition\", value);\r\n      });\r\n    }\r\n    // console.log('iconSize:'+localStorage.getItem('sns_iconSize'))\r\n    let iconSize = localStorage.getItem(\"sns_iconSize\");\r\n    this.iconWidth = iconSize;\r\n    this.iconHeight = iconSize;\r\n    // let thisitem = document.getElementById('')\r\n  },\r\n  methods: {\r\n    pointDragPitchUp(item) {\r\n      if (arguments[1] !== 0) {\r\n        document.getElementById(arguments[1]).style.background =\r\n          \"url(\" + point + \") 0% 0% / cover no-repeat\";\r\n      }\r\n      document.getElementById(item.id).style.background =\r\n        \"url(\" + pointRed + \") 0% 0% / cover no-repeat\";\r\n    },\r\n    clearObject() {\r\n      // console.log('DDDD:'+this.item.id)\r\n      let container = document.getElementById(this.item.id);\r\n      // console.log('container:'+container)\r\n      container.onmousedown = function () {};\r\n      container.style.cursor = \"\";\r\n    },\r\n  },\r\n  watch: {\r\n    draggable: {\r\n      handler() {\r\n        if (this.draggable) {\r\n          pointDrag(this._props.box, this.item.id, (value) => {\r\n            this.$emit(\"pointPosition\", value);\r\n          });\r\n        } else {\r\n          this.clearObject();\r\n        }\r\n      },\r\n    },\r\n    item: function (newItem, oldItem) {\r\n      this.item = newItem;\r\n      // console.log('this.item.coordinates.x:'+this.item.coordinates.x)\r\n\r\n      document.getElementById(newItem.id).style.left =\r\n        this.item.coordinates.x * this.pxRate -\r\n        parseInt(this.iconWidth) / 2 +\r\n        1 +\r\n        \"px\";\r\n      document.getElementById(newItem.id).style.top =\r\n        this.item.coordinates.y * this.pyRate -\r\n        parseInt(this.iconHeight) +\r\n        \"px\";\r\n      // if (oldItem.id) {\r\n      //     document.getElementById(oldItem.id).style.background = 'url(' + this.pointImg + ') 0% 0% / cover no-repeat'\r\n      // }\r\n      // document.getElementById(newItem.id).style.background = 'url(' + this.pointActive + ') 0% 0% / cover no-repeat'\r\n\r\n      // document.getElementById(newItem.id).style.background = 'url(' + this.pointActive + ') 0% 0% / cover no-repeat'\r\n      // this.$forceUpdate()\r\n      // pointDrag(this._props.box, this.item.id, (value) => {\r\n      //     this.$emit('pointPosition', value)\r\n      // })\r\n    },\r\n    currentActivePoint: function (newItem, oldItem) {\r\n      if (oldItem) {\r\n        document.getElementById(oldItem).style.background =\r\n          \"url(\" + point + \") 0% 0% / cover no-repeat\";\r\n        document.getElementById(oldItem).style.zIndex = \"1\";\r\n      }\r\n      if (newItem && this.pointActive) {\r\n        document.getElementById(newItem).style.background =\r\n          \"url(\" + pointRed + \") 0% 0% / cover no-repeat\";\r\n        document.getElementById(newItem).style.zIndex = \"999\";\r\n      }\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n/deep/ .ivu-tooltip-content {\r\n  margin-left: 0px;\r\n}\r\n\r\n/deep/ .ivu-tooltip-content > .ivu-tooltip-arrow {\r\n  left: 50%;\r\n  margin-left: -5px;\r\n}\r\n\r\n/deep/ .ivu-tooltip-content > .ivu-tooltip-inner {\r\n  margin-left: 0px;\r\n}\r\n</style>"], "sourceRoot": "src/components/administrative/apps/sns/planeSetting/sitePlan"}]}