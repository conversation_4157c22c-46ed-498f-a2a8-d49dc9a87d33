{"remainingRequest": "E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\src\\components\\administrative\\systemManagement\\about.vue?vue&type=style&index=0&lang=scss&", "dependencies": [{"path": "E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\src\\components\\administrative\\systemManagement\\about.vue", "mtime": 1754362736974}, {"path": "E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["about.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA+IA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "about.vue", "sourceRoot": "src/components/administrative/systemManagement", "sourcesContent": ["<template>\r\n  <div class=\"personal-data\">\r\n    <Modal\r\n      class=\"about-data-modal\"\r\n      :mask-closable=\"false\"\r\n      v-model=\"isShow\"\r\n      class-name=\"vertical-center-modal\"\r\n      @on-cancel=\"cancel\"\r\n      width=\"850\"\r\n    >\r\n      <a slot=\"close\">\r\n        <Icon type=\"md-close\" color=\"#ADADAD\" size=\"24\"></Icon>\r\n      </a>\r\n      <div slot=\"header\" class=\"header\">\r\n        <i class=\"left\"></i>\r\n        <span class=\"title\"></span>\r\n          <span class=\"title\">{{ $t(\"LocaleString.L30052\") }}</span>\r\n        </span>\r\n        <!--\r\n        <Button class=\"es-btn\" type=\"primary\" icon=\"ios-refresh\" @click=\"refreshBtn\">Refresh</Button>\r\n        -->\r\n      </div>\r\n      <div class=\"content\">\r\n        <Table\r\n          class=\"about-table\"\r\n          :columns=\"columns1\"\r\n          :data=\"systemInfo\"\r\n          :no-data-text=\"noDataText\"\r\n        ></Table>\r\n      </div>\r\n      <div slot=\"footer\"></div>\r\n    </Modal>\r\n  </div>\r\n</template>\r\n\r\n<script type='es6'>\r\nimport Config from \"@/common/config\";\r\nlet moment = require(\"moment\");\r\nexport default {\r\n  data() {\r\n    return {\r\n      isShow: false,\r\n      systemInfo: [],\r\n      noDataText: this.$t(\"LocaleString.L00081\"),\r\n      columns1: [\r\n        {\r\n          title: this.$t(\"LocaleString.L30053\"),\r\n          key: \"name\",\r\n          width: \"380px\",\r\n          sortable: true,\r\n        },\r\n        {\r\n          title: this.$t(\"LocaleString.L00071\"),\r\n          key: \"version\",\r\n          sortable: true,\r\n        },\r\n      ],\r\n    };\r\n  },\r\n  mounted() {\r\n    this.isShow = true;\r\n    this.getSystemAbout();\r\n    this.getLincense();\r\n    //this.normalizeHeight();\r\n  },\r\n  methods: {\r\n    normalizeHeight() {\r\n      setTimeout(\r\n        function () {\r\n          const modal = document.querySelector(\".ivu-modal-body .content\");\r\n          if (modal) {\r\n            modal.style.height = window.innerHeight - 200 + \"px\";\r\n          }\r\n        }.bind(this),\r\n        50\r\n      );\r\n    },\r\n    dynamicSort(property) {\r\n      var sortOrder = 1;\r\n      if (property[0] === \"-\") {\r\n        sortOrder = -1;\r\n        property = property.substr(1);\r\n      }\r\n      return function (a, b) {\r\n        var result =\r\n          a[property] < b[property] ? -1 : a[property] > b[property] ? 1 : 0;\r\n        return result * sortOrder;\r\n      };\r\n    },\r\n    refreshBtn() {\r\n      this.getSystemAbout();\r\n    },\r\n    getSystemAbout() {\r\n      let _this = this;\r\n      _this.systemInfo.push({\r\n        name: this.$t(\"LocaleString.S00002\"),\r\n        version: Config.WEB_VERSION,\r\n      });\r\n    },\r\n    getLincense(current, pageSize) {\r\n      let _this = this\r\n      let str = 'active eq true'\r\n      let params = {\r\n          search: str,\r\n          inlinecount: true,\r\n          // sort: _this.sort,\r\n          // page: current,\r\n          // size: pageSize\r\n      }\r\n      // params = new URLSearchParams(params)\r\n      // this.$service.getUsers.requestCommon(_this.queryStr, current, pageSize)\r\n      this.$service.getServiceLicense.send(params).then((data) => {\r\n        if (data.count > 0) { \r\n           _this.systemInfo.push({\r\n            name: this.$t(\"LocaleString.L00103\"),\r\n            version: data.results.some(item=>item.code == \"LossSignal\" && item.license.expiresAt)?  data.results.find(item=>item.code == \"LossSignal\").license.expiresAt : \"-\",\r\n          });\r\n        }\r\n      });\r\n    },\r\n    computedDate(val) {\r\n      if (val) {\r\n        return moment(val).format(\"YYYY-MM-DD\");\r\n      }\r\n      return \"\";\r\n    },\r\n    computedDateTime(val) {\r\n      if (val) {\r\n        return moment(val).format(\"YYYY-MM-DD HH:mm:ss\");\r\n      }\r\n      return \"\";\r\n    },\r\n    cancel() {\r\n      this.isShow = false;\r\n      setTimeout(() => {\r\n        this.$emit(\"closeAbout\");\r\n      }, 500);\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n.about-data-modal {\r\n  // .vertical-center-modal {\r\n  //   display: flex;\r\n  //   align-items: center;\r\n  //   justify-content: center;\r\n  //   .ivu-modal {\r\n  //     top: 0;\r\n  //   }\r\n  // }\r\n  .ivu-modal-close {\r\n    top: 15px;\r\n    right: 20px;\r\n  }\r\n  .content {\r\n    padding: 4px 14px;\r\n    overflow-y: auto;\r\n    //height: 700px;\r\n    //height: 400px;\r\n  }\r\n\r\n  @media only screen and (min-width: 1200px) {\r\n    .ivu-modal-close {\r\n      top: 18px;\r\n    }\r\n  }\r\n  .ivu-modal-header {\r\n    padding: 15px 20px;\r\n    border-bottom: 1px solid #f7f7f7;\r\n    .header {\r\n      overflow: hidden;\r\n      .left {\r\n        float: left;\r\n        width: 6px;\r\n        height: 24px;\r\n        margin-right: 8px;\r\n        background-color: #0ac7c0;\r\n        border-radius: 3px;\r\n      }\r\n      .title {\r\n        float: left;\r\n        height: 24px;\r\n        line-height: 24px;\r\n        font-size: 14px;\r\n        font-weight: bold;\r\n        color: #999;\r\n        span {\r\n          color: #333;\r\n        }\r\n      }\r\n      .es-btn {\r\n        float: right;\r\n        margin-right: 35px;\r\n      }\r\n      @media only screen and (min-width: 1200px) {\r\n        .left {\r\n          height: 30px;\r\n        }\r\n        .title {\r\n          height: 30px;\r\n          line-height: 30px;\r\n          font-size: 18px;\r\n        }\r\n      }\r\n    }\r\n  }\r\n  .ivu-modal-body {\r\n    .content {\r\n      padding: 4px 14px;\r\n      overflow-y: auto;\r\n      .row {\r\n        margin-bottom: 20px;\r\n        .item {\r\n          padding: 15px;\r\n          border: 1px solid #f7f7f7;\r\n          border-radius: 4px;\r\n          label {\r\n            color: #999;\r\n            font-weight: bold;\r\n          }\r\n          p {\r\n            margin-top: 10px;\r\n            color: #333;\r\n          }\r\n        }\r\n      }\r\n      .form-item {\r\n        padding: 15px 15px 22px;\r\n        margin-bottom: 16px;\r\n        border-radius: 4px;\r\n        background-color: #f7f7f7;\r\n      }\r\n    }\r\n  }\r\n  .ivu-modal-footer {\r\n    border-top: none;\r\n    padding: 0;\r\n  }\r\n}\r\n</style>\r\n"]}]}