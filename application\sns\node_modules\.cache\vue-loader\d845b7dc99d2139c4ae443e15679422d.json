{"remainingRequest": "E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\src\\components\\administrative\\apps\\sns\\planeSetting\\sitePlan\\pointDrag.vue?vue&type=template&id=04ac45ed&scoped=true&", "dependencies": [{"path": "E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\src\\components\\administrative\\apps\\sns\\planeSetting\\sitePlan\\pointDrag.vue", "mtime": 1754362736894}, {"path": "E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:dmFyIHJlbmRlciA9IGZ1bmN0aW9uKCkgewogIHZhciBfdm0gPSB0aGlzCiAgdmFyIF9oID0gX3ZtLiRjcmVhdGVFbGVtZW50CiAgdmFyIF9jID0gX3ZtLl9zZWxmLl9jIHx8IF9oCiAgcmV0dXJuIF9jKAogICAgImRpdiIsCiAgICB7CiAgICAgIHN0YXRpY0NsYXNzOiAiYW5pbWF0ZWQgZHJhZ0NvbnRhaW5Qb2ludCB6b29tSW4iLAogICAgICBjbGFzczogeyBkcmFnQ29udGFpblBvaW50SGVpZ2h0OiBfdm0ucG9pbnRUeXBlID09PSAiZ3VhcmQiIH0sCiAgICAgIHN0eWxlOiBfdm0ucG9pbnRTdHlsZSwKICAgICAgYXR0cnM6IHsgaWQ6IF92bS5pdGVtLmlkIH0KICAgIH0sCiAgICBbCiAgICAgIF9jKAogICAgICAgICJUb29sdGlwIiwKICAgICAgICB7CiAgICAgICAgICBhdHRyczogeyBjb250ZW50OiBfdm0uaXRlbS5uYW1lLCBwbGFjZW1lbnQ6ICJib3R0b20iLCB0cmFuc2ZlcjogdHJ1ZSB9CiAgICAgICAgfSwKICAgICAgICBbX2MoImRpdiIsIHsgc3RhdGljQ2xhc3M6ICJzdGF0aW9uVGV4dCIgfSwgW192bS5fdigiMSIpXSldCiAgICAgICkKICAgIF0sCiAgICAxCiAgKQp9CnZhciBzdGF0aWNSZW5kZXJGbnMgPSBbXQpyZW5kZXIuX3dpdGhTdHJpcHBlZCA9IHRydWUKCmV4cG9ydCB7IHJlbmRlciwgc3RhdGljUmVuZGVyRm5zIH0="}]}