{"remainingRequest": "E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\node_modules\\babel-loader\\lib\\index.js!E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\node_modules\\string-replace-loader\\index.js??ref--13-0!E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\src\\common\\mqttws31.js", "dependencies": [{"path": "E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\src\\common\\mqttws31.js", "mtime": 1754362736384}, {"path": "E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\node_modules\\string-replace-loader\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["E:/GitRepos/FusionNetProject/6849/frontend/application/sns/src/common/mqttws31.js"], "names": ["<PERSON><PERSON>", "MQTT", "global", "version", "buildLevel", "MESSAGE_TYPE", "CONNECT", "CONNACK", "PUBLISH", "PUBACK", "PUBREC", "PUBREL", "PUBCOMP", "SUBSCRIBE", "SUBACK", "UNSUBSCRIBE", "UNSUBACK", "PINGREQ", "PINGRESP", "DISCONNECT", "validate", "obj", "keys", "key", "hasOwnProperty", "Error", "format", "ERROR", "INVALID_TYPE", "errorStr", "scope", "f", "apply", "arguments", "OK", "code", "text", "CONNECT_TIMEOUT", "SUBSCRIBE_TIMEOUT", "UNSUBSCRIBE_TIMEOUT", "PING_TIMEOUT", "INTERNAL_ERROR", "CONNACK_RETURNCODE", "SOCKET_ERROR", "SOCKET_CLOSE", "MALFORMED_UTF", "UNSUPPORTED", "INVALID_STATE", "INVALID_ARGUMENT", "UNSUPPORTED_OPERATION", "INVALID_STORED_DATA", "INVALID_MQTT_MESSAGE_TYPE", "MALFORMED_UNICODE", "CONNACK_RC", "error", "substitutions", "i", "length", "field", "start", "indexOf", "part1", "substring", "part2", "MqttProtoIdentifierv3", "MqttProtoIdentifierv4", "WireMessage", "type", "options", "name", "prototype", "encode", "first", "re<PERSON><PERSON><PERSON><PERSON>", "topicStrLength", "Array", "messageIdentifier", "undefined", "mqttVersion", "UTF8Length", "clientId", "willMessage", "destinationName", "willMessagePayloadBytes", "payloadBytes", "Uint8Array", "byteLength", "userName", "password", "topics", "requestedQos", "payloadMessage", "duplicate", "qos", "retained", "destinationNameLength", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "buffer", "mbi", "encodeMBI", "pos", "byteStream", "set", "writeString", "connectFlags", "cleanSession", "writeUint16", "keepAliveInterval", "decodeMessage", "input", "startingPos", "messageInfo", "digit", "multiplier", "endPos", "wireMessage", "connectAcknowledgeFlags", "sessionPresent", "returnCode", "len", "readUint16", "topicName", "parseUTF8", "message", "Message", "subarray", "offset", "utf8Length", "stringToUTF8", "number", "output", "numBytes", "charCode", "charCodeAt", "lowCharCode", "isNaN", "utf16", "byte1", "byte2", "toString", "byte3", "byte4", "String", "fromCharCode", "<PERSON><PERSON>", "client", "window", "_client", "_window", "_keepAliveInterval", "isReset", "pingReq", "doTimeout", "pinger", "doPing", "console", "log", "_trace", "_disconnected", "socket", "send", "timeout", "setTimeout", "reset", "clearTimeout", "cancel", "Timeout", "timeoutSeconds", "action", "args", "ClientImpl", "uri", "host", "port", "path", "_local<PERSON>ey", "_msg_queue", "_sentMessages", "_receivedMessages", "_notify_msg_sent", "_message_identifier", "_sequence", "localStorage", "restore", "connected", "maxMessageIdentifier", "connectOptions", "hostIndex", "onConnectionLost", "onMessageDelivered", "onMessageArrived", "_connectTimeout", "send<PERSON><PERSON>", "receive<PERSON>inger", "<PERSON><PERSON><PERSON><PERSON>", "_trace<PERSON><PERSON>er", "_MAX_TRACE_ENTRIES", "connect", "connectOptionsMasked", "_traceMask", "uris", "_doConnect", "subscribe", "filter", "subscribeOptions", "onSuccess", "grantedQos", "invocationContext", "onFailure", "errorCode", "timeOut", "errorMessage", "_requires_ack", "_schedule_message", "unsubscribe", "unsubscribeOptions", "callback", "disconnect", "getTraceLog", "Date", "startTrace", "stopTrace", "wsurl", "useSSL", "uriP<PERSON>s", "split", "join", "WebSocket", "binaryType", "onopen", "_on_socket_open", "onmessage", "_on_socket_message", "onerror", "_on_socket_error", "onclose", "_on_socket_close", "push", "_process_queue", "store", "prefix", "storedMessage", "pubRecReceived", "hex", "messageBytes", "payloadHex", "sequence", "setItem", "JSON", "stringify", "value", "getItem", "parse", "x", "parseInt", "fifo", "reverse", "pop", "_socket_send", "messageCount", "Object", "event", "data", "messages", "_deframeMessages", "_handleMessage", "byteArray", "newData", "result", "sentMessage", "removeItem", "receivedMessage", "sequencedMessages", "msgId", "sort", "a", "b", "pubRelMessage", "_receivePublish", "_receiveMessage", "pubCompMessage", "wireMessageMasked", "pubAckMessage", "pubRecMessage", "wireMmessage", "errorText", "readyState", "close", "mqttVersionExplicit", "max", "shift", "traceObject", "masked", "traceObjectMasked", "attr", "Client", "match", "ipv6AddSBracket", "slice", "clientIdLength", "_getHost", "_setHost", "_getPort", "_setPort", "_getPath", "_setPath", "_getURI", "_setURI", "_getClientId", "_setClientId", "_getOnConnectionLost", "_setOnConnectionLost", "newOnConnectionLost", "_getOnMessageDelivered", "_setOnMessageDelivered", "newOnMessageDelivered", "_getOnMessageArrived", "_setOnMessageArrived", "newOnMessageArrived", "hosts", "ports", "stringPayload", "usingURIs", "test", "ipv6", "isConnected", "newHost", "newPort", "newPath", "newClientId", "newPayload", "payload", "Int8Array", "Int16Array", "Uint16Array", "Int32Array", "Uint32Array", "Float32Array", "Float64Array", "_getPayloadString", "_getPayloadBytes", "_getDestinationName", "_setDestinationName", "newDestinationName", "_getQos", "_setQos", "newQos", "_getRetained", "_setRetained", "newRetained", "_getDuplicate", "_setDuplicate", "newDuplicate", "payloadString"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;;;;;;;;;;;;;;;AAiBA;AACA;AACA;AACA;;AAEA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2DA,IAAIA,IAAI,GAAG,EAAX;;AACA,IAAI,OAAOA,IAAP,KAAgB,WAApB,EAAiC;AAChCA,EAAAA,IAAI,GAAG,EAAP;AACA;;AAEDA,IAAI,CAACC,IAAL,GAAa,UAAUC,MAAV,EAAkB;AAE9B;AACA;AAEA,MAAIC,OAAO,GAAG,WAAd;AACA,MAAIC,UAAU,GAAG,cAAjB;AAEA;;;;;;AAKA,MAAIC,YAAY,GAAG;AAClBC,IAAAA,OAAO,EAAE,CADS;AAElBC,IAAAA,OAAO,EAAE,CAFS;AAGlBC,IAAAA,OAAO,EAAE,CAHS;AAIlBC,IAAAA,MAAM,EAAE,CAJU;AAKlBC,IAAAA,MAAM,EAAE,CALU;AAMlBC,IAAAA,MAAM,EAAE,CANU;AAOlBC,IAAAA,OAAO,EAAE,CAPS;AAQlBC,IAAAA,SAAS,EAAE,CARO;AASlBC,IAAAA,MAAM,EAAE,CATU;AAUlBC,IAAAA,WAAW,EAAE,EAVK;AAWlBC,IAAAA,QAAQ,EAAE,EAXQ;AAYlBC,IAAAA,OAAO,EAAE,EAZS;AAalBC,IAAAA,QAAQ,EAAE,EAbQ;AAclBC,IAAAA,UAAU,EAAE;AAdM,GAAnB,CAb8B,CA8B9B;AACA;;AAEA;;;;;;;;;;;AAUA,MAAIC,QAAQ,GAAG,SAAXA,QAAW,CAAUC,GAAV,EAAeC,IAAf,EAAqB;AACnC,SAAK,IAAIC,GAAT,IAAgBF,GAAhB,EAAqB;AACpB,UAAIA,GAAG,CAACG,cAAJ,CAAmBD,GAAnB,CAAJ,EAA6B;AAC5B,YAAID,IAAI,CAACE,cAAL,CAAoBD,GAApB,CAAJ,EAA8B;AAC7B,cAAI,QAAOF,GAAG,CAACE,GAAD,CAAV,MAAoBD,IAAI,CAACC,GAAD,CAA5B,EACC,MAAM,IAAIE,KAAJ,CAAUC,MAAM,CAACC,KAAK,CAACC,YAAP,EAAqB,SAAQP,GAAG,CAACE,GAAD,CAAX,GAAkBA,GAAlB,CAArB,CAAhB,CAAN;AACD,SAHD,MAGO;AACN,cAAIM,QAAQ,GAAG,uBAAuBN,GAAvB,GAA6B,yBAA5C;;AACA,eAAK,IAAIA,GAAT,IAAgBD,IAAhB;AACC,gBAAIA,IAAI,CAACE,cAAL,CAAoBD,GAApB,CAAJ,EACCM,QAAQ,GAAGA,QAAQ,GAAG,GAAX,GAAiBN,GAA5B;AAFF;;AAGA,gBAAM,IAAIE,KAAJ,CAAUI,QAAV,CAAN;AACA;AACD;AACD;AACD,GAfD;AAiBA;;;;;;;;;;AAQA,MAAIC,KAAK,GAAG,eAAUC,CAAV,EAAaD,MAAb,EAAoB;AAC/B,WAAO,YAAY;AAClB,aAAOC,CAAC,CAACC,KAAF,CAAQF,MAAR,EAAeG,SAAf,CAAP;AACA,KAFD;AAGA,GAJD;AAMA;;;;;;;AAKA,MAAIN,KAAK,GAAG;AACXO,IAAAA,EAAE,EAAE;AAAEC,MAAAA,IAAI,EAAE,CAAR;AAAWC,MAAAA,IAAI,EAAE;AAAjB,KADO;AAEXC,IAAAA,eAAe,EAAE;AAAEF,MAAAA,IAAI,EAAE,CAAR;AAAWC,MAAAA,IAAI,EAAE;AAAjB,KAFN;AAGXE,IAAAA,iBAAiB,EAAE;AAAEH,MAAAA,IAAI,EAAE,CAAR;AAAWC,MAAAA,IAAI,EAAE;AAAjB,KAHR;AAIXG,IAAAA,mBAAmB,EAAE;AAAEJ,MAAAA,IAAI,EAAE,CAAR;AAAWC,MAAAA,IAAI,EAAE;AAAjB,KAJV;AAKXI,IAAAA,YAAY,EAAE;AAAEL,MAAAA,IAAI,EAAE,CAAR;AAAWC,MAAAA,IAAI,EAAE;AAAjB,KALH;AAMXK,IAAAA,cAAc,EAAE;AAAEN,MAAAA,IAAI,EAAE,CAAR;AAAWC,MAAAA,IAAI,EAAE;AAAjB,KANL;AAOXM,IAAAA,kBAAkB,EAAE;AAAEP,MAAAA,IAAI,EAAE,CAAR;AAAWC,MAAAA,IAAI,EAAE;AAAjB,KAPT;AAQXO,IAAAA,YAAY,EAAE;AAAER,MAAAA,IAAI,EAAE,CAAR;AAAWC,MAAAA,IAAI,EAAE;AAAjB,KARH;AASXQ,IAAAA,YAAY,EAAE;AAAET,MAAAA,IAAI,EAAE,CAAR;AAAWC,MAAAA,IAAI,EAAE;AAAjB,KATH;AAUXS,IAAAA,aAAa,EAAE;AAAEV,MAAAA,IAAI,EAAE,CAAR;AAAWC,MAAAA,IAAI,EAAE;AAAjB,KAVJ;AAWXU,IAAAA,WAAW,EAAE;AAAEX,MAAAA,IAAI,EAAE,EAAR;AAAYC,MAAAA,IAAI,EAAE;AAAlB,KAXF;AAYXW,IAAAA,aAAa,EAAE;AAAEZ,MAAAA,IAAI,EAAE,EAAR;AAAYC,MAAAA,IAAI,EAAE;AAAlB,KAZJ;AAaXR,IAAAA,YAAY,EAAE;AAAEO,MAAAA,IAAI,EAAE,EAAR;AAAYC,MAAAA,IAAI,EAAE;AAAlB,KAbH;AAcXY,IAAAA,gBAAgB,EAAE;AAAEb,MAAAA,IAAI,EAAE,EAAR;AAAYC,MAAAA,IAAI,EAAE;AAAlB,KAdP;AAeXa,IAAAA,qBAAqB,EAAE;AAAEd,MAAAA,IAAI,EAAE,EAAR;AAAYC,MAAAA,IAAI,EAAE;AAAlB,KAfZ;AAgBXc,IAAAA,mBAAmB,EAAE;AAAEf,MAAAA,IAAI,EAAE,EAAR;AAAYC,MAAAA,IAAI,EAAE;AAAlB,KAhBV;AAiBXe,IAAAA,yBAAyB,EAAE;AAAEhB,MAAAA,IAAI,EAAE,EAAR;AAAYC,MAAAA,IAAI,EAAE;AAAlB,KAjBhB;AAkBXgB,IAAAA,iBAAiB,EAAE;AAAEjB,MAAAA,IAAI,EAAE,EAAR;AAAYC,MAAAA,IAAI,EAAE;AAAlB;AAlBR,GAAZ;AAqBA;;AACA,MAAIiB,UAAU,GAAG;AAChB,OAAG,qBADa;AAEhB,OAAG,mDAFa;AAGhB,OAAG,yCAHa;AAIhB,OAAG,wCAJa;AAKhB,OAAG,+CALa;AAMhB,OAAG;AANa,GAAjB;AASA;;;;;;;;AAOA,MAAI3B,MAAM,GAAG,SAATA,MAAS,CAAU4B,KAAV,EAAiBC,aAAjB,EAAgC;AAC5C,QAAInB,IAAI,GAAGkB,KAAK,CAAClB,IAAjB;;AACA,QAAImB,aAAJ,EAAmB;AAClB,WAAK,IAAIC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGD,aAAa,CAACE,MAAlC,EAA0CD,CAAC,EAA3C,EAA+C;AAC9C,YAAIE,KAAK,GAAG,MAAMF,CAAN,GAAU,GAAtB;AACA,YAAIG,KAAK,GAAGvB,IAAI,CAACwB,OAAL,CAAaF,KAAb,CAAZ;;AACA,YAAIC,KAAK,GAAG,CAAZ,EAAe;AACd,cAAIE,KAAK,GAAGzB,IAAI,CAAC0B,SAAL,CAAe,CAAf,EAAkBH,KAAlB,CAAZ;AACA,cAAII,KAAK,GAAG3B,IAAI,CAAC0B,SAAL,CAAeH,KAAK,GAAGD,KAAK,CAACD,MAA7B,CAAZ;AACArB,UAAAA,IAAI,GAAGyB,KAAK,GAAGN,aAAa,CAACC,CAAD,CAArB,GAA2BO,KAAlC;AACA;AACD;AACD;;AACD,WAAO3B,IAAP;AACA,GAdD,CArH8B,CAqI9B;;;AACA,MAAI4B,qBAAqB,GAAG,CAAC,IAAD,EAAO,IAAP,EAAa,IAAb,EAAmB,IAAnB,EAAyB,IAAzB,EAA+B,IAA/B,EAAqC,IAArC,EAA2C,IAA3C,EAAiD,IAAjD,CAA5B,CAtI8B,CAuI9B;;AACA,MAAIC,qBAAqB,GAAG,CAAC,IAAD,EAAO,IAAP,EAAa,IAAb,EAAmB,IAAnB,EAAyB,IAAzB,EAA+B,IAA/B,EAAqC,IAArC,CAA5B;AAEA;;;;;;;;;;;;;;;;;;;;;;;;;AAwBA,MAAIC,WAAW,GAAG,SAAdA,WAAc,CAAUC,IAAV,EAAgBC,OAAhB,EAAyB;AAC1C,SAAKD,IAAL,GAAYA,IAAZ;;AACA,SAAK,IAAIE,IAAT,IAAiBD,OAAjB,EAA0B;AACzB,UAAIA,OAAO,CAAC5C,cAAR,CAAuB6C,IAAvB,CAAJ,EAAkC;AACjC,aAAKA,IAAL,IAAaD,OAAO,CAACC,IAAD,CAApB;AACA;AACD;AACD,GAPD;;AASAH,EAAAA,WAAW,CAACI,SAAZ,CAAsBC,MAAtB,GAA+B,YAAY;AAC1C;AACA,QAAIC,KAAK,GAAI,CAAC,KAAKL,IAAL,GAAY,IAAb,KAAsB,CAAnC;AAEA;;;;;AAKA,QAAIM,SAAS,GAAG,CAAhB;AACA,QAAIC,cAAc,GAAG,IAAIC,KAAJ,EAArB,CAV0C,CAY1C;;AACA,QAAI,KAAKC,iBAAL,IAA0BC,SAA9B,EACCJ,SAAS,IAAI,CAAb;;AAED,YAAQ,KAAKN,IAAb;AACC;AACA,WAAK9D,YAAY,CAACC,OAAlB;AACC,gBAAQ,KAAKwE,WAAb;AACC,eAAK,CAAL;AACCL,YAAAA,SAAS,IAAIT,qBAAqB,CAACP,MAAtB,GAA+B,CAA5C;AACA;;AACD,eAAK,CAAL;AACCgB,YAAAA,SAAS,IAAIR,qBAAqB,CAACR,MAAtB,GAA+B,CAA5C;AACA;AANF;;AASAgB,QAAAA,SAAS,IAAIM,UAAU,CAAC,KAAKC,QAAN,CAAV,GAA4B,CAAzC;;AACA,YAAI,KAAKC,WAAL,IAAoBJ,SAAxB,EAAmC;AAClCJ,UAAAA,SAAS,IAAIM,UAAU,CAAC,KAAKE,WAAL,CAAiBC,eAAlB,CAAV,GAA+C,CAA5D,CADkC,CAElC;;AACA,cAAIC,uBAAuB,GAAG,KAAKF,WAAL,CAAiBG,YAA/C;AACA,cAAI,EAAED,uBAAuB,YAAYE,UAArC,CAAJ,EACCF,uBAAuB,GAAG,IAAIE,UAAJ,CAAeD,YAAf,CAA1B;AACDX,UAAAA,SAAS,IAAIU,uBAAuB,CAACG,UAAxB,GAAqC,CAAlD;AACA;;AACD,YAAI,KAAKC,QAAL,IAAiBV,SAArB,EACCJ,SAAS,IAAIM,UAAU,CAAC,KAAKQ,QAAN,CAAV,GAA4B,CAAzC;AACD,YAAI,KAAKC,QAAL,IAAiBX,SAArB,EACCJ,SAAS,IAAIM,UAAU,CAAC,KAAKS,QAAN,CAAV,GAA4B,CAAzC;AACD;AAED;;AACA,WAAKnF,YAAY,CAACQ,SAAlB;AACC2D,QAAAA,KAAK,IAAI,IAAT,CADD,CACgB;;AACf,aAAK,IAAIhB,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAKiC,MAAL,CAAYhC,MAAhC,EAAwCD,CAAC,EAAzC,EAA6C;AAC5CkB,UAAAA,cAAc,CAAClB,CAAD,CAAd,GAAoBuB,UAAU,CAAC,KAAKU,MAAL,CAAYjC,CAAZ,CAAD,CAA9B;AACAiB,UAAAA,SAAS,IAAIC,cAAc,CAAClB,CAAD,CAAd,GAAoB,CAAjC;AACA;;AACDiB,QAAAA,SAAS,IAAI,KAAKiB,YAAL,CAAkBjC,MAA/B,CAND,CAMwC;AACvC;;AACA;;AAED,WAAKpD,YAAY,CAACU,WAAlB;AACCyD,QAAAA,KAAK,IAAI,IAAT,CADD,CACgB;;AACf,aAAK,IAAIhB,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAKiC,MAAL,CAAYhC,MAAhC,EAAwCD,CAAC,EAAzC,EAA6C;AAC5CkB,UAAAA,cAAc,CAAClB,CAAD,CAAd,GAAoBuB,UAAU,CAAC,KAAKU,MAAL,CAAYjC,CAAZ,CAAD,CAA9B;AACAiB,UAAAA,SAAS,IAAIC,cAAc,CAAClB,CAAD,CAAd,GAAoB,CAAjC;AACA;;AACD;;AAED,WAAKnD,YAAY,CAACM,MAAlB;AACC6D,QAAAA,KAAK,IAAI,IAAT,CADD,CACgB;;AACf;;AAED,WAAKnE,YAAY,CAACG,OAAlB;AACC,YAAI,KAAKmF,cAAL,CAAoBC,SAAxB,EAAmCpB,KAAK,IAAI,IAAT;AACnCA,QAAAA,KAAK,GAAGA,KAAK,IAAK,KAAKmB,cAAL,CAAoBE,GAApB,IAA2B,CAA7C;AACA,YAAI,KAAKF,cAAL,CAAoBG,QAAxB,EAAkCtB,KAAK,IAAI,IAAT;AAClC,YAAIuB,qBAAqB,GAAGhB,UAAU,CAAC,KAAKY,cAAL,CAAoBT,eAArB,CAAtC;AACAT,QAAAA,SAAS,IAAIsB,qBAAqB,GAAG,CAArC;AACA,YAAIX,YAAY,GAAG,KAAKO,cAAL,CAAoBP,YAAvC;AACAX,QAAAA,SAAS,IAAIW,YAAY,CAACE,UAA1B;AACA,YAAIF,YAAY,YAAYY,WAA5B,EACCZ,YAAY,GAAG,IAAIC,UAAJ,CAAeD,YAAf,CAAf,CADD,KAEK,IAAI,EAAEA,YAAY,YAAYC,UAA1B,CAAJ,EACJD,YAAY,GAAG,IAAIC,UAAJ,CAAeD,YAAY,CAACa,MAA5B,CAAf;AACD;;AAED,WAAK5F,YAAY,CAACc,UAAlB;AACC;;AAED;AACC;AApEF,KAhB0C,CAuF1C;;;AAEA,QAAI+E,GAAG,GAAGC,SAAS,CAAC1B,SAAD,CAAnB,CAzF0C,CAyFT;;AACjC,QAAI2B,GAAG,GAAGF,GAAG,CAACzC,MAAJ,GAAa,CAAvB,CA1F0C,CA0FT;;AACjC,QAAIwC,MAAM,GAAG,IAAID,WAAJ,CAAgBvB,SAAS,GAAG2B,GAA5B,CAAb;AACA,QAAIC,UAAU,GAAG,IAAIhB,UAAJ,CAAeY,MAAf,CAAjB,CA5F0C,CA4FE;AAE5C;;AACAI,IAAAA,UAAU,CAAC,CAAD,CAAV,GAAgB7B,KAAhB;AACA6B,IAAAA,UAAU,CAACC,GAAX,CAAeJ,GAAf,EAAoB,CAApB,EAhG0C,CAkG1C;;AACA,QAAI,KAAK/B,IAAL,IAAa9D,YAAY,CAACG,OAA9B,EACC4F,GAAG,GAAGG,WAAW,CAAC,KAAKZ,cAAL,CAAoBT,eAArB,EAAsCa,qBAAtC,EAA6DM,UAA7D,EAAyED,GAAzE,CAAjB,CADD,CAEA;AAFA,SAIK,IAAI,KAAKjC,IAAL,IAAa9D,YAAY,CAACC,OAA9B,EAAuC;AAC3C,gBAAQ,KAAKwE,WAAb;AACC,eAAK,CAAL;AACCuB,YAAAA,UAAU,CAACC,GAAX,CAAetC,qBAAf,EAAsCoC,GAAtC;AACAA,YAAAA,GAAG,IAAIpC,qBAAqB,CAACP,MAA7B;AACA;;AACD,eAAK,CAAL;AACC4C,YAAAA,UAAU,CAACC,GAAX,CAAerC,qBAAf,EAAsCmC,GAAtC;AACAA,YAAAA,GAAG,IAAInC,qBAAqB,CAACR,MAA7B;AACA;AARF;;AAUA,YAAI+C,YAAY,GAAG,CAAnB;AACA,YAAI,KAAKC,YAAT,EACCD,YAAY,GAAG,IAAf;;AACD,YAAI,KAAKvB,WAAL,IAAoBJ,SAAxB,EAAmC;AAClC2B,UAAAA,YAAY,IAAI,IAAhB;AACAA,UAAAA,YAAY,IAAK,KAAKvB,WAAL,CAAiBY,GAAjB,IAAwB,CAAzC;;AACA,cAAI,KAAKZ,WAAL,CAAiBa,QAArB,EAA+B;AAC9BU,YAAAA,YAAY,IAAI,IAAhB;AACA;AACD;;AACD,YAAI,KAAKjB,QAAL,IAAiBV,SAArB,EACC2B,YAAY,IAAI,IAAhB;AACD,YAAI,KAAKhB,QAAL,IAAiBX,SAArB,EACC2B,YAAY,IAAI,IAAhB;AACDH,QAAAA,UAAU,CAACD,GAAG,EAAJ,CAAV,GAAoBI,YAApB;AACAJ,QAAAA,GAAG,GAAGM,WAAW,CAAC,KAAKC,iBAAN,EAAyBN,UAAzB,EAAqCD,GAArC,CAAjB;AACA,OAlIyC,CAoI1C;;AACA,QAAI,KAAKxB,iBAAL,IAA0BC,SAA9B,EACCuB,GAAG,GAAGM,WAAW,CAAC,KAAK9B,iBAAN,EAAyByB,UAAzB,EAAqCD,GAArC,CAAjB;;AAED,YAAQ,KAAKjC,IAAb;AACC,WAAK9D,YAAY,CAACC,OAAlB;AACC8F,QAAAA,GAAG,GAAGG,WAAW,CAAC,KAAKvB,QAAN,EAAgBD,UAAU,CAAC,KAAKC,QAAN,CAA1B,EAA2CqB,UAA3C,EAAuDD,GAAvD,CAAjB;;AACA,YAAI,KAAKnB,WAAL,IAAoBJ,SAAxB,EAAmC;AAClCuB,UAAAA,GAAG,GAAGG,WAAW,CAAC,KAAKtB,WAAL,CAAiBC,eAAlB,EAAmCH,UAAU,CAAC,KAAKE,WAAL,CAAiBC,eAAlB,CAA7C,EAAiFmB,UAAjF,EAA6FD,GAA7F,CAAjB;AACAA,UAAAA,GAAG,GAAGM,WAAW,CAACvB,uBAAuB,CAACG,UAAzB,EAAqCe,UAArC,EAAiDD,GAAjD,CAAjB;AACAC,UAAAA,UAAU,CAACC,GAAX,CAAenB,uBAAf,EAAwCiB,GAAxC;AACAA,UAAAA,GAAG,IAAIjB,uBAAuB,CAACG,UAA/B;AAEA;;AACD,YAAI,KAAKC,QAAL,IAAiBV,SAArB,EACCuB,GAAG,GAAGG,WAAW,CAAC,KAAKhB,QAAN,EAAgBR,UAAU,CAAC,KAAKQ,QAAN,CAA1B,EAA2Cc,UAA3C,EAAuDD,GAAvD,CAAjB;AACD,YAAI,KAAKZ,QAAL,IAAiBX,SAArB,EACCuB,GAAG,GAAGG,WAAW,CAAC,KAAKf,QAAN,EAAgBT,UAAU,CAAC,KAAKS,QAAN,CAA1B,EAA2Ca,UAA3C,EAAuDD,GAAvD,CAAjB;AACD;;AAED,WAAK/F,YAAY,CAACG,OAAlB;AACC;AACA6F,QAAAA,UAAU,CAACC,GAAX,CAAelB,YAAf,EAA6BgB,GAA7B;AAEA;AAED;AACA;AACA;AACA;;AAEA,WAAK/F,YAAY,CAACQ,SAAlB;AACC;AACA,aAAK,IAAI2C,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAKiC,MAAL,CAAYhC,MAAhC,EAAwCD,CAAC,EAAzC,EAA6C;AAC5C4C,UAAAA,GAAG,GAAGG,WAAW,CAAC,KAAKd,MAAL,CAAYjC,CAAZ,CAAD,EAAiBkB,cAAc,CAAClB,CAAD,CAA/B,EAAoC6C,UAApC,EAAgDD,GAAhD,CAAjB;AACAC,UAAAA,UAAU,CAACD,GAAG,EAAJ,CAAV,GAAoB,KAAKV,YAAL,CAAkBlC,CAAlB,CAApB;AACA;;AACD;;AAED,WAAKnD,YAAY,CAACU,WAAlB;AACC;AACA,aAAK,IAAIyC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAKiC,MAAL,CAAYhC,MAAhC,EAAwCD,CAAC,EAAzC;AACC4C,UAAAA,GAAG,GAAGG,WAAW,CAAC,KAAKd,MAAL,CAAYjC,CAAZ,CAAD,EAAiBkB,cAAc,CAAClB,CAAD,CAA/B,EAAoC6C,UAApC,EAAgDD,GAAhD,CAAjB;AADD;;AAEA;;AAED,cAzCD,CA0CC;;AA1CD;;AA6CA,WAAOH,MAAP;AACA,GAtLD;;AAwLA,WAASW,aAAT,CAAuBC,KAAvB,EAA8BT,GAA9B,EAAmC;AAClC,QAAIU,WAAW,GAAGV,GAAlB;AACA,QAAI5B,KAAK,GAAGqC,KAAK,CAACT,GAAD,CAAjB;AACA,QAAIjC,IAAI,GAAGK,KAAK,IAAI,CAApB;AACA,QAAIuC,WAAW,GAAGvC,KAAK,IAAI,IAA3B;AACA4B,IAAAA,GAAG,IAAI,CAAP,CALkC,CAQlC;;AAEA,QAAIY,KAAJ;AACA,QAAIvC,SAAS,GAAG,CAAhB;AACA,QAAIwC,UAAU,GAAG,CAAjB;;AACA,OAAG;AACF,UAAIb,GAAG,IAAIS,KAAK,CAACpD,MAAjB,EAAyB;AACxB,eAAO,CAAC,IAAD,EAAOqD,WAAP,CAAP;AACA;;AACDE,MAAAA,KAAK,GAAGH,KAAK,CAACT,GAAG,EAAJ,CAAb;AACA3B,MAAAA,SAAS,IAAK,CAACuC,KAAK,GAAG,IAAT,IAAiBC,UAA/B;AACAA,MAAAA,UAAU,IAAI,GAAd;AACA,KAPD,QAOS,CAACD,KAAK,GAAG,IAAT,KAAkB,CAP3B;;AASA,QAAIE,MAAM,GAAGd,GAAG,GAAG3B,SAAnB;;AACA,QAAIyC,MAAM,GAAGL,KAAK,CAACpD,MAAnB,EAA2B;AAC1B,aAAO,CAAC,IAAD,EAAOqD,WAAP,CAAP;AACA;;AAED,QAAIK,WAAW,GAAG,IAAIjD,WAAJ,CAAgBC,IAAhB,CAAlB;;AACA,YAAQA,IAAR;AACC,WAAK9D,YAAY,CAACE,OAAlB;AACC,YAAI6G,uBAAuB,GAAGP,KAAK,CAACT,GAAG,EAAJ,CAAnC;AACA,YAAIgB,uBAAuB,GAAG,IAA9B,EACCD,WAAW,CAACE,cAAZ,GAA6B,IAA7B;AACDF,QAAAA,WAAW,CAACG,UAAZ,GAAyBT,KAAK,CAACT,GAAG,EAAJ,CAA9B;AACA;;AAED,WAAK/F,YAAY,CAACG,OAAlB;AACC,YAAIqF,GAAG,GAAIkB,WAAW,IAAI,CAAhB,GAAqB,IAA/B;AAEA,YAAIQ,GAAG,GAAGC,UAAU,CAACX,KAAD,EAAQT,GAAR,CAApB;AACAA,QAAAA,GAAG,IAAI,CAAP;AACA,YAAIqB,SAAS,GAAGC,SAAS,CAACb,KAAD,EAAQT,GAAR,EAAamB,GAAb,CAAzB;AACAnB,QAAAA,GAAG,IAAImB,GAAP,CAND,CAOC;;AACA,YAAI1B,GAAG,GAAG,CAAV,EAAa;AACZsB,UAAAA,WAAW,CAACvC,iBAAZ,GAAgC4C,UAAU,CAACX,KAAD,EAAQT,GAAR,CAA1C;AACAA,UAAAA,GAAG,IAAI,CAAP;AACA;;AAED,YAAIuB,OAAO,GAAG,IAAI3H,IAAI,CAACC,IAAL,CAAU2H,OAAd,CAAsBf,KAAK,CAACgB,QAAN,CAAezB,GAAf,EAAoBc,MAApB,CAAtB,CAAd;AACA,YAAI,CAACH,WAAW,GAAG,IAAf,KAAwB,IAA5B,EACCY,OAAO,CAAC7B,QAAR,GAAmB,IAAnB;AACD,YAAI,CAACiB,WAAW,GAAG,IAAf,KAAwB,IAA5B,EACCY,OAAO,CAAC/B,SAAR,GAAoB,IAApB;AACD+B,QAAAA,OAAO,CAAC9B,GAAR,GAAcA,GAAd;AACA8B,QAAAA,OAAO,CAACzC,eAAR,GAA0BuC,SAA1B;AACAN,QAAAA,WAAW,CAACxB,cAAZ,GAA6BgC,OAA7B;AACA;;AAED,WAAKtH,YAAY,CAACI,MAAlB;AACA,WAAKJ,YAAY,CAACK,MAAlB;AACA,WAAKL,YAAY,CAACM,MAAlB;AACA,WAAKN,YAAY,CAACO,OAAlB;AACA,WAAKP,YAAY,CAACW,QAAlB;AACCmG,QAAAA,WAAW,CAACvC,iBAAZ,GAAgC4C,UAAU,CAACX,KAAD,EAAQT,GAAR,CAA1C;AACA;;AAED,WAAK/F,YAAY,CAACS,MAAlB;AACCqG,QAAAA,WAAW,CAACvC,iBAAZ,GAAgC4C,UAAU,CAACX,KAAD,EAAQT,GAAR,CAA1C;AACAA,QAAAA,GAAG,IAAI,CAAP;AACAe,QAAAA,WAAW,CAACG,UAAZ,GAAyBT,KAAK,CAACgB,QAAN,CAAezB,GAAf,EAAoBc,MAApB,CAAzB;AACA;;AAED;AACC;AA9CF;;AAiDA,WAAO,CAACC,WAAD,EAAcD,MAAd,CAAP;AACA;;AAED,WAASR,WAAT,CAAqBG,KAArB,EAA4BZ,MAA5B,EAAoC6B,MAApC,EAA4C;AAC3C7B,IAAAA,MAAM,CAAC6B,MAAM,EAAP,CAAN,GAAmBjB,KAAK,IAAI,CAA5B,CAD2C,CACP;;AACpCZ,IAAAA,MAAM,CAAC6B,MAAM,EAAP,CAAN,GAAmBjB,KAAK,GAAG,GAA3B,CAF2C,CAEP;;AACpC,WAAOiB,MAAP;AACA;;AAED,WAASvB,WAAT,CAAqBM,KAArB,EAA4BkB,UAA5B,EAAwC9B,MAAxC,EAAgD6B,MAAhD,EAAwD;AACvDA,IAAAA,MAAM,GAAGpB,WAAW,CAACqB,UAAD,EAAa9B,MAAb,EAAqB6B,MAArB,CAApB;AACAE,IAAAA,YAAY,CAACnB,KAAD,EAAQZ,MAAR,EAAgB6B,MAAhB,CAAZ;AACA,WAAOA,MAAM,GAAGC,UAAhB;AACA;;AAED,WAASP,UAAT,CAAoBvB,MAApB,EAA4B6B,MAA5B,EAAoC;AACnC,WAAO,MAAM7B,MAAM,CAAC6B,MAAD,CAAZ,GAAuB7B,MAAM,CAAC6B,MAAM,GAAG,CAAV,CAApC;AACA;AAED;;;;;;AAIA,WAAS3B,SAAT,CAAmB8B,MAAnB,EAA2B;AAC1B,QAAIC,MAAM,GAAG,IAAIvD,KAAJ,CAAU,CAAV,CAAb;AACA,QAAIwD,QAAQ,GAAG,CAAf;;AAEA,OAAG;AACF,UAAInB,KAAK,GAAGiB,MAAM,GAAG,GAArB;AACAA,MAAAA,MAAM,GAAGA,MAAM,IAAI,CAAnB;;AACA,UAAIA,MAAM,GAAG,CAAb,EAAgB;AACfjB,QAAAA,KAAK,IAAI,IAAT;AACA;;AACDkB,MAAAA,MAAM,CAACC,QAAQ,EAAT,CAAN,GAAqBnB,KAArB;AACA,KAPD,QAOUiB,MAAM,GAAG,CAAV,IAAiBE,QAAQ,GAAG,CAPrC;;AASA,WAAOD,MAAP;AACA;AAED;;;;;;AAIA,WAASnD,UAAT,CAAoB8B,KAApB,EAA2B;AAC1B,QAAIqB,MAAM,GAAG,CAAb;;AACA,SAAK,IAAI1E,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGqD,KAAK,CAACpD,MAA1B,EAAkCD,CAAC,EAAnC,EAAuC;AACtC,UAAI4E,QAAQ,GAAGvB,KAAK,CAACwB,UAAN,CAAiB7E,CAAjB,CAAf;;AACA,UAAI4E,QAAQ,GAAG,KAAf,EAAsB;AACrB;AACA,YAAI,UAAUA,QAAV,IAAsBA,QAAQ,IAAI,MAAtC,EAA8C;AAC7C5E,UAAAA,CAAC;AACD0E,UAAAA,MAAM;AACN;;AACDA,QAAAA,MAAM,IAAI,CAAV;AACA,OAPD,MAQK,IAAIE,QAAQ,GAAG,IAAf,EACJF,MAAM,IAAI,CAAV,CADI,KAGJA,MAAM;AACP;;AACD,WAAOA,MAAP;AACA;AAED;;;;;;AAIA,WAASF,YAAT,CAAsBnB,KAAtB,EAA6BqB,MAA7B,EAAqCvE,KAArC,EAA4C;AAC3C,QAAIyC,GAAG,GAAGzC,KAAV;;AACA,SAAK,IAAIH,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGqD,KAAK,CAACpD,MAA1B,EAAkCD,CAAC,EAAnC,EAAuC;AACtC,UAAI4E,QAAQ,GAAGvB,KAAK,CAACwB,UAAN,CAAiB7E,CAAjB,CAAf,CADsC,CAGtC;;AACA,UAAI,UAAU4E,QAAV,IAAsBA,QAAQ,IAAI,MAAtC,EAA8C;AAC7CE,QAAAA,WAAW,GAAGzB,KAAK,CAACwB,UAAN,CAAiB,EAAE7E,CAAnB,CAAd;;AACA,YAAI+E,KAAK,CAACD,WAAD,CAAT,EAAwB;AACvB,gBAAM,IAAI7G,KAAJ,CAAUC,MAAM,CAACC,KAAK,CAACyB,iBAAP,EAA0B,CAACgF,QAAD,EAAWE,WAAX,CAA1B,CAAhB,CAAN;AACA;;AACDF,QAAAA,QAAQ,GAAG,CAAEA,QAAQ,GAAG,MAAZ,IAAuB,EAAxB,KAA+BE,WAAW,GAAG,MAA7C,IAAuD,OAAlE;AAEA;;AAED,UAAIF,QAAQ,IAAI,IAAhB,EAAsB;AACrBF,QAAAA,MAAM,CAAC9B,GAAG,EAAJ,CAAN,GAAgBgC,QAAhB;AACA,OAFD,MAEO,IAAIA,QAAQ,IAAI,KAAhB,EAAuB;AAC7BF,QAAAA,MAAM,CAAC9B,GAAG,EAAJ,CAAN,GAAgBgC,QAAQ,IAAI,CAAZ,GAAgB,IAAhB,GAAuB,IAAvC;AACAF,QAAAA,MAAM,CAAC9B,GAAG,EAAJ,CAAN,GAAgBgC,QAAQ,GAAG,IAAX,GAAkB,IAAlC;AACA,OAHM,MAGA,IAAIA,QAAQ,IAAI,MAAhB,EAAwB;AAC9BF,QAAAA,MAAM,CAAC9B,GAAG,EAAJ,CAAN,GAAgBgC,QAAQ,IAAI,EAAZ,GAAiB,IAAjB,GAAwB,IAAxC;AACAF,QAAAA,MAAM,CAAC9B,GAAG,EAAJ,CAAN,GAAgBgC,QAAQ,IAAI,CAAZ,GAAgB,IAAhB,GAAuB,IAAvC;AACAF,QAAAA,MAAM,CAAC9B,GAAG,EAAJ,CAAN,GAAgBgC,QAAQ,GAAG,IAAX,GAAkB,IAAlC;AACA,OAJM,MAIA;AACNF,QAAAA,MAAM,CAAC9B,GAAG,EAAJ,CAAN,GAAgBgC,QAAQ,IAAI,EAAZ,GAAiB,IAAjB,GAAwB,IAAxC;AACAF,QAAAA,MAAM,CAAC9B,GAAG,EAAJ,CAAN,GAAgBgC,QAAQ,IAAI,EAAZ,GAAiB,IAAjB,GAAwB,IAAxC;AACAF,QAAAA,MAAM,CAAC9B,GAAG,EAAJ,CAAN,GAAgBgC,QAAQ,IAAI,CAAZ,GAAgB,IAAhB,GAAuB,IAAvC;AACAF,QAAAA,MAAM,CAAC9B,GAAG,EAAJ,CAAN,GAAgBgC,QAAQ,GAAG,IAAX,GAAkB,IAAlC;AACA;;AAAA;AACD;;AACD,WAAOF,MAAP;AACA;;AAED,WAASR,SAAT,CAAmBb,KAAnB,EAA0BiB,MAA1B,EAAkCrE,MAAlC,EAA0C;AACzC,QAAIyE,MAAM,GAAG,EAAb;AACA,QAAIM,KAAJ;AACA,QAAIpC,GAAG,GAAG0B,MAAV;;AAEA,WAAO1B,GAAG,GAAG0B,MAAM,GAAGrE,MAAtB,EAA8B;AAC7B,UAAIgF,KAAK,GAAG5B,KAAK,CAACT,GAAG,EAAJ,CAAjB;AACA,UAAIqC,KAAK,GAAG,GAAZ,EACCD,KAAK,GAAGC,KAAR,CADD,KAEK;AACJ,YAAIC,KAAK,GAAG7B,KAAK,CAACT,GAAG,EAAJ,CAAL,GAAe,GAA3B;AACA,YAAIsC,KAAK,GAAG,CAAZ,EACC,MAAM,IAAIjH,KAAJ,CAAUC,MAAM,CAACC,KAAK,CAACkB,aAAP,EAAsB,CAAC4F,KAAK,CAACE,QAAN,CAAe,EAAf,CAAD,EAAqBD,KAAK,CAACC,QAAN,CAAe,EAAf,CAArB,EAAyC,EAAzC,CAAtB,CAAhB,CAAN;AACD,YAAIF,KAAK,GAAG,IAAZ,EAA8B;AAC7BD,UAAAA,KAAK,GAAG,MAAMC,KAAK,GAAG,IAAd,IAAsBC,KAA9B,CADD,KAEK;AACJ,cAAIE,KAAK,GAAG/B,KAAK,CAACT,GAAG,EAAJ,CAAL,GAAe,GAA3B;AACA,cAAIwC,KAAK,GAAG,CAAZ,EACC,MAAM,IAAInH,KAAJ,CAAUC,MAAM,CAACC,KAAK,CAACkB,aAAP,EAAsB,CAAC4F,KAAK,CAACE,QAAN,CAAe,EAAf,CAAD,EAAqBD,KAAK,CAACC,QAAN,CAAe,EAAf,CAArB,EAAyCC,KAAK,CAACD,QAAN,CAAe,EAAf,CAAzC,CAAtB,CAAhB,CAAN;AACD,cAAIF,KAAK,GAAG,IAAZ,EAAyB;AACxBD,YAAAA,KAAK,GAAG,QAAQC,KAAK,GAAG,IAAhB,IAAwB,KAAKC,KAA7B,GAAqCE,KAA7C,CADD,KAEK;AACJ,gBAAIC,KAAK,GAAGhC,KAAK,CAACT,GAAG,EAAJ,CAAL,GAAe,GAA3B;AACA,gBAAIyC,KAAK,GAAG,CAAZ,EACC,MAAM,IAAIpH,KAAJ,CAAUC,MAAM,CAACC,KAAK,CAACkB,aAAP,EAAsB,CAAC4F,KAAK,CAACE,QAAN,CAAe,EAAf,CAAD,EAAqBD,KAAK,CAACC,QAAN,CAAe,EAAf,CAArB,EAAyCC,KAAK,CAACD,QAAN,CAAe,EAAf,CAAzC,EAA6DE,KAAK,CAACF,QAAN,CAAe,EAAf,CAA7D,CAAtB,CAAhB,CAAN;AACD,gBAAIF,KAAK,GAAG,IAAZ,EAAyB;AACxBD,cAAAA,KAAK,GAAG,UAAUC,KAAK,GAAG,IAAlB,IAA0B,OAAOC,KAAjC,GAAyC,KAAKE,KAA9C,GAAsDC,KAA9D,CADD,KAEyB;AACxB,oBAAM,IAAIpH,KAAJ,CAAUC,MAAM,CAACC,KAAK,CAACkB,aAAP,EAAsB,CAAC4F,KAAK,CAACE,QAAN,CAAe,EAAf,CAAD,EAAqBD,KAAK,CAACC,QAAN,CAAe,EAAf,CAArB,EAAyCC,KAAK,CAACD,QAAN,CAAe,EAAf,CAAzC,EAA6DE,KAAK,CAACF,QAAN,CAAe,EAAf,CAA7D,CAAtB,CAAhB,CAAN;AACD;AACD;AACD;;AAED,UAAIH,KAAK,GAAG,MAAZ,EAAsB;AACtB;AACCA,UAAAA,KAAK,IAAI,OAAT;AACAN,UAAAA,MAAM,IAAIY,MAAM,CAACC,YAAP,CAAoB,UAAUP,KAAK,IAAI,EAAnB,CAApB,CAAV,CAFD,CAEwD;;AACvDA,UAAAA,KAAK,GAAG,UAAUA,KAAK,GAAG,KAAlB,CAAR,CAHD,CAGoC;AACnC;;AACDN,MAAAA,MAAM,IAAIY,MAAM,CAACC,YAAP,CAAoBP,KAApB,CAAV;AACA;;AACD,WAAON,MAAP;AACA;AAED;;;;;;AAIA,MAAIc,MAAM,GAAG,SAATA,MAAS,CAAUC,MAAV,EAAkBC,MAAlB,EAA0BvC,iBAA1B,EAA6C;AACzD,SAAKwC,OAAL,GAAeF,MAAf;AACA,SAAKG,OAAL,GAAeF,MAAf;AACA,SAAKG,kBAAL,GAA0B1C,iBAAiB,GAAG,IAA9C;AACA,SAAK2C,OAAL,GAAe,KAAf;AAEA,QAAIC,OAAO,GAAG,IAAIrF,WAAJ,CAAgB7D,YAAY,CAACY,OAA7B,EAAsCsD,MAAtC,EAAd;;AAEA,QAAIiF,SAAS,GAAG,SAAZA,SAAY,CAAUC,MAAV,EAAkB;AACjC,aAAO,YAAY;AAClB,eAAOC,MAAM,CAAC1H,KAAP,CAAayH,MAAb,CAAP;AACA,OAFD;AAGA,KAJD;AAMA;;;AACA,QAAIC,MAAM,GAAG,SAATA,MAAS,GAAY;AACxBC,MAAAA,OAAO,CAACC,GAAR,CAAY,kCAAZ;;AACA,UAAI,CAAC,KAAKN,OAAV,EAAmB;AAClB,aAAKH,OAAL,CAAaU,MAAb,CAAoB,eAApB,EAAqC,WAArC;;AACA,aAAKV,OAAL,CAAaW,aAAb,CAA2BnI,KAAK,CAACa,YAAN,CAAmBL,IAA9C,EAAoDT,MAAM,CAACC,KAAK,CAACa,YAAP,CAA1D;AACA,OAHD,MAGO;AACN,aAAK8G,OAAL,GAAe,KAAf;;AACA,aAAKH,OAAL,CAAaU,MAAb,CAAoB,eAApB,EAAqC,cAArC;;AACA,aAAKV,OAAL,CAAaY,MAAb,CAAoBC,IAApB,CAAyBT,OAAzB;;AACA,aAAKU,OAAL,GAAe,KAAKb,OAAL,CAAac,UAAb,CAAwBV,SAAS,CAAC,IAAD,CAAjC,EAAyC,KAAKH,kBAA9C,CAAf;AACA;AACD,KAXD;;AAaA,SAAKc,KAAL,GAAa,YAAY;AACxB,WAAKb,OAAL,GAAe,IAAf;;AACA,WAAKF,OAAL,CAAagB,YAAb,CAA0B,KAAKH,OAA/B;;AACA,UAAI,KAAKZ,kBAAL,GAA0B,CAA9B,EACC,KAAKY,OAAL,GAAeC,UAAU,CAACV,SAAS,CAAC,IAAD,CAAV,EAAkB,KAAKH,kBAAvB,CAAzB;AACD,KALD;;AAOA,SAAKgB,MAAL,GAAc,YAAY;AACzB,WAAKjB,OAAL,CAAagB,YAAb,CAA0B,KAAKH,OAA/B;AACA,KAFD;AAGA,GAtCD;AAwCA;;;;;;AAIA,MAAIK,OAAO,GAAG,SAAVA,OAAU,CAAUrB,MAAV,EAAkBC,MAAlB,EAA0BqB,cAA1B,EAA0CC,MAA1C,EAAkDC,IAAlD,EAAwD;AACrE,SAAKrB,OAAL,GAAeF,MAAf;AACA,QAAI,CAACqB,cAAL,EACCA,cAAc,GAAG,EAAjB;;AAED,QAAIf,SAAS,GAAG,SAAZA,SAAY,CAAUgB,MAAV,EAAkBvB,MAAlB,EAA0BwB,IAA1B,EAAgC;AAC/C,aAAO,YAAY;AAClB,eAAOD,MAAM,CAACxI,KAAP,CAAaiH,MAAb,EAAqBwB,IAArB,CAAP;AACA,OAFD;AAGA,KAJD;;AAKA,SAAKR,OAAL,GAAeC,UAAU,CAACV,SAAS,CAACgB,MAAD,EAASvB,MAAT,EAAiBwB,IAAjB,CAAV,EAAkCF,cAAc,GAAG,IAAnD,CAAzB;;AAEA,SAAKF,MAAL,GAAc,YAAY;AACzB,WAAKjB,OAAL,CAAagB,YAAb,CAA0B,KAAKH,OAA/B;AACA,KAFD;AAGA,GAfD;AAiBA;;;;;;;;;;AAQA,MAAIS,UAAU,GAAG,SAAbA,UAAa,CAAUC,GAAV,EAAeC,IAAf,EAAqBC,IAArB,EAA2BC,IAA3B,EAAiC9F,QAAjC,EAA2C;AAC3D;AACA,QAAI,EAAE,eAAe9E,MAAf,IAAyBA,MAAM,CAAC,WAAD,CAAN,KAAwB,IAAnD,CAAJ,EAA8D;AAC7D,YAAM,IAAIuB,KAAJ,CAAUC,MAAM,CAACC,KAAK,CAACmB,WAAP,EAAoB,CAAC,WAAD,CAApB,CAAhB,CAAN;AACA;;AACD,QAAI,EAAE,kBAAkB5C,MAAlB,IAA4BA,MAAM,CAAC,cAAD,CAAN,KAA2B,IAAzD,CAAJ,EAAoE;AACnE,YAAM,IAAIuB,KAAJ,CAAUC,MAAM,CAACC,KAAK,CAACmB,WAAP,EAAoB,CAAC,cAAD,CAApB,CAAhB,CAAN;AACA;;AACD,QAAI,EAAE,iBAAiB5C,MAAjB,IAA2BA,MAAM,CAAC,aAAD,CAAN,KAA0B,IAAvD,CAAJ,EAAkE;AACjE,YAAM,IAAIuB,KAAJ,CAAUC,MAAM,CAACC,KAAK,CAACmB,WAAP,EAAoB,CAAC,aAAD,CAApB,CAAhB,CAAN;AACA;;AACD,SAAK+G,MAAL,CAAY,kBAAZ,EAAgCc,GAAhC,EAAqCC,IAArC,EAA2CC,IAA3C,EAAiDC,IAAjD,EAAuD9F,QAAvD;;AAEA,SAAK4F,IAAL,GAAYA,IAAZ;AACA,SAAKC,IAAL,GAAYA,IAAZ;AACA,SAAKC,IAAL,GAAYA,IAAZ;AACA,SAAKH,GAAL,GAAWA,GAAX;AACA,SAAK3F,QAAL,GAAgBA,QAAhB,CAjB2D,CAmB3D;AACA;AACA;AACA;;AACA,SAAK+F,SAAL,GAAiBH,IAAI,GAAG,GAAP,GAAaC,IAAb,IAAqBC,IAAI,IAAI,OAAR,GAAkB,MAAMA,IAAxB,GAA+B,EAApD,IAA0D,GAA1D,GAAgE9F,QAAhE,GAA2E,GAA5F,CAvB2D,CAyB3D;AACA;;AACA,SAAKgG,UAAL,GAAkB,EAAlB,CA3B2D,CA6B3D;;AACA,SAAKC,aAAL,GAAqB,EAArB,CA9B2D,CAgC3D;AACA;;AACA,SAAKC,iBAAL,GAAyB,EAAzB,CAlC2D,CAoC3D;AACA;AACA;;AACA,SAAKC,gBAAL,GAAwB,EAAxB,CAvC2D,CAyC3D;AACA;;AACA,SAAKC,mBAAL,GAA2B,CAA3B,CA3C2D,CA6C3D;;AACA,SAAKC,SAAL,GAAiB,CAAjB,CA9C2D,CAiD3D;;AACA,SAAK,IAAI9J,GAAT,IAAgB+J,YAAhB;AACC,UAAI/J,GAAG,CAACqC,OAAJ,CAAY,UAAU,KAAKmH,SAA3B,KAAyC,CAAzC,IACAxJ,GAAG,CAACqC,OAAJ,CAAY,cAAc,KAAKmH,SAA/B,KAA6C,CADjD,EAEC,KAAKQ,OAAL,CAAahK,GAAb;AAHF;AAIA,GAtDD,CA1oB8B,CAksB9B;;;AACAmJ,EAAAA,UAAU,CAACpG,SAAX,CAAqBsG,IAArB;AACAF,EAAAA,UAAU,CAACpG,SAAX,CAAqBuG,IAArB;AACAH,EAAAA,UAAU,CAACpG,SAAX,CAAqBwG,IAArB;AACAJ,EAAAA,UAAU,CAACpG,SAAX,CAAqBqG,GAArB;AACAD,EAAAA,UAAU,CAACpG,SAAX,CAAqBU,QAArB,CAvsB8B,CAysB9B;;AACA0F,EAAAA,UAAU,CAACpG,SAAX,CAAqByF,MAArB;AACA;;AACAW,EAAAA,UAAU,CAACpG,SAAX,CAAqBkH,SAArB,GAAiC,KAAjC;AACA;;;;AAGAd,EAAAA,UAAU,CAACpG,SAAX,CAAqBmH,oBAArB,GAA4C,KAA5C;AACAf,EAAAA,UAAU,CAACpG,SAAX,CAAqBoH,cAArB;AACAhB,EAAAA,UAAU,CAACpG,SAAX,CAAqBqH,SAArB;AACAjB,EAAAA,UAAU,CAACpG,SAAX,CAAqBsH,gBAArB;AACAlB,EAAAA,UAAU,CAACpG,SAAX,CAAqBuH,kBAArB;AACAnB,EAAAA,UAAU,CAACpG,SAAX,CAAqBwH,gBAArB;AACApB,EAAAA,UAAU,CAACpG,SAAX,CAAqB0G,UAArB,GAAkC,IAAlC;AACAN,EAAAA,UAAU,CAACpG,SAAX,CAAqByH,eAArB;AACA;;AACArB,EAAAA,UAAU,CAACpG,SAAX,CAAqB0H,UAArB,GAAkC,IAAlC;AACA;;AACAtB,EAAAA,UAAU,CAACpG,SAAX,CAAqB2H,aAArB,GAAqC,IAArC;AAEAvB,EAAAA,UAAU,CAACpG,SAAX,CAAqB4H,aAArB,GAAqC,IAArC;AAEAxB,EAAAA,UAAU,CAACpG,SAAX,CAAqB6H,YAArB,GAAoC,IAApC;AACAzB,EAAAA,UAAU,CAACpG,SAAX,CAAqB8H,kBAArB,GAA0C,GAA1C;;AAEA1B,EAAAA,UAAU,CAACpG,SAAX,CAAqB+H,OAArB,GAA+B,UAAUX,cAAV,EAA0B;AACxD,QAAIY,oBAAoB,GAAG,KAAKC,UAAL,CAAgBb,cAAhB,EAAgC,UAAhC,CAA3B;;AACA,SAAK7B,MAAL,CAAY,gBAAZ,EAA8ByC,oBAA9B,EAAoD,KAAKvC,MAAzD,EAAiE,KAAKyB,SAAtE;;AAEA,QAAI,KAAKA,SAAT,EACC,MAAM,IAAI/J,KAAJ,CAAUC,MAAM,CAACC,KAAK,CAACoB,aAAP,EAAsB,CAAC,mBAAD,CAAtB,CAAhB,CAAN;AACD,QAAI,KAAKgH,MAAT,EACC,MAAM,IAAItI,KAAJ,CAAUC,MAAM,CAACC,KAAK,CAACoB,aAAP,EAAsB,CAAC,mBAAD,CAAtB,CAAhB,CAAN;AAED,SAAK2I,cAAL,GAAsBA,cAAtB;;AAEA,QAAIA,cAAc,CAACc,IAAnB,EAAyB;AACxB,WAAKb,SAAL,GAAiB,CAAjB;;AACA,WAAKc,UAAL,CAAgBf,cAAc,CAACc,IAAf,CAAoB,CAApB,CAAhB;AACA,KAHD,MAGO;AACN,WAAKC,UAAL,CAAgB,KAAK9B,GAArB;AACA;AAED,GAlBD;;AAoBAD,EAAAA,UAAU,CAACpG,SAAX,CAAqBoI,SAArB,GAAiC,UAAUC,MAAV,EAAkBC,gBAAlB,EAAoC;AACpE,SAAK/C,MAAL,CAAY,kBAAZ,EAAgC8C,MAAhC,EAAwCC,gBAAxC;;AAEA,QAAI,CAAC,KAAKpB,SAAV,EACC,MAAM,IAAI/J,KAAJ,CAAUC,MAAM,CAACC,KAAK,CAACoB,aAAP,EAAsB,CAAC,eAAD,CAAtB,CAAhB,CAAN;AAED,QAAIoE,WAAW,GAAG,IAAIjD,WAAJ,CAAgB7D,YAAY,CAACQ,SAA7B,CAAlB;AACAsG,IAAAA,WAAW,CAAC1B,MAAZ,GAAqB,CAACkH,MAAD,CAArB;AACA,QAAIC,gBAAgB,CAAC/G,GAAjB,IAAwBhB,SAA5B,EACCsC,WAAW,CAACzB,YAAZ,GAA2B,CAACkH,gBAAgB,CAAC/G,GAAlB,CAA3B,CADD,KAGCsB,WAAW,CAACzB,YAAZ,GAA2B,CAAC,CAAD,CAA3B;;AAED,QAAIkH,gBAAgB,CAACC,SAArB,EAAgC;AAC/B1F,MAAAA,WAAW,CAAC0F,SAAZ,GAAwB,UAAUC,UAAV,EAAsB;AAAEF,QAAAA,gBAAgB,CAACC,SAAjB,CAA2B;AAAEE,UAAAA,iBAAiB,EAAEH,gBAAgB,CAACG,iBAAtC;AAAyDD,UAAAA,UAAU,EAAEA;AAArE,SAA3B;AAAgH,OAAhK;AACA;;AAED,QAAIF,gBAAgB,CAACI,SAArB,EAAgC;AAC/B7F,MAAAA,WAAW,CAAC6F,SAAZ,GAAwB,UAAUC,SAAV,EAAqB;AAAEL,QAAAA,gBAAgB,CAACI,SAAjB,CAA2B;AAAED,UAAAA,iBAAiB,EAAEH,gBAAgB,CAACG,iBAAtC;AAAyDE,UAAAA,SAAS,EAAEA;AAApE,SAA3B;AAA8G,OAA7J;AACA;;AAED,QAAIL,gBAAgB,CAAC3C,OAArB,EAA8B;AAC7B9C,MAAAA,WAAW,CAAC+F,OAAZ,GAAsB,IAAI5C,OAAJ,CAAY,IAAZ,EAAkBpB,MAAlB,EAA0B0D,gBAAgB,CAAC3C,OAA3C,EAAoD2C,gBAAgB,CAACI,SAArE,EACnB,CAAC;AACFD,QAAAA,iBAAiB,EAAEH,gBAAgB,CAACG,iBADlC;AAEFE,QAAAA,SAAS,EAAEtL,KAAK,CAACW,iBAAN,CAAwBH,IAFjC;AAGFgL,QAAAA,YAAY,EAAEzL,MAAM,CAACC,KAAK,CAACW,iBAAP;AAHlB,OAAD,CADmB,CAAtB;AAMA,KA5BmE,CA8BpE;;;AACA,SAAK8K,aAAL,CAAmBjG,WAAnB;;AACA,SAAKkG,iBAAL,CAAuBlG,WAAvB;AACA,GAjCD;AAmCA;;;AACAuD,EAAAA,UAAU,CAACpG,SAAX,CAAqBgJ,WAArB,GAAmC,UAAUX,MAAV,EAAkBY,kBAAlB,EAAsC;AACxE,SAAK1D,MAAL,CAAY,oBAAZ,EAAkC8C,MAAlC,EAA0CY,kBAA1C;;AAEA,QAAI,CAAC,KAAK/B,SAAV,EACC,MAAM,IAAI/J,KAAJ,CAAUC,MAAM,CAACC,KAAK,CAACoB,aAAP,EAAsB,CAAC,eAAD,CAAtB,CAAhB,CAAN;AAED,QAAIoE,WAAW,GAAG,IAAIjD,WAAJ,CAAgB7D,YAAY,CAACU,WAA7B,CAAlB;AACAoG,IAAAA,WAAW,CAAC1B,MAAZ,GAAqB,CAACkH,MAAD,CAArB;;AAEA,QAAIY,kBAAkB,CAACV,SAAvB,EAAkC;AACjC1F,MAAAA,WAAW,CAACqG,QAAZ,GAAuB,YAAY;AAAED,QAAAA,kBAAkB,CAACV,SAAnB,CAA6B;AAAEE,UAAAA,iBAAiB,EAAEQ,kBAAkB,CAACR;AAAxC,SAA7B;AAA4F,OAAjI;AACA;;AACD,QAAIQ,kBAAkB,CAACtD,OAAvB,EAAgC;AAC/B9C,MAAAA,WAAW,CAAC+F,OAAZ,GAAsB,IAAI5C,OAAJ,CAAY,IAAZ,EAAkBpB,MAAlB,EAA0BqE,kBAAkB,CAACtD,OAA7C,EAAsDsD,kBAAkB,CAACP,SAAzE,EACnB,CAAC;AACFD,QAAAA,iBAAiB,EAAEQ,kBAAkB,CAACR,iBADpC;AAEFE,QAAAA,SAAS,EAAEtL,KAAK,CAACY,mBAAN,CAA0BJ,IAFnC;AAGFgL,QAAAA,YAAY,EAAEzL,MAAM,CAACC,KAAK,CAACY,mBAAP;AAHlB,OAAD,CADmB,CAAtB;AAMA,KAnBuE,CAqBxE;;;AACA,SAAK6K,aAAL,CAAmBjG,WAAnB;;AACA,SAAKkG,iBAAL,CAAuBlG,WAAvB;AACA,GAxBD;;AA0BAuD,EAAAA,UAAU,CAACpG,SAAX,CAAqB0F,IAArB,GAA4B,UAAUrC,OAAV,EAAmB;AAC9C,SAAKkC,MAAL,CAAY,aAAZ,EAA2BlC,OAA3B;;AAEA,QAAI,CAAC,KAAK6D,SAAV,EACC,MAAM,IAAI/J,KAAJ,CAAUC,MAAM,CAACC,KAAK,CAACoB,aAAP,EAAsB,CAAC,eAAD,CAAtB,CAAhB,CAAN;AAED,QAAIoE,WAAW,GAAG,IAAIjD,WAAJ,CAAgB7D,YAAY,CAACG,OAA7B,CAAlB;AACA2G,IAAAA,WAAW,CAACxB,cAAZ,GAA6BgC,OAA7B;AAEA,QAAIA,OAAO,CAAC9B,GAAR,GAAc,CAAlB,EACC,KAAKuH,aAAL,CAAmBjG,WAAnB,EADD,KAEK,IAAI,KAAK0E,kBAAT,EACJ,KAAKV,gBAAL,CAAsBhE,WAAtB,IAAqC,KAAK0E,kBAAL,CAAwB1E,WAAW,CAACxB,cAApC,CAArC;;AACD,SAAK0H,iBAAL,CAAuBlG,WAAvB;AACA,GAdD;;AAgBAuD,EAAAA,UAAU,CAACpG,SAAX,CAAqBmJ,UAArB,GAAkC,YAAY;AAC7C9D,IAAAA,OAAO,CAACC,GAAR,CAAY,mBAAZ;;AACA,SAAKC,MAAL,CAAY,mBAAZ;;AAEA,QAAI,CAAC,KAAKE,MAAV,EACC,MAAM,IAAItI,KAAJ,CAAUC,MAAM,CAACC,KAAK,CAACoB,aAAP,EAAsB,CAAC,6BAAD,CAAtB,CAAhB,CAAN;AAED,QAAIoE,WAAW,GAAG,IAAIjD,WAAJ,CAAgB7D,YAAY,CAACc,UAA7B,CAAlB,CAP6C,CAS7C;AACA;AACA;;AACA,SAAKgK,gBAAL,CAAsBhE,WAAtB,IAAqCrF,KAAK,CAAC,KAAKgI,aAAN,EAAqB,IAArB,CAA1C;;AAEA,SAAKuD,iBAAL,CAAuBlG,WAAvB;AACA,GAfD;;AAiBAuD,EAAAA,UAAU,CAACpG,SAAX,CAAqBoJ,WAArB,GAAmC,YAAY;AAC9C,QAAI,KAAKvB,YAAL,KAAsB,IAA1B,EAAgC;AAC/B,WAAKtC,MAAL,CAAY,oBAAZ,EAAkC,IAAI8D,IAAJ,EAAlC;;AACA,WAAK9D,MAAL,CAAY,uCAAZ,EAAqD,KAAKoB,aAAL,CAAmBxH,MAAxE;;AACA,WAAK,IAAIlC,GAAT,IAAgB,KAAK0J,aAArB;AACC,aAAKpB,MAAL,CAAY,gBAAZ,EAA8BtI,GAA9B,EAAmC,KAAK0J,aAAL,CAAmB1J,GAAnB,CAAnC;AADD;;AAEA,WAAK,IAAIA,GAAT,IAAgB,KAAK2J,iBAArB;AACC,aAAKrB,MAAL,CAAY,oBAAZ,EAAkCtI,GAAlC,EAAuC,KAAK2J,iBAAL,CAAuB3J,GAAvB,CAAvC;AADD;;AAGA,aAAO,KAAK4K,YAAZ;AACA;AACD,GAXD;;AAaAzB,EAAAA,UAAU,CAACpG,SAAX,CAAqBsJ,UAArB,GAAkC,YAAY;AAC7C,QAAI,KAAKzB,YAAL,KAAsB,IAA1B,EAAgC;AAC/B,WAAKA,YAAL,GAAoB,EAApB;AACA;;AACD,SAAKtC,MAAL,CAAY,mBAAZ,EAAiC,IAAI8D,IAAJ,EAAjC,EAA6CxN,OAA7C;AACA,GALD;;AAOAuK,EAAAA,UAAU,CAACpG,SAAX,CAAqBuJ,SAArB,GAAiC,YAAY;AAC5C,WAAO,KAAK1B,YAAZ;AACA,GAFD;;AAIAzB,EAAAA,UAAU,CAACpG,SAAX,CAAqBmI,UAArB,GAAkC,UAAUqB,KAAV,EAAiB;AAClD;AACAnE,IAAAA,OAAO,CAACC,GAAR,CAAY,iCAAZ;;AACA,QAAI,KAAK8B,cAAL,CAAoBqC,MAAxB,EAAgC;AAC/B,UAAIC,QAAQ,GAAGF,KAAK,CAACG,KAAN,CAAY,GAAZ,CAAf;AACAD,MAAAA,QAAQ,CAAC,CAAD,CAAR,GAAc,KAAd;AACAF,MAAAA,KAAK,GAAGE,QAAQ,CAACE,IAAT,CAAc,GAAd,CAAR;AACA;;AACD,SAAK1C,SAAL,GAAiB,KAAjB;AACA,SAAKzB,MAAL,GAAc,IAAIoE,SAAJ,CAAcL,KAAd,EAAqB,CAAC,MAAD,CAArB,CAAd;AACA,SAAK/D,MAAL,CAAYqE,UAAZ,GAAyB,aAAzB;AAEA,SAAKrE,MAAL,CAAYsE,MAAZ,GAAqBvM,KAAK,CAAC,KAAKwM,eAAN,EAAuB,IAAvB,CAA1B;AACA,SAAKvE,MAAL,CAAYwE,SAAZ,GAAwBzM,KAAK,CAAC,KAAK0M,kBAAN,EAA0B,IAA1B,CAA7B;AACA,SAAKzE,MAAL,CAAY0E,OAAZ,GAAsB3M,KAAK,CAAC,KAAK4M,gBAAN,EAAwB,IAAxB,CAA3B;AACA,SAAK3E,MAAL,CAAY4E,OAAZ,GAAsB7M,KAAK,CAAC,KAAK8M,gBAAN,EAAwB,IAAxB,CAA3B;AAEA,SAAK5C,UAAL,GAAkB,IAAIhD,MAAJ,CAAW,IAAX,EAAiBE,MAAjB,EAAyB,KAAKwC,cAAL,CAAoB/E,iBAA7C,CAAlB;AACA,SAAKsF,aAAL,GAAqB,IAAIjD,MAAJ,CAAW,IAAX,EAAiBE,MAAjB,EAAyB,KAAKwC,cAAL,CAAoB/E,iBAA7C,CAArB;AAEA,SAAKoF,eAAL,GAAuB,IAAIzB,OAAJ,CAAY,IAAZ,EAAkBpB,MAAlB,EAA0B,KAAKwC,cAAL,CAAoBzB,OAA9C,EAAuD,KAAKH,aAA5D,EAA2E,CAACnI,KAAK,CAACU,eAAN,CAAsBF,IAAvB,EAA6BT,MAAM,CAACC,KAAK,CAACU,eAAP,CAAnC,CAA3E,CAAvB;AACA,GArBD,CA72B8B,CAq4B9B;AACA;AACA;AACA;AACA;;;AACAqI,EAAAA,UAAU,CAACpG,SAAX,CAAqB+I,iBAArB,GAAyC,UAAU1F,OAAV,EAAmB;AAC3D,SAAKqD,UAAL,CAAgB6D,IAAhB,CAAqBlH,OAArB,EAD2D,CAE3D;;;AACA,QAAI,KAAK6D,SAAT,EAAoB;AACnB,WAAKsD,cAAL;AACA;AACD,GAND;;AAQApE,EAAAA,UAAU,CAACpG,SAAX,CAAqByK,KAArB,GAA6B,UAAUC,MAAV,EAAkB7H,WAAlB,EAA+B;AAC3D8H,IAAAA,aAAa,GAAG;AAAE9K,MAAAA,IAAI,EAAEgD,WAAW,CAAChD,IAApB;AAA0BS,MAAAA,iBAAiB,EAAEuC,WAAW,CAACvC,iBAAzD;AAA4EzE,MAAAA,OAAO,EAAE;AAArF,KAAhB;;AAEA,YAAQgH,WAAW,CAAChD,IAApB;AACC,WAAK9D,YAAY,CAACG,OAAlB;AACC,YAAI2G,WAAW,CAAC+H,cAAhB,EACCD,aAAa,CAACC,cAAd,GAA+B,IAA/B,CAFF,CAIC;;AACAD,QAAAA,aAAa,CAACtJ,cAAd,GAA+B,EAA/B;AACA,YAAIwJ,GAAG,GAAG,EAAV;AACA,YAAIC,YAAY,GAAGjI,WAAW,CAACxB,cAAZ,CAA2BP,YAA9C;;AACA,aAAK,IAAI5B,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG4L,YAAY,CAAC3L,MAAjC,EAAyCD,CAAC,EAA1C,EAA8C;AAC7C,cAAI4L,YAAY,CAAC5L,CAAD,CAAZ,IAAmB,GAAvB,EACC2L,GAAG,GAAGA,GAAG,GAAG,GAAN,GAAYC,YAAY,CAAC5L,CAAD,CAAZ,CAAgBmF,QAAhB,CAAyB,EAAzB,CAAlB,CADD,KAGCwG,GAAG,GAAGA,GAAG,GAAGC,YAAY,CAAC5L,CAAD,CAAZ,CAAgBmF,QAAhB,CAAyB,EAAzB,CAAZ;AACD;;AACDsG,QAAAA,aAAa,CAACtJ,cAAd,CAA6B0J,UAA7B,GAA0CF,GAA1C;AAEAF,QAAAA,aAAa,CAACtJ,cAAd,CAA6BE,GAA7B,GAAmCsB,WAAW,CAACxB,cAAZ,CAA2BE,GAA9D;AACAoJ,QAAAA,aAAa,CAACtJ,cAAd,CAA6BT,eAA7B,GAA+CiC,WAAW,CAACxB,cAAZ,CAA2BT,eAA1E;AACA,YAAIiC,WAAW,CAACxB,cAAZ,CAA2BC,SAA/B,EACCqJ,aAAa,CAACtJ,cAAd,CAA6BC,SAA7B,GAAyC,IAAzC;AACD,YAAIuB,WAAW,CAACxB,cAAZ,CAA2BG,QAA/B,EACCmJ,aAAa,CAACtJ,cAAd,CAA6BG,QAA7B,GAAwC,IAAxC,CArBF,CAuBC;;AACA,YAAIkJ,MAAM,CAACpL,OAAP,CAAe,OAAf,KAA2B,CAA/B,EAAkC;AACjC,cAAIuD,WAAW,CAACmI,QAAZ,KAAyBzK,SAA7B,EACCsC,WAAW,CAACmI,QAAZ,GAAuB,EAAE,KAAKjE,SAA9B;AACD4D,UAAAA,aAAa,CAACK,QAAd,GAAyBnI,WAAW,CAACmI,QAArC;AACA;;AACD;;AAED;AACC,cAAM7N,KAAK,CAACC,MAAM,CAACC,KAAK,CAACuB,mBAAP,EAA4B,CAAC3B,GAAD,EAAM0N,aAAN,CAA5B,CAAP,CAAX;AAjCF;;AAmCA3D,IAAAA,YAAY,CAACiE,OAAb,CAAqBP,MAAM,GAAG,KAAKjE,SAAd,GAA0B5D,WAAW,CAACvC,iBAA3D,EAA8E4K,IAAI,CAACC,SAAL,CAAeR,aAAf,CAA9E;AACA,GAvCD;;AAyCAvE,EAAAA,UAAU,CAACpG,SAAX,CAAqBiH,OAArB,GAA+B,UAAUhK,GAAV,EAAe;AAC7C,QAAImO,KAAK,GAAGpE,YAAY,CAACqE,OAAb,CAAqBpO,GAArB,CAAZ;AACA,QAAI0N,aAAa,GAAGO,IAAI,CAACI,KAAL,CAAWF,KAAX,CAApB;AAEA,QAAIvI,WAAW,GAAG,IAAIjD,WAAJ,CAAgB+K,aAAa,CAAC9K,IAA9B,EAAoC8K,aAApC,CAAlB;;AAEA,YAAQA,aAAa,CAAC9K,IAAtB;AACC,WAAK9D,YAAY,CAACG,OAAlB;AACC;AACA,YAAI2O,GAAG,GAAGF,aAAa,CAACtJ,cAAd,CAA6B0J,UAAvC;AACA,YAAIpJ,MAAM,GAAG,IAAID,WAAJ,CAAiBmJ,GAAG,CAAC1L,MAAL,GAAe,CAA/B,CAAb;AACA,YAAI4C,UAAU,GAAG,IAAIhB,UAAJ,CAAeY,MAAf,CAAjB;AACA,YAAIzC,CAAC,GAAG,CAAR;;AACA,eAAO2L,GAAG,CAAC1L,MAAJ,IAAc,CAArB,EAAwB;AACvB,cAAIoM,CAAC,GAAGC,QAAQ,CAACX,GAAG,CAACrL,SAAJ,CAAc,CAAd,EAAiB,CAAjB,CAAD,EAAsB,EAAtB,CAAhB;AACAqL,UAAAA,GAAG,GAAGA,GAAG,CAACrL,SAAJ,CAAc,CAAd,EAAiBqL,GAAG,CAAC1L,MAArB,CAAN;AACA4C,UAAAA,UAAU,CAAC7C,CAAC,EAAF,CAAV,GAAkBqM,CAAlB;AACA;;AACD,YAAIlK,cAAc,GAAG,IAAI3F,IAAI,CAACC,IAAL,CAAU2H,OAAd,CAAsBvB,UAAtB,CAArB;AAEAV,QAAAA,cAAc,CAACE,GAAf,GAAqBoJ,aAAa,CAACtJ,cAAd,CAA6BE,GAAlD;AACAF,QAAAA,cAAc,CAACT,eAAf,GAAiC+J,aAAa,CAACtJ,cAAd,CAA6BT,eAA9D;AACA,YAAI+J,aAAa,CAACtJ,cAAd,CAA6BC,SAAjC,EACCD,cAAc,CAACC,SAAf,GAA2B,IAA3B;AACD,YAAIqJ,aAAa,CAACtJ,cAAd,CAA6BG,QAAjC,EACCH,cAAc,CAACG,QAAf,GAA0B,IAA1B;AACDqB,QAAAA,WAAW,CAACxB,cAAZ,GAA6BA,cAA7B;AAEA;;AAED;AACC,cAAMlE,KAAK,CAACC,MAAM,CAACC,KAAK,CAACuB,mBAAP,EAA4B,CAAC3B,GAAD,EAAMmO,KAAN,CAA5B,CAAP,CAAX;AAzBF;;AA4BA,QAAInO,GAAG,CAACqC,OAAJ,CAAY,UAAU,KAAKmH,SAA3B,KAAyC,CAA7C,EAAgD;AAC/C5D,MAAAA,WAAW,CAACxB,cAAZ,CAA2BC,SAA3B,GAAuC,IAAvC;AACA,WAAKqF,aAAL,CAAmB9D,WAAW,CAACvC,iBAA/B,IAAoDuC,WAApD;AACA,KAHD,MAGO,IAAI5F,GAAG,CAACqC,OAAJ,CAAY,cAAc,KAAKmH,SAA/B,KAA6C,CAAjD,EAAoD;AAC1D,WAAKG,iBAAL,CAAuB/D,WAAW,CAACvC,iBAAnC,IAAwDuC,WAAxD;AACA;AACD,GAxCD;;AA0CAuD,EAAAA,UAAU,CAACpG,SAAX,CAAqBwK,cAArB,GAAsC,YAAY;AACjD,QAAInH,OAAO,GAAG,IAAd,CADiD,CAEjD;;AACA,QAAIoI,IAAI,GAAG,KAAK/E,UAAL,CAAgBgF,OAAhB,EAAX,CAHiD,CAKjD;;;AACA,WAAQrI,OAAO,GAAGoI,IAAI,CAACE,GAAL,EAAlB,EAA+B;AAC9B,WAAKC,YAAL,CAAkBvI,OAAlB,EAD8B,CAE9B;;;AACA,UAAI,KAAKwD,gBAAL,CAAsBxD,OAAtB,CAAJ,EAAoC;AACnC,aAAKwD,gBAAL,CAAsBxD,OAAtB;;AACA,eAAO,KAAKwD,gBAAL,CAAsBxD,OAAtB,CAAP;AACA;AACD;AACD,GAdD;AAgBA;;;;;;;AAKA+C,EAAAA,UAAU,CAACpG,SAAX,CAAqB8I,aAArB,GAAqC,UAAUjG,WAAV,EAAuB;AAC3D,QAAIgJ,YAAY,GAAGC,MAAM,CAAC9O,IAAP,CAAY,KAAK2J,aAAjB,EAAgCxH,MAAnD;AACA,QAAI0M,YAAY,GAAG,KAAK1E,oBAAxB,EACC,MAAMhK,KAAK,CAAC,uBAAuB0O,YAAxB,CAAX;;AAED,WAAO,KAAKlF,aAAL,CAAmB,KAAKG,mBAAxB,MAAiDvG,SAAxD,EAAmE;AAClE,WAAKuG,mBAAL;AACA;;AACDjE,IAAAA,WAAW,CAACvC,iBAAZ,GAAgC,KAAKwG,mBAArC;AACA,SAAKH,aAAL,CAAmB9D,WAAW,CAACvC,iBAA/B,IAAoDuC,WAApD;;AACA,QAAIA,WAAW,CAAChD,IAAZ,KAAqB9D,YAAY,CAACG,OAAtC,EAA+C;AAC9C,WAAKuO,KAAL,CAAW,OAAX,EAAoB5H,WAApB;AACA;;AACD,QAAI,KAAKiE,mBAAL,KAA6B,KAAKK,oBAAtC,EAA4D;AAC3D,WAAKL,mBAAL,GAA2B,CAA3B;AACA;AACD,GAhBD;AAkBA;;;;;;AAIAV,EAAAA,UAAU,CAACpG,SAAX,CAAqBgK,eAArB,GAAuC,YAAY;AAClD;AACA,QAAInH,WAAW,GAAG,IAAIjD,WAAJ,CAAgB7D,YAAY,CAACC,OAA7B,EAAsC,KAAKoL,cAA3C,CAAlB;AACAvE,IAAAA,WAAW,CAACnC,QAAZ,GAAuB,KAAKA,QAA5B;;AACA,SAAKkL,YAAL,CAAkB/I,WAAlB;AACA,GALD;AAOA;;;;;;AAIAuD,EAAAA,UAAU,CAACpG,SAAX,CAAqBkK,kBAArB,GAA0C,UAAU6B,KAAV,EAAiB;AAC1D,SAAKxG,MAAL,CAAY,2BAAZ,EAAyCwG,KAAK,CAACC,IAA/C,EAD0D,CAE1D;;;AACA,SAAKrE,aAAL,CAAmB9B,KAAnB;;AACA,QAAIoG,QAAQ,GAAG,KAAKC,gBAAL,CAAsBH,KAAK,CAACC,IAA5B,CAAf;;AACA,SAAK,IAAI9M,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG+M,QAAQ,CAAC9M,MAA7B,EAAqCD,CAAC,IAAI,CAA1C,EAA6C;AAC5C,WAAKiN,cAAL,CAAoBF,QAAQ,CAAC/M,CAAD,CAA5B;AACA;AACD,GARD;;AAUAkH,EAAAA,UAAU,CAACpG,SAAX,CAAqBkM,gBAArB,GAAwC,UAAUF,IAAV,EAAgB;AACvD,QAAII,SAAS,GAAG,IAAIrL,UAAJ,CAAeiL,IAAf,CAAhB;;AACA,QAAI,KAAKpE,aAAT,EAAwB;AACvB,UAAIyE,OAAO,GAAG,IAAItL,UAAJ,CAAe,KAAK6G,aAAL,CAAmBzI,MAAnB,GAA4BiN,SAAS,CAACjN,MAArD,CAAd;AACAkN,MAAAA,OAAO,CAACrK,GAAR,CAAY,KAAK4F,aAAjB;AACAyE,MAAAA,OAAO,CAACrK,GAAR,CAAYoK,SAAZ,EAAuB,KAAKxE,aAAL,CAAmBzI,MAA1C;AACAiN,MAAAA,SAAS,GAAGC,OAAZ;AACA,aAAO,KAAKzE,aAAZ;AACA;;AACD,QAAI;AACH,UAAIpE,MAAM,GAAG,CAAb;AACA,UAAIyI,QAAQ,GAAG,EAAf;;AACA,aAAOzI,MAAM,GAAG4I,SAAS,CAACjN,MAA1B,EAAkC;AACjC,YAAImN,MAAM,GAAGhK,aAAa,CAAC8J,SAAD,EAAY5I,MAAZ,CAA1B;AACA,YAAIX,WAAW,GAAGyJ,MAAM,CAAC,CAAD,CAAxB;AACA9I,QAAAA,MAAM,GAAG8I,MAAM,CAAC,CAAD,CAAf;;AACA,YAAIzJ,WAAW,KAAK,IAApB,EAA0B;AACzBoJ,UAAAA,QAAQ,CAAC1B,IAAT,CAAc1H,WAAd;AACA,SAFD,MAEO;AACN;AACA;AACD;;AACD,UAAIW,MAAM,GAAG4I,SAAS,CAACjN,MAAvB,EAA+B;AAC9B,aAAKyI,aAAL,GAAqBwE,SAAS,CAAC7I,QAAV,CAAmBC,MAAnB,CAArB;AACA;AACD,KAhBD,CAgBE,OAAOxE,KAAP,EAAc;AACf,WAAKwG,aAAL,CAAmBnI,KAAK,CAACc,cAAN,CAAqBN,IAAxC,EAA8CT,MAAM,CAACC,KAAK,CAACc,cAAP,EAAuB,CAACa,KAAK,CAACqE,OAAP,CAAvB,CAApD;;AACA;AACA;;AACD,WAAO4I,QAAP;AACA,GA9BD;;AAgCA7F,EAAAA,UAAU,CAACpG,SAAX,CAAqBmM,cAArB,GAAsC,UAAUtJ,WAAV,EAAuB;AAE5D,SAAK0C,MAAL,CAAY,uBAAZ,EAAqC1C,WAArC;;AAEA,QAAI;AACH,cAAQA,WAAW,CAAChD,IAApB;AACC,aAAK9D,YAAY,CAACE,OAAlB;AACC,eAAKwL,eAAL,CAAqB1B,MAArB,GADD,CAGC;;;AACA,cAAI,KAAKqB,cAAL,CAAoBjF,YAAxB,EAAsC;AACrC,iBAAK,IAAIlF,GAAT,IAAgB,KAAK0J,aAArB,EAAoC;AACnC,kBAAI4F,WAAW,GAAG,KAAK5F,aAAL,CAAmB1J,GAAnB,CAAlB;AACA+J,cAAAA,YAAY,CAACwF,UAAb,CAAwB,cAAc,KAAK/F,SAAnB,GAA+B8F,WAAW,CAACjM,iBAAnE;AACA;;AACD,iBAAKqG,aAAL,GAAqB,EAArB;;AAEA,iBAAK,IAAI1J,GAAT,IAAgB,KAAK2J,iBAArB,EAAwC;AACvC,kBAAI6F,eAAe,GAAG,KAAK7F,iBAAL,CAAuB3J,GAAvB,CAAtB;AACA+J,cAAAA,YAAY,CAACwF,UAAb,CAAwB,kBAAkB,KAAK/F,SAAvB,GAAmCgG,eAAe,CAACnM,iBAA3E;AACA;;AACD,iBAAKsG,iBAAL,GAAyB,EAAzB;AACA,WAhBF,CAiBC;;;AACA,cAAI/D,WAAW,CAACG,UAAZ,KAA2B,CAA/B,EAAkC;AACjC,iBAAKkE,SAAL,GAAiB,IAAjB,CADiC,CAEjC;;AACA,gBAAI,KAAKE,cAAL,CAAoBc,IAAxB,EACC,KAAKb,SAAL,GAAiB,KAAKD,cAAL,CAAoBc,IAApB,CAAyB/I,MAA1C;AACD,WALD,MAKO;AACN,iBAAKqG,aAAL,CAAmBnI,KAAK,CAACe,kBAAN,CAAyBP,IAA5C,EAAkDT,MAAM,CAACC,KAAK,CAACe,kBAAP,EAA2B,CAACyE,WAAW,CAACG,UAAb,EAAyBjE,UAAU,CAAC8D,WAAW,CAACG,UAAb,CAAnC,CAA3B,CAAxD;;AACA;AACA,WA1BF,CA4BC;;;AACA,cAAI0J,iBAAiB,GAAG,IAAIrM,KAAJ,EAAxB;;AACA,eAAK,IAAIsM,KAAT,IAAkB,KAAKhG,aAAvB,EAAsC;AACrC,gBAAI,KAAKA,aAAL,CAAmBzJ,cAAnB,CAAkCyP,KAAlC,CAAJ,EACCD,iBAAiB,CAACnC,IAAlB,CAAuB,KAAK5D,aAAL,CAAmBgG,KAAnB,CAAvB;AACD,WAjCF,CAmCC;;;AACA,cAAID,iBAAiB,GAAGA,iBAAiB,CAACE,IAAlB,CAAuB,UAAUC,CAAV,EAAaC,CAAb,EAAgB;AAAE,mBAAOD,CAAC,CAAC7B,QAAF,GAAa8B,CAAC,CAAC9B,QAAtB;AAAiC,WAA1E,CAAxB;;AACA,eAAK,IAAI9L,CAAC,GAAG,CAAR,EAAW+D,GAAG,GAAGyJ,iBAAiB,CAACvN,MAAxC,EAAgDD,CAAC,GAAG+D,GAApD,EAAyD/D,CAAC,EAA1D,EAA8D;AAC7D,gBAAIqN,WAAW,GAAGG,iBAAiB,CAACxN,CAAD,CAAnC;;AACA,gBAAIqN,WAAW,CAAC1M,IAAZ,IAAoB9D,YAAY,CAACG,OAAjC,IAA4CqQ,WAAW,CAAC3B,cAA5D,EAA4E;AAC3E,kBAAImC,aAAa,GAAG,IAAInN,WAAJ,CAAgB7D,YAAY,CAACM,MAA7B,EAAqC;AAAEiE,gBAAAA,iBAAiB,EAAEiM,WAAW,CAACjM;AAAjC,eAArC,CAApB;;AACA,mBAAKyI,iBAAL,CAAuBgE,aAAvB;AACA,aAHD,MAGO;AACN,mBAAKhE,iBAAL,CAAuBwD,WAAvB;AACA;;AAAA;AACD,WA7CF,CA+CC;;;AACA,cAAI,KAAKnF,cAAL,CAAoBmB,SAAxB,EAAmC;AAClC,iBAAKnB,cAAL,CAAoBmB,SAApB,CAA8B;AAAEE,cAAAA,iBAAiB,EAAE,KAAKrB,cAAL,CAAoBqB;AAAzC,aAA9B;AACA,WAlDF,CAoDC;;;AACA,eAAK+B,cAAL;;AACA;;AAED,aAAKzO,YAAY,CAACG,OAAlB;AACC,eAAK8Q,eAAL,CAAqBnK,WAArB;;AACA;;AAED,aAAK9G,YAAY,CAACI,MAAlB;AACC,cAAIoQ,WAAW,GAAG,KAAK5F,aAAL,CAAmB9D,WAAW,CAACvC,iBAA/B,CAAlB,CADD,CAEC;;AACA,cAAIiM,WAAJ,EAAiB;AAChB,mBAAO,KAAK5F,aAAL,CAAmB9D,WAAW,CAACvC,iBAA/B,CAAP;AACA0G,YAAAA,YAAY,CAACwF,UAAb,CAAwB,cAAc,KAAK/F,SAAnB,GAA+B5D,WAAW,CAACvC,iBAAnE;AACA,gBAAI,KAAKiH,kBAAT,EACC,KAAKA,kBAAL,CAAwBgF,WAAW,CAAClL,cAApC;AACD;;AACD;;AAED,aAAKtF,YAAY,CAACK,MAAlB;AACC,cAAImQ,WAAW,GAAG,KAAK5F,aAAL,CAAmB9D,WAAW,CAACvC,iBAA/B,CAAlB,CADD,CAEC;;AACA,cAAIiM,WAAJ,EAAiB;AAChBA,YAAAA,WAAW,CAAC3B,cAAZ,GAA6B,IAA7B;AACA,gBAAImC,aAAa,GAAG,IAAInN,WAAJ,CAAgB7D,YAAY,CAACM,MAA7B,EAAqC;AAAEiE,cAAAA,iBAAiB,EAAEuC,WAAW,CAACvC;AAAjC,aAArC,CAApB;AACA,iBAAKmK,KAAL,CAAW,OAAX,EAAoB8B,WAApB;;AACA,iBAAKxD,iBAAL,CAAuBgE,aAAvB;AACA;;AACD;;AAED,aAAKhR,YAAY,CAACM,MAAlB;AACC,cAAIoQ,eAAe,GAAG,KAAK7F,iBAAL,CAAuB/D,WAAW,CAACvC,iBAAnC,CAAtB;AACA0G,UAAAA,YAAY,CAACwF,UAAb,CAAwB,kBAAkB,KAAK/F,SAAvB,GAAmC5D,WAAW,CAACvC,iBAAvE,EAFD,CAGC;;AACA,cAAImM,eAAJ,EAAqB;AACpB,iBAAKQ,eAAL,CAAqBR,eAArB;;AACA,mBAAO,KAAK7F,iBAAL,CAAuB/D,WAAW,CAACvC,iBAAnC,CAAP;AACA,WAPF,CAQC;;;AACA4M,UAAAA,cAAc,GAAG,IAAItN,WAAJ,CAAgB7D,YAAY,CAACO,OAA7B,EAAsC;AAAEgE,YAAAA,iBAAiB,EAAEuC,WAAW,CAACvC;AAAjC,WAAtC,CAAjB;;AACA,eAAKyI,iBAAL,CAAuBmE,cAAvB;;AAGA;;AAED,aAAKnR,YAAY,CAACO,OAAlB;AACC,cAAIiQ,WAAW,GAAG,KAAK5F,aAAL,CAAmB9D,WAAW,CAACvC,iBAA/B,CAAlB;AACA,iBAAO,KAAKqG,aAAL,CAAmB9D,WAAW,CAACvC,iBAA/B,CAAP;AACA0G,UAAAA,YAAY,CAACwF,UAAb,CAAwB,cAAc,KAAK/F,SAAnB,GAA+B5D,WAAW,CAACvC,iBAAnE;AACA,cAAI,KAAKiH,kBAAT,EACC,KAAKA,kBAAL,CAAwBgF,WAAW,CAAClL,cAApC;AACD;;AAED,aAAKtF,YAAY,CAACS,MAAlB;AACC,cAAI+P,WAAW,GAAG,KAAK5F,aAAL,CAAmB9D,WAAW,CAACvC,iBAA/B,CAAlB;;AACA,cAAIiM,WAAJ,EAAiB;AAChB,gBAAIA,WAAW,CAAC3D,OAAhB,EACC2D,WAAW,CAAC3D,OAAZ,CAAoB7C,MAApB;AACDlD,YAAAA,WAAW,CAACG,UAAZ,CAAuB1D,OAAvB,GAAiCe,KAAK,CAACL,SAAN,CAAgBV,OAAjD;;AACA,gBAAIuD,WAAW,CAACG,UAAZ,CAAuB1D,OAAvB,CAA+B,IAA/B,MAAyC,CAAC,CAA9C,EAAiD;AAChD,kBAAIiN,WAAW,CAAC7D,SAAhB,EAA2B;AAC1B6D,gBAAAA,WAAW,CAAC7D,SAAZ,CAAsB7F,WAAW,CAACG,UAAlC;AACA;AACD,aAJD,MAIO,IAAIuJ,WAAW,CAAChE,SAAhB,EAA2B;AACjCgE,cAAAA,WAAW,CAAChE,SAAZ,CAAsB1F,WAAW,CAACG,UAAlC;AACA;;AACD,mBAAO,KAAK2D,aAAL,CAAmB9D,WAAW,CAACvC,iBAA/B,CAAP;AACA;;AACD;;AAED,aAAKvE,YAAY,CAACW,QAAlB;AACC,cAAI6P,WAAW,GAAG,KAAK5F,aAAL,CAAmB9D,WAAW,CAACvC,iBAA/B,CAAlB;;AACA,cAAIiM,WAAJ,EAAiB;AAChB,gBAAIA,WAAW,CAAC3D,OAAhB,EACC2D,WAAW,CAAC3D,OAAZ,CAAoB7C,MAApB;;AACD,gBAAIwG,WAAW,CAACrD,QAAhB,EAA0B;AACzBqD,cAAAA,WAAW,CAACrD,QAAZ;AACA;;AACD,mBAAO,KAAKvC,aAAL,CAAmB9D,WAAW,CAACvC,iBAA/B,CAAP;AACA;;AAED;;AAED,aAAKvE,YAAY,CAACa,QAAlB;AACC;AACA,eAAK8K,UAAL,CAAgB7B,KAAhB;AACA;;AAED,aAAK9J,YAAY,CAACc,UAAlB;AACC;AACA,eAAK2I,aAAL,CAAmBnI,KAAK,CAACwB,yBAAN,CAAgChB,IAAnD,EAAyDT,MAAM,CAACC,KAAK,CAACwB,yBAAP,EAAkC,CAACgE,WAAW,CAAChD,IAAb,CAAlC,CAA/D;;AACA;;AAED;AACC,eAAK2F,aAAL,CAAmBnI,KAAK,CAACwB,yBAAN,CAAgChB,IAAnD,EAAyDT,MAAM,CAACC,KAAK,CAACwB,yBAAP,EAAkC,CAACgE,WAAW,CAAChD,IAAb,CAAlC,CAA/D;;AAnJF;;AAoJC;AACD,KAtJD,CAsJE,OAAOb,KAAP,EAAc;AACf,WAAKwG,aAAL,CAAmBnI,KAAK,CAACc,cAAN,CAAqBN,IAAxC,EAA8CT,MAAM,CAACC,KAAK,CAACc,cAAP,EAAuB,CAACa,KAAK,CAACqE,OAAP,CAAvB,CAApD;;AACA;AACA;AACD,GA9JD;AAgKA;;;AACA+C,EAAAA,UAAU,CAACpG,SAAX,CAAqBoK,gBAArB,GAAwC,UAAUpL,KAAV,EAAiB;AACxD,SAAKwG,aAAL,CAAmBnI,KAAK,CAACgB,YAAN,CAAmBR,IAAtC,EAA4CT,MAAM,CAACC,KAAK,CAACgB,YAAP,EAAqB,CAACW,KAAK,CAACgN,IAAP,CAArB,CAAlD;AACA,GAFD;AAIA;;;AACA5F,EAAAA,UAAU,CAACpG,SAAX,CAAqBsK,gBAArB,GAAwC,YAAY;AACnD,SAAK9E,aAAL,CAAmBnI,KAAK,CAACiB,YAAN,CAAmBT,IAAtC,EAA4CT,MAAM,CAACC,KAAK,CAACiB,YAAP,CAAlD;AACA,GAFD;AAIA;;;AACA8H,EAAAA,UAAU,CAACpG,SAAX,CAAqB4L,YAArB,GAAoC,UAAU/I,WAAV,EAAuB;AAE1D,QAAIA,WAAW,CAAChD,IAAZ,IAAoB,CAAxB,EAA2B;AAC1B,UAAIsN,iBAAiB,GAAG,KAAKlF,UAAL,CAAgBpF,WAAhB,EAA6B,UAA7B,CAAxB;;AACA,WAAK0C,MAAL,CAAY,qBAAZ,EAAmC4H,iBAAnC;AACA,KAHD,MAIK,KAAK5H,MAAL,CAAY,qBAAZ,EAAmC1C,WAAnC;;AAEL,SAAK4C,MAAL,CAAYC,IAAZ,CAAiB7C,WAAW,CAAC5C,MAAZ,EAAjB;AACA;;AACA,SAAKyH,UAAL,CAAgB7B,KAAhB;AACA,GAXD;AAaA;;;AACAO,EAAAA,UAAU,CAACpG,SAAX,CAAqBgN,eAArB,GAAuC,UAAUnK,WAAV,EAAuB;AAC7D,YAAQA,WAAW,CAACxB,cAAZ,CAA2BE,GAAnC;AACC,WAAK,WAAL;AACA,WAAK,CAAL;AACC,aAAK0L,eAAL,CAAqBpK,WAArB;;AACA;;AAED,WAAK,CAAL;AACC,YAAIuK,aAAa,GAAG,IAAIxN,WAAJ,CAAgB7D,YAAY,CAACI,MAA7B,EAAqC;AAAEmE,UAAAA,iBAAiB,EAAEuC,WAAW,CAACvC;AAAjC,SAArC,CAApB;;AACA,aAAKyI,iBAAL,CAAuBqE,aAAvB;;AACA,aAAKH,eAAL,CAAqBpK,WAArB;;AACA;;AAED,WAAK,CAAL;AACC,aAAK+D,iBAAL,CAAuB/D,WAAW,CAACvC,iBAAnC,IAAwDuC,WAAxD;AACA,aAAK4H,KAAL,CAAW,WAAX,EAAwB5H,WAAxB;AACA,YAAIwK,aAAa,GAAG,IAAIzN,WAAJ,CAAgB7D,YAAY,CAACK,MAA7B,EAAqC;AAAEkE,UAAAA,iBAAiB,EAAEuC,WAAW,CAACvC;AAAjC,SAArC,CAApB;;AACA,aAAKyI,iBAAL,CAAuBsE,aAAvB;;AAEA;;AAED;AACC,cAAMlQ,KAAK,CAAC,iBAAiBmQ,YAAY,CAACjM,cAAb,CAA4BE,GAA9C,CAAX;AArBF;;AAsBC;AACD,GAxBD;AA0BA;;;AACA6E,EAAAA,UAAU,CAACpG,SAAX,CAAqBiN,eAArB,GAAuC,UAAUpK,WAAV,EAAuB;AAC7D,QAAI,KAAK2E,gBAAT,EAA2B;AAC1B,WAAKA,gBAAL,CAAsB3E,WAAW,CAACxB,cAAlC;AACA;AACD,GAJD;AAMA;;;;;;;;;AAOA+E,EAAAA,UAAU,CAACpG,SAAX,CAAqBwF,aAArB,GAAqC,UAAUmD,SAAV,EAAqB4E,SAArB,EAAgC;AACpE,SAAKhI,MAAL,CAAY,sBAAZ,EAAoCoD,SAApC,EAA+C4E,SAA/C;;AAEA,SAAK7F,UAAL,CAAgB3B,MAAhB;AACA,SAAK4B,aAAL,CAAmB5B,MAAnB;AACA,QAAI,KAAK0B,eAAT,EACC,KAAKA,eAAL,CAAqB1B,MAArB,GANmE,CAOpE;;AACA,SAAKW,UAAL,GAAkB,EAAlB;AACA,SAAKG,gBAAL,GAAwB,EAAxB;;AAEA,QAAI,KAAKpB,MAAT,EAAiB;AAChB;AACA,WAAKA,MAAL,CAAYsE,MAAZ,GAAqB,IAArB;AACA,WAAKtE,MAAL,CAAYwE,SAAZ,GAAwB,IAAxB;AACA,WAAKxE,MAAL,CAAY0E,OAAZ,GAAsB,IAAtB;AACA,WAAK1E,MAAL,CAAY4E,OAAZ,GAAsB,IAAtB;AACA,UAAI,KAAK5E,MAAL,CAAY+H,UAAZ,KAA2B,CAA/B,EACC,KAAK/H,MAAL,CAAYgI,KAAZ;AACD,aAAO,KAAKhI,MAAZ;AACA;;AAED,QAAI,KAAK2B,cAAL,CAAoBc,IAApB,IAA4B,KAAKb,SAAL,GAAiB,KAAKD,cAAL,CAAoBc,IAApB,CAAyB/I,MAAzB,GAAkC,CAAnF,EAAsF;AACrF;AACA,WAAKkI,SAAL;;AACA,WAAKc,UAAL,CAAgB,KAAKf,cAAL,CAAoBc,IAApB,CAAyB,KAAKb,SAA9B,CAAhB;AAEA,KALD,MAKO;AAEN,UAAIsB,SAAS,KAAKpI,SAAlB,EAA6B;AAC5BoI,QAAAA,SAAS,GAAGtL,KAAK,CAACO,EAAN,CAASC,IAArB;AACA0P,QAAAA,SAAS,GAAGnQ,MAAM,CAACC,KAAK,CAACO,EAAP,CAAlB;AACA,OALK,CAON;;;AACA,UAAI,KAAKsJ,SAAT,EAAoB;AACnB,aAAKA,SAAL,GAAiB,KAAjB,CADmB,CAEnB;;AACA,YAAI,KAAKI,gBAAT,EACCjC,OAAO,CAACC,GAAR,CAAY,0CAAZ;AACD,aAAKgC,gBAAL,CAAsB;AAAEqB,UAAAA,SAAS,EAAEA,SAAb;AAAwBE,UAAAA,YAAY,EAAE0E;AAAtC,SAAtB;AACA,OAND,MAMO;AACN;AACA,YAAI,KAAKnG,cAAL,CAAoB5G,WAApB,KAAoC,CAApC,IAAyC,KAAK4G,cAAL,CAAoBsG,mBAApB,KAA4C,KAAzF,EAAgG;AAC/F,eAAKnI,MAAL,CAAY,2CAAZ;;AACA,eAAK6B,cAAL,CAAoB5G,WAApB,GAAkC,CAAlC;;AACA,cAAI,KAAK4G,cAAL,CAAoBc,IAAxB,EAA8B;AAC7B,iBAAKb,SAAL,GAAiB,CAAjB;;AACA,iBAAKc,UAAL,CAAgB,KAAKf,cAAL,CAAoBc,IAApB,CAAyB,CAAzB,CAAhB;AACA,WAHD,MAGO;AACN,iBAAKC,UAAL,CAAgB,KAAK9B,GAArB;AACA;AACD,SATD,MASO,IAAI,KAAKe,cAAL,CAAoBsB,SAAxB,EAAmC;AACzC,eAAKtB,cAAL,CAAoBsB,SAApB,CAA8B;AAAED,YAAAA,iBAAiB,EAAE,KAAKrB,cAAL,CAAoBqB,iBAAzC;AAA4DE,YAAAA,SAAS,EAAEA,SAAvE;AAAkFE,YAAAA,YAAY,EAAE0E;AAAhG,WAA9B;AACA;AACD;AACD;AACD,GAzDD;AA2DA;;;AACAnH,EAAAA,UAAU,CAACpG,SAAX,CAAqBuF,MAArB,GAA8B,YAAY;AACzC,QAAI,KAAKsC,YAAL,KAAsB,IAA1B,EAAgC;AAC/B,WAAK,IAAI3I,CAAC,GAAG,CAAR,EAAWyO,GAAG,GAAGhQ,SAAS,CAACwB,MAAhC,EAAwCD,CAAC,GAAGyO,GAA5C,EAAiDzO,CAAC,EAAlD,EAAsD;AACrD,YAAI,KAAK2I,YAAL,CAAkB1I,MAAlB,IAA4B,KAAK2I,kBAArC,EAAyD;AACxD,eAAKD,YAAL,CAAkB+F,KAAlB;AACA;;AACD,YAAI1O,CAAC,KAAK,CAAV,EAAa,KAAK2I,YAAL,CAAkB0C,IAAlB,CAAuB5M,SAAS,CAACuB,CAAD,CAAhC,EAAb,KACK,IAAI,OAAOvB,SAAS,CAACuB,CAAD,CAAhB,KAAwB,WAA5B,EAAyC,KAAK2I,YAAL,CAAkB0C,IAAlB,CAAuB5M,SAAS,CAACuB,CAAD,CAAhC,EAAzC,KACA,KAAK2I,YAAL,CAAkB0C,IAAlB,CAAuB,OAAOW,IAAI,CAACC,SAAL,CAAexN,SAAS,CAACuB,CAAD,CAAxB,CAA9B;AACL;;AAAA;AACD;;AAAA;AACD,GAXD;AAaA;;;AACAkH,EAAAA,UAAU,CAACpG,SAAX,CAAqBiI,UAArB,GAAkC,UAAU4F,WAAV,EAAuBC,MAAvB,EAA+B;AAChE,QAAIC,iBAAiB,GAAG,EAAxB;;AACA,SAAK,IAAIC,IAAT,IAAiBH,WAAjB,EAA8B;AAC7B,UAAIA,WAAW,CAAC3Q,cAAZ,CAA2B8Q,IAA3B,CAAJ,EAAsC;AACrC,YAAIA,IAAI,IAAIF,MAAZ,EACCC,iBAAiB,CAACC,IAAD,CAAjB,GAA0B,QAA1B,CADD,KAGCD,iBAAiB,CAACC,IAAD,CAAjB,GAA0BH,WAAW,CAACG,IAAD,CAArC;AACD;AACD;;AACD,WAAOD,iBAAP;AACA,GAXD,CAh3C8B,CA63C9B;AACA;AACA;;AAEA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyDA,MAAIE,MAAM,GAAG,SAATA,MAAS,CAAU3H,IAAV,EAAgBC,IAAhB,EAAsBC,IAAtB,EAA4B9F,QAA5B,EAAsC;AAElD,QAAI2F,GAAJ;AAEA,QAAI,OAAOC,IAAP,KAAgB,QAApB,EACC,MAAM,IAAInJ,KAAJ,CAAUC,MAAM,CAACC,KAAK,CAACC,YAAP,EAAqB,SAAQgJ,IAAR,GAAc,MAAd,CAArB,CAAhB,CAAN;;AAED,QAAI3I,SAAS,CAACwB,MAAV,IAAoB,CAAxB,EAA2B;AAC1B;AACA;AACAuB,MAAAA,QAAQ,GAAG6F,IAAX;AACAF,MAAAA,GAAG,GAAGC,IAAN;AACA,UAAI4H,KAAK,GAAG7H,GAAG,CAAC6H,KAAJ,CAAU,oDAAV,CAAZ;;AACA,UAAIA,KAAJ,EAAW;AACV5H,QAAAA,IAAI,GAAG4H,KAAK,CAAC,CAAD,CAAL,IAAYA,KAAK,CAAC,CAAD,CAAxB;AACA3H,QAAAA,IAAI,GAAGiF,QAAQ,CAAC0C,KAAK,CAAC,CAAD,CAAN,CAAf;AACA1H,QAAAA,IAAI,GAAG0H,KAAK,CAAC,CAAD,CAAZ;AACA,OAJD,MAIO;AACN,cAAM,IAAI/Q,KAAJ,CAAUC,MAAM,CAACC,KAAK,CAACqB,gBAAP,EAAyB,CAAC4H,IAAD,EAAO,MAAP,CAAzB,CAAhB,CAAN;AACA;AACD,KAbD,MAaO;AACN,UAAI3I,SAAS,CAACwB,MAAV,IAAoB,CAAxB,EAA2B;AAC1BuB,QAAAA,QAAQ,GAAG8F,IAAX;AACAA,QAAAA,IAAI,GAAG,OAAP;AACA;;AACD,UAAI,OAAOD,IAAP,KAAgB,QAAhB,IAA4BA,IAAI,GAAG,CAAvC,EACC,MAAM,IAAIpJ,KAAJ,CAAUC,MAAM,CAACC,KAAK,CAACC,YAAP,EAAqB,SAAQiJ,IAAR,GAAc,MAAd,CAArB,CAAhB,CAAN;AACD,UAAI,OAAOC,IAAP,KAAgB,QAApB,EACC,MAAM,IAAIrJ,KAAJ,CAAUC,MAAM,CAACC,KAAK,CAACC,YAAP,EAAqB,SAAQkJ,IAAR,GAAc,MAAd,CAArB,CAAhB,CAAN;AAED,UAAI2H,eAAe,GAAI7H,IAAI,CAAChH,OAAL,CAAa,GAAb,KAAqB,CAAC,CAAtB,IAA2BgH,IAAI,CAAC8H,KAAL,CAAW,CAAX,EAAc,CAAd,KAAoB,GAA/C,IAAsD9H,IAAI,CAAC8H,KAAL,CAAW,CAAC,CAAZ,KAAkB,GAA/F;AACA/H,MAAAA,GAAG,GAAG,WAAW8H,eAAe,GAAG,MAAM7H,IAAN,GAAa,GAAhB,GAAsBA,IAAhD,IAAwD,GAAxD,GAA8DC,IAA9D,GAAqEC,IAA3E;AACA;;AAED,QAAI6H,cAAc,GAAG,CAArB;;AACA,SAAK,IAAInP,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGwB,QAAQ,CAACvB,MAA7B,EAAqCD,CAAC,EAAtC,EAA0C;AACzC,UAAI4E,QAAQ,GAAGpD,QAAQ,CAACqD,UAAT,CAAoB7E,CAApB,CAAf;;AACA,UAAI,UAAU4E,QAAV,IAAsBA,QAAQ,IAAI,MAAtC,EAA8C;AAC7C5E,QAAAA,CAAC,GAD4C,CACxC;AACL;;AACDmP,MAAAA,cAAc;AACd;;AACD,QAAI,OAAO3N,QAAP,KAAoB,QAApB,IAAgC2N,cAAc,GAAG,KAArD,EACC,MAAM,IAAIlR,KAAJ,CAAUC,MAAM,CAACC,KAAK,CAACqB,gBAAP,EAAyB,CAACgC,QAAD,EAAW,UAAX,CAAzB,CAAhB,CAAN;AAED,QAAIiE,MAAM,GAAG,IAAIyB,UAAJ,CAAeC,GAAf,EAAoBC,IAApB,EAA0BC,IAA1B,EAAgCC,IAAhC,EAAsC9F,QAAtC,CAAb;;AACA,SAAK4N,QAAL,GAAgB,YAAY;AAAE,aAAOhI,IAAP;AAAc,KAA5C;;AACA,SAAKiI,QAAL,GAAgB,YAAY;AAAE,YAAM,IAAIpR,KAAJ,CAAUC,MAAM,CAACC,KAAK,CAACsB,qBAAP,CAAhB,CAAN;AAAuD,KAArF;;AAEA,SAAK6P,QAAL,GAAgB,YAAY;AAAE,aAAOjI,IAAP;AAAc,KAA5C;;AACA,SAAKkI,QAAL,GAAgB,YAAY;AAAE,YAAM,IAAItR,KAAJ,CAAUC,MAAM,CAACC,KAAK,CAACsB,qBAAP,CAAhB,CAAN;AAAuD,KAArF;;AAEA,SAAK+P,QAAL,GAAgB,YAAY;AAAE,aAAOlI,IAAP;AAAc,KAA5C;;AACA,SAAKmI,QAAL,GAAgB,YAAY;AAAE,YAAM,IAAIxR,KAAJ,CAAUC,MAAM,CAACC,KAAK,CAACsB,qBAAP,CAAhB,CAAN;AAAuD,KAArF;;AAEA,SAAKiQ,OAAL,GAAe,YAAY;AAAE,aAAOvI,GAAP;AAAa,KAA1C;;AACA,SAAKwI,OAAL,GAAe,YAAY;AAAE,YAAM,IAAI1R,KAAJ,CAAUC,MAAM,CAACC,KAAK,CAACsB,qBAAP,CAAhB,CAAN;AAAuD,KAApF;;AAEA,SAAKmQ,YAAL,GAAoB,YAAY;AAAE,aAAOnK,MAAM,CAACjE,QAAd;AAAyB,KAA3D;;AACA,SAAKqO,YAAL,GAAoB,YAAY;AAAE,YAAM,IAAI5R,KAAJ,CAAUC,MAAM,CAACC,KAAK,CAACsB,qBAAP,CAAhB,CAAN;AAAuD,KAAzF;;AAEA,SAAKqQ,oBAAL,GAA4B,YAAY;AAAE,aAAOrK,MAAM,CAAC2C,gBAAd;AAAiC,KAA3E;;AACA,SAAK2H,oBAAL,GAA4B,UAAUC,mBAAV,EAA+B;AAC1D,UAAI,OAAOA,mBAAP,KAA+B,UAAnC,EACCvK,MAAM,CAAC2C,gBAAP,GAA0B4H,mBAA1B,CADD,KAGC,MAAM,IAAI/R,KAAJ,CAAUC,MAAM,CAACC,KAAK,CAACC,YAAP,EAAqB,SAAQ4R,mBAAR,GAA6B,kBAA7B,CAArB,CAAhB,CAAN;AACD,KALD;;AAOA,SAAKC,sBAAL,GAA8B,YAAY;AAAE,aAAOxK,MAAM,CAAC4C,kBAAd;AAAmC,KAA/E;;AACA,SAAK6H,sBAAL,GAA8B,UAAUC,qBAAV,EAAiC;AAC9D,UAAI,OAAOA,qBAAP,KAAiC,UAArC,EACC1K,MAAM,CAAC4C,kBAAP,GAA4B8H,qBAA5B,CADD,KAGC,MAAM,IAAIlS,KAAJ,CAAUC,MAAM,CAACC,KAAK,CAACC,YAAP,EAAqB,SAAQ+R,qBAAR,GAA+B,oBAA/B,CAArB,CAAhB,CAAN;AACD,KALD;;AAOA,SAAKC,oBAAL,GAA4B,YAAY;AAAE,aAAO3K,MAAM,CAAC6C,gBAAd;AAAiC,KAA3E;;AACA,SAAK+H,oBAAL,GAA4B,UAAUC,mBAAV,EAA+B;AAC1D,UAAI,OAAOA,mBAAP,KAA+B,UAAnC,EACC7K,MAAM,CAAC6C,gBAAP,GAA0BgI,mBAA1B,CADD,KAGC,MAAM,IAAIrS,KAAJ,CAAUC,MAAM,CAACC,KAAK,CAACC,YAAP,EAAqB,SAAQkS,mBAAR,GAA6B,kBAA7B,CAArB,CAAhB,CAAN;AACD,KALD;AAOA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA0CA,SAAKzH,OAAL,GAAe,UAAUX,cAAV,EAA0B;AACxCA,MAAAA,cAAc,GAAGA,cAAc,IAAI,EAAnC;AACAtK,MAAAA,QAAQ,CAACsK,cAAD,EAAiB;AACxBzB,QAAAA,OAAO,EAAE,QADe;AAExB1E,QAAAA,QAAQ,EAAE,QAFc;AAGxBC,QAAAA,QAAQ,EAAE,QAHc;AAIxBP,QAAAA,WAAW,EAAE,QAJW;AAKxB0B,QAAAA,iBAAiB,EAAE,QALK;AAMxBF,QAAAA,YAAY,EAAE,SANU;AAOxBsH,QAAAA,MAAM,EAAE,SAPgB;AAQxBhB,QAAAA,iBAAiB,EAAE,QARK;AASxBF,QAAAA,SAAS,EAAE,UATa;AAUxBG,QAAAA,SAAS,EAAE,UAVa;AAWxB+G,QAAAA,KAAK,EAAE,QAXiB;AAYxBC,QAAAA,KAAK,EAAE,QAZiB;AAaxBlP,QAAAA,WAAW,EAAE;AAbW,OAAjB,CAAR,CAFwC,CAkBxC;;AACA,UAAI4G,cAAc,CAAC/E,iBAAf,KAAqC9B,SAAzC,EACC6G,cAAc,CAAC/E,iBAAf,GAAmC,EAAnC;;AAED,UAAI+E,cAAc,CAAC5G,WAAf,GAA6B,CAA7B,IAAkC4G,cAAc,CAAC5G,WAAf,GAA6B,CAAnE,EAAsE;AACrE,cAAM,IAAIrD,KAAJ,CAAUC,MAAM,CAACC,KAAK,CAACqB,gBAAP,EAAyB,CAAC0I,cAAc,CAAC5G,WAAhB,EAA6B,4BAA7B,CAAzB,CAAhB,CAAN;AACA;;AAED,UAAI4G,cAAc,CAAC5G,WAAf,KAA+BD,SAAnC,EAA8C;AAC7C6G,QAAAA,cAAc,CAACsG,mBAAf,GAAqC,KAArC;AACAtG,QAAAA,cAAc,CAAC5G,WAAf,GAA6B,CAA7B;AACA,OAHD,MAGO;AACN4G,QAAAA,cAAc,CAACsG,mBAAf,GAAqC,IAArC;AACA,OA/BuC,CAiCxC;;;AACA,UAAItG,cAAc,CAAClG,QAAf,KAA4BX,SAA5B,IAAyC6G,cAAc,CAACnG,QAAf,KAA4BV,SAAzE,EACC,MAAM,IAAIpD,KAAJ,CAAUC,MAAM,CAACC,KAAK,CAACqB,gBAAP,EAAyB,CAAC0I,cAAc,CAAClG,QAAhB,EAA0B,yBAA1B,CAAzB,CAAhB,CAAN;;AAED,UAAIkG,cAAc,CAACzG,WAAnB,EAAgC;AAC/B,YAAI,EAAEyG,cAAc,CAACzG,WAAf,YAAsC2C,OAAxC,CAAJ,EACC,MAAM,IAAInG,KAAJ,CAAUC,MAAM,CAACC,KAAK,CAACC,YAAP,EAAqB,CAAC8J,cAAc,CAACzG,WAAhB,EAA6B,4BAA7B,CAArB,CAAhB,CAAN,CAF8B,CAG/B;AACA;;AACAyG,QAAAA,cAAc,CAACzG,WAAf,CAA2BgP,aAA3B;AAEA,YAAI,OAAOvI,cAAc,CAACzG,WAAf,CAA2BC,eAAlC,KAAsD,WAA1D,EACC,MAAM,IAAIzD,KAAJ,CAAUC,MAAM,CAACC,KAAK,CAACC,YAAP,EAAqB,SAAQ8J,cAAc,CAACzG,WAAf,CAA2BC,eAAnC,GAAoD,4CAApD,CAArB,CAAhB,CAAN;AACD;;AACD,UAAI,OAAOwG,cAAc,CAACjF,YAAtB,KAAuC,WAA3C,EACCiF,cAAc,CAACjF,YAAf,GAA8B,IAA9B;;AACD,UAAIiF,cAAc,CAACqI,KAAnB,EAA0B;AAEzB,YAAI,EAAErI,cAAc,CAACqI,KAAf,YAAgCpP,KAAlC,CAAJ,EACC,MAAM,IAAIlD,KAAJ,CAAUC,MAAM,CAACC,KAAK,CAACqB,gBAAP,EAAyB,CAAC0I,cAAc,CAACqI,KAAhB,EAAuB,sBAAvB,CAAzB,CAAhB,CAAN;AACD,YAAIrI,cAAc,CAACqI,KAAf,CAAqBtQ,MAArB,GAA8B,CAAlC,EACC,MAAM,IAAIhC,KAAJ,CAAUC,MAAM,CAACC,KAAK,CAACqB,gBAAP,EAAyB,CAAC0I,cAAc,CAACqI,KAAhB,EAAuB,sBAAvB,CAAzB,CAAhB,CAAN;AAED,YAAIG,SAAS,GAAG,KAAhB;;AACA,aAAK,IAAI1Q,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGkI,cAAc,CAACqI,KAAf,CAAqBtQ,MAAzC,EAAiDD,CAAC,EAAlD,EAAsD;AACrD,cAAI,OAAOkI,cAAc,CAACqI,KAAf,CAAqBvQ,CAArB,CAAP,KAAmC,QAAvC,EACC,MAAM,IAAI/B,KAAJ,CAAUC,MAAM,CAACC,KAAK,CAACC,YAAP,EAAqB,SAAQ8J,cAAc,CAACqI,KAAf,CAAqBvQ,CAArB,CAAR,GAAiC,0BAA0BA,CAA1B,GAA8B,GAA/D,CAArB,CAAhB,CAAN;;AACD,cAAI,qDAAqD2Q,IAArD,CAA0DzI,cAAc,CAACqI,KAAf,CAAqBvQ,CAArB,CAA1D,CAAJ,EAAwF;AACvF,gBAAIA,CAAC,IAAI,CAAT,EAAY;AACX0Q,cAAAA,SAAS,GAAG,IAAZ;AACA,aAFD,MAEO,IAAI,CAACA,SAAL,EAAgB;AACtB,oBAAM,IAAIzS,KAAJ,CAAUC,MAAM,CAACC,KAAK,CAACqB,gBAAP,EAAyB,CAAC0I,cAAc,CAACqI,KAAf,CAAqBvQ,CAArB,CAAD,EAA0B,0BAA0BA,CAA1B,GAA8B,GAAxD,CAAzB,CAAhB,CAAN;AACA;AACD,WAND,MAMO,IAAI0Q,SAAJ,EAAe;AACrB,kBAAM,IAAIzS,KAAJ,CAAUC,MAAM,CAACC,KAAK,CAACqB,gBAAP,EAAyB,CAAC0I,cAAc,CAACqI,KAAf,CAAqBvQ,CAArB,CAAD,EAA0B,0BAA0BA,CAA1B,GAA8B,GAAxD,CAAzB,CAAhB,CAAN;AACA;AACD;;AAED,YAAI,CAAC0Q,SAAL,EAAgB;AACf,cAAI,CAACxI,cAAc,CAACsI,KAApB,EACC,MAAM,IAAIvS,KAAJ,CAAUC,MAAM,CAACC,KAAK,CAACqB,gBAAP,EAAyB,CAAC0I,cAAc,CAACsI,KAAhB,EAAuB,sBAAvB,CAAzB,CAAhB,CAAN;AACD,cAAI,EAAEtI,cAAc,CAACsI,KAAf,YAAgCrP,KAAlC,CAAJ,EACC,MAAM,IAAIlD,KAAJ,CAAUC,MAAM,CAACC,KAAK,CAACqB,gBAAP,EAAyB,CAAC0I,cAAc,CAACsI,KAAhB,EAAuB,sBAAvB,CAAzB,CAAhB,CAAN;AACD,cAAItI,cAAc,CAACqI,KAAf,CAAqBtQ,MAArB,IAA+BiI,cAAc,CAACsI,KAAf,CAAqBvQ,MAAxD,EACC,MAAM,IAAIhC,KAAJ,CAAUC,MAAM,CAACC,KAAK,CAACqB,gBAAP,EAAyB,CAAC0I,cAAc,CAACsI,KAAhB,EAAuB,sBAAvB,CAAzB,CAAhB,CAAN;AAEDtI,UAAAA,cAAc,CAACc,IAAf,GAAsB,EAAtB;;AAEA,eAAK,IAAIhJ,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGkI,cAAc,CAACqI,KAAf,CAAqBtQ,MAAzC,EAAiDD,CAAC,EAAlD,EAAsD;AACrD,gBAAI,OAAOkI,cAAc,CAACsI,KAAf,CAAqBxQ,CAArB,CAAP,KAAmC,QAAnC,IAA+CkI,cAAc,CAACsI,KAAf,CAAqBxQ,CAArB,IAA0B,CAA7E,EACC,MAAM,IAAI/B,KAAJ,CAAUC,MAAM,CAACC,KAAK,CAACC,YAAP,EAAqB,SAAQ8J,cAAc,CAACsI,KAAf,CAAqBxQ,CAArB,CAAR,GAAiC,0BAA0BA,CAA1B,GAA8B,GAA/D,CAArB,CAAhB,CAAN;AACD,gBAAIoH,IAAI,GAAGc,cAAc,CAACqI,KAAf,CAAqBvQ,CAArB,CAAX;AACA,gBAAIqH,IAAI,GAAGa,cAAc,CAACsI,KAAf,CAAqBxQ,CAArB,CAAX;AAEA,gBAAI4Q,IAAI,GAAIxJ,IAAI,CAAChH,OAAL,CAAa,GAAb,KAAqB,CAAC,CAAlC;AACA+G,YAAAA,GAAG,GAAG,WAAWyJ,IAAI,GAAG,MAAMxJ,IAAN,GAAa,GAAhB,GAAsBA,IAArC,IAA6C,GAA7C,GAAmDC,IAAnD,GAA0DC,IAAhE;AACAY,YAAAA,cAAc,CAACc,IAAf,CAAoBqC,IAApB,CAAyBlE,GAAzB;AACA;AACD,SApBD,MAoBO;AACNe,UAAAA,cAAc,CAACc,IAAf,GAAsBd,cAAc,CAACqI,KAArC;AACA;AACD;;AAED9K,MAAAA,MAAM,CAACoD,OAAP,CAAeX,cAAf;AACA,KAjGD;AAmGA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgCA,SAAKgB,SAAL,GAAiB,UAAUC,MAAV,EAAkBC,gBAAlB,EAAoC;AACpD,UAAI,OAAOD,MAAP,KAAkB,QAAtB,EACC,MAAM,IAAIlL,KAAJ,CAAU,sBAAsBkL,MAAhC,CAAN;AACDC,MAAAA,gBAAgB,GAAGA,gBAAgB,IAAI,EAAvC;AACAxL,MAAAA,QAAQ,CAACwL,gBAAD,EAAmB;AAC1B/G,QAAAA,GAAG,EAAE,QADqB;AAE1BkH,QAAAA,iBAAiB,EAAE,QAFO;AAG1BF,QAAAA,SAAS,EAAE,UAHe;AAI1BG,QAAAA,SAAS,EAAE,UAJe;AAK1B/C,QAAAA,OAAO,EAAE;AALiB,OAAnB,CAAR;AAOA,UAAI2C,gBAAgB,CAAC3C,OAAjB,IAA4B,CAAC2C,gBAAgB,CAACI,SAAlD,EACC,MAAM,IAAIvL,KAAJ,CAAU,gEAAV,CAAN;AACD,UAAI,OAAOmL,gBAAgB,CAAC/G,GAAxB,KAAgC,WAAhC,IACA,EAAE+G,gBAAgB,CAAC/G,GAAjB,KAAyB,CAAzB,IAA8B+G,gBAAgB,CAAC/G,GAAjB,KAAyB,CAAvD,IAA4D+G,gBAAgB,CAAC/G,GAAjB,KAAyB,CAAvF,CADJ,EAEC,MAAM,IAAIpE,KAAJ,CAAUC,MAAM,CAACC,KAAK,CAACqB,gBAAP,EAAyB,CAAC4J,gBAAgB,CAAC/G,GAAlB,EAAuB,sBAAvB,CAAzB,CAAhB,CAAN;AACDoD,MAAAA,MAAM,CAACyD,SAAP,CAAiBC,MAAjB,EAAyBC,gBAAzB;AACA,KAjBD;AAmBA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4BA,SAAKU,WAAL,GAAmB,UAAUX,MAAV,EAAkBY,kBAAlB,EAAsC;AACxD,UAAI,OAAOZ,MAAP,KAAkB,QAAtB,EACC,MAAM,IAAIlL,KAAJ,CAAU,sBAAsBkL,MAAhC,CAAN;AACDY,MAAAA,kBAAkB,GAAGA,kBAAkB,IAAI,EAA3C;AACAnM,MAAAA,QAAQ,CAACmM,kBAAD,EAAqB;AAC5BR,QAAAA,iBAAiB,EAAE,QADS;AAE5BF,QAAAA,SAAS,EAAE,UAFiB;AAG5BG,QAAAA,SAAS,EAAE,UAHiB;AAI5B/C,QAAAA,OAAO,EAAE;AAJmB,OAArB,CAAR;AAMA,UAAIsD,kBAAkB,CAACtD,OAAnB,IAA8B,CAACsD,kBAAkB,CAACP,SAAtD,EACC,MAAM,IAAIvL,KAAJ,CAAU,kEAAV,CAAN;AACDwH,MAAAA,MAAM,CAACqE,WAAP,CAAmBX,MAAnB,EAA2BY,kBAA3B;AACA,KAbD;AAeA;;;;;;;;;;;AASA,SAAKvD,IAAL,GAAY,UAAUrC,OAAV,EAAmB;AAC9B,UAAI,EAAEA,OAAO,YAAYC,OAArB,CAAJ,EACC,MAAM,IAAInG,KAAJ,CAAU,8BAA6BkG,OAA7B,CAAV,CAAN;AACD,UAAI,OAAOA,OAAO,CAACzC,eAAf,KAAmC,WAAvC,EACC,MAAM,IAAIzD,KAAJ,CAAU,+CAA+CkG,OAAO,CAACzC,eAAjE,CAAN;AAED+D,MAAAA,MAAM,CAACe,IAAP,CAAYrC,OAAZ;AACA,KAPD;AASA;;;;;;;;;AAOA,SAAK8F,UAAL,GAAkB,YAAY;AAC7BxE,MAAAA,MAAM,CAACwE,UAAP;AACA,KAFD;AAIA;;;;;;;;;AAOA,SAAKC,WAAL,GAAmB,YAAY;AAC9B,aAAOzE,MAAM,CAACyE,WAAP,EAAP;AACA,KAFD;AAIA;;;;;;;;AAMA,SAAKE,UAAL,GAAkB,YAAY;AAC7B3E,MAAAA,MAAM,CAAC2E,UAAP;AACA,KAFD;AAIA;;;;;;;;AAMA,SAAKC,SAAL,GAAiB,YAAY;AAC5B5E,MAAAA,MAAM,CAAC4E,SAAP;AACA,KAFD;;AAIA,SAAKwG,WAAL,GAAmB,YAAY;AAC9B,aAAOpL,MAAM,CAACuC,SAAd;AACA,KAFD;AAGA,GA/XD;;AAiYA+G,EAAAA,MAAM,CAACjO,SAAP,GAAmB;AAClB,QAAIsG,IAAJ,GAAW;AAAE,aAAO,KAAKgI,QAAL,EAAP;AAAyB,KADpB;;AAElB,QAAIhI,IAAJ,CAAS0J,OAAT,EAAkB;AAAE,WAAKzB,QAAL,CAAcyB,OAAd;AAAyB,KAF3B;;AAIlB,QAAIzJ,IAAJ,GAAW;AAAE,aAAO,KAAKiI,QAAL,EAAP;AAAyB,KAJpB;;AAKlB,QAAIjI,IAAJ,CAAS0J,OAAT,EAAkB;AAAE,WAAKxB,QAAL,CAAcwB,OAAd;AAAyB,KAL3B;;AAOlB,QAAIzJ,IAAJ,GAAW;AAAE,aAAO,KAAKkI,QAAL,EAAP;AAAyB,KAPpB;;AAQlB,QAAIlI,IAAJ,CAAS0J,OAAT,EAAkB;AAAE,WAAKvB,QAAL,CAAcuB,OAAd;AAAyB,KAR3B;;AAUlB,QAAIxP,QAAJ,GAAe;AAAE,aAAO,KAAKoO,YAAL,EAAP;AAA6B,KAV5B;;AAWlB,QAAIpO,QAAJ,CAAayP,WAAb,EAA0B;AAAE,WAAKpB,YAAL,CAAkBoB,WAAlB;AAAiC,KAX3C;;AAalB,QAAI7I,gBAAJ,GAAuB;AAAE,aAAO,KAAK0H,oBAAL,EAAP;AAAqC,KAb5C;;AAclB,QAAI1H,gBAAJ,CAAqB4H,mBAArB,EAA0C;AAAE,WAAKD,oBAAL,CAA0BC,mBAA1B;AAAiD,KAd3E;;AAgBlB,QAAI3H,kBAAJ,GAAyB;AAAE,aAAO,KAAK4H,sBAAL,EAAP;AAAuC,KAhBhD;;AAiBlB,QAAI5H,kBAAJ,CAAuB8H,qBAAvB,EAA8C;AAAE,WAAKD,sBAAL,CAA4BC,qBAA5B;AAAqD,KAjBnF;;AAmBlB,QAAI7H,gBAAJ,GAAuB;AAAE,aAAO,KAAK8H,oBAAL,EAAP;AAAqC,KAnB5C;;AAoBlB,QAAI9H,gBAAJ,CAAqBgI,mBAArB,EAA0C;AAAE,WAAKD,oBAAL,CAA0BC,mBAA1B;AAAiD;;AApB3E,GAAnB;AAuBA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkCA,MAAIlM,OAAO,GAAG,SAAVA,OAAU,CAAU8M,UAAV,EAAsB;AACnC,QAAIC,OAAJ;;AACA,QAAI,OAAOD,UAAP,KAAsB,QAAtB,IACAA,UAAU,YAAY1O,WADtB,IAEA0O,UAAU,YAAYE,SAFtB,IAGAF,UAAU,YAAYrP,UAHtB,IAIAqP,UAAU,YAAYG,UAJtB,IAKAH,UAAU,YAAYI,WALtB,IAMAJ,UAAU,YAAYK,UANtB,IAOAL,UAAU,YAAYM,WAPtB,IAQAN,UAAU,YAAYO,YARtB,IASAP,UAAU,YAAYQ,YAT1B,EAUE;AACDP,MAAAA,OAAO,GAAGD,UAAV;AACA,KAZD,MAYO;AACN,YAAOhT,MAAM,CAACC,KAAK,CAACqB,gBAAP,EAAyB,CAAC0R,UAAD,EAAa,YAAb,CAAzB,CAAb;AACA;;AAED,SAAKS,iBAAL,GAAyB,YAAY;AACpC,UAAI,OAAOR,OAAP,KAAmB,QAAvB,EACC,OAAOA,OAAP,CADD,KAGC,OAAOjN,SAAS,CAACiN,OAAD,EAAU,CAAV,EAAaA,OAAO,CAAClR,MAArB,CAAhB;AACD,KALD;;AAOA,SAAK2R,gBAAL,GAAwB,YAAY;AACnC,UAAI,OAAOT,OAAP,KAAmB,QAAvB,EAAiC;AAChC,YAAI1O,MAAM,GAAG,IAAID,WAAJ,CAAgBjB,UAAU,CAAC4P,OAAD,CAA1B,CAAb;AACA,YAAItO,UAAU,GAAG,IAAIhB,UAAJ,CAAeY,MAAf,CAAjB;AACA+B,QAAAA,YAAY,CAAC2M,OAAD,EAAUtO,UAAV,EAAsB,CAAtB,CAAZ;AAEA,eAAOA,UAAP;AACA,OAND,MAMO;AACN,eAAOsO,OAAP;AACA;;AAAA;AACD,KAVD;;AAYA,QAAIzP,eAAe,GAAGL,SAAtB;;AACA,SAAKwQ,mBAAL,GAA2B,YAAY;AAAE,aAAOnQ,eAAP;AAAyB,KAAlE;;AACA,SAAKoQ,mBAAL,GAA2B,UAAUC,kBAAV,EAA8B;AACxD,UAAI,OAAOA,kBAAP,KAA8B,QAAlC,EACCrQ,eAAe,GAAGqQ,kBAAlB,CADD,KAGC,MAAM,IAAI9T,KAAJ,CAAUC,MAAM,CAACC,KAAK,CAACqB,gBAAP,EAAyB,CAACuS,kBAAD,EAAqB,oBAArB,CAAzB,CAAhB,CAAN;AACD,KALD;;AAOA,QAAI1P,GAAG,GAAG,CAAV;;AACA,SAAK2P,OAAL,GAAe,YAAY;AAAE,aAAO3P,GAAP;AAAa,KAA1C;;AACA,SAAK4P,OAAL,GAAe,UAAUC,MAAV,EAAkB;AAChC,UAAIA,MAAM,KAAK,CAAX,IAAgBA,MAAM,KAAK,CAA3B,IAAgCA,MAAM,KAAK,CAA/C,EACC7P,GAAG,GAAG6P,MAAN,CADD,KAGC,MAAM,IAAIjU,KAAJ,CAAU,sBAAsBiU,MAAhC,CAAN;AACD,KALD;;AAOA,QAAI5P,QAAQ,GAAG,KAAf;;AACA,SAAK6P,YAAL,GAAoB,YAAY;AAAE,aAAO7P,QAAP;AAAkB,KAApD;;AACA,SAAK8P,YAAL,GAAoB,UAAUC,WAAV,EAAuB;AAC1C,UAAI,OAAOA,WAAP,KAAuB,SAA3B,EACC/P,QAAQ,GAAG+P,WAAX,CADD,KAGC,MAAM,IAAIpU,KAAJ,CAAUC,MAAM,CAACC,KAAK,CAACqB,gBAAP,EAAyB,CAAC6S,WAAD,EAAc,aAAd,CAAzB,CAAhB,CAAN;AACD,KALD;;AAOA,QAAIjQ,SAAS,GAAG,KAAhB;;AACA,SAAKkQ,aAAL,GAAqB,YAAY;AAAE,aAAOlQ,SAAP;AAAmB,KAAtD;;AACA,SAAKmQ,aAAL,GAAqB,UAAUC,YAAV,EAAwB;AAAEpQ,MAAAA,SAAS,GAAGoQ,YAAZ;AAA2B,KAA1E;AACA,GAnED;;AAqEApO,EAAAA,OAAO,CAACtD,SAAR,GAAoB;AACnB,QAAI2R,aAAJ,GAAoB;AAAE,aAAO,KAAKd,iBAAL,EAAP;AAAkC,KADrC;;AAEnB,QAAI/P,YAAJ,GAAmB;AAAE,aAAO,KAAKgQ,gBAAL,EAAP;AAAiC,KAFnC;;AAInB,QAAIlQ,eAAJ,GAAsB;AAAE,aAAO,KAAKmQ,mBAAL,EAAP;AAAoC,KAJzC;;AAKnB,QAAInQ,eAAJ,CAAoBqQ,kBAApB,EAAwC;AAAE,WAAKD,mBAAL,CAAyBC,kBAAzB;AAA+C,KALtE;;AAOnB,QAAI1P,GAAJ,GAAU;AAAE,aAAO,KAAK2P,OAAL,EAAP;AAAwB,KAPjB;;AAQnB,QAAI3P,GAAJ,CAAQ6P,MAAR,EAAgB;AAAE,WAAKD,OAAL,CAAaC,MAAb;AAAuB,KARtB;;AAUnB,QAAI5P,QAAJ,GAAe;AAAE,aAAO,KAAK6P,YAAL,EAAP;AAA6B,KAV3B;;AAWnB,QAAI7P,QAAJ,CAAa+P,WAAb,EAA0B;AAAE,WAAKD,YAAL,CAAkBC,WAAlB;AAAiC,KAX1C;;AAanB,QAAIjQ,SAAJ,GAAgB;AAAE,aAAO,KAAKkQ,aAAL,EAAP;AAA8B,KAb7B;;AAcnB,QAAIlQ,SAAJ,CAAcoQ,YAAd,EAA4B;AAAE,WAAKD,aAAL,CAAmBC,YAAnB;AAAmC;;AAd9C,GAApB,CAz7D8B,CA08D9B;;AACA,SAAO;AACNzD,IAAAA,MAAM,EAAEA,MADF;AAEN3K,IAAAA,OAAO,EAAEA;AAFH,GAAP;AAIA,CA/8DW,CA+8DTsB,MA/8DS,CAAZ;;AAi9DA,SAASlJ,IAAT", "sourcesContent": ["/*******************************************************************************\r\n * Copyright (c) 2013 IBM Corp.\r\n *\r\n * All rights reserved. This program and the accompanying materials\r\n * are made available under the terms of the Eclipse Public License v1.0\r\n * and Eclipse Distribution License v1.0 which accompany this distribution. \r\n *\r\n * The Eclipse Public License is available at \r\n *    http://www.eclipse.org/legal/epl-v10.html\r\n * and the Eclipse Distribution License is available at \r\n *   http://www.eclipse.org/org/documents/edl-v10.php.\r\n *\r\n * Contributors:\r\n *    <PERSON> - initial API and implementation and initial documentation\r\n *******************************************************************************/\r\n\r\n\r\n// Only expose a single object name in the global namespace.\r\n// Everything must go through this module. Global Paho.MQTT module\r\n// only has a single public function, client, which returns\r\n// a Paho.MQTT client object given connection details.\r\n\r\n/**\r\n * Send and receive messages using web browsers.\r\n * <p> \r\n * This programming interface lets a JavaScript client application use the MQTT V3.1 or\r\n * V3.1.1 protocol to connect to an MQTT-supporting messaging server.\r\n *  \r\n * The function supported includes:\r\n * <ol>\r\n * <li>Connecting to and disconnecting from a server. The server is identified by its host name and port number. \r\n * <li>Specifying options that relate to the communications link with the server, \r\n * for example the frequency of keep-alive heartbeats, and whether SSL/TLS is required.\r\n * <li>Subscribing to and receiving messages from MQTT Topics.\r\n * <li>Publishing messages to MQTT Topics.\r\n * </ol>\r\n * <p>\r\n * The API consists of two main objects:\r\n * <dl>\r\n * <dt><b>{@link Paho.MQTT.Client}</b></dt>\r\n * <dd>This contains methods that provide the functionality of the API,\r\n * including provision of callbacks that notify the application when a message\r\n * arrives from or is delivered to the messaging server,\r\n * or when the status of its connection to the messaging server changes.</dd>\r\n * <dt><b>{@link Paho.MQTT.Message}</b></dt>\r\n * <dd>This encapsulates the payload of the message along with various attributes\r\n * associated with its delivery, in particular the destination to which it has\r\n * been (or is about to be) sent.</dd>\r\n * </dl> \r\n * <p>\r\n * The programming interface validates parameters passed to it, and will throw\r\n * an Error containing an error message intended for developer use, if it detects\r\n * an error with any parameter.\r\n * <p>\r\n * Example:\r\n * \r\n * <code><pre>\r\nclient = new Paho.MQTT.Client(location.hostname, Number(location.port), \"clientId\");\r\nclient.onConnectionLost = onConnectionLost;\r\nclient.onMessageArrived = onMessageArrived;\r\nclient.connect({onSuccess:onConnect});\r\n\r\nfunction onConnect() {\r\n  // Once a connection has been made, make a subscription and send a message.\r\n  console.log(\"onConnect\");\r\n  client.subscribe(\"/World\");\r\n  message = new Paho.MQTT.Message(\"Hello\");\r\n  message.destinationName = \"/World\";\r\n  client.send(message); \r\n};\r\nfunction onConnectionLost(responseObject) {\r\n  if (responseObject.errorCode !== 0)\r\n\tconsole.log(\"onConnectionLost:\"+responseObject.errorMessage);\r\n};\r\nfunction onMessageArrived(message) {\r\n  console.log(\"onMessageArrived:\"+message.payloadString);\r\n  client.disconnect(); \r\n};\t\r\n * </pre></code>\r\n * @namespace Paho.MQTT \r\n */\r\nvar Paho = {}\r\nif (typeof Paho === \"undefined\") {\r\n\tPaho = {};\r\n}\r\n\r\nPaho.MQTT = (function (global) {\r\n\r\n\t// Private variables below, these are only visible inside the function closure\r\n\t// which is used to define the module. \r\n\r\n\tvar version = \"@VERSION@\";\r\n\tvar buildLevel = \"@BUILDLEVEL@\";\r\n\r\n\t/** \r\n\t * Unique message type identifiers, with associated\r\n\t * associated integer values.\r\n\t * @private \r\n\t */\r\n\tvar MESSAGE_TYPE = {\r\n\t\tCONNECT: 1,\r\n\t\tCONNACK: 2,\r\n\t\tPUBLISH: 3,\r\n\t\tPUBACK: 4,\r\n\t\tPUBREC: 5,\r\n\t\tPUBREL: 6,\r\n\t\tPUBCOMP: 7,\r\n\t\tSUBSCRIBE: 8,\r\n\t\tSUBACK: 9,\r\n\t\tUNSUBSCRIBE: 10,\r\n\t\tUNSUBACK: 11,\r\n\t\tPINGREQ: 12,\r\n\t\tPINGRESP: 13,\r\n\t\tDISCONNECT: 14\r\n\t};\r\n\r\n\t// Collection of utility methods used to simplify module code \r\n\t// and promote the DRY pattern.  \r\n\r\n\t/**\r\n\t * Validate an object's parameter names to ensure they \r\n\t * match a list of expected variables name for this option\r\n\t * type. Used to ensure option object passed into the API don't\r\n\t * contain erroneous parameters.\r\n\t * @param {Object} obj - User options object\r\n\t * @param {Object} keys - valid keys and types that may exist in obj. \r\n\t * @throws {Error} Invalid option parameter found. \r\n\t * @private \r\n\t */\r\n\tvar validate = function (obj, keys) {\r\n\t\tfor (var key in obj) {\r\n\t\t\tif (obj.hasOwnProperty(key)) {\r\n\t\t\t\tif (keys.hasOwnProperty(key)) {\r\n\t\t\t\t\tif (typeof obj[key] !== keys[key])\r\n\t\t\t\t\t\tthrow new Error(format(ERROR.INVALID_TYPE, [typeof obj[key], key]));\r\n\t\t\t\t} else {\r\n\t\t\t\t\tvar errorStr = \"Unknown property, \" + key + \". Valid properties are:\";\r\n\t\t\t\t\tfor (var key in keys)\r\n\t\t\t\t\t\tif (keys.hasOwnProperty(key))\r\n\t\t\t\t\t\t\terrorStr = errorStr + \" \" + key;\r\n\t\t\t\t\tthrow new Error(errorStr);\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t};\r\n\r\n\t/**\r\n\t * Return a new function which runs the user function bound\r\n\t * to a fixed scope. \r\n\t * @param {function} User function\r\n\t * @param {object} Function scope  \r\n\t * @return {function} User function bound to another scope\r\n\t * @private \r\n\t */\r\n\tvar scope = function (f, scope) {\r\n\t\treturn function () {\r\n\t\t\treturn f.apply(scope, arguments);\r\n\t\t};\r\n\t};\r\n\r\n\t/** \r\n\t * Unique message type identifiers, with associated\r\n\t * associated integer values.\r\n\t * @private \r\n\t */\r\n\tvar ERROR = {\r\n\t\tOK: { code: 0, text: \"AMQJSC0000I OK.\" },\r\n\t\tCONNECT_TIMEOUT: { code: 1, text: \"AMQJSC0001E Connect timed out.\" },\r\n\t\tSUBSCRIBE_TIMEOUT: { code: 2, text: \"AMQJS0002E Subscribe timed out.\" },\r\n\t\tUNSUBSCRIBE_TIMEOUT: { code: 3, text: \"AMQJS0003E Unsubscribe timed out.\" },\r\n\t\tPING_TIMEOUT: { code: 4, text: \"AMQJS0004E Ping timed out.\" },\r\n\t\tINTERNAL_ERROR: { code: 5, text: \"AMQJS0005E Internal error.\" },\r\n\t\tCONNACK_RETURNCODE: { code: 6, text: \"AMQJS0006E Bad Connack return code:{0} {1}.\" },\r\n\t\tSOCKET_ERROR: { code: 7, text: \"AMQJS0007E Socket error:{0}.\" },\r\n\t\tSOCKET_CLOSE: { code: 8, text: \"AMQJS0008I Socket closed.\" },\r\n\t\tMALFORMED_UTF: { code: 9, text: \"AMQJS0009E Malformed UTF data:{0} {1} {2}.\" },\r\n\t\tUNSUPPORTED: { code: 10, text: \"AMQJS0010E {0} is not supported by this browser.\" },\r\n\t\tINVALID_STATE: { code: 11, text: \"AMQJS0011E Invalid state {0}.\" },\r\n\t\tINVALID_TYPE: { code: 12, text: \"AMQJS0012E Invalid type {0} for {1}.\" },\r\n\t\tINVALID_ARGUMENT: { code: 13, text: \"AMQJS0013E Invalid argument {0} for {1}.\" },\r\n\t\tUNSUPPORTED_OPERATION: { code: 14, text: \"AMQJS0014E Unsupported operation.\" },\r\n\t\tINVALID_STORED_DATA: { code: 15, text: \"AMQJS0015E Invalid data in local storage key={0} value={1}.\" },\r\n\t\tINVALID_MQTT_MESSAGE_TYPE: { code: 16, text: \"AMQJS0016E Invalid MQTT message type {0}.\" },\r\n\t\tMALFORMED_UNICODE: { code: 17, text: \"AMQJS0017E Malformed Unicode string:{0} {1}.\" },\r\n\t};\r\n\r\n\t/** CONNACK RC Meaning. */\r\n\tvar CONNACK_RC = {\r\n\t\t0: \"Connection Accepted\",\r\n\t\t1: \"Connection Refused: unacceptable protocol version\",\r\n\t\t2: \"Connection Refused: identifier rejected\",\r\n\t\t3: \"Connection Refused: server unavailable\",\r\n\t\t4: \"Connection Refused: bad user name or password\",\r\n\t\t5: \"Connection Refused: not authorized\"\r\n\t};\r\n\r\n\t/**\r\n\t * Format an error message text.\r\n\t * @private\r\n\t * @param {error} ERROR.KEY value above.\r\n\t * @param {substitutions} [array] substituted into the text.\r\n\t * @return the text with the substitutions made.\r\n\t */\r\n\tvar format = function (error, substitutions) {\r\n\t\tvar text = error.text;\r\n\t\tif (substitutions) {\r\n\t\t\tfor (var i = 0; i < substitutions.length; i++) {\r\n\t\t\t\tvar field = \"{\" + i + \"}\";\r\n\t\t\t\tvar start = text.indexOf(field);\r\n\t\t\t\tif (start > 0) {\r\n\t\t\t\t\tvar part1 = text.substring(0, start);\r\n\t\t\t\t\tvar part2 = text.substring(start + field.length);\r\n\t\t\t\t\ttext = part1 + substitutions[i] + part2;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t\treturn text;\r\n\t};\r\n\r\n\t//MQTT protocol and version          6    M    Q    I    s    d    p    3\r\n\tvar MqttProtoIdentifierv3 = [0x00, 0x06, 0x4d, 0x51, 0x49, 0x73, 0x64, 0x70, 0x03];\r\n\t//MQTT proto/version for 311         4    M    Q    T    T    4\r\n\tvar MqttProtoIdentifierv4 = [0x00, 0x04, 0x4d, 0x51, 0x54, 0x54, 0x04];\r\n\r\n\t/**\r\n\t * Construct an MQTT wire protocol message.\r\n\t * @param type MQTT packet type.\r\n\t * @param options optional wire message attributes.\r\n\t * \r\n\t * Optional properties\r\n\t * \r\n\t * messageIdentifier: message ID in the range [0..65535]\r\n\t * payloadMessage:\tApplication Message - PUBLISH only\r\n\t * connectStrings:\tarray of 0 or more Strings to be put into the CONNECT payload\r\n\t * topics:\t\t\tarray of strings (SUBSCRIBE, UNSUBSCRIBE)\r\n\t * requestQoS:\t\tarray of QoS values [0..2]\r\n\t *  \r\n\t * \"Flag\" properties \r\n\t * cleanSession:\ttrue if present / false if absent (CONNECT)\r\n\t * willMessage:  \ttrue if present / false if absent (CONNECT)\r\n\t * isRetained:\t\ttrue if present / false if absent (CONNECT)\r\n\t * userName:\t\ttrue if present / false if absent (CONNECT)\r\n\t * password:\t\ttrue if present / false if absent (CONNECT)\r\n\t * keepAliveInterval:\tinteger [0..65535]  (CONNECT)\r\n\t *\r\n\t * @private\r\n\t * @ignore\r\n\t */\r\n\tvar WireMessage = function (type, options) {\r\n\t\tthis.type = type;\r\n\t\tfor (var name in options) {\r\n\t\t\tif (options.hasOwnProperty(name)) {\r\n\t\t\t\tthis[name] = options[name];\r\n\t\t\t}\r\n\t\t}\r\n\t};\r\n\r\n\tWireMessage.prototype.encode = function () {\r\n\t\t// Compute the first byte of the fixed header\r\n\t\tvar first = ((this.type & 0x0f) << 4);\r\n\r\n\t\t/*\r\n\t\t * Now calculate the length of the variable header + payload by adding up the lengths\r\n\t\t * of all the component parts\r\n\t\t */\r\n\r\n\t\tvar remLength = 0;\r\n\t\tvar topicStrLength = new Array();\r\n\r\n\t\t// if the message contains a messageIdentifier then we need two bytes for that\r\n\t\tif (this.messageIdentifier != undefined)\r\n\t\t\tremLength += 2;\r\n\r\n\t\tswitch (this.type) {\r\n\t\t\t// If this a Connect then we need to include 12 bytes for its header\r\n\t\t\tcase MESSAGE_TYPE.CONNECT:\r\n\t\t\t\tswitch (this.mqttVersion) {\r\n\t\t\t\t\tcase 3:\r\n\t\t\t\t\t\tremLength += MqttProtoIdentifierv3.length + 3;\r\n\t\t\t\t\t\tbreak;\r\n\t\t\t\t\tcase 4:\r\n\t\t\t\t\t\tremLength += MqttProtoIdentifierv4.length + 3;\r\n\t\t\t\t\t\tbreak;\r\n\t\t\t\t}\r\n\r\n\t\t\t\tremLength += UTF8Length(this.clientId) + 2;\r\n\t\t\t\tif (this.willMessage != undefined) {\r\n\t\t\t\t\tremLength += UTF8Length(this.willMessage.destinationName) + 2;\r\n\t\t\t\t\t// Will message is always a string, sent as UTF-8 characters with a preceding length.\r\n\t\t\t\t\tvar willMessagePayloadBytes = this.willMessage.payloadBytes;\r\n\t\t\t\t\tif (!(willMessagePayloadBytes instanceof Uint8Array))\r\n\t\t\t\t\t\twillMessagePayloadBytes = new Uint8Array(payloadBytes);\r\n\t\t\t\t\tremLength += willMessagePayloadBytes.byteLength + 2;\r\n\t\t\t\t}\r\n\t\t\t\tif (this.userName != undefined)\r\n\t\t\t\t\tremLength += UTF8Length(this.userName) + 2;\r\n\t\t\t\tif (this.password != undefined)\r\n\t\t\t\t\tremLength += UTF8Length(this.password) + 2;\r\n\t\t\t\tbreak;\r\n\r\n\t\t\t// Subscribe, Unsubscribe can both contain topic strings\r\n\t\t\tcase MESSAGE_TYPE.SUBSCRIBE:\r\n\t\t\t\tfirst |= 0x02; // Qos = 1;\r\n\t\t\t\tfor (var i = 0; i < this.topics.length; i++) {\r\n\t\t\t\t\ttopicStrLength[i] = UTF8Length(this.topics[i]);\r\n\t\t\t\t\tremLength += topicStrLength[i] + 2;\r\n\t\t\t\t}\r\n\t\t\t\tremLength += this.requestedQos.length; // 1 byte for each topic's Qos\r\n\t\t\t\t// QoS on Subscribe only\r\n\t\t\t\tbreak;\r\n\r\n\t\t\tcase MESSAGE_TYPE.UNSUBSCRIBE:\r\n\t\t\t\tfirst |= 0x02; // Qos = 1;\r\n\t\t\t\tfor (var i = 0; i < this.topics.length; i++) {\r\n\t\t\t\t\ttopicStrLength[i] = UTF8Length(this.topics[i]);\r\n\t\t\t\t\tremLength += topicStrLength[i] + 2;\r\n\t\t\t\t}\r\n\t\t\t\tbreak;\r\n\r\n\t\t\tcase MESSAGE_TYPE.PUBREL:\r\n\t\t\t\tfirst |= 0x02; // Qos = 1;\r\n\t\t\t\tbreak;\r\n\r\n\t\t\tcase MESSAGE_TYPE.PUBLISH:\r\n\t\t\t\tif (this.payloadMessage.duplicate) first |= 0x08;\r\n\t\t\t\tfirst = first |= (this.payloadMessage.qos << 1);\r\n\t\t\t\tif (this.payloadMessage.retained) first |= 0x01;\r\n\t\t\t\tvar destinationNameLength = UTF8Length(this.payloadMessage.destinationName);\r\n\t\t\t\tremLength += destinationNameLength + 2;\r\n\t\t\t\tvar payloadBytes = this.payloadMessage.payloadBytes;\r\n\t\t\t\tremLength += payloadBytes.byteLength;\r\n\t\t\t\tif (payloadBytes instanceof ArrayBuffer)\r\n\t\t\t\t\tpayloadBytes = new Uint8Array(payloadBytes);\r\n\t\t\t\telse if (!(payloadBytes instanceof Uint8Array))\r\n\t\t\t\t\tpayloadBytes = new Uint8Array(payloadBytes.buffer);\r\n\t\t\t\tbreak;\r\n\r\n\t\t\tcase MESSAGE_TYPE.DISCONNECT:\r\n\t\t\t\tbreak;\r\n\r\n\t\t\tdefault:\r\n\t\t\t\t;\r\n\t\t}\r\n\r\n\t\t// Now we can allocate a buffer for the message\r\n\r\n\t\tvar mbi = encodeMBI(remLength);  // Convert the length to MQTT MBI format\r\n\t\tvar pos = mbi.length + 1;        // Offset of start of variable header\r\n\t\tvar buffer = new ArrayBuffer(remLength + pos);\r\n\t\tvar byteStream = new Uint8Array(buffer);    // view it as a sequence of bytes\r\n\r\n\t\t//Write the fixed header into the buffer\r\n\t\tbyteStream[0] = first;\r\n\t\tbyteStream.set(mbi, 1);\r\n\r\n\t\t// If this is a PUBLISH then the variable header starts with a topic\r\n\t\tif (this.type == MESSAGE_TYPE.PUBLISH)\r\n\t\t\tpos = writeString(this.payloadMessage.destinationName, destinationNameLength, byteStream, pos);\r\n\t\t// If this is a CONNECT then the variable header contains the protocol name/version, flags and keepalive time\r\n\r\n\t\telse if (this.type == MESSAGE_TYPE.CONNECT) {\r\n\t\t\tswitch (this.mqttVersion) {\r\n\t\t\t\tcase 3:\r\n\t\t\t\t\tbyteStream.set(MqttProtoIdentifierv3, pos);\r\n\t\t\t\t\tpos += MqttProtoIdentifierv3.length;\r\n\t\t\t\t\tbreak;\r\n\t\t\t\tcase 4:\r\n\t\t\t\t\tbyteStream.set(MqttProtoIdentifierv4, pos);\r\n\t\t\t\t\tpos += MqttProtoIdentifierv4.length;\r\n\t\t\t\t\tbreak;\r\n\t\t\t}\r\n\t\t\tvar connectFlags = 0;\r\n\t\t\tif (this.cleanSession)\r\n\t\t\t\tconnectFlags = 0x02;\r\n\t\t\tif (this.willMessage != undefined) {\r\n\t\t\t\tconnectFlags |= 0x04;\r\n\t\t\t\tconnectFlags |= (this.willMessage.qos << 3);\r\n\t\t\t\tif (this.willMessage.retained) {\r\n\t\t\t\t\tconnectFlags |= 0x20;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\tif (this.userName != undefined)\r\n\t\t\t\tconnectFlags |= 0x80;\r\n\t\t\tif (this.password != undefined)\r\n\t\t\t\tconnectFlags |= 0x40;\r\n\t\t\tbyteStream[pos++] = connectFlags;\r\n\t\t\tpos = writeUint16(this.keepAliveInterval, byteStream, pos);\r\n\t\t}\r\n\r\n\t\t// Output the messageIdentifier - if there is one\r\n\t\tif (this.messageIdentifier != undefined)\r\n\t\t\tpos = writeUint16(this.messageIdentifier, byteStream, pos);\r\n\r\n\t\tswitch (this.type) {\r\n\t\t\tcase MESSAGE_TYPE.CONNECT:\r\n\t\t\t\tpos = writeString(this.clientId, UTF8Length(this.clientId), byteStream, pos);\r\n\t\t\t\tif (this.willMessage != undefined) {\r\n\t\t\t\t\tpos = writeString(this.willMessage.destinationName, UTF8Length(this.willMessage.destinationName), byteStream, pos);\r\n\t\t\t\t\tpos = writeUint16(willMessagePayloadBytes.byteLength, byteStream, pos);\r\n\t\t\t\t\tbyteStream.set(willMessagePayloadBytes, pos);\r\n\t\t\t\t\tpos += willMessagePayloadBytes.byteLength;\r\n\r\n\t\t\t\t}\r\n\t\t\t\tif (this.userName != undefined)\r\n\t\t\t\t\tpos = writeString(this.userName, UTF8Length(this.userName), byteStream, pos);\r\n\t\t\t\tif (this.password != undefined)\r\n\t\t\t\t\tpos = writeString(this.password, UTF8Length(this.password), byteStream, pos);\r\n\t\t\t\tbreak;\r\n\r\n\t\t\tcase MESSAGE_TYPE.PUBLISH:\r\n\t\t\t\t// PUBLISH has a text or binary payload, if text do not add a 2 byte length field, just the UTF characters.\t\r\n\t\t\t\tbyteStream.set(payloadBytes, pos);\r\n\r\n\t\t\t\tbreak;\r\n\r\n\t\t\t//    \t    case MESSAGE_TYPE.PUBREC:\t\r\n\t\t\t//    \t    case MESSAGE_TYPE.PUBREL:\t\r\n\t\t\t//    \t    case MESSAGE_TYPE.PUBCOMP:\t\r\n\t\t\t//    \t    \tbreak;\r\n\r\n\t\t\tcase MESSAGE_TYPE.SUBSCRIBE:\r\n\t\t\t\t// SUBSCRIBE has a list of topic strings and request QoS\r\n\t\t\t\tfor (var i = 0; i < this.topics.length; i++) {\r\n\t\t\t\t\tpos = writeString(this.topics[i], topicStrLength[i], byteStream, pos);\r\n\t\t\t\t\tbyteStream[pos++] = this.requestedQos[i];\r\n\t\t\t\t}\r\n\t\t\t\tbreak;\r\n\r\n\t\t\tcase MESSAGE_TYPE.UNSUBSCRIBE:\r\n\t\t\t\t// UNSUBSCRIBE has a list of topic strings\r\n\t\t\t\tfor (var i = 0; i < this.topics.length; i++)\r\n\t\t\t\t\tpos = writeString(this.topics[i], topicStrLength[i], byteStream, pos);\r\n\t\t\t\tbreak;\r\n\r\n\t\t\tdefault:\r\n\t\t\t// Do nothing.\r\n\t\t}\r\n\r\n\t\treturn buffer;\r\n\t}\r\n\r\n\tfunction decodeMessage(input, pos) {\r\n\t\tvar startingPos = pos;\r\n\t\tvar first = input[pos];\r\n\t\tvar type = first >> 4;\r\n\t\tvar messageInfo = first &= 0x0f;\r\n\t\tpos += 1;\r\n\r\n\r\n\t\t// Decode the remaining length (MBI format)\r\n\r\n\t\tvar digit;\r\n\t\tvar remLength = 0;\r\n\t\tvar multiplier = 1;\r\n\t\tdo {\r\n\t\t\tif (pos == input.length) {\r\n\t\t\t\treturn [null, startingPos];\r\n\t\t\t}\r\n\t\t\tdigit = input[pos++];\r\n\t\t\tremLength += ((digit & 0x7F) * multiplier);\r\n\t\t\tmultiplier *= 128;\r\n\t\t} while ((digit & 0x80) != 0);\r\n\r\n\t\tvar endPos = pos + remLength;\r\n\t\tif (endPos > input.length) {\r\n\t\t\treturn [null, startingPos];\r\n\t\t}\r\n\r\n\t\tvar wireMessage = new WireMessage(type);\r\n\t\tswitch (type) {\r\n\t\t\tcase MESSAGE_TYPE.CONNACK:\r\n\t\t\t\tvar connectAcknowledgeFlags = input[pos++];\r\n\t\t\t\tif (connectAcknowledgeFlags & 0x01)\r\n\t\t\t\t\twireMessage.sessionPresent = true;\r\n\t\t\t\twireMessage.returnCode = input[pos++];\r\n\t\t\t\tbreak;\r\n\r\n\t\t\tcase MESSAGE_TYPE.PUBLISH:\r\n\t\t\t\tvar qos = (messageInfo >> 1) & 0x03;\r\n\r\n\t\t\t\tvar len = readUint16(input, pos);\r\n\t\t\t\tpos += 2;\r\n\t\t\t\tvar topicName = parseUTF8(input, pos, len);\r\n\t\t\t\tpos += len;\r\n\t\t\t\t// If QoS 1 or 2 there will be a messageIdentifier\r\n\t\t\t\tif (qos > 0) {\r\n\t\t\t\t\twireMessage.messageIdentifier = readUint16(input, pos);\r\n\t\t\t\t\tpos += 2;\r\n\t\t\t\t}\r\n\r\n\t\t\t\tvar message = new Paho.MQTT.Message(input.subarray(pos, endPos));\r\n\t\t\t\tif ((messageInfo & 0x01) == 0x01)\r\n\t\t\t\t\tmessage.retained = true;\r\n\t\t\t\tif ((messageInfo & 0x08) == 0x08)\r\n\t\t\t\t\tmessage.duplicate = true;\r\n\t\t\t\tmessage.qos = qos;\r\n\t\t\t\tmessage.destinationName = topicName;\r\n\t\t\t\twireMessage.payloadMessage = message;\r\n\t\t\t\tbreak;\r\n\r\n\t\t\tcase MESSAGE_TYPE.PUBACK:\r\n\t\t\tcase MESSAGE_TYPE.PUBREC:\r\n\t\t\tcase MESSAGE_TYPE.PUBREL:\r\n\t\t\tcase MESSAGE_TYPE.PUBCOMP:\r\n\t\t\tcase MESSAGE_TYPE.UNSUBACK:\r\n\t\t\t\twireMessage.messageIdentifier = readUint16(input, pos);\r\n\t\t\t\tbreak;\r\n\r\n\t\t\tcase MESSAGE_TYPE.SUBACK:\r\n\t\t\t\twireMessage.messageIdentifier = readUint16(input, pos);\r\n\t\t\t\tpos += 2;\r\n\t\t\t\twireMessage.returnCode = input.subarray(pos, endPos);\r\n\t\t\t\tbreak;\r\n\r\n\t\t\tdefault:\r\n\t\t\t\t;\r\n\t\t}\r\n\r\n\t\treturn [wireMessage, endPos];\r\n\t}\r\n\r\n\tfunction writeUint16(input, buffer, offset) {\r\n\t\tbuffer[offset++] = input >> 8;      //MSB\r\n\t\tbuffer[offset++] = input % 256;     //LSB \r\n\t\treturn offset;\r\n\t}\r\n\r\n\tfunction writeString(input, utf8Length, buffer, offset) {\r\n\t\toffset = writeUint16(utf8Length, buffer, offset);\r\n\t\tstringToUTF8(input, buffer, offset);\r\n\t\treturn offset + utf8Length;\r\n\t}\r\n\r\n\tfunction readUint16(buffer, offset) {\r\n\t\treturn 256 * buffer[offset] + buffer[offset + 1];\r\n\t}\r\n\r\n\t/**\r\n\t * Encodes an MQTT Multi-Byte Integer\r\n\t * @private \r\n\t */\r\n\tfunction encodeMBI(number) {\r\n\t\tvar output = new Array(1);\r\n\t\tvar numBytes = 0;\r\n\r\n\t\tdo {\r\n\t\t\tvar digit = number % 128;\r\n\t\t\tnumber = number >> 7;\r\n\t\t\tif (number > 0) {\r\n\t\t\t\tdigit |= 0x80;\r\n\t\t\t}\r\n\t\t\toutput[numBytes++] = digit;\r\n\t\t} while ((number > 0) && (numBytes < 4));\r\n\r\n\t\treturn output;\r\n\t}\r\n\r\n\t/**\r\n\t * Takes a String and calculates its length in bytes when encoded in UTF8.\r\n\t * @private\r\n\t */\r\n\tfunction UTF8Length(input) {\r\n\t\tvar output = 0;\r\n\t\tfor (var i = 0; i < input.length; i++) {\r\n\t\t\tvar charCode = input.charCodeAt(i);\r\n\t\t\tif (charCode > 0x7FF) {\r\n\t\t\t\t// Surrogate pair means its a 4 byte character\r\n\t\t\t\tif (0xD800 <= charCode && charCode <= 0xDBFF) {\r\n\t\t\t\t\ti++;\r\n\t\t\t\t\toutput++;\r\n\t\t\t\t}\r\n\t\t\t\toutput += 3;\r\n\t\t\t}\r\n\t\t\telse if (charCode > 0x7F)\r\n\t\t\t\toutput += 2;\r\n\t\t\telse\r\n\t\t\t\toutput++;\r\n\t\t}\r\n\t\treturn output;\r\n\t}\r\n\r\n\t/**\r\n\t * Takes a String and writes it into an array as UTF8 encoded bytes.\r\n\t * @private\r\n\t */\r\n\tfunction stringToUTF8(input, output, start) {\r\n\t\tvar pos = start;\r\n\t\tfor (var i = 0; i < input.length; i++) {\r\n\t\t\tvar charCode = input.charCodeAt(i);\r\n\r\n\t\t\t// Check for a surrogate pair.\r\n\t\t\tif (0xD800 <= charCode && charCode <= 0xDBFF) {\r\n\t\t\t\tlowCharCode = input.charCodeAt(++i);\r\n\t\t\t\tif (isNaN(lowCharCode)) {\r\n\t\t\t\t\tthrow new Error(format(ERROR.MALFORMED_UNICODE, [charCode, lowCharCode]));\r\n\t\t\t\t}\r\n\t\t\t\tcharCode = ((charCode - 0xD800) << 10) + (lowCharCode - 0xDC00) + 0x10000;\r\n\r\n\t\t\t}\r\n\r\n\t\t\tif (charCode <= 0x7F) {\r\n\t\t\t\toutput[pos++] = charCode;\r\n\t\t\t} else if (charCode <= 0x7FF) {\r\n\t\t\t\toutput[pos++] = charCode >> 6 & 0x1F | 0xC0;\r\n\t\t\t\toutput[pos++] = charCode & 0x3F | 0x80;\r\n\t\t\t} else if (charCode <= 0xFFFF) {\r\n\t\t\t\toutput[pos++] = charCode >> 12 & 0x0F | 0xE0;\r\n\t\t\t\toutput[pos++] = charCode >> 6 & 0x3F | 0x80;\r\n\t\t\t\toutput[pos++] = charCode & 0x3F | 0x80;\r\n\t\t\t} else {\r\n\t\t\t\toutput[pos++] = charCode >> 18 & 0x07 | 0xF0;\r\n\t\t\t\toutput[pos++] = charCode >> 12 & 0x3F | 0x80;\r\n\t\t\t\toutput[pos++] = charCode >> 6 & 0x3F | 0x80;\r\n\t\t\t\toutput[pos++] = charCode & 0x3F | 0x80;\r\n\t\t\t};\r\n\t\t}\r\n\t\treturn output;\r\n\t}\r\n\r\n\tfunction parseUTF8(input, offset, length) {\r\n\t\tvar output = \"\";\r\n\t\tvar utf16;\r\n\t\tvar pos = offset;\r\n\r\n\t\twhile (pos < offset + length) {\r\n\t\t\tvar byte1 = input[pos++];\r\n\t\t\tif (byte1 < 128)\r\n\t\t\t\tutf16 = byte1;\r\n\t\t\telse {\r\n\t\t\t\tvar byte2 = input[pos++] - 128;\r\n\t\t\t\tif (byte2 < 0)\r\n\t\t\t\t\tthrow new Error(format(ERROR.MALFORMED_UTF, [byte1.toString(16), byte2.toString(16), \"\"]));\r\n\t\t\t\tif (byte1 < 0xE0)             // 2 byte character\r\n\t\t\t\t\tutf16 = 64 * (byte1 - 0xC0) + byte2;\r\n\t\t\t\telse {\r\n\t\t\t\t\tvar byte3 = input[pos++] - 128;\r\n\t\t\t\t\tif (byte3 < 0)\r\n\t\t\t\t\t\tthrow new Error(format(ERROR.MALFORMED_UTF, [byte1.toString(16), byte2.toString(16), byte3.toString(16)]));\r\n\t\t\t\t\tif (byte1 < 0xF0)        // 3 byte character\r\n\t\t\t\t\t\tutf16 = 4096 * (byte1 - 0xE0) + 64 * byte2 + byte3;\r\n\t\t\t\t\telse {\r\n\t\t\t\t\t\tvar byte4 = input[pos++] - 128;\r\n\t\t\t\t\t\tif (byte4 < 0)\r\n\t\t\t\t\t\t\tthrow new Error(format(ERROR.MALFORMED_UTF, [byte1.toString(16), byte2.toString(16), byte3.toString(16), byte4.toString(16)]));\r\n\t\t\t\t\t\tif (byte1 < 0xF8)        // 4 byte character \r\n\t\t\t\t\t\t\tutf16 = 262144 * (byte1 - 0xF0) + 4096 * byte2 + 64 * byte3 + byte4;\r\n\t\t\t\t\t\telse                     // longer encodings are not supported  \r\n\t\t\t\t\t\t\tthrow new Error(format(ERROR.MALFORMED_UTF, [byte1.toString(16), byte2.toString(16), byte3.toString(16), byte4.toString(16)]));\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\tif (utf16 > 0xFFFF)   // 4 byte character - express as a surrogate pair\r\n\t\t\t{\r\n\t\t\t\tutf16 -= 0x10000;\r\n\t\t\t\toutput += String.fromCharCode(0xD800 + (utf16 >> 10)); // lead character\r\n\t\t\t\tutf16 = 0xDC00 + (utf16 & 0x3FF);  // trail character\r\n\t\t\t}\r\n\t\t\toutput += String.fromCharCode(utf16);\r\n\t\t}\r\n\t\treturn output;\r\n\t}\r\n\r\n\t/** \r\n\t * Repeat keepalive requests, monitor responses.\r\n\t * @ignore\r\n\t */\r\n\tvar Pinger = function (client, window, keepAliveInterval) {\r\n\t\tthis._client = client;\r\n\t\tthis._window = window;\r\n\t\tthis._keepAliveInterval = keepAliveInterval * 1000;\r\n\t\tthis.isReset = false;\r\n\r\n\t\tvar pingReq = new WireMessage(MESSAGE_TYPE.PINGREQ).encode();\r\n\r\n\t\tvar doTimeout = function (pinger) {\r\n\t\t\treturn function () {\r\n\t\t\t\treturn doPing.apply(pinger);\r\n\t\t\t};\r\n\t\t};\r\n\r\n\t\t/** @ignore */\r\n\t\tvar doPing = function () {\r\n\t\t\tconsole.log('Do Ping !!!!!!!!!! -------------');\r\n\t\t\tif (!this.isReset) {\r\n\t\t\t\tthis._client._trace(\"Pinger.doPing\", \"Timed out\");\r\n\t\t\t\tthis._client._disconnected(ERROR.PING_TIMEOUT.code, format(ERROR.PING_TIMEOUT));\r\n\t\t\t} else {\r\n\t\t\t\tthis.isReset = false;\r\n\t\t\t\tthis._client._trace(\"Pinger.doPing\", \"send PINGREQ\");\r\n\t\t\t\tthis._client.socket.send(pingReq);\r\n\t\t\t\tthis.timeout = this._window.setTimeout(doTimeout(this), this._keepAliveInterval);\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\tthis.reset = function () {\r\n\t\t\tthis.isReset = true;\r\n\t\t\tthis._window.clearTimeout(this.timeout);\r\n\t\t\tif (this._keepAliveInterval > 0)\r\n\t\t\t\tthis.timeout = setTimeout(doTimeout(this), this._keepAliveInterval);\r\n\t\t}\r\n\r\n\t\tthis.cancel = function () {\r\n\t\t\tthis._window.clearTimeout(this.timeout);\r\n\t\t}\r\n\t};\r\n\r\n\t/**\r\n\t * Monitor request completion.\r\n\t * @ignore\r\n\t */\r\n\tvar Timeout = function (client, window, timeoutSeconds, action, args) {\r\n\t\tthis._window = window;\r\n\t\tif (!timeoutSeconds)\r\n\t\t\ttimeoutSeconds = 30;\r\n\r\n\t\tvar doTimeout = function (action, client, args) {\r\n\t\t\treturn function () {\r\n\t\t\t\treturn action.apply(client, args);\r\n\t\t\t};\r\n\t\t};\r\n\t\tthis.timeout = setTimeout(doTimeout(action, client, args), timeoutSeconds * 1000);\r\n\r\n\t\tthis.cancel = function () {\r\n\t\t\tthis._window.clearTimeout(this.timeout);\r\n\t\t}\r\n\t};\r\n\r\n\t/*\r\n\t * Internal implementation of the Websockets MQTT V3.1 client.\r\n\t * \r\n\t * @name Paho.MQTT.ClientImpl @constructor \r\n\t * @param {String} host the DNS nameof the webSocket host. \r\n\t * @param {Number} port the port number for that host.\r\n\t * @param {String} clientId the MQ client identifier.\r\n\t */\r\n\tvar ClientImpl = function (uri, host, port, path, clientId) {\r\n\t\t// Check dependencies are satisfied in this browser.\r\n\t\tif (!(\"WebSocket\" in global && global[\"WebSocket\"] !== null)) {\r\n\t\t\tthrow new Error(format(ERROR.UNSUPPORTED, [\"WebSocket\"]));\r\n\t\t}\r\n\t\tif (!(\"localStorage\" in global && global[\"localStorage\"] !== null)) {\r\n\t\t\tthrow new Error(format(ERROR.UNSUPPORTED, [\"localStorage\"]));\r\n\t\t}\r\n\t\tif (!(\"ArrayBuffer\" in global && global[\"ArrayBuffer\"] !== null)) {\r\n\t\t\tthrow new Error(format(ERROR.UNSUPPORTED, [\"ArrayBuffer\"]));\r\n\t\t}\r\n\t\tthis._trace(\"Paho.MQTT.Client\", uri, host, port, path, clientId);\r\n\r\n\t\tthis.host = host;\r\n\t\tthis.port = port;\r\n\t\tthis.path = path;\r\n\t\tthis.uri = uri;\r\n\t\tthis.clientId = clientId;\r\n\r\n\t\t// Local storagekeys are qualified with the following string.\r\n\t\t// The conditional inclusion of path in the key is for backward\r\n\t\t// compatibility to when the path was not configurable and assumed to\r\n\t\t// be /mqtt\r\n\t\tthis._localKey = host + \":\" + port + (path != \"/mqtt\" ? \":\" + path : \"\") + \":\" + clientId + \":\";\r\n\r\n\t\t// Create private instance-only message queue\r\n\t\t// Internal queue of messages to be sent, in sending order. \r\n\t\tthis._msg_queue = [];\r\n\r\n\t\t// Messages we have sent and are expecting a response for, indexed by their respective message ids. \r\n\t\tthis._sentMessages = {};\r\n\r\n\t\t// Messages we have received and acknowleged and are expecting a confirm message for\r\n\t\t// indexed by their respective message ids. \r\n\t\tthis._receivedMessages = {};\r\n\r\n\t\t// Internal list of callbacks to be executed when messages\r\n\t\t// have been successfully sent over web socket, e.g. disconnect\r\n\t\t// when it doesn't have to wait for ACK, just message is dispatched.\r\n\t\tthis._notify_msg_sent = {};\r\n\r\n\t\t// Unique identifier for SEND messages, incrementing\r\n\t\t// counter as messages are sent.\r\n\t\tthis._message_identifier = 1;\r\n\r\n\t\t// Used to determine the transmission sequence of stored sent messages.\r\n\t\tthis._sequence = 0;\r\n\r\n\r\n\t\t// Load the local state, if any, from the saved version, only restore state relevant to this client.   \t\r\n\t\tfor (var key in localStorage)\r\n\t\t\tif (key.indexOf(\"Sent:\" + this._localKey) == 0\r\n\t\t\t\t|| key.indexOf(\"Received:\" + this._localKey) == 0)\r\n\t\t\t\tthis.restore(key);\r\n\t};\r\n\r\n\t// Messaging Client public instance members. \r\n\tClientImpl.prototype.host;\r\n\tClientImpl.prototype.port;\r\n\tClientImpl.prototype.path;\r\n\tClientImpl.prototype.uri;\r\n\tClientImpl.prototype.clientId;\r\n\r\n\t// Messaging Client private instance members.\r\n\tClientImpl.prototype.socket;\r\n\t/* true once we have received an acknowledgement to a CONNECT packet. */\r\n\tClientImpl.prototype.connected = false;\r\n\t/* The largest message identifier allowed, may not be larger than 2**16 but \r\n\t * if set smaller reduces the maximum number of outbound messages allowed.\r\n\t */\r\n\tClientImpl.prototype.maxMessageIdentifier = 65536;\r\n\tClientImpl.prototype.connectOptions;\r\n\tClientImpl.prototype.hostIndex;\r\n\tClientImpl.prototype.onConnectionLost;\r\n\tClientImpl.prototype.onMessageDelivered;\r\n\tClientImpl.prototype.onMessageArrived;\r\n\tClientImpl.prototype._msg_queue = null;\r\n\tClientImpl.prototype._connectTimeout;\r\n\t/* The sendPinger monitors how long we allow before we send data to prove to the server that we are alive. */\r\n\tClientImpl.prototype.sendPinger = null;\r\n\t/* The receivePinger monitors how long we allow before we require evidence that the server is alive. */\r\n\tClientImpl.prototype.receivePinger = null;\r\n\r\n\tClientImpl.prototype.receiveBuffer = null;\r\n\r\n\tClientImpl.prototype._traceBuffer = null;\r\n\tClientImpl.prototype._MAX_TRACE_ENTRIES = 100;\r\n\r\n\tClientImpl.prototype.connect = function (connectOptions) {\r\n\t\tvar connectOptionsMasked = this._traceMask(connectOptions, \"password\");\r\n\t\tthis._trace(\"Client.connect\", connectOptionsMasked, this.socket, this.connected);\r\n\r\n\t\tif (this.connected)\r\n\t\t\tthrow new Error(format(ERROR.INVALID_STATE, [\"already connected\"]));\r\n\t\tif (this.socket)\r\n\t\t\tthrow new Error(format(ERROR.INVALID_STATE, [\"already connected\"]));\r\n\r\n\t\tthis.connectOptions = connectOptions;\r\n\r\n\t\tif (connectOptions.uris) {\r\n\t\t\tthis.hostIndex = 0;\r\n\t\t\tthis._doConnect(connectOptions.uris[0]);\r\n\t\t} else {\r\n\t\t\tthis._doConnect(this.uri);\r\n\t\t}\r\n\r\n\t};\r\n\r\n\tClientImpl.prototype.subscribe = function (filter, subscribeOptions) {\r\n\t\tthis._trace(\"Client.subscribe\", filter, subscribeOptions);\r\n\r\n\t\tif (!this.connected)\r\n\t\t\tthrow new Error(format(ERROR.INVALID_STATE, [\"not connected\"]));\r\n\r\n\t\tvar wireMessage = new WireMessage(MESSAGE_TYPE.SUBSCRIBE);\r\n\t\twireMessage.topics = [filter];\r\n\t\tif (subscribeOptions.qos != undefined)\r\n\t\t\twireMessage.requestedQos = [subscribeOptions.qos];\r\n\t\telse\r\n\t\t\twireMessage.requestedQos = [0];\r\n\r\n\t\tif (subscribeOptions.onSuccess) {\r\n\t\t\twireMessage.onSuccess = function (grantedQos) { subscribeOptions.onSuccess({ invocationContext: subscribeOptions.invocationContext, grantedQos: grantedQos }); };\r\n\t\t}\r\n\r\n\t\tif (subscribeOptions.onFailure) {\r\n\t\t\twireMessage.onFailure = function (errorCode) { subscribeOptions.onFailure({ invocationContext: subscribeOptions.invocationContext, errorCode: errorCode }); };\r\n\t\t}\r\n\r\n\t\tif (subscribeOptions.timeout) {\r\n\t\t\twireMessage.timeOut = new Timeout(this, window, subscribeOptions.timeout, subscribeOptions.onFailure\r\n\t\t\t\t, [{\r\n\t\t\t\t\tinvocationContext: subscribeOptions.invocationContext,\r\n\t\t\t\t\terrorCode: ERROR.SUBSCRIBE_TIMEOUT.code,\r\n\t\t\t\t\terrorMessage: format(ERROR.SUBSCRIBE_TIMEOUT)\r\n\t\t\t\t}]);\r\n\t\t}\r\n\r\n\t\t// All subscriptions return a SUBACK. \r\n\t\tthis._requires_ack(wireMessage);\r\n\t\tthis._schedule_message(wireMessage);\r\n\t};\r\n\r\n\t/** @ignore */\r\n\tClientImpl.prototype.unsubscribe = function (filter, unsubscribeOptions) {\r\n\t\tthis._trace(\"Client.unsubscribe\", filter, unsubscribeOptions);\r\n\r\n\t\tif (!this.connected)\r\n\t\t\tthrow new Error(format(ERROR.INVALID_STATE, [\"not connected\"]));\r\n\r\n\t\tvar wireMessage = new WireMessage(MESSAGE_TYPE.UNSUBSCRIBE);\r\n\t\twireMessage.topics = [filter];\r\n\r\n\t\tif (unsubscribeOptions.onSuccess) {\r\n\t\t\twireMessage.callback = function () { unsubscribeOptions.onSuccess({ invocationContext: unsubscribeOptions.invocationContext }); };\r\n\t\t}\r\n\t\tif (unsubscribeOptions.timeout) {\r\n\t\t\twireMessage.timeOut = new Timeout(this, window, unsubscribeOptions.timeout, unsubscribeOptions.onFailure\r\n\t\t\t\t, [{\r\n\t\t\t\t\tinvocationContext: unsubscribeOptions.invocationContext,\r\n\t\t\t\t\terrorCode: ERROR.UNSUBSCRIBE_TIMEOUT.code,\r\n\t\t\t\t\terrorMessage: format(ERROR.UNSUBSCRIBE_TIMEOUT)\r\n\t\t\t\t}]);\r\n\t\t}\r\n\r\n\t\t// All unsubscribes return a SUBACK.         \r\n\t\tthis._requires_ack(wireMessage);\r\n\t\tthis._schedule_message(wireMessage);\r\n\t};\r\n\r\n\tClientImpl.prototype.send = function (message) {\r\n\t\tthis._trace(\"Client.send\", message);\r\n\r\n\t\tif (!this.connected)\r\n\t\t\tthrow new Error(format(ERROR.INVALID_STATE, [\"not connected\"]));\r\n\r\n\t\tvar wireMessage = new WireMessage(MESSAGE_TYPE.PUBLISH);\r\n\t\twireMessage.payloadMessage = message;\r\n\r\n\t\tif (message.qos > 0)\r\n\t\t\tthis._requires_ack(wireMessage);\r\n\t\telse if (this.onMessageDelivered)\r\n\t\t\tthis._notify_msg_sent[wireMessage] = this.onMessageDelivered(wireMessage.payloadMessage);\r\n\t\tthis._schedule_message(wireMessage);\r\n\t};\r\n\r\n\tClientImpl.prototype.disconnect = function () {\r\n\t\tconsole.log(\"Client.disconnect\")\r\n\t\tthis._trace(\"Client.disconnect\");\r\n\r\n\t\tif (!this.socket)\r\n\t\t\tthrow new Error(format(ERROR.INVALID_STATE, [\"not connecting or connected\"]));\r\n\r\n\t\tlet wireMessage = new WireMessage(MESSAGE_TYPE.DISCONNECT);\r\n\r\n\t\t// Run the disconnected call back as soon as the message has been sent,\r\n\t\t// in case of a failure later on in the disconnect processing.\r\n\t\t// as a consequence, the _disconected call back may be run several times.\r\n\t\tthis._notify_msg_sent[wireMessage] = scope(this._disconnected, this);\r\n\r\n\t\tthis._schedule_message(wireMessage);\r\n\t};\r\n\r\n\tClientImpl.prototype.getTraceLog = function () {\r\n\t\tif (this._traceBuffer !== null) {\r\n\t\t\tthis._trace(\"Client.getTraceLog\", new Date());\r\n\t\t\tthis._trace(\"Client.getTraceLog in flight messages\", this._sentMessages.length);\r\n\t\t\tfor (var key in this._sentMessages)\r\n\t\t\t\tthis._trace(\"_sentMessages \", key, this._sentMessages[key]);\r\n\t\t\tfor (var key in this._receivedMessages)\r\n\t\t\t\tthis._trace(\"_receivedMessages \", key, this._receivedMessages[key]);\r\n\r\n\t\t\treturn this._traceBuffer;\r\n\t\t}\r\n\t};\r\n\r\n\tClientImpl.prototype.startTrace = function () {\r\n\t\tif (this._traceBuffer === null) {\r\n\t\t\tthis._traceBuffer = [];\r\n\t\t}\r\n\t\tthis._trace(\"Client.startTrace\", new Date(), version);\r\n\t};\r\n\r\n\tClientImpl.prototype.stopTrace = function () {\r\n\t\tdelete this._traceBuffer;\r\n\t};\r\n\r\n\tClientImpl.prototype._doConnect = function (wsurl) {\r\n\t\t// When the socket is open, this client will send the CONNECT WireMessage using the saved parameters. \r\n\t\tconsole.log(\"do Connect !...................\");\r\n\t\tif (this.connectOptions.useSSL) {\r\n\t\t\tvar uriParts = wsurl.split(\":\");\r\n\t\t\turiParts[0] = \"wss\";\r\n\t\t\twsurl = uriParts.join(\":\");\r\n\t\t}\r\n\t\tthis.connected = false;\r\n\t\tthis.socket = new WebSocket(wsurl, [\"mqtt\"]);\r\n\t\tthis.socket.binaryType = 'arraybuffer';\r\n\r\n\t\tthis.socket.onopen = scope(this._on_socket_open, this);\r\n\t\tthis.socket.onmessage = scope(this._on_socket_message, this);\r\n\t\tthis.socket.onerror = scope(this._on_socket_error, this);\r\n\t\tthis.socket.onclose = scope(this._on_socket_close, this);\r\n\r\n\t\tthis.sendPinger = new Pinger(this, window, this.connectOptions.keepAliveInterval);\r\n\t\tthis.receivePinger = new Pinger(this, window, this.connectOptions.keepAliveInterval);\r\n\r\n\t\tthis._connectTimeout = new Timeout(this, window, this.connectOptions.timeout, this._disconnected, [ERROR.CONNECT_TIMEOUT.code, format(ERROR.CONNECT_TIMEOUT)]);\r\n\t};\r\n\r\n\r\n\t// Schedule a new message to be sent over the WebSockets\r\n\t// connection. CONNECT messages cause WebSocket connection\r\n\t// to be started. All other messages are queued internally\r\n\t// until this has happened. When WS connection starts, process\r\n\t// all outstanding messages. \r\n\tClientImpl.prototype._schedule_message = function (message) {\r\n\t\tthis._msg_queue.push(message);\r\n\t\t// Process outstanding messages in the queue if we have an  open socket, and have received CONNACK. \r\n\t\tif (this.connected) {\r\n\t\t\tthis._process_queue();\r\n\t\t}\r\n\t};\r\n\r\n\tClientImpl.prototype.store = function (prefix, wireMessage) {\r\n\t\tstoredMessage = { type: wireMessage.type, messageIdentifier: wireMessage.messageIdentifier, version: 1 };\r\n\r\n\t\tswitch (wireMessage.type) {\r\n\t\t\tcase MESSAGE_TYPE.PUBLISH:\r\n\t\t\t\tif (wireMessage.pubRecReceived)\r\n\t\t\t\t\tstoredMessage.pubRecReceived = true;\r\n\r\n\t\t\t\t// Convert the payload to a hex string.\r\n\t\t\t\tstoredMessage.payloadMessage = {};\r\n\t\t\t\tvar hex = \"\";\r\n\t\t\t\tvar messageBytes = wireMessage.payloadMessage.payloadBytes;\r\n\t\t\t\tfor (var i = 0; i < messageBytes.length; i++) {\r\n\t\t\t\t\tif (messageBytes[i] <= 0xF)\r\n\t\t\t\t\t\thex = hex + \"0\" + messageBytes[i].toString(16);\r\n\t\t\t\t\telse\r\n\t\t\t\t\t\thex = hex + messageBytes[i].toString(16);\r\n\t\t\t\t}\r\n\t\t\t\tstoredMessage.payloadMessage.payloadHex = hex;\r\n\r\n\t\t\t\tstoredMessage.payloadMessage.qos = wireMessage.payloadMessage.qos;\r\n\t\t\t\tstoredMessage.payloadMessage.destinationName = wireMessage.payloadMessage.destinationName;\r\n\t\t\t\tif (wireMessage.payloadMessage.duplicate)\r\n\t\t\t\t\tstoredMessage.payloadMessage.duplicate = true;\r\n\t\t\t\tif (wireMessage.payloadMessage.retained)\r\n\t\t\t\t\tstoredMessage.payloadMessage.retained = true;\r\n\r\n\t\t\t\t// Add a sequence number to sent messages.\r\n\t\t\t\tif (prefix.indexOf(\"Sent:\") == 0) {\r\n\t\t\t\t\tif (wireMessage.sequence === undefined)\r\n\t\t\t\t\t\twireMessage.sequence = ++this._sequence;\r\n\t\t\t\t\tstoredMessage.sequence = wireMessage.sequence;\r\n\t\t\t\t}\r\n\t\t\t\tbreak;\r\n\r\n\t\t\tdefault:\r\n\t\t\t\tthrow Error(format(ERROR.INVALID_STORED_DATA, [key, storedMessage]));\r\n\t\t}\r\n\t\tlocalStorage.setItem(prefix + this._localKey + wireMessage.messageIdentifier, JSON.stringify(storedMessage));\r\n\t};\r\n\r\n\tClientImpl.prototype.restore = function (key) {\r\n\t\tvar value = localStorage.getItem(key);\r\n\t\tvar storedMessage = JSON.parse(value);\r\n\r\n\t\tvar wireMessage = new WireMessage(storedMessage.type, storedMessage);\r\n\r\n\t\tswitch (storedMessage.type) {\r\n\t\t\tcase MESSAGE_TYPE.PUBLISH:\r\n\t\t\t\t// Replace the payload message with a Message object.\r\n\t\t\t\tvar hex = storedMessage.payloadMessage.payloadHex;\r\n\t\t\t\tvar buffer = new ArrayBuffer((hex.length) / 2);\r\n\t\t\t\tvar byteStream = new Uint8Array(buffer);\r\n\t\t\t\tvar i = 0;\r\n\t\t\t\twhile (hex.length >= 2) {\r\n\t\t\t\t\tvar x = parseInt(hex.substring(0, 2), 16);\r\n\t\t\t\t\thex = hex.substring(2, hex.length);\r\n\t\t\t\t\tbyteStream[i++] = x;\r\n\t\t\t\t}\r\n\t\t\t\tvar payloadMessage = new Paho.MQTT.Message(byteStream);\r\n\r\n\t\t\t\tpayloadMessage.qos = storedMessage.payloadMessage.qos;\r\n\t\t\t\tpayloadMessage.destinationName = storedMessage.payloadMessage.destinationName;\r\n\t\t\t\tif (storedMessage.payloadMessage.duplicate)\r\n\t\t\t\t\tpayloadMessage.duplicate = true;\r\n\t\t\t\tif (storedMessage.payloadMessage.retained)\r\n\t\t\t\t\tpayloadMessage.retained = true;\r\n\t\t\t\twireMessage.payloadMessage = payloadMessage;\r\n\r\n\t\t\t\tbreak;\r\n\r\n\t\t\tdefault:\r\n\t\t\t\tthrow Error(format(ERROR.INVALID_STORED_DATA, [key, value]));\r\n\t\t}\r\n\r\n\t\tif (key.indexOf(\"Sent:\" + this._localKey) == 0) {\r\n\t\t\twireMessage.payloadMessage.duplicate = true;\r\n\t\t\tthis._sentMessages[wireMessage.messageIdentifier] = wireMessage;\r\n\t\t} else if (key.indexOf(\"Received:\" + this._localKey) == 0) {\r\n\t\t\tthis._receivedMessages[wireMessage.messageIdentifier] = wireMessage;\r\n\t\t}\r\n\t};\r\n\r\n\tClientImpl.prototype._process_queue = function () {\r\n\t\tvar message = null;\r\n\t\t// Process messages in order they were added\r\n\t\tvar fifo = this._msg_queue.reverse();\r\n\r\n\t\t// Send all queued messages down socket connection\r\n\t\twhile ((message = fifo.pop())) {\r\n\t\t\tthis._socket_send(message);\r\n\t\t\t// Notify listeners that message was successfully sent\r\n\t\t\tif (this._notify_msg_sent[message]) {\r\n\t\t\t\tthis._notify_msg_sent[message]();\r\n\t\t\t\tdelete this._notify_msg_sent[message];\r\n\t\t\t}\r\n\t\t}\r\n\t};\r\n\r\n\t/**\r\n\t * Expect an ACK response for this message. Add message to the set of in progress\r\n\t * messages and set an unused identifier in this message.\r\n\t * @ignore\r\n\t */\r\n\tClientImpl.prototype._requires_ack = function (wireMessage) {\r\n\t\tvar messageCount = Object.keys(this._sentMessages).length;\r\n\t\tif (messageCount > this.maxMessageIdentifier)\r\n\t\t\tthrow Error(\"Too many messages:\" + messageCount);\r\n\r\n\t\twhile (this._sentMessages[this._message_identifier] !== undefined) {\r\n\t\t\tthis._message_identifier++;\r\n\t\t}\r\n\t\twireMessage.messageIdentifier = this._message_identifier;\r\n\t\tthis._sentMessages[wireMessage.messageIdentifier] = wireMessage;\r\n\t\tif (wireMessage.type === MESSAGE_TYPE.PUBLISH) {\r\n\t\t\tthis.store(\"Sent:\", wireMessage);\r\n\t\t}\r\n\t\tif (this._message_identifier === this.maxMessageIdentifier) {\r\n\t\t\tthis._message_identifier = 1;\r\n\t\t}\r\n\t};\r\n\r\n\t/** \r\n\t * Called when the underlying websocket has been opened.\r\n\t * @ignore\r\n\t */\r\n\tClientImpl.prototype._on_socket_open = function () {\r\n\t\t// Create the CONNECT message object.\r\n\t\tvar wireMessage = new WireMessage(MESSAGE_TYPE.CONNECT, this.connectOptions);\r\n\t\twireMessage.clientId = this.clientId;\r\n\t\tthis._socket_send(wireMessage);\r\n\t};\r\n\r\n\t/** \r\n\t * Called when the underlying websocket has received a complete packet.\r\n\t * @ignore\r\n\t */\r\n\tClientImpl.prototype._on_socket_message = function (event) {\r\n\t\tthis._trace(\"Client._on_socket_message\", event.data);\r\n\t\t// Reset the receive ping timer, we now have evidence the server is alive.\r\n\t\tthis.receivePinger.reset();\r\n\t\tvar messages = this._deframeMessages(event.data);\r\n\t\tfor (var i = 0; i < messages.length; i += 1) {\r\n\t\t\tthis._handleMessage(messages[i]);\r\n\t\t}\r\n\t}\r\n\r\n\tClientImpl.prototype._deframeMessages = function (data) {\r\n\t\tvar byteArray = new Uint8Array(data);\r\n\t\tif (this.receiveBuffer) {\r\n\t\t\tvar newData = new Uint8Array(this.receiveBuffer.length + byteArray.length);\r\n\t\t\tnewData.set(this.receiveBuffer);\r\n\t\t\tnewData.set(byteArray, this.receiveBuffer.length);\r\n\t\t\tbyteArray = newData;\r\n\t\t\tdelete this.receiveBuffer;\r\n\t\t}\r\n\t\ttry {\r\n\t\t\tvar offset = 0;\r\n\t\t\tvar messages = [];\r\n\t\t\twhile (offset < byteArray.length) {\r\n\t\t\t\tvar result = decodeMessage(byteArray, offset);\r\n\t\t\t\tvar wireMessage = result[0];\r\n\t\t\t\toffset = result[1];\r\n\t\t\t\tif (wireMessage !== null) {\r\n\t\t\t\t\tmessages.push(wireMessage);\r\n\t\t\t\t} else {\r\n\t\t\t\t\tbreak;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\tif (offset < byteArray.length) {\r\n\t\t\t\tthis.receiveBuffer = byteArray.subarray(offset);\r\n\t\t\t}\r\n\t\t} catch (error) {\r\n\t\t\tthis._disconnected(ERROR.INTERNAL_ERROR.code, format(ERROR.INTERNAL_ERROR, [error.message]));\r\n\t\t\treturn;\r\n\t\t}\r\n\t\treturn messages;\r\n\t}\r\n\r\n\tClientImpl.prototype._handleMessage = function (wireMessage) {\r\n\r\n\t\tthis._trace(\"Client._handleMessage\", wireMessage);\r\n\r\n\t\ttry {\r\n\t\t\tswitch (wireMessage.type) {\r\n\t\t\t\tcase MESSAGE_TYPE.CONNACK:\r\n\t\t\t\t\tthis._connectTimeout.cancel();\r\n\r\n\t\t\t\t\t// If we have started using clean session then clear up the local state.\r\n\t\t\t\t\tif (this.connectOptions.cleanSession) {\r\n\t\t\t\t\t\tfor (var key in this._sentMessages) {\r\n\t\t\t\t\t\t\tvar sentMessage = this._sentMessages[key];\r\n\t\t\t\t\t\t\tlocalStorage.removeItem(\"sns_Sent:\" + this._localKey + sentMessage.messageIdentifier);\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tthis._sentMessages = {};\r\n\r\n\t\t\t\t\t\tfor (var key in this._receivedMessages) {\r\n\t\t\t\t\t\t\tvar receivedMessage = this._receivedMessages[key];\r\n\t\t\t\t\t\t\tlocalStorage.removeItem(\"sns_Received:\" + this._localKey + receivedMessage.messageIdentifier);\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tthis._receivedMessages = {};\r\n\t\t\t\t\t}\r\n\t\t\t\t\t// Client connected and ready for business.\r\n\t\t\t\t\tif (wireMessage.returnCode === 0) {\r\n\t\t\t\t\t\tthis.connected = true;\r\n\t\t\t\t\t\t// Jump to the end of the list of uris and stop looking for a good host.\r\n\t\t\t\t\t\tif (this.connectOptions.uris)\r\n\t\t\t\t\t\t\tthis.hostIndex = this.connectOptions.uris.length;\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tthis._disconnected(ERROR.CONNACK_RETURNCODE.code, format(ERROR.CONNACK_RETURNCODE, [wireMessage.returnCode, CONNACK_RC[wireMessage.returnCode]]));\r\n\t\t\t\t\t\tbreak;\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t// Resend messages.\r\n\t\t\t\t\tvar sequencedMessages = new Array();\r\n\t\t\t\t\tfor (var msgId in this._sentMessages) {\r\n\t\t\t\t\t\tif (this._sentMessages.hasOwnProperty(msgId))\r\n\t\t\t\t\t\t\tsequencedMessages.push(this._sentMessages[msgId]);\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t// Sort sentMessages into the original sent order.\r\n\t\t\t\t\tvar sequencedMessages = sequencedMessages.sort(function (a, b) { return a.sequence - b.sequence; });\r\n\t\t\t\t\tfor (var i = 0, len = sequencedMessages.length; i < len; i++) {\r\n\t\t\t\t\t\tvar sentMessage = sequencedMessages[i];\r\n\t\t\t\t\t\tif (sentMessage.type == MESSAGE_TYPE.PUBLISH && sentMessage.pubRecReceived) {\r\n\t\t\t\t\t\t\tvar pubRelMessage = new WireMessage(MESSAGE_TYPE.PUBREL, { messageIdentifier: sentMessage.messageIdentifier });\r\n\t\t\t\t\t\t\tthis._schedule_message(pubRelMessage);\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\tthis._schedule_message(sentMessage);\r\n\t\t\t\t\t\t};\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t// Execute the connectOptions.onSuccess callback if there is one.\r\n\t\t\t\t\tif (this.connectOptions.onSuccess) {\r\n\t\t\t\t\t\tthis.connectOptions.onSuccess({ invocationContext: this.connectOptions.invocationContext });\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t// Process all queued messages now that the connection is established. \r\n\t\t\t\t\tthis._process_queue();\r\n\t\t\t\t\tbreak;\r\n\r\n\t\t\t\tcase MESSAGE_TYPE.PUBLISH:\r\n\t\t\t\t\tthis._receivePublish(wireMessage);\r\n\t\t\t\t\tbreak;\r\n\r\n\t\t\t\tcase MESSAGE_TYPE.PUBACK:\r\n\t\t\t\t\tvar sentMessage = this._sentMessages[wireMessage.messageIdentifier];\r\n\t\t\t\t\t// If this is a re flow of a PUBACK after we have restarted receivedMessage will not exist.\r\n\t\t\t\t\tif (sentMessage) {\r\n\t\t\t\t\t\tdelete this._sentMessages[wireMessage.messageIdentifier];\r\n\t\t\t\t\t\tlocalStorage.removeItem(\"sns_Sent:\" + this._localKey + wireMessage.messageIdentifier);\r\n\t\t\t\t\t\tif (this.onMessageDelivered)\r\n\t\t\t\t\t\t\tthis.onMessageDelivered(sentMessage.payloadMessage);\r\n\t\t\t\t\t}\r\n\t\t\t\t\tbreak;\r\n\r\n\t\t\t\tcase MESSAGE_TYPE.PUBREC:\r\n\t\t\t\t\tvar sentMessage = this._sentMessages[wireMessage.messageIdentifier];\r\n\t\t\t\t\t// If this is a re flow of a PUBREC after we have restarted receivedMessage will not exist.\r\n\t\t\t\t\tif (sentMessage) {\r\n\t\t\t\t\t\tsentMessage.pubRecReceived = true;\r\n\t\t\t\t\t\tvar pubRelMessage = new WireMessage(MESSAGE_TYPE.PUBREL, { messageIdentifier: wireMessage.messageIdentifier });\r\n\t\t\t\t\t\tthis.store(\"Sent:\", sentMessage);\r\n\t\t\t\t\t\tthis._schedule_message(pubRelMessage);\r\n\t\t\t\t\t}\r\n\t\t\t\t\tbreak;\r\n\r\n\t\t\t\tcase MESSAGE_TYPE.PUBREL:\r\n\t\t\t\t\tvar receivedMessage = this._receivedMessages[wireMessage.messageIdentifier];\r\n\t\t\t\t\tlocalStorage.removeItem(\"sns_Received:\" + this._localKey + wireMessage.messageIdentifier);\r\n\t\t\t\t\t// If this is a re flow of a PUBREL after we have restarted receivedMessage will not exist.\r\n\t\t\t\t\tif (receivedMessage) {\r\n\t\t\t\t\t\tthis._receiveMessage(receivedMessage);\r\n\t\t\t\t\t\tdelete this._receivedMessages[wireMessage.messageIdentifier];\r\n\t\t\t\t\t}\r\n\t\t\t\t\t// Always flow PubComp, we may have previously flowed PubComp but the server lost it and restarted.\r\n\t\t\t\t\tpubCompMessage = new WireMessage(MESSAGE_TYPE.PUBCOMP, { messageIdentifier: wireMessage.messageIdentifier });\r\n\t\t\t\t\tthis._schedule_message(pubCompMessage);\r\n\r\n\r\n\t\t\t\t\tbreak;\r\n\r\n\t\t\t\tcase MESSAGE_TYPE.PUBCOMP:\r\n\t\t\t\t\tvar sentMessage = this._sentMessages[wireMessage.messageIdentifier];\r\n\t\t\t\t\tdelete this._sentMessages[wireMessage.messageIdentifier];\r\n\t\t\t\t\tlocalStorage.removeItem(\"sns_Sent:\" + this._localKey + wireMessage.messageIdentifier);\r\n\t\t\t\t\tif (this.onMessageDelivered)\r\n\t\t\t\t\t\tthis.onMessageDelivered(sentMessage.payloadMessage);\r\n\t\t\t\t\tbreak;\r\n\r\n\t\t\t\tcase MESSAGE_TYPE.SUBACK:\r\n\t\t\t\t\tvar sentMessage = this._sentMessages[wireMessage.messageIdentifier];\r\n\t\t\t\t\tif (sentMessage) {\r\n\t\t\t\t\t\tif (sentMessage.timeOut)\r\n\t\t\t\t\t\t\tsentMessage.timeOut.cancel();\r\n\t\t\t\t\t\twireMessage.returnCode.indexOf = Array.prototype.indexOf;\r\n\t\t\t\t\t\tif (wireMessage.returnCode.indexOf(0x80) !== -1) {\r\n\t\t\t\t\t\t\tif (sentMessage.onFailure) {\r\n\t\t\t\t\t\t\t\tsentMessage.onFailure(wireMessage.returnCode);\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t} else if (sentMessage.onSuccess) {\r\n\t\t\t\t\t\t\tsentMessage.onSuccess(wireMessage.returnCode);\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tdelete this._sentMessages[wireMessage.messageIdentifier];\r\n\t\t\t\t\t}\r\n\t\t\t\t\tbreak;\r\n\r\n\t\t\t\tcase MESSAGE_TYPE.UNSUBACK:\r\n\t\t\t\t\tvar sentMessage = this._sentMessages[wireMessage.messageIdentifier];\r\n\t\t\t\t\tif (sentMessage) {\r\n\t\t\t\t\t\tif (sentMessage.timeOut)\r\n\t\t\t\t\t\t\tsentMessage.timeOut.cancel();\r\n\t\t\t\t\t\tif (sentMessage.callback) {\r\n\t\t\t\t\t\t\tsentMessage.callback();\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tdelete this._sentMessages[wireMessage.messageIdentifier];\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\tbreak;\r\n\r\n\t\t\t\tcase MESSAGE_TYPE.PINGRESP:\r\n\t\t\t\t\t/* The sendPinger or receivePinger may have sent a ping, the receivePinger has already been reset. */\r\n\t\t\t\t\tthis.sendPinger.reset();\r\n\t\t\t\t\tbreak;\r\n\r\n\t\t\t\tcase MESSAGE_TYPE.DISCONNECT:\r\n\t\t\t\t\t// Clients do not expect to receive disconnect packets.\r\n\t\t\t\t\tthis._disconnected(ERROR.INVALID_MQTT_MESSAGE_TYPE.code, format(ERROR.INVALID_MQTT_MESSAGE_TYPE, [wireMessage.type]));\r\n\t\t\t\t\tbreak;\r\n\r\n\t\t\t\tdefault:\r\n\t\t\t\t\tthis._disconnected(ERROR.INVALID_MQTT_MESSAGE_TYPE.code, format(ERROR.INVALID_MQTT_MESSAGE_TYPE, [wireMessage.type]));\r\n\t\t\t};\r\n\t\t} catch (error) {\r\n\t\t\tthis._disconnected(ERROR.INTERNAL_ERROR.code, format(ERROR.INTERNAL_ERROR, [error.message]));\r\n\t\t\treturn;\r\n\t\t}\r\n\t};\r\n\r\n\t/** @ignore */\r\n\tClientImpl.prototype._on_socket_error = function (error) {\r\n\t\tthis._disconnected(ERROR.SOCKET_ERROR.code, format(ERROR.SOCKET_ERROR, [error.data]));\r\n\t};\r\n\r\n\t/** @ignore */\r\n\tClientImpl.prototype._on_socket_close = function () {\r\n\t\tthis._disconnected(ERROR.SOCKET_CLOSE.code, format(ERROR.SOCKET_CLOSE));\r\n\t};\r\n\r\n\t/** @ignore */\r\n\tClientImpl.prototype._socket_send = function (wireMessage) {\r\n\r\n\t\tif (wireMessage.type == 1) {\r\n\t\t\tvar wireMessageMasked = this._traceMask(wireMessage, \"password\");\r\n\t\t\tthis._trace(\"Client._socket_send\", wireMessageMasked);\r\n\t\t}\r\n\t\telse this._trace(\"Client._socket_send\", wireMessage);\r\n\r\n\t\tthis.socket.send(wireMessage.encode());\r\n\t\t/* We have proved to the server we are alive. */\r\n\t\tthis.sendPinger.reset();\r\n\t};\r\n\r\n\t/** @ignore */\r\n\tClientImpl.prototype._receivePublish = function (wireMessage) {\r\n\t\tswitch (wireMessage.payloadMessage.qos) {\r\n\t\t\tcase \"undefined\":\r\n\t\t\tcase 0:\r\n\t\t\t\tthis._receiveMessage(wireMessage);\r\n\t\t\t\tbreak;\r\n\r\n\t\t\tcase 1:\r\n\t\t\t\tvar pubAckMessage = new WireMessage(MESSAGE_TYPE.PUBACK, { messageIdentifier: wireMessage.messageIdentifier });\r\n\t\t\t\tthis._schedule_message(pubAckMessage);\r\n\t\t\t\tthis._receiveMessage(wireMessage);\r\n\t\t\t\tbreak;\r\n\r\n\t\t\tcase 2:\r\n\t\t\t\tthis._receivedMessages[wireMessage.messageIdentifier] = wireMessage;\r\n\t\t\t\tthis.store(\"Received:\", wireMessage);\r\n\t\t\t\tvar pubRecMessage = new WireMessage(MESSAGE_TYPE.PUBREC, { messageIdentifier: wireMessage.messageIdentifier });\r\n\t\t\t\tthis._schedule_message(pubRecMessage);\r\n\r\n\t\t\t\tbreak;\r\n\r\n\t\t\tdefault:\r\n\t\t\t\tthrow Error(\"Invaild qos=\" + wireMmessage.payloadMessage.qos);\r\n\t\t};\r\n\t};\r\n\r\n\t/** @ignore */\r\n\tClientImpl.prototype._receiveMessage = function (wireMessage) {\r\n\t\tif (this.onMessageArrived) {\r\n\t\t\tthis.onMessageArrived(wireMessage.payloadMessage);\r\n\t\t}\r\n\t};\r\n\r\n\t/**\r\n\t * Client has disconnected either at its own request or because the server\r\n\t * or network disconnected it. Remove all non-durable state.\r\n\t * @param {errorCode} [number] the error number.\r\n\t * @param {errorText} [string] the error text.\r\n\t * @ignore\r\n\t */\r\n\tClientImpl.prototype._disconnected = function (errorCode, errorText) {\r\n\t\tthis._trace(\"Client._disconnected\", errorCode, errorText);\r\n\r\n\t\tthis.sendPinger.cancel();\r\n\t\tthis.receivePinger.cancel();\r\n\t\tif (this._connectTimeout)\r\n\t\t\tthis._connectTimeout.cancel();\r\n\t\t// Clear message buffers.\r\n\t\tthis._msg_queue = [];\r\n\t\tthis._notify_msg_sent = {};\r\n\r\n\t\tif (this.socket) {\r\n\t\t\t// Cancel all socket callbacks so that they cannot be driven again by this socket.\r\n\t\t\tthis.socket.onopen = null;\r\n\t\t\tthis.socket.onmessage = null;\r\n\t\t\tthis.socket.onerror = null;\r\n\t\t\tthis.socket.onclose = null;\r\n\t\t\tif (this.socket.readyState === 1)\r\n\t\t\t\tthis.socket.close();\r\n\t\t\tdelete this.socket;\r\n\t\t}\r\n\r\n\t\tif (this.connectOptions.uris && this.hostIndex < this.connectOptions.uris.length - 1) {\r\n\t\t\t// Try the next host.\r\n\t\t\tthis.hostIndex++;\r\n\t\t\tthis._doConnect(this.connectOptions.uris[this.hostIndex]);\r\n\r\n\t\t} else {\r\n\r\n\t\t\tif (errorCode === undefined) {\r\n\t\t\t\terrorCode = ERROR.OK.code;\r\n\t\t\t\terrorText = format(ERROR.OK);\r\n\t\t\t}\r\n\r\n\t\t\t// Run any application callbacks last as they may attempt to reconnect and hence create a new socket.\r\n\t\t\tif (this.connected) {\r\n\t\t\t\tthis.connected = false;\r\n\t\t\t\t// Execute the connectionLostCallback if there is one, and we were connected.       \r\n\t\t\t\tif (this.onConnectionLost)\r\n\t\t\t\t\tconsole.log(\"do onConnectionLost !...................\");\r\n\t\t\t\tthis.onConnectionLost({ errorCode: errorCode, errorMessage: errorText });\r\n\t\t\t} else {\r\n\t\t\t\t// Otherwise we never had a connection, so indicate that the connect has failed.\r\n\t\t\t\tif (this.connectOptions.mqttVersion === 4 && this.connectOptions.mqttVersionExplicit === false) {\r\n\t\t\t\t\tthis._trace(\"Failed to connect V4, dropping back to V3\")\r\n\t\t\t\t\tthis.connectOptions.mqttVersion = 3;\r\n\t\t\t\t\tif (this.connectOptions.uris) {\r\n\t\t\t\t\t\tthis.hostIndex = 0;\r\n\t\t\t\t\t\tthis._doConnect(this.connectOptions.uris[0]);\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tthis._doConnect(this.uri);\r\n\t\t\t\t\t}\r\n\t\t\t\t} else if (this.connectOptions.onFailure) {\r\n\t\t\t\t\tthis.connectOptions.onFailure({ invocationContext: this.connectOptions.invocationContext, errorCode: errorCode, errorMessage: errorText });\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t};\r\n\r\n\t/** @ignore */\r\n\tClientImpl.prototype._trace = function () {\r\n\t\tif (this._traceBuffer !== null) {\r\n\t\t\tfor (var i = 0, max = arguments.length; i < max; i++) {\r\n\t\t\t\tif (this._traceBuffer.length == this._MAX_TRACE_ENTRIES) {\r\n\t\t\t\t\tthis._traceBuffer.shift();\r\n\t\t\t\t}\r\n\t\t\t\tif (i === 0) this._traceBuffer.push(arguments[i]);\r\n\t\t\t\telse if (typeof arguments[i] === \"undefined\") this._traceBuffer.push(arguments[i]);\r\n\t\t\t\telse this._traceBuffer.push(\"  \" + JSON.stringify(arguments[i]));\r\n\t\t\t};\r\n\t\t};\r\n\t};\r\n\r\n\t/** @ignore */\r\n\tClientImpl.prototype._traceMask = function (traceObject, masked) {\r\n\t\tvar traceObjectMasked = {};\r\n\t\tfor (var attr in traceObject) {\r\n\t\t\tif (traceObject.hasOwnProperty(attr)) {\r\n\t\t\t\tif (attr == masked)\r\n\t\t\t\t\ttraceObjectMasked[attr] = \"******\";\r\n\t\t\t\telse\r\n\t\t\t\t\ttraceObjectMasked[attr] = traceObject[attr];\r\n\t\t\t}\r\n\t\t}\r\n\t\treturn traceObjectMasked;\r\n\t};\r\n\r\n\t// ------------------------------------------------------------------------\r\n\t// Public Programming interface.\r\n\t// ------------------------------------------------------------------------\r\n\r\n\t/** \r\n\t * The JavaScript application communicates to the server using a {@link Paho.MQTT.Client} object. \r\n\t * <p>\r\n\t * Most applications will create just one Client object and then call its connect() method,\r\n\t * however applications can create more than one Client object if they wish. \r\n\t * In this case the combination of host, port and clientId attributes must be different for each Client object.\r\n\t * <p>\r\n\t * The send, subscribe and unsubscribe methods are implemented as asynchronous JavaScript methods \r\n\t * (even though the underlying protocol exchange might be synchronous in nature). \r\n\t * This means they signal their completion by calling back to the application, \r\n\t * via Success or Failure callback functions provided by the application on the method in question. \r\n\t * Such callbacks are called at most once per method invocation and do not persist beyond the lifetime \r\n\t * of the script that made the invocation.\r\n\t * <p>\r\n\t * In contrast there are some callback functions, most notably <i>onMessageArrived</i>, \r\n\t * that are defined on the {@link Paho.MQTT.Client} object.  \r\n\t * These may get called multiple times, and aren't directly related to specific method invocations made by the client. \r\n\t *\r\n\t * @name Paho.MQTT.Client    \r\n\t * \r\n\t * @constructor\r\n\t *  \r\n\t * @param {string} host - the address of the messaging server, as a fully qualified WebSocket URI, as a DNS name or dotted decimal IP address.\r\n\t * @param {number} port - the port number to connect to - only required if host is not a URI\r\n\t * @param {string} path - the path on the host to connect to - only used if host is not a URI. Default: '/mqtt'.\r\n\t * @param {string} clientId - the Messaging client identifier, between 1 and 23 characters in length.\r\n\t * \r\n\t * @property {string} host - <i>read only</i> the server's DNS hostname or dotted decimal IP address.\r\n\t * @property {number} port - <i>read only</i> the server's port.\r\n\t * @property {string} path - <i>read only</i> the server's path.\r\n\t * @property {string} clientId - <i>read only</i> used when connecting to the server.\r\n\t * @property {function} onConnectionLost - called when a connection has been lost. \r\n\t *                            after a connect() method has succeeded.\r\n\t *                            Establish the call back used when a connection has been lost. The connection may be\r\n\t *                            lost because the client initiates a disconnect or because the server or network \r\n\t *                            cause the client to be disconnected. The disconnect call back may be called without \r\n\t *                            the connectionComplete call back being invoked if, for example the client fails to \r\n\t *                            connect.\r\n\t *                            A single response object parameter is passed to the onConnectionLost callback containing the following fields:\r\n\t *                            <ol>   \r\n\t *                            <li>errorCode\r\n\t *                            <li>errorMessage       \r\n\t *                            </ol>\r\n\t * @property {function} onMessageDelivered called when a message has been delivered. \r\n\t *                            All processing that this Client will ever do has been completed. So, for example,\r\n\t *                            in the case of a Qos=2 message sent by this client, the PubComp flow has been received from the server\r\n\t *                            and the message has been removed from persistent storage before this callback is invoked. \r\n\t *                            Parameters passed to the onMessageDelivered callback are:\r\n\t *                            <ol>   \r\n\t *                            <li>{@link Paho.MQTT.Message} that was delivered.\r\n\t *                            </ol>    \r\n\t * @property {function} onMessageArrived called when a message has arrived in this Paho.MQTT.client. \r\n\t *                            Parameters passed to the onMessageArrived callback are:\r\n\t *                            <ol>   \r\n\t *                            <li>{@link Paho.MQTT.Message} that has arrived.\r\n\t *                            </ol>    \r\n\t */\r\n\tvar Client = function (host, port, path, clientId) {\r\n\r\n\t\tvar uri;\r\n\r\n\t\tif (typeof host !== \"string\")\r\n\t\t\tthrow new Error(format(ERROR.INVALID_TYPE, [typeof host, \"host\"]));\r\n\r\n\t\tif (arguments.length == 2) {\r\n\t\t\t// host: must be full ws:// uri\r\n\t\t\t// port: clientId\r\n\t\t\tclientId = port;\r\n\t\t\turi = host;\r\n\t\t\tvar match = uri.match(/^(wss?):\\/\\/((\\[(.+)\\])|([^\\/]+?))(:(\\d+))?(\\/.*)$/);\r\n\t\t\tif (match) {\r\n\t\t\t\thost = match[4] || match[2];\r\n\t\t\t\tport = parseInt(match[7]);\r\n\t\t\t\tpath = match[8];\r\n\t\t\t} else {\r\n\t\t\t\tthrow new Error(format(ERROR.INVALID_ARGUMENT, [host, \"host\"]));\r\n\t\t\t}\r\n\t\t} else {\r\n\t\t\tif (arguments.length == 3) {\r\n\t\t\t\tclientId = path;\r\n\t\t\t\tpath = \"/mqtt\";\r\n\t\t\t}\r\n\t\t\tif (typeof port !== \"number\" || port < 0)\r\n\t\t\t\tthrow new Error(format(ERROR.INVALID_TYPE, [typeof port, \"port\"]));\r\n\t\t\tif (typeof path !== \"string\")\r\n\t\t\t\tthrow new Error(format(ERROR.INVALID_TYPE, [typeof path, \"path\"]));\r\n\r\n\t\t\tvar ipv6AddSBracket = (host.indexOf(\":\") != -1 && host.slice(0, 1) != \"[\" && host.slice(-1) != \"]\");\r\n\t\t\turi = \"ws://\" + (ipv6AddSBracket ? \"[\" + host + \"]\" : host) + \":\" + port + path;\r\n\t\t}\r\n\r\n\t\tvar clientIdLength = 0;\r\n\t\tfor (var i = 0; i < clientId.length; i++) {\r\n\t\t\tvar charCode = clientId.charCodeAt(i);\r\n\t\t\tif (0xD800 <= charCode && charCode <= 0xDBFF) {\r\n\t\t\t\ti++; // Surrogate pair.\r\n\t\t\t}\r\n\t\t\tclientIdLength++;\r\n\t\t}\r\n\t\tif (typeof clientId !== \"string\" || clientIdLength > 65535)\r\n\t\t\tthrow new Error(format(ERROR.INVALID_ARGUMENT, [clientId, \"clientId\"]));\r\n\r\n\t\tvar client = new ClientImpl(uri, host, port, path, clientId);\r\n\t\tthis._getHost = function () { return host; };\r\n\t\tthis._setHost = function () { throw new Error(format(ERROR.UNSUPPORTED_OPERATION)); };\r\n\r\n\t\tthis._getPort = function () { return port; };\r\n\t\tthis._setPort = function () { throw new Error(format(ERROR.UNSUPPORTED_OPERATION)); };\r\n\r\n\t\tthis._getPath = function () { return path; };\r\n\t\tthis._setPath = function () { throw new Error(format(ERROR.UNSUPPORTED_OPERATION)); };\r\n\r\n\t\tthis._getURI = function () { return uri; };\r\n\t\tthis._setURI = function () { throw new Error(format(ERROR.UNSUPPORTED_OPERATION)); };\r\n\r\n\t\tthis._getClientId = function () { return client.clientId; };\r\n\t\tthis._setClientId = function () { throw new Error(format(ERROR.UNSUPPORTED_OPERATION)); };\r\n\r\n\t\tthis._getOnConnectionLost = function () { return client.onConnectionLost; };\r\n\t\tthis._setOnConnectionLost = function (newOnConnectionLost) {\r\n\t\t\tif (typeof newOnConnectionLost === \"function\")\r\n\t\t\t\tclient.onConnectionLost = newOnConnectionLost;\r\n\t\t\telse\r\n\t\t\t\tthrow new Error(format(ERROR.INVALID_TYPE, [typeof newOnConnectionLost, \"onConnectionLost\"]));\r\n\t\t};\r\n\r\n\t\tthis._getOnMessageDelivered = function () { return client.onMessageDelivered; };\r\n\t\tthis._setOnMessageDelivered = function (newOnMessageDelivered) {\r\n\t\t\tif (typeof newOnMessageDelivered === \"function\")\r\n\t\t\t\tclient.onMessageDelivered = newOnMessageDelivered;\r\n\t\t\telse\r\n\t\t\t\tthrow new Error(format(ERROR.INVALID_TYPE, [typeof newOnMessageDelivered, \"onMessageDelivered\"]));\r\n\t\t};\r\n\r\n\t\tthis._getOnMessageArrived = function () { return client.onMessageArrived; };\r\n\t\tthis._setOnMessageArrived = function (newOnMessageArrived) {\r\n\t\t\tif (typeof newOnMessageArrived === \"function\")\r\n\t\t\t\tclient.onMessageArrived = newOnMessageArrived;\r\n\t\t\telse\r\n\t\t\t\tthrow new Error(format(ERROR.INVALID_TYPE, [typeof newOnMessageArrived, \"onMessageArrived\"]));\r\n\t\t};\r\n\r\n\t\t/** \r\n\t\t * Connect this Messaging client to its server. \r\n\t\t * \r\n\t\t * @name Paho.MQTT.Client#connect\r\n\t\t * @function\r\n\t\t * @param {Object} connectOptions - attributes used with the connection. \r\n\t\t * @param {number} connectOptions.timeout - If the connect has not succeeded within this \r\n\t\t *                    number of seconds, it is deemed to have failed.\r\n\t\t *                    The default is 30 seconds.\r\n\t\t * @param {string} connectOptions.userName - Authentication username for this connection.\r\n\t\t * @param {string} connectOptions.password - Authentication password for this connection.\r\n\t\t * @param {Paho.MQTT.Message} connectOptions.willMessage - sent by the server when the client\r\n\t\t *                    disconnects abnormally.\r\n\t\t * @param {Number} connectOptions.keepAliveInterval - the server disconnects this client if\r\n\t\t *                    there is no activity for this number of seconds.\r\n\t\t *                    The default value of 60 seconds is assumed if not set.\r\n\t\t * @param {boolean} connectOptions.cleanSession - if true(default) the client and server \r\n\t\t *                    persistent state is deleted on successful connect.\r\n\t\t * @param {boolean} connectOptions.useSSL - if present and true, use an SSL Websocket connection.\r\n\t\t * @param {object} connectOptions.invocationContext - passed to the onSuccess callback or onFailure callback.\r\n\t\t * @param {function} connectOptions.onSuccess - called when the connect acknowledgement \r\n\t\t *                    has been received from the server.\r\n\t\t * A single response object parameter is passed to the onSuccess callback containing the following fields:\r\n\t\t * <ol>\r\n\t\t * <li>invocationContext as passed in to the onSuccess method in the connectOptions.       \r\n\t\t * </ol>\r\n\t\t * @config {function} [onFailure] called when the connect request has failed or timed out.\r\n\t\t * A single response object parameter is passed to the onFailure callback containing the following fields:\r\n\t\t * <ol>\r\n\t\t * <li>invocationContext as passed in to the onFailure method in the connectOptions.       \r\n\t\t * <li>errorCode a number indicating the nature of the error.\r\n\t\t * <li>errorMessage text describing the error.      \r\n\t\t * </ol>\r\n\t\t * @config {Array} [hosts] If present this contains either a set of hostnames or fully qualified\r\n\t\t * WebSocket URIs (ws://example.com:1883/mqtt), that are tried in order in place \r\n\t\t * of the host and port paramater on the construtor. The hosts are tried one at at time in order until\r\n\t\t * one of then succeeds.\r\n\t\t * @config {Array} [ports] If present the set of ports matching the hosts. If hosts contains URIs, this property\r\n\t\t * is not used.\r\n\t\t * @throws {InvalidState} if the client is not in disconnected state. The client must have received connectionLost\r\n\t\t * or disconnected before calling connect for a second or subsequent time.\r\n\t\t */\r\n\t\tthis.connect = function (connectOptions) {\r\n\t\t\tconnectOptions = connectOptions || {};\r\n\t\t\tvalidate(connectOptions, {\r\n\t\t\t\ttimeout: \"number\",\r\n\t\t\t\tuserName: \"string\",\r\n\t\t\t\tpassword: \"string\",\r\n\t\t\t\twillMessage: \"object\",\r\n\t\t\t\tkeepAliveInterval: \"number\",\r\n\t\t\t\tcleanSession: \"boolean\",\r\n\t\t\t\tuseSSL: \"boolean\",\r\n\t\t\t\tinvocationContext: \"object\",\r\n\t\t\t\tonSuccess: \"function\",\r\n\t\t\t\tonFailure: \"function\",\r\n\t\t\t\thosts: \"object\",\r\n\t\t\t\tports: \"object\",\r\n\t\t\t\tmqttVersion: \"number\"\r\n\t\t\t});\r\n\r\n\t\t\t// If no keep alive interval is set, assume 60 seconds.\r\n\t\t\tif (connectOptions.keepAliveInterval === undefined)\r\n\t\t\t\tconnectOptions.keepAliveInterval = 60;\r\n\r\n\t\t\tif (connectOptions.mqttVersion > 4 || connectOptions.mqttVersion < 3) {\r\n\t\t\t\tthrow new Error(format(ERROR.INVALID_ARGUMENT, [connectOptions.mqttVersion, \"connectOptions.mqttVersion\"]));\r\n\t\t\t}\r\n\r\n\t\t\tif (connectOptions.mqttVersion === undefined) {\r\n\t\t\t\tconnectOptions.mqttVersionExplicit = false;\r\n\t\t\t\tconnectOptions.mqttVersion = 4;\r\n\t\t\t} else {\r\n\t\t\t\tconnectOptions.mqttVersionExplicit = true;\r\n\t\t\t}\r\n\r\n\t\t\t//Check that if password is set, so is username\r\n\t\t\tif (connectOptions.password === undefined && connectOptions.userName !== undefined)\r\n\t\t\t\tthrow new Error(format(ERROR.INVALID_ARGUMENT, [connectOptions.password, \"connectOptions.password\"]))\r\n\r\n\t\t\tif (connectOptions.willMessage) {\r\n\t\t\t\tif (!(connectOptions.willMessage instanceof Message))\r\n\t\t\t\t\tthrow new Error(format(ERROR.INVALID_TYPE, [connectOptions.willMessage, \"connectOptions.willMessage\"]));\r\n\t\t\t\t// The will message must have a payload that can be represented as a string.\r\n\t\t\t\t// Cause the willMessage to throw an exception if this is not the case.\r\n\t\t\t\tconnectOptions.willMessage.stringPayload;\r\n\r\n\t\t\t\tif (typeof connectOptions.willMessage.destinationName === \"undefined\")\r\n\t\t\t\t\tthrow new Error(format(ERROR.INVALID_TYPE, [typeof connectOptions.willMessage.destinationName, \"connectOptions.willMessage.destinationName\"]));\r\n\t\t\t}\r\n\t\t\tif (typeof connectOptions.cleanSession === \"undefined\")\r\n\t\t\t\tconnectOptions.cleanSession = true;\r\n\t\t\tif (connectOptions.hosts) {\r\n\r\n\t\t\t\tif (!(connectOptions.hosts instanceof Array))\r\n\t\t\t\t\tthrow new Error(format(ERROR.INVALID_ARGUMENT, [connectOptions.hosts, \"connectOptions.hosts\"]));\r\n\t\t\t\tif (connectOptions.hosts.length < 1)\r\n\t\t\t\t\tthrow new Error(format(ERROR.INVALID_ARGUMENT, [connectOptions.hosts, \"connectOptions.hosts\"]));\r\n\r\n\t\t\t\tvar usingURIs = false;\r\n\t\t\t\tfor (var i = 0; i < connectOptions.hosts.length; i++) {\r\n\t\t\t\t\tif (typeof connectOptions.hosts[i] !== \"string\")\r\n\t\t\t\t\t\tthrow new Error(format(ERROR.INVALID_TYPE, [typeof connectOptions.hosts[i], \"connectOptions.hosts[\" + i + \"]\"]));\r\n\t\t\t\t\tif (/^(wss?):\\/\\/((\\[(.+)\\])|([^\\/]+?))(:(\\d+))?(\\/.*)$/.test(connectOptions.hosts[i])) {\r\n\t\t\t\t\t\tif (i == 0) {\r\n\t\t\t\t\t\t\tusingURIs = true;\r\n\t\t\t\t\t\t} else if (!usingURIs) {\r\n\t\t\t\t\t\t\tthrow new Error(format(ERROR.INVALID_ARGUMENT, [connectOptions.hosts[i], \"connectOptions.hosts[\" + i + \"]\"]));\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t} else if (usingURIs) {\r\n\t\t\t\t\t\tthrow new Error(format(ERROR.INVALID_ARGUMENT, [connectOptions.hosts[i], \"connectOptions.hosts[\" + i + \"]\"]));\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\r\n\t\t\t\tif (!usingURIs) {\r\n\t\t\t\t\tif (!connectOptions.ports)\r\n\t\t\t\t\t\tthrow new Error(format(ERROR.INVALID_ARGUMENT, [connectOptions.ports, \"connectOptions.ports\"]));\r\n\t\t\t\t\tif (!(connectOptions.ports instanceof Array))\r\n\t\t\t\t\t\tthrow new Error(format(ERROR.INVALID_ARGUMENT, [connectOptions.ports, \"connectOptions.ports\"]));\r\n\t\t\t\t\tif (connectOptions.hosts.length != connectOptions.ports.length)\r\n\t\t\t\t\t\tthrow new Error(format(ERROR.INVALID_ARGUMENT, [connectOptions.ports, \"connectOptions.ports\"]));\r\n\r\n\t\t\t\t\tconnectOptions.uris = [];\r\n\r\n\t\t\t\t\tfor (var i = 0; i < connectOptions.hosts.length; i++) {\r\n\t\t\t\t\t\tif (typeof connectOptions.ports[i] !== \"number\" || connectOptions.ports[i] < 0)\r\n\t\t\t\t\t\t\tthrow new Error(format(ERROR.INVALID_TYPE, [typeof connectOptions.ports[i], \"connectOptions.ports[\" + i + \"]\"]));\r\n\t\t\t\t\t\tvar host = connectOptions.hosts[i];\r\n\t\t\t\t\t\tvar port = connectOptions.ports[i];\r\n\r\n\t\t\t\t\t\tvar ipv6 = (host.indexOf(\":\") != -1);\r\n\t\t\t\t\t\turi = \"ws://\" + (ipv6 ? \"[\" + host + \"]\" : host) + \":\" + port + path;\r\n\t\t\t\t\t\tconnectOptions.uris.push(uri);\r\n\t\t\t\t\t}\r\n\t\t\t\t} else {\r\n\t\t\t\t\tconnectOptions.uris = connectOptions.hosts;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\tclient.connect(connectOptions);\r\n\t\t};\r\n\r\n\t\t/** \r\n\t\t * Subscribe for messages, request receipt of a copy of messages sent to the destinations described by the filter.\r\n\t\t * \r\n\t\t * @name Paho.MQTT.Client#subscribe\r\n\t\t * @function\r\n\t\t * @param {string} filter describing the destinations to receive messages from.\r\n\t\t * <br>\r\n\t\t * @param {object} subscribeOptions - used to control the subscription\r\n\t\t *\r\n\t\t * @param {number} subscribeOptions.qos - the maiximum qos of any publications sent \r\n\t\t *                                  as a result of making this subscription.\r\n\t\t * @param {object} subscribeOptions.invocationContext - passed to the onSuccess callback \r\n\t\t *                                  or onFailure callback.\r\n\t\t * @param {function} subscribeOptions.onSuccess - called when the subscribe acknowledgement\r\n\t\t *                                  has been received from the server.\r\n\t\t *                                  A single response object parameter is passed to the onSuccess callback containing the following fields:\r\n\t\t *                                  <ol>\r\n\t\t *                                  <li>invocationContext if set in the subscribeOptions.       \r\n\t\t *                                  </ol>\r\n\t\t * @param {function} subscribeOptions.onFailure - called when the subscribe request has failed or timed out.\r\n\t\t *                                  A single response object parameter is passed to the onFailure callback containing the following fields:\r\n\t\t *                                  <ol>\r\n\t\t *                                  <li>invocationContext - if set in the subscribeOptions.       \r\n\t\t *                                  <li>errorCode - a number indicating the nature of the error.\r\n\t\t *                                  <li>errorMessage - text describing the error.      \r\n\t\t *                                  </ol>\r\n\t\t * @param {number} subscribeOptions.timeout - which, if present, determines the number of\r\n\t\t *                                  seconds after which the onFailure calback is called.\r\n\t\t *                                  The presence of a timeout does not prevent the onSuccess\r\n\t\t *                                  callback from being called when the subscribe completes.         \r\n\t\t * @throws {InvalidState} if the client is not in connected state.\r\n\t\t */\r\n\t\tthis.subscribe = function (filter, subscribeOptions) {\r\n\t\t\tif (typeof filter !== \"string\")\r\n\t\t\t\tthrow new Error(\"Invalid argument:\" + filter);\r\n\t\t\tsubscribeOptions = subscribeOptions || {};\r\n\t\t\tvalidate(subscribeOptions, {\r\n\t\t\t\tqos: \"number\",\r\n\t\t\t\tinvocationContext: \"object\",\r\n\t\t\t\tonSuccess: \"function\",\r\n\t\t\t\tonFailure: \"function\",\r\n\t\t\t\ttimeout: \"number\"\r\n\t\t\t});\r\n\t\t\tif (subscribeOptions.timeout && !subscribeOptions.onFailure)\r\n\t\t\t\tthrow new Error(\"subscribeOptions.timeout specified with no onFailure callback.\");\r\n\t\t\tif (typeof subscribeOptions.qos !== \"undefined\"\r\n\t\t\t\t&& !(subscribeOptions.qos === 0 || subscribeOptions.qos === 1 || subscribeOptions.qos === 2))\r\n\t\t\t\tthrow new Error(format(ERROR.INVALID_ARGUMENT, [subscribeOptions.qos, \"subscribeOptions.qos\"]));\r\n\t\t\tclient.subscribe(filter, subscribeOptions);\r\n\t\t};\r\n\r\n\t\t/**\r\n\t\t * Unsubscribe for messages, stop receiving messages sent to destinations described by the filter.\r\n\t\t * \r\n\t\t * @name Paho.MQTT.Client#unsubscribe\r\n\t\t * @function\r\n\t\t * @param {string} filter - describing the destinations to receive messages from.\r\n\t\t * @param {object} unsubscribeOptions - used to control the subscription\r\n\t\t * @param {object} unsubscribeOptions.invocationContext - passed to the onSuccess callback \r\n\t\t\t\t\t\t\t\t\t\t\t  or onFailure callback.\r\n\t\t * @param {function} unsubscribeOptions.onSuccess - called when the unsubscribe acknowledgement has been received from the server.\r\n\t\t *                                    A single response object parameter is passed to the \r\n\t\t *                                    onSuccess callback containing the following fields:\r\n\t\t *                                    <ol>\r\n\t\t *                                    <li>invocationContext - if set in the unsubscribeOptions.     \r\n\t\t *                                    </ol>\r\n\t\t * @param {function} unsubscribeOptions.onFailure called when the unsubscribe request has failed or timed out.\r\n\t\t *                                    A single response object parameter is passed to the onFailure callback containing the following fields:\r\n\t\t *                                    <ol>\r\n\t\t *                                    <li>invocationContext - if set in the unsubscribeOptions.       \r\n\t\t *                                    <li>errorCode - a number indicating the nature of the error.\r\n\t\t *                                    <li>errorMessage - text describing the error.      \r\n\t\t *                                    </ol>\r\n\t\t * @param {number} unsubscribeOptions.timeout - which, if present, determines the number of seconds\r\n\t\t *                                    after which the onFailure callback is called. The presence of\r\n\t\t *                                    a timeout does not prevent the onSuccess callback from being\r\n\t\t *                                    called when the unsubscribe completes\r\n\t\t * @throws {InvalidState} if the client is not in connected state.\r\n\t\t */\r\n\t\tthis.unsubscribe = function (filter, unsubscribeOptions) {\r\n\t\t\tif (typeof filter !== \"string\")\r\n\t\t\t\tthrow new Error(\"Invalid argument:\" + filter);\r\n\t\t\tunsubscribeOptions = unsubscribeOptions || {};\r\n\t\t\tvalidate(unsubscribeOptions, {\r\n\t\t\t\tinvocationContext: \"object\",\r\n\t\t\t\tonSuccess: \"function\",\r\n\t\t\t\tonFailure: \"function\",\r\n\t\t\t\ttimeout: \"number\"\r\n\t\t\t});\r\n\t\t\tif (unsubscribeOptions.timeout && !unsubscribeOptions.onFailure)\r\n\t\t\t\tthrow new Error(\"unsubscribeOptions.timeout specified with no onFailure callback.\");\r\n\t\t\tclient.unsubscribe(filter, unsubscribeOptions);\r\n\t\t};\r\n\r\n\t\t/**\r\n\t\t * Send a message to the consumers of the destination in the Message.\r\n\t\t * \r\n\t\t * @name Paho.MQTT.Client#send\r\n\t\t * @function \r\n\t\t * @param {Paho.MQTT.Message} message to send.\r\n\t\t \r\n\t\t * @throws {InvalidState} if the client is not connected.\r\n\t\t */\r\n\t\tthis.send = function (message) {\r\n\t\t\tif (!(message instanceof Message))\r\n\t\t\t\tthrow new Error(\"Invalid argument:\" + typeof message);\r\n\t\t\tif (typeof message.destinationName === \"undefined\")\r\n\t\t\t\tthrow new Error(\"Invalid parameter Message.destinationName:\" + message.destinationName);\r\n\r\n\t\t\tclient.send(message);\r\n\t\t};\r\n\r\n\t\t/** \r\n\t\t * Normal disconnect of this Messaging client from its server.\r\n\t\t * \r\n\t\t * @name Paho.MQTT.Client#disconnect\r\n\t\t * @function\r\n\t\t * @throws {InvalidState} if the client is already disconnected.     \r\n\t\t */\r\n\t\tthis.disconnect = function () {\r\n\t\t\tclient.disconnect();\r\n\t\t};\r\n\r\n\t\t/** \r\n\t\t * Get the contents of the trace log.\r\n\t\t * \r\n\t\t * @name Paho.MQTT.Client#getTraceLog\r\n\t\t * @function\r\n\t\t * @return {Object[]} tracebuffer containing the time ordered trace records.\r\n\t\t */\r\n\t\tthis.getTraceLog = function () {\r\n\t\t\treturn client.getTraceLog();\r\n\t\t}\r\n\r\n\t\t/** \r\n\t\t * Start tracing.\r\n\t\t * \r\n\t\t * @name Paho.MQTT.Client#startTrace\r\n\t\t * @function\r\n\t\t */\r\n\t\tthis.startTrace = function () {\r\n\t\t\tclient.startTrace();\r\n\t\t};\r\n\r\n\t\t/** \r\n\t\t * Stop tracing.\r\n\t\t * \r\n\t\t * @name Paho.MQTT.Client#stopTrace\r\n\t\t * @function\r\n\t\t */\r\n\t\tthis.stopTrace = function () {\r\n\t\t\tclient.stopTrace();\r\n\t\t};\r\n\r\n\t\tthis.isConnected = function () {\r\n\t\t\treturn client.connected;\r\n\t\t};\r\n\t};\r\n\r\n\tClient.prototype = {\r\n\t\tget host() { return this._getHost(); },\r\n\t\tset host(newHost) { this._setHost(newHost); },\r\n\r\n\t\tget port() { return this._getPort(); },\r\n\t\tset port(newPort) { this._setPort(newPort); },\r\n\r\n\t\tget path() { return this._getPath(); },\r\n\t\tset path(newPath) { this._setPath(newPath); },\r\n\r\n\t\tget clientId() { return this._getClientId(); },\r\n\t\tset clientId(newClientId) { this._setClientId(newClientId); },\r\n\r\n\t\tget onConnectionLost() { return this._getOnConnectionLost(); },\r\n\t\tset onConnectionLost(newOnConnectionLost) { this._setOnConnectionLost(newOnConnectionLost); },\r\n\r\n\t\tget onMessageDelivered() { return this._getOnMessageDelivered(); },\r\n\t\tset onMessageDelivered(newOnMessageDelivered) { this._setOnMessageDelivered(newOnMessageDelivered); },\r\n\r\n\t\tget onMessageArrived() { return this._getOnMessageArrived(); },\r\n\t\tset onMessageArrived(newOnMessageArrived) { this._setOnMessageArrived(newOnMessageArrived); }\r\n\t};\r\n\r\n\t/** \r\n\t * An application message, sent or received.\r\n\t * <p>\r\n\t * All attributes may be null, which implies the default values.\r\n\t * \r\n\t * @name Paho.MQTT.Message\r\n\t * @constructor\r\n\t * @param {String|ArrayBuffer} payload The message data to be sent.\r\n\t * <p>\r\n\t * @property {string} payloadString <i>read only</i> The payload as a string if the payload consists of valid UTF-8 characters.\r\n\t * @property {ArrayBuffer} payloadBytes <i>read only</i> The payload as an ArrayBuffer.\r\n\t * <p>\r\n\t * @property {string} destinationName <b>mandatory</b> The name of the destination to which the message is to be sent\r\n\t *                    (for messages about to be sent) or the name of the destination from which the message has been received.\r\n\t *                    (for messages received by the onMessage function).\r\n\t * <p>\r\n\t * @property {number} qos The Quality of Service used to deliver the message.\r\n\t * <dl>\r\n\t *     <dt>0 Best effort (default).\r\n\t *     <dt>1 At least once.\r\n\t *     <dt>2 Exactly once.     \r\n\t * </dl>\r\n\t * <p>\r\n\t * @property {Boolean} retained If true, the message is to be retained by the server and delivered \r\n\t *                     to both current and future subscriptions.\r\n\t *                     If false the server only delivers the message to current subscribers, this is the default for new Messages. \r\n\t *                     A received message has the retained boolean set to true if the message was published \r\n\t *                     with the retained boolean set to true\r\n\t *                     and the subscrption was made after the message has been published. \r\n\t * <p>\r\n\t * @property {Boolean} duplicate <i>read only</i> If true, this message might be a duplicate of one which has already been received. \r\n\t *                     This is only set on messages received from the server.\r\n\t *                     \r\n\t */\r\n\tvar Message = function (newPayload) {\r\n\t\tvar payload;\r\n\t\tif (typeof newPayload === \"string\"\r\n\t\t\t|| newPayload instanceof ArrayBuffer\r\n\t\t\t|| newPayload instanceof Int8Array\r\n\t\t\t|| newPayload instanceof Uint8Array\r\n\t\t\t|| newPayload instanceof Int16Array\r\n\t\t\t|| newPayload instanceof Uint16Array\r\n\t\t\t|| newPayload instanceof Int32Array\r\n\t\t\t|| newPayload instanceof Uint32Array\r\n\t\t\t|| newPayload instanceof Float32Array\r\n\t\t\t|| newPayload instanceof Float64Array\r\n\t\t) {\r\n\t\t\tpayload = newPayload;\r\n\t\t} else {\r\n\t\t\tthrow (format(ERROR.INVALID_ARGUMENT, [newPayload, \"newPayload\"]));\r\n\t\t}\r\n\r\n\t\tthis._getPayloadString = function () {\r\n\t\t\tif (typeof payload === \"string\")\r\n\t\t\t\treturn payload;\r\n\t\t\telse\r\n\t\t\t\treturn parseUTF8(payload, 0, payload.length);\r\n\t\t};\r\n\r\n\t\tthis._getPayloadBytes = function () {\r\n\t\t\tif (typeof payload === \"string\") {\r\n\t\t\t\tvar buffer = new ArrayBuffer(UTF8Length(payload));\r\n\t\t\t\tvar byteStream = new Uint8Array(buffer);\r\n\t\t\t\tstringToUTF8(payload, byteStream, 0);\r\n\r\n\t\t\t\treturn byteStream;\r\n\t\t\t} else {\r\n\t\t\t\treturn payload;\r\n\t\t\t};\r\n\t\t};\r\n\r\n\t\tvar destinationName = undefined;\r\n\t\tthis._getDestinationName = function () { return destinationName; };\r\n\t\tthis._setDestinationName = function (newDestinationName) {\r\n\t\t\tif (typeof newDestinationName === \"string\")\r\n\t\t\t\tdestinationName = newDestinationName;\r\n\t\t\telse\r\n\t\t\t\tthrow new Error(format(ERROR.INVALID_ARGUMENT, [newDestinationName, \"newDestinationName\"]));\r\n\t\t};\r\n\r\n\t\tvar qos = 0;\r\n\t\tthis._getQos = function () { return qos; };\r\n\t\tthis._setQos = function (newQos) {\r\n\t\t\tif (newQos === 0 || newQos === 1 || newQos === 2)\r\n\t\t\t\tqos = newQos;\r\n\t\t\telse\r\n\t\t\t\tthrow new Error(\"Invalid argument:\" + newQos);\r\n\t\t};\r\n\r\n\t\tvar retained = false;\r\n\t\tthis._getRetained = function () { return retained; };\r\n\t\tthis._setRetained = function (newRetained) {\r\n\t\t\tif (typeof newRetained === \"boolean\")\r\n\t\t\t\tretained = newRetained;\r\n\t\t\telse\r\n\t\t\t\tthrow new Error(format(ERROR.INVALID_ARGUMENT, [newRetained, \"newRetained\"]));\r\n\t\t};\r\n\r\n\t\tvar duplicate = false;\r\n\t\tthis._getDuplicate = function () { return duplicate; };\r\n\t\tthis._setDuplicate = function (newDuplicate) { duplicate = newDuplicate; };\r\n\t};\r\n\r\n\tMessage.prototype = {\r\n\t\tget payloadString() { return this._getPayloadString(); },\r\n\t\tget payloadBytes() { return this._getPayloadBytes(); },\r\n\r\n\t\tget destinationName() { return this._getDestinationName(); },\r\n\t\tset destinationName(newDestinationName) { this._setDestinationName(newDestinationName); },\r\n\r\n\t\tget qos() { return this._getQos(); },\r\n\t\tset qos(newQos) { this._setQos(newQos); },\r\n\r\n\t\tget retained() { return this._getRetained(); },\r\n\t\tset retained(newRetained) { this._setRetained(newRetained); },\r\n\r\n\t\tget duplicate() { return this._getDuplicate(); },\r\n\t\tset duplicate(newDuplicate) { this._setDuplicate(newDuplicate); }\r\n\t};\r\n\r\n\t// Module contents.\r\n\treturn {\r\n\t\tClient: Client,\r\n\t\tMessage: Message\r\n\t};\r\n})(window);\r\n\r\nexport { Paho }"]}]}