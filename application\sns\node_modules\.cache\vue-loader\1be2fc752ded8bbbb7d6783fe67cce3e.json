{"remainingRequest": "E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\src\\components\\administrative\\systemManagement\\systemConfig.vue?vue&type=script&lang=js&", "dependencies": [{"path": "E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\src\\components\\administrative\\systemManagement\\systemConfig.vue", "mtime": 1754362736978}, {"path": "E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\node_modules\\string-replace-loader\\index.js", "mtime": 499162500000}, {"path": "E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgcmVzZXRJY29uIGZyb20gIkAvYXNzZXRzL2ltYWdlcy9pY19yZXNldC5zdmciOw0KaW1wb3J0IGNvcHlJY29uIGZyb20gIkAvYXNzZXRzL2ltYWdlcy9pY19jb3B5LnBuZyI7DQppbXBvcnQgZ2VuZXJhdGVJY29uIGZyb20gIkAvYXNzZXRzL2ltYWdlcy9pY19nZW5lcmF0ZS5wbmciOw0KaW1wb3J0IENvbmZpZyBmcm9tICJAL2NvbW1vbi9jb25maWciOw0KaW1wb3J0IENvbW1vblNlcnZpY2UgZnJvbSAiQC9jb21wb25lbnRzL2FkbWluaXN0cmF0aXZlL2FwcHMvY29tbW9uU2VydmljZSI7DQppbXBvcnQgSW5mb0ljb24gZnJvbSAiQC9jb21wb25lbnRzL2FkbWluaXN0cmF0aXZlL2NvbW1vbi9pbWFnZXMvcG9pbnRQbGFuZS9pY29uLWljLWluZm8uc3ZnIjsNCmNvbnN0IGFsZXJ0U291bmQgPSByZXF1aXJlKCJAL2Fzc2V0cy9iZWVwLm1wMyIpOyAvLyByZXF1aXJlIHRoZSBzb3VuZA0KbGV0IEJhc2U2NCA9IHJlcXVpcmUoImpzLWJhc2U2NCIpOw0KbGV0IG1vbWVudCA9IHJlcXVpcmUoIm1vbWVudCIpOw0KZXhwb3J0IGRlZmF1bHQgew0KICBkYXRhKCkgew0KICAgIHJldHVybiB7DQogICAgICB0aGlyZFBhcnR5RGF0YTogew0KICAgICAgICBsb2dpblRoaXJkUGFydHk6IGZhbHNlLA0KICAgICAgICB1cmw6ICIiLA0KICAgICAgICB1c2VyTmFtZTogIiIsDQogICAgICAgIHNlY3JldDogIiINCiAgICAgIH0sDQogICAgICBkYXRhVHlwZUxpc3Q6IFsNCiAgICAgICAgeyBrZXk6ICJNRURJQU4iLCB2YWx1ZTogdGhpcy4kdCgiTG9jYWxlU3RyaW5nLkwzMDE3OCIpIH0sDQogICAgICAgIHsga2V5OiAiTUVBTiIsIHZhbHVlOiB0aGlzLiR0KCJMb2NhbGVTdHJpbmcuTDMwMTc5IikgfSwNCiAgICAgICAgeyBrZXk6ICJMQVNUIiwgdmFsdWU6IHRoaXMuJHQoIkxvY2FsZVN0cmluZy5MMzAxODAiKSB9DQogICAgICBdLA0KICAgICAgc2VydmljZUNvZGVMaXN0OiBbXSwNCiAgICAgIHNlcnZpY2VOYW1lTGlzdDogW10sDQogICAgICBjb3B5SWNvbjogY29weUljb24sDQogICAgICBnZW5lcmF0ZUljb246IGdlbmVyYXRlSWNvbiwNCiAgICAgIGxvZ2luQWNjb3VudDogIiIsDQogICAgICBsb2dpblBhc3N3b3JkOiAiIiwNCiAgICAgIHRva2VuOiAiIiwNCiAgICAgIGlzQWRtaW46IGZhbHNlLA0KICAgICAgb3BlbkRldmljZVNlbGVjdDogZmFsc2UsDQogICAgICBkZXNjcmlwdGlvbjogIiIsDQogICAgICB0YWJCdXR0b25XaWR0aDogew0KICAgICAgICB3aWR0aDogIjExNTBweCINCiAgICAgIH0sDQogICAgICBtb2RhbFdpZHRoOiAxMjAwLA0KICAgICAgbW9kYWxJbm5lckhlaWdodDogd2luZG93LmlubmVySGVpZ2h0IC0gMzAwLA0KICAgICAgY3VycmVudFRhYjogInN5c3RlbSIsDQogICAgICBzaG93Q2xlYW5JY29uOiBmYWxzZSwNCiAgICAgIHJlc2V0SWNvbjogcmVzZXRJY29uLA0KICAgICAgaW1nVVJMOiAiIiwNCiAgICAgIG9yaVRhYjogIiIsDQogICAgICBldmVudFNlcnZpY2VDb2RlTGlzdDogW10sDQogICAgICBzZXJ2aWNlQ29kZUxvY2FsZVN0cmluZ3NMaXN0OiBbXSwNCiAgICAgIHN1Ym1pdExvYWRpbmc6IGZhbHNlLA0KICAgICAgYWxsRGV2aWNlVHlwZU1lbnU6IG51bGwsDQogICAgICBkZXZpY2VMaXN0TG9zc1NpZ25hbDogW10sDQogICAgICBkZXZpY2VMaXN0TG9zc1NpZ25hbDI6IFtdLA0KICAgICAgZGV2aWNlTGlzdExvd0JhdHRlcnk6IFtdLA0KICAgICAgZGV2aWNlTGlzdEFibm9ybWFsRGV2aWNlOiBbXSwNCiAgICAgIGFsZXJ0U291bmQ6IGFsZXJ0U291bmQsDQogICAgICBpbWdGaWxlOiBudWxsLA0KICAgICAgaW1nRmlsZVVSTDogbnVsbCwNCiAgICAgIGltZ0ZpbGVPcmlVUkw6IG51bGwsDQogICAgICBmaWxlVVJMOiBudWxsLA0KICAgICAgZmlsZU9yaVVSTDogbnVsbCwNCiAgICAgIHRlbXBVUkw6IG51bGwsDQogICAgICBmaWxlOiBudWxsLA0KICAgICAgaXNQbGF5OiBmYWxzZSwNCiAgICAgIHRpcE1vZGFsOiBmYWxzZSwNCiAgICAgIHRpcDJNb2RhbDogZmFsc2UsDQogICAgICB0aXAzTW9kYWw6IGZhbHNlLA0KICAgICAgdGlwNE1vZGFsOiBmYWxzZSwNCiAgICAgIHRpcDVNb2RhbDogZmFsc2UsDQogICAgICBJbmZvSWNvbjogSW5mb0ljb24sDQogICAgICBpY29uU2l6ZVRpdGxlOiAiIiwNCiAgICAgIG01T2JqZWN0VHlwZVRpdGxlOiAiIiwNCiAgICAgIGRlZmF1bHRQYWdlU2l6ZVRpdGxlOiAiIiwNCiAgICAgIGRlZmF1bHRUYWJUaXRsZTogIiIsDQogICAgICBvcmlnQ29uZmlnRGF0YToge30sDQogICAgICBvcmlnVGhpcmRQYXJ0eURhdGE6IHt9LA0KICAgICAgY29uZmlnRGF0YTogew0KICAgICAgICBkZWZhdWx0UGFnZVNpemU6ICIiLA0KICAgICAgICBtNU9iamVjdFR5cGU6ICJhbGwiLA0KICAgICAgICBtMUxvbmdQcmVzczogWyJmdXNpb24ta203Il0sDQogICAgICAgIG11bHRpUGxhbmVzOiBmYWxzZSwNCiAgICAgICAgc3R1Y2tQbGFuZTogZmFsc2UsDQogICAgICAgIHNlbGVjdGVkUGxhbmVzOiBbXSwNCiAgICAgICAgcGxhbmVMaXN0OiBbXSwNCiAgICAgICAgc3lzdGVtTmFtZTogIiIsDQogICAgICAgIHRhYkxpc3Q6IFsibTEiLCAibTIiLCAibTMiLCAibTQiLCAibTUiLCAibTYiLCAibTgiXSwNCiAgICAgICAgc291bmRVUkw6IG51bGwsDQogICAgICAgIHNvdW5kOiBmYWxzZSwNCiAgICAgICAgcmVwZWF0U291bmQ6IDAsDQogICAgICAgIG0yTGlzdDogWw0KICAgICAgICAgICJ0ZW1wZXJhdHVyZSIsDQogICAgICAgICAgImhlYXJ0UmF0ZSIsDQogICAgICAgICAgImJsb29kT3h5Z2VuIiwNCiAgICAgICAgICAiYmxvb2RQcmVzc3VyZSIsDQogICAgICAgICAgInN0ZXAiDQogICAgICAgIF0sDQogICAgICAgIGljb25TaXplOiAiNDAiLA0KICAgICAgICBkZWZhdWx0VGFiOiAibTEiLA0KICAgICAgICB1cGRhdGVTZWNvbmRzOiAiMzAiLA0KICAgICAgICBncmF5VGltZTogMTUsDQogICAgICAgIG0yTGluZTogZmFsc2UsDQogICAgICAgIGV2ZW50QXV0aENsb3NlOiBmYWxzZSwNCiAgICAgICAgZXZlbnRLZWVwRGF5czogIjIiLA0KICAgICAgICBpbmZsdXhSYW5nZTogOCwNCiAgICAgICAgbG9zc1NpZ25hbDogMSwNCiAgICAgICAgbG9zc1NpZ25hbDI6IDYwLA0KICAgICAgICBsb3dCYXR0ZXJ5OiAzMCwNCiAgICAgICAgbG9zc1NpZ25hbERldmljZXM6IG51bGwsDQogICAgICAgIGxvc3NTaWduYWxEZXZpY2VzMjogW10sDQogICAgICAgIGxvd0JhdHRlcnlEZXZpY2VzOiBudWxsLA0KICAgICAgICBhYm5vcm1hbERldmljZXM6IG51bGwsDQogICAgICAgIHNraXBFdmVudDogW10sDQogICAgICAgIGRldmljZVNlbGVjdExpc3Q6IFtdLA0KICAgICAgICBsaWNlbnNlOiBbXSwNCiAgICAgICAgc2hvd0xlYXZlQmVkOiBmYWxzZSwNCiAgICAgICAgbTJTdGF0aXN0aWM6IDEwLA0KICAgICAgICBtM1N0YXRpc3RpYzogMTAsDQogICAgICAgIG00U3RhdGlzdGljOiA2MCwNCiAgICAgICAgbThTdGF0aXN0aWM6IDYwLA0KICAgICAgICBtOVN0YXRpc3RpYzogNjAsDQogICAgICAgIHN0YXRlQ29sb3I6ICIjMDAwMDAwIiwNCiAgICAgICAgbG9nb3V0VGltZTogMA0KICAgICAgfSwNCiAgICAgIHZpZXdUYWI6ICIiLA0KICAgICAgdmlld0xpY2Vuc2U6ICIiLA0KICAgICAgdmlld1BsYXlTb3VuZDogIiIsDQogICAgICB2aWV3TTJMaW5lOiAiIiwNCiAgICAgIHZpZXdNMkxpc3Q6ICIiLA0KICAgICAgdmlld0V2ZW50QXV0aENsb3NlOiAiIiwNCiAgICAgIHZpZXdFdmVudEtlZXBEYXlzOiAiIiwNCiAgICAgIHZpZXdMb2dvOiAiIiwNCiAgICAgIHZpZXdNdWx0aVBsYW5lczogIiIsDQogICAgICB2aWV3U3R1Y2tQbGFuZTogIiIsDQogICAgICB2aWV3TG9naW5UaGlyZFBhcnR5OiAiIiwNCiAgICAgIGlzRWRpdDogZmFsc2UsDQogICAgICBpc1Nob3c6IGZhbHNlLA0KDQogICAgICBjb25maWdMaXN0OiBbXSwNCiAgICAgIGNvbmZpZ0xpc3RUb1NhdmU6IFtdLA0KICAgICAgbm9EYXRhVGV4dDogdGhpcy4kdCgiTG9jYWxlU3RyaW5nLkwwMDA4MSIpLA0KICAgICAga21PYmplY3RUeXBlTGlzdDogWw0KICAgICAgICB7DQogICAgICAgICAgdHlwZTogImVxdWlwbWVudCIsDQogICAgICAgICAgbmFtZTogdGhpcy4kdCgiTG9jYWxlU3RyaW5nLkQwMDAwOCIpDQogICAgICAgIH0sDQogICAgICAgIHsNCiAgICAgICAgICB0eXBlOiAicGVvcGxlIiwNCiAgICAgICAgICBuYW1lOiB0aGlzLiR0KCJMb2NhbGVTdHJpbmcuRDAwMDA3IikNCiAgICAgICAgfSwNCiAgICAgICAgew0KICAgICAgICAgIHR5cGU6ICJzcGFjZSIsDQogICAgICAgICAgbmFtZTogdGhpcy4kdCgiTG9jYWxlU3RyaW5nLkQwMDAwOSIpDQogICAgICAgIH0sDQogICAgICAgIHsNCiAgICAgICAgICB0eXBlOiAib3RoZXIiLA0KICAgICAgICAgIG5hbWU6IHRoaXMuJHQoIkxvY2FsZVN0cmluZy5EMDAwMTAiKQ0KICAgICAgICB9DQogICAgICBdLA0KICAgICAgY2FtZXJhVHlwZUxpc3Q6IFsNCiAgICAgICAgew0KICAgICAgICAgIHR5cGU6ICJoZWxwIiwNCiAgICAgICAgICBuYW1lOiB0aGlzLiR0KCJMb2NhbGVTdHJpbmcuRDAwMDMwIikNCiAgICAgICAgfSwNCiAgICAgICAgew0KICAgICAgICAgIHR5cGU6ICJlbnRlciIsDQogICAgICAgICAgbmFtZTogdGhpcy4kdCgiTG9jYWxlU3RyaW5nLkQwMDAyOSIpDQogICAgICAgIH0sDQogICAgICAgIHsNCiAgICAgICAgICB0eXBlOiAibGVhdmUiLA0KICAgICAgICAgIG5hbWU6IHRoaXMuJHQoIkxvY2FsZVN0cmluZy5EMDAwMzEiKQ0KICAgICAgICB9LA0KICAgICAgICB7DQogICAgICAgICAgdHlwZTogImZhbGwiLA0KICAgICAgICAgIG5hbWU6IHRoaXMuJHQoIkxvY2FsZVN0cmluZy5EMDAwMzUiKQ0KICAgICAgICB9DQogICAgICBdLA0KICAgICAgY2FtZXJhQ29sdW1uc0xpc3Q6IFsNCiAgICAgICAgew0KICAgICAgICAgIHRpdGxlOiB0aGlzLiR0KCJMb2NhbGVTdHJpbmcuTDAwMDQ3IiksDQogICAgICAgICAgc2xvdDogInR5cGUiLA0KICAgICAgICAgIHdpZHRoOiA5MCwNCiAgICAgICAgICByZW5kZXI6IChoLCBwYXJhbXMpID0+IHsNCiAgICAgICAgICAgIHJldHVybiBoKCJzcGFuIiwgdGhpcy5nZXRDYW1lcmFUeXBlKHBhcmFtcy5yb3cudHlwZSkpOw0KICAgICAgICAgIH0NCiAgICAgICAgfSwNCiAgICAgICAgew0KICAgICAgICAgIHRpdGxlOiB0aGlzLiR0KCJMb2NhbGVTdHJpbmcuTDMwMTQwIiksDQogICAgICAgICAgc2xvdDogImNhcHR1cmUiLA0KICAgICAgICAgIHdpZHRoOiA3MA0KICAgICAgICB9LA0KICAgICAgICB7DQogICAgICAgICAgdGl0bGU6IHRoaXMuJHQoIkxvY2FsZVN0cmluZy5MMzAxNDEiKSwNCiAgICAgICAgICBzbG90OiAicHJlZml4TWludXRlIiwNCiAgICAgICAgICB3aWR0aDogODUNCiAgICAgICAgfSwNCiAgICAgICAgew0KICAgICAgICAgIHRpdGxlOiB0aGlzLiR0KCJMb2NhbGVTdHJpbmcuTDMwMTQyIiksDQogICAgICAgICAgc2xvdDogInN1ZmZpeE1pbnV0ZSIsDQogICAgICAgICAgd2lkdGg6IDg1DQogICAgICAgIH0sDQogICAgICAgIHsNCiAgICAgICAgICB0aXRsZTogdGhpcy4kdCgiTG9jYWxlU3RyaW5nLkwzMDE0MyIpLA0KICAgICAgICAgIHNsb3Q6ICJiYWNrdXBEaXJlY3RvcnkiDQogICAgICAgIH0sDQogICAgICAgIHsNCiAgICAgICAgICB0aXRsZTogdGhpcy4kdCgiTG9jYWxlU3RyaW5nLkwzMDE0NCIpLA0KICAgICAgICAgIHNsb3Q6ICJhY2NvdW50IiwNCiAgICAgICAgICB3aWR0aDogMTEwDQogICAgICAgIH0sDQogICAgICAgIHsNCiAgICAgICAgICB0aXRsZTogdGhpcy4kdCgiTG9jYWxlU3RyaW5nLkwwMDAwMyIpLA0KICAgICAgICAgIHNsb3Q6ICJwYXNzd29yZCIsDQogICAgICAgICAgd2lkdGg6IDExMA0KICAgICAgICB9LA0KICAgICAgICB7DQogICAgICAgICAgdGl0bGU6IHRoaXMuJHQoIkxvY2FsZVN0cmluZy5MMzAxNDUiKSwNCiAgICAgICAgICBzbG90OiAia2VlcERheXMiLA0KICAgICAgICAgIHdpZHRoOiA5MA0KICAgICAgICB9LA0KICAgICAgICB7DQogICAgICAgICAgdGl0bGU6IHRoaXMuJHQoIkxvY2FsZVN0cmluZy5MMzAxNDYiKSwNCiAgICAgICAgICBzbG90OiAicG9zaXRpb25DYXB0dXJlIiwNCiAgICAgICAgICB3aWR0aDogMTIwDQogICAgICAgIH0sDQogICAgICAgIHsNCiAgICAgICAgICB0aXRsZTogIiAiLA0KICAgICAgICAgIHNsb3Q6ICJhY3Rpb24iLA0KICAgICAgICAgIHdpZHRoOiAxMTANCiAgICAgICAgfQ0KICAgICAgXSwNCiAgICAgIHN0YXRpc3RpY0xpbmVDb2x1bW5zTGlzdDogWw0KICAgICAgICB7DQogICAgICAgICAgdGl0bGU6IHRoaXMuJHQoIkxvY2FsZVN0cmluZy5MMzAwNTciKSwNCiAgICAgICAgICBzbG90OiAidHlwZSINCiAgICAgICAgfSwNCiAgICAgICAgew0KICAgICAgICAgIHRpdGxlOiB0aGlzLiR0KCJMb2NhbGVTdHJpbmcuTDMwMTUwIiksDQogICAgICAgICAgc2xvdDogImludGVydmFsIg0KICAgICAgICB9LA0KICAgICAgICB7DQogICAgICAgICAgdGl0bGU6IHRoaXMuJHQoIkxvY2FsZVN0cmluZy5MMDAxNTciKSwNCiAgICAgICAgICBzbG90OiAiZGF0YVR5cGUiDQogICAgICAgIH0sDQogICAgICAgIHsNCiAgICAgICAgICB0aXRsZTogdGhpcy4kdCgiTG9jYWxlU3RyaW5nLkwzMDE1MSIpLA0KICAgICAgICAgIHNsb3Q6ICJ3YXJuaW5nTGluZSINCiAgICAgICAgfQ0KICAgICAgXSwNCiAgICAgIGRlZmF1bHRTdGF0aXN0aWNMaW5lQ29uZmlnTGlzdDogWw0KICAgICAgICB7DQogICAgICAgICAgdHlwZTogdGhpcy4kdCgiTG9jYWxlU3RyaW5nLkYzMDAwMiIpLA0KICAgICAgICAgIGludGVydmFsOiAxLA0KICAgICAgICAgIGRhdGFUeXBlOiAiTUVESUFOIiwNCiAgICAgICAgICB3YXJuaW5nTGluZTogdHJ1ZSwNCiAgICAgICAgICBlbnRyeTogIm0yIg0KICAgICAgICB9LA0KICAgICAgICB7DQogICAgICAgICAgdHlwZTogdGhpcy4kdCgiTG9jYWxlU3RyaW5nLkYzMDAwMyIpLA0KICAgICAgICAgIGludGVydmFsOiAxLA0KICAgICAgICAgIGRhdGFUeXBlOiAiTUVESUFOIiwNCiAgICAgICAgICB3YXJuaW5nTGluZTogdHJ1ZSwNCiAgICAgICAgICBlbnRyeTogIm0zIg0KICAgICAgICB9LA0KICAgICAgICB7DQogICAgICAgICAgdHlwZTogdGhpcy4kdCgiTG9jYWxlU3RyaW5nLkYzMDAwNCIpLA0KICAgICAgICAgIGludGVydmFsOiAxLA0KICAgICAgICAgIGRhdGFUeXBlOiAiTUVESUFOIiwNCiAgICAgICAgICB3YXJuaW5nTGluZTogdHJ1ZSwNCiAgICAgICAgICBlbnRyeTogIm00Ig0KICAgICAgICB9LA0KICAgICAgICB7DQogICAgICAgICAgdHlwZTogdGhpcy4kdCgiTG9jYWxlU3RyaW5nLkYzMDAxNiIpLA0KICAgICAgICAgIGludGVydmFsOiAxLA0KICAgICAgICAgIGRhdGFUeXBlOiAiTUVESUFOIiwNCiAgICAgICAgICB3YXJuaW5nTGluZTogdHJ1ZSwNCiAgICAgICAgICBlbnRyeTogIm04Ig0KICAgICAgICB9DQogICAgICBdLA0KICAgICAgc3RhdGlzdGljTGluZUNvbmZpZ0xpc3Q6IFtdLA0KICAgICAgdGhpcmRQYXJ0eUNvbmZpZ0xpc3Q6IFsNCiAgICAgICAgew0KICAgICAgICAgIGl0ZW06IHRoaXMuJHQoIkxvY2FsZVN0cmluZy5MMzAxOTUiKSwNCiAgICAgICAgICBzZWN0aW9uOiAxLA0KICAgICAgICAgIHZhbHVlOiBmYWxzZQ0KICAgICAgICB9LA0KICAgICAgICB7DQogICAgICAgICAgaXRlbTogdGhpcy4kdCgiTG9jYWxlU3RyaW5nLkwzMDE5NiIpLA0KICAgICAgICAgIHNlY3Rpb246IDIsDQogICAgICAgICAgdmFsdWU6ICIiDQogICAgICAgIH0sDQogICAgICAgIHsNCiAgICAgICAgICBpdGVtOiAiVXNlck5hbWUiLA0KICAgICAgICAgIHNlY3Rpb246IDMsDQogICAgICAgICAgdmFsdWU6ICIiDQogICAgICAgIH0sDQogICAgICAgIHsNCiAgICAgICAgICBpdGVtOiAiQ3JlZGVudGlhbFNlY3JldCIsDQogICAgICAgICAgc2VjdGlvbjogNCwNCiAgICAgICAgICB2YWx1ZTogIiINCiAgICAgICAgfQ0KICAgICAgXSwNCiAgICAgIGRlZmF1bHRDYW1lcmFDb25maWdMaXN0OiBbDQogICAgICAgIHsNCiAgICAgICAgICB0eXBlOiAiaGVscCIsDQogICAgICAgICAgY2FwdHVyZTogZmFsc2UsDQogICAgICAgICAgcHJlZml4TWludXRlOiAzLA0KICAgICAgICAgIHN1ZmZpeE1pbnV0ZTogMywNCiAgICAgICAgICBiYWNrdXBEaXJlY3Rvcnk6ICIiLA0KICAgICAgICAgIGFjY291bnQ6ICIiLA0KICAgICAgICAgIHBhc3N3b3JkOiAiIiwNCiAgICAgICAgICBrZWVwRGF5czogOTAsDQogICAgICAgICAgcG9zaXRpb25DYXB0dXJlOiAzDQogICAgICAgIH0sDQogICAgICAgIHsNCiAgICAgICAgICB0eXBlOiAiZW50ZXIiLA0KICAgICAgICAgIGNhcHR1cmU6IGZhbHNlLA0KICAgICAgICAgIHByZWZpeE1pbnV0ZTogMywNCiAgICAgICAgICBzdWZmaXhNaW51dGU6IDMsDQogICAgICAgICAgYmFja3VwRGlyZWN0b3J5OiAiIiwNCiAgICAgICAgICBhY2NvdW50OiAiIiwNCiAgICAgICAgICBwYXNzd29yZDogIiIsDQogICAgICAgICAga2VlcERheXM6IDkwLA0KICAgICAgICAgIHBvc2l0aW9uQ2FwdHVyZTogMw0KICAgICAgICB9LA0KICAgICAgICB7DQogICAgICAgICAgdHlwZTogImxlYXZlIiwNCiAgICAgICAgICBjYXB0dXJlOiBmYWxzZSwNCiAgICAgICAgICBwcmVmaXhNaW51dGU6IDMsDQogICAgICAgICAgc3VmZml4TWludXRlOiAzLA0KICAgICAgICAgIGJhY2t1cERpcmVjdG9yeTogIiIsDQogICAgICAgICAgYWNjb3VudDogIiIsDQogICAgICAgICAgcGFzc3dvcmQ6ICIiLA0KICAgICAgICAgIGtlZXBEYXlzOiA5MCwNCiAgICAgICAgICBwb3NpdGlvbkNhcHR1cmU6IDMNCiAgICAgICAgfSwNCiAgICAgICAgew0KICAgICAgICAgIHR5cGU6ICJmYWxsIiwNCiAgICAgICAgICBjYXB0dXJlOiBmYWxzZSwNCiAgICAgICAgICBwcmVmaXhNaW51dGU6IDMsDQogICAgICAgICAgc3VmZml4TWludXRlOiAzLA0KICAgICAgICAgIGJhY2t1cERpcmVjdG9yeTogIiIsDQogICAgICAgICAgYWNjb3VudDogIiIsDQogICAgICAgICAgcGFzc3dvcmQ6ICIiLA0KICAgICAgICAgIGtlZXBEYXlzOiA5MCwNCiAgICAgICAgICBwb3NpdGlvbkNhcHR1cmU6IDMNCiAgICAgICAgfQ0KICAgICAgXSwNCiAgICAgIGNhbWVyYUNvbmZpZ0xpc3Q6IFtdLA0KICAgICAga21Db2x1bW5zTGlzdDogWw0KICAgICAgICB7DQogICAgICAgICAgdGl0bGU6IHRoaXMuJHQoIkxvY2FsZVN0cmluZy5MMDAwNDciKSwNCiAgICAgICAgICBrZXk6ICJ0eXBlIiwNCiAgICAgICAgICB3aWR0aDogMTAwLA0KICAgICAgICAgIHJlbmRlcjogKGgsIHBhcmFtcykgPT4gew0KICAgICAgICAgICAgcmV0dXJuIGgoInNwYW4iLCB0aGlzLmdldE9iamVjdFR5cGUocGFyYW1zLnJvdy50eXBlKSk7DQogICAgICAgICAgfQ0KICAgICAgICB9LA0KICAgICAgICB7DQogICAgICAgICAgdGl0bGU6IHRoaXMuJHQoIkxvY2FsZVN0cmluZy5MMzAxMDgiKSwNCiAgICAgICAgICBrZXk6ICJsb25nUHJlc3NfY2h0Ig0KICAgICAgICB9LA0KICAgICAgICB7DQogICAgICAgICAgdGl0bGU6IHRoaXMuJHQoIkxvY2FsZVN0cmluZy5MMzAxMDkiKSwNCiAgICAgICAgICBrZXk6ICJsb25nUHJlc3NfZW4iDQogICAgICAgIH0sDQogICAgICAgIHsNCiAgICAgICAgICB0aXRsZTogdGhpcy4kdCgiTG9jYWxlU3RyaW5nLkwzMDExMCIpLA0KICAgICAgICAgIGtleTogInNob3J0Q2xpY2tfY2h0Ig0KICAgICAgICB9LA0KICAgICAgICB7DQogICAgICAgICAgdGl0bGU6IHRoaXMuJHQoIkxvY2FsZVN0cmluZy5MMzAxMTEiKSwNCiAgICAgICAgICBrZXk6ICJzaG9ydENsaWNrX2VuIg0KICAgICAgICB9LA0KICAgICAgICB7DQogICAgICAgICAgdGl0bGU6IHRoaXMuJHQoIkxvY2FsZVN0cmluZy5MMzAxMTIiKSwNCiAgICAgICAgICBrZXk6ICJkb3VibGVDbGlja19jaHQiDQogICAgICAgIH0sDQogICAgICAgIHsNCiAgICAgICAgICB0aXRsZTogdGhpcy4kdCgiTG9jYWxlU3RyaW5nLkwzMDExMyIpLA0KICAgICAgICAgIGtleTogImRvdWJsZUNsaWNrX2VuIg0KICAgICAgICB9DQogICAgICBdLA0KICAgICAga21Db2x1bW5zRWRpdDogWw0KICAgICAgICB7DQogICAgICAgICAgdGl0bGU6IHRoaXMuJHQoIkxvY2FsZVN0cmluZy5MMDAwNDciKSwNCiAgICAgICAgICBzbG90OiAidHlwZSIsDQogICAgICAgICAgd2lkdGg6IDEwMCwNCiAgICAgICAgICByZW5kZXI6IChoLCBwYXJhbXMpID0+IHsNCiAgICAgICAgICAgIHJldHVybiBoKCJzcGFuIiwgdGhpcy5nZXRPYmplY3RUeXBlKHBhcmFtcy5yb3cudHlwZSkpOw0KICAgICAgICAgIH0NCiAgICAgICAgfSwNCiAgICAgICAgew0KICAgICAgICAgIHRpdGxlOiB0aGlzLiR0KCJMb2NhbGVTdHJpbmcuTDMwMTA4IiksDQogICAgICAgICAgc2xvdDogImxvbmdQcmVzc19jaHQiDQogICAgICAgIH0sDQogICAgICAgIHsNCiAgICAgICAgICB0aXRsZTogdGhpcy4kdCgiTG9jYWxlU3RyaW5nLkwzMDEwOSIpLA0KICAgICAgICAgIHNsb3Q6ICJsb25nUHJlc3NfZW4iDQogICAgICAgIH0sDQogICAgICAgIHsNCiAgICAgICAgICB0aXRsZTogdGhpcy4kdCgiTG9jYWxlU3RyaW5nLkwzMDExMCIpLA0KICAgICAgICAgIHNsb3Q6ICJzaG9ydENsaWNrX2NodCINCiAgICAgICAgfSwNCiAgICAgICAgew0KICAgICAgICAgIHRpdGxlOiB0aGlzLiR0KCJMb2NhbGVTdHJpbmcuTDMwMTExIiksDQogICAgICAgICAgc2xvdDogInNob3J0Q2xpY2tfZW4iDQogICAgICAgIH0sDQogICAgICAgIHsNCiAgICAgICAgICB0aXRsZTogdGhpcy4kdCgiTG9jYWxlU3RyaW5nLkwzMDExMiIpLA0KICAgICAgICAgIHNsb3Q6ICJkb3VibGVDbGlja19jaHQiDQogICAgICAgIH0sDQogICAgICAgIHsNCiAgICAgICAgICB0aXRsZTogdGhpcy4kdCgiTG9jYWxlU3RyaW5nLkwzMDExMyIpLA0KICAgICAgICAgIHNsb3Q6ICJkb3VibGVDbGlja19lbiINCiAgICAgICAgfQ0KICAgICAgXSwNCiAgICAgIGttQ29uZmlnTGlzdDogWw0KICAgICAgICB7DQogICAgICAgICAgdHlwZTogImVxdWlwbWVudCIsDQogICAgICAgICAgbG9uZ1ByZXNzX2NodDogIiIsDQogICAgICAgICAgbG9uZ1ByZXNzX2VuOiAiIiwNCiAgICAgICAgICBzaG9ydENsaWNrX2NodDogIiIsDQogICAgICAgICAgc2hvcnRDbGlja19lbjogIiIsDQogICAgICAgICAgZG91YmxlQ2xpY2tfY2h0OiAiIiwNCiAgICAgICAgICBkb3VibGVDbGlja19lbjogIiINCiAgICAgICAgfSwNCiAgICAgICAgew0KICAgICAgICAgIHR5cGU6ICJwZW9wbGUiLA0KICAgICAgICAgIGxvbmdQcmVzc19jaHQ6ICIiLA0KICAgICAgICAgIGxvbmdQcmVzc19lbjogIiIsDQogICAgICAgICAgc2hvcnRDbGlja19jaHQ6ICIiLA0KICAgICAgICAgIHNob3J0Q2xpY2tfZW46ICIiLA0KICAgICAgICAgIGRvdWJsZUNsaWNrX2NodDogIiIsDQogICAgICAgICAgZG91YmxlQ2xpY2tfZW46ICIiDQogICAgICAgIH0sDQogICAgICAgIHsNCiAgICAgICAgICB0eXBlOiAic3BhY2UiLA0KICAgICAgICAgIGxvbmdQcmVzc19jaHQ6ICIiLA0KICAgICAgICAgIGxvbmdQcmVzc19lbjogIiIsDQogICAgICAgICAgc2hvcnRDbGlja19jaHQ6ICIiLA0KICAgICAgICAgIHNob3J0Q2xpY2tfZW46ICIiLA0KICAgICAgICAgIGRvdWJsZUNsaWNrX2NodDogIiIsDQogICAgICAgICAgZG91YmxlQ2xpY2tfZW46ICIiDQogICAgICAgIH0sDQogICAgICAgIHsNCiAgICAgICAgICB0eXBlOiAib3RoZXIiLA0KICAgICAgICAgIGxvbmdQcmVzc19jaHQ6ICIiLA0KICAgICAgICAgIGxvbmdQcmVzc19lbjogIiIsDQogICAgICAgICAgc2hvcnRDbGlja19jaHQ6ICIiLA0KICAgICAgICAgIHNob3J0Q2xpY2tfZW46ICIiLA0KICAgICAgICAgIGRvdWJsZUNsaWNrX2NodDogIiIsDQogICAgICAgICAgZG91YmxlQ2xpY2tfZW46ICIiDQogICAgICAgIH0NCiAgICAgIF0sDQogICAgICBrbUNvbmZpZ0xpc3RFZGl0OiBbDQogICAgICAgIHsNCiAgICAgICAgICB0eXBlOiAiZXF1aXBtZW50IiwNCiAgICAgICAgICBsb25nUHJlc3NfY2h0OiAiIiwNCiAgICAgICAgICBsb25nUHJlc3NfZW46ICIiLA0KICAgICAgICAgIHNob3J0Q2xpY2tfY2h0OiAiIiwNCiAgICAgICAgICBzaG9ydENsaWNrX2VuOiAiIiwNCiAgICAgICAgICBkb3VibGVDbGlja19jaHQ6ICIiLA0KICAgICAgICAgIGRvdWJsZUNsaWNrX2VuOiAiIg0KICAgICAgICB9LA0KICAgICAgICB7DQogICAgICAgICAgdHlwZTogInBlb3BsZSIsDQogICAgICAgICAgbG9uZ1ByZXNzX2NodDogIiIsDQogICAgICAgICAgbG9uZ1ByZXNzX2VuOiAiIiwNCiAgICAgICAgICBzaG9ydENsaWNrX2NodDogIiIsDQogICAgICAgICAgc2hvcnRDbGlja19lbjogIiIsDQogICAgICAgICAgZG91YmxlQ2xpY2tfY2h0OiAiIiwNCiAgICAgICAgICBkb3VibGVDbGlja19lbjogIiINCiAgICAgICAgfSwNCiAgICAgICAgew0KICAgICAgICAgIHR5cGU6ICJzcGFjZSIsDQogICAgICAgICAgbG9uZ1ByZXNzX2NodDogIiIsDQogICAgICAgICAgbG9uZ1ByZXNzX2VuOiAiIiwNCiAgICAgICAgICBzaG9ydENsaWNrX2NodDogIiIsDQogICAgICAgICAgc2hvcnRDbGlja19lbjogIiIsDQogICAgICAgICAgZG91YmxlQ2xpY2tfY2h0OiAiIiwNCiAgICAgICAgICBkb3VibGVDbGlja19lbjogIiINCiAgICAgICAgfSwNCiAgICAgICAgew0KICAgICAgICAgIHR5cGU6ICJvdGhlciIsDQogICAgICAgICAgbG9uZ1ByZXNzX2NodDogIiIsDQogICAgICAgICAgbG9uZ1ByZXNzX2VuOiAiIiwNCiAgICAgICAgICBzaG9ydENsaWNrX2NodDogIiIsDQogICAgICAgICAgc2hvcnRDbGlja19lbjogIiIsDQogICAgICAgICAgZG91YmxlQ2xpY2tfY2h0OiAiIiwNCiAgICAgICAgICBkb3VibGVDbGlja19lbjogIiINCiAgICAgICAgfQ0KICAgICAgXSwNCiAgICAgIGNvbHVtbnM6IFsNCiAgICAgICAgew0KICAgICAgICAgIHRpdGxlOiB0aGlzLiR0KCJMb2NhbGVTdHJpbmcuTDAwMTgzIiksDQogICAgICAgICAgc2xvdDogIml0ZW0iDQogICAgICAgICAgLy8gd2lkdGg6IDM2MA0KICAgICAgICB9LA0KICAgICAgICB7DQogICAgICAgICAgdGl0bGU6IHRoaXMuJHQoIkxvY2FsZVN0cmluZy5MMDAxODQiKSwNCiAgICAgICAgICBzbG90OiAidmFsdWUiDQogICAgICAgIH0NCiAgICAgIF0sDQogICAgICBkYXRhU2VjdGlvbjA6IFsNCiAgICAgICAgew0KICAgICAgICAgIGl0ZW06IHRoaXMuJHQoIkxvY2FsZVN0cmluZy5GMDAwMDQiKSwNCiAgICAgICAgICBzZWN0aW9uOiAyMywNCiAgICAgICAgICB2YWx1ZTogIiINCiAgICAgICAgfQ0KICAgICAgXSwNCiAgICAgIGRhdGFTZWN0aW9uMDE6IFsNCiAgICAgICAgew0KICAgICAgICAgIGl0ZW06IHRoaXMuJHQoIkxvY2FsZVN0cmluZy5MMzAxMTgiKSwNCiAgICAgICAgICBzZWN0aW9uOiAwLA0KICAgICAgICAgIHZhbHVlOiAiIg0KICAgICAgICB9LA0KICAgICAgICB7DQogICAgICAgICAgaXRlbTogdGhpcy4kdCgiTG9jYWxlU3RyaW5nLkwzMDEzNCIpLA0KICAgICAgICAgIHNlY3Rpb246IDE3LA0KICAgICAgICAgIHZhbHVlOiAiIg0KICAgICAgICB9LA0KICAgICAgICB7DQogICAgICAgICAgaXRlbTogdGhpcy4kdCgiTG9jYWxlU3RyaW5nLkwwMDQ0NiIpLA0KICAgICAgICAgIHNlY3Rpb246IDMxLA0KICAgICAgICAgIHZhbHVlOiAiIg0KICAgICAgICB9DQogICAgICBdLA0KICAgICAgZGF0YTogWw0KICAgICAgICB7DQogICAgICAgICAgaXRlbTogdGhpcy4kdCgiTG9jYWxlU3RyaW5nLkwzMDEzOCIpLA0KICAgICAgICAgIHNlY3Rpb246IDE4LA0KICAgICAgICAgIHZhbHVlOiAiIg0KICAgICAgICB9LA0KICAgICAgICB7DQogICAgICAgICAgaXRlbTogdGhpcy4kdCgiTG9jYWxlU3RyaW5nLkwzMDE4OCIpLA0KICAgICAgICAgIHNlY3Rpb246IDMwLA0KICAgICAgICAgIHZhbHVlOiAiIg0KICAgICAgICB9LA0KICAgICAgICB7DQogICAgICAgICAgaXRlbTogdGhpcy4kdCgiTG9jYWxlU3RyaW5nLkwzMDA1NyIpLA0KICAgICAgICAgIHNlY3Rpb246IDEsDQogICAgICAgICAgdmFsdWU6ICIiDQogICAgICAgIH0sDQogICAgICAgIHsNCiAgICAgICAgICBpdGVtOiB0aGlzLiR0KCJMb2NhbGVTdHJpbmcuTDMwMDU1IiksDQogICAgICAgICAgc2VjdGlvbjogMiwNCiAgICAgICAgICB2YWx1ZTogIiINCiAgICAgICAgfSwNCiAgICAgICAgew0KICAgICAgICAgIGl0ZW06IHRoaXMuJHQoIkxvY2FsZVN0cmluZy5MMzAwNTYiKSwNCiAgICAgICAgICBzZWN0aW9uOiAzLA0KICAgICAgICAgIHZhbHVlOiAiIg0KICAgICAgICB9LA0KICAgICAgICB7DQogICAgICAgICAgaXRlbTogdGhpcy4kdCgiTG9jYWxlU3RyaW5nLkwzMDA1OCIpLA0KICAgICAgICAgIHNlY3Rpb246IDQsDQogICAgICAgICAgdmFsdWU6ICIiDQogICAgICAgIH0sDQogICAgICAgIHsNCiAgICAgICAgICBpdGVtOiB0aGlzLiR0KCJMb2NhbGVTdHJpbmcuTDMwMDYwIiksDQogICAgICAgICAgc2VjdGlvbjogNSwNCiAgICAgICAgICB2YWx1ZTogIiINCiAgICAgICAgfSwNCiAgICAgICAgew0KICAgICAgICAgIGl0ZW06IHRoaXMuJHQoIkxvY2FsZVN0cmluZy5MMzAwNjEiKSwNCiAgICAgICAgICBzZWN0aW9uOiA2LA0KICAgICAgICAgIHZhbHVlOiAiIg0KICAgICAgICB9LA0KICAgICAgICB7DQogICAgICAgICAgaXRlbTogdGhpcy4kdCgiTG9jYWxlU3RyaW5nLkwzMDA2MiIpLA0KICAgICAgICAgIHNlY3Rpb246IDcsDQogICAgICAgICAgdmFsdWU6IDENCiAgICAgICAgfSwNCiAgICAgICAgew0KICAgICAgICAgIGl0ZW06IHRoaXMuJHQoIkxvY2FsZVN0cmluZy5MMzAwODUiKSwNCiAgICAgICAgICBzZWN0aW9uOiA4LA0KICAgICAgICAgIHZhbHVlOiAiIg0KICAgICAgICB9LA0KICAgICAgICB7DQogICAgICAgICAgaXRlbTogdGhpcy4kdCgiTG9jYWxlU3RyaW5nLkwzMDEwMSIpLA0KICAgICAgICAgIHNlY3Rpb246IDExLA0KICAgICAgICAgIHZhbHVlOiAiIg0KICAgICAgICB9LA0KICAgICAgICB7DQogICAgICAgICAgaXRlbTogdGhpcy4kdCgiTG9jYWxlU3RyaW5nLkwzMDEwMyIpLA0KICAgICAgICAgIHNlY3Rpb246IDEyLA0KICAgICAgICAgIHZhbHVlOiAiIiwNCiAgICAgICAgICB2YWx1ZURldmljZXM6IFtdDQogICAgICAgIH0sDQogICAgICAgIHsNCiAgICAgICAgICBpdGVtOiB0aGlzLiR0KCJMb2NhbGVTdHJpbmcuTDMwMTU2IiksDQogICAgICAgICAgc2VjdGlvbjogMjIsDQogICAgICAgICAgdmFsdWU6ICIiLA0KICAgICAgICAgIHZhbHVlRGV2aWNlczogW10NCiAgICAgICAgfSwNCiAgICAgICAgew0KICAgICAgICAgIGl0ZW06IHRoaXMuJHQoIkxvY2FsZVN0cmluZy5MMzAxMDQiKSwNCiAgICAgICAgICBzZWN0aW9uOiAxMywNCiAgICAgICAgICB2YWx1ZTogIiIsDQogICAgICAgICAgdmFsdWVEZXZpY2VzOiBbXQ0KICAgICAgICB9LA0KICAgICAgICB7DQogICAgICAgICAgaXRlbTogdGhpcy4kdCgiTG9jYWxlU3RyaW5nLkwzMDExNSIpLA0KICAgICAgICAgIHNlY3Rpb246IDE1LA0KICAgICAgICAgIHZhbHVlOiAiIiwNCiAgICAgICAgICB2YWx1ZURldmljZXM6IFtdDQogICAgICAgIH0sDQogICAgICAgIHsNCiAgICAgICAgICBpdGVtOiB0aGlzLiR0KCJMb2NhbGVTdHJpbmcuTDMwMTE2IiksDQogICAgICAgICAgc2VjdGlvbjogMTQsDQogICAgICAgICAgdmFsdWU6ICIiLA0KICAgICAgICAgIHZhbHVlU2tpcEV2ZW50OiBbXQ0KICAgICAgICB9LA0KICAgICAgICB7DQogICAgICAgICAgaXRlbTogdGhpcy4kdCgiTG9jYWxlU3RyaW5nLkwzMDEyOSIpLA0KICAgICAgICAgIHNlY3Rpb246IDE2LA0KICAgICAgICAgIHZhbHVlOiAiIiwNCiAgICAgICAgICB2YWx1ZURldmljZVNlbGVjdExpc3Q6IFtdDQogICAgICAgIH0sDQogICAgICAgIHsNCiAgICAgICAgICBpdGVtOiB0aGlzLiR0KCJMb2NhbGVTdHJpbmcuTDMwMTM2IiksDQogICAgICAgICAgc2VjdGlvbjogMTksDQogICAgICAgICAgdmFsdWU6ICIiLA0KICAgICAgICAgIHZhbHVlTTFMb25nUHJlc3NMaXN0OiBbXQ0KICAgICAgICB9LA0KICAgICAgICB7DQogICAgICAgICAgaXRlbTogdGhpcy4kdCgiTG9jYWxlU3RyaW5nLkwzMDE1OSIpLA0KICAgICAgICAgIHNlY3Rpb246IDI0LA0KICAgICAgICAgIHZhbHVlOiAiIg0KICAgICAgICB9LA0KICAgICAgICB7DQogICAgICAgICAgaXRlbTogdGhpcy4kdCgiTG9jYWxlU3RyaW5nLkwzMDE1MyIpLA0KICAgICAgICAgIHNlY3Rpb246IDIwLA0KICAgICAgICAgIHZhbHVlOiAiIg0KICAgICAgICB9LA0KICAgICAgICB7DQogICAgICAgICAgaXRlbTogdGhpcy4kdCgiTG9jYWxlU3RyaW5nLkwzMDE1NCIpLA0KICAgICAgICAgIHNlY3Rpb246IDIxLA0KICAgICAgICAgIHZhbHVlOiAiIg0KICAgICAgICB9LA0KICAgICAgICB7DQogICAgICAgICAgaXRlbTogdGhpcy4kdCgiTG9jYWxlU3RyaW5nLkwzMDE4MiIpLA0KICAgICAgICAgIHNlY3Rpb246IDI1LA0KICAgICAgICAgIHZhbHVlOiAiIg0KICAgICAgICB9LA0KDQogICAgICAgIHsNCiAgICAgICAgICBpdGVtOiB0aGlzLiR0KCJMb2NhbGVTdHJpbmcuTDMwMTgzIiksDQogICAgICAgICAgc2VjdGlvbjogMjYsDQogICAgICAgICAgdmFsdWU6ICIiDQogICAgICAgIH0sDQogICAgICAgIHsNCiAgICAgICAgICBpdGVtOiB0aGlzLiR0KCJMb2NhbGVTdHJpbmcuTDMwMTg0IiksDQogICAgICAgICAgc2VjdGlvbjogMjcsDQogICAgICAgICAgdmFsdWU6ICIiDQogICAgICAgIH0sDQoNCiAgICAgICAgew0KICAgICAgICAgIGl0ZW06IHRoaXMuJHQoIkxvY2FsZVN0cmluZy5MMzAxODUiKSwNCiAgICAgICAgICBzZWN0aW9uOiAyOCwNCiAgICAgICAgICB2YWx1ZTogIiINCiAgICAgICAgfSwNCg0KICAgICAgICB7DQogICAgICAgICAgaXRlbTogdGhpcy4kdCgiTG9jYWxlU3RyaW5nLkwzMDE4NiIpLA0KICAgICAgICAgIHNlY3Rpb246IDI5LA0KICAgICAgICAgIHZhbHVlOiAiIg0KICAgICAgICB9LA0KICAgICAgICB7DQogICAgICAgICAgaXRlbTogdGhpcy4kdCgiTG9jYWxlU3RyaW5nLkwzMDIxNCIpLA0KICAgICAgICAgIHNlY3Rpb246IDMyLA0KICAgICAgICAgIHZhbHVlOiAiIg0KICAgICAgICB9LA0KICAgICAgICB7DQogICAgICAgICAgaXRlbTogdGhpcy4kdCgiTG9jYWxlU3RyaW5nLkwzMDA4MiIpLA0KICAgICAgICAgIHNlY3Rpb246IDksDQogICAgICAgICAgdmFsdWU6ICIiDQogICAgICAgIH0sDQogICAgICAgIHsNCiAgICAgICAgICBpdGVtOiB0aGlzLiR0KCJMb2NhbGVTdHJpbmcuTDMwMDgzIiksDQogICAgICAgICAgc2VjdGlvbjogMTAsDQogICAgICAgICAgdmFsdWU6ICIiDQogICAgICAgIH0NCiAgICAgIF0sDQogICAgICBpY29uU2l6ZUxpc3Q6IFsNCiAgICAgICAgew0KICAgICAgICAgIGxhYmVsOiAiMzIqMzIiLA0KICAgICAgICAgIHZhbHVlOiAiMzIiDQogICAgICAgIH0sDQogICAgICAgIHsNCiAgICAgICAgICBsYWJlbDogIjQwKjQwIiwNCiAgICAgICAgICB2YWx1ZTogIjQwIg0KICAgICAgICB9DQogICAgICBdLA0KICAgICAgbTVPYmplY3RUeXBlTGlzdDogWw0KICAgICAgICB7DQogICAgICAgICAgbGFiZWw6IHRoaXMuJHQoIkxvY2FsZVN0cmluZy5EMDAwMDIiKSwNCiAgICAgICAgICB2YWx1ZTogImFsbCINCiAgICAgICAgfSwNCiAgICAgICAgew0KICAgICAgICAgIGxhYmVsOiB0aGlzLiR0KCJMb2NhbGVTdHJpbmcuRDAwMDA3IiksDQogICAgICAgICAgdmFsdWU6ICJwZW9wbGUiDQogICAgICAgIH0sDQogICAgICAgIHsNCiAgICAgICAgICBsYWJlbDogdGhpcy4kdCgiTG9jYWxlU3RyaW5nLkQwMDAwOCIpLA0KICAgICAgICAgIHZhbHVlOiAiZXF1aXBtZW50Ig0KICAgICAgICB9LA0KICAgICAgICB7DQogICAgICAgICAgbGFiZWw6IHRoaXMuJHQoIkxvY2FsZVN0cmluZy5EMDAwMDkiKSwNCiAgICAgICAgICB2YWx1ZTogInNwYWNlIg0KICAgICAgICB9LA0KICAgICAgICB7DQogICAgICAgICAgbGFiZWw6IHRoaXMuJHQoIkxvY2FsZVN0cmluZy5EMDAwMTAiKSwNCiAgICAgICAgICB2YWx1ZTogIm90aGVyIg0KICAgICAgICB9DQogICAgICBdLA0KICAgICAgcGFnZVNpemVMaXN0OiBbDQogICAgICAgIHsNCiAgICAgICAgICBsYWJlbDogIjEwLyIgKyB0aGlzLiR0KCJMb2NhbGVTdHJpbmcuTDAwMDIwIiksDQogICAgICAgICAgdmFsdWU6ICIxMCINCiAgICAgICAgfSwNCiAgICAgICAgew0KICAgICAgICAgIGxhYmVsOiAiMjUvIiArIHRoaXMuJHQoIkxvY2FsZVN0cmluZy5MMDAwMjAiKSwNCiAgICAgICAgICB2YWx1ZTogIjI1Ig0KICAgICAgICB9LA0KICAgICAgICB7DQogICAgICAgICAgbGFiZWw6ICI1MC8iICsgdGhpcy4kdCgiTG9jYWxlU3RyaW5nLkwwMDAyMCIpLA0KICAgICAgICAgIHZhbHVlOiAiNTAiDQogICAgICAgIH0sDQogICAgICAgIHsNCiAgICAgICAgICBsYWJlbDogIjEwMC8iICsgdGhpcy4kdCgiTG9jYWxlU3RyaW5nLkwwMDAyMCIpLA0KICAgICAgICAgIHZhbHVlOiAiMTAwIg0KICAgICAgICB9DQogICAgICBdLA0KICAgICAgc2hvd2luZ1RhYjogWw0KICAgICAgICB7DQogICAgICAgICAgbGFiZWw6IHRoaXMuJHQoIkxvY2FsZVN0cmluZy5EMzAwMzEiKSwNCiAgICAgICAgICB2YWx1ZTogIm0xIg0KICAgICAgICB9LA0KICAgICAgICB7DQogICAgICAgICAgbGFiZWw6IHRoaXMuJHQoIkxvY2FsZVN0cmluZy5EMzAwMzIiKSwNCiAgICAgICAgICB2YWx1ZTogIm0yIg0KICAgICAgICB9LA0KICAgICAgICB7DQogICAgICAgICAgbGFiZWw6IHRoaXMuJHQoIkxvY2FsZVN0cmluZy5EMzAwMzMiKSwNCiAgICAgICAgICB2YWx1ZTogIm0zIg0KICAgICAgICB9LA0KICAgICAgICB7DQogICAgICAgICAgbGFiZWw6IHRoaXMuJHQoIkxvY2FsZVN0cmluZy5EMzAwMzQiKSwNCiAgICAgICAgICB2YWx1ZTogIm00Ig0KICAgICAgICB9LA0KICAgICAgICB7DQogICAgICAgICAgbGFiZWw6IHRoaXMuJHQoIkxvY2FsZVN0cmluZy5EMzAwMzUiKSwNCiAgICAgICAgICB2YWx1ZTogIm01Ig0KICAgICAgICB9LA0KICAgICAgICB7DQogICAgICAgICAgbGFiZWw6IHRoaXMuJHQoIkxvY2FsZVN0cmluZy5EMzAwMzYiKSwNCiAgICAgICAgICB2YWx1ZTogIm02Ig0KICAgICAgICB9LA0KICAgICAgICB7DQogICAgICAgICAgbGFiZWw6IHRoaXMuJHQoIkxvY2FsZVN0cmluZy5GMzAwMTYiKSwNCiAgICAgICAgICB2YWx1ZTogIm04Ig0KICAgICAgICB9LA0KICAgICAgICB7DQogICAgICAgICAgbGFiZWw6IHRoaXMuJHQoIkxvY2FsZVN0cmluZy5EMzAwNDQiKSwNCiAgICAgICAgICB2YWx1ZTogIm05Ig0KICAgICAgICB9DQogICAgICAgIC8vIHsNCiAgICAgICAgLy8gICBsYWJlbDogIui7jOi3oeebo+aOpyIsDQogICAgICAgIC8vICAgdmFsdWU6ICJtNyIsDQogICAgICAgIC8vIH0sDQogICAgICBdLA0KICAgICAgc2hvd2luZ1R5cGU6IFsNCiAgICAgICAgew0KICAgICAgICAgIGxhYmVsOiB0aGlzLiR0KCJMb2NhbGVTdHJpbmcuTDMwMDU5IiksDQogICAgICAgICAgdmFsdWU6ICJ0ZW1wZXJhdHVyZSINCiAgICAgICAgfSwNCiAgICAgICAgew0KICAgICAgICAgIGxhYmVsOiB0aGlzLiR0KCJMb2NhbGVTdHJpbmcuTDMwMDEwIiksDQogICAgICAgICAgdmFsdWU6ICJoZWFydFJhdGUiDQogICAgICAgIH0sDQogICAgICAgIHsNCiAgICAgICAgICBsYWJlbDogdGhpcy4kdCgiTG9jYWxlU3RyaW5nLkwzMDAxMyIpLA0KICAgICAgICAgIHZhbHVlOiAiYmxvb2RPeHlnZW4iDQogICAgICAgIH0sDQogICAgICAgIHsNCiAgICAgICAgICBsYWJlbDogdGhpcy4kdCgiTG9jYWxlU3RyaW5nLkwzMDAzNyIpLA0KICAgICAgICAgIHZhbHVlOiAiYmxvb2RQcmVzc3VyZSINCiAgICAgICAgfSwNCiAgICAgICAgew0KICAgICAgICAgIGxhYmVsOiB0aGlzLiR0KCJMb2NhbGVTdHJpbmcuTDMwMjA4IiksDQogICAgICAgICAgdmFsdWU6ICJzdGVwIg0KICAgICAgICB9LA0KICAgICAgICB7DQogICAgICAgICAgbGFiZWw6IHRoaXMuJHQoIkxvY2FsZVN0cmluZy5MMzAxMDUiKSwNCiAgICAgICAgICB2YWx1ZTogImJyZWF0aGUiDQogICAgICAgIH0NCiAgICAgIF0sDQogICAgICBkZWZhdWx0VGFiTGlzdDogW10sDQogICAgICBkZWZhdWx0VXBkYXRlU2Vjb25kczogWw0KICAgICAgICB7DQogICAgICAgICAgbGFiZWw6ICIwIiwNCiAgICAgICAgICB2YWx1ZTogIjAiDQogICAgICAgIH0sDQogICAgICAgIHsNCiAgICAgICAgICBsYWJlbDogIjMwIiwNCiAgICAgICAgICB2YWx1ZTogIjMwIg0KICAgICAgICB9LA0KICAgICAgICB7DQogICAgICAgICAgbGFiZWw6ICI2MCIsDQogICAgICAgICAgdmFsdWU6ICI2MCINCiAgICAgICAgfSwNCiAgICAgICAgew0KICAgICAgICAgIGxhYmVsOiAiMTIwIiwNCiAgICAgICAgICB2YWx1ZTogIjEyMCINCiAgICAgICAgfQ0KICAgICAgXSwNCiAgICAgIGRlZmF1bHRJbmZsdXhSYW5nZTogWw0KICAgICAgICB7DQogICAgICAgICAgbGFiZWw6ICI4IiwNCiAgICAgICAgICB2YWx1ZTogOA0KICAgICAgICB9LA0KICAgICAgICB7DQogICAgICAgICAgbGFiZWw6ICIxMiIsDQogICAgICAgICAgdmFsdWU6IDEyDQogICAgICAgIH0sDQogICAgICAgIHsNCiAgICAgICAgICBsYWJlbDogIjI0IiwNCiAgICAgICAgICB2YWx1ZTogMjQNCiAgICAgICAgfSwNCiAgICAgICAgew0KICAgICAgICAgIGxhYmVsOiAiNDgiLA0KICAgICAgICAgIHZhbHVlOiA0OA0KICAgICAgICB9LA0KICAgICAgICB7DQogICAgICAgICAgbGFiZWw6ICI3MiIsDQogICAgICAgICAgdmFsdWU6IDcyDQogICAgICAgIH0NCiAgICAgIF0NCiAgICB9Ow0KICB9LA0KICBjcmVhdGVkKCkge30sDQogIGFzeW5jIG1vdW50ZWQoKSB7DQogICAgY29uc29sZS5sb2coImxvYWQgc3lzdGVtQ29uZmlnIik7DQogICAgYXdhaXQgdGhpcy5nZXRFdmVudExvY2FsZVN0cmluZ0xpc3QoKTsNCiAgICBhd2FpdCB0aGlzLmdldFNlcnZpY2VDb2RlTWVudSgpOw0KICAgIHRoaXMuZ2V0RGV2aWNlVHlwZU1lbnUoKTsNCiAgICB0aGlzLmxvYWRLTURhdGEoKTsNCiAgICB0aGlzLmxvYWRDYW1lcmFEYXRhKCk7DQogICAgdGhpcy5sb2FkU3RhdGlzdGljTGluZURhdGEoKTsNCiAgICB0aGlzLmlzU2hvdyA9IHRydWU7DQogICAgbGV0IGlkZW50aXR5ID0gbG9jYWxTdG9yYWdlLmdldEl0ZW0oInNuc19pZGVudGl0eSIpOw0KICAgIHRoaXMuaXNBZG1pbiA9IGlkZW50aXR5ID09ICJ0cnVlIiA/IHRydWUgOiBmYWxzZTsNCg0KICAgIGxldCBwZXJtaXNzaW9uID0gbG9jYWxTdG9yYWdlLmdldEl0ZW0oInNuc19wZXJtaXNzaW9uIik7DQogICAgaWYgKHBlcm1pc3Npb24uaW5jbHVkZXMoIltzbnNdLVsxXS1bdmlld10iKSB8fCBpZGVudGl0eSA9PT0gInRydWUiKSB7DQogICAgICB0aGlzLmRhdGEgPSBbLi4udGhpcy5kYXRhU2VjdGlvbjAxLCAuLi50aGlzLmRhdGFdOw0KICAgIH0NCiAgICBpZiAoaWRlbnRpdHkgPT09ICJ0cnVlIikgew0KICAgICAgdGhpcy5kYXRhID0gWy4uLnRoaXMuZGF0YVNlY3Rpb24wLCAuLi50aGlzLmRhdGFdOw0KICAgIH0NCiAgICB0aGlzLmNhbWVyYUNvbmZpZ0xpc3QgPSB0aGlzLmRlZmF1bHRDYW1lcmFDb25maWdMaXN0Ow0KICAgIHRoaXMuc3RhdGlzdGljTGluZUNvbmZpZ0xpc3QgPSB0aGlzLmRlZmF1bHRTdGF0aXN0aWNMaW5lQ29uZmlnTGlzdDsNCiAgICB3aW5kb3cuYWRkRXZlbnRMaXN0ZW5lcigicmVzaXplIiwgdGhpcy5yZXNpemVIZWlnaHQpOw0KICAgIC8vdGhpcy5nZXRTeXN0ZW1Db25maWcoKTsNCiAgICAvLyB0aGlzLm5vcm1hbGl6ZUhlaWdodCgpOw0KICB9LA0KICBiZWZvcmVEZXN0cm95KCkgew0KICAgIHdpbmRvdy5yZW1vdmVFdmVudExpc3RlbmVyKCJyZXNpemUiLCB0aGlzLnJlc2l6ZUhlaWdodCk7DQogIH0sDQogIG1ldGhvZHM6IHsNCiAgICBhc3luYyB0ZXN0Q29ubmVjdGlvbigpIHsNCiAgICAgIGxldCBlcnJvck1zZyA9IFsNCiAgICAgICAgdGhpcy4kdCgiTG9jYWxlU3RyaW5nLkwzMDE5NiIpLA0KICAgICAgICAiVXNlck5hbWUiLA0KICAgICAgICAiQ3JlZGVudGlhbFNlY3JldCINCiAgICAgIF07DQogICAgICBpZiAoDQogICAgICAgIHRoaXMudGhpcmRQYXJ0eURhdGEudXJsID09ICIiIHx8DQogICAgICAgIHRoaXMudGhpcmRQYXJ0eURhdGEudXNlck5hbWUgPT0gIiIgfHwNCiAgICAgICAgdGhpcy50aGlyZFBhcnR5RGF0YS5zZWNyZXQgPT0gIiINCiAgICAgICkgew0KICAgICAgICB0aGlzLiROb3RpY2UuZXJyb3Ioew0KICAgICAgICAgIHRpdGxlOiB0aGlzLiR0KCJMb2NhbGVTdHJpbmcuRTAwMDM3IiksDQogICAgICAgICAgZGVzYzogdGhpcy4kdCgiTG9jYWxlU3RyaW5nLlcwMDAzOCIsIHsgMDogZXJyb3JNc2cuam9pbigiLCIpIH0pLA0KICAgICAgICAgIGR1cmF0aW9uOiBDb25maWcuV0FSTklOR19EVVJBVElPTg0KICAgICAgICB9KTsNCiAgICAgICAgcmV0dXJuOw0KICAgICAgfQ0KICAgICAgdGhpcy4kc2VydmljZVRoaXJkUGFydHkudGVzdENvbm5lY3Rpb24ucmVxdWVzdENvbW1vbigNCiAgICAgICAgdGhpcy50aGlyZFBhcnR5RGF0YS51cmwNCiAgICAgICk7DQogICAgICBsZXQgcmVzdWx0ID0gYXdhaXQgdGhpcy4kc2VydmljZVRoaXJkUGFydHkudGVzdENvbm5lY3Rpb24uc2VuZCgNCiAgICAgICAgbnVsbCwNCiAgICAgICAgbnVsbCwNCiAgICAgICAgew0KICAgICAgICAgIHVzZXJOYW1lOiB0aGlzLnRoaXJkUGFydHlEYXRhLnVzZXJOYW1lLA0KICAgICAgICAgIHNlY3JldDogdGhpcy50aGlyZFBhcnR5RGF0YS5zZWNyZXQNCiAgICAgICAgfQ0KICAgICAgKTsNCiAgICAgIGlmIChyZXN1bHQpIHsNCiAgICAgICAgdGhpcy4kTm90aWNlLnN1Y2Nlc3Moew0KICAgICAgICAgIHRpdGxlOiB0aGlzLiR0KCJMb2NhbGVTdHJpbmcuTTAwMDA0IiksDQogICAgICAgICAgZGVzYzoNCiAgICAgICAgICAgIHRoaXMuJHQoIkxvY2FsZVN0cmluZy5EMDAwNjMiKSArDQogICAgICAgICAgICAiICIgKw0KICAgICAgICAgICAgdGhpcy4kdCgiTG9jYWxlU3RyaW5nLkQwMDE2NyIpLA0KICAgICAgICAgIGR1cmF0aW9uOiBDb25maWcuU1VDQ0VTU19EVVJBVElPTg0KICAgICAgICB9KTsNCiAgICAgIH0NCiAgICB9LA0KICAgIGdldEVkaXRQZXJtaXNzaW9uKCkgew0KICAgICAgbGV0IGlzQWxsb3cgPSBmYWxzZTsNCg0KICAgICAgbGV0IHBlcm1pc3Npb24gPSBsb2NhbFN0b3JhZ2UuZ2V0SXRlbSgic25zX3Blcm1pc3Npb24iKTsNCiAgICAgIGlmIChwZXJtaXNzaW9uLmluY2x1ZGVzKCJbc25zXS1bMV0tW2VkaXRdIikgfHwgdGhpcy5pc0FkbWluKSB7DQogICAgICAgIGlzQWxsb3cgPSB0cnVlOw0KICAgICAgfQ0KICAgICAgcmV0dXJuIGlzQWxsb3c7DQogICAgfSwNCg0KICAgIHN3aXRjaGxvZ2luVGhpcmRQYXJ0eShlbnRyeSkgew0KICAgICAgaWYgKCFlbnRyeSkgew0KICAgICAgICB0aGlzLnRoaXJkUGFydHlEYXRhLnVybCA9ICIiOw0KICAgICAgICB0aGlzLnRoaXJkUGFydHlEYXRhLnVzZXJOYW1lID0gIiI7DQogICAgICAgIHRoaXMudGhpcmRQYXJ0eURhdGEuc2VjcmV0ID0gIiI7DQogICAgICB9DQogICAgfSwNCiAgICByZXNpemVIZWlnaHQoKSB7DQogICAgICB0aGlzLm1vZGFsSW5uZXJIZWlnaHQgPQ0KICAgICAgICB0aGlzLmN1cnJlbnRUYWIgPT0gInN5c3RlbSIgPyB3aW5kb3cuaW5uZXJIZWlnaHQgLSAzMDAgOiAyNTA7DQogICAgfSwNCiAgICBhc3luYyBnZXRTZXJ2aWNlQ29kZU1lbnUoKSB7DQogICAgICBsZXQgY29uZmlnUGFyYW1zID0gew0KICAgICAgICAvLyBoYXNMaWNlbnNlOiB0cnVlLA0KICAgICAgICBjYXRlZ29yeTogImV2ZW50Ig0KICAgICAgfTsNCiAgICAgIGxldCB0bXBlcnZpY2VDb2RlTGlzdCA9IGF3YWl0IHRoaXMuJHNlcnZpY2UuZ2V0U2VydmljZXMuc2VuZCgNCiAgICAgICAgY29uZmlnUGFyYW1zDQogICAgICApOw0KICAgICAgdGhpcy5zZXJ2aWNlQ29kZUxpc3QgPSB0bXBlcnZpY2VDb2RlTGlzdC5yZXN1bHRzOw0KICAgICAgdGhpcy5zZXJ2aWNlQ29kZUxpc3QuZm9yRWFjaChpdGVtID0+IHsNCiAgICAgICAgbGV0IHRyYW5zTmFtZSA9IHRoaXMuc2VydmljZUNvZGVMb2NhbGVTdHJpbmdzTGlzdC5zb21lKA0KICAgICAgICAgIGRhdGEgPT4gZGF0YS5rZXkgPT0gaXRlbS5jb2RlDQogICAgICAgICkNCiAgICAgICAgICA/IHRoaXMuc2VydmljZUNvZGVMb2NhbGVTdHJpbmdzTGlzdC5maW5kKA0KICAgICAgICAgICAgICBkYXRhID0+IGRhdGEua2V5ID09IGl0ZW0uY29kZQ0KICAgICAgICAgICAgKS52YWx1ZQ0KICAgICAgICAgIDogaXRlbS5jb2RlOw0KDQogICAgICAgIGl0ZW0ua2V5ID0gaXRlbS5jb2RlOw0KICAgICAgICBpdGVtLnZhbHVlID0gdHJhbnNOYW1lOw0KICAgICAgfSk7DQogICAgfSwNCiAgICBjb3B5VG9rZW4oKSB7DQogICAgICBuYXZpZ2F0b3IuY2xpcGJvYXJkLndyaXRlVGV4dCh0aGlzLnRva2VuKTsNCiAgICAgIHRoaXMuJE5vdGljZS5zdWNjZXNzKHsNCiAgICAgICAgdGl0bGU6IHRoaXMuJHQoIkxvY2FsZVN0cmluZy5NMDAwMDQiKSwNCiAgICAgICAgZGVzYzogdGhpcy4kdCgiTG9jYWxlU3RyaW5nLk0zMDAwNiIpLA0KICAgICAgICBkdXJhdGlvbjogQ29uZmlnLlNVQ0NFU1NfRFVSQVRJT04NCiAgICAgIH0pOw0KICAgIH0sDQogICAgYXN5bmMgZ2VuZXJhdGVUb2tlbigpIHsNCiAgICAgIGxldCBwb3N0RGF0YSA9IHsNCiAgICAgICAgYWNjb3VudDogdGhpcy5sb2dpbkFjY291bnQsDQogICAgICAgIHBhc3N3b3JkOiB0aGlzLmxvZ2luUGFzc3dvcmQNCiAgICAgIH07DQoNCiAgICAgIGxldCB2YWxpZCA9IGF3YWl0IHRoaXMuJHNlcnZpY2UuZ2V0QXV0aFRva2VuTm9sb2FkLnNlbmQocG9zdERhdGEpOw0KICAgICAgaWYgKHZhbGlkKSB7DQogICAgICAgIGxldCByZXMgPSBhd2FpdCB0aGlzLiRzZXJ2aWNlLmdlbmVyYXRlVG9rZW4uc2VuZChwb3N0RGF0YSk7DQogICAgICAgIHRoaXMudG9rZW4gPSBsb2NhdGlvbi5vcmlnaW4gKyAiL3Nucy8jL3VzZXIvbG9naW4/ZGlyZWN0PSIgKyByZXM7DQoNCiAgICAgICAgdGhpcy4kTm90aWNlLnN1Y2Nlc3Moew0KICAgICAgICAgIHRpdGxlOiB0aGlzLiR0KCJMb2NhbGVTdHJpbmcuTTAwMDA0IiksDQogICAgICAgICAgZGVzYzogdGhpcy4kdCgiTG9jYWxlU3RyaW5nLk0zMDAwNyIpLA0KICAgICAgICAgIGR1cmF0aW9uOiBDb25maWcuU1VDQ0VTU19EVVJBVElPTg0KICAgICAgICB9KTsNCiAgICAgIH0NCiAgICB9LA0KICAgIGFzeW5jIHRlc3RDYW1lcmFDb25uZWN0aW9uKHJvdykgew0KICAgICAgbGV0IGVycm9yTWVzc2FnZSA9ICIiOw0KICAgICAgaWYgKHJvdy5iYWNrdXBEaXJlY3RvcnkgPT0gIiIpIHsNCiAgICAgICAgZXJyb3JNZXNzYWdlICs9IHRoaXMuJHQoIkxvY2FsZVN0cmluZy5XMDAwMzgiLCB7DQogICAgICAgICAgMDogdGhpcy4kdCgiTG9jYWxlU3RyaW5nLkwzMDE0MyIpDQogICAgICAgIH0pOw0KICAgICAgfQ0KICAgICAgaWYgKHJvdy5hY2NvdW50ID09ICIiKSB7DQogICAgICAgIGVycm9yTWVzc2FnZSArPSB0aGlzLiR0KCJMb2NhbGVTdHJpbmcuVzAwMDM4Iiwgew0KICAgICAgICAgIDA6IHRoaXMuJHQoIkxvY2FsZVN0cmluZy5MMzAxNDQiKQ0KICAgICAgICB9KTsNCiAgICAgIH0NCiAgICAgIGlmIChyb3cucGFzc3dvcmQgPT0gIiIpIHsNCiAgICAgICAgZXJyb3JNZXNzYWdlICs9IHRoaXMuJHQoIkxvY2FsZVN0cmluZy5XMDAwMzgiLCB7DQogICAgICAgICAgMDogdGhpcy4kdCgiTG9jYWxlU3RyaW5nLkwwMDAwMyIpDQogICAgICAgIH0pOw0KICAgICAgfQ0KICAgICAgaWYgKGVycm9yTWVzc2FnZSAhPSAiIikgew0KICAgICAgICB0aGlzLiROb3RpY2Uud2FybmluZyh7DQogICAgICAgICAgdGl0bGU6IHRoaXMuJHQoIkxvY2FsZVN0cmluZy5XMDAwMDUiKSwNCiAgICAgICAgICBkZXNjOiBlcnJvck1lc3NhZ2UsDQogICAgICAgICAgZHVyYXRpb246IENvbmZpZy5XQVJOSU5HX0RVUkFUSU9ODQogICAgICAgIH0pOw0KICAgICAgICByZXR1cm4gZmFsc2U7DQogICAgICB9DQoNCiAgICAgIGxldCBwYXJhbXMgPSB7DQogICAgICAgIGZpbGVwYXRoOiByb3cuYmFja3VwRGlyZWN0b3J5LA0KICAgICAgICB1c2VybmFtZTogcm93LmFjY291bnQsDQogICAgICAgIHVzZXJBdXRoOiBCYXNlNjQuZW5jb2RlKHJvdy5wYXNzd29yZCkNCiAgICAgIH07DQoNCiAgICAgIGxldCBjb25uZXRSZXN1bHQgPSBhd2FpdCB0aGlzLiRzZXJ2aWNlLnRlc3RDYW1lcmFDb25uZWN0aW9uLnNlbmQocGFyYW1zKTsNCiAgICAgIGlmIChjb25uZXRSZXN1bHQgPT0gIiIpIHsNCiAgICAgICAgdGhpcy4kTm90aWNlLnN1Y2Nlc3Moew0KICAgICAgICAgIHRpdGxlOiB0aGlzLiR0KCJMb2NhbGVTdHJpbmcuTTAwMDA0IiksIC8v5oiQ5YqfDQogICAgICAgICAgZGVzYzoNCiAgICAgICAgICAgIHRoaXMuJHQoIkxvY2FsZVN0cmluZy5CMzAwMTAiKSArDQogICAgICAgICAgICAiICIgKw0KICAgICAgICAgICAgdGhpcy4kdCgiTG9jYWxlU3RyaW5nLk0wMDAwNCIpLA0KICAgICAgICAgIGR1cmF0aW9uOiBDb25maWcuV0FSTklOR19EVVJBVElPTg0KICAgICAgICB9KTsNCiAgICAgIH0NCiAgICB9LA0KICAgIGFzeW5jIG9uQ2FtZXJhRGF0YUNoYW5nZSh0eXBlLCByb3csIGluZGV4KSB7DQogICAgICBzd2l0Y2ggKHR5cGUpIHsNCiAgICAgICAgY2FzZSAiY2FwdHVyZSI6DQogICAgICAgICAgdGhpcy5jYW1lcmFDb25maWdMaXN0W2luZGV4XS5jYXB0dXJlID0gcm93LmNhcHR1cmU7DQogICAgICAgICAgYnJlYWs7DQogICAgICAgIGNhc2UgInByZWZpeE1pbnV0ZSI6DQogICAgICAgICAgdGhpcy5jYW1lcmFDb25maWdMaXN0W2luZGV4XS5wcmVmaXhNaW51dGUgPSByb3cucHJlZml4TWludXRlOw0KICAgICAgICAgIGJyZWFrOw0KICAgICAgICBjYXNlICJzdWZmaXhNaW51dGUiOg0KICAgICAgICAgIHRoaXMuY2FtZXJhQ29uZmlnTGlzdFtpbmRleF0uc3VmZml4TWludXRlID0gcm93LnN1ZmZpeE1pbnV0ZTsNCiAgICAgICAgICBicmVhazsNCiAgICAgICAgY2FzZSAiYmFja3VwRGlyZWN0b3J5IjoNCiAgICAgICAgICB0aGlzLmNhbWVyYUNvbmZpZ0xpc3RbaW5kZXhdLmJhY2t1cERpcmVjdG9yeSA9IHJvdy5iYWNrdXBEaXJlY3Rvcnk7DQogICAgICAgICAgYnJlYWs7DQogICAgICAgIGNhc2UgImFjY291bnQiOg0KICAgICAgICAgIHRoaXMuY2FtZXJhQ29uZmlnTGlzdFtpbmRleF0uYWNjb3VudCA9IHJvdy5hY2NvdW50Ow0KICAgICAgICAgIGJyZWFrOw0KICAgICAgICBjYXNlICJwYXNzd29yZCI6DQogICAgICAgICAgdGhpcy5jYW1lcmFDb25maWdMaXN0W2luZGV4XS5wYXNzd29yZCA9IHJvdy5wYXNzd29yZDsNCiAgICAgICAgICBicmVhazsNCiAgICAgICAgY2FzZSAia2VlcERheXMiOg0KICAgICAgICAgIHRoaXMuY2FtZXJhQ29uZmlnTGlzdFtpbmRleF0ua2VlcERheXMgPSByb3cua2VlcERheXM7DQogICAgICAgICAgYnJlYWs7DQogICAgICAgIGNhc2UgInBvc2l0aW9uQ2FwdHVyZSI6DQogICAgICAgICAgdGhpcy5jYW1lcmFDb25maWdMaXN0W2luZGV4XS5wb3NpdGlvbkNhcHR1cmUgPSByb3cucG9zaXRpb25DYXB0dXJlOw0KICAgICAgICAgIGJyZWFrOw0KICAgICAgfQ0KICAgICAgLy8gY29uc29sZS5sb2codGhpcy5jYW1lcmFDb25maWdMaXN0KTsNCiAgICB9LA0KDQogICAgYXN5bmMgb25TdGF0aXN0aWNMaW5lRGF0YUNoYW5nZSh0eXBlLCByb3csIGluZGV4KSB7DQogICAgICBzd2l0Y2ggKHR5cGUpIHsNCiAgICAgICAgY2FzZSAiaW50ZXJ2YWwiOg0KICAgICAgICAgIHRoaXMuc3RhdGlzdGljTGluZUNvbmZpZ0xpc3RbaW5kZXhdLmludGVydmFsID0gcm93LmludGVydmFsOw0KICAgICAgICAgIGJyZWFrOw0KICAgICAgICBjYXNlICJkYXRhVHlwZSI6DQogICAgICAgICAgdGhpcy5zdGF0aXN0aWNMaW5lQ29uZmlnTGlzdFtpbmRleF0uZGF0YVR5cGUgPSByb3cuZGF0YVR5cGU7DQogICAgICAgICAgYnJlYWs7DQogICAgICAgIGNhc2UgIndhcm5pbmdMaW5lIjoNCiAgICAgICAgICB0aGlzLnN0YXRpc3RpY0xpbmVDb25maWdMaXN0W2luZGV4XS53YXJuaW5nTGluZSA9IHJvdy53YXJuaW5nTGluZTsNCiAgICAgICAgICBicmVhazsNCiAgICAgIH0NCiAgICAgIC8vIGNvbnNvbGUubG9nKHRoaXMuY2FtZXJhQ29uZmlnTGlzdCk7DQogICAgfSwNCiAgICAvKioNCiAgICAgIHdoZW4gY2hhbmdlIGRldmljZSBkZWZhdWx0IHNlbGVjdCBMaXN0IGFjdGlvbg0KICAgICAgMS4gZGVmYXVsdCBpcyBhbGwNCiAgICAgIDIuIHdoZW4gZGlzYWJsZSBhbGwsIGF1dG8gaW5zZXJ0IGRldmljZSB0eXBlIHdoaWNoIGV4aXN0IGZyb20gY29uc29sZQ0KICAgICAgMy4gd2hlbiBkaWFibGUgYSBkZXZpY2UgdHlwZSB3aGljaCBpcyBpbmNsdWRlZCBpbiBEZWZhdWx0RGV2aWNlVHlwZUxpc3QoZnJvbSBBUEkpICwgaXQgd2lsbCBzaG93IGFsZXJ0IGRpYWxvZyBhbmQgcmVpbnNlcnQgdG8gc2VsZWN0aW9uDQogICAgICoqLw0KICAgIG9wZW5EZXZpY2VTZWxlY3Rpb24oZGF0YSkgew0KICAgICAgdGhpcy5vcGVuRGV2aWNlU2VsZWN0ID0gZGF0YTsNCiAgICB9LA0KICAgIGNoYW5nZURldmljZURlZmF1bHRTZWxlY3RMaXN0KGl0ZW0pIHsNCiAgICAgIGlmICh0aGlzLm9wZW5EZXZpY2VTZWxlY3QpIHsNCiAgICAgICAgaWYgKGl0ZW0uZmlsdGVyKGRhdGEgPT4gZGF0YSA9PSAiYWxsIikubGVuZ3RoID4gMCkgew0KICAgICAgICAgIHRoaXMuY29uZmlnRGF0YS5kZXZpY2VTZWxlY3RMaXN0ID0gWyJhbGwiXTsNCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICB0aGlzLiRzZXJ2aWNlLmdldERlZmF1bHREZXZpY2VUeXBlTGlzdA0KICAgICAgICAgICAgLnNlbmQobnVsbCkNCiAgICAgICAgICAgIC50aGVuKGRlZmF1bHRMaXN0ID0+IHsNCiAgICAgICAgICAgICAgbGV0IGRlZmF1bHRTZWxlY3RMaXN0ID0gZGVmYXVsdExpc3QuZGV2aWNlTGlzdC5zcGxpdCgiLCIpOw0KICAgICAgICAgICAgICBpZiAoaXRlbS5sZW5ndGggPT0gMCkgew0KICAgICAgICAgICAgICAgIHRoaXMuY29uZmlnRGF0YS5kZXZpY2VTZWxlY3RMaXN0ID0gZGVmYXVsdFNlbGVjdExpc3Q7DQogICAgICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICAgICAgZGVmYXVsdFNlbGVjdExpc3QuZm9yRWFjaChpdGVtID0+IHsNCiAgICAgICAgICAgICAgICAgIGlmICghdGhpcy5jb25maWdEYXRhLmRldmljZVNlbGVjdExpc3QuaW5jbHVkZXMoaXRlbSkpIHsNCiAgICAgICAgICAgICAgICAgICAgbGV0IGRldmljZU5hbWUgPSAiIjsNCiAgICAgICAgICAgICAgICAgICAgaWYgKA0KICAgICAgICAgICAgICAgICAgICAgIHRoaXMuZGV2aWNlRGVmYXVsdFNlbGVjdGlvbi5zb21lKGRhdGEgPT4NCiAgICAgICAgICAgICAgICAgICAgICAgIGl0ZW0uaW5jbHVkZXMoZGF0YS5rZXkpDQogICAgICAgICAgICAgICAgICAgICAgKQ0KICAgICAgICAgICAgICAgICAgICApIHsNCiAgICAgICAgICAgICAgICAgICAgICBkZXZpY2VOYW1lID0gdGhpcy5kZXZpY2VEZWZhdWx0U2VsZWN0aW9uLmZpbmQoZGF0YSA9Pg0KICAgICAgICAgICAgICAgICAgICAgICAgaXRlbS5pbmNsdWRlcyhkYXRhLmtleSkNCiAgICAgICAgICAgICAgICAgICAgICApLnZhbHVlOw0KICAgICAgICAgICAgICAgICAgICAgIHRoaXMuJE5vdGljZS5lcnJvcih7DQogICAgICAgICAgICAgICAgICAgICAgICB0aXRsZTogdGhpcy4kdCgiTG9jYWxlU3RyaW5nLkUwMDAzNyIpLA0KICAgICAgICAgICAgICAgICAgICAgICAgZGVzYzogdGhpcy4kdCgiTG9jYWxlU3RyaW5nLlczMDAxMSIsIHsgMDogZGV2aWNlTmFtZSB9KSwNCiAgICAgICAgICAgICAgICAgICAgICAgIGR1cmF0aW9uOiBDb25maWcuZXJyb3JEdXJhdGlvbg0KICAgICAgICAgICAgICAgICAgICAgIH0pOw0KICAgICAgICAgICAgICAgICAgICAgIHRoaXMuY29uZmlnRGF0YS5kZXZpY2VTZWxlY3RMaXN0LnB1c2goaXRlbSk7DQogICAgICAgICAgICAgICAgICAgIH0NCiAgICAgICAgICAgICAgICAgIH0NCiAgICAgICAgICAgICAgICB9KTsNCiAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgfSk7DQogICAgICAgIH0NCiAgICAgIH0NCiAgICB9LA0KICAgIGFzeW5jIGdldEV2ZW50TG9jYWxlU3RyaW5nTGlzdCgpIHsNCiAgICAgIGxldCBwYXJhbXMgPSB7DQogICAgICAgIHNlYXJjaDogIiINCiAgICAgIH07DQogICAgICBsZXQgdG1wID0gW107DQogICAgICBsZXQgcmVzID0gYXdhaXQgdGhpcy4kc2VydmljZS5nZXRTZXJ2aWNlQ29kZUxvY2FsZVN0cmluZ3Muc2VuZChwYXJhbXMpOw0KICAgICAgcmVzLnJlc3VsdHMuZm9yRWFjaChyID0+IHsNCiAgICAgICAgdG1wLnB1c2goew0KICAgICAgICAgIGtleTogci5jb2RlLA0KICAgICAgICAgIHZhbHVlOiB0aGlzLiR0KCJMb2NhbGVTdHJpbmcuIiArIHIubGFuZ3MubmFtZUlkKQ0KICAgICAgICB9KTsNCiAgICAgIH0pOw0KICAgICAgdGhpcy5zZXJ2aWNlQ29kZUxvY2FsZVN0cmluZ3NMaXN0ID0gSlNPTi5wYXJzZShKU09OLnN0cmluZ2lmeSh0bXApKTsNCiAgICB9LA0KICAgIGNoYW5nZU11bHRpUGxhbmVzKHZhbCkgew0KICAgICAgdGhpcy5jb25maWdEYXRhLnNlbGVjdGVkUGxhbmVzID0gdmFsOw0KICAgIH0sDQogICAgc3dpdGNoTXVsdGlQbGFuZXModmFsKSB7DQogICAgICBpZiAoIXZhbCkgew0KICAgICAgICB0aGlzLmNvbmZpZ0RhdGEuc2VsZWN0ZWRQbGFuZXMgPSBbXTsNCiAgICAgIH0NCiAgICB9LA0KICAgIGNoYW5nZVRhYihlKSB7DQogICAgICB0aGlzLmN1cnJlbnRUYWIgPSBlOw0KICAgICAgdGhpcy5yZXNpemVIZWlnaHQoKTsNCiAgICB9LA0KICAgIHRyYW5zRXZlbnROYW1lKHNlcnZpY2VDb2RlKSB7DQogICAgICAvLyBjb25zb2xlLmxvZygNCiAgICAgIC8vICAgInRyYW5zRXZlbnROYW1lOiAiICsgSlNPTi5zdHJpbmdpZnkodGhpcy5zZXJ2aWNlQ29kZUxvY2FsZVN0cmluZ3NMaXN0KQ0KICAgICAgLy8gKTsNCiAgICAgIGxldCBydG5TdHJpbmcgPSAiIjsNCiAgICAgIGlmICgNCiAgICAgICAgdGhpcy5zZXJ2aWNlQ29kZUxvY2FsZVN0cmluZ3NMaXN0ICYmDQogICAgICAgIHRoaXMuc2VydmljZUNvZGVMb2NhbGVTdHJpbmdzTGlzdC5sZW5ndGggPiAwDQogICAgICApIHsNCiAgICAgICAgcnRuU3RyaW5nID0gdGhpcy5zZXJ2aWNlQ29kZUxvY2FsZVN0cmluZ3NMaXN0LmZpbmQoDQogICAgICAgICAgaXRlbSA9PiBpdGVtLmtleSA9PT0gc2VydmljZUNvZGUNCiAgICAgICAgKS52YWx1ZTsNCiAgICAgIH0NCiAgICAgIHJldHVybiBydG5TdHJpbmc7DQogICAgfSwNCiAgICBnZXREZXZpY2VMaXN0TG9zc1NpZ25hbExvd0JhdHRlcnlBYm5vcm1hbERldmljZSgpIHsNCiAgICAgIGxldCB0ZW1wRGV2aWNlTGlzdExvc3NTaWduYWwgPSBbXTsNCiAgICAgIGxldCB0ZW1wRGV2aWNlTGlzdExvc3NTaWduYWwyID0gW107DQogICAgICBsZXQgdGVtcERldmljZUxpc3RMb3dCYXR0ZXJ5ID0gW107DQogICAgICBsZXQgdGVtcERldmljZUxpc3RBYm5vcm1hbERldmljZSA9IFtdOw0KICAgICAgdGhpcy5hbGxEZXZpY2VUeXBlTWVudS5mb3JFYWNoKGl0ZW0gPT4gew0KICAgICAgICBpZiAoaXRlbS5zdXBwb3J0RGF0YUV2ZW50KSB7DQogICAgICAgICAgaWYgKA0KICAgICAgICAgICAgaXRlbS5zdXBwb3J0RGF0YUV2ZW50LnNvbWUoaSA9PiBpLnNlcnZpY2VDb2RlID09PSAiTG9zc1NpZ25hbCIpICYmDQogICAgICAgICAgICBpdGVtLmlzQmxlRGV2aWNlDQogICAgICAgICAgKSB7DQogICAgICAgICAgICB0ZW1wRGV2aWNlTGlzdExvc3NTaWduYWwucHVzaCh7DQogICAgICAgICAgICAgIGtleTogaXRlbS50eXBlLA0KICAgICAgICAgICAgICB2YWx1ZTogdGhpcy4kdCgiTG9jYWxlU3RyaW5nLiIgKyBpdGVtLmxhbmdzLm5hbWVJZCkNCiAgICAgICAgICAgIH0pOw0KICAgICAgICAgIH0NCiAgICAgICAgICBpZiAoDQogICAgICAgICAgICBpdGVtLnN1cHBvcnREYXRhRXZlbnQuc29tZShpID0+IGkuc2VydmljZUNvZGUgPT09ICJMb3NzU2lnbmFsIikgJiYNCiAgICAgICAgICAgICFpdGVtLmlzQmxlRGV2aWNlDQogICAgICAgICAgKSB7DQogICAgICAgICAgICB0ZW1wRGV2aWNlTGlzdExvc3NTaWduYWwyLnB1c2goew0KICAgICAgICAgICAgICBrZXk6IGl0ZW0udHlwZSwNCiAgICAgICAgICAgICAgdmFsdWU6IHRoaXMuJHQoIkxvY2FsZVN0cmluZy4iICsgaXRlbS5sYW5ncy5uYW1lSWQpDQogICAgICAgICAgICB9KTsNCiAgICAgICAgICB9DQogICAgICAgICAgaWYgKGl0ZW0uc3VwcG9ydERhdGFFdmVudC5zb21lKGkgPT4gaS5zZXJ2aWNlQ29kZSA9PT0gIkxvd0JhdHRlcnkiKSkgew0KICAgICAgICAgICAgdGVtcERldmljZUxpc3RMb3dCYXR0ZXJ5LnB1c2goew0KICAgICAgICAgICAgICBrZXk6IGl0ZW0udHlwZSwNCiAgICAgICAgICAgICAgdmFsdWU6IHRoaXMuJHQoIkxvY2FsZVN0cmluZy4iICsgaXRlbS5sYW5ncy5uYW1lSWQpDQogICAgICAgICAgICB9KTsNCiAgICAgICAgICB9DQogICAgICAgICAgaWYgKA0KICAgICAgICAgICAgaXRlbS5zdXBwb3J0RGF0YUV2ZW50LnNvbWUoaSA9PiBpLnNlcnZpY2VDb2RlID09PSAiQWJub3JtYWxEZXZpY2UiKQ0KICAgICAgICAgICkgew0KICAgICAgICAgICAgdGVtcERldmljZUxpc3RBYm5vcm1hbERldmljZS5wdXNoKHsNCiAgICAgICAgICAgICAga2V5OiBpdGVtLnR5cGUsDQogICAgICAgICAgICAgIHZhbHVlOiB0aGlzLiR0KCJMb2NhbGVTdHJpbmcuIiArIGl0ZW0ubGFuZ3MubmFtZUlkKQ0KICAgICAgICAgICAgfSk7DQogICAgICAgICAgfQ0KICAgICAgICB9DQogICAgICB9KTsNCiAgICAgIHRoaXMuZGV2aWNlTGlzdExvc3NTaWduYWwgPSBBcnJheS5mcm9tKG5ldyBTZXQodGVtcERldmljZUxpc3RMb3NzU2lnbmFsKSk7DQogICAgICB0aGlzLmRldmljZUxpc3RMb3NzU2lnbmFsMiA9IEFycmF5LmZyb20oDQogICAgICAgIG5ldyBTZXQodGVtcERldmljZUxpc3RMb3NzU2lnbmFsMikNCiAgICAgICk7DQogICAgICB0aGlzLmRldmljZUxpc3RMb3dCYXR0ZXJ5ID0gQXJyYXkuZnJvbShuZXcgU2V0KHRlbXBEZXZpY2VMaXN0TG93QmF0dGVyeSkpOw0KICAgICAgdGhpcy5kZXZpY2VMaXN0QWJub3JtYWxEZXZpY2UgPSBBcnJheS5mcm9tKA0KICAgICAgICBuZXcgU2V0KHRlbXBEZXZpY2VMaXN0QWJub3JtYWxEZXZpY2UpDQogICAgICApOw0KICAgIH0sDQogICAgZ2V0RXZlbnRTZXJ2aWNlQ29kZUxpc3QoKSB7DQogICAgICBsZXQgdG1wRXZlbnRTZXJ2aWNlQ29kZUxpc3QgPSBbXTsNCiAgICAgIHRoaXMuYWxsRGV2aWNlVHlwZU1lbnUuZm9yRWFjaChpdGVtID0+IHsNCiAgICAgICAgaWYgKGl0ZW0uc3VwcG9ydERhdGFFdmVudCkgew0KICAgICAgICAgIGxldCBzY0xpc3QgPSBpdGVtLnN1cHBvcnREYXRhRXZlbnQubWFwKGUgPT4gZS5zZXJ2aWNlQ29kZSk7DQogICAgICAgICAgdG1wRXZlbnRTZXJ2aWNlQ29kZUxpc3QgPSBbLi4udG1wRXZlbnRTZXJ2aWNlQ29kZUxpc3QsIC4uLnNjTGlzdF07DQogICAgICAgIH0NCiAgICAgIH0pOw0KDQogICAgICB0bXBFdmVudFNlcnZpY2VDb2RlTGlzdCA9IEFycmF5LmZyb20obmV3IFNldCh0bXBFdmVudFNlcnZpY2VDb2RlTGlzdCkpOw0KICAgICAgdG1wRXZlbnRTZXJ2aWNlQ29kZUxpc3QuZm9yRWFjaChpdGVtID0+IHsNCiAgICAgICAgdGhpcy5ldmVudFNlcnZpY2VDb2RlTGlzdC5wdXNoKHsNCiAgICAgICAgICBrZXk6IGl0ZW0sDQogICAgICAgICAgdmFsdWU6IHRoaXMudHJhbnNFdmVudE5hbWUoaXRlbSkNCiAgICAgICAgfSk7DQogICAgICB9KTsNCiAgICB9LA0KICAgIGFzeW5jIGdldERldmljZVR5cGVNZW51KCkgew0KICAgICAgbGV0IGRldmljZVR5cGVzUGFyYW1zID0gbmV3IFVSTFNlYXJjaFBhcmFtcygpOw0KICAgICAgZGV2aWNlVHlwZXNQYXJhbXMuYXBwZW5kKCJhY3RpdmUiLCB0cnVlKTsNCiAgICAgIHRoaXMuYWxsRGV2aWNlVHlwZU1lbnUgPSBhd2FpdCB0aGlzLiRzZXJ2aWNlLmdldERldmljZVR5cGVzbWVudS5zZW5kKA0KICAgICAgICBkZXZpY2VUeXBlc1BhcmFtcw0KICAgICAgKTsNCiAgICAgIHRoaXMuZ2V0RGV2aWNlU2VsZWN0TGlzdCgpOw0KICAgICAgdGhpcy5nZXRFdmVudFNlcnZpY2VDb2RlTGlzdCgpOw0KICAgICAgdGhpcy5nZXREZXZpY2VMaXN0TG9zc1NpZ25hbExvd0JhdHRlcnlBYm5vcm1hbERldmljZSgpOw0KICAgICAgdGhpcy5sb2FkTG9nbygpOw0KICAgICAgdGhpcy5sb2FkU291bmQoKTsNCiAgICAgIHRoaXMubG9hZENvbmZpZygpOw0KICAgICAgdGhpcy5sb2FkVGhpcmRQYXJ0eSgpOw0KICAgIH0sDQogICAgZ2V0RGV2aWNlU2VsZWN0TGlzdCgpIHsNCiAgICAgIHRoaXMuZGV2aWNlRGVmYXVsdFNlbGVjdGlvbiA9IHRoaXMuYWxsRGV2aWNlVHlwZU1lbnUubWFwKGl0ZW0gPT4gKHsNCiAgICAgICAga2V5OiBpdGVtLnR5cGUsDQogICAgICAgIHZhbHVlOiB0aGlzLiR0KCJMb2NhbGVTdHJpbmcuIiArIGl0ZW0ubGFuZ3MubmFtZUlkKQ0KICAgICAgfSkpOw0KICAgICAgdGhpcy5kZXZpY2VEZWZhdWx0U2VsZWN0aW9uID0gWw0KICAgICAgICAuLi5beyBrZXk6ICJhbGwiLCB2YWx1ZTogdGhpcy4kdCgiTG9jYWxlU3RyaW5nLkQwMDAwMiIpIH1dLA0KICAgICAgICAuLi50aGlzLmRldmljZURlZmF1bHRTZWxlY3Rpb24NCiAgICAgIF07DQogICAgfSwNCiAgICBlbmRlZCgpIHsNCiAgICAgIGxldCBhdWRpbyA9IHRoaXMuJHJlZnMuYXVkaW87DQogICAgICB0aGlzLmlzUGxheSA9IGZhbHNlOw0KICAgICAgYXVkaW8ucGF1c2UoKTsNCiAgICAgIGF1ZGlvLmN1cnJlbnRUaW1lID0gMDsNCiAgICAgIGRvY3VtZW50LnF1ZXJ5U2VsZWN0b3IoIi50b2dnbGUtc291bmQiKS5jbGFzc0xpc3QuYWRkKCJwYXVzZWQiKTsNCiAgICB9LA0KICAgIHBsYXkoY29udHJvbCkgew0KICAgICAgbGV0IGF1ZGlvID0gdGhpcy4kcmVmcy5hdWRpbzsNCiAgICAgIGlmIChjb250cm9sICE9IG51bGwgfHwgY29udHJvbCAhPSB1bmRlZmluZWQpIHsNCiAgICAgICAgdGhpcy5pc1BsYXkgPSBmYWxzZTsNCiAgICAgICAgYXVkaW8ucGF1c2UoKTsNCiAgICAgICAgYXVkaW8uY3VycmVudFRpbWUgPSAwOw0KICAgICAgICBkb2N1bWVudC5xdWVyeVNlbGVjdG9yKCIudG9nZ2xlLXNvdW5kIikuY2xhc3NMaXN0LmFkZCgicGF1c2VkIik7DQogICAgICAgIHJldHVybjsNCiAgICAgIH0NCg0KICAgICAgaWYgKA0KICAgICAgICBhdWRpby5wYXVzZWQgJiYNCiAgICAgICAgZG9jdW1lbnQucXVlcnlTZWxlY3RvcigiLnRvZ2dsZS1zb3VuZCIpLmNsYXNzTGlzdC5jb250YWlucygicGF1c2VkIikNCiAgICAgICkgew0KICAgICAgICB0aGlzLmlzUGxheSA9IHRydWU7DQogICAgICAgIGRvY3VtZW50LnF1ZXJ5U2VsZWN0b3IoIi50b2dnbGUtc291bmQiKS5jbGFzc0xpc3QucmVtb3ZlKCJwYXVzZWQiKTsNCiAgICAgICAgYXVkaW8ucGxheSgpOw0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgdGhpcy5pc1BsYXkgPSBmYWxzZTsNCiAgICAgICAgYXVkaW8ucGF1c2UoKTsNCiAgICAgICAgYXVkaW8uY3VycmVudFRpbWUgPSAwOw0KICAgICAgICBkb2N1bWVudC5xdWVyeVNlbGVjdG9yKCIudG9nZ2xlLXNvdW5kIikuY2xhc3NMaXN0LmFkZCgicGF1c2VkIik7DQogICAgICB9DQogICAgfSwNCiAgICByZXN0SW1hZ2UoKSB7DQogICAgICBjb25zdCBwcmV2aWV3ID0gZG9jdW1lbnQucXVlcnlTZWxlY3RvcigiI3ByZXZpZXciKTsNCiAgICAgIHByZXZpZXcuc3JjID0gIiI7DQogICAgICB0aGlzLnNob3dDbGVhbkljb24gPSBmYWxzZTsNCiAgICAgIHRoaXMuaW1nRmlsZVVSTCA9IG51bGw7DQogICAgfSwNCg0KICAgIGhhbmRsZVVwbG9hZEltYWdlKGltZ0ZpbGUpIHsNCiAgICAgIGxldCBpbWdGaWxldXJsID0gbnVsbDsNCiAgICAgIGlmICgNCiAgICAgICAgaW1nRmlsZS50eXBlLnRvTG93ZXJDYXNlKCkuaW5jbHVkZXMoInBuZyIpIHx8DQogICAgICAgIGltZ0ZpbGUudHlwZS50b0xvd2VyQ2FzZSgpLmluY2x1ZGVzKCJqcGciKSB8fA0KICAgICAgICBpbWdGaWxlLnR5cGUudG9Mb3dlckNhc2UoKS5pbmNsdWRlcygianBlZyIpDQogICAgICApIHsNCiAgICAgICAgY29uc3QgcmVhZGVyID0gbmV3IEZpbGVSZWFkZXIoKTsNCiAgICAgICAgY29uc3QgcHJldmlldyA9IGRvY3VtZW50LnF1ZXJ5U2VsZWN0b3IoIiNwcmV2aWV3Iik7DQoNCiAgICAgICAgcmVhZGVyLmFkZEV2ZW50TGlzdGVuZXIoDQogICAgICAgICAgImxvYWQiLA0KICAgICAgICAgIGZ1bmN0aW9uKCkgew0KICAgICAgICAgICAgcHJldmlldy5zcmMgPSByZWFkZXIucmVzdWx0Ow0KICAgICAgICAgIH0sDQogICAgICAgICAgZmFsc2UNCiAgICAgICAgKTsNCg0KICAgICAgICBpZiAoaW1nRmlsZSkgew0KICAgICAgICAgIHJlYWRlci5yZWFkQXNEYXRhVVJMKGltZ0ZpbGUpOw0KICAgICAgICB9DQogICAgICAgIC8vDQogICAgICAgIC8vIGlmICh3aW5kb3cuY3JlYXRlT2JqZWN0VVJMICE9IHVuZGVmaW5lZCkgew0KICAgICAgICAvLyAgIGltZ0ZpbGV1cmwgPSB3aW5kb3cuY3JlYXRlT2JqZWN0VVJMKGltZ0ZpbGUpOw0KICAgICAgICAvLyB9IGVsc2UgaWYgKHdpbmRvdy5VUkwgIT0gdW5kZWZpbmVkKSB7DQogICAgICAgIC8vICAgaW1nRmlsZXVybCA9IHdpbmRvdy5VUkwuY3JlYXRlT2JqZWN0VVJMKGltZ0ZpbGUpOw0KICAgICAgICAvLyB9IGVsc2UgaWYgKHdpbmRvdy53ZWJraXRVUkwgIT0gdW5kZWZpbmVkKSB7DQogICAgICAgIC8vICAgaW1nRmlsZXVybCA9IHdpbmRvdy53ZWJpdFVSTC5jcmVhdGVPYmplY3RVUkwoaW1nRmlsZSk7DQogICAgICAgIC8vIH0NCiAgICAgICAgdGhpcy5pbWdGaWxlID0gaW1nRmlsZTsNCiAgICAgICAgdGhpcy5pbWdGaWxlVVJMID0gaW1nRmlsZXVybDsNCiAgICAgICAgdGhpcy5zaG93Q2xlYW5JY29uID0gdHJ1ZTsNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIHRoaXMuJE5vdGljZS5lcnJvcih7DQogICAgICAgICAgdGl0bGU6IHRoaXMuJHQoIkxvY2FsZVN0cmluZy5FMDAwMzciKSwNCiAgICAgICAgICBkZXNjOiB0aGlzLiR0KCJMb2NhbGVTdHJpbmcuRTAwMDQwIiwgew0KICAgICAgICAgICAgMDogdGhpcy4kdCgiTG9jYWxlU3RyaW5nLkwzMDEzNCIpDQogICAgICAgICAgfSksDQogICAgICAgICAgZHVyYXRpb246IENvbmZpZy5lcnJvckR1cmF0aW9uDQogICAgICAgIH0pOw0KICAgICAgfQ0KICAgIH0sDQogICAgaGFuZGxlVXBsb2FkKGZpbGUpIHsNCiAgICAgIHRoaXMuaXNQbGF5ID0gZmFsc2U7DQogICAgICBsZXQgYXVkaW8gPSB0aGlzLiRyZWZzLmF1ZGlvOw0KICAgICAgYXVkaW8ucGF1c2UoKTsNCiAgICAgIGRvY3VtZW50LnF1ZXJ5U2VsZWN0b3IoIi50b2dnbGUtc291bmQiKS5jbGFzc0xpc3QuYWRkKCJwYXVzZWQiKTsNCg0KICAgICAgbGV0IGF1ZGlvRWxlbWVudCA9IG5ldyBGaWxlUmVhZGVyKCk7DQogICAgICBhdWRpb0VsZW1lbnQucmVhZEFzRGF0YVVSTChmaWxlKTsNCiAgICAgIGF1ZGlvRWxlbWVudC5hZGRFdmVudExpc3RlbmVyKCJsb2FkIiwgKCkgPT4gew0KICAgICAgICBpZiAoIWF1ZGlvRWxlbWVudC5yZXN1bHQuaW5jbHVkZXMoImRhdGE6YXVkaW8vIikpIHsNCiAgICAgICAgICB0aGlzLmZpbGUgPSBudWxsOw0KICAgICAgICAgIHRoaXMuZmlsZVVSTCA9IHRoaXMuZmlsZU9yaVVSTDsNCiAgICAgICAgICB0aGlzLiROb3RpY2UuZXJyb3Ioew0KICAgICAgICAgICAgdGl0bGU6IHRoaXMuJHQoIkxvY2FsZVN0cmluZy5FMDAwMzciKSwNCiAgICAgICAgICAgIGRlc2M6IHRoaXMuJHQoIkxvY2FsZVN0cmluZy5FMDAwNDAiLCB7DQogICAgICAgICAgICAgIDA6IHRoaXMuJHQoIkxvY2FsZVN0cmluZy5MMzAwNTUiKQ0KICAgICAgICAgICAgfSksDQogICAgICAgICAgICBkdXJhdGlvbjogQ29uZmlnLmVycm9yRHVyYXRpb24NCiAgICAgICAgICB9KTsNCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICB0aGlzLmZpbGUgPSBmaWxlOw0KICAgICAgICAgIHRoaXMuZmlsZVVSTCA9IGF1ZGlvRWxlbWVudC5yZXN1bHQ7DQoNCiAgICAgICAgICBzZXRUaW1lb3V0KCgpID0+IHsNCiAgICAgICAgICAgIGlmIChhdWRpby5kdXJhdGlvbiA+IDE1KSB7DQogICAgICAgICAgICAgIHRoaXMuZmlsZSA9IG51bGw7DQogICAgICAgICAgICAgIHRoaXMuZmlsZVVSTCA9IHRoaXMuZmlsZU9yaVVSTDsNCiAgICAgICAgICAgICAgdGhpcy4kTm90aWNlLmVycm9yKHsNCiAgICAgICAgICAgICAgICB0aXRsZTogdGhpcy4kdCgiTG9jYWxlU3RyaW5nLkUwMDAzNyIpLA0KICAgICAgICAgICAgICAgIGRlc2M6IHRoaXMuJHQoIkxvY2FsZVN0cmluZy5XMzAwMDgiLCB7IDA6ICIxNSIgfSksDQogICAgICAgICAgICAgICAgZHVyYXRpb246IENvbmZpZy5lcnJvckR1cmF0aW9uDQogICAgICAgICAgICAgIH0pOw0KICAgICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgICAgdGhpcy5maWxlID0gZmlsZTsNCiAgICAgICAgICAgICAgdGhpcy5maWxlVVJMID0gYXVkaW9FbGVtZW50LnJlc3VsdDsNCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9LCAxMDApOw0KICAgICAgICB9DQogICAgICB9KTsNCiAgICB9LA0KICAgIGNoYW5nZURlZmF1bHRUYWJMaXN0KGl0ZW0pIHsNCiAgICAgIC8vIGNvbnNvbGUubG9nKCdTaXRlbTonK0pTT04uc3RyaW5naWZ5KGl0ZW0pKQ0KICAgICAgbGV0IGFyciA9IFtdOw0KICAgICAgaXRlbS5mb3JFYWNoKGVsZW1lbnQgPT4gew0KICAgICAgICBsZXQgdiA9IHRoaXMuc2hvd2luZ1RhYi5maW5kKHQgPT4gdC52YWx1ZSA9PSBlbGVtZW50KS5sYWJlbDsNCiAgICAgICAgaWYgKHYpIHsNCiAgICAgICAgICBhcnIucHVzaCh7DQogICAgICAgICAgICBsYWJlbDogdiwNCiAgICAgICAgICAgIHZhbHVlOiBlbGVtZW50DQogICAgICAgICAgfSk7DQogICAgICAgIH0NCiAgICAgIH0pOw0KICAgICAgdGhpcy5kZWZhdWx0VGFiTGlzdCA9IGFycjsNCiAgICB9LA0KICAgIGxvYWRMb2dvKCkgew0KICAgICAgbGV0IHBvY0dyb3VwUGFyYW1zID0gew0KICAgICAgICBpbmxpbmVjb3VudDogdHJ1ZSwNCiAgICAgICAgc2VhcmNoOiAiYWN0aXZlIGVxIHRydWUgYW5kIGNhdGVnb3J5IGVxIEBTTlNATG9nbyINCiAgICAgIH07DQogICAgICB0aGlzLiRzZXJ2aWNlLmdldFBPQ0dyb3Vwc0J5Q2F0ZWdvcnkuc2VuZChwb2NHcm91cFBhcmFtcykudGhlbihwb2NSZXMgPT4gew0KICAgICAgICBpZiAoDQogICAgICAgICAgcG9jUmVzLmNvdW50ID4gMCAmJg0KICAgICAgICAgIHBvY1Jlcy5yZXN1bHRzWzBdLmZpbGVzICYmDQogICAgICAgICAgcG9jUmVzLnJlc3VsdHNbMF0uZmlsZXMubGVuZ3RoID4gMA0KICAgICAgICApIHsNCiAgICAgICAgICB0aGlzLmltZ0ZpbGVVUkwgPSBwb2NSZXMucmVzdWx0c1swXS5maWxlc1swXS51cmw7DQogICAgICAgICAgdGhpcy5pbWdGaWxlT3JpVVJMID0gcG9jUmVzLnJlc3VsdHNbMF0uZmlsZXNbMF0udXJsOw0KICAgICAgICAgIHRoaXMuc2hvd0NsZWFuSWNvbiA9IHRydWU7DQogICAgICAgIH0NCiAgICAgIH0pOw0KICAgIH0sDQogICAgbG9hZFNvdW5kKCkgew0KICAgICAgbGV0IHBvY0dyb3VwUGFyYW1zID0gew0KICAgICAgICBpbmxpbmVjb3VudDogdHJ1ZSwNCiAgICAgICAgc2VhcmNoOiAiYWN0aXZlIGVxIHRydWUgYW5kIGNhdGVnb3J5IGVxIEBTTlNAU291bmQiDQogICAgICB9Ow0KICAgICAgdGhpcy4kc2VydmljZS5nZXRQT0NHcm91cHNCeUNhdGVnb3J5LnNlbmQocG9jR3JvdXBQYXJhbXMpLnRoZW4ocG9jUmVzID0+IHsNCiAgICAgICAgaWYgKA0KICAgICAgICAgIHBvY1Jlcy5jb3VudCA+IDAgJiYNCiAgICAgICAgICBwb2NSZXMucmVzdWx0c1swXS5maWxlcyAmJg0KICAgICAgICAgIHBvY1Jlcy5yZXN1bHRzWzBdLmZpbGVzLmxlbmd0aCA+IDANCiAgICAgICAgKSB7DQogICAgICAgICAgdGhpcy5maWxlVVJMID0gcG9jUmVzLnJlc3VsdHNbMF0uZmlsZXNbMF0udXJsOw0KICAgICAgICAgIHRoaXMuZmlsZU9yaVVSTCA9IHBvY1Jlcy5yZXN1bHRzWzBdLmZpbGVzWzBdLnVybDsNCiAgICAgICAgfQ0KICAgICAgfSk7DQogICAgfSwNCiAgICBsb2FkQ29uZmlnKCkgew0KICAgICAgLy9sZXQgbG9naW5Vc2VyTmFtZSA9IGxvY2FsU3RvcmFnZS5nZXRJdGVtKCJzbnNfbG9naW5Vc2VyIik7DQoNCiAgICAgIGxldCBwYXJhbXMgPSB7DQogICAgICAgIGlubGluZWNvdW50OiB0cnVlLA0KICAgICAgICBzZWFyY2g6ICJjYXRlZ29yeSBlcSBAU05TQFNldHRpbmciDQogICAgICB9Ow0KDQogICAgICBsZXQgcGxhbmVTdHIgPSAiYWN0aXZlIGVxIHRydWUgYW5kIGVuYWJsZSBlcSB0cnVlIjsNCiAgICAgIGxldCBwbGFuZVBhcmFtcyA9IHsNCiAgICAgICAgc2VhcmNoOiBwbGFuZVN0ciwNCiAgICAgICAgaW5saW5lY291bnQ6IHRydWUsDQogICAgICAgIC8vIHBhZ2U6IDAsDQogICAgICAgIC8vIHNpemU6IDEsDQogICAgICAgIHNvcnQ6ICJtb2RpZmllc0F0LGRlc2MiDQogICAgICAgIC8vIGFjdGl2ZTogdHJ1ZSwNCiAgICAgIH07DQoNCiAgICAgIHRoaXMuJHNlcnZpY2UuZ2V0UGxhbmVzLnNlbmQocGxhbmVQYXJhbXMpLnRoZW4ocGxhbmVEYXRhID0+IHsNCiAgICAgICAgaWYgKHBsYW5lRGF0YS5yZXN1bHRzLmxlbmd0aCA+IDApIHsNCiAgICAgICAgICB0aGlzLnBsYW5lTGlzdCA9IHBsYW5lRGF0YS5yZXN1bHRzLm1hcChpdGVtID0+ICh7DQogICAgICAgICAgICB2YWx1ZTogaXRlbS5jb2RlLA0KICAgICAgICAgICAgbGFiZWw6IGl0ZW0ubmFtZQ0KICAgICAgICAgIH0pKTsNCiAgICAgICAgfQ0KICAgICAgICB0aGlzLiRzZXJ2aWNlLmdldFBPQ1Byb3BlcnRpZXMuc2VuZChwYXJhbXMpLnRoZW4ocmVzID0+IHsNCiAgICAgICAgICBpZiAocmVzLmNvdW50ID4gMCAmJiByZXMucmVzdWx0c1swXS5wcm9wZXJ0aWVzLmxlbmd0aCA+IDApIHsNCiAgICAgICAgICAgIHJlcy5yZXN1bHRzWzBdLnByb3BlcnRpZXMuZm9yRWFjaChyZXMgPT4gew0KICAgICAgICAgICAgICBzd2l0Y2ggKHJlcy5rZXkpIHsNCiAgICAgICAgICAgICAgICBjYXNlICJsaWNlbnNlIjoNCiAgICAgICAgICAgICAgICAgIHRoaXMuY29uZmlnRGF0YS5saWNlbnNlID0NCiAgICAgICAgICAgICAgICAgICAgcmVzLnZhbHVlICE9ICIiID8gcmVzLnZhbHVlLnNwbGl0KCIsIikgOiBbXTsNCiAgICAgICAgICAgICAgICAgIGJyZWFrOw0KICAgICAgICAgICAgICAgIGNhc2UgInN5c3RlbU5hbWUiOg0KICAgICAgICAgICAgICAgICAgdGhpcy5jb25maWdEYXRhLnN5c3RlbU5hbWUgPSByZXMudmFsdWU7DQogICAgICAgICAgICAgICAgICBicmVhazsNCiAgICAgICAgICAgICAgICBjYXNlICJ0YWJMaXN0IjoNCiAgICAgICAgICAgICAgICAgIHRoaXMuY29uZmlnRGF0YS50YWJMaXN0ID0gcmVzLnZhbHVlLnNwbGl0KCIsIik7DQogICAgICAgICAgICAgICAgICBsZXQgYXJyID0gW107DQogICAgICAgICAgICAgICAgICB0aGlzLmNvbmZpZ0RhdGEudGFiTGlzdC5mb3JFYWNoKGVsZW1lbnQgPT4gew0KICAgICAgICAgICAgICAgICAgICBsZXQgdiA9IHRoaXMuc2hvd2luZ1RhYi5maW5kKHQgPT4gdC52YWx1ZSA9PSBlbGVtZW50KS5sYWJlbDsNCiAgICAgICAgICAgICAgICAgICAgaWYgKHYpIHsNCiAgICAgICAgICAgICAgICAgICAgICBhcnIucHVzaCh7DQogICAgICAgICAgICAgICAgICAgICAgICBsYWJlbDogdiwNCiAgICAgICAgICAgICAgICAgICAgICAgIHZhbHVlOiBlbGVtZW50DQogICAgICAgICAgICAgICAgICAgICAgfSk7DQogICAgICAgICAgICAgICAgICAgIH0NCiAgICAgICAgICAgICAgICAgIH0pOw0KICAgICAgICAgICAgICAgICAgdGhpcy5kZWZhdWx0VGFiTGlzdCA9IGFycjsNCiAgICAgICAgICAgICAgICAgIGJyZWFrOw0KICAgICAgICAgICAgICAgIC8vIGNhc2UgInNvdW5kIjoNCiAgICAgICAgICAgICAgICAvLyAgIGJyZWFrOw0KICAgICAgICAgICAgICAgIGNhc2UgIm0yTGlzdCI6DQogICAgICAgICAgICAgICAgICB0aGlzLmNvbmZpZ0RhdGEubTJMaXN0ID0gcmVzLnZhbHVlLnNwbGl0KCIsIik7DQogICAgICAgICAgICAgICAgICBicmVhazsNCiAgICAgICAgICAgICAgICBjYXNlICJpY29uU2l6ZSI6DQogICAgICAgICAgICAgICAgICB0aGlzLmNvbmZpZ0RhdGEuaWNvblNpemUgPSByZXMudmFsdWU7DQogICAgICAgICAgICAgICAgICBicmVhazsNCiAgICAgICAgICAgICAgICBjYXNlICJkZWZhdWx0VGFiIjoNCiAgICAgICAgICAgICAgICAgIHRoaXMuY29uZmlnRGF0YS5kZWZhdWx0VGFiID0gcmVzLnZhbHVlOw0KICAgICAgICAgICAgICAgICAgdGhpcy5vcmlUYWIgPSByZXMudmFsdWU7DQogICAgICAgICAgICAgICAgICBicmVhazsNCiAgICAgICAgICAgICAgICBjYXNlICJ1cGRhdGVTZWNvbmRzIjoNCiAgICAgICAgICAgICAgICAgIHRoaXMuY29uZmlnRGF0YS51cGRhdGVTZWNvbmRzID0gcmVzLnZhbHVlOw0KICAgICAgICAgICAgICAgICAgYnJlYWs7DQogICAgICAgICAgICAgICAgY2FzZSAiZ3JheVRpbWUiOg0KICAgICAgICAgICAgICAgICAgdGhpcy5jb25maWdEYXRhLmdyYXlUaW1lID0gcmVzLnZhbHVlICogMTsNCiAgICAgICAgICAgICAgICAgIGJyZWFrOw0KICAgICAgICAgICAgICAgIGNhc2UgIkV2ZW50QXV0aENsb3NlIjoNCiAgICAgICAgICAgICAgICAgIHRoaXMuY29uZmlnRGF0YS5ldmVudEF1dGhDbG9zZSA9DQogICAgICAgICAgICAgICAgICAgIHJlcy52YWx1ZSA9PSAidHJ1ZSIgPyB0cnVlIDogZmFsc2U7DQogICAgICAgICAgICAgICAgICBicmVhazsNCiAgICAgICAgICAgICAgICBjYXNlICJFdmVudEtlZXBEYXlzIjoNCiAgICAgICAgICAgICAgICAgIHRoaXMuY29uZmlnRGF0YS5ldmVudEtlZXBEYXlzID0gcmVzLnZhbHVlICogMTsNCiAgICAgICAgICAgICAgICAgIGJyZWFrOw0KICAgICAgICAgICAgICAgIGNhc2UgIkluZmx1eFJhbmdlIjoNCiAgICAgICAgICAgICAgICAgIHRoaXMuY29uZmlnRGF0YS5pbmZsdXhSYW5nZSA9IHJlcy52YWx1ZSAqIDE7DQogICAgICAgICAgICAgICAgICBicmVhazsNCiAgICAgICAgICAgICAgICBjYXNlICJsb3NzU2lnbmFsIjoNCiAgICAgICAgICAgICAgICAgIHRoaXMuY29uZmlnRGF0YS5sb3NzU2lnbmFsID0gcmVzLnZhbHVlICogMTsNCiAgICAgICAgICAgICAgICAgIGJyZWFrOw0KICAgICAgICAgICAgICAgIGNhc2UgImxvc3NTaWduYWwyIjoNCiAgICAgICAgICAgICAgICAgIHRoaXMuY29uZmlnRGF0YS5sb3NzU2lnbmFsMiA9IHJlcy52YWx1ZSAqIDE7DQogICAgICAgICAgICAgICAgICBicmVhazsNCiAgICAgICAgICAgICAgICBjYXNlICJsb3dCYXR0ZXJ5IjoNCiAgICAgICAgICAgICAgICAgIHRoaXMuY29uZmlnRGF0YS5sb3dCYXR0ZXJ5ID0gcmVzLnZhbHVlICogMTsNCiAgICAgICAgICAgICAgICAgIGJyZWFrOw0KICAgICAgICAgICAgICAgIGNhc2UgImxvc3NTaWduYWxEZXZpY2VzIjoNCiAgICAgICAgICAgICAgICAgIHRoaXMuY29uZmlnRGF0YS5sb3NzU2lnbmFsRGV2aWNlcyA9IHJlcy52YWx1ZS5zcGxpdCgiLCIpOw0KICAgICAgICAgICAgICAgICAgYnJlYWs7DQogICAgICAgICAgICAgICAgY2FzZSAibG9zc1NpZ25hbERldmljZXMyIjoNCiAgICAgICAgICAgICAgICAgIHRoaXMuY29uZmlnRGF0YS5sb3NzU2lnbmFsRGV2aWNlczIgPSByZXMudmFsdWUuc3BsaXQoIiwiKTsNCiAgICAgICAgICAgICAgICAgIGJyZWFrOw0KICAgICAgICAgICAgICAgIGNhc2UgImxvd0JhdHRlcnlEZXZpY2VzIjoNCiAgICAgICAgICAgICAgICAgIHRoaXMuY29uZmlnRGF0YS5sb3dCYXR0ZXJ5RGV2aWNlcyA9IHJlcy52YWx1ZS5zcGxpdCgiLCIpOw0KICAgICAgICAgICAgICAgICAgYnJlYWs7DQogICAgICAgICAgICAgICAgY2FzZSAiYWJub3JtYWxEZXZpY2VzIjoNCiAgICAgICAgICAgICAgICAgIHRoaXMuY29uZmlnRGF0YS5hYm5vcm1hbERldmljZXMgPSByZXMudmFsdWUuc3BsaXQoIiwiKTsNCiAgICAgICAgICAgICAgICAgIGJyZWFrOw0KICAgICAgICAgICAgICAgIGNhc2UgInNraXBFdmVudCI6DQogICAgICAgICAgICAgICAgICB0aGlzLmNvbmZpZ0RhdGEuc2tpcEV2ZW50ID0gcmVzLnZhbHVlLnNwbGl0KCIsIik7DQogICAgICAgICAgICAgICAgICBicmVhazsNCiAgICAgICAgICAgICAgICBjYXNlICJkZXZpY2VTZWxlY3RMaXN0IjoNCiAgICAgICAgICAgICAgICAgIHRoaXMuY29uZmlnRGF0YS5kZXZpY2VTZWxlY3RMaXN0ID0gcmVzLnZhbHVlLnNwbGl0KCIsIik7DQogICAgICAgICAgICAgICAgICBicmVhazsNCiAgICAgICAgICAgICAgICBjYXNlICJtdWx0aVBsYW5lcyI6DQogICAgICAgICAgICAgICAgICB0aGlzLmNvbmZpZ0RhdGEubXVsdGlQbGFuZXMgPQ0KICAgICAgICAgICAgICAgICAgICByZXMudmFsdWUgPT0gInRydWUiID8gdHJ1ZSA6IGZhbHNlOw0KICAgICAgICAgICAgICAgICAgYnJlYWs7DQogICAgICAgICAgICAgICAgY2FzZSAic2VsZWN0ZWRQbGFuZXMiOg0KICAgICAgICAgICAgICAgICAgdGhpcy5jb25maWdEYXRhLnNlbGVjdGVkUGxhbmVzID0gcmVzLnZhbHVlLnNwbGl0KCIsIik7DQogICAgICAgICAgICAgICAgICBicmVhazsNCiAgICAgICAgICAgICAgICBjYXNlICJzdHVja1BsYW5lIjoNCiAgICAgICAgICAgICAgICAgIHRoaXMuY29uZmlnRGF0YS5zdHVja1BsYW5lID0NCiAgICAgICAgICAgICAgICAgICAgcmVzLnZhbHVlID09ICJ0cnVlIiA/IHRydWUgOiBmYWxzZTsNCiAgICAgICAgICAgICAgICAgIGJyZWFrOw0KICAgICAgICAgICAgICAgIGNhc2UgIm0xTG9uZ1ByZXNzIjoNCiAgICAgICAgICAgICAgICAgIHRoaXMuY29uZmlnRGF0YS5tMUxvbmdQcmVzcyA9IHJlcy52YWx1ZS5zcGxpdCgiLCIpOw0KICAgICAgICAgICAgICAgICAgYnJlYWs7DQogICAgICAgICAgICAgICAgY2FzZSAiZGVmYXVsdFBhZ2VTaXplIjoNCiAgICAgICAgICAgICAgICAgIHRoaXMuY29uZmlnRGF0YS5kZWZhdWx0UGFnZVNpemUgPSByZXMudmFsdWU7DQogICAgICAgICAgICAgICAgICBicmVhazsNCiAgICAgICAgICAgICAgICBjYXNlICJtNU9iamVjdFR5cGUiOg0KICAgICAgICAgICAgICAgICAgdGhpcy5jb25maWdEYXRhLm01T2JqZWN0VHlwZSA9DQogICAgICAgICAgICAgICAgICAgIHJlcy52YWx1ZSA9PSAiIiA/ICJhbGwiIDogcmVzLnZhbHVlOw0KICAgICAgICAgICAgICAgICAgYnJlYWs7DQogICAgICAgICAgICAgICAgY2FzZSAic2hvd0xlYXZlQmVkIjoNCiAgICAgICAgICAgICAgICAgIHRoaXMuY29uZmlnRGF0YS5zaG93TGVhdmVCZWQgPQ0KICAgICAgICAgICAgICAgICAgICByZXMudmFsdWUgPT09ICJ0cnVlIiA/IHRydWUgOiBmYWxzZTsNCiAgICAgICAgICAgICAgICAgIGJyZWFrOw0KICAgICAgICAgICAgICAgIGNhc2UgIm0yU3RhdGlzdGljIjoNCiAgICAgICAgICAgICAgICAgIHRoaXMuY29uZmlnRGF0YS5tMlN0YXRpc3RpYyA9IHJlcy52YWx1ZSAqIDE7DQogICAgICAgICAgICAgICAgICBicmVhazsNCiAgICAgICAgICAgICAgICBjYXNlICJtM1N0YXRpc3RpYyI6DQogICAgICAgICAgICAgICAgICB0aGlzLmNvbmZpZ0RhdGEubTNTdGF0aXN0aWMgPSByZXMudmFsdWUgKiAxOw0KICAgICAgICAgICAgICAgICAgYnJlYWs7DQogICAgICAgICAgICAgICAgY2FzZSAibTRTdGF0aXN0aWMiOg0KICAgICAgICAgICAgICAgICAgdGhpcy5jb25maWdEYXRhLm00U3RhdGlzdGljID0gcmVzLnZhbHVlICogMTsNCiAgICAgICAgICAgICAgICAgIGJyZWFrOw0KICAgICAgICAgICAgICAgIGNhc2UgIm04U3RhdGlzdGljIjoNCiAgICAgICAgICAgICAgICAgIHRoaXMuY29uZmlnRGF0YS5tOFN0YXRpc3RpYyA9IHJlcy52YWx1ZSAqIDE7DQogICAgICAgICAgICAgICAgICBicmVhazsNCiAgICAgICAgICAgICAgICBjYXNlICJtOVN0YXRpc3RpYyI6DQogICAgICAgICAgICAgICAgICB0aGlzLmNvbmZpZ0RhdGEubTlTdGF0aXN0aWMgPSByZXMudmFsdWUgKiAxOw0KICAgICAgICAgICAgICAgICAgYnJlYWs7DQogICAgICAgICAgICAgICAgY2FzZSAic3RhdGVDb2xvciI6DQogICAgICAgICAgICAgICAgICB0aGlzLmNvbmZpZ0RhdGEuc3RhdGVDb2xvciA9IHJlcy52YWx1ZTsNCiAgICAgICAgICAgICAgICAgIGJyZWFrOw0KICAgICAgICAgICAgICAgIGNhc2UgInJlcGVhdFNvdW5kIjoNCiAgICAgICAgICAgICAgICAgIHRoaXMuY29uZmlnRGF0YS5yZXBlYXRTb3VuZCA9IHJlcy52YWx1ZSAqIDE7DQogICAgICAgICAgICAgICAgICBicmVhazsNCiAgICAgICAgICAgICAgICBjYXNlICJsb2dvdXRUaW1lIjoNCiAgICAgICAgICAgICAgICAgIHRoaXMuY29uZmlnRGF0YS5sb2dvdXRUaW1lID0gcmVzLnZhbHVlICogMTsNCiAgICAgICAgICAgICAgICAgIGJyZWFrOw0KICAgICAgICAgICAgICB9DQogICAgICAgICAgICB9KTsNCiAgICAgICAgICAgIHRoaXMuY29uZmlnRGF0YS5zb3VuZCA9DQogICAgICAgICAgICAgIGxvY2FsU3RvcmFnZS5nZXRJdGVtKCJzbnNfYWxlcnRTb3VuZCIpID09ICJ0cnVlIiA/IHRydWUgOiBmYWxzZTsNCiAgICAgICAgICAgIHRoaXMuY29uZmlnRGF0YS5tMkxpbmUgPQ0KICAgICAgICAgICAgICBsb2NhbFN0b3JhZ2UuZ2V0SXRlbSgic25zX20yTGluZSIpID09ICJ0cnVlIiA/IHRydWUgOiBmYWxzZTsNCiAgICAgICAgICAgIC8vDQoNCiAgICAgICAgICAgIHRoaXMub3JpZ0NvbmZpZ0RhdGEgPSBKU09OLnBhcnNlKEpTT04uc3RyaW5naWZ5KHRoaXMuY29uZmlnRGF0YSkpOw0KICAgICAgICAgICAgdGhpcy51cGRhdGVWYWx1ZSh0aGlzLm9yaWdDb25maWdEYXRhKTsNCiAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgdGd1czsNCiAgICAgICAgICAgIHRoaXMuZWRpdEhhbmRsZVN1Ym1pdE5ldygibmV3Iik7DQogICAgICAgICAgfQ0KICAgICAgICB9KTsNCiAgICAgIH0pOw0KICAgIH0sDQogICAgbG9hZFRoaXJkUGFydHkoKSB7DQogICAgICAvL2xldCBsb2dpblVzZXJOYW1lID0gbG9jYWxTdG9yYWdlLmdldEl0ZW0oInNuc19sb2dpblVzZXIiKTsNCg0KICAgICAgbGV0IHBhcmFtcyA9IHsNCiAgICAgICAgaW5saW5lY291bnQ6IHRydWUsDQogICAgICAgIHNlYXJjaDogImNhdGVnb3J5IGVxIEBTTlNAU2V0dGluZ1RoaXJkUGFydHkiDQogICAgICB9Ow0KDQogICAgICB0aGlzLiRzZXJ2aWNlLmdldFBPQ1Byb3BlcnRpZXMuc2VuZChwYXJhbXMpLnRoZW4ocmVzID0+IHsNCiAgICAgICAgaWYgKHJlcy5jb3VudCA+IDAgJiYgcmVzLnJlc3VsdHNbMF0ucHJvcGVydGllcy5sZW5ndGggPiAwKSB7DQogICAgICAgICAgcmVzLnJlc3VsdHNbMF0ucHJvcGVydGllcy5mb3JFYWNoKHJlcyA9PiB7DQogICAgICAgICAgICBzd2l0Y2ggKHJlcy5rZXkpIHsNCiAgICAgICAgICAgICAgY2FzZSAibG9naW5UaGlyZFBhcnR5IjoNCiAgICAgICAgICAgICAgICB0aGlzLnRoaXJkUGFydHlEYXRhLmxvZ2luVGhpcmRQYXJ0eSA9DQogICAgICAgICAgICAgICAgICByZXMudmFsdWUgPT0gInRydWUiID8gdHJ1ZSA6IGZhbHNlOw0KICAgICAgICAgICAgICAgIGJyZWFrOw0KICAgICAgICAgICAgICBjYXNlICJ1cmwiOg0KICAgICAgICAgICAgICAgIHRoaXMudGhpcmRQYXJ0eURhdGEudXJsID0gcmVzLnZhbHVlOw0KICAgICAgICAgICAgICAgIGJyZWFrOw0KICAgICAgICAgICAgICBjYXNlICJ1c2VyTmFtZSI6DQogICAgICAgICAgICAgICAgdGhpcy50aGlyZFBhcnR5RGF0YS51c2VyTmFtZSA9IHJlcy52YWx1ZTsNCiAgICAgICAgICAgICAgICBicmVhazsNCiAgICAgICAgICAgICAgY2FzZSAic2VjcmV0IjoNCiAgICAgICAgICAgICAgICB0aGlzLnRoaXJkUGFydHlEYXRhLnNlY3JldCA9IHJlcy52YWx1ZTsNCiAgICAgICAgICAgICAgICBicmVhazsNCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9KTsNCiAgICAgICAgICB0aGlzLm9yaWdUaGlyZFBhcnR5RGF0YSA9IEpTT04ucGFyc2UoDQogICAgICAgICAgICBKU09OLnN0cmluZ2lmeSh0aGlzLnRoaXJkUGFydHlEYXRhKQ0KICAgICAgICAgICk7DQogICAgICAgICAgdGhpcy51cGRhdGVUaGlyZFBhcnR5VmFsdWUodGhpcy5vcmlnVGhpcmRQYXJ0eURhdGEpOw0KICAgICAgICB9DQogICAgICB9KTsNCiAgICB9LA0KICAgIGNoZWNrRWRpdEhhbmRsZVN1Ym1pdE5ldygpIHsNCiAgICAgIC8vDQogICAgICBsZXQgZXJyb3JEZXNjID0gIiI7DQogICAgICBsZXQgdGFiTGlzdCA9IHRoaXMuY29uZmlnRGF0YS50YWJMaXN0Ow0KICAgICAgbGV0IG0yTGlzdCA9IHRoaXMuY29uZmlnRGF0YS5tMkxpc3Q7DQogICAgICBsZXQgZGVmYXVsdFRhYiA9IHRoaXMuY29uZmlnRGF0YS5kZWZhdWx0VGFiOw0KICAgICAgbGV0IGdyYXlUaW1lID0gdGhpcy5jb25maWdEYXRhLmdyYXlUaW1lOw0KICAgICAgbGV0IGV2ZW50QXV0aENsb3NlID0gdGhpcy5jb25maWdEYXRhLmV2ZW50QXV0aENsb3NlOw0KICAgICAgbGV0IGV2ZW50S2VlcERheXMgPSB0aGlzLmNvbmZpZ0RhdGEuZXZlbnRLZWVwRGF5czsNCiAgICAgIGxldCBsb3dCYXR0ZXJ5ID0gdGhpcy5jb25maWdEYXRhLmxvd0JhdHRlcnk7DQogICAgICBsZXQgbG9zc1NpZ25hbCA9IHRoaXMuY29uZmlnRGF0YS5sb3NzU2lnbmFsOw0KICAgICAgbGV0IGxvc3NTaWduYWwyID0gdGhpcy5jb25maWdEYXRhLmxvc3NTaWduYWwyOw0KICAgICAgbGV0IGxvd0JhdHRlcnlEZXZpY2VzID0gdGhpcy5jb25maWdEYXRhLmxvd0JhdHRlcnlEZXZpY2VzOw0KICAgICAgbGV0IGxvc3NTaWduYWxEZXZpY2VzID0gdGhpcy5jb25maWdEYXRhLmxvc3NTaWduYWxEZXZpY2VzOw0KICAgICAgbGV0IGxvc3NTaWduYWxEZXZpY2VzMiA9IHRoaXMuY29uZmlnRGF0YS5sb3NzU2lnbmFsRGV2aWNlczI7DQogICAgICBsZXQgYWJub3JtYWxEZXZpY2VzID0gdGhpcy5jb25maWdEYXRhLmFibm9ybWFsRGV2aWNlczsNCiAgICAgIGxldCBza2lwRXZlbnQgPSB0aGlzLmNvbmZpZ0RhdGEuc2tpcEV2ZW50Ow0KICAgICAgbGV0IGRldmljZVNlbGVjdExpc3QgPSB0aGlzLmNvbmZpZ0RhdGEuZGV2aWNlU2VsZWN0TGlzdDsNCiAgICAgIGxldCBtdWx0aVBsYW5lcyA9IHRoaXMuY29uZmlnRGF0YS5tdWx0aVBsYW5lczsNCiAgICAgIGxldCBzZWxlY3RlZFBsYW5lcyA9IHRoaXMuY29uZmlnRGF0YS5zZWxlY3RlZFBsYW5lczsNCiAgICAgIGxldCBzdHVja1BsYW5lID0gdGhpcy5jb25maWdEYXRhLnN0dWNrUGxhbmU7DQogICAgICBsZXQgbTFMb2duUHJlc3MgPSB0aGlzLmNvbmZpZ0RhdGEubTFMb25nUHJlc3M7DQogICAgICBsZXQgc2hvd0xlYXZlQmVkID0gdGhpcy5jb25maWdEYXRhLnNob3dMZWF2ZUJlZDsNCiAgICAgIGxldCBtMlN0YXRpc3RpYyA9IHRoaXMuY29uZmlnRGF0YS5tMlN0YXRpc3RpYzsNCiAgICAgIGxldCBtM1N0YXRpc3RpYyA9IHRoaXMuY29uZmlnRGF0YS5tM1N0YXRpc3RpYzsNCiAgICAgIGxldCBtNFN0YXRpc3RpYyA9IHRoaXMuY29uZmlnRGF0YS5tNFN0YXRpc3RpYzsNCiAgICAgIGxldCBtOFN0YXRpc3RpYyA9IHRoaXMuY29uZmlnRGF0YS5tOFN0YXRpc3RpYzsNCiAgICAgIGxldCBtOVN0YXRpc3RpYyA9IHRoaXMuY29uZmlnRGF0YS5tOVN0YXRpc3RpYzsNCiAgICAgIGxldCBzdGF0ZUNvbG9yID0gdGhpcy5jb25maWdEYXRhLnN0YXRlQ29sb3I7DQogICAgICBsZXQgbG9nb3V0VGltZSA9IHRoaXMuY29uZmlnRGF0YS5sb2dvdXRUaW1lOw0KICAgICAgLy8gbGV0IGljb25TaXplID0gdGhpcy5jb25maWdEYXRhLmljb25TaXplDQogICAgICBpZiAodGFiTGlzdC5sZW5ndGggPCAxKSB7DQogICAgICAgIGVycm9yRGVzYyArPQ0KICAgICAgICAgIHRoaXMuJHQoIkxvY2FsZVN0cmluZy5NMDAwMTAiLCB7DQogICAgICAgICAgICAwOiB0aGlzLiR0KCJMb2NhbGVTdHJpbmcuTDMwMDU3IikNCiAgICAgICAgICB9KSArICI8YnI+IjsNCiAgICAgIH0NCiAgICAgIGlmIChtMkxpc3QubGVuZ3RoIDwgMSkgew0KICAgICAgICBlcnJvckRlc2MgKz0NCiAgICAgICAgICB0aGlzLiR0KCJMb2NhbGVTdHJpbmcuTTAwMDEwIiwgew0KICAgICAgICAgICAgMDogdGhpcy4kdCgiTG9jYWxlU3RyaW5nLkwzMDA1NiIpDQogICAgICAgICAgfSkgKyAiPGJyPiI7DQogICAgICB9DQogICAgICBpZiAobTJMaXN0Lmxlbmd0aCA+IDQpIHsNCiAgICAgICAgZXJyb3JEZXNjICs9IHRoaXMuJHQoIkxvY2FsZVN0cmluZy5XMzAwMDIiKSArICI8YnI+IjsNCiAgICAgIH0NCiAgICAgIGlmICghZGVmYXVsdFRhYikgew0KICAgICAgICBlcnJvckRlc2MgKz0NCiAgICAgICAgICB0aGlzLiR0KCJMb2NhbGVTdHJpbmcuTTAwMDEwIiwgew0KICAgICAgICAgICAgMDogdGhpcy4kdCgiTG9jYWxlU3RyaW5nLkwzMDA2MCIpDQogICAgICAgICAgfSkgKyAiPGJyPiI7DQogICAgICB9DQogICAgICBpZiAoZ3JheVRpbWUgPT0gbnVsbCkgew0KICAgICAgICBlcnJvckRlc2MgKz0NCiAgICAgICAgICB0aGlzLiR0KCJMb2NhbGVTdHJpbmcuTDAwMDAxIiwgew0KICAgICAgICAgICAgMDogdGhpcy4kdCgiTG9jYWxlU3RyaW5nLkwzMDA2MiIpDQogICAgICAgICAgfSkgKyAiPGJyPiI7DQogICAgICB9DQogICAgICBpZiAoZXZlbnRBdXRoQ2xvc2UgPT0gdHJ1ZSkgew0KICAgICAgICBpZiAoZXZlbnRLZWVwRGF5cyA9PSBudWxsKSB7DQogICAgICAgICAgZXJyb3JEZXNjICs9DQogICAgICAgICAgICB0aGlzLiR0KCJMb2NhbGVTdHJpbmcuTDAwMDAxIiwgew0KICAgICAgICAgICAgICAwOiB0aGlzLiR0KCJMb2NhbGVTdHJpbmcuTDMwMDgzIikNCiAgICAgICAgICAgIH0pICsgIjxicj4iOw0KICAgICAgICB9DQogICAgICB9DQogICAgICBpZiAobG93QmF0dGVyeSA9PSBudWxsKSB7DQogICAgICAgIGVycm9yRGVzYyArPQ0KICAgICAgICAgIHRoaXMuJHQoIkxvY2FsZVN0cmluZy5MMDAwMDEiLCB7DQogICAgICAgICAgICAwOiB0aGlzLiR0KCJMb2NhbGVTdHJpbmcuTDMwMTA0IikNCiAgICAgICAgICB9KSArICI8YnI+IjsNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIGlmIChsb3dCYXR0ZXJ5IDwgMTApIHsNCiAgICAgICAgICBlcnJvckRlc2MgKz0NCiAgICAgICAgICAgIHRoaXMuJHQoIkxvY2FsZVN0cmluZy5MMzAxMDQiKSArDQogICAgICAgICAgICB0aGlzLiR0KCJMb2NhbGVTdHJpbmcuVzMwMDA5Iiwgew0KICAgICAgICAgICAgICAwOiAxMCwNCiAgICAgICAgICAgICAgMTogNjANCiAgICAgICAgICAgIH0pICsNCiAgICAgICAgICAgICI8YnI+IjsNCiAgICAgICAgfQ0KICAgICAgfQ0KICAgICAgaWYgKGxvc3NTaWduYWwgPT0gbnVsbCkgew0KICAgICAgICBlcnJvckRlc2MgKz0NCiAgICAgICAgICB0aGlzLiR0KCJMb2NhbGVTdHJpbmcuTDAwMDAxIiwgew0KICAgICAgICAgICAgMDogdGhpcy4kdCgiTG9jYWxlU3RyaW5nLkwzMDEwMyIpDQogICAgICAgICAgfSkgKyAiPGJyPiI7DQogICAgICB9IGVsc2Ugew0KICAgICAgICBpZiAobG9zc1NpZ25hbCA8IDEpIHsNCiAgICAgICAgICBlcnJvckRlc2MgKz0NCiAgICAgICAgICAgIHRoaXMuJHQoIkxvY2FsZVN0cmluZy5MMzAxMDMiKSArDQogICAgICAgICAgICB0aGlzLiR0KCJMb2NhbGVTdHJpbmcuVzMwMDA5Iiwgew0KICAgICAgICAgICAgICAwOiAxLA0KICAgICAgICAgICAgICAxOiA2MA0KICAgICAgICAgICAgfSkgKw0KICAgICAgICAgICAgIjxicj4iOw0KICAgICAgICB9DQogICAgICB9DQogICAgICBpZiAoZGV2aWNlU2VsZWN0TGlzdC5sZW5ndGggPCAxKSB7DQogICAgICAgIGVycm9yRGVzYyArPQ0KICAgICAgICAgIHRoaXMuJHQoIkxvY2FsZVN0cmluZy5NMDAwMTAiLCB7DQogICAgICAgICAgICAwOiB0aGlzLiR0KCJMb2NhbGVTdHJpbmcuTDMwMTI5IikNCiAgICAgICAgICB9KSArICI8YnI+IjsNCiAgICAgIH0NCiAgICAgIGlmICgNCiAgICAgICAgbXVsdGlQbGFuZXMgJiYNCiAgICAgICAgKHNlbGVjdGVkUGxhbmVzLmxlbmd0aCA8IDEgfHwNCiAgICAgICAgICAoc2VsZWN0ZWRQbGFuZXMubGVuZ3RoID09IDEgJiYgc2VsZWN0ZWRQbGFuZXNbMF0gPT0gIiIpKQ0KICAgICAgKSB7DQogICAgICAgIGVycm9yRGVzYyArPQ0KICAgICAgICAgIHRoaXMuJHQoIkxvY2FsZVN0cmluZy5NMDAwMTAiLCB7DQogICAgICAgICAgICAwOiB0aGlzLiR0KCJMb2NhbGVTdHJpbmcuTDMwMTM4IikNCiAgICAgICAgICB9KSArICI8YnI+IjsNCiAgICAgIH0NCg0KICAgICAgaWYgKG0yU3RhdGlzdGljID09IG51bGwpIHsNCiAgICAgICAgZXJyb3JEZXNjICs9DQogICAgICAgICAgdGhpcy4kdCgiTG9jYWxlU3RyaW5nLkwwMDAwMSIsIHsNCiAgICAgICAgICAgIDA6IHRoaXMuJHQoIkxvY2FsZVN0cmluZy5MMzAxODIiKQ0KICAgICAgICAgIH0pICsgIjxicj4iOw0KICAgICAgfQ0KICAgICAgaWYgKG0zU3RhdGlzdGljID09IG51bGwpIHsNCiAgICAgICAgZXJyb3JEZXNjICs9DQogICAgICAgICAgdGhpcy4kdCgiTG9jYWxlU3RyaW5nLkwwMDAwMSIsIHsNCiAgICAgICAgICAgIDA6IHRoaXMuJHQoIkxvY2FsZVN0cmluZy5MMzAxODMiKQ0KICAgICAgICAgIH0pICsgIjxicj4iOw0KICAgICAgfQ0KICAgICAgaWYgKG00U3RhdGlzdGljID09IG51bGwpIHsNCiAgICAgICAgZXJyb3JEZXNjICs9DQogICAgICAgICAgdGhpcy4kdCgiTG9jYWxlU3RyaW5nLkwwMDAwMSIsIHsNCiAgICAgICAgICAgIDA6IHRoaXMuJHQoIkxvY2FsZVN0cmluZy5MMzAxODQiKQ0KICAgICAgICAgIH0pICsgIjxicj4iOw0KICAgICAgfQ0KICAgICAgaWYgKG04U3RhdGlzdGljID09IG51bGwpIHsNCiAgICAgICAgZXJyb3JEZXNjICs9DQogICAgICAgICAgdGhpcy4kdCgiTG9jYWxlU3RyaW5nLkwwMDAwMSIsIHsNCiAgICAgICAgICAgIDA6IHRoaXMuJHQoIkxvY2FsZVN0cmluZy5MMzAxODUiKQ0KICAgICAgICAgIH0pICsgIjxicj4iOw0KICAgICAgfQ0KICAgICAgaWYgKG05U3RhdGlzdGljID09IG51bGwpIHsNCiAgICAgICAgZXJyb3JEZXNjICs9DQogICAgICAgICAgdGhpcy4kdCgiTG9jYWxlU3RyaW5nLkwwMDAwMSIsIHsNCiAgICAgICAgICAgIDA6IHRoaXMuJHQoIkxvY2FsZVN0cmluZy5MMzAxODYiKQ0KICAgICAgICAgIH0pICsgIjxicj4iOw0KICAgICAgfQ0KICAgICAgaWYgKHN0YXRlQ29sb3IgPT0gIiIpIHsNCiAgICAgICAgZXJyb3JEZXNjICs9IHRoaXMuJHQoIkxvY2FsZVN0cmluZy5XMzAwMTkiKSArICI8YnI+IjsNCiAgICAgIH0NCiAgICAgIGlmIChsb2dvdXRUaW1lID09IG51bGwpIHsNCiAgICAgICAgZXJyb3JEZXNjICs9DQogICAgICAgICAgdGhpcy4kdCgiTG9jYWxlU3RyaW5nLkwwMDAwMSIsIHsNCiAgICAgICAgICAgIDA6IHRoaXMuJHQoIkxvY2FsZVN0cmluZy5MMDA0NDYiKQ0KICAgICAgICAgIH0pICsgIjxicj4iOw0KICAgICAgfQ0KDQogICAgICB0aGlzLmNhbWVyYUNvbmZpZ0xpc3QuZm9yRWFjaChpdGVtID0+IHsNCiAgICAgICAgaWYgKA0KICAgICAgICAgIGl0ZW0uY2FwdHVyZSAmJg0KICAgICAgICAgIChpdGVtLnByZWZpeE1pbnV0ZSA9PSAiIiB8fA0KICAgICAgICAgICAgaXRlbS5zdWZmaXhNaW51dGUgPT0gIiIgfHwNCiAgICAgICAgICAgIGl0ZW0uYmFja3VwRGlyZWN0b3J5ID09ICIiIHx8DQogICAgICAgICAgICBpdGVtLmFjY291bnQgPT0gIiIgfHwNCiAgICAgICAgICAgIGl0ZW0ucGFzc3dvcmQgPT0gIiIgfHwNCiAgICAgICAgICAgIGl0ZW0ua2VlcERheXMgPT0gIiIgfHwNCiAgICAgICAgICAgIGl0ZW0ucG9zaXRpb25DYXB0dXJlID09ICIiKQ0KICAgICAgICApIHsNCiAgICAgICAgICBsZXQgbmFtZSA9IHRoaXMuY2FtZXJhVHlwZUxpc3QuZmluZChpdGVtMiA9PiBpdGVtLnR5cGUgPT0gaXRlbTIudHlwZSkNCiAgICAgICAgICAgIC5uYW1lOw0KICAgICAgICAgIGVycm9yRGVzYyArPQ0KICAgICAgICAgICAgdGhpcy4kdCgiTG9jYWxlU3RyaW5nLkwwMDAwMSIsIHsNCiAgICAgICAgICAgICAgMDogdGhpcy4kdCgiTG9jYWxlU3RyaW5nLkwzMDEzOSIpICsgIiAtICIgKyBuYW1lDQogICAgICAgICAgICB9KSArICI8YnI+IjsNCiAgICAgICAgfQ0KICAgICAgfSk7DQogICAgICAvLyBpZiAoaWNvblNpemUgPT0gIiIpIHsNCiAgICAgIC8vICAgICBlcnJvckRlc2MgKz0gJ+iri+i8uOWFpeW5s+mdouWcluaomeWkp+Wwjzxicj4nDQogICAgICAvLyB9ZWxzZSB7DQogICAgICAvLyAgIGxldCByZSA9IC9eWzAtOV17MSx9XCpbMC05XXsxLH0kLw0KICAgICAgLy8gICBpZiAoIXJlLnRlc3QoaWNvblNpemUpKSB7DQogICAgICAvLyAgICAgZXJyb3JEZXNjICs9ICflubPpnaLlnJbmqJnlpKflsI/moLzlvI/pjK/oqqQ8YnI+Jw0KICAgICAgLy8gICB9DQogICAgICAvLyB9DQogICAgICAvLw0KICAgICAgaWYgKGVycm9yRGVzYykgew0KICAgICAgICB0aGlzLiROb3RpY2UuZXJyb3Ioew0KICAgICAgICAgIHRpdGxlOiB0aGlzLiR0KCJMb2NhbGVTdHJpbmcuRTAwMDM3IiksDQogICAgICAgICAgZGVzYzogZXJyb3JEZXNjLA0KICAgICAgICAgIGR1cmF0aW9uOiBDb25maWcuZXJyb3JEdXJhdGlvbg0KICAgICAgICB9KTsNCiAgICAgICAgcmV0dXJuIGZhbHNlOw0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgcmV0dXJuIHRydWU7DQogICAgICB9DQogICAgfSwNCiAgICBlZGl0SGFuZGxlU3VibWl0TmV3KHR5cGUpIHsNCiAgICAgIGxldCByZXF1ZXN0SWQgPSB0aGlzLmNyZWF0ZVJlcXVlc3RJRCgpOw0KICAgICAgaWYgKHR5cGUgPT09ICJ1cGRhdGUiKSB7DQogICAgICAgIHRoaXMuc3VibWl0TG9hZGluZyA9IHRydWU7DQogICAgICB9DQogICAgICAvL2xldCBsb2dpblVzZXJOYW1lID0gbG9jYWxTdG9yYWdlLmdldEl0ZW0oInNuc19sb2dpblVzZXIiKTsNCiAgICAgIGxldCBuZXdMb3dCYXR0ZXJ5RGV2aWNlcyA9IFtdOw0KICAgICAgbGV0IG5ld0xvc3NpZ25hbERldmljZXMgPSBbXTsNCiAgICAgIGxldCBuZXdMb3NzaWduYWxEZXZpY2VzMiA9IFtdOw0KICAgICAgbGV0IG5ld0Fibm9ybWFsRGV2aWNlcyA9IFtdOw0KICAgICAgbGV0IHN1Ym1pdEV2ZW50QXJyID0gW107DQogICAgICBsZXQgcHV0UHJvcGVydHlEYXRhcyA9IFtdOw0KICAgICAgbGV0IHZhbGlkID0gdGhpcy5jaGVja0VkaXRIYW5kbGVTdWJtaXROZXcoKTsNCiAgICAgIGlmICh2YWxpZCkgew0KICAgICAgICBpZiAodHlwZSA9PT0gIm5ldyIpIHsNCiAgICAgICAgICB0aGlzLmNvbmZpZ0RhdGEubG9zc1NpZ25hbERldmljZXMgPSB0aGlzLmRldmljZUxpc3RMb3NzU2lnbmFsLm1hcCgNCiAgICAgICAgICAgIGl0ZW0gPT4gaXRlbS5rZXkNCiAgICAgICAgICApOw0KICAgICAgICAgIG5ld0xvc3NpZ25hbERldmljZXMgPSB0aGlzLmNvbmZpZ0RhdGEubG9zc1NpZ25hbERldmljZXM7DQoNCiAgICAgICAgICB0aGlzLmNvbmZpZ0RhdGEubG9zc1NpZ25hbERldmljZXMyID0gdGhpcy5kZXZpY2VMaXN0TG9zc1NpZ25hbDIubWFwKA0KICAgICAgICAgICAgaXRlbSA9PiBpdGVtLmtleQ0KICAgICAgICAgICk7DQogICAgICAgICAgbmV3TG9zc2lnbmFsRGV2aWNlczIgPSB0aGlzLmNvbmZpZ0RhdGEubG9zc1NpZ25hbERldmljZXMyOw0KDQogICAgICAgICAgdGhpcy5jb25maWdEYXRhLmxvd0JhdHRlcnlEZXZpY2VzID0gdGhpcy5kZXZpY2VMaXN0TG93QmF0dGVyeS5tYXAoDQogICAgICAgICAgICBpdGVtID0+IGl0ZW0ua2V5DQogICAgICAgICAgKTsNCiAgICAgICAgICBuZXdMb3dCYXR0ZXJ5RGV2aWNlcyA9IHRoaXMuY29uZmlnRGF0YS5sb3dCYXR0ZXJ5RGV2aWNlczsNCg0KICAgICAgICAgIHRoaXMuY29uZmlnRGF0YS5hYm5vcm1hbERldmljZXMgPSB0aGlzLmRldmljZUxpc3RBYm5vcm1hbERldmljZS5tYXAoDQogICAgICAgICAgICBpdGVtID0+IGl0ZW0ua2V5DQogICAgICAgICAgKTsNCiAgICAgICAgICBuZXdBYm5vcm1hbERldmljZXMgPSB0aGlzLmNvbmZpZ0RhdGEuYWJub3JtYWxEZXZpY2VzOw0KICAgICAgICB9DQoNCiAgICAgICAgbGV0IHB1dERhdGEgPSB7DQogICAgICAgICAgY2F0ZWdvcnk6ICJAU05TQFNldHRpbmciLA0KICAgICAgICAgIHByb3BlcnRpZXM6IFsNCiAgICAgICAgICAgIHsNCiAgICAgICAgICAgICAga2V5OiAibGljZW5zZSIsDQogICAgICAgICAgICAgIHZhbHVlOiB0aGlzLmNvbmZpZ0RhdGEubGljZW5zZS5qb2luKCIsIikNCiAgICAgICAgICAgIH0sDQogICAgICAgICAgICB7DQogICAgICAgICAgICAgIGtleTogInN5c3RlbU5hbWUiLA0KICAgICAgICAgICAgICB2YWx1ZTogdGhpcy5jb25maWdEYXRhLnN5c3RlbU5hbWUNCiAgICAgICAgICAgIH0sDQogICAgICAgICAgICB7DQogICAgICAgICAgICAgIGtleTogInRhYkxpc3QiLA0KICAgICAgICAgICAgICB2YWx1ZTogdGhpcy5jb25maWdEYXRhLnRhYkxpc3Quam9pbigiLCIpDQogICAgICAgICAgICB9LA0KICAgICAgICAgICAgLy8gew0KICAgICAgICAgICAgLy8gICBrZXk6ICJzb3VuZCIsDQogICAgICAgICAgICAvLyAgIHZhbHVlOiB0aGlzLmNvbmZpZ0RhdGEuc291bmQgPT0gdHJ1ZSA/ICJ0cnVlIiA6ICJmYWxzZSIsDQogICAgICAgICAgICAvLyB9LA0KICAgICAgICAgICAgew0KICAgICAgICAgICAgICBrZXk6ICJtMkxpc3QiLA0KICAgICAgICAgICAgICB2YWx1ZTogdGhpcy5jb25maWdEYXRhLm0yTGlzdC5qb2luKCIsIikNCiAgICAgICAgICAgIH0sDQogICAgICAgICAgICB7DQogICAgICAgICAgICAgIGtleTogImljb25TaXplIiwNCiAgICAgICAgICAgICAgdmFsdWU6IHRoaXMuY29uZmlnRGF0YS5pY29uU2l6ZQ0KICAgICAgICAgICAgfSwNCiAgICAgICAgICAgIHsNCiAgICAgICAgICAgICAga2V5OiAiZGVmYXVsdFRhYiIsDQogICAgICAgICAgICAgIHZhbHVlOiB0aGlzLmNvbmZpZ0RhdGEuZGVmYXVsdFRhYg0KICAgICAgICAgICAgfSwNCiAgICAgICAgICAgIHsNCiAgICAgICAgICAgICAga2V5OiAidXBkYXRlU2Vjb25kcyIsDQogICAgICAgICAgICAgIHZhbHVlOiB0aGlzLmNvbmZpZ0RhdGEudXBkYXRlU2Vjb25kcw0KICAgICAgICAgICAgfSwNCiAgICAgICAgICAgIHsNCiAgICAgICAgICAgICAga2V5OiAiZ3JheVRpbWUiLA0KICAgICAgICAgICAgICB2YWx1ZTogdGhpcy5jb25maWdEYXRhLmdyYXlUaW1lDQogICAgICAgICAgICB9LA0KICAgICAgICAgICAgew0KICAgICAgICAgICAgICBrZXk6ICJFdmVudEF1dGhDbG9zZSIsDQogICAgICAgICAgICAgIHZhbHVlOiB0aGlzLmNvbmZpZ0RhdGEuZXZlbnRBdXRoQ2xvc2UNCiAgICAgICAgICAgIH0sDQogICAgICAgICAgICB7DQogICAgICAgICAgICAgIGtleTogIkV2ZW50S2VlcERheXMiLA0KICAgICAgICAgICAgICB2YWx1ZTogdGhpcy5jb25maWdEYXRhLmV2ZW50S2VlcERheXMNCiAgICAgICAgICAgIH0sDQogICAgICAgICAgICB7DQogICAgICAgICAgICAgIGtleTogIkluZmx1eFJhbmdlIiwNCiAgICAgICAgICAgICAgdmFsdWU6IHRoaXMuY29uZmlnRGF0YS5pbmZsdXhSYW5nZQ0KICAgICAgICAgICAgfSwNCiAgICAgICAgICAgIHsNCiAgICAgICAgICAgICAga2V5OiAibG9zc1NpZ25hbCIsDQogICAgICAgICAgICAgIHZhbHVlOiB0aGlzLmNvbmZpZ0RhdGEubG9zc1NpZ25hbA0KICAgICAgICAgICAgfSwNCiAgICAgICAgICAgIHsNCiAgICAgICAgICAgICAga2V5OiAibG9zc1NpZ25hbDIiLA0KICAgICAgICAgICAgICB2YWx1ZTogdGhpcy5jb25maWdEYXRhLmxvc3NTaWduYWwyDQogICAgICAgICAgICB9LA0KICAgICAgICAgICAgew0KICAgICAgICAgICAgICBrZXk6ICJsb3dCYXR0ZXJ5IiwNCiAgICAgICAgICAgICAgdmFsdWU6IHRoaXMuY29uZmlnRGF0YS5sb3dCYXR0ZXJ5DQogICAgICAgICAgICB9LA0KICAgICAgICAgICAgew0KICAgICAgICAgICAgICBrZXk6ICJsb3NzU2lnbmFsRGV2aWNlcyIsDQogICAgICAgICAgICAgIHZhbHVlOiB0aGlzLmNvbmZpZ0RhdGEubG9zc1NpZ25hbERldmljZXMuam9pbigiLCIpDQogICAgICAgICAgICB9LA0KICAgICAgICAgICAgew0KICAgICAgICAgICAgICBrZXk6ICJsb3NzU2lnbmFsRGV2aWNlczIiLA0KICAgICAgICAgICAgICB2YWx1ZTogdGhpcy5jb25maWdEYXRhLmxvc3NTaWduYWxEZXZpY2VzMi5qb2luKCIsIikNCiAgICAgICAgICAgIH0sDQogICAgICAgICAgICB7DQogICAgICAgICAgICAgIGtleTogImxvd0JhdHRlcnlEZXZpY2VzIiwNCiAgICAgICAgICAgICAgdmFsdWU6IHRoaXMuY29uZmlnRGF0YS5sb3dCYXR0ZXJ5RGV2aWNlcy5qb2luKCIsIikNCiAgICAgICAgICAgIH0sDQogICAgICAgICAgICB7DQogICAgICAgICAgICAgIGtleTogImFibm9ybWFsRGV2aWNlcyIsDQogICAgICAgICAgICAgIHZhbHVlOiB0aGlzLmNvbmZpZ0RhdGEuYWJub3JtYWxEZXZpY2VzLmpvaW4oIiwiKQ0KICAgICAgICAgICAgfSwNCiAgICAgICAgICAgIHsNCiAgICAgICAgICAgICAga2V5OiAic2tpcEV2ZW50IiwNCiAgICAgICAgICAgICAgdmFsdWU6IHRoaXMuY29uZmlnRGF0YS5za2lwRXZlbnQuam9pbigiLCIpDQogICAgICAgICAgICB9LA0KICAgICAgICAgICAgew0KICAgICAgICAgICAgICBrZXk6ICJkZXZpY2VTZWxlY3RMaXN0IiwNCiAgICAgICAgICAgICAgdmFsdWU6IHRoaXMuY29uZmlnRGF0YS5kZXZpY2VTZWxlY3RMaXN0LmpvaW4oIiwiKQ0KICAgICAgICAgICAgfSwNCiAgICAgICAgICAgIHsNCiAgICAgICAgICAgICAga2V5OiAic2VsZWN0ZWRQbGFuZXMiLA0KICAgICAgICAgICAgICB2YWx1ZTogdGhpcy5jb25maWdEYXRhLnNlbGVjdGVkUGxhbmVzLmpvaW4oIiwiKQ0KICAgICAgICAgICAgfSwNCiAgICAgICAgICAgIHsNCiAgICAgICAgICAgICAga2V5OiAibXVsdGlQbGFuZXMiLA0KICAgICAgICAgICAgICB2YWx1ZTogdGhpcy5jb25maWdEYXRhLm11bHRpUGxhbmVzDQogICAgICAgICAgICB9LA0KICAgICAgICAgICAgew0KICAgICAgICAgICAgICBrZXk6ICJzdHVja1BsYW5lIiwNCiAgICAgICAgICAgICAgdmFsdWU6IHRoaXMuY29uZmlnRGF0YS5zdHVja1BsYW5lDQogICAgICAgICAgICB9LA0KICAgICAgICAgICAgew0KICAgICAgICAgICAgICBrZXk6ICJtMUxvbmdQcmVzcyIsDQogICAgICAgICAgICAgIHZhbHVlOiB0aGlzLmNvbmZpZ0RhdGEubTFMb25nUHJlc3Muam9pbigiLCIpDQogICAgICAgICAgICB9LA0KICAgICAgICAgICAgew0KICAgICAgICAgICAgICBrZXk6ICJkZWZhdWx0UGFnZVNpemUiLA0KICAgICAgICAgICAgICB2YWx1ZTogdGhpcy5jb25maWdEYXRhLmRlZmF1bHRQYWdlU2l6ZQ0KICAgICAgICAgICAgfSwNCiAgICAgICAgICAgIHsNCiAgICAgICAgICAgICAga2V5OiAibTVPYmplY3RUeXBlIiwNCiAgICAgICAgICAgICAgdmFsdWU6IHRoaXMuY29uZmlnRGF0YS5tNU9iamVjdFR5cGUNCiAgICAgICAgICAgIH0sDQogICAgICAgICAgICB7DQogICAgICAgICAgICAgIGtleTogInNob3dMZWF2ZUJlZCIsDQogICAgICAgICAgICAgIHZhbHVlOiB0aGlzLmNvbmZpZ0RhdGEuc2hvd0xlYXZlQmVkDQogICAgICAgICAgICB9LA0KICAgICAgICAgICAgew0KICAgICAgICAgICAgICBrZXk6ICJtMlN0YXRpc3RpYyIsDQogICAgICAgICAgICAgIHZhbHVlOiB0aGlzLmNvbmZpZ0RhdGEubTJTdGF0aXN0aWMNCiAgICAgICAgICAgIH0sDQogICAgICAgICAgICB7DQogICAgICAgICAgICAgIGtleTogIm0zU3RhdGlzdGljIiwNCiAgICAgICAgICAgICAgdmFsdWU6IHRoaXMuY29uZmlnRGF0YS5tM1N0YXRpc3RpYw0KICAgICAgICAgICAgfSwNCiAgICAgICAgICAgIHsNCiAgICAgICAgICAgICAga2V5OiAibTRTdGF0aXN0aWMiLA0KICAgICAgICAgICAgICB2YWx1ZTogdGhpcy5jb25maWdEYXRhLm00U3RhdGlzdGljDQogICAgICAgICAgICB9LA0KICAgICAgICAgICAgew0KICAgICAgICAgICAgICBrZXk6ICJtOFN0YXRpc3RpYyIsDQogICAgICAgICAgICAgIHZhbHVlOiB0aGlzLmNvbmZpZ0RhdGEubThTdGF0aXN0aWMNCiAgICAgICAgICAgIH0sDQogICAgICAgICAgICB7DQogICAgICAgICAgICAgIGtleTogIm05U3RhdGlzdGljIiwNCiAgICAgICAgICAgICAgdmFsdWU6IHRoaXMuY29uZmlnRGF0YS5tOVN0YXRpc3RpYw0KICAgICAgICAgICAgfSwNCiAgICAgICAgICAgIHsNCiAgICAgICAgICAgICAga2V5OiAic3RhdGVDb2xvciIsDQogICAgICAgICAgICAgIHZhbHVlOiB0aGlzLmNvbmZpZ0RhdGEuc3RhdGVDb2xvcg0KICAgICAgICAgICAgfSwNCiAgICAgICAgICAgIHsNCiAgICAgICAgICAgICAga2V5OiAicmVwZWF0U291bmQiLA0KICAgICAgICAgICAgICB2YWx1ZTogdGhpcy5jb25maWdEYXRhLnJlcGVhdFNvdW5kDQogICAgICAgICAgICB9LA0KICAgICAgICAgICAgew0KICAgICAgICAgICAgICBrZXk6ICJsb2dvdXRUaW1lIiwNCiAgICAgICAgICAgICAgdmFsdWU6IHRoaXMuY29uZmlnRGF0YS5sb2dvdXRUaW1lDQogICAgICAgICAgICB9DQogICAgICAgICAgXQ0KICAgICAgICB9Ow0KICAgICAgICBpZiAodGhpcy5jb25maWdEYXRhLnN5c3RlbU5hbWUgPT0gIiIpIHsNCiAgICAgICAgICBsb2NhbFN0b3JhZ2UucmVtb3ZlSXRlbSgic25zX3N5c3RlbVRpdGxlIik7DQogICAgICAgICAgZG9jdW1lbnQudGl0bGUgPSB0aGlzLiR0KCJMb2NhbGVTdHJpbmcuUzAwMDAyIik7DQogICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgbG9jYWxTdG9yYWdlLnNldEl0ZW0oInNuc19zeXN0ZW1UaXRsZSIsIHRoaXMuY29uZmlnRGF0YS5zeXN0ZW1OYW1lKTsNCiAgICAgICAgICBkb2N1bWVudC50aXRsZSA9IHRoaXMuY29uZmlnRGF0YS5zeXN0ZW1OYW1lOw0KICAgICAgICB9DQogICAgICAgIGxvY2FsU3RvcmFnZS5zZXRJdGVtKCJzbnNfZGVmYXVsdFRhYiIsIHRoaXMuY29uZmlnRGF0YS5kZWZhdWx0VGFiKTsNCiAgICAgICAgbG9jYWxTdG9yYWdlLnNldEl0ZW0oDQogICAgICAgICAgInNuc19hbGVydFNvdW5kIiwNCiAgICAgICAgICB0aGlzLmNvbmZpZ0RhdGEuc291bmQgPT0gdHJ1ZSA/ICJ0cnVlIiA6ICJmYWxzZSINCiAgICAgICAgKTsNCiAgICAgICAgbG9jYWxTdG9yYWdlLnNldEl0ZW0oDQogICAgICAgICAgInNuc19tMkxpbmUiLA0KICAgICAgICAgIHRoaXMuY29uZmlnRGF0YS5tMkxpbmUgPT0gdHJ1ZSA/ICJ0cnVlIiA6ICJmYWxzZSINCiAgICAgICAgKTsNCiAgICAgICAgbG9jYWxTdG9yYWdlLnNldEl0ZW0oDQogICAgICAgICAgInNuc19zdHVja1BsYW5lIiwNCiAgICAgICAgICB0aGlzLmNvbmZpZ0RhdGEuc3R1Y2tQbGFuZSA9PSB0cnVlID8gInRydWUiIDogImZhbHNlIg0KICAgICAgICApOw0KICAgICAgICBsb2NhbFN0b3JhZ2Uuc2V0SXRlbSgic25zX3BhZ2VTaXplIiwgdGhpcy5jb25maWdEYXRhLmRlZmF1bHRQYWdlU2l6ZSk7DQogICAgICAgIGxvY2FsU3RvcmFnZS5zZXRJdGVtKCJzbnNfbG9nb3V0VGltZSIsIHRoaXMuY29uZmlnRGF0YS5sb2dvdXRUaW1lKTsNCiAgICAgICAgcHV0UHJvcGVydHlEYXRhcy5wdXNoKHB1dERhdGEpOw0KDQogICAgICAgIGxldCBlZGl0UE9DR3JvdXBzID0gWw0KICAgICAgICAgIHsNCiAgICAgICAgICAgIGNvZGU6ICJAU05TQFNvdW5kIiwNCiAgICAgICAgICAgIG5hbWU6ICJAU05TQFNvdW5kIiwNCiAgICAgICAgICAgIGNhdGVnb3J5OiAiQFNOU0BTb3VuZCINCiAgICAgICAgICB9LA0KICAgICAgICAgIHsNCiAgICAgICAgICAgIGNvZGU6ICJAU05TQExvZ28iLA0KICAgICAgICAgICAgbmFtZTogIkBTTlNATG9nbyIsDQogICAgICAgICAgICBjYXRlZ29yeTogIkBTTlNATG9nbyINCiAgICAgICAgICB9DQogICAgICAgIF07DQogICAgICAgIGxldCBwYXJhbXNFdmVudCA9IHsNCiAgICAgICAgICBpbmxpbmVjb3VudDogdHJ1ZSwNCiAgICAgICAgICBzZWFyY2g6DQogICAgICAgICAgICAiYWN0aXZlIGVxIHRydWUgYW5kIGNvZGUgaW4gQFNOU0BMb3dCYXR0ZXJ5LEBTTlNATG9zc1NpZ25hbCxAU05TQExvc3NTaWduYWwyLEBTTlNAQWJub3JtYWxEZXZpY2UiDQogICAgICAgIH07DQoNCiAgICAgICAgbGV0IG9iamVjdFBhcmFtcyA9IHsNCiAgICAgICAgICBzZWFyY2g6ICJhY3RpdmUgZXEgdHJ1ZSBhbmQgZm9yZWlnbktleXMua2V5MSBlcSBAU05TQCINCiAgICAgICAgfTsNCg0KICAgICAgICAvLy8vLy8vIGNvbnN0cnVjdCBrbSBkYXRhIC8vLy8vLy8NCiAgICAgICAgbGV0IHB1dEtNRGF0YSA9IHsNCiAgICAgICAgICBjYXRlZ29yeTogIkBTTlNAU2V0dGluZ0tNIiwNCiAgICAgICAgICBwcm9wZXJ0aWVzOiBbXQ0KICAgICAgICB9Ow0KICAgICAgICB0aGlzLmttQ29uZmlnTGlzdEVkaXQuZm9yRWFjaChpdGVtID0+IHsNCiAgICAgICAgICBsZXQga2V5VmFsdWVQYWlycyA9IFsNCiAgICAgICAgICAgIHsNCiAgICAgICAgICAgICAga2V5OiBpdGVtLnR5cGUgKyAiX2xvbmdQcmVzc19jaHQiLA0KICAgICAgICAgICAgICB2YWx1ZTogaXRlbS5sb25nUHJlc3NfY2h0DQogICAgICAgICAgICB9LA0KICAgICAgICAgICAgew0KICAgICAgICAgICAgICBrZXk6IGl0ZW0udHlwZSArICJfbG9uZ1ByZXNzX2VuIiwNCiAgICAgICAgICAgICAgdmFsdWU6IGl0ZW0ubG9uZ1ByZXNzX2VuDQogICAgICAgICAgICB9LA0KICAgICAgICAgICAgew0KICAgICAgICAgICAgICBrZXk6IGl0ZW0udHlwZSArICJfc2hvcnRDbGlja19jaHQiLA0KICAgICAgICAgICAgICB2YWx1ZTogaXRlbS5zaG9ydENsaWNrX2NodA0KICAgICAgICAgICAgfSwNCiAgICAgICAgICAgIHsNCiAgICAgICAgICAgICAga2V5OiBpdGVtLnR5cGUgKyAiX3Nob3J0Q2xpY2tfZW4iLA0KICAgICAgICAgICAgICB2YWx1ZTogaXRlbS5zaG9ydENsaWNrX2VuDQogICAgICAgICAgICB9LA0KICAgICAgICAgICAgew0KICAgICAgICAgICAgICBrZXk6IGl0ZW0udHlwZSArICJfZG91YmxlQ2xpY2tfY2h0IiwNCiAgICAgICAgICAgICAgdmFsdWU6IGl0ZW0uZG91YmxlQ2xpY2tfY2h0DQogICAgICAgICAgICB9LA0KICAgICAgICAgICAgew0KICAgICAgICAgICAgICBrZXk6IGl0ZW0udHlwZSArICJfZG91YmxlQ2xpY2tfZW4iLA0KICAgICAgICAgICAgICB2YWx1ZTogaXRlbS5kb3VibGVDbGlja19lbg0KICAgICAgICAgICAgfQ0KICAgICAgICAgIF07DQogICAgICAgICAgcHV0S01EYXRhLnByb3BlcnRpZXMgPSBbLi4ucHV0S01EYXRhLnByb3BlcnRpZXMsIC4uLmtleVZhbHVlUGFpcnNdOw0KICAgICAgICB9KTsNCiAgICAgICAgcHV0UHJvcGVydHlEYXRhcy5wdXNoKHB1dEtNRGF0YSk7DQogICAgICAgIC8vLy8vLy8gY29uc3RydWN0IGttIGRhdGEgLy8vLy8vLw0KDQogICAgICAgIC8vLy8vLy8gY29uc3RydWN0IGNhbWVyYSBkYXRhIC8vLy8vLy8NCiAgICAgICAgbGV0IHB1dENhbWVyYURhdGEgPSB7DQogICAgICAgICAgY2F0ZWdvcnk6ICJAU05TQFNldHRpbmdDYW1lcmEiLA0KICAgICAgICAgIHByb3BlcnRpZXM6IFtdDQogICAgICAgIH07DQogICAgICAgIHRoaXMuY2FtZXJhQ29uZmlnTGlzdC5mb3JFYWNoKGl0ZW0gPT4gew0KICAgICAgICAgIGxldCBrZXlWYWx1ZVBhaXJzID0gWw0KICAgICAgICAgICAgew0KICAgICAgICAgICAgICBrZXk6IGl0ZW0udHlwZSArICJfY2FwdHVyZSIsDQogICAgICAgICAgICAgIHZhbHVlOiBpdGVtLmNhcHR1cmUNCiAgICAgICAgICAgIH0sDQogICAgICAgICAgICB7DQogICAgICAgICAgICAgIGtleTogaXRlbS50eXBlICsgIl9wcmVmaXhNaW51dGUiLA0KICAgICAgICAgICAgICB2YWx1ZTogaXRlbS5wcmVmaXhNaW51dGUNCiAgICAgICAgICAgIH0sDQogICAgICAgICAgICB7DQogICAgICAgICAgICAgIGtleTogaXRlbS50eXBlICsgIl9zdWZmaXhNaW51dGUiLA0KICAgICAgICAgICAgICB2YWx1ZTogaXRlbS5zdWZmaXhNaW51dGUNCiAgICAgICAgICAgIH0sDQogICAgICAgICAgICB7DQogICAgICAgICAgICAgIGtleTogaXRlbS50eXBlICsgIl9iYWNrdXBEaXJlY3RvcnkiLA0KICAgICAgICAgICAgICB2YWx1ZTogaXRlbS5iYWNrdXBEaXJlY3RvcnkNCiAgICAgICAgICAgIH0sDQogICAgICAgICAgICB7DQogICAgICAgICAgICAgIGtleTogaXRlbS50eXBlICsgIl9hY2NvdW50IiwNCiAgICAgICAgICAgICAgdmFsdWU6IGl0ZW0uYWNjb3VudA0KICAgICAgICAgICAgfSwNCiAgICAgICAgICAgIHsNCiAgICAgICAgICAgICAga2V5OiBpdGVtLnR5cGUgKyAiX3Bhc3N3b3JkIiwNCiAgICAgICAgICAgICAgdmFsdWU6IGl0ZW0ucGFzc3dvcmQNCiAgICAgICAgICAgIH0sDQogICAgICAgICAgICB7DQogICAgICAgICAgICAgIGtleTogaXRlbS50eXBlICsgIl9rZWVwRGF5cyIsDQogICAgICAgICAgICAgIHZhbHVlOiBpdGVtLmtlZXBEYXlzDQogICAgICAgICAgICB9LA0KICAgICAgICAgICAgew0KICAgICAgICAgICAgICBrZXk6IGl0ZW0udHlwZSArICJfcG9zaXRpb25DYXB0dXJlIiwNCiAgICAgICAgICAgICAgdmFsdWU6IGl0ZW0ucG9zaXRpb25DYXB0dXJlDQogICAgICAgICAgICB9DQogICAgICAgICAgXTsNCiAgICAgICAgICBwdXRDYW1lcmFEYXRhLnByb3BlcnRpZXMgPSBbDQogICAgICAgICAgICAuLi5wdXRDYW1lcmFEYXRhLnByb3BlcnRpZXMsDQogICAgICAgICAgICAuLi5rZXlWYWx1ZVBhaXJzDQogICAgICAgICAgXTsNCiAgICAgICAgfSk7DQogICAgICAgIHB1dFByb3BlcnR5RGF0YXMucHVzaChwdXRDYW1lcmFEYXRhKTsNCiAgICAgICAgLy8vLy8vLyBjb25zdHJ1Y3QgY2FtZXJhIGRhdGEgLy8vLy8vLw0KICAgICAgICAvLy8vLy8vIGNvbnN0cnVjdCBzdGF0aXN0aWMgTGluZSBkYXRhIC8vLy8vLy8NCiAgICAgICAgbGV0IHB1dFN0YXRpc3RpY0xpbmVEYXRhID0gew0KICAgICAgICAgIGNhdGVnb3J5OiAiQFNOU0BTZXR0aW5nU3RhdGlzdGljTGluZSIsDQogICAgICAgICAgcHJvcGVydGllczogW10NCiAgICAgICAgfTsNCiAgICAgICAgdGhpcy5zdGF0aXN0aWNMaW5lQ29uZmlnTGlzdC5mb3JFYWNoKGl0ZW0gPT4gew0KICAgICAgICAgIGxldCBrZXlWYWx1ZVBhaXJzID0gWw0KICAgICAgICAgICAgew0KICAgICAgICAgICAgICBrZXk6IGl0ZW0uZW50cnkgKyAiX2ludGVydmFsIiwNCiAgICAgICAgICAgICAgdmFsdWU6IGl0ZW0uaW50ZXJ2YWwNCiAgICAgICAgICAgIH0sDQogICAgICAgICAgICB7DQogICAgICAgICAgICAgIGtleTogaXRlbS5lbnRyeSArICJfZGF0YVR5cGUiLA0KICAgICAgICAgICAgICB2YWx1ZTogaXRlbS5kYXRhVHlwZQ0KICAgICAgICAgICAgfSwNCiAgICAgICAgICAgIHsNCiAgICAgICAgICAgICAga2V5OiBpdGVtLmVudHJ5ICsgIl93YXJuaW5nTGluZSIsDQogICAgICAgICAgICAgIHZhbHVlOiBpdGVtLndhcm5pbmdMaW5lDQogICAgICAgICAgICB9DQogICAgICAgICAgXTsNCiAgICAgICAgICBwdXRTdGF0aXN0aWNMaW5lRGF0YS5wcm9wZXJ0aWVzID0gWw0KICAgICAgICAgICAgLi4ucHV0U3RhdGlzdGljTGluZURhdGEucHJvcGVydGllcywNCiAgICAgICAgICAgIC4uLmtleVZhbHVlUGFpcnMNCiAgICAgICAgICBdOw0KICAgICAgICB9KTsNCiAgICAgICAgcHV0UHJvcGVydHlEYXRhcy5wdXNoKHB1dFN0YXRpc3RpY0xpbmVEYXRhKTsNCiAgICAgICAgLy8vLy8vLyBjb25zdHJ1Y3Qgc3RhdGlzdGljIExpbmUgZGF0YSAvLy8vLy8vDQogICAgICAgIC8vLy8vLy8gY29uc3RydWN0IHRoaXJkUGFydHkgZGF0YSAvLy8vLy8vDQoNCiAgICAgICAgaWYgKHRoaXMudGhpcmRQYXJ0eURhdGEubG9naW5UaGlyZFBhcnR5KSB7DQogICAgICAgICAgbGV0IGVycm9yTXNnID0gWw0KICAgICAgICAgICAgdGhpcy4kdCgiTG9jYWxlU3RyaW5nLkwzMDE5NiIpLA0KICAgICAgICAgICAgIlVzZXJOYW1lIiwNCiAgICAgICAgICAgICJDcmVkZW50aWFsU2VjcmV0Ig0KICAgICAgICAgIF07DQogICAgICAgICAgaWYgKA0KICAgICAgICAgICAgdGhpcy50aGlyZFBhcnR5RGF0YS51cmwgPT0gIiIgfHwNCiAgICAgICAgICAgIHRoaXMudGhpcmRQYXJ0eURhdGEudXNlck5hbWUgPT0gIiIgfHwNCiAgICAgICAgICAgIHRoaXMudGhpcmRQYXJ0eURhdGEuc2VjcmV0ID09ICIiDQogICAgICAgICAgKSB7DQogICAgICAgICAgICB0aGlzLiROb3RpY2UuZXJyb3Ioew0KICAgICAgICAgICAgICB0aXRsZTogdGhpcy4kdCgiTG9jYWxlU3RyaW5nLkUwMDAzNyIpLA0KICAgICAgICAgICAgICBkZXNjOiB0aGlzLiR0KCJMb2NhbGVTdHJpbmcuVzAwMDM4IiwgeyAwOiBlcnJvck1zZy5qb2luKCIsICIpIH0pLA0KICAgICAgICAgICAgICBkdXJhdGlvbjogQ29uZmlnLldBUk5JTkdfRFVSQVRJT04NCiAgICAgICAgICAgIH0pOw0KICAgICAgICAgICAgdGhpcy5zdWJtaXRMb2FkaW5nID0gZmFsc2U7DQogICAgICAgICAgICByZXR1cm47DQogICAgICAgICAgfQ0KICAgICAgICB9DQogICAgICAgIGxldCBwdXRUaGlyZFBhcnR5RGF0YSA9IHsNCiAgICAgICAgICBjYXRlZ29yeTogIkBTTlNAU2V0dGluZ1RoaXJkUGFydHkiLA0KICAgICAgICAgIHByb3BlcnRpZXM6IFsNCiAgICAgICAgICAgIHsNCiAgICAgICAgICAgICAga2V5OiAibG9naW5UaGlyZFBhcnR5IiwNCiAgICAgICAgICAgICAgdmFsdWU6IHRoaXMudGhpcmRQYXJ0eURhdGEubG9naW5UaGlyZFBhcnR5DQogICAgICAgICAgICB9LA0KICAgICAgICAgICAgew0KICAgICAgICAgICAgICBrZXk6ICJ1cmwiLA0KICAgICAgICAgICAgICB2YWx1ZTogdGhpcy50aGlyZFBhcnR5RGF0YS51cmwNCiAgICAgICAgICAgIH0sDQogICAgICAgICAgICB7DQogICAgICAgICAgICAgIGtleTogInVzZXJOYW1lIiwNCiAgICAgICAgICAgICAgdmFsdWU6IHRoaXMudGhpcmRQYXJ0eURhdGEudXNlck5hbWUNCiAgICAgICAgICAgIH0sDQogICAgICAgICAgICB7DQogICAgICAgICAgICAgIGtleTogInNlY3JldCIsDQogICAgICAgICAgICAgIHZhbHVlOiB0aGlzLnRoaXJkUGFydHlEYXRhLnNlY3JldA0KICAgICAgICAgICAgfQ0KICAgICAgICAgIF0NCiAgICAgICAgfTsNCiAgICAgICAgcHV0UHJvcGVydHlEYXRhcy5wdXNoKHB1dFRoaXJkUGFydHlEYXRhKTsNCiAgICAgICAgLy8vLy8vLyBjb25zdHJ1Y3QgdGhpcmRQYXJ0eSBkYXRhIC8vLy8vLy8NCiAgICAgICAgbGV0IGNyZWF0ZUV2ZW50QXJyID0gW107DQogICAgICAgIGxldCBvYmplY3REZXZpY2VzTG9zc1NpZ25hbCA9IFtdOw0KICAgICAgICBsZXQgb2JqZWN0RGV2aWNlc0xvc3NTaWduYWwyID0gW107DQogICAgICAgIGxldCBvYmplY3REZXZpY2VzTG93QmF0dGVyeSA9IFtdOw0KICAgICAgICB0aGlzLiRzZXJ2aWNlLmdldE9iamVjdHNOb0xvYWQuc2VuZChvYmplY3RQYXJhbXMpLnRoZW4ob2JqUmVzID0+IHsNCiAgICAgICAgICB0aGlzLiRzZXJ2aWNlLmdldEV2ZW50c05vTG9hZC5zZW5kKHBhcmFtc0V2ZW50KS50aGVuKGV2ZW50UmVzID0+IHsNCiAgICAgICAgICAgIHRoaXMuJHNlcnZpY2UuZWRpdFBPQ0dyb3Vwcw0KICAgICAgICAgICAgICAuc2VuZChlZGl0UE9DR3JvdXBzLCBudWxsLCB7DQogICAgICAgICAgICAgICAgcmVxdWVzdElEOiByZXF1ZXN0SWQsDQogICAgICAgICAgICAgICAgcmVxdWVzdEZ1bmN0aW9uOiAic3lzdGVtUGFyYW1ldGVyIiwNCiAgICAgICAgICAgICAgICByZXF1ZXN0QWN0aW9uOiAiVXBkYXRlIg0KICAgICAgICAgICAgICB9KQ0KICAgICAgICAgICAgICAudGhlbihlZGl0UE9DUmVzID0+IHsNCiAgICAgICAgICAgICAgICB0aGlzLiRzZXJ2aWNlLmVkaXRQT0NQcm9wZXJ0aWVzDQogICAgICAgICAgICAgICAgICAuc2VuZChwdXRQcm9wZXJ0eURhdGFzLCBudWxsLCB7DQogICAgICAgICAgICAgICAgICAgIHJlcXVlc3RJRDogcmVxdWVzdElkLA0KICAgICAgICAgICAgICAgICAgICByZXF1ZXN0RnVuY3Rpb246ICJzeXN0ZW1QYXJhbWV0ZXIiLA0KICAgICAgICAgICAgICAgICAgICByZXF1ZXN0QWN0aW9uOiAiVXBkYXRlIg0KICAgICAgICAgICAgICAgICAgfSkNCiAgICAgICAgICAgICAgICAgIC50aGVuKHByb3BlcnR5UmVzID0+IHsNCiAgICAgICAgICAgICAgICAgICAgY29uc29sZS5sb2cocHJvcGVydHlSZXMpOw0KICAgICAgICAgICAgICAgICAgICBpZiAodHlwZSAhPSAibmV3Iikgew0KICAgICAgICAgICAgICAgICAgICAgIHRoaXMuJE5vdGljZS5zdWNjZXNzKHsNCiAgICAgICAgICAgICAgICAgICAgICAgIHRpdGxlOiB0aGlzLiR0KCJMb2NhbGVTdHJpbmcuTTAwMDA0IiksDQogICAgICAgICAgICAgICAgICAgICAgICBkZXNjOiB0aGlzLiR0KCJMb2NhbGVTdHJpbmcuTTAwMDAyIiksDQogICAgICAgICAgICAgICAgICAgICAgICBkdXJhdGlvbjogQ29uZmlnLlNVQ0NFU1NfRFVSQVRJT04NCiAgICAgICAgICAgICAgICAgICAgICB9KTsNCiAgICAgICAgICAgICAgICAgICAgfQ0KDQogICAgICAgICAgICAgICAgICAgIGxvY2FsU3RvcmFnZS5zZXRJdGVtKA0KICAgICAgICAgICAgICAgICAgICAgICJzbnNfaWNvblNpemUiLA0KICAgICAgICAgICAgICAgICAgICAgIHRoaXMuY29uZmlnRGF0YS5pY29uU2l6ZQ0KICAgICAgICAgICAgICAgICAgICApOw0KICAgICAgICAgICAgICAgICAgICBsb2NhbFN0b3JhZ2Uuc2V0SXRlbSgNCiAgICAgICAgICAgICAgICAgICAgICAic25zX3VwZGF0ZVNlY29uZHMiLA0KICAgICAgICAgICAgICAgICAgICAgIHRoaXMuY29uZmlnRGF0YS51cGRhdGVTZWNvbmRzDQogICAgICAgICAgICAgICAgICAgICk7DQoNCiAgICAgICAgICAgICAgICAgICAgdGhpcy5pc0VkaXQgPSBmYWxzZTsNCiAgICAgICAgICAgICAgICAgICAgdGhpcy5vcmlnQ29uZmlnRGF0YSA9IEpTT04ucGFyc2UoDQogICAgICAgICAgICAgICAgICAgICAgSlNPTi5zdHJpbmdpZnkodGhpcy5jb25maWdEYXRhKQ0KICAgICAgICAgICAgICAgICAgICApOw0KICAgICAgICAgICAgICAgICAgICB0aGlzLnVwZGF0ZVZhbHVlKHRoaXMub3JpZ0NvbmZpZ0RhdGEpOw0KDQogICAgICAgICAgICAgICAgICAgIHRoaXMub3JpVGhpcmRQYXJ0eURhdGEgPSBKU09OLnBhcnNlKA0KICAgICAgICAgICAgICAgICAgICAgIEpTT04uc3RyaW5naWZ5KHRoaXMudGhpcmRQYXJ0eURhdGEpDQogICAgICAgICAgICAgICAgICAgICk7DQogICAgICAgICAgICAgICAgICAgIHRoaXMudXBkYXRlVGhpcmRQYXJ0eVZhbHVlKHRoaXMub3JpVGhpcmRQYXJ0eURhdGEpOw0KDQogICAgICAgICAgICAgICAgICAgIGlmIChldmVudFJlcy5yZXN1bHRzLmxlbmd0aCA+IDApIHsNCiAgICAgICAgICAgICAgICAgICAgICBldmVudFJlcy5yZXN1bHRzLmZvckVhY2goY3VycmVudEV2ZW50ID0+IHsNCiAgICAgICAgICAgICAgICAgICAgICAgIGxldCBzdWJtaXRBcmd1bWVudCA9IGN1cnJlbnRFdmVudC5hcmd1bWVudHM7DQogICAgICAgICAgICAgICAgICAgICAgICBzdWJtaXRBcmd1bWVudC5mb3JFYWNoKGl0ZW0gPT4gew0KICAgICAgICAgICAgICAgICAgICAgICAgICBpZiAoaXRlbS5rZXkgPT0gInRocmVzaG9sZCIpIHsNCiAgICAgICAgICAgICAgICAgICAgICAgICAgICBpdGVtLnZhbHVlID0gY3VycmVudEV2ZW50LmNvZGUuaW5jbHVkZXMoDQogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAiTG93QmF0dGVyeSINCiAgICAgICAgICAgICAgICAgICAgICAgICAgICApDQogICAgICAgICAgICAgICAgICAgICAgICAgICAgICA/IHRoaXMuY29uZmlnRGF0YS5sb3dCYXR0ZXJ5LnRvU3RyaW5nKCkNCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDogY3VycmVudEV2ZW50LmNvZGUuaW5jbHVkZXMoIkxvc3NTaWduYWwyIikNCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgID8gdGhpcy5jb25maWdEYXRhLmxvc3NTaWduYWwyLnRvU3RyaW5nKCkNCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDogdGhpcy5jb25maWdEYXRhLmxvc3NTaWduYWwudG9TdHJpbmcoKTsNCiAgICAgICAgICAgICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICAgICAgICAgICAgfSk7DQogICAgICAgICAgICAgICAgICAgICAgICBsZXQgdXBkYXRlRXZlbnQgPSB7DQogICAgICAgICAgICAgICAgICAgICAgICAgIGNvZGU6IGN1cnJlbnRFdmVudC5jb2RlLA0KICAgICAgICAgICAgICAgICAgICAgICAgICBhcmd1bWVudHM6IHN1Ym1pdEFyZ3VtZW50LA0KICAgICAgICAgICAgICAgICAgICAgICAgICAvLyBzcG9uc29yT2JqZWN0OiB7DQogICAgICAgICAgICAgICAgICAgICAgICAgIC8vICAgb2JqZWN0Q29kZXM6IHRvdGFsX29iakNvZGVzLmxlbmd0aA0KICAgICAgICAgICAgICAgICAgICAgICAgICAvLyAgICAgPyB0b3RhbF9vYmpDb2Rlcw0KICAgICAgICAgICAgICAgICAgICAgICAgICAvLyAgICAgOiBbXSwNCiAgICAgICAgICAgICAgICAgICAgICAgICAgLy8gfSwNCiAgICAgICAgICAgICAgICAgICAgICAgICAgc3BvbnNvckRldmljZTogew0KICAgICAgICAgICAgICAgICAgICAgICAgICAgIGRldmljZVBpZHM6IGN1cnJlbnRFdmVudC5jb2RlLmluY2x1ZGVzKCJMb3dCYXR0ZXJ5IikNCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgID8gdGhpcy5nZXRFdmVudFBpZHMoIkxvd0JhdHRlcnkiLCBvYmpSZXMpDQogICAgICAgICAgICAgICAgICAgICAgICAgICAgICA6IGN1cnJlbnRFdmVudC5jb2RlLmluY2x1ZGVzKCJMb3NzU2lnbmFsMiIpDQogICAgICAgICAgICAgICAgICAgICAgICAgICAgICA/IHRoaXMuZ2V0RXZlbnRQaWRzKCJMb3NzU2lnbmFsMiIsIG9ialJlcykNCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDogY3VycmVudEV2ZW50LmNvZGUuaW5jbHVkZXMoIkFibm9ybWFsRGV2aWNlIikNCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgID8gdGhpcy5nZXRFdmVudFBpZHMoIkFibm9ybWFsRGV2aWNlIiwgb2JqUmVzKQ0KICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgOiB0aGlzLmdldEV2ZW50UGlkcygiTG9zc1NpZ25hbCIsIG9ialJlcyksDQogICAgICAgICAgICAgICAgICAgICAgICAgICAgZGV2aWNlVHlwZXM6IGN1cnJlbnRFdmVudC5jb2RlLmluY2x1ZGVzKA0KICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIkxvd0JhdHRlcnkiDQogICAgICAgICAgICAgICAgICAgICAgICAgICAgKQ0KICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPyB0aGlzLmNvbmZpZ0RhdGEubG93QmF0dGVyeURldmljZXMNCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDogY3VycmVudEV2ZW50LmNvZGUuaW5jbHVkZXMoIkxvc3NTaWduYWwyIikNCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgID8gdGhpcy5jb25maWdEYXRhLmxvc3NTaWduYWxEZXZpY2VzMg0KICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgOiBjdXJyZW50RXZlbnQuY29kZS5pbmNsdWRlcygiQWJub3JtYWxEZXZpY2UiKQ0KICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPyB0aGlzLmNvbmZpZ0RhdGEuYWJub3JtYWxEZXZpY2VzDQogICAgICAgICAgICAgICAgICAgICAgICAgICAgICA6IHRoaXMuY29uZmlnRGF0YS5sb3NzU2lnbmFsRGV2aWNlcw0KICAgICAgICAgICAgICAgICAgICAgICAgICB9DQogICAgICAgICAgICAgICAgICAgICAgICB9Ow0KICAgICAgICAgICAgICAgICAgICAgICAgc3VibWl0RXZlbnRBcnIucHVzaCh1cGRhdGVFdmVudCk7DQogICAgICAgICAgICAgICAgICAgICAgfSk7DQogICAgICAgICAgICAgICAgICAgICAgaWYgKA0KICAgICAgICAgICAgICAgICAgICAgICAgIWV2ZW50UmVzLnJlc3VsdHMuc29tZSgNCiAgICAgICAgICAgICAgICAgICAgICAgICAgaXRlbSA9PiBpdGVtLmNvZGUgPT0gIkBTTlNATG9zc1NpZ25hbCINCiAgICAgICAgICAgICAgICAgICAgICAgICkNCiAgICAgICAgICAgICAgICAgICAgICApIHsNCiAgICAgICAgICAgICAgICAgICAgICAgIC8vIGNyZWF0ZSBsb3NzU2lnbmFsIGV2ZW50DQogICAgICAgICAgICAgICAgICAgICAgICBsZXQgbG9zc1NpZ25hbEV2ZW50ID0gdGhpcy5sb3NzU2lnbmFsQ3JlYXRlVGVtcGxhdGUoDQogICAgICAgICAgICAgICAgICAgICAgICAgIG5ld0xvc3NpZ25hbERldmljZXMNCiAgICAgICAgICAgICAgICAgICAgICAgICk7DQogICAgICAgICAgICAgICAgICAgICAgICBjcmVhdGVFdmVudEFyci5wdXNoKGxvc3NTaWduYWxFdmVudCk7DQogICAgICAgICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICAgICAgICAgIGlmICgNCiAgICAgICAgICAgICAgICAgICAgICAgICFldmVudFJlcy5yZXN1bHRzLnNvbWUoDQogICAgICAgICAgICAgICAgICAgICAgICAgIGl0ZW0gPT4gaXRlbS5jb2RlID09ICJAU05TQExvc3NTaWduYWwyIg0KICAgICAgICAgICAgICAgICAgICAgICAgKQ0KICAgICAgICAgICAgICAgICAgICAgICkgew0KICAgICAgICAgICAgICAgICAgICAgICAgLy8gY3JlYXRlIGxvc3NTaWduYWwyIGV2ZW50DQogICAgICAgICAgICAgICAgICAgICAgICBsZXQgbG9zc1NpZ25hbEV2ZW50MiA9IHRoaXMubG9zc1NpZ25hbENyZWF0ZVRlbXBsYXRlMigNCiAgICAgICAgICAgICAgICAgICAgICAgICAgbmV3TG9zc2lnbmFsRGV2aWNlczINCiAgICAgICAgICAgICAgICAgICAgICAgICk7DQogICAgICAgICAgICAgICAgICAgICAgICBjcmVhdGVFdmVudEFyci5wdXNoKGxvc3NTaWduYWxFdmVudDIpOw0KICAgICAgICAgICAgICAgICAgICAgIH0NCiAgICAgICAgICAgICAgICAgICAgICBpZiAoDQogICAgICAgICAgICAgICAgICAgICAgICAhZXZlbnRSZXMucmVzdWx0cy5zb21lKA0KICAgICAgICAgICAgICAgICAgICAgICAgICBpdGVtID0+IGl0ZW0uY29kZSA9PSAiQFNOU0BMb3dCYXR0ZXJ5Ig0KICAgICAgICAgICAgICAgICAgICAgICAgKQ0KICAgICAgICAgICAgICAgICAgICAgICkgew0KICAgICAgICAgICAgICAgICAgICAgICAgLy8gY3JlYXRlIGxvd0JhdHRlcnkgZXZlbnQNCiAgICAgICAgICAgICAgICAgICAgICAgIGxldCBsb3dCZXR0ZXJ5RXZlbnQgPSB0aGlzLmxvd0JhdHRlcnlDcmVhdGVUZW1wbGF0ZSgNCiAgICAgICAgICAgICAgICAgICAgICAgICAgbmV3TG93QmF0dGVyeURldmljZXMNCiAgICAgICAgICAgICAgICAgICAgICAgICk7DQogICAgICAgICAgICAgICAgICAgICAgICBjcmVhdGVFdmVudEFyci5wdXNoKGxvd0JldHRlcnlFdmVudCk7DQogICAgICAgICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICAgICAgICAgIGlmICgNCiAgICAgICAgICAgICAgICAgICAgICAgICFldmVudFJlcy5yZXN1bHRzLnNvbWUoDQogICAgICAgICAgICAgICAgICAgICAgICAgIGl0ZW0gPT4gaXRlbS5jb2RlID09ICJAU05TQEFibm9ybWFsRGV2aWNlIg0KICAgICAgICAgICAgICAgICAgICAgICAgKQ0KICAgICAgICAgICAgICAgICAgICAgICkgew0KICAgICAgICAgICAgICAgICAgICAgICAgLy8gY3JlYXRlIGFibm9ybWFsRGV2aWNlIGV2ZW50DQogICAgICAgICAgICAgICAgICAgICAgICBsZXQgYWJub3JtYWxEZXZpY2VFdmVudCA9IHRoaXMuYWJub3JtYWxEZXZpY2VDcmVhdGVUZW1wbGF0ZSgNCiAgICAgICAgICAgICAgICAgICAgICAgICAgbmV3QWJub3JtYWxEZXZpY2VzDQogICAgICAgICAgICAgICAgICAgICAgICApOw0KICAgICAgICAgICAgICAgICAgICAgICAgY3JlYXRlRXZlbnRBcnIucHVzaChhYm5vcm1hbERldmljZUV2ZW50KTsNCiAgICAgICAgICAgICAgICAgICAgICB9DQogICAgICAgICAgICAgICAgICAgICAgLy9lZGl0IGxvd0JhdHRlcnkgZXZlbnQNCiAgICAgICAgICAgICAgICAgICAgICB0aGlzLiRzZXJ2aWNlLnBvc3ROZXdFdmVudA0KICAgICAgICAgICAgICAgICAgICAgICAgLnNlbmQoY3JlYXRlRXZlbnRBcnIsIG51bGwsIHsNCiAgICAgICAgICAgICAgICAgICAgICAgICAgcmVxdWVzdElEOiByZXF1ZXN0SWQsDQogICAgICAgICAgICAgICAgICAgICAgICAgIHJlcXVlc3RGdW5jdGlvbjogInN5c3RlbVBhcmFtZXRlciIsDQogICAgICAgICAgICAgICAgICAgICAgICAgIHJlcXVlc3RBY3Rpb246ICJVcGRhdGUiDQogICAgICAgICAgICAgICAgICAgICAgICB9KQ0KICAgICAgICAgICAgICAgICAgICAgICAgLnRoZW4ocG9zdFJlcyA9PiB7DQogICAgICAgICAgICAgICAgICAgICAgICAgIHRoaXMuJHNlcnZpY2UuZWRpdEV2ZW50DQogICAgICAgICAgICAgICAgICAgICAgICAgICAgLnNlbmQoc3VibWl0RXZlbnRBcnIsIG51bGwsIHsNCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHJlcXVlc3RJRDogcmVxdWVzdElkLA0KICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgcmVxdWVzdEZ1bmN0aW9uOiAic3lzdGVtUGFyYW1ldGVyIiwNCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHJlcXVlc3RBY3Rpb246ICJVcGRhdGUiDQogICAgICAgICAgICAgICAgICAgICAgICAgICAgfSkNCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAudGhlbihwYXRjaFJlcyA9PiB7DQogICAgICAgICAgICAgICAgICAgICAgICAgICAgICB0aGlzLmZpbGVVcGxvYWQodHlwZSk7DQogICAgICAgICAgICAgICAgICAgICAgICAgICAgfSk7DQogICAgICAgICAgICAgICAgICAgICAgICB9KTsNCiAgICAgICAgICAgICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgICAgICAgICAgICAvLyBjcmVhdGUgbG93QmF0dGVyeSBldmVudA0KICAgICAgICAgICAgICAgICAgICAgIGxldCBsb3dCZXR0ZXJ5RXZlbnQgPSB0aGlzLmxvd0JhdHRlcnlDcmVhdGVUZW1wbGF0ZSgNCiAgICAgICAgICAgICAgICAgICAgICAgIG5ld0xvd0JhdHRlcnlEZXZpY2VzDQogICAgICAgICAgICAgICAgICAgICAgKTsNCiAgICAgICAgICAgICAgICAgICAgICAvLyBjcmVhdGUgbG9zc1NpZ25hbCBldmVudA0KICAgICAgICAgICAgICAgICAgICAgIGxldCBsb3NzU2lnbmFsRXZlbnQgPSB0aGlzLmxvc3NTaWduYWxDcmVhdGVUZW1wbGF0ZSgNCiAgICAgICAgICAgICAgICAgICAgICAgIG5ld0xvc3NpZ25hbERldmljZXMNCiAgICAgICAgICAgICAgICAgICAgICApOw0KICAgICAgICAgICAgICAgICAgICAgIC8vIGNyZWF0ZSBsb3NzU2lnbmFsIGV2ZW50DQogICAgICAgICAgICAgICAgICAgICAgbGV0IGxvc3NTaWduYWxFdmVudDIgPSB0aGlzLmxvc3NTaWduYWxDcmVhdGVUZW1wbGF0ZTIoDQogICAgICAgICAgICAgICAgICAgICAgICBuZXdMb3NzaWduYWxEZXZpY2VzMg0KICAgICAgICAgICAgICAgICAgICAgICk7DQogICAgICAgICAgICAgICAgICAgICAgLy8gY3JlYXRlIGxvc3NTaWduYWwgZXZlbnQNCiAgICAgICAgICAgICAgICAgICAgICBsZXQgYWJub3JtYWxEZXZpY2VFdmVudCA9IHRoaXMuYWJub3JtYWxEZXZpY2VDcmVhdGVUZW1wbGF0ZSgNCiAgICAgICAgICAgICAgICAgICAgICAgIG5ld0Fibm9ybWFsRGV2aWNlcw0KICAgICAgICAgICAgICAgICAgICAgICk7DQoNCiAgICAgICAgICAgICAgICAgICAgICBjcmVhdGVFdmVudEFyci5wdXNoKGxvd0JldHRlcnlFdmVudCk7DQogICAgICAgICAgICAgICAgICAgICAgY3JlYXRlRXZlbnRBcnIucHVzaChsb3NzU2lnbmFsRXZlbnQpOw0KICAgICAgICAgICAgICAgICAgICAgIGNyZWF0ZUV2ZW50QXJyLnB1c2gobG9zc1NpZ25hbEV2ZW50Mik7DQogICAgICAgICAgICAgICAgICAgICAgY3JlYXRlRXZlbnRBcnIucHVzaChhYm5vcm1hbERldmljZUV2ZW50KTsNCiAgICAgICAgICAgICAgICAgICAgICB0aGlzLiRzZXJ2aWNlLnBvc3ROZXdFdmVudA0KICAgICAgICAgICAgICAgICAgICAgICAgLnNlbmQoY3JlYXRlRXZlbnRBcnIsIG51bGwsIHsNCiAgICAgICAgICAgICAgICAgICAgICAgICAgcmVxdWVzdElEOiByZXF1ZXN0SWQsDQogICAgICAgICAgICAgICAgICAgICAgICAgIHJlcXVlc3RGdW5jdGlvbjogInN5c3RlbVBhcmFtZXRlciIsDQogICAgICAgICAgICAgICAgICAgICAgICAgIHJlcXVlc3RBY3Rpb246ICJVcGRhdGUiDQogICAgICAgICAgICAgICAgICAgICAgICB9KQ0KICAgICAgICAgICAgICAgICAgICAgICAgLnRoZW4oZWRpdFJlcyA9PiB7DQogICAgICAgICAgICAgICAgICAgICAgICAgIHRoaXMuZmlsZVVwbG9hZCh0eXBlKTsNCiAgICAgICAgICAgICAgICAgICAgICAgIH0pOw0KICAgICAgICAgICAgICAgICAgICB9DQogICAgICAgICAgICAgICAgICB9KTsNCiAgICAgICAgICAgICAgfSk7DQogICAgICAgICAgfSk7DQogICAgICAgIH0pOw0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgaWYgKHR5cGUgPT09ICJ1cGRhdGUiKSB7DQogICAgICAgICAgdGhpcy5zdWJtaXRMb2FkaW5nID0gZmFsc2U7DQogICAgICAgIH0NCiAgICAgIH0NCiAgICB9LA0KICAgIGxvc3NTaWduYWxDcmVhdGVUZW1wbGF0ZShkZXZpY2VUeXBlcykgew0KICAgICAgbGV0IGV2ZW50ID0gew0KICAgICAgICBjb2RlOiAiQFNOU0BMb3NzU2lnbmFsIiwNCiAgICAgICAgbmFtZToNCiAgICAgICAgICB0aGlzLiR0KCJMb2NhbGVTdHJpbmcuRDAwMDMzIikgKyAiXyIgKyB0aGlzLiR0KCJMb2NhbGVTdHJpbmcuTDMwMTU4IiksDQogICAgICAgIHNlcnZpY2VDb2RlOiAiTG9zc1NpZ25hbCIsDQogICAgICAgIGFyZ3VtZW50czogWw0KICAgICAgICAgIHsNCiAgICAgICAgICAgIGtleTogInRocmVzaG9sZCIsDQogICAgICAgICAgICB2YWx1ZTogdGhpcy5jb25maWdEYXRhLmxvc3NTaWduYWwudG9TdHJpbmcoKQ0KICAgICAgICAgIH0sDQogICAgICAgICAgeyBrZXk6ICJhdXRvVHJlYXRlZCIsIHZhbHVlOiB0cnVlIH0NCiAgICAgICAgXSwNCiAgICAgICAgc3BvbnNvckRldmljZTogew0KICAgICAgICAgIGRldmljZVR5cGVzOiBkZXZpY2VUeXBlcw0KICAgICAgICB9DQogICAgICB9Ow0KICAgICAgcmV0dXJuIGV2ZW50Ow0KICAgIH0sDQogICAgbG9zc1NpZ25hbENyZWF0ZVRlbXBsYXRlMihkZXZpY2VUeXBlcykgew0KICAgICAgbGV0IGV2ZW50ID0gew0KICAgICAgICBjb2RlOiAiQFNOU0BMb3NzU2lnbmFsMiIsDQogICAgICAgIG5hbWU6DQogICAgICAgICAgdGhpcy4kdCgiTG9jYWxlU3RyaW5nLkQwMDAzMyIpICsgIl8iICsgdGhpcy4kdCgiTG9jYWxlU3RyaW5nLkwzMDE1NyIpLA0KICAgICAgICBzZXJ2aWNlQ29kZTogIkxvc3NTaWduYWwiLA0KICAgICAgICBhcmd1bWVudHM6IFsNCiAgICAgICAgICB7DQogICAgICAgICAgICBrZXk6ICJ0aHJlc2hvbGQiLA0KICAgICAgICAgICAgdmFsdWU6IHRoaXMuY29uZmlnRGF0YS5sb3NzU2lnbmFsMi50b1N0cmluZygpDQogICAgICAgICAgfSwNCiAgICAgICAgICB7IGtleTogImF1dG9UcmVhdGVkIiwgdmFsdWU6IHRydWUgfQ0KICAgICAgICBdLA0KICAgICAgICBzcG9uc29yRGV2aWNlOiB7DQogICAgICAgICAgZGV2aWNlVHlwZXM6IGRldmljZVR5cGVzDQogICAgICAgIH0NCiAgICAgIH07DQogICAgICByZXR1cm4gZXZlbnQ7DQogICAgfSwNCiAgICBsb3dCYXR0ZXJ5Q3JlYXRlVGVtcGxhdGUoZGV2aWNlVHlwZXMpIHsNCiAgICAgIGxldCBldmVudCA9IHsNCiAgICAgICAgY29kZTogIkBTTlNATG93QmF0dGVyeSIsDQogICAgICAgIG5hbWU6IHRoaXMuJHQoIkxvY2FsZVN0cmluZy5EMDAwMzQiKSwNCiAgICAgICAgc2VydmljZUNvZGU6ICJMb3dCYXR0ZXJ5IiwNCiAgICAgICAgYXJndW1lbnRzOiBbDQogICAgICAgICAgew0KICAgICAgICAgICAga2V5OiAidGhyZXNob2xkIiwNCiAgICAgICAgICAgIHZhbHVlOiB0aGlzLmNvbmZpZ0RhdGEubG93QmF0dGVyeS50b1N0cmluZygpDQogICAgICAgICAgfSwNCiAgICAgICAgICB7IGtleTogImF1dG9UcmVhdGVkIiwgdmFsdWU6IHRydWUgfQ0KICAgICAgICBdLA0KICAgICAgICBzcG9uc29yRGV2aWNlOiB7DQogICAgICAgICAgZGV2aWNlVHlwZXM6IGRldmljZVR5cGVzDQogICAgICAgIH0NCiAgICAgIH07DQogICAgICByZXR1cm4gZXZlbnQ7DQogICAgfSwNCiAgICBhYm5vcm1hbERldmljZUNyZWF0ZVRlbXBsYXRlKGRldmljZVR5cGVzKSB7DQogICAgICBsZXQgZXZlbnQgPSB7DQogICAgICAgIGNvZGU6ICJAU05TQEFibm9ybWFsRGV2aWNlIiwNCiAgICAgICAgbmFtZTogdGhpcy4kdCgiTG9jYWxlU3RyaW5nLkQwMDAyNiIpLA0KICAgICAgICBzZXJ2aWNlQ29kZTogIkFibm9ybWFsRGV2aWNlIiwNCiAgICAgICAgYXJndW1lbnRzOiBbeyBrZXk6ICJhdXRvVHJlYXRlZCIsIHZhbHVlOiB0cnVlIH1dLA0KICAgICAgICBzcG9uc29yRGV2aWNlOiB7DQogICAgICAgICAgZGV2aWNlVHlwZXM6IGRldmljZVR5cGVzDQogICAgICAgIH0NCiAgICAgIH07DQogICAgICByZXR1cm4gZXZlbnQ7DQogICAgfSwNCiAgICBnZXRFdmVudFBpZHModHlwZSwgb2JqZWN0UmVzcG9uc2UpIHsNCiAgICAgIGxldCBwaWRzID0gW107DQogICAgICBpZiAob2JqZWN0UmVzcG9uc2UucmVzdWx0cy5sZW5ndGggPiAwKSB7DQogICAgICAgIG9iamVjdFJlc3BvbnNlLnJlc3VsdHMuZm9yRWFjaChvYmogPT4gew0KICAgICAgICAgIGlmIChvYmouZGV2aWNlcykgew0KICAgICAgICAgICAgb2JqLmRldmljZXMuZm9yRWFjaChkID0+IHsNCiAgICAgICAgICAgICAgaWYgKA0KICAgICAgICAgICAgICAgIHR5cGUgPT09ICJMb3NzU2lnbmFsIiAmJg0KICAgICAgICAgICAgICAgIHRoaXMuY29uZmlnRGF0YS5sb3NzU2lnbmFsRGV2aWNlcy5pbmNsdWRlcyhkLnR5cGUpDQogICAgICAgICAgICAgICkgew0KICAgICAgICAgICAgICAgIHBpZHMucHVzaChkLnBpZCk7DQogICAgICAgICAgICAgIH0NCiAgICAgICAgICAgICAgaWYgKA0KICAgICAgICAgICAgICAgIHR5cGUgPT09ICJMb3NzU2lnbmFsMiIgJiYNCiAgICAgICAgICAgICAgICB0aGlzLmNvbmZpZ0RhdGEubG9zc1NpZ25hbERldmljZXMyLmluY2x1ZGVzKGQudHlwZSkNCiAgICAgICAgICAgICAgKSB7DQogICAgICAgICAgICAgICAgcGlkcy5wdXNoKGQucGlkKTsNCiAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICBpZiAoDQogICAgICAgICAgICAgICAgdHlwZSA9PT0gIkxvd0JhdHRlcnkiICYmDQogICAgICAgICAgICAgICAgdGhpcy5jb25maWdEYXRhLmxvd0JhdHRlcnlEZXZpY2VzLmluY2x1ZGVzKGQudHlwZSkNCiAgICAgICAgICAgICAgKSB7DQogICAgICAgICAgICAgICAgcGlkcy5wdXNoKGQucGlkKTsNCiAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICBpZiAoDQogICAgICAgICAgICAgICAgdHlwZSA9PT0gIkFibm9ybWFsRGV2aWNlIiAmJg0KICAgICAgICAgICAgICAgIHRoaXMuY29uZmlnRGF0YS5hYm5vcm1hbERldmljZXMuaW5jbHVkZXMoZC50eXBlKQ0KICAgICAgICAgICAgICApIHsNCiAgICAgICAgICAgICAgICBwaWRzLnB1c2goZC5waWQpOw0KICAgICAgICAgICAgICB9DQogICAgICAgICAgICB9KTsNCiAgICAgICAgICB9DQogICAgICAgIH0pOw0KICAgICAgfQ0KICAgICAgcmV0dXJuIHBpZHM7DQogICAgfSwNCiAgICBhc3luYyBmaWxlVXBsb2FkKHR5cGUpIHsNCiAgICAgIGlmICh0aGlzLmltZ0ZpbGUgIT0gbnVsbCkgew0KICAgICAgICBsZXQgaW1nUGFyYW1zID0gWyJAU05TQExvZ28iXTsNCiAgICAgICAgbGV0IGltZ0RhdGEgPSB7DQogICAgICAgICAgaW1hZ2U6IHRoaXMuaW1nRmlsZQ0KICAgICAgICB9Ow0KICAgICAgICBsZXQgcmVzMiA9IGF3YWl0IHRoaXMuJHNlcnZpY2UuZWRpdFBPQ0dyb3Vwc0ltYWdlLmZpbGVVcGxvYWQoDQogICAgICAgICAgaW1nRGF0YSwNCiAgICAgICAgICBpbWdQYXJhbXMNCiAgICAgICAgKTsNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIGlmICghdGhpcy5zaG93Q2xlYW5JY29uICYmICF0aGlzLmltZ0ZpbGVVUkwpIHsNCiAgICAgICAgICBsZXQgcGFyYW1zID0gIkBTTlNATG9nbyI7DQogICAgICAgICAgbGV0IHJlcyA9IGF3YWl0IHRoaXMuJHNlcnZpY2UuZGVsZXRlUE9DR3JvdXBzSW1hZ2Uuc2VuZChwYXJhbXMpOw0KICAgICAgICAgIGxvY2FsU3RvcmFnZS5yZW1vdmVJdGVtKCJzbnNfbG9nb1VSTCIpOw0KICAgICAgICB9DQogICAgICB9DQogICAgICBpZiAodGhpcy5maWxlICE9IG51bGwpIHsNCiAgICAgICAgbGV0IGRhdGEgPSB7DQogICAgICAgICAgaW1hZ2U6IHRoaXMuZmlsZQ0KICAgICAgICB9Ow0KICAgICAgICBsZXQgcGFyYW1zID0gWyJAU05TQFNvdW5kIl07DQogICAgICAgIGxldCByZXMgPSBhd2FpdCB0aGlzLiRzZXJ2aWNlLmVkaXRQT0NHcm91cHNNdXNpYy5maWxlVXBsb2FkKA0KICAgICAgICAgIGRhdGEsDQogICAgICAgICAgcGFyYW1zDQogICAgICAgICk7DQogICAgICB9DQogICAgICB0aGlzLmdvRGVmYXVsdFBhZ2UodHlwZSk7DQogICAgfSwNCiAgICBnb0RlZmF1bHRQYWdlKHR5cGUpIHsNCiAgICAgIGlmICh0aGlzLm9yaVRhYiAhPSB0aGlzLmNvbmZpZ0RhdGEuZGVmYXVsdFRhYikgew0KICAgICAgICAvL+mHjeWwjuWQkQ0KICAgICAgICB0aGlzLiRyb3V0ZXIucHVzaCh7DQogICAgICAgICAgcGF0aDogZGVjb2RlVVJJQ29tcG9uZW50KA0KICAgICAgICAgICAgIi9hZG1pbmlzdHJhdGl2ZS9hcHBzL3Nucy8iICsgdGhpcy5jb25maWdEYXRhLmRlZmF1bHRUYWINCiAgICAgICAgICApIC8vIOWwjumggeiHs+mgkOioremggemdog0KICAgICAgICB9KTsNCiAgICAgIH0NCiAgICAgIHNldFRpbWVvdXQoKCkgPT4gew0KICAgICAgICB0aGlzLiRzdG9yZS5jb21taXQoInNldFBPQ0NvbmZpZ0NoYW5nZWQiLCBtb21lbnQoKS52YWx1ZU9mKCkpOw0KICAgICAgICBpZiAodHlwZSAhPSAibmV3Iikgew0KICAgICAgICAgIHRoaXMuc3VibWl0TG9hZGluZyA9IGZhbHNlOw0KICAgICAgICAgIHRoaXMuJGVtaXQoImNsb3NlU3lzdGVtQ29uZmlnIik7DQogICAgICAgIH0NCiAgICAgIH0sIDExMDApOw0KICAgIH0sDQogICAgdXBkYXRlVGhpcmRQYXJ0eVZhbHVlKGRhdGEpIHsNCiAgICAgIHRoaXMudGhpcmRQYXJ0eUNvbmZpZ0xpc3QuZm9yRWFjaChkID0+IHsNCiAgICAgICAgc3dpdGNoIChkLnNlY3Rpb24pIHsNCiAgICAgICAgICBjYXNlIDE6DQogICAgICAgICAgICBkLnZhbHVlID0gZGF0YS5sb2dpblRoaXJkUGFydHk7DQogICAgICAgICAgICB0aGlzLnZpZXdMb2dpblRoaXJkUGFydHkgPQ0KICAgICAgICAgICAgICBkYXRhLmxvZ2luVGhpcmRQYXJ0eSA9PSB0cnVlDQogICAgICAgICAgICAgICAgPyB0aGlzLiR0KCJMb2NhbGVTdHJpbmcuQjIwMDA5IikNCiAgICAgICAgICAgICAgICA6IHRoaXMuJHQoIkxvY2FsZVN0cmluZy5CMjAwMTAiKTsNCiAgICAgICAgICAgIGJyZWFrOw0KICAgICAgICAgIGNhc2UgMjoNCiAgICAgICAgICAgIGQudmFsdWUgPSBkYXRhLnVybDsNCiAgICAgICAgICAgIGJyZWFrOw0KICAgICAgICAgIGNhc2UgMzoNCiAgICAgICAgICAgIGQudmFsdWUgPSBkYXRhLnVzZXJOYW1lOw0KICAgICAgICAgICAgYnJlYWs7DQogICAgICAgICAgY2FzZSA0Og0KICAgICAgICAgICAgZC52YWx1ZSA9IGRhdGEuc2VjcmV0Ow0KICAgICAgICAgICAgYnJlYWs7DQogICAgICAgIH0NCiAgICAgIH0pOw0KDQogICAgICAvLyBkLnZpZXdTdHVja1BsYW5lID0NCiAgICAgIC8vICAgICAgICAgZGF0YS5zdHVja1BsYW5lID09IHRydWUNCiAgICAgIC8vICAgICAgICAgICA/IHRoaXMuJHQoIkxvY2FsZVN0cmluZy5CMjAwMDkiKQ0KICAgICAgLy8gICAgICAgICAgIDogdGhpcy4kdCgiTG9jYWxlU3RyaW5nLkIyMDAxMCIpOw0KICAgIH0sDQogICAgdXBkYXRlVmFsdWUoZGF0YSkgew0KICAgICAgbGV0IHZpZXdNMkxpc3RBcnIgPSBbXTsNCiAgICAgIGxldCB2aWV3VGFiQXJyID0gW107DQogICAgICBsZXQgdmlld0xpY2Vuc2VBcnIgPSBbXTsNCiAgICAgIGRhdGEubTJMaXN0LmZvckVhY2goZCA9PiB7DQogICAgICAgIGxldCB2ID0gdGhpcy5zaG93aW5nVHlwZS5maW5kKHQgPT4gdC52YWx1ZSA9PSBkKS5sYWJlbDsNCiAgICAgICAgdmlld00yTGlzdEFyci5wdXNoKHYpOw0KICAgICAgfSk7DQogICAgICB0aGlzLnZpZXdNMkxpc3QgPSB2aWV3TTJMaXN0QXJyLmpvaW4oIiwgIik7DQoNCiAgICAgIGRhdGEudGFiTGlzdC5mb3JFYWNoKGQgPT4gew0KICAgICAgICBsZXQgdiA9IHRoaXMuc2hvd2luZ1RhYi5maW5kKHQgPT4gdC52YWx1ZSA9PSBkKS5sYWJlbDsNCiAgICAgICAgdmlld1RhYkFyci5wdXNoKHYpOw0KICAgICAgfSk7DQoNCiAgICAgIGRhdGEubGljZW5zZS5mb3JFYWNoKGQgPT4gew0KICAgICAgICBsZXQgdiA9IHRoaXMuc2VydmljZUNvZGVMaXN0LmZpbmQodCA9PiB0LmtleSA9PSBkKS52YWx1ZTsNCiAgICAgICAgdmlld0xpY2Vuc2VBcnIucHVzaCh2KTsNCiAgICAgIH0pOw0KDQogICAgICB0aGlzLmljb25TaXplVGl0bGUgPSB0aGlzLmljb25TaXplTGlzdC5maW5kKA0KICAgICAgICB0ID0+IHQudmFsdWUgPT0gZGF0YS5pY29uU2l6ZQ0KICAgICAgKS5sYWJlbDsNCiAgICAgIHRoaXMuZGVmYXVsdFBhZ2VTaXplVGl0bGUgPQ0KICAgICAgICBkYXRhLmRlZmF1bHRQYWdlU2l6ZSA9PSAiIg0KICAgICAgICAgID8gIiINCiAgICAgICAgICA6IHRoaXMucGFnZVNpemVMaXN0LmZpbmQodCA9PiB0LnZhbHVlID09IGRhdGEuZGVmYXVsdFBhZ2VTaXplKS5sYWJlbDsNCiAgICAgIHRoaXMubTVPYmplY3RUeXBlVGl0bGUgPQ0KICAgICAgICBkYXRhLm01T2JqZWN0VHlwZSA9PSAiIg0KICAgICAgICAgID8gIiINCiAgICAgICAgICA6IHRoaXMubTVPYmplY3RUeXBlTGlzdC5maW5kKHQgPT4gdC52YWx1ZSA9PSBkYXRhLm01T2JqZWN0VHlwZSkubGFiZWw7DQogICAgICB0aGlzLmRlZmF1bHRUYWJUaXRsZSA9IHRoaXMuc2hvd2luZ1RhYi5maW5kKA0KICAgICAgICB0ID0+IHQudmFsdWUgPT0gZGF0YS5kZWZhdWx0VGFiDQogICAgICApLmxhYmVsOw0KDQogICAgICB0aGlzLnZpZXdUYWIgPSB2aWV3VGFiQXJyLmpvaW4oIiwgIik7DQogICAgICB0aGlzLnZpZXdMaWNlbnNlID0gdmlld0xpY2Vuc2VBcnIuam9pbigiLCAiKTsNCiAgICAgIHRoaXMudmlld1BsYXlTb3VuZCA9DQogICAgICAgIGxvY2FsU3RvcmFnZS5nZXRJdGVtKCJzbnNfYWxlcnRTb3VuZCIpID09ICJ0cnVlIg0KICAgICAgICAgID8gdGhpcy4kdCgiTG9jYWxlU3RyaW5nLkIyMDAwOSIpDQogICAgICAgICAgOiB0aGlzLiR0KCJMb2NhbGVTdHJpbmcuQjIwMDEwIik7DQogICAgICB0aGlzLnZpZXdNMkxpbmUgPQ0KICAgICAgICBsb2NhbFN0b3JhZ2UuZ2V0SXRlbSgic25zX20yTGluZSIpID09ICJ0cnVlIg0KICAgICAgICAgID8gdGhpcy4kdCgiTG9jYWxlU3RyaW5nLkIyMDAwOSIpDQogICAgICAgICAgOiB0aGlzLiR0KCJMb2NhbGVTdHJpbmcuQjIwMDEwIik7DQogICAgICB0aGlzLmRhdGEuZm9yRWFjaChkID0+IHsNCiAgICAgICAgc3dpdGNoIChkLnNlY3Rpb24pIHsNCiAgICAgICAgICBjYXNlIDA6DQogICAgICAgICAgICBkLnZhbHVlID0gZGF0YS5zeXN0ZW1OYW1lOw0KICAgICAgICAgICAgYnJlYWs7DQogICAgICAgICAgY2FzZSAxOg0KICAgICAgICAgICAgZC52YWx1ZSA9IHRoaXMudmlld1RhYjsNCiAgICAgICAgICAgIGJyZWFrOw0KICAgICAgICAgIGNhc2UgMjoNCiAgICAgICAgICAgIGQudmFsdWUgPSB0aGlzLnZpZXdQbGF5U291bmQ7DQogICAgICAgICAgICBkLnZhbHVlVGltZSA9IGRhdGEucmVwZWF0U291bmQ7DQogICAgICAgICAgICBicmVhazsNCiAgICAgICAgICBjYXNlIDM6DQogICAgICAgICAgICBkLnZhbHVlID0gdGhpcy52aWV3TTJMaXN0Ow0KICAgICAgICAgICAgYnJlYWs7DQogICAgICAgICAgY2FzZSA0Og0KICAgICAgICAgICAgZC52YWx1ZSA9IGRhdGEuaWNvblNpemU7DQogICAgICAgICAgICBicmVhazsNCiAgICAgICAgICBjYXNlIDU6DQogICAgICAgICAgICBkLnZhbHVlID0gZGF0YS5kZWZhdWx0VGFiOw0KICAgICAgICAgICAgYnJlYWs7DQogICAgICAgICAgY2FzZSA2Og0KICAgICAgICAgICAgZC52YWx1ZSA9IGRhdGEudXBkYXRlU2Vjb25kczsNCiAgICAgICAgICAgIGJyZWFrOw0KICAgICAgICAgIGNhc2UgNzoNCiAgICAgICAgICAgIGQudmFsdWUgPSBkYXRhLmdyYXlUaW1lICogMTsNCiAgICAgICAgICAgIGJyZWFrOw0KICAgICAgICAgIGNhc2UgODoNCiAgICAgICAgICAgIGQudmFsdWUgPSB0aGlzLnZpZXdNMkxpbmU7DQogICAgICAgICAgICBicmVhazsNCiAgICAgICAgICBjYXNlIDk6DQogICAgICAgICAgICBkLnZhbHVlID0NCiAgICAgICAgICAgICAgZGF0YS5ldmVudEF1dGhDbG9zZSA9PSB0cnVlDQogICAgICAgICAgICAgICAgPyB0aGlzLiR0KCJMb2NhbGVTdHJpbmcuQjIwMDA5IikNCiAgICAgICAgICAgICAgICA6IHRoaXMuJHQoIkxvY2FsZVN0cmluZy5CMjAwMTAiKTsNCiAgICAgICAgICAgIGQuYm9vbGVhbiA9IGRhdGEuZXZlbnRBdXRoQ2xvc2UgPT0gdHJ1ZSA/IHRydWUgOiBmYWxzZTsNCiAgICAgICAgICAgIGJyZWFrOw0KICAgICAgICAgIGNhc2UgMTA6DQogICAgICAgICAgICBkLnZhbHVlID0gZGF0YS5ldmVudEtlZXBEYXlzOw0KICAgICAgICAgICAgYnJlYWs7DQogICAgICAgICAgY2FzZSAxMToNCiAgICAgICAgICAgIGQudmFsdWUgPSBkYXRhLmluZmx1eFJhbmdlOw0KICAgICAgICAgICAgYnJlYWs7DQogICAgICAgICAgY2FzZSAxMjoNCiAgICAgICAgICAgIGQudmFsdWUgPSBkYXRhLmxvc3NTaWduYWw7DQogICAgICAgICAgICBkLnZhbHVlRGV2aWNlcyA9IHRoaXMudHJhbnNmb3JtTmFtZSgNCiAgICAgICAgICAgICAgZGF0YS5sb3NzU2lnbmFsRGV2aWNlcywNCiAgICAgICAgICAgICAgImxvc3NTaWduYWwiDQogICAgICAgICAgICApOw0KICAgICAgICAgICAgYnJlYWs7DQogICAgICAgICAgY2FzZSAyMjoNCiAgICAgICAgICAgIGQudmFsdWUgPSBkYXRhLmxvc3NTaWduYWwyOw0KICAgICAgICAgICAgZC52YWx1ZURldmljZXMgPSB0aGlzLnRyYW5zZm9ybU5hbWUoDQogICAgICAgICAgICAgIGRhdGEubG9zc1NpZ25hbERldmljZXMyLA0KICAgICAgICAgICAgICAibG9zc1NpZ25hbDIiDQogICAgICAgICAgICApOw0KICAgICAgICAgICAgYnJlYWs7DQogICAgICAgICAgY2FzZSAxMzoNCiAgICAgICAgICAgIGQudmFsdWUgPSBkYXRhLmxvd0JhdHRlcnk7DQogICAgICAgICAgICBkLnZhbHVlRGV2aWNlcyA9IHRoaXMudHJhbnNmb3JtTmFtZSgNCiAgICAgICAgICAgICAgZGF0YS5sb3dCYXR0ZXJ5RGV2aWNlcywNCiAgICAgICAgICAgICAgImxvd0JhdHRlcnkiDQogICAgICAgICAgICApOw0KICAgICAgICAgICAgYnJlYWs7DQogICAgICAgICAgY2FzZSAxNDoNCiAgICAgICAgICAgIGQudmFsdWUgPSBkYXRhLnNraXBFdmVudDsNCiAgICAgICAgICAgIGQudmFsdWVTa2lwRXZlbnQgPSB0aGlzLnRyYW5zZm9ybU5hbWUoZGF0YS5za2lwRXZlbnQsICJza2lwRXZlbnQiKTsNCiAgICAgICAgICAgIGJyZWFrOw0KICAgICAgICAgIGNhc2UgMTU6DQogICAgICAgICAgICBkLnZhbHVlID0gZGF0YS5hYm5vcm1hbERldmljZTsNCiAgICAgICAgICAgIGQudmFsdWVEZXZpY2VzID0gdGhpcy50cmFuc2Zvcm1OYW1lKA0KICAgICAgICAgICAgICBkYXRhLmFibm9ybWFsRGV2aWNlcywNCiAgICAgICAgICAgICAgImFibm9ybWFsRGV2aWNlIg0KICAgICAgICAgICAgKTsNCiAgICAgICAgICAgIGJyZWFrOw0KICAgICAgICAgIGNhc2UgMTY6DQogICAgICAgICAgICBkLnZhbHVlID0gZGF0YS5kZXZpY2VTZWxlY3RMaXN0Ow0KICAgICAgICAgICAgZC52YWx1ZURldmljZVNlbGVjdExpc3QgPSB0aGlzLnRyYW5zZm9ybU5hbWUoDQogICAgICAgICAgICAgIGRhdGEuZGV2aWNlU2VsZWN0TGlzdCwNCiAgICAgICAgICAgICAgImRldmljZVNlbGVjdExpc3QiDQogICAgICAgICAgICApOw0KICAgICAgICAgICAgYnJlYWs7DQogICAgICAgICAgY2FzZSAxNzoNCiAgICAgICAgICAgIGQudmFsdWUgPSB0aGlzLnZpZXdMb2dvOw0KICAgICAgICAgICAgYnJlYWs7DQogICAgICAgICAgY2FzZSAxODoNCiAgICAgICAgICAgIGQudmlld011bHRpUGxhbmVzID0NCiAgICAgICAgICAgICAgZGF0YS5tdWx0aVBsYW5lcyA9PSB0cnVlDQogICAgICAgICAgICAgICAgPyB0aGlzLiR0KCJMb2NhbGVTdHJpbmcuQjIwMDA5IikNCiAgICAgICAgICAgICAgICA6IHRoaXMuJHQoIkxvY2FsZVN0cmluZy5CMjAwMTAiKTsNCiAgICAgICAgICAgIGQubXVsdGlQbGFuZXMgPSBkYXRhLm11bHRpUGxhbmVzID09IHRydWUgPyB0cnVlIDogZmFsc2U7DQogICAgICAgICAgICBkLnZhbHVlID0gZGF0YS5zZWxlY3RlZFBsYW5lczsNCiAgICAgICAgICAgIGQudmFsdWVQbGFuZVNlbGVjdExpc3QgPSB0aGlzLnRyYW5zZm9ybVBsYW5lTmFtZSgNCiAgICAgICAgICAgICAgZGF0YS5zZWxlY3RlZFBsYW5lcw0KICAgICAgICAgICAgKTsNCiAgICAgICAgICAgIGJyZWFrOw0KICAgICAgICAgIGNhc2UgMTk6DQogICAgICAgICAgICBkLnZhbHVlID0gZGF0YS5tMUxvbmdQcmVzczsNCiAgICAgICAgICAgIGQudmFsdWVNMUxvbmdQcmVzc0xpc3QgPSB0aGlzLnRyYW5zZm9ybU5hbWUoDQogICAgICAgICAgICAgIGRhdGEubTFMb25nUHJlc3MsDQogICAgICAgICAgICAgICJtMUxvbmdQcmVzcyINCiAgICAgICAgICAgICk7DQogICAgICAgICAgICBicmVhazsNCiAgICAgICAgICBjYXNlIDIwOg0KICAgICAgICAgICAgZC52YWx1ZSA9IGRhdGEuZGVmYXVsdFBhZ2VTaXplOw0KICAgICAgICAgICAgYnJlYWs7DQogICAgICAgICAgY2FzZSAyMToNCiAgICAgICAgICAgIGQudmFsdWUgPSBkYXRhLm01T2JqZWN0VHlwZTsNCiAgICAgICAgICAgIGJyZWFrOw0KICAgICAgICAgIGNhc2UgMjM6DQogICAgICAgICAgICBkLnZhbHVlID0gdGhpcy52aWV3TGljZW5zZTsNCiAgICAgICAgICAgIGJyZWFrOw0KICAgICAgICAgIGNhc2UgMjQ6DQogICAgICAgICAgICBkLnZhbHVlID0NCiAgICAgICAgICAgICAgZGF0YS5zaG93TGVhdmVCZWQgPT0gdHJ1ZQ0KICAgICAgICAgICAgICAgID8gdGhpcy4kdCgiTG9jYWxlU3RyaW5nLkIyMDAwOSIpDQogICAgICAgICAgICAgICAgOiB0aGlzLiR0KCJMb2NhbGVTdHJpbmcuQjIwMDEwIik7DQogICAgICAgICAgICBicmVhazsNCiAgICAgICAgICBjYXNlIDI1Og0KICAgICAgICAgICAgZC52YWx1ZSA9IGRhdGEubTJTdGF0aXN0aWM7DQogICAgICAgICAgICBicmVhazsNCiAgICAgICAgICBjYXNlIDI2Og0KICAgICAgICAgICAgZC52YWx1ZSA9IGRhdGEubTNTdGF0aXN0aWM7DQogICAgICAgICAgICBicmVhazsNCiAgICAgICAgICBjYXNlIDI3Og0KICAgICAgICAgICAgZC52YWx1ZSA9IGRhdGEubTRTdGF0aXN0aWM7DQogICAgICAgICAgICBicmVhazsNCiAgICAgICAgICBjYXNlIDI4Og0KICAgICAgICAgICAgZC52YWx1ZSA9IGRhdGEubThTdGF0aXN0aWM7DQogICAgICAgICAgICBicmVhazsNCiAgICAgICAgICBjYXNlIDI5Og0KICAgICAgICAgICAgZC52YWx1ZSA9IGRhdGEubTlTdGF0aXN0aWM7DQogICAgICAgICAgICBicmVhazsNCiAgICAgICAgICBjYXNlIDMwOg0KICAgICAgICAgICAgZC52aWV3U3R1Y2tQbGFuZSA9DQogICAgICAgICAgICAgIGRhdGEuc3R1Y2tQbGFuZSA9PSB0cnVlDQogICAgICAgICAgICAgICAgPyB0aGlzLiR0KCJMb2NhbGVTdHJpbmcuQjIwMDA5IikNCiAgICAgICAgICAgICAgICA6IHRoaXMuJHQoIkxvY2FsZVN0cmluZy5CMjAwMTAiKTsNCiAgICAgICAgICAgIGQuc3R1Y2tQbGFuZSA9IGRhdGEuc3R1Y2tQbGFuZSA9PSB0cnVlID8gdHJ1ZSA6IGZhbHNlOw0KICAgICAgICAgICAgYnJlYWs7DQogICAgICAgICAgY2FzZSAzMToNCiAgICAgICAgICAgIGQudmFsdWUgPSBkYXRhLmxvZ291dFRpbWU7DQogICAgICAgICAgICBicmVhazsNCiAgICAgICAgICBjYXNlIDMyOg0KICAgICAgICAgICAgZC52YWx1ZSA9IGRhdGEuc3RhdGVDb2xvcjsNCiAgICAgICAgICAgIGJyZWFrOw0KICAgICAgICB9DQogICAgICB9KTsNCiAgICB9LA0KICAgIHRyYW5zZm9ybU5hbWUoZGV2aWNlQXJyYXksIHR5cGUpIHsNCiAgICAgIGxldCB0ZW1wRGF0YSA9IFtdOw0KICAgICAgbGV0IGRldmljZVR5cGUgPSAiIjsNCiAgICAgIGlmIChkZXZpY2VBcnJheSAmJiBkZXZpY2VBcnJheS5sZW5ndGggPiAwKSB7DQogICAgICAgIGlmIChkZXZpY2VBcnJheS5sZW5ndGggPT0gMSAmJiBkZXZpY2VBcnJheVswXSA9PT0gIiIpIHsNCiAgICAgICAgICByZXR1cm4gIiI7DQogICAgICAgIH0NCiAgICAgICAgc3dpdGNoICh0eXBlKSB7DQogICAgICAgICAgY2FzZSAibG9zc1NpZ25hbCI6DQogICAgICAgICAgICB0cnkgew0KICAgICAgICAgICAgICBkZXZpY2VBcnJheS5mb3JFYWNoKGl0ZW0gPT4gew0KICAgICAgICAgICAgICAgIGRldmljZVR5cGUgPSBpdGVtOw0KICAgICAgICAgICAgICAgIGxldCBkID0gdGhpcy5kZXZpY2VMaXN0TG9zc1NpZ25hbC5maW5kKGRhdGEgPT4gZGF0YS5rZXkgPT0gaXRlbSkNCiAgICAgICAgICAgICAgICAgIC52YWx1ZTsNCiAgICAgICAgICAgICAgICB0ZW1wRGF0YS5wdXNoKGQpOw0KICAgICAgICAgICAgICB9KTsNCiAgICAgICAgICAgIH0gY2F0Y2ggKGUpIHsNCiAgICAgICAgICAgICAgdGVtcERhdGEucHVzaChkZXZpY2VUeXBlKTsNCiAgICAgICAgICAgIH0NCg0KICAgICAgICAgICAgYnJlYWs7DQogICAgICAgICAgY2FzZSAibG9zc1NpZ25hbDIiOg0KICAgICAgICAgICAgdHJ5IHsNCiAgICAgICAgICAgICAgZGV2aWNlQXJyYXkuZm9yRWFjaChpdGVtID0+IHsNCiAgICAgICAgICAgICAgICBkZXZpY2VUeXBlID0gaXRlbTsNCiAgICAgICAgICAgICAgICBsZXQgZCA9IHRoaXMuZGV2aWNlTGlzdExvc3NTaWduYWwyLmZpbmQoDQogICAgICAgICAgICAgICAgICBkYXRhID0+IGRhdGEua2V5ID09IGl0ZW0NCiAgICAgICAgICAgICAgICApLnZhbHVlOw0KICAgICAgICAgICAgICAgIHRlbXBEYXRhLnB1c2goZCk7DQogICAgICAgICAgICAgIH0pOw0KICAgICAgICAgICAgfSBjYXRjaCAoZSkgew0KICAgICAgICAgICAgICB0ZW1wRGF0YS5wdXNoKGRldmljZVR5cGUpOw0KICAgICAgICAgICAgfQ0KDQogICAgICAgICAgICBicmVhazsNCiAgICAgICAgICBjYXNlICJsb3dCYXR0ZXJ5IjoNCiAgICAgICAgICAgIHRyeSB7DQogICAgICAgICAgICAgIGRldmljZUFycmF5LmZvckVhY2goaXRlbSA9PiB7DQogICAgICAgICAgICAgICAgZGV2aWNlVHlwZSA9IGl0ZW07DQogICAgICAgICAgICAgICAgbGV0IGQgPSB0aGlzLmRldmljZUxpc3RMb3dCYXR0ZXJ5LmZpbmQoZGF0YSA9PiBkYXRhLmtleSA9PSBpdGVtKQ0KICAgICAgICAgICAgICAgICAgLnZhbHVlOw0KICAgICAgICAgICAgICAgIHRlbXBEYXRhLnB1c2goZCk7DQogICAgICAgICAgICAgIH0pOw0KICAgICAgICAgICAgfSBjYXRjaCAoZSkgew0KICAgICAgICAgICAgICB0ZW1wRGF0YS5wdXNoKGRldmljZVR5cGUpOw0KICAgICAgICAgICAgfQ0KICAgICAgICAgICAgYnJlYWs7DQogICAgICAgICAgY2FzZSAiYWJub3JtYWxEZXZpY2UiOg0KICAgICAgICAgICAgdHJ5IHsNCiAgICAgICAgICAgICAgZGV2aWNlQXJyYXkuZm9yRWFjaChpdGVtID0+IHsNCiAgICAgICAgICAgICAgICBkZXZpY2VUeXBlID0gaXRlbTsNCiAgICAgICAgICAgICAgICBsZXQgZCA9IHRoaXMuZGV2aWNlTGlzdEFibm9ybWFsRGV2aWNlLmZpbmQoDQogICAgICAgICAgICAgICAgICBkYXRhID0+IGRhdGEua2V5ID09IGl0ZW0NCiAgICAgICAgICAgICAgICApLnZhbHVlOw0KICAgICAgICAgICAgICAgIHRlbXBEYXRhLnB1c2goZCk7DQogICAgICAgICAgICAgIH0pOw0KICAgICAgICAgICAgfSBjYXRjaCAoZSkgew0KICAgICAgICAgICAgICB0ZW1wRGF0YS5wdXNoKGRldmljZVR5cGUpOw0KICAgICAgICAgICAgfQ0KICAgICAgICAgICAgYnJlYWs7DQogICAgICAgICAgY2FzZSAic2tpcEV2ZW50IjoNCiAgICAgICAgICAgIGRldmljZUFycmF5LmZvckVhY2goaXRlbSA9PiB7DQogICAgICAgICAgICAgIGxldCBkID0gdGhpcy5ldmVudFNlcnZpY2VDb2RlTGlzdC5maW5kKGRhdGEgPT4gZGF0YS5rZXkgPT0gaXRlbSkNCiAgICAgICAgICAgICAgICAudmFsdWU7DQogICAgICAgICAgICAgIHRlbXBEYXRhLnB1c2goZCk7DQogICAgICAgICAgICB9KTsNCiAgICAgICAgICAgIGJyZWFrOw0KICAgICAgICAgIGNhc2UgImRldmljZVNlbGVjdExpc3QiOg0KICAgICAgICAgICAgdHJ5IHsNCiAgICAgICAgICAgICAgZGV2aWNlQXJyYXkuZm9yRWFjaChpdGVtID0+IHsNCiAgICAgICAgICAgICAgICBkZXZpY2VUeXBlID0gaXRlbTsNCiAgICAgICAgICAgICAgICBsZXQgZCA9IHRoaXMuZGV2aWNlRGVmYXVsdFNlbGVjdGlvbi5maW5kKA0KICAgICAgICAgICAgICAgICAgZGF0YSA9PiBkYXRhLmtleSA9PSBpdGVtDQogICAgICAgICAgICAgICAgKS52YWx1ZTsNCiAgICAgICAgICAgICAgICB0ZW1wRGF0YS5wdXNoKGQpOw0KICAgICAgICAgICAgICB9KTsNCiAgICAgICAgICAgIH0gY2F0Y2ggKGUpIHsNCiAgICAgICAgICAgICAgdGVtcERhdGEucHVzaChkZXZpY2VUeXBlKTsNCiAgICAgICAgICAgIH0NCiAgICAgICAgICAgIGJyZWFrOw0KICAgICAgICAgIGNhc2UgIm0xTG9uZ1ByZXNzIjoNCiAgICAgICAgICAgIHRyeSB7DQogICAgICAgICAgICAgIGRldmljZUFycmF5LmZvckVhY2goaXRlbSA9PiB7DQogICAgICAgICAgICAgICAgZGV2aWNlVHlwZSA9IGl0ZW07DQogICAgICAgICAgICAgICAgbGV0IGQgPSB0aGlzLmRldmljZURlZmF1bHRTZWxlY3Rpb24uZmluZCgNCiAgICAgICAgICAgICAgICAgIGRhdGEgPT4gZGF0YS5rZXkgPT0gaXRlbQ0KICAgICAgICAgICAgICAgICkudmFsdWU7DQogICAgICAgICAgICAgICAgdGVtcERhdGEucHVzaChkKTsNCiAgICAgICAgICAgICAgfSk7DQogICAgICAgICAgICB9IGNhdGNoIChlKSB7DQogICAgICAgICAgICAgIHRlbXBEYXRhLnB1c2goZGV2aWNlVHlwZSk7DQogICAgICAgICAgICB9DQogICAgICAgICAgICBicmVhazsNCiAgICAgICAgfQ0KICAgICAgICByZXR1cm4gdGVtcERhdGEuam9pbigiLCAiKTsNCiAgICAgIH0NCiAgICB9LA0KICAgIHRyYW5zZm9ybVBsYW5lTmFtZShwbGFuZUFycmF5KSB7DQogICAgICBsZXQgdGVtcERhdGEgPSBbXTsNCiAgICAgIGlmIChwbGFuZUFycmF5Lmxlbmd0aCA9PSAxICYmIHBsYW5lQXJyYXlbMF0gPT09ICIiKSB7DQogICAgICAgIHJldHVybiAiIjsNCiAgICAgIH0NCiAgICAgIGlmIChwbGFuZUFycmF5ICYmIHBsYW5lQXJyYXkubGVuZ3RoID4gMCAmJiB0aGlzLnBsYW5lTGlzdC5sZW5ndGggPiAwKSB7DQogICAgICAgIHBsYW5lQXJyYXkuZm9yRWFjaChpdGVtID0+IHsNCiAgICAgICAgICBsZXQgZCA9IHRoaXMucGxhbmVMaXN0LmZpbmQoZGF0YSA9PiBkYXRhLnZhbHVlID09IGl0ZW0pLmxhYmVsOw0KICAgICAgICAgIHRlbXBEYXRhLnB1c2goZCk7DQogICAgICAgIH0pOw0KICAgICAgICByZXR1cm4gdGVtcERhdGEuam9pbigiLCAiKTsNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIHJldHVybiAiIjsNCiAgICAgIH0NCiAgICB9LA0KICAgIGxvYWRLTURhdGEoKSB7DQogICAgICBsZXQgcGFyYW1zID0gew0KICAgICAgICBpbmxpbmVjb3VudDogdHJ1ZSwNCiAgICAgICAgc2VhcmNoOiAiY2F0ZWdvcnkgZXEgQFNOU0BTZXR0aW5nS00iDQogICAgICB9Ow0KDQogICAgICB0aGlzLiRzZXJ2aWNlLmdldFBPQ1Byb3BlcnRpZXMuc2VuZChwYXJhbXMpLnRoZW4ocmVzID0+IHsNCiAgICAgICAgaWYgKHJlcy5jb3VudCA+IDApIHsNCiAgICAgICAgICByZXMucmVzdWx0c1swXS5wcm9wZXJ0aWVzLmZvckVhY2gocmVzID0+IHsNCiAgICAgICAgICAgIHN3aXRjaCAocmVzLmtleSkgew0KICAgICAgICAgICAgICBjYXNlICJlcXVpcG1lbnRfbG9uZ1ByZXNzX2NodCI6DQogICAgICAgICAgICAgICAgdGhpcy5rbUNvbmZpZ0xpc3RbMF0ubG9uZ1ByZXNzX2NodCA9IHJlcy52YWx1ZTsNCiAgICAgICAgICAgICAgICBicmVhazsNCiAgICAgICAgICAgICAgY2FzZSAiZXF1aXBtZW50X2xvbmdQcmVzc19lbiI6DQogICAgICAgICAgICAgICAgdGhpcy5rbUNvbmZpZ0xpc3RbMF0ubG9uZ1ByZXNzX2VuID0gcmVzLnZhbHVlOw0KICAgICAgICAgICAgICAgIGJyZWFrOw0KICAgICAgICAgICAgICBjYXNlICJlcXVpcG1lbnRfc2hvcnRDbGlja19jaHQiOg0KICAgICAgICAgICAgICAgIHRoaXMua21Db25maWdMaXN0WzBdLnNob3J0Q2xpY2tfY2h0ID0gcmVzLnZhbHVlOw0KICAgICAgICAgICAgICAgIGJyZWFrOw0KICAgICAgICAgICAgICBjYXNlICJlcXVpcG1lbnRfc2hvcnRDbGlja19lbiI6DQogICAgICAgICAgICAgICAgdGhpcy5rbUNvbmZpZ0xpc3RbMF0uc2hvcnRDbGlja19lbiA9IHJlcy52YWx1ZTsNCiAgICAgICAgICAgICAgICBicmVhazsNCiAgICAgICAgICAgICAgY2FzZSAiZXF1aXBtZW50X2RvdWJsZUNsaWNrX2NodCI6DQogICAgICAgICAgICAgICAgdGhpcy5rbUNvbmZpZ0xpc3RbMF0uZG91YmxlQ2xpY2tfY2h0ID0gcmVzLnZhbHVlOw0KICAgICAgICAgICAgICAgIGJyZWFrOw0KICAgICAgICAgICAgICBjYXNlICJlcXVpcG1lbnRfZG91YmxlQ2xpY2tfZW4iOg0KICAgICAgICAgICAgICAgIHRoaXMua21Db25maWdMaXN0WzBdLmRvdWJsZUNsaWNrX2VuID0gcmVzLnZhbHVlOw0KICAgICAgICAgICAgICAgIGJyZWFrOw0KICAgICAgICAgICAgICBjYXNlICJwZW9wbGVfbG9uZ1ByZXNzX2NodCI6DQogICAgICAgICAgICAgICAgdGhpcy5rbUNvbmZpZ0xpc3RbMV0ubG9uZ1ByZXNzX2NodCA9IHJlcy52YWx1ZTsNCiAgICAgICAgICAgICAgICBicmVhazsNCiAgICAgICAgICAgICAgY2FzZSAicGVvcGxlX2xvbmdQcmVzc19lbiI6DQogICAgICAgICAgICAgICAgdGhpcy5rbUNvbmZpZ0xpc3RbMV0ubG9uZ1ByZXNzX2VuID0gcmVzLnZhbHVlOw0KICAgICAgICAgICAgICAgIGJyZWFrOw0KICAgICAgICAgICAgICBjYXNlICJwZW9wbGVfc2hvcnRDbGlja19jaHQiOg0KICAgICAgICAgICAgICAgIHRoaXMua21Db25maWdMaXN0WzFdLnNob3J0Q2xpY2tfY2h0ID0gcmVzLnZhbHVlOw0KICAgICAgICAgICAgICAgIGJyZWFrOw0KICAgICAgICAgICAgICBjYXNlICJwZW9wbGVfc2hvcnRDbGlja19lbiI6DQogICAgICAgICAgICAgICAgdGhpcy5rbUNvbmZpZ0xpc3RbMV0uc2hvcnRDbGlja19lbiA9IHJlcy52YWx1ZTsNCiAgICAgICAgICAgICAgICBicmVhazsNCiAgICAgICAgICAgICAgY2FzZSAicGVvcGxlX2RvdWJsZUNsaWNrX2NodCI6DQogICAgICAgICAgICAgICAgdGhpcy5rbUNvbmZpZ0xpc3RbMV0uZG91YmxlQ2xpY2tfY2h0ID0gcmVzLnZhbHVlOw0KICAgICAgICAgICAgICAgIGJyZWFrOw0KICAgICAgICAgICAgICBjYXNlICJwZW9wbGVfZG91YmxlQ2xpY2tfZW4iOg0KICAgICAgICAgICAgICAgIHRoaXMua21Db25maWdMaXN0WzFdLmRvdWJsZUNsaWNrX2VuID0gcmVzLnZhbHVlOw0KICAgICAgICAgICAgICAgIGJyZWFrOw0KICAgICAgICAgICAgICBjYXNlICJzcGFjZV9sb25nUHJlc3NfY2h0IjoNCiAgICAgICAgICAgICAgICB0aGlzLmttQ29uZmlnTGlzdFsyXS5sb25nUHJlc3NfY2h0ID0gcmVzLnZhbHVlOw0KICAgICAgICAgICAgICAgIGJyZWFrOw0KICAgICAgICAgICAgICBjYXNlICJzcGFjZV9sb25nUHJlc3NfZW4iOg0KICAgICAgICAgICAgICAgIHRoaXMua21Db25maWdMaXN0WzJdLmxvbmdQcmVzc19lbiA9IHJlcy52YWx1ZTsNCiAgICAgICAgICAgICAgICBicmVhazsNCiAgICAgICAgICAgICAgY2FzZSAic3BhY2Vfc2hvcnRDbGlja19jaHQiOg0KICAgICAgICAgICAgICAgIHRoaXMua21Db25maWdMaXN0WzJdLnNob3J0Q2xpY2tfY2h0ID0gcmVzLnZhbHVlOw0KICAgICAgICAgICAgICAgIGJyZWFrOw0KICAgICAgICAgICAgICBjYXNlICJzcGFjZV9zaG9ydENsaWNrX2VuIjoNCiAgICAgICAgICAgICAgICB0aGlzLmttQ29uZmlnTGlzdFsyXS5zaG9ydENsaWNrX2VuID0gcmVzLnZhbHVlOw0KICAgICAgICAgICAgICAgIGJyZWFrOw0KICAgICAgICAgICAgICBjYXNlICJzcGFjZV9kb3VibGVDbGlja19jaHQiOg0KICAgICAgICAgICAgICAgIHRoaXMua21Db25maWdMaXN0WzJdLmRvdWJsZUNsaWNrX2NodCA9IHJlcy52YWx1ZTsNCiAgICAgICAgICAgICAgICBicmVhazsNCiAgICAgICAgICAgICAgY2FzZSAic3BhY2VfZG91YmxlQ2xpY2tfZW4iOg0KICAgICAgICAgICAgICAgIHRoaXMua21Db25maWdMaXN0WzJdLmRvdWJsZUNsaWNrX2VuID0gcmVzLnZhbHVlOw0KICAgICAgICAgICAgICAgIGJyZWFrOw0KICAgICAgICAgICAgICBjYXNlICJvdGhlcl9sb25nUHJlc3NfY2h0IjoNCiAgICAgICAgICAgICAgICB0aGlzLmttQ29uZmlnTGlzdFszXS5sb25nUHJlc3NfY2h0ID0gcmVzLnZhbHVlOw0KICAgICAgICAgICAgICAgIGJyZWFrOw0KICAgICAgICAgICAgICBjYXNlICJvdGhlcl9sb25nUHJlc3NfZW4iOg0KICAgICAgICAgICAgICAgIHRoaXMua21Db25maWdMaXN0WzNdLmxvbmdQcmVzc19lbiA9IHJlcy52YWx1ZTsNCiAgICAgICAgICAgICAgICBicmVhazsNCiAgICAgICAgICAgICAgY2FzZSAib3RoZXJfc2hvcnRDbGlja19jaHQiOg0KICAgICAgICAgICAgICAgIHRoaXMua21Db25maWdMaXN0WzNdLnNob3J0Q2xpY2tfY2h0ID0gcmVzLnZhbHVlOw0KICAgICAgICAgICAgICAgIGJyZWFrOw0KICAgICAgICAgICAgICBjYXNlICJvdGhlcl9zaG9ydENsaWNrX2VuIjoNCiAgICAgICAgICAgICAgICB0aGlzLmttQ29uZmlnTGlzdFszXS5zaG9ydENsaWNrX2VuID0gcmVzLnZhbHVlOw0KICAgICAgICAgICAgICAgIGJyZWFrOw0KICAgICAgICAgICAgICBjYXNlICJvdGhlcl9kb3VibGVDbGlja19jaHQiOg0KICAgICAgICAgICAgICAgIHRoaXMua21Db25maWdMaXN0WzNdLmRvdWJsZUNsaWNrX2NodCA9IHJlcy52YWx1ZTsNCiAgICAgICAgICAgICAgICBicmVhazsNCiAgICAgICAgICAgICAgY2FzZSAib3RoZXJfZG91YmxlQ2xpY2tfZW4iOg0KICAgICAgICAgICAgICAgIHRoaXMua21Db25maWdMaXN0WzNdLmRvdWJsZUNsaWNrX2VuID0gcmVzLnZhbHVlOw0KICAgICAgICAgICAgICAgIGJyZWFrOw0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0pOw0KICAgICAgICB9DQogICAgICAgIHRoaXMua21Db25maWdMaXN0RWRpdCA9IEpTT04ucGFyc2UoSlNPTi5zdHJpbmdpZnkodGhpcy5rbUNvbmZpZ0xpc3QpKTsNCiAgICAgIH0pOw0KICAgIH0sDQogICAgbG9hZENhbWVyYURhdGEoKSB7DQogICAgICBsZXQgcGFyYW1zID0gew0KICAgICAgICBpbmxpbmVjb3VudDogdHJ1ZSwNCiAgICAgICAgc2VhcmNoOiAiY2F0ZWdvcnkgZXEgQFNOU0BTZXR0aW5nQ2FtZXJhIg0KICAgICAgfTsNCg0KICAgICAgdGhpcy4kc2VydmljZS5nZXRQT0NQcm9wZXJ0aWVzLnNlbmQocGFyYW1zKS50aGVuKHJlcyA9PiB7DQogICAgICAgIGlmIChyZXMuY291bnQgPiAwKSB7DQogICAgICAgICAgcmVzLnJlc3VsdHNbMF0ucHJvcGVydGllcy5mb3JFYWNoKHJlcyA9PiB7DQogICAgICAgICAgICBzd2l0Y2ggKHJlcy5rZXkpIHsNCiAgICAgICAgICAgICAgLy9oZWxwDQogICAgICAgICAgICAgIGNhc2UgImhlbHBfY2FwdHVyZSI6DQogICAgICAgICAgICAgICAgdGhpcy5jYW1lcmFDb25maWdMaXN0WzBdLmNhcHR1cmUgPQ0KICAgICAgICAgICAgICAgICAgcmVzLnZhbHVlID09ICJ0cnVlIiA/IHRydWUgOiBmYWxzZTsNCiAgICAgICAgICAgICAgICBicmVhazsNCiAgICAgICAgICAgICAgY2FzZSAiaGVscF9wcmVmaXhNaW51dGUiOg0KICAgICAgICAgICAgICAgIHRoaXMuY2FtZXJhQ29uZmlnTGlzdFswXS5wcmVmaXhNaW51dGUgPSByZXMudmFsdWUgKiAxOw0KICAgICAgICAgICAgICAgIGJyZWFrOw0KICAgICAgICAgICAgICBjYXNlICJoZWxwX3N1ZmZpeE1pbnV0ZSI6DQogICAgICAgICAgICAgICAgdGhpcy5jYW1lcmFDb25maWdMaXN0WzBdLnN1ZmZpeE1pbnV0ZSA9IHJlcy52YWx1ZSAqIDE7DQogICAgICAgICAgICAgICAgYnJlYWs7DQogICAgICAgICAgICAgIGNhc2UgImhlbHBfYmFja3VwRGlyZWN0b3J5IjoNCiAgICAgICAgICAgICAgICB0aGlzLmNhbWVyYUNvbmZpZ0xpc3RbMF0uYmFja3VwRGlyZWN0b3J5ID0gcmVzLnZhbHVlOw0KICAgICAgICAgICAgICAgIGJyZWFrOw0KICAgICAgICAgICAgICBjYXNlICJoZWxwX2FjY291bnQiOg0KICAgICAgICAgICAgICAgIHRoaXMuY2FtZXJhQ29uZmlnTGlzdFswXS5hY2NvdW50ID0gcmVzLnZhbHVlOw0KICAgICAgICAgICAgICAgIGJyZWFrOw0KICAgICAgICAgICAgICBjYXNlICJoZWxwX3Bhc3N3b3JkIjoNCiAgICAgICAgICAgICAgICB0aGlzLmNhbWVyYUNvbmZpZ0xpc3RbMF0ucGFzc3dvcmQgPSByZXMudmFsdWU7DQogICAgICAgICAgICAgICAgYnJlYWs7DQogICAgICAgICAgICAgIGNhc2UgImhlbHBfa2VlcERheXMiOg0KICAgICAgICAgICAgICAgIHRoaXMuY2FtZXJhQ29uZmlnTGlzdFswXS5rZWVwRGF5cyA9IHJlcy52YWx1ZSAqIDE7DQogICAgICAgICAgICAgICAgYnJlYWs7DQogICAgICAgICAgICAgIGNhc2UgImhlbHBfcG9zaXRpb25DYXB0dXJlIjoNCiAgICAgICAgICAgICAgICB0aGlzLmNhbWVyYUNvbmZpZ0xpc3RbMF0ucG9zaXRpb25DYXB0dXJlID0gcmVzLnZhbHVlICogMTsNCiAgICAgICAgICAgICAgICBicmVhazsNCg0KICAgICAgICAgICAgICAvL0VudGVyDQogICAgICAgICAgICAgIGNhc2UgImVudGVyX2NhcHR1cmUiOg0KICAgICAgICAgICAgICAgIHRoaXMuY2FtZXJhQ29uZmlnTGlzdFsxXS5jYXB0dXJlID0NCiAgICAgICAgICAgICAgICAgIHJlcy52YWx1ZSA9PSAidHJ1ZSIgPyB0cnVlIDogZmFsc2U7DQogICAgICAgICAgICAgICAgYnJlYWs7DQogICAgICAgICAgICAgIGNhc2UgImVudGVyX3ByZWZpeE1pbnV0ZSI6DQogICAgICAgICAgICAgICAgdGhpcy5jYW1lcmFDb25maWdMaXN0WzFdLnByZWZpeE1pbnV0ZSA9IHJlcy52YWx1ZSAqIDE7DQogICAgICAgICAgICAgICAgYnJlYWs7DQogICAgICAgICAgICAgIGNhc2UgImVudGVyX3N1ZmZpeE1pbnV0ZSI6DQogICAgICAgICAgICAgICAgdGhpcy5jYW1lcmFDb25maWdMaXN0WzFdLnN1ZmZpeE1pbnV0ZSA9IHJlcy52YWx1ZSAqIDE7DQogICAgICAgICAgICAgICAgYnJlYWs7DQogICAgICAgICAgICAgIGNhc2UgImVudGVyX2JhY2t1cERpcmVjdG9yeSI6DQogICAgICAgICAgICAgICAgdGhpcy5jYW1lcmFDb25maWdMaXN0WzFdLmJhY2t1cERpcmVjdG9yeSA9IHJlcy52YWx1ZTsNCiAgICAgICAgICAgICAgICBicmVhazsNCiAgICAgICAgICAgICAgY2FzZSAiZW50ZXJfYWNjb3VudCI6DQogICAgICAgICAgICAgICAgdGhpcy5jYW1lcmFDb25maWdMaXN0WzFdLmFjY291bnQgPSByZXMudmFsdWU7DQogICAgICAgICAgICAgICAgYnJlYWs7DQogICAgICAgICAgICAgIGNhc2UgImVudGVyX3Bhc3N3b3JkIjoNCiAgICAgICAgICAgICAgICB0aGlzLmNhbWVyYUNvbmZpZ0xpc3RbMV0ucGFzc3dvcmQgPSByZXMudmFsdWU7DQogICAgICAgICAgICAgICAgYnJlYWs7DQogICAgICAgICAgICAgIGNhc2UgImVudGVyX2tlZXBEYXlzIjoNCiAgICAgICAgICAgICAgICB0aGlzLmNhbWVyYUNvbmZpZ0xpc3RbMV0ua2VlcERheXMgPSByZXMudmFsdWUgKiAxOw0KICAgICAgICAgICAgICAgIGJyZWFrOw0KICAgICAgICAgICAgICBjYXNlICJlbnRlcl9wb3NpdGlvbkNhcHR1cmUiOg0KICAgICAgICAgICAgICAgIHRoaXMuY2FtZXJhQ29uZmlnTGlzdFsxXS5wb3NpdGlvbkNhcHR1cmUgPSByZXMudmFsdWUgKiAxOw0KICAgICAgICAgICAgICAgIGJyZWFrOw0KDQogICAgICAgICAgICAgIC8vTGVhdmUNCiAgICAgICAgICAgICAgY2FzZSAibGVhdmVfY2FwdHVyZSI6DQogICAgICAgICAgICAgICAgdGhpcy5jYW1lcmFDb25maWdMaXN0WzJdLmNhcHR1cmUgPQ0KICAgICAgICAgICAgICAgICAgcmVzLnZhbHVlID09ICJ0cnVlIiA/IHRydWUgOiBmYWxzZTsNCiAgICAgICAgICAgICAgICBicmVhazsNCiAgICAgICAgICAgICAgY2FzZSAibGVhdmVfcHJlZml4TWludXRlIjoNCiAgICAgICAgICAgICAgICB0aGlzLmNhbWVyYUNvbmZpZ0xpc3RbMl0ucHJlZml4TWludXRlID0gcmVzLnZhbHVlICogMTsNCiAgICAgICAgICAgICAgICBicmVhazsNCiAgICAgICAgICAgICAgY2FzZSAibGVhdmVfc3VmZml4TWludXRlIjoNCiAgICAgICAgICAgICAgICB0aGlzLmNhbWVyYUNvbmZpZ0xpc3RbMl0uc3VmZml4TWludXRlID0gcmVzLnZhbHVlICogMTsNCiAgICAgICAgICAgICAgICBicmVhazsNCiAgICAgICAgICAgICAgY2FzZSAibGVhdmVfYmFja3VwRGlyZWN0b3J5IjoNCiAgICAgICAgICAgICAgICB0aGlzLmNhbWVyYUNvbmZpZ0xpc3RbMl0uYmFja3VwRGlyZWN0b3J5ID0gcmVzLnZhbHVlOw0KICAgICAgICAgICAgICAgIGJyZWFrOw0KICAgICAgICAgICAgICBjYXNlICJsZWF2ZV9hY2NvdW50IjoNCiAgICAgICAgICAgICAgICB0aGlzLmNhbWVyYUNvbmZpZ0xpc3RbMl0uYWNjb3VudCA9IHJlcy52YWx1ZTsNCiAgICAgICAgICAgICAgICBicmVhazsNCiAgICAgICAgICAgICAgY2FzZSAibGVhdmVfcGFzc3dvcmQiOg0KICAgICAgICAgICAgICAgIHRoaXMuY2FtZXJhQ29uZmlnTGlzdFsyXS5wYXNzd29yZCA9IHJlcy52YWx1ZTsNCiAgICAgICAgICAgICAgICBicmVhazsNCiAgICAgICAgICAgICAgY2FzZSAibGVhdmVfa2VlcERheXMiOg0KICAgICAgICAgICAgICAgIHRoaXMuY2FtZXJhQ29uZmlnTGlzdFsyXS5rZWVwRGF5cyA9IHJlcy52YWx1ZSAqIDE7DQogICAgICAgICAgICAgICAgYnJlYWs7DQogICAgICAgICAgICAgIGNhc2UgImxlYXZlX3Bvc2l0aW9uQ2FwdHVyZSI6DQogICAgICAgICAgICAgICAgdGhpcy5jYW1lcmFDb25maWdMaXN0WzJdLnBvc2l0aW9uQ2FwdHVyZSA9IHJlcy52YWx1ZSAqIDE7DQogICAgICAgICAgICAgICAgYnJlYWs7DQoNCiAgICAgICAgICAgICAgLy9mYWxsDQogICAgICAgICAgICAgIGNhc2UgImZhbGxfY2FwdHVyZSI6DQogICAgICAgICAgICAgICAgdGhpcy5jYW1lcmFDb25maWdMaXN0WzNdLmNhcHR1cmUgPQ0KICAgICAgICAgICAgICAgICAgcmVzLnZhbHVlID09ICJ0cnVlIiA/IHRydWUgOiBmYWxzZTsNCiAgICAgICAgICAgICAgICBicmVhazsNCiAgICAgICAgICAgICAgY2FzZSAiZmFsbF9wcmVmaXhNaW51dGUiOg0KICAgICAgICAgICAgICAgIHRoaXMuY2FtZXJhQ29uZmlnTGlzdFszXS5wcmVmaXhNaW51dGUgPSByZXMudmFsdWUgKiAxOw0KICAgICAgICAgICAgICAgIGJyZWFrOw0KICAgICAgICAgICAgICBjYXNlICJmYWxsX3N1ZmZpeE1pbnV0ZSI6DQogICAgICAgICAgICAgICAgdGhpcy5jYW1lcmFDb25maWdMaXN0WzNdLnN1ZmZpeE1pbnV0ZSA9IHJlcy52YWx1ZSAqIDE7DQogICAgICAgICAgICAgICAgYnJlYWs7DQogICAgICAgICAgICAgIGNhc2UgImZhbGxfYmFja3VwRGlyZWN0b3J5IjoNCiAgICAgICAgICAgICAgICB0aGlzLmNhbWVyYUNvbmZpZ0xpc3RbM10uYmFja3VwRGlyZWN0b3J5ID0gcmVzLnZhbHVlOw0KICAgICAgICAgICAgICAgIGJyZWFrOw0KICAgICAgICAgICAgICBjYXNlICJmYWxsX2FjY291bnQiOg0KICAgICAgICAgICAgICAgIHRoaXMuY2FtZXJhQ29uZmlnTGlzdFszXS5hY2NvdW50ID0gcmVzLnZhbHVlOw0KICAgICAgICAgICAgICAgIGJyZWFrOw0KICAgICAgICAgICAgICBjYXNlICJmYWxsX3Bhc3N3b3JkIjoNCiAgICAgICAgICAgICAgICB0aGlzLmNhbWVyYUNvbmZpZ0xpc3RbM10ucGFzc3dvcmQgPSByZXMudmFsdWU7DQogICAgICAgICAgICAgICAgYnJlYWs7DQogICAgICAgICAgICAgIGNhc2UgImZhbGxfa2VlcERheXMiOg0KICAgICAgICAgICAgICAgIHRoaXMuY2FtZXJhQ29uZmlnTGlzdFszXS5rZWVwRGF5cyA9IHJlcy52YWx1ZSAqIDE7DQogICAgICAgICAgICAgICAgYnJlYWs7DQogICAgICAgICAgICAgIGNhc2UgImZhbGxfcG9zaXRpb25DYXB0dXJlIjoNCiAgICAgICAgICAgICAgICB0aGlzLmNhbWVyYUNvbmZpZ0xpc3RbM10ucG9zaXRpb25DYXB0dXJlID0gcmVzLnZhbHVlICogMTsNCiAgICAgICAgICAgICAgICBicmVhazsNCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9KTsNCiAgICAgICAgfQ0KICAgICAgICB0aGlzLmttQ29uZmlnTGlzdEVkaXQgPSBKU09OLnBhcnNlKEpTT04uc3RyaW5naWZ5KHRoaXMua21Db25maWdMaXN0KSk7DQogICAgICB9KTsNCiAgICB9LA0KICAgIGxvYWRTdGF0aXN0aWNMaW5lRGF0YSgpIHsNCiAgICAgIGxldCBwYXJhbXMgPSB7DQogICAgICAgIGlubGluZWNvdW50OiB0cnVlLA0KICAgICAgICBzZWFyY2g6ICJjYXRlZ29yeSBlcSBAU05TQFNldHRpbmdTdGF0aXN0aWNMaW5lIg0KICAgICAgfTsNCiAgICAgIHRoaXMuJHNlcnZpY2UuZ2V0UE9DUHJvcGVydGllcy5zZW5kKHBhcmFtcykudGhlbihyZXNEYXRhID0+IHsNCiAgICAgICAgaWYgKHJlc0RhdGEuY291bnQgPiAwKSB7DQogICAgICAgICAgcmVzRGF0YS5yZXN1bHRzWzBdLnByb3BlcnRpZXMuZm9yRWFjaChyZXMgPT4gew0KICAgICAgICAgICAgbGV0IGtleSA9IHJlcy5rZXkuc3BsaXQoIl8iKVswXTsNCiAgICAgICAgICAgIGxldCBkYXRhID0gcmVzLmtleS5zcGxpdCgiXyIpWzFdOw0KICAgICAgICAgICAgLy8gaWYocmVxKQ0KICAgICAgICAgICAgLy8gICAgIHRoaXMuY2FtZXJhQ29uZmlnTGlzdFswXS5jYXB0dXJlID0gcmVzLnZhbHVlID09ICJ0cnVlIiA/IHRydWUgOiBmYWxzZTsNCg0KICAgICAgICAgICAgdGhpcy5zdGF0aXN0aWNMaW5lQ29uZmlnTGlzdC5mb3JFYWNoKGl0ZW0gPT4gew0KICAgICAgICAgICAgICBpZiAoaXRlbS5lbnRyeSA9PSBrZXkpIHsNCiAgICAgICAgICAgICAgICBpZiAoIWlzTmFOKHBhcnNlSW50KHJlcy52YWx1ZSkpKSB7DQogICAgICAgICAgICAgICAgICBpdGVtW2RhdGFdID0gcmVzLnZhbHVlICogMTsNCiAgICAgICAgICAgICAgICB9IGVsc2UgaWYgKHJlcy52YWx1ZSA9PSAidHJ1ZSIgfHwgcmVzID09ICJmYWxzZSIpIHsNCiAgICAgICAgICAgICAgICAgIGl0ZW1bZGF0YV0gPQ0KICAgICAgICAgICAgICAgICAgICByZXMudmFsdWUgPT0gInRydWUiDQogICAgICAgICAgICAgICAgICAgICAgPyB0cnVlDQogICAgICAgICAgICAgICAgICAgICAgOiByZXMudmFsdWUgPT0gImZhbHNlIg0KICAgICAgICAgICAgICAgICAgICAgID8gZmFsc2UNCiAgICAgICAgICAgICAgICAgICAgICA6IGZhbHNlOw0KICAgICAgICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICAgICAgICBpdGVtW2RhdGFdID0gcmVzLnZhbHVlOw0KICAgICAgICAgICAgICAgIH0NCiAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgfSk7DQogICAgICAgICAgfSk7DQogICAgICAgIH0NCiAgICAgIH0pOw0KICAgIH0sDQogICAgZ2V0Q2FtZXJhVHlwZSh0eXBlKSB7DQogICAgICBpZiAodHlwZSAhPSB1bmRlZmluZWQpIHsNCiAgICAgICAgbGV0IHR5cGVEYXRhID0gdGhpcy5jYW1lcmFUeXBlTGlzdC5maW5kKHQgPT4gdC50eXBlID09IHR5cGUpLm5hbWU7DQogICAgICAgIHJldHVybiB0eXBlRGF0YTsNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIHJldHVybiAiIjsNCiAgICAgIH0NCiAgICB9LA0KICAgIGdldE9iamVjdFR5cGUodHlwZSkgew0KICAgICAgaWYgKHR5cGUgIT0gdW5kZWZpbmVkKSB7DQogICAgICAgIGxldCB0eXBlRGF0YSA9IHRoaXMua21PYmplY3RUeXBlTGlzdC5maW5kKHQgPT4gdC50eXBlID09IHR5cGUpLm5hbWU7DQogICAgICAgIHJldHVybiB0eXBlRGF0YTsNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIHJldHVybiAiIjsNCiAgICAgIH0NCiAgICB9LA0KICAgIG9uRWRpdERhdGFDaGFuZ2UodHlwZSwgcm93LCBpbmRleCkgew0KICAgICAgc3dpdGNoICh0eXBlKSB7DQogICAgICAgIGNhc2UgImxvbmdQcmVzc19jaHQiOg0KICAgICAgICAgIHRoaXMua21Db25maWdMaXN0RWRpdFtpbmRleF0ubG9uZ1ByZXNzX2NodCA9IHJvdy5sb25nUHJlc3NfY2h0Ow0KICAgICAgICAgIGJyZWFrOw0KICAgICAgICBjYXNlICJsb25nUHJlc3NfZW4iOg0KICAgICAgICAgIHRoaXMua21Db25maWdMaXN0RWRpdFtpbmRleF0ubG9uZ1ByZXNzX2VuID0gcm93LmxvbmdQcmVzc19lbjsNCiAgICAgICAgICBicmVhazsNCiAgICAgICAgY2FzZSAic2hvcnRDbGlja19jaHQiOg0KICAgICAgICAgIHRoaXMua21Db25maWdMaXN0RWRpdFtpbmRleF0uc2hvcnRDbGlja19jaHQgPSByb3cuc2hvcnRDbGlja19jaHQ7DQogICAgICAgICAgYnJlYWs7DQogICAgICAgIGNhc2UgInNob3J0Q2xpY2tfZW4iOg0KICAgICAgICAgIHRoaXMua21Db25maWdMaXN0RWRpdFtpbmRleF0uc2hvcnRDbGlja19lbiA9IHJvdy5zaG9ydENsaWNrX2VuOw0KICAgICAgICAgIGJyZWFrOw0KICAgICAgICBjYXNlICJkb3VibGVDbGlja19jaHQiOg0KICAgICAgICAgIHRoaXMua21Db25maWdMaXN0RWRpdFtpbmRleF0uZG91YmxlQ2xpY2tfY2h0ID0gcm93LmRvdWJsZUNsaWNrX2NodDsNCiAgICAgICAgICBicmVhazsNCiAgICAgICAgY2FzZSAiZG91YmxlQ2xpY2tfZW4iOg0KICAgICAgICAgIHRoaXMua21Db25maWdMaXN0RWRpdFtpbmRleF0uZG91YmxlQ2xpY2tfZW4gPSByb3cuZG91YmxlQ2xpY2tfZW47DQogICAgICAgICAgYnJlYWs7DQogICAgICB9DQogICAgfSwNCiAgICBub3JtYWxpemVIZWlnaHQoKSB7DQogICAgICBzZXRUaW1lb3V0KA0KICAgICAgICBmdW5jdGlvbigpIHsNCiAgICAgICAgICBjb25zdCBtb2RhbCA9IGRvY3VtZW50LnF1ZXJ5U2VsZWN0b3IoIi5pdnUtbW9kYWwtYm9keSAuY29udGVudCIpOw0KICAgICAgICAgIGlmIChtb2RhbCkgew0KICAgICAgICAgICAgbW9kYWwuc3R5bGUuaGVpZ2h0ID0gd2luZG93LmlubmVySGVpZ2h0IC0gNDAwICsgInB4IjsNCiAgICAgICAgICB9DQogICAgICAgICAgY29uc3QgbW9kYWwyID0gZG9jdW1lbnQucXVlcnlTZWxlY3RvcigiLml2dS1tb2RhbC1mb290ZXIiKTsNCiAgICAgICAgICBpZiAobW9kYWwyKSB7DQogICAgICAgICAgICBtb2RhbDIuc3R5bGUuaGVpZ2h0ID0gIjMwcHgiOw0KICAgICAgICAgIH0NCiAgICAgICAgfS5iaW5kKHRoaXMpLA0KICAgICAgICA1MA0KICAgICAgKTsNCiAgICB9LA0KICAgIGR5bmFtaWNTb3J0KHByb3BlcnR5KSB7DQogICAgICB2YXIgc29ydE9yZGVyID0gMTsNCiAgICAgIGlmIChwcm9wZXJ0eVswXSA9PT0gIi0iKSB7DQogICAgICAgIHNvcnRPcmRlciA9IC0xOw0KICAgICAgICBwcm9wZXJ0eSA9IHByb3BlcnR5LnN1YnN0cigxKTsNCiAgICAgIH0NCiAgICAgIHJldHVybiBmdW5jdGlvbihhLCBiKSB7DQogICAgICAgIHZhciByZXN1bHQgPQ0KICAgICAgICAgIGFbcHJvcGVydHldIDwgYltwcm9wZXJ0eV0gPyAtMSA6IGFbcHJvcGVydHldID4gYltwcm9wZXJ0eV0gPyAxIDogMDsNCiAgICAgICAgcmV0dXJuIHJlc3VsdCAqIHNvcnRPcmRlcjsNCiAgICAgIH07DQogICAgfSwNCiAgICBlZGl0KCkgew0KICAgICAgdGhpcy5pc0VkaXQgPSB0cnVlOw0KICAgICAgdGhpcy5wbGF5KCJzdG9wIik7DQogICAgICB0aGlzLmxvYWRMb2dvKCk7DQogICAgICB0aGlzLmxvYWRTb3VuZCgpOw0KICAgICAgLy90aGlzLmdldFN5c3RlbUNvbmZpZygpDQogICAgfSwNCiAgICBjYW5jZWxFZGl0KCkgew0KICAgICAgdGhpcy5wbGF5KCJzdG9wIik7DQogICAgICB0aGlzLmZpbGUgPSBudWxsOw0KICAgICAgdGhpcy5maWxlVVJMID0gbnVsbDsNCiAgICAgIHRoaXMuaXNFZGl0ID0gZmFsc2U7DQogICAgICB0aGlzLmNvbmZpZ0RhdGEgPSBKU09OLnBhcnNlKEpTT04uc3RyaW5naWZ5KHRoaXMub3JpZ0NvbmZpZ0RhdGEpKTsNCiAgICAgIHRoaXMudGhpcmRQYXJ0eURhdGEgPSBKU09OLnBhcnNlKEpTT04uc3RyaW5naWZ5KHRoaXMub3JpZ1RoaXJkUGFydHlEYXRhKSk7DQogICAgICB0aGlzLmNhbWVyYUNvbmZpZ0xpc3QgPSBKU09OLnBhcnNlKA0KICAgICAgICBKU09OLnN0cmluZ2lmeSh0aGlzLmRlZmF1bHRDYW1lcmFDb25maWdMaXN0KQ0KICAgICAgKTsNCiAgICAgIHRoaXMudXBkYXRlVmFsdWUodGhpcy5jb25maWdEYXRhKTsNCiAgICAgIHRoaXMubG9hZExvZ28oKTsNCiAgICAgIHRoaXMubG9hZFNvdW5kKCk7DQogICAgICB0aGlzLmxvYWRLTURhdGEoKTsNCiAgICAgIHRoaXMubG9hZENhbWVyYURhdGEoKTsNCiAgICAgIHRoaXMubG9hZFRoaXJkUGFydHkoKTsNCiAgICAgIC8vdGhpcy5nZXRTeXN0ZW1Db25maWcoKTsNCiAgICB9LA0KDQogICAgLy8gZWRpdEhhbmRsZVN1Ym1pdCgpIHsNCiAgICAvLyAgIGNvbnNvbGUubG9nKCJlZGl0SGFuZGxlU3VibWl0Iik7DQogICAgLy8gICBjb25zb2xlLmxvZyh0aGlzLmNvbmZpZ0xpc3QpOw0KICAgIC8vICAgY29uc29sZS5sb2codGhpcy5jb25maWdMaXN0VG9TYXZlKTsNCiAgICAvLyAgIGNvbnNvbGUubG9nKCJMb2dpblVzZXI6ICIgKyBsb2NhbFN0b3JhZ2UuZ2V0SXRlbSgic25zX2xvZ2luVXNlciIpKTsNCg0KICAgIC8vICAgbGV0IHB1dGRhdGEgPSBbXTsNCiAgICAvLyAgIGxldCBwcm9wZXJ0aWVzID0gW107DQogICAgLy8gICBsZXQgbG9naW5Vc2VyTmFtZSA9IGxvY2FsU3RvcmFnZS5nZXRJdGVtKCJzbnNfbG9naW5Vc2VyIik7DQogICAgLy8gICBsZXQgYXBwRW5hYmxlQWNjb3VudEtleSA9ICJhcHBzRW5hYmxlLSIgKyBsb2dpblVzZXJOYW1lOw0KICAgIC8vICAgbGV0IGFwcEVkaXRBY2NvdW50S2V5ID0gImFwcHNFZGl0LSIgKyBsb2dpblVzZXJOYW1lOw0KICAgIC8vICAgbGV0IGFwcEVuYWJsZUZvclVzZXIgPSAiIjsNCiAgICAvLyAgIGxldCBhcHBFZGl0Rm9yVXNlciA9ICIiOw0KDQogICAgLy8gICB0aGlzLmNvbmZpZ0xpc3QuZm9yRWFjaCgoeCkgPT4gew0KICAgIC8vICAgICBpZiAoeC5rZXkgPT0gYXBwRW5hYmxlQWNjb3VudEtleSkgYXBwRW5hYmxlRm9yVXNlciA9IHgudmFsdWUudG9TdHJpbmcoKTsNCiAgICAvLyAgICAgZWxzZSBpZiAoeC5rZXkgPT0gYXBwRWRpdEFjY291bnRLZXkpDQogICAgLy8gICAgICAgYXBwRWRpdEZvclVzZXIgPSB4LnZhbHVlLnRvU3RyaW5nKCk7DQogICAgLy8gICB9KTsNCiAgICAvLyAgIHRoaXMuY29uZmlnTGlzdFRvU2F2ZS5mb3JFYWNoKCh5KSA9PiB7DQogICAgLy8gICAgIHN3aXRjaCAoeS5zY2hlbWEudHlwZSkgew0KICAgIC8vICAgICAgIGNhc2UgInN0cmluZyI6DQogICAgLy8gICAgICAgICBwcm9wZXJ0aWVzLnB1c2goew0KICAgIC8vICAgICAgICAgICBrZXk6IHkua2V5LA0KICAgIC8vICAgICAgICAgICB2YWx1ZTogeS52YWx1ZSwNCiAgICAvLyAgICAgICAgIH0pOw0KICAgIC8vICAgICAgICAgYnJlYWs7DQogICAgLy8gICAgICAgY2FzZSAic2VsZWN0IjoNCiAgICAvLyAgICAgICAgIGlmICh5LmtleSAhPSBhcHBFbmFibGVBY2NvdW50S2V5KQ0KICAgIC8vICAgICAgICAgICBwcm9wZXJ0aWVzLnB1c2goew0KICAgIC8vICAgICAgICAgICAgIGtleTogeS5rZXksDQogICAgLy8gICAgICAgICAgICAgdmFsdWU6IHkudmFsdWUudG9TdHJpbmcoKSwNCiAgICAvLyAgICAgICAgICAgfSk7DQogICAgLy8gICAgICAgICBicmVhazsNCiAgICAvLyAgICAgICBjYXNlICJib29sZWFuIjoNCiAgICAvLyAgICAgICAgIHByb3BlcnRpZXMucHVzaCh7DQogICAgLy8gICAgICAgICAgIGtleTogeS5rZXksDQogICAgLy8gICAgICAgICAgIHZhbHVlOiB5LnZhbHVlLnRvU3RyaW5nKCksDQogICAgLy8gICAgICAgICB9KTsNCiAgICAvLyAgICAgICAgIGJyZWFrOw0KICAgIC8vICAgICB9DQogICAgLy8gICB9KTsNCiAgICAvLyAgIGlmIChsb2dpblVzZXJOYW1lKSB7DQogICAgLy8gICAgIHByb3BlcnRpZXMucHVzaCh7DQogICAgLy8gICAgICAga2V5OiBhcHBFbmFibGVBY2NvdW50S2V5LA0KICAgIC8vICAgICAgIHZhbHVlOiBhcHBFbmFibGVGb3JVc2VyLA0KICAgIC8vICAgICB9KTsNCiAgICAvLyAgICAgcHJvcGVydGllcy5wdXNoKHsNCiAgICAvLyAgICAgICBrZXk6IGFwcEVkaXRBY2NvdW50S2V5LA0KICAgIC8vICAgICAgIHZhbHVlOiBhcHBFZGl0Rm9yVXNlciwNCiAgICAvLyAgICAgfSk7DQogICAgLy8gICB9DQoNCiAgICAvLyAgIHB1dGRhdGEucHVzaCh7DQogICAgLy8gICAgIGNhdGVnb3J5OiAicG9jQ29uZmlnIiwNCiAgICAvLyAgICAgcHJvcGVydGllczogcHJvcGVydGllcywNCiAgICAvLyAgIH0pOw0KICAgIC8vICAgY29uc29sZS5sb2coInByb3BlcnRpZXM9Iik7DQogICAgLy8gICBjb25zb2xlLmxvZyhwdXRkYXRhKTsNCg0KICAgIC8vICAgdGhpcy4kc2VydmljZS5lZGl0UE9DUHJvcGVydGllcy5zZW5kKHB1dGRhdGEpLnRoZW4oKHJlcykgPT4gew0KICAgIC8vICAgICBjb25zb2xlLmxvZyhyZXMpOw0KICAgIC8vICAgICB0aGlzLiROb3RpY2Uuc3VjY2Vzcyh7DQogICAgLy8gICAgICAgdGl0bGU6IHRoaXMuJHQoIkxvY2FsZVN0cmluZy5NMDAwMDQiKSwNCiAgICAvLyAgICAgICBkZXNjOiB0aGlzLiR0KCJMb2NhbGVTdHJpbmcuTTAwMDAyIiksDQogICAgLy8gICAgICAgZHVyYXRpb246IENvbmZpZy5TVUNDRVNTX0RVUkFUSU9OLA0KICAgIC8vICAgICB9KTsNCg0KICAgIC8vICAgICB0aGlzLiRzdG9yZS5jb21taXQoInNldFBPQ0NvbmZpZ0NoYW5nZWQiLCBtb21lbnQoKS52YWx1ZU9mKCkpOw0KDQogICAgLy8gICAgIHRoaXMuaXNTaG93ID0gZmFsc2U7DQogICAgLy8gICAgIHRoaXMuJGVtaXQoImNsb3NlU3lzdGVtQ29uZmlnIik7DQogICAgLy8gICB9KTsNCiAgICAvLyB9LA0KDQogICAgLy8gY29tcHV0ZWREYXRlKHZhbCkgew0KICAgIC8vICAgaWYgKHZhbCkgew0KICAgIC8vICAgICByZXR1cm4gbW9tZW50KHZhbCkuZm9ybWF0KCJZWVlZLU1NLUREIik7DQogICAgLy8gICB9DQogICAgLy8gICByZXR1cm4gIiI7DQogICAgLy8gfSwNCiAgICAvLyBjb21wdXRlZERhdGVUaW1lKHZhbCkgew0KICAgIC8vICAgaWYgKHZhbCkgew0KICAgIC8vICAgICByZXR1cm4gbW9tZW50KHZhbCkuZm9ybWF0KCJZWVlZLU1NLUREIEhIOm1tOnNzIik7DQogICAgLy8gICB9DQogICAgLy8gICByZXR1cm4gIiI7DQogICAgLy8gfSwNCiAgICBjYW5jZWwoKSB7DQogICAgICB0aGlzLmlzU2hvdyA9IGZhbHNlOw0KICAgICAgdGhpcy5jb25maWdEYXRhID0gdGhpcy5vcmlnQ29uZmlnRGF0YTsNCiAgICAgIHRoaXMudGhpcmRQYXJ0eURhdGEgPSB0aGlzLm9yaWdUaGlyZFBhcnR5RGF0YTsNCiAgICAgIHNldFRpbWVvdXQoKCkgPT4gew0KICAgICAgICB0aGlzLiRlbWl0KCJjbG9zZVN5c3RlbUNvbmZpZyIpOw0KICAgICAgfSwgNTAwKTsNCiAgICB9DQogIH0NCn07DQo="}, {"version": 3, "sources": ["systemConfig.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgnCA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "systemConfig.vue", "sourceRoot": "src/components/administrative/systemManagement", "sourcesContent": ["<template>\r\n  <div class=\"personal-data\">\r\n    <Modal\r\n      class=\"about-data-modal\"\r\n      :closable=\"false\"\r\n      :mask-closable=\"false\"\r\n      v-model=\"isShow\"\r\n      class-name=\"vertical-center-modal\"\r\n      :width=\"modalWidth\"\r\n    >\r\n      <!-- <a slot=\"close\">\r\n        <Icon type=\"md-close\" color=\"#ADADAD\" size=\"24\"></Icon>\r\n      </a>-->\r\n      <div slot=\"header\" class=\"header\">\r\n        <div style=\"position:absolute;\">\r\n          <div :style=\"tabButtonWidth\">\r\n            <div style=\"float: right;margin-top:5px\">\r\n              <Button v-show=\"!isEdit\" type=\"primary\" size=\"small\" class=\"font-small\" @click=\"edit\">\r\n                {{\r\n                $t(\"LocaleString.B20014\") }}\r\n              </Button>\r\n              <Button v-show=\"isEdit\" size=\"small\" class=\"font-small\" @click=\"cancelEdit\">\r\n                {{ $t(\"LocaleString.B00015\")\r\n                }}\r\n              </Button>\r\n              <Button\r\n                v-show=\"isEdit\"\r\n                type=\"primary\"\r\n                size=\"small\"\r\n                class=\"font-small\"\r\n                :loading=\"submitLoading\"\r\n                @click=\"editHandleSubmitNew('update')\"\r\n              >{{ $t(\"LocaleString.B00012\") }}</Button>\r\n            </div>\r\n            <div style=\"clear: both\"></div>\r\n          </div>\r\n        </div>\r\n        <!-- <i class=\"left\"></i> -->\r\n        <span class=\"title\">{{ $t(\"LocaleString.L30054\") }}</span>\r\n      </div>\r\n      <div class=\"content\">\r\n        <Tabs :animated=\"false\" :value=\"currentTab\" @on-click=\"changeTab\">\r\n          <TabPane :label=\"$t('LocaleString.F00037')\" name=\"system\">\r\n            <Table :columns=\"columns\" :data=\"data\" :height=\"modalInnerHeight\">\r\n              <template slot-scope=\"{ row, index }\" slot=\"item\">\r\n                <!-- <Input type=\"text\" v-model=\"row.name\" v-if=\"isEdit\" /> -->\r\n                <div v-if=\"row.section == 10\">\r\n                  <span v-if=\"configData.eventAuthClose == true\">\r\n                    {{\r\n                    row.item\r\n                    }}\r\n                  </span>\r\n                </div>\r\n                <div v-if=\"row.section != 10\">\r\n                  <span>\r\n                    {{ row.item }}\r\n                    <img\r\n                      v-if=\"row.section == 6\"\r\n                      :src=\"InfoIcon\"\r\n                      style=\"vertical-align: middle; cursor: pointer\"\r\n                      @click=\"tipModal = true\"\r\n                    />\r\n                    <img\r\n                      v-if=\"row.section == 7\"\r\n                      :src=\"InfoIcon\"\r\n                      style=\"vertical-align: middle; cursor: pointer\"\r\n                      @click=\"tip2Modal = true\"\r\n                    />\r\n                    <img\r\n                      v-if=\"row.section == 11\"\r\n                      :src=\"InfoIcon\"\r\n                      style=\"vertical-align: middle; cursor: pointer\"\r\n                      @click=\"tip3Modal = true\"\r\n                    />\r\n                    <img\r\n                      v-if=\"row.section == 14\"\r\n                      :src=\"InfoIcon\"\r\n                      style=\"vertical-align: middle; cursor: pointer\"\r\n                      @click=\"tip4Modal = true\"\r\n                    />\r\n                    <img\r\n                      v-if=\"row.section == 16\"\r\n                      :src=\"InfoIcon\"\r\n                      style=\"vertical-align: middle; cursor: pointer\"\r\n                      @click=\"tip5Modal = true\"\r\n                    />\r\n                  </span>\r\n                </div>\r\n              </template>\r\n              <template slot-scope=\"{ row, index }\" slot=\"value\">\r\n                <div v-if=\"row.section == 23\">\r\n                  <Select\r\n                    :placeholder=\"$t('LocaleString.D00001')\"\r\n                    v-model=\"configData.license\"\r\n                    v-if=\"isEdit\"\r\n                    multiple\r\n                    transfer\r\n                  >\r\n                    <Option\r\n                      v-for=\"item in serviceCodeList\"\r\n                      :value=\"item.key\"\r\n                      :key=\"item.key\"\r\n                    >{{ item.value }}</Option>\r\n                  </Select>\r\n                  <span v-else>{{ row.value }}</span>\r\n                </div>\r\n                <div v-if=\"row.section == 0\">\r\n                  <Input\r\n                    maxlength=\"25\"\r\n                    show-word-limit\r\n                    v-if=\"isEdit && getEditPermission()\"\r\n                    v-model=\"configData.systemName\"\r\n                  ></Input>\r\n                  <span v-else>{{ row.value }}</span>\r\n                </div>\r\n                <div v-if=\"row.section == 17\">\r\n                  <div\r\n                    v-show=\"isEdit && getEditPermission()\"\r\n                    class=\"toggle-sound paused\"\r\n                    style=\"display: inline-block\"\r\n                  >\r\n                    <Button\r\n                      ghost\r\n                      shape=\"circle\"\r\n                      style=\"width: 20px; margin-left: 10px\"\r\n                      v-if=\"showCleanIcon\"\r\n                      @click=\"restImage\"\r\n                    >\r\n                      <img :src=\"resetIcon\" style=\"width: 30.4px; margin-left: -14.8px\" />\r\n                    </Button>\r\n                  </div>\r\n                  <Upload\r\n                    v-if=\"isEdit && getEditPermission()\"\r\n                    :before-upload=\"handleUploadImage\"\r\n                    action\r\n                    style=\"display: inline-block\"\r\n                  >\r\n                    <span>\r\n                      <Icon class=\"musicIcon\" type=\"md-cloud-upload\" size=\"30\" />\r\n                    </span>\r\n                  </Upload>\r\n                  <img\r\n                    v-if=\"isEdit && getEditPermission()\"\r\n                    id=\"preview\"\r\n                    :src=\"imgFileURL ? imgFileURL : ''\"\r\n                  />\r\n                  <span v-if=\"!isEdit\">\r\n                    <img :src=\"imgFileURL\" />\r\n                  </span>\r\n                </div>\r\n                <div v-if=\"row.section == 31\">\r\n                  <InputNumber\r\n                    :precision=\"0\"\r\n                    :min=\"0\"\r\n                    :max=\"300\"\r\n                    step=\"1\"\r\n                    v-if=\"isEdit\"\r\n                    v-model=\"configData.logoutTime\"\r\n                  ></InputNumber>\r\n                  <span v-else>{{ row.value }}</span>\r\n                </div>\r\n                <div v-if=\"row.section == 18\">\r\n                  <i-switch\r\n                    @on-change=\"switchMultiPlanes\"\r\n                    v-model=\"configData.multiPlanes\"\r\n                    v-if=\"isEdit\"\r\n                  />\r\n                  <span style=\"margin-right:25px\" v-else>{{ row.viewMultiPlanes }}</span>\r\n                  <Select\r\n                    style=\"margin-left:5px; width:770px\"\r\n                    :placeholder=\"$t('LocaleString.D00001')\"\r\n                    v-model=\"configData.selectedPlanes\"\r\n                    v-if=\"isEdit && configData.multiPlanes\"\r\n                    multiple\r\n                    :max-tag-count=\"8\"\r\n                    transfer\r\n                    @on-change=\"changeMultiPlanes\"\r\n                  >\r\n                    <Option\r\n                      v-for=\"item in planeList\"\r\n                      :value=\"item.value\"\r\n                      :key=\"item.value\"\r\n                    >{{ item.label }}</Option>\r\n                  </Select>\r\n                  <span v-else>\r\n                    <span\r\n                      v-if=\"!isEdit && row.valuePlaneSelectList !== ''\"\r\n                    >{{ row.valuePlaneSelectList }}</span>\r\n                  </span>\r\n                </div>\r\n                <div v-if=\"row.section == 30\">\r\n                  <i-switch v-model=\"configData.stuckPlane\" v-if=\"isEdit\" />\r\n                  <span style=\"margin-right:25px\" v-else>{{ row.viewStuckPlane }}</span>\r\n                </div>\r\n                <div v-if=\"row.section == 1\">\r\n                  <Select\r\n                    :placeholder=\"$t('LocaleString.D00001')\"\r\n                    v-model=\"configData.tabList\"\r\n                    v-if=\"isEdit\"\r\n                    multiple\r\n                    transfer\r\n                    @on-change=\"changeDefaultTabList\"\r\n                  >\r\n                    <Option\r\n                      v-for=\"item in showingTab\"\r\n                      :value=\"item.value\"\r\n                      :key=\"item.value\"\r\n                    >{{ item.label }}</Option>\r\n                  </Select>\r\n                  <span v-else>{{ row.value }}</span>\r\n                </div>\r\n                <div v-if=\"row.section == 2\">\r\n                  <i-switch v-model=\"configData.sound\" v-if=\"isEdit\" />\r\n                  <div\r\n                    v-show=\"isEdit\"\r\n                    @click=\"play()\"\r\n                    class=\"toggle-sound paused\"\r\n                    style=\"display: inline-block\"\r\n                  >\r\n                    <span v-if=\"isPlay\">\r\n                      <Icon class=\"musicIcon\" type=\"md-pause\" size=\"30\" />\r\n                    </span>\r\n                    <span v-if=\"!isPlay\">\r\n                      <Icon class=\"musicIcon\" type=\"md-play\" size=\"30\" />\r\n                    </span>\r\n                  </div>\r\n                  <Upload\r\n                    v-if=\"isEdit\"\r\n                    :before-upload=\"handleUpload\"\r\n                    action\r\n                    style=\"display: inline-block\"\r\n                  >\r\n                    <span>\r\n                      <Icon class=\"musicIcon\" type=\"md-cloud-upload\" size=\"30\" />\r\n                    </span>\r\n                  </Upload>\r\n                  <audio\r\n                    ref=\"audio\"\r\n                    :src=\"fileURL != null ? fileURL : alertSound\"\r\n                    @ended=\"ended\"\r\n                    preload\r\n                    id=\"audio\"\r\n                    muted\r\n                  ></audio>\r\n\r\n                  <span v-if=\"!isEdit\">{{ row.value }}</span>\r\n\r\n                  <div\r\n                    v-show=\"!isEdit && configData.sound\"\r\n                    @click=\"play()\"\r\n                    class=\"toggle-sound paused\"\r\n                    style=\"display: inline-block\"\r\n                  >\r\n                    <span v-if=\"isPlay\">\r\n                      <Icon class=\"musicIcon\" type=\"md-pause\" size=\"30\" />\r\n                    </span>\r\n                    <span v-if=\"!isPlay\">\r\n                      <Icon class=\"musicIcon\" type=\"md-play\" size=\"30\" />\r\n                    </span>\r\n                  </div>\r\n                  <span style=\"margin-left:10px\" v-if=\"!isEdit && configData.sound\">\r\n                    <span>{{$t(\"LocaleString.L30203\")}}</span>\r\n                    {{ row.valueTime }}\r\n                    <span>{{$t(\"LocaleString.L30204\")}}</span>\r\n                  </span>\r\n                  <span style=\"margin-left:10px\" v-if=\"isEdit && configData.sound\">\r\n                    <span>{{$t(\"LocaleString.L30203\")}}</span>\r\n                    <InputNumber\r\n                      style=\"width:60px\"\r\n                      :min=\"0\"\r\n                      :max=\"20\"\r\n                      v-model=\"configData.repeatSound\"\r\n                      :placeholder=\"$t('LocaleString.L00001', { 0: $t('LocaleString.L30027') })\r\n      \"\r\n                    ></InputNumber>\r\n                    <span>{{$t(\"LocaleString.L30204\")}}(0~20)</span>\r\n                  </span>\r\n                </div>\r\n                <div v-if=\"row.section == 3\">\r\n                  <Select\r\n                    :placeholder=\"$t('LocaleString.D00001')\"\r\n                    v-model=\"configData.m2List\"\r\n                    v-if=\"isEdit\"\r\n                    multiple\r\n                    transfer\r\n                  >\r\n                    <Option\r\n                      v-for=\"item in showingType\"\r\n                      v-if=\"item.value != 'wetUrine'\"\r\n                      :value=\"item.value\"\r\n                      :key=\"item.value\"\r\n                    >{{ item.label }}</Option>\r\n                  </Select>\r\n                  <span v-else>{{ row.value }}</span>\r\n                </div>\r\n                <div v-if=\"row.section == 4\">\r\n                  <!-- <i-Input v-if=\"isEdit\" v-model=\"configData.iconSize\" placeholder=\"ex: 40*40\"></i-Input> -->\r\n                  <Select\r\n                    :placeholder=\"$t('LocaleString.D00001')\"\r\n                    v-model=\"configData.iconSize\"\r\n                    v-if=\"isEdit\"\r\n                    transfer\r\n                  >\r\n                    <Option\r\n                      v-for=\"item in iconSizeList\"\r\n                      :value=\"item.value\"\r\n                      :key=\"item.value\"\r\n                    >{{ item.label }}</Option>\r\n                  </Select>\r\n                  <span v-else>{{ iconSizeTitle }}</span>\r\n                </div>\r\n                <div v-if=\"row.section == 5\">\r\n                  <Select\r\n                    :placeholder=\"$t('LocaleString.D00001')\"\r\n                    v-model=\"configData.defaultTab\"\r\n                    v-if=\"isEdit\"\r\n                    transfer\r\n                  >\r\n                    <Option\r\n                      v-for=\"item in defaultTabList\"\r\n                      :value=\"item.value\"\r\n                      :key=\"item.value\"\r\n                    >{{ item.label }}</Option>\r\n                  </Select>\r\n                  <span v-else>{{ defaultTabTitle }}</span>\r\n                </div>\r\n                <div v-if=\"row.section == 6\">\r\n                  <Select\r\n                    :placeholder=\"$t('LocaleString.D00001')\"\r\n                    v-model=\"configData.updateSeconds\"\r\n                    v-if=\"isEdit\"\r\n                    transfer\r\n                  >\r\n                    <Option\r\n                      v-for=\"item in defaultUpdateSeconds\"\r\n                      :value=\"item.value\"\r\n                      :key=\"item.value\"\r\n                    >{{ item.label }}</Option>\r\n                  </Select>\r\n                  <!-- <i-Input\r\n\r\n                v-if=\"isEdit\"\r\n                v-model=\"configData.updateSeconds\"\r\n                placeholder=\"30\"\r\n                  ></i-Input>-->\r\n                  <span v-else>{{ row.value }}</span>\r\n                </div>\r\n                <div v-if=\"row.section == 7\">\r\n                  <InputNumber\r\n                    :min=\"0\"\r\n                    v-if=\"isEdit\"\r\n                    v-model=\"configData.grayTime\"\r\n                    :placeholder=\"$t('LocaleString.L00001', { 0: $t('LocaleString.L30027') })\r\n      \"\r\n                  ></InputNumber>\r\n                  <span v-else>{{ row.value }}</span>\r\n                </div>\r\n                <div v-if=\"row.section == 8\">\r\n                  <i-switch v-model=\"configData.m2Line\" v-if=\"isEdit\" />\r\n                  <span v-else>{{ row.value }}</span>\r\n                </div>\r\n                <div v-if=\"row.section == 11\">\r\n                  <Select\r\n                    :placeholder=\"$t('LocaleString.D00001')\"\r\n                    v-model=\"configData.influxRange\"\r\n                    v-if=\"isEdit\"\r\n                    transfer\r\n                  >\r\n                    <Option\r\n                      v-for=\"item in defaultInfluxRange\"\r\n                      :value=\"item.value\"\r\n                      :key=\"item.value\"\r\n                    >{{ item.label }}</Option>\r\n                  </Select>\r\n                  <span v-else>{{ row.value }}</span>\r\n                </div>\r\n                <div v-if=\"row.section == 12\">\r\n                  <InputNumber\r\n                    :precision=\"0\"\r\n                    :formatter=\"(value) => `${Number(value.toFixed(2))}`\"\r\n                    :min=\"1\"\r\n                    :max=\"60\"\r\n                    v-if=\"isEdit\"\r\n                    v-model=\"configData.lossSignal\"\r\n                    :placeholder=\"$t('LocaleString.L00001', { 0: $t('LocaleString.L30027') })\r\n      \"\r\n                  ></InputNumber>\r\n                  <Select\r\n                    :placeholder=\"$t('LocaleString.D00001')\"\r\n                    multiple\r\n                    :max-tag-count=\"6\"\r\n                    style=\"margin-left: 5px; width: 736px\"\r\n                    v-model=\"configData.lossSignalDevices\"\r\n                    v-if=\"isEdit\"\r\n                    transfer\r\n                  >\r\n                    <Option\r\n                      v-for=\"item in deviceListLossSignal\"\r\n                      :value=\"item.key\"\r\n                      :key=\"item.key\"\r\n                    >{{ item.value }}</Option>\r\n                  </Select>\r\n                  <div v-else>\r\n                    <Row>\r\n                      <Col span=\"3\">{{ row.value }}</Col>\r\n                      <Col span=\"21\" v-if=\"row.valueDevices !== ''\">\r\n                        <span>{{ row.valueDevices }}</span>\r\n                      </Col>\r\n                    </Row>\r\n                  </div>\r\n                </div>\r\n                <div v-if=\"row.section == 22\">\r\n                  <InputNumber\r\n                    :precision=\"0\"\r\n                    :formatter=\"(value) => `${Number(value.toFixed(2))}`\"\r\n                    :min=\"1\"\r\n                    :max=\"300\"\r\n                    v-if=\"isEdit\"\r\n                    v-model=\"configData.lossSignal2\"\r\n                    :placeholder=\"$t('LocaleString.L00001', { 0: $t('LocaleString.L30027') })\r\n      \"\r\n                  ></InputNumber>\r\n                  <Select\r\n                    :placeholder=\"$t('LocaleString.D00001')\"\r\n                    multiple\r\n                    :max-tag-count=\"6\"\r\n                    style=\"margin-left: 5px; width: 736px\"\r\n                    v-model=\"configData.lossSignalDevices2\"\r\n                    v-if=\"isEdit\"\r\n                    transfer\r\n                  >\r\n                    <Option\r\n                      v-for=\"item in deviceListLossSignal2\"\r\n                      :value=\"item.key\"\r\n                      :key=\"item.key\"\r\n                    >{{ item.value }}</Option>\r\n                  </Select>\r\n                  <div v-else>\r\n                    <Row>\r\n                      <Col span=\"3\">{{ row.value }}</Col>\r\n                      <Col span=\"21\" v-if=\"row.valueDevices !== ''\">\r\n                        <span>{{ row.valueDevices }}</span>\r\n                      </Col>\r\n                    </Row>\r\n                  </div>\r\n                </div>\r\n                <div v-if=\"row.section == 13\">\r\n                  <InputNumber\r\n                    :precision=\"0\"\r\n                    :formatter=\"(value) => `${Number(value.toFixed(2))}`\"\r\n                    :min=\"1\"\r\n                    :max=\"60\"\r\n                    v-if=\"isEdit\"\r\n                    v-model=\"configData.lowBattery\"\r\n                    :placeholder=\"$t('LocaleString.L00001', { 0: '%' })\"\r\n                  ></InputNumber>\r\n                  <Select\r\n                    :placeholder=\"$t('LocaleString.D00001')\"\r\n                    style=\"margin-left: 5px; width: 736px\"\r\n                    :max-tag-count=\"6\"\r\n                    multiple\r\n                    v-model=\"configData.lowBatteryDevices\"\r\n                    v-if=\"isEdit\"\r\n                    transfer\r\n                  >\r\n                    <Option\r\n                      v-for=\"item in deviceListLowBattery\"\r\n                      :value=\"item.key\"\r\n                      :key=\"item.key\"\r\n                    >{{ item.value }}</Option>\r\n                  </Select>\r\n                  <div v-else>\r\n                    <Row>\r\n                      <Col span=\"3\">{{ row.value }}</Col>\r\n                      <Col span=\"21\" v-if=\"row.valueDevices !== ''\">\r\n                        <span>{{ row.valueDevices }}</span>\r\n                      </Col>\r\n                    </Row>\r\n                  </div>\r\n                </div>\r\n                <div v-if=\"row.section == 15\">\r\n                  <Select\r\n                    :placeholder=\"$t('LocaleString.D00001')\"\r\n                    :max-tag-count=\"8\"\r\n                    multiple\r\n                    v-model=\"configData.abnormalDevices\"\r\n                    v-if=\"isEdit\"\r\n                    transfer\r\n                  >\r\n                    <Option\r\n                      v-for=\"item in deviceListAbnormalDevice\"\r\n                      :value=\"item.key\"\r\n                      :key=\"item.key\"\r\n                    >{{ item.value }}</Option>\r\n                  </Select>\r\n                  <div v-else>\r\n                    <Row>\r\n                      <Col span=\"3\">{{ row.value }}</Col>\r\n                      <Col span=\"21\" v-if=\"row.valueDevices !== ''\">\r\n                        <span>{{ row.valueDevices }}</span>\r\n                      </Col>\r\n                    </Row>\r\n                  </div>\r\n                </div>\r\n                <div v-if=\"row.section == 20\">\r\n                  <Select\r\n                    :placeholder=\"$t('LocaleString.D00001')\"\r\n                    v-model=\"configData.defaultPageSize\"\r\n                    v-if=\"isEdit\"\r\n                    transfer\r\n                  >\r\n                    <Option\r\n                      v-for=\"item in pageSizeList\"\r\n                      :value=\"item.value\"\r\n                      :key=\"item.value\"\r\n                    >{{ item.label }}</Option>\r\n                  </Select>\r\n                  <span v-else>{{ defaultPageSizeTitle }}</span>\r\n                </div>\r\n                <div v-if=\"row.section == 21\">\r\n                  <Select\r\n                    :placeholder=\"$t('LocaleString.D00001')\"\r\n                    v-model=\"configData.m5ObjectType\"\r\n                    v-if=\"isEdit\"\r\n                    transfer\r\n                  >\r\n                    <Option\r\n                      v-for=\"item in m5ObjectTypeList\"\r\n                      :value=\"item.value\"\r\n                      :key=\"item.value\"\r\n                    >{{ item.label }}</Option>\r\n                  </Select>\r\n                  <span v-else>{{ m5ObjectTypeTitle }}</span>\r\n                </div>\r\n                <div v-if=\"row.section == 9\">\r\n                  <i-switch v-model=\"configData.eventAuthClose\" v-if=\"isEdit\" />\r\n                  <span v-else>{{ row.value }}</span>\r\n                </div>\r\n                <div v-if=\"row.section == 10\">\r\n                  <div v-if=\"configData.eventAuthClose == true\">\r\n                    <InputNumber\r\n                      :precision=\"0\"\r\n                      :formatter=\"(value) => `${Number(value.toFixed(2))}`\"\r\n                      :min=\"1\"\r\n                      :max=\"7\"\r\n                      v-if=\"isEdit\"\r\n                      v-model=\"configData.eventKeepDays\"\r\n                      :placeholder=\"$t('LocaleString.L00001', {\r\n      0: $t('LocaleString.L20150'),\r\n    })\r\n      \"\r\n                    ></InputNumber>\r\n\r\n                    <span v-else>{{ row.value }}</span>\r\n                  </div>\r\n                </div>\r\n                <div v-if=\"row.section == 14\">\r\n                  <Select\r\n                    :placeholder=\"$t('LocaleString.D00001')\"\r\n                    v-model=\"configData.skipEvent\"\r\n                    v-if=\"isEdit\"\r\n                    multiple\r\n                    :max-tag-count=\"10\"\r\n                    transfer\r\n                  >\r\n                    <Option\r\n                      v-for=\"item in eventServiceCodeList\"\r\n                      :value=\"item.key\"\r\n                      :key=\"item.key\"\r\n                    >{{ item.value }}</Option>\r\n                  </Select>\r\n                  <span v-else>\r\n                    <span v-if=\"row.valueSkipEvent !== ''\">{{ row.valueSkipEvent }}</span>\r\n                  </span>\r\n                </div>\r\n                <div v-if=\"row.section == 16\">\r\n                  <Select\r\n                    :placeholder=\"$t('LocaleString.D00001')\"\r\n                    v-model=\"configData.deviceSelectList\"\r\n                    v-if=\"isEdit\"\r\n                    multiple\r\n                    :max-tag-count=\"10\"\r\n                    transfer\r\n                    @on-open-change=\"openDeviceSelection\"\r\n                    @on-change=\"changeDeviceDefaultSelectList\"\r\n                  >\r\n                    <Option\r\n                      v-for=\"item in deviceDefaultSelection\"\r\n                      :value=\"item.key\"\r\n                      :key=\"item.key\"\r\n                    >{{ item.value }}</Option>\r\n                  </Select>\r\n                  <span v-else>\r\n                    <span v-if=\"row.valueDeviceSelectList !== ''\">{{ row.valueDeviceSelectList }}</span>\r\n                  </span>\r\n                </div>\r\n                <div v-if=\"row.section == 19\">\r\n                  <Select\r\n                    :placeholder=\"$t('LocaleString.D00001')\"\r\n                    v-model=\"configData.m1LongPress\"\r\n                    v-if=\"isEdit\"\r\n                    multiple\r\n                    :max-tag-count=\"8\"\r\n                    transfer\r\n                  >\r\n                    <Option\r\n                      v-for=\"item in deviceDefaultSelection.filter((item) => item.key.includes('fusion-km'))\"\r\n                      :value=\"item.key\"\r\n                      :key=\"item.key\"\r\n                    >{{ item.value }}</Option>\r\n                  </Select>\r\n                  <span v-else>\r\n                    <span v-if=\"row.valueM1LongPressList !== ''\">{{ row.valueM1LongPressList }}</span>\r\n                  </span>\r\n                </div>\r\n                <div v-if=\"row.section == 24\">\r\n                  <i-switch v-model=\"configData.showLeaveBed\" v-if=\"isEdit\" />\r\n                  <span v-else>{{ row.value }}</span>\r\n                </div>\r\n                <div v-if=\"row.section == 25\">\r\n                  <InputNumber\r\n                    :precision=\"0\"\r\n                    :formatter=\"(value) => `${Number(value.toFixed(2))}`\"\r\n                    :min=\"2\"\r\n                    :max=\"600\"\r\n                    v-if=\"isEdit\"\r\n                    v-model=\"configData.m2Statistic\"\r\n                    :placeholder=\"$t('LocaleString.L00001', {\r\n      0: $t('LocaleString.L20150'),\r\n    })\r\n      \"\r\n                  ></InputNumber>\r\n                  <span v-else>{{ row.value }}</span>\r\n                </div>\r\n                <div v-if=\"row.section == 26\">\r\n                  <InputNumber\r\n                    :precision=\"0\"\r\n                    :formatter=\"(value) => `${Number(value.toFixed(2))}`\"\r\n                    :min=\"2\"\r\n                    :max=\"600\"\r\n                    v-if=\"isEdit\"\r\n                    v-model=\"configData.m3Statistic\"\r\n                    :placeholder=\"$t('LocaleString.L00001', {\r\n      0: $t('LocaleString.L20150'),\r\n    })\r\n      \"\r\n                  ></InputNumber>\r\n                  <span v-else>{{ row.value }}</span>\r\n                </div>\r\n                <div v-if=\"row.section == 27\">\r\n                  <InputNumber\r\n                    :precision=\"0\"\r\n                    :formatter=\"(value) => `${Number(value.toFixed(2))}`\"\r\n                    :min=\"2\"\r\n                    :max=\"600\"\r\n                    v-if=\"isEdit\"\r\n                    v-model=\"configData.m4Statistic\"\r\n                    :placeholder=\"$t('LocaleString.L00001', {\r\n      0: $t('LocaleString.L20150'),\r\n    })\r\n      \"\r\n                  ></InputNumber>\r\n                  <span v-else>{{ row.value }}</span>\r\n                </div>\r\n                <div v-if=\"row.section == 28\">\r\n                  <InputNumber\r\n                    :precision=\"0\"\r\n                    :formatter=\"(value) => `${Number(value.toFixed(2))}`\"\r\n                    :min=\"2\"\r\n                    :max=\"600\"\r\n                    v-if=\"isEdit\"\r\n                    v-model=\"configData.m8Statistic\"\r\n                    :placeholder=\"$t('LocaleString.L00001', {\r\n      0: $t('LocaleString.L20150'),\r\n    })\r\n      \"\r\n                  ></InputNumber>\r\n                  <span v-else>{{ row.value }}</span>\r\n                </div>\r\n                <div v-if=\"row.section == 29\">\r\n                  <InputNumber\r\n                    :precision=\"0\"\r\n                    :formatter=\"(value) => `${Number(value.toFixed(2))}`\"\r\n                    :min=\"2\"\r\n                    :max=\"600\"\r\n                    v-if=\"isEdit\"\r\n                    v-model=\"configData.m9Statistic\"\r\n                    :placeholder=\"$t('LocaleString.L00001', {\r\n      0: $t('LocaleString.L20150'),\r\n    })\r\n      \"\r\n                  ></InputNumber>\r\n                  <span v-else>{{ row.value }}</span>\r\n                </div>\r\n                <div v-if=\"row.section == 32\">\r\n                  <ColorPicker v-model=\"configData.stateColor\" v-if=\"isEdit\" />\r\n                  <span v-else>\r\n                    <div\r\n                      :style=\"{height:'20px',width:'20px',borderRadius:'2px' , backgroundColor: configData.stateColor}\"\r\n                    ></div>\r\n                  </span>\r\n                </div>\r\n              </template>\r\n            </Table>\r\n\r\n            <Row style=\"margin-top:10px\" v-if=\"isAdmin\">\r\n              <Col\r\n                span=\"4\"\r\n                style=\"padding-left:10px;line-height:32px\"\r\n              >{{ $t('LocaleString.L30155') }}</Col>\r\n              <Col span=\"20\">\r\n                <Input\r\n                  v-model=\"loginAccount\"\r\n                  :placeholder=\"$t('LocaleString.L00001', { 0: $t('LocaleString.L00002') })\r\n      \"\r\n                  style=\"width:200px;margin-right:10px\"\r\n                />\r\n                <Input\r\n                  type=\"password\"\r\n                  v-model=\"loginPassword\"\r\n                  autocomplete=\"new-password\"\r\n                  :placeholder=\"$t('LocaleString.L00001', { 0: $t('LocaleString.L00003') })\r\n      \"\r\n                  style=\"width:200px;margin-right:10px\"\r\n                />\r\n                <Button type=\"text\" @click=\"generateToken\" style=\"width:30px\">\r\n                  <img :src=\"generateIcon\" style=\"margin-left:-15px;width: 30px;;margin-right:5px\" />\r\n                </Button>\r\n                <Input\r\n                  type=\"password\"\r\n                  id=\"generateTokenData\"\r\n                  v-model=\"token\"\r\n                  autocomplete=\"new-password\"\r\n                  style=\"width:420px;margin-right:10px\"\r\n                />\r\n                <Button type=\"text\" @click=\"copyToken\" style=\"width:30px\">\r\n                  <img :src=\"copyIcon\" style=\"margin-left:-15px;width: 30px;\" />\r\n                </Button>\r\n              </Col>\r\n            </Row>\r\n          </TabPane>\r\n          <TabPane :label=\"$t('LocaleString.L30107')\" name=\"km\">\r\n            <Table\r\n              v-if=\"!isEdit\"\r\n              class=\"config-table\"\r\n              :columns=\"kmColumnsList\"\r\n              :data=\"kmConfigList\"\r\n              :no-data-text=\"noDataText\"\r\n            >\r\n              <template slot-scope=\"{ row, index }\" slot=\"type\">\r\n                <span>{{ row.type }}</span>\r\n              </template>\r\n            </Table>\r\n            <Table\r\n              v-if=\"isEdit\"\r\n              class=\"config-table\"\r\n              :columns=\"kmColumnsEdit\"\r\n              :data=\"kmConfigListEdit\"\r\n              :no-data-text=\"noDataText\"\r\n            >\r\n              <template slot-scope=\"{ row, index }\" slot=\"type\">\r\n                <span>{{ getObjectType(row.type) }}</span>\r\n              </template>\r\n              <template slot-scope=\"{ row, index }\" slot=\"longPress_cht\">\r\n                <Input\r\n                  type=\"text\"\r\n                  v-model=\"row.longPress_cht\"\r\n                  maxlength=\"20\"\r\n                  @on-change=\"onEditDataChange('longPress_cht', row, index)\"\r\n                />\r\n              </template>\r\n              <template slot-scope=\"{ row, index }\" slot=\"longPress_en\">\r\n                <Input\r\n                  type=\"text\"\r\n                  v-model=\"row.longPress_en\"\r\n                  maxlength=\"20\"\r\n                  @on-change=\"onEditDataChange('longPress_en', row, index)\"\r\n                />\r\n              </template>\r\n              <template slot-scope=\"{ row, index }\" slot=\"shortClick_cht\">\r\n                <Input\r\n                  type=\"text\"\r\n                  v-model=\"row.shortClick_cht\"\r\n                  maxlength=\"20\"\r\n                  @on-change=\"onEditDataChange('shortClick_cht', row, index)\"\r\n                />\r\n              </template>\r\n              <template slot-scope=\"{ row, index }\" slot=\"shortClick_en\">\r\n                <Input\r\n                  type=\"text\"\r\n                  v-model=\"row.shortClick_en\"\r\n                  maxlength=\"20\"\r\n                  @on-change=\"onEditDataChange('shortClick_en', row, index)\"\r\n                />\r\n              </template>\r\n              <template slot-scope=\"{ row, index }\" slot=\"doubleClick_cht\">\r\n                <Input\r\n                  type=\"text\"\r\n                  v-model=\"row.doubleClick_cht\"\r\n                  maxlength=\"20\"\r\n                  @on-change=\"onEditDataChange('doubleClick_cht', row, index)\"\r\n                />\r\n              </template>\r\n              <template slot-scope=\"{ row, index }\" slot=\"doubleClick_en\">\r\n                <Input\r\n                  type=\"text\"\r\n                  v-model=\"row.doubleClick_en\"\r\n                  maxlength=\"20\"\r\n                  @on-change=\"onEditDataChange('doubleClick_en', row, index)\"\r\n                />\r\n              </template>\r\n            </Table>\r\n          </TabPane>\r\n          <TabPane :label=\"$t('LocaleString.L30139')\" name=\"camera\" v-if=\"isAdmin\">\r\n            <Table\r\n              class=\"config-table\"\r\n              :columns=\"cameraColumnsList\"\r\n              :data=\"cameraConfigList\"\r\n              :no-data-text=\"noDataText\"\r\n            >\r\n              <template slot-scope=\"{ row, index }\" slot=\"capture\">\r\n                <i-switch\r\n                  v-if=\"isAdmin && isEdit\"\r\n                  v-model=\"row.capture\"\r\n                  @on-change=\"onCameraDataChange('capture', row, index)\"\r\n                />\r\n                <span v-if=\"!isEdit || !isAdmin\">\r\n                  {{ row.capture ? $t('LocaleString.D00003') : $t('LocaleString.D00004')\r\n                  }}\r\n                </span>\r\n              </template>\r\n              <template slot-scope=\"{ row, index }\" slot=\"prefixMinute\">\r\n                <InputNumber\r\n                  style=\"width:50px\"\r\n                  v-if=\"row.capture && isAdmin && isEdit\"\r\n                  v-model=\"row.prefixMinute\"\r\n                  :precision=\"0\"\r\n                  :min=\"0\"\r\n                  :max=\"15\"\r\n                  @on-change=\"onCameraDataChange('prefixMinute', row, index)\"\r\n                />\r\n                <span v-if=\"(!isEdit && row.capture) || !isAdmin\">{{ row.prefixMinute }}</span>\r\n              </template>\r\n              <template slot-scope=\"{ row, index }\" slot=\"suffixMinute\">\r\n                <InputNumber\r\n                  style=\"width:50px\"\r\n                  v-if=\"row.capture && isAdmin && isEdit\"\r\n                  v-model=\"row.suffixMinute\"\r\n                  :precision=\"0\"\r\n                  :min=\"0\"\r\n                  :max=\"15\"\r\n                  @on-change=\"onCameraDataChange('suffixMinute', row, index)\"\r\n                />\r\n                <span v-if=\"(!isEdit && row.capture) || !isAdmin\">{{ row.suffixMinute }}</span>\r\n              </template>\r\n              <template slot-scope=\"{ row, index }\" slot=\"backupDirectory\">\r\n                <Input\r\n                  v-model=\"row.backupDirectory\"\r\n                  @on-change=\"onCameraDataChange('backupDirectory', row, index)\"\r\n                  v-if=\"row.capture && isAdmin && isEdit\"\r\n                />\r\n                <span v-if=\"(!isEdit && row.capture) || !isAdmin\">{{ row.backupDirectory }}</span>\r\n              </template>\r\n              <template slot-scope=\"{ row, index }\" slot=\"account\">\r\n                <Input\r\n                  v-if=\"row.capture && isAdmin && isEdit\"\r\n                  v-model=\"row.account\"\r\n                  @on-change=\"onCameraDataChange('account', row, index)\"\r\n                />\r\n                <span v-if=\"(!isEdit && row.capture) || !isAdmin\">{{ row.account }}</span>\r\n              </template>\r\n              <template slot-scope=\"{ row, index }\" slot=\"password\">\r\n                <Input\r\n                  type=\"password\"\r\n                  v-model=\"row.password\"\r\n                  autocomplete=\"new-password\"\r\n                  @on-change=\"onCameraDataChange('password', row, index)\"\r\n                  v-if=\"row.capture && isAdmin && isEdit\"\r\n                />\r\n                <span v-if=\"(!isEdit && row.capture) || !isAdmin\">{{ row.password }}</span>\r\n              </template>\r\n              <template slot-scope=\"{ row, index }\" slot=\"keepDays\">\r\n                <InputNumber\r\n                  style=\"width:70px\"\r\n                  v-if=\"row.capture && isAdmin && isEdit\"\r\n                  v-model=\"row.keepDays\"\r\n                  :precision=\"0\"\r\n                  :min=\"0\"\r\n                  @on-change=\"onCameraDataChange('keepDays', row, index)\"\r\n                />\r\n                <span v-if=\"(!isEdit && row.capture) || !isAdmin\">{{ row.keepDays }}</span>\r\n              </template>\r\n              <template slot-scope=\"{ row, index }\" slot=\"positionCapture\">\r\n                <InputNumber\r\n                  v-if=\"row.capture && isAdmin && isEdit\"\r\n                  :precision=\"0\"\r\n                  :min=\"0\"\r\n                  :max=\"15\"\r\n                  v-model=\"row.positionCapture\"\r\n                  @on-change=\"onCameraDataChange('positionCapture', row, index)\"\r\n                />\r\n                <span v-if=\"(!isEdit && row.capture) || !isAdmin\">{{ row.positionCapture }}</span>\r\n              </template>\r\n              <template slot-scope=\"{ row, index }\" slot=\"action\">\r\n                <Button\r\n                  @click=\"testCameraConnection(row)\"\r\n                  type=\"primary\"\r\n                  class=\"font-small\"\r\n                  v-if=\"row.capture && isAdmin && isEdit\"\r\n                >\r\n                  {{\r\n                  $t(\"LocaleString.B30010\") }}\r\n                </Button>\r\n              </template>\r\n            </Table>\r\n          </TabPane>\r\n          <TabPane :label=\"$t('LocaleString.L30177')\" name=\"statisticLine\">\r\n            <Table\r\n              class=\"config-table\"\r\n              :columns=\"statisticLineColumnsList\"\r\n              :data=\"statisticLineConfigList\"\r\n              :no-data-text=\"noDataText\"\r\n            >\r\n              <template slot-scope=\"{ row }\" slot=\"type\">\r\n                <span>{{ row.type }}</span>\r\n              </template>\r\n              <template slot-scope=\"{ row,index }\" slot=\"interval\">\r\n                <Select\r\n                  :placeholder=\"$t('LocaleString.D00001')\"\r\n                  v-model=\"row.interval\"\r\n                  v-if=\"isEdit\"\r\n                  transfer\r\n                  @on-change=\"onStatisticLineDataChange('interval', row, index)\"\r\n                >\r\n                  <Option v-for=\"item in 10\" :value=\"item\" :key=\"item\">{{ item }}</Option>\r\n                </Select>\r\n                <span v-else>{{ row.interval }}</span>\r\n              </template>\r\n              <template slot-scope=\"{ row,index }\" slot=\"dataType\">\r\n                <Select\r\n                  :placeholder=\"$t('LocaleString.D00001')\"\r\n                  v-model=\"row.dataType\"\r\n                  v-if=\"isEdit\"\r\n                  transfer\r\n                  @on-change=\"onStatisticLineDataChange('dataType', row, index)\"\r\n                >\r\n                  <Option\r\n                    v-for=\"item in dataTypeList\"\r\n                    :value=\"item.key\"\r\n                    :key=\"item.key\"\r\n                  >{{ item.value }}</Option>\r\n                </Select>\r\n                <span v-else>{{ dataTypeList.find(item=> item.key == row.dataType).value }}</span>\r\n              </template>\r\n              <template slot-scope=\"{ row, index }\" slot=\"warningLine\">\r\n                <i-switch\r\n                  v-if=\"isEdit\"\r\n                  v-model=\"row.warningLine\"\r\n                  @on-change=\"onStatisticLineDataChange('warningLine', row, index)\"\r\n                />\r\n                <span\r\n                  v-else\r\n                >{{ row.warningLine == true ? $t('LocaleString.D30029') : $t('LocaleString.D30030') }}</span>\r\n              </template>\r\n            </Table>\r\n          </TabPane>\r\n          <TabPane :label=\"$t('LocaleString.L30194')\" name=\"thirdParty\" v-if=\"isAdmin\">\r\n            <Table\r\n              class=\"config-table\"\r\n              :columns=\"columns\"\r\n              :data=\"thirdPartyConfigList\"\r\n              :no-data-text=\"noDataText\"\r\n            >\r\n              <template slot-scope=\"{ row }\" slot=\"item\">\r\n                <span>{{ row.item }}</span>\r\n              </template>\r\n              <template slot-scope=\"{ row,index }\" slot=\"value\">\r\n                <div v-if=\"row.section == 1\">\r\n                  <i-switch\r\n                    @on-change=\"switchloginThirdParty\"\r\n                    v-model=\"thirdPartyData.loginThirdParty\"\r\n                    v-if=\"isEdit\"\r\n                  />\r\n                  <span\r\n                    style=\"margin-right:25px\"\r\n                    v-else\r\n                  >{{ viewLoginThirdParty == \"\" ? $t(\"LocaleString.B20010\") :viewLoginThirdParty }}</span>\r\n                </div>\r\n                <div v-if=\"row.section == 2\">\r\n                  <Input v-if=\"isEdit\" v-model=\"thirdPartyData.url\"></Input>\r\n                  <span v-else>{{ row.value }}</span>\r\n                </div>\r\n                <div v-if=\"row.section == 3\">\r\n                  <Input v-if=\"isEdit\" v-model=\"thirdPartyData.userName\"></Input>\r\n                  <span v-else>{{ row.value }}</span>\r\n                </div>\r\n                <div v-if=\"row.section == 4\">\r\n                  <Input v-if=\"isEdit\" v-model=\"thirdPartyData.secret\"></Input>\r\n                  <span v-else>{{ row.value }}</span>\r\n                </div>\r\n              </template>\r\n            </Table>\r\n            <Button\r\n              v-if=\"thirdPartyData.loginThirdParty\"\r\n              style=\"margin-top:5px\"\r\n              @click=\"testConnection\"\r\n            >{{ $t(\"LocaleString.B30010\")}}</Button>\r\n          </TabPane>\r\n        </Tabs>\r\n      </div>\r\n      <div slot=\"footer\" style=\"text-align: center; margin-top: 10px\">\r\n        <Button type=\"primary\" class=\"font-small\" @click=\"cancel\" v-show=\"!isEdit\">\r\n          {{ $t(\"LocaleString.B00044\")\r\n          }}\r\n        </Button>\r\n      </div>\r\n    </Modal>\r\n    <Modal\r\n      v-model=\"tipModal\"\r\n      :title=\"$t('LocaleString.L30061')\"\r\n      :mask-closable=\"false\"\r\n      :closable=\"false\"\r\n    >\r\n      <div class=\"message\">\r\n        <div>\r\n          <p>{{ $t(\"LocaleString.L30067\") }}</p>\r\n          <p>\r\n            <B>{{ $t(\"LocaleString.L30106\") }}</B>\r\n          </p>\r\n          <p>\r\n            <B>{{ $t(\"LocaleString.L30068\") }}</B>\r\n          </p>\r\n          <br />\r\n          <p>{{ $t(\"LocaleString.L30066\") }}</p>\r\n        </div>\r\n      </div>\r\n      <div slot=\"footer\" style=\"text-align: center; margin-top: 5px\">\r\n        <Button type=\"primary\" @click=\"tipModal = false\">\r\n          {{\r\n          $t(\"LocaleString.B00044\")\r\n          }}\r\n        </Button>\r\n      </div>\r\n    </Modal>\r\n    <Modal\r\n      v-model=\"tip2Modal\"\r\n      :title=\"$t('LocaleString.L30062')\"\r\n      :mask-closable=\"false\"\r\n      :closable=\"false\"\r\n    >\r\n      <div class=\"message\">\r\n        <div>\r\n          <p>{{ $t(\"LocaleString.L30063\") }}</p>\r\n          <p>\r\n            <B>{{ $t(\"LocaleString.L30064\") }}</B>\r\n          </p>\r\n          <p>\r\n            <B>{{ $t(\"LocaleString.L30065\") }}</B>\r\n          </p>\r\n          <br />\r\n          <p>{{ $t(\"LocaleString.L30066\") }}</p>\r\n        </div>\r\n      </div>\r\n      <div slot=\"footer\" style=\"text-align: center; margin-top: 5px\">\r\n        <Button type=\"primary\" @click=\"tip2Modal = false\">\r\n          {{\r\n          $t(\"LocaleString.B00044\")\r\n          }}\r\n        </Button>\r\n      </div>\r\n    </Modal>\r\n    <Modal\r\n      v-model=\"tip3Modal\"\r\n      :title=\"$t('LocaleString.L30101')\"\r\n      :mask-closable=\"false\"\r\n      :closable=\"false\"\r\n    >\r\n      <div class=\"message\">\r\n        <div>\r\n          <p>\r\n            <B>{{ $t(\"LocaleString.L30102\") }}</B>\r\n          </p>\r\n          <br />\r\n          <p>{{ $t(\"LocaleString.L30066\") }}</p>\r\n        </div>\r\n      </div>\r\n      <div slot=\"footer\" style=\"text-align: center; margin-top: 5px\">\r\n        <Button type=\"primary\" @click=\"tip3Modal = false\">\r\n          {{\r\n          $t(\"LocaleString.B00044\")\r\n          }}\r\n        </Button>\r\n      </div>\r\n    </Modal>\r\n    <Modal\r\n      v-model=\"tip4Modal\"\r\n      :title=\"$t('LocaleString.L30116')\"\r\n      :mask-closable=\"false\"\r\n      :closable=\"false\"\r\n    >\r\n      <div class=\"message\">\r\n        <div>\r\n          <p>{{ $t(\"LocaleString.L30117\") }}</p>\r\n        </div>\r\n      </div>\r\n      <div slot=\"footer\" style=\"text-align: center; margin-top: 5px\">\r\n        <Button type=\"primary\" @click=\"tip4Modal = false\">\r\n          {{\r\n          $t(\"LocaleString.B00044\")\r\n          }}\r\n        </Button>\r\n      </div>\r\n    </Modal>\r\n    <Modal\r\n      v-model=\"tip5Modal\"\r\n      :title=\"$t('LocaleString.L30129')\"\r\n      :mask-closable=\"false\"\r\n      :closable=\"false\"\r\n    >\r\n      <div class=\"message\">\r\n        <div>\r\n          <p>{{ $t(\"LocaleString.L30130\") }}</p>\r\n        </div>\r\n      </div>\r\n      <div slot=\"footer\" style=\"text-align: center; margin-top: 5px\">\r\n        <Button type=\"primary\" @click=\"tip5Modal = false\">\r\n          {{\r\n          $t(\"LocaleString.B00044\")\r\n          }}\r\n        </Button>\r\n      </div>\r\n    </Modal>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport resetIcon from \"@/assets/images/ic_reset.svg\";\r\nimport copyIcon from \"@/assets/images/ic_copy.png\";\r\nimport generateIcon from \"@/assets/images/ic_generate.png\";\r\nimport Config from \"@/common/config\";\r\nimport CommonService from \"@/components/administrative/apps/commonService\";\r\nimport InfoIcon from \"@/components/administrative/common/images/pointPlane/icon-ic-info.svg\";\r\nconst alertSound = require(\"@/assets/beep.mp3\"); // require the sound\r\nlet Base64 = require(\"js-base64\");\r\nlet moment = require(\"moment\");\r\nexport default {\r\n  data() {\r\n    return {\r\n      thirdPartyData: {\r\n        loginThirdParty: false,\r\n        url: \"\",\r\n        userName: \"\",\r\n        secret: \"\"\r\n      },\r\n      dataTypeList: [\r\n        { key: \"MEDIAN\", value: this.$t(\"LocaleString.L30178\") },\r\n        { key: \"MEAN\", value: this.$t(\"LocaleString.L30179\") },\r\n        { key: \"LAST\", value: this.$t(\"LocaleString.L30180\") }\r\n      ],\r\n      serviceCodeList: [],\r\n      serviceNameList: [],\r\n      copyIcon: copyIcon,\r\n      generateIcon: generateIcon,\r\n      loginAccount: \"\",\r\n      loginPassword: \"\",\r\n      token: \"\",\r\n      isAdmin: false,\r\n      openDeviceSelect: false,\r\n      description: \"\",\r\n      tabButtonWidth: {\r\n        width: \"1150px\"\r\n      },\r\n      modalWidth: 1200,\r\n      modalInnerHeight: window.innerHeight - 300,\r\n      currentTab: \"system\",\r\n      showCleanIcon: false,\r\n      resetIcon: resetIcon,\r\n      imgURL: \"\",\r\n      oriTab: \"\",\r\n      eventServiceCodeList: [],\r\n      serviceCodeLocaleStringsList: [],\r\n      submitLoading: false,\r\n      allDeviceTypeMenu: null,\r\n      deviceListLossSignal: [],\r\n      deviceListLossSignal2: [],\r\n      deviceListLowBattery: [],\r\n      deviceListAbnormalDevice: [],\r\n      alertSound: alertSound,\r\n      imgFile: null,\r\n      imgFileURL: null,\r\n      imgFileOriURL: null,\r\n      fileURL: null,\r\n      fileOriURL: null,\r\n      tempURL: null,\r\n      file: null,\r\n      isPlay: false,\r\n      tipModal: false,\r\n      tip2Modal: false,\r\n      tip3Modal: false,\r\n      tip4Modal: false,\r\n      tip5Modal: false,\r\n      InfoIcon: InfoIcon,\r\n      iconSizeTitle: \"\",\r\n      m5ObjectTypeTitle: \"\",\r\n      defaultPageSizeTitle: \"\",\r\n      defaultTabTitle: \"\",\r\n      origConfigData: {},\r\n      origThirdPartyData: {},\r\n      configData: {\r\n        defaultPageSize: \"\",\r\n        m5ObjectType: \"all\",\r\n        m1LongPress: [\"fusion-km7\"],\r\n        multiPlanes: false,\r\n        stuckPlane: false,\r\n        selectedPlanes: [],\r\n        planeList: [],\r\n        systemName: \"\",\r\n        tabList: [\"m1\", \"m2\", \"m3\", \"m4\", \"m5\", \"m6\", \"m8\"],\r\n        soundURL: null,\r\n        sound: false,\r\n        repeatSound: 0,\r\n        m2List: [\r\n          \"temperature\",\r\n          \"heartRate\",\r\n          \"bloodOxygen\",\r\n          \"bloodPressure\",\r\n          \"step\"\r\n        ],\r\n        iconSize: \"40\",\r\n        defaultTab: \"m1\",\r\n        updateSeconds: \"30\",\r\n        grayTime: 15,\r\n        m2Line: false,\r\n        eventAuthClose: false,\r\n        eventKeepDays: \"2\",\r\n        influxRange: 8,\r\n        lossSignal: 1,\r\n        lossSignal2: 60,\r\n        lowBattery: 30,\r\n        lossSignalDevices: null,\r\n        lossSignalDevices2: [],\r\n        lowBatteryDevices: null,\r\n        abnormalDevices: null,\r\n        skipEvent: [],\r\n        deviceSelectList: [],\r\n        license: [],\r\n        showLeaveBed: false,\r\n        m2Statistic: 10,\r\n        m3Statistic: 10,\r\n        m4Statistic: 60,\r\n        m8Statistic: 60,\r\n        m9Statistic: 60,\r\n        stateColor: \"#000000\",\r\n        logoutTime: 0\r\n      },\r\n      viewTab: \"\",\r\n      viewLicense: \"\",\r\n      viewPlaySound: \"\",\r\n      viewM2Line: \"\",\r\n      viewM2List: \"\",\r\n      viewEventAuthClose: \"\",\r\n      viewEventKeepDays: \"\",\r\n      viewLogo: \"\",\r\n      viewMultiPlanes: \"\",\r\n      viewStuckPlane: \"\",\r\n      viewLoginThirdParty: \"\",\r\n      isEdit: false,\r\n      isShow: false,\r\n\r\n      configList: [],\r\n      configListToSave: [],\r\n      noDataText: this.$t(\"LocaleString.L00081\"),\r\n      kmObjectTypeList: [\r\n        {\r\n          type: \"equipment\",\r\n          name: this.$t(\"LocaleString.D00008\")\r\n        },\r\n        {\r\n          type: \"people\",\r\n          name: this.$t(\"LocaleString.D00007\")\r\n        },\r\n        {\r\n          type: \"space\",\r\n          name: this.$t(\"LocaleString.D00009\")\r\n        },\r\n        {\r\n          type: \"other\",\r\n          name: this.$t(\"LocaleString.D00010\")\r\n        }\r\n      ],\r\n      cameraTypeList: [\r\n        {\r\n          type: \"help\",\r\n          name: this.$t(\"LocaleString.D00030\")\r\n        },\r\n        {\r\n          type: \"enter\",\r\n          name: this.$t(\"LocaleString.D00029\")\r\n        },\r\n        {\r\n          type: \"leave\",\r\n          name: this.$t(\"LocaleString.D00031\")\r\n        },\r\n        {\r\n          type: \"fall\",\r\n          name: this.$t(\"LocaleString.D00035\")\r\n        }\r\n      ],\r\n      cameraColumnsList: [\r\n        {\r\n          title: this.$t(\"LocaleString.L00047\"),\r\n          slot: \"type\",\r\n          width: 90,\r\n          render: (h, params) => {\r\n            return h(\"span\", this.getCameraType(params.row.type));\r\n          }\r\n        },\r\n        {\r\n          title: this.$t(\"LocaleString.L30140\"),\r\n          slot: \"capture\",\r\n          width: 70\r\n        },\r\n        {\r\n          title: this.$t(\"LocaleString.L30141\"),\r\n          slot: \"prefixMinute\",\r\n          width: 85\r\n        },\r\n        {\r\n          title: this.$t(\"LocaleString.L30142\"),\r\n          slot: \"suffixMinute\",\r\n          width: 85\r\n        },\r\n        {\r\n          title: this.$t(\"LocaleString.L30143\"),\r\n          slot: \"backupDirectory\"\r\n        },\r\n        {\r\n          title: this.$t(\"LocaleString.L30144\"),\r\n          slot: \"account\",\r\n          width: 110\r\n        },\r\n        {\r\n          title: this.$t(\"LocaleString.L00003\"),\r\n          slot: \"password\",\r\n          width: 110\r\n        },\r\n        {\r\n          title: this.$t(\"LocaleString.L30145\"),\r\n          slot: \"keepDays\",\r\n          width: 90\r\n        },\r\n        {\r\n          title: this.$t(\"LocaleString.L30146\"),\r\n          slot: \"positionCapture\",\r\n          width: 120\r\n        },\r\n        {\r\n          title: \" \",\r\n          slot: \"action\",\r\n          width: 110\r\n        }\r\n      ],\r\n      statisticLineColumnsList: [\r\n        {\r\n          title: this.$t(\"LocaleString.L30057\"),\r\n          slot: \"type\"\r\n        },\r\n        {\r\n          title: this.$t(\"LocaleString.L30150\"),\r\n          slot: \"interval\"\r\n        },\r\n        {\r\n          title: this.$t(\"LocaleString.L00157\"),\r\n          slot: \"dataType\"\r\n        },\r\n        {\r\n          title: this.$t(\"LocaleString.L30151\"),\r\n          slot: \"warningLine\"\r\n        }\r\n      ],\r\n      defaultStatisticLineConfigList: [\r\n        {\r\n          type: this.$t(\"LocaleString.F30002\"),\r\n          interval: 1,\r\n          dataType: \"MEDIAN\",\r\n          warningLine: true,\r\n          entry: \"m2\"\r\n        },\r\n        {\r\n          type: this.$t(\"LocaleString.F30003\"),\r\n          interval: 1,\r\n          dataType: \"MEDIAN\",\r\n          warningLine: true,\r\n          entry: \"m3\"\r\n        },\r\n        {\r\n          type: this.$t(\"LocaleString.F30004\"),\r\n          interval: 1,\r\n          dataType: \"MEDIAN\",\r\n          warningLine: true,\r\n          entry: \"m4\"\r\n        },\r\n        {\r\n          type: this.$t(\"LocaleString.F30016\"),\r\n          interval: 1,\r\n          dataType: \"MEDIAN\",\r\n          warningLine: true,\r\n          entry: \"m8\"\r\n        }\r\n      ],\r\n      statisticLineConfigList: [],\r\n      thirdPartyConfigList: [\r\n        {\r\n          item: this.$t(\"LocaleString.L30195\"),\r\n          section: 1,\r\n          value: false\r\n        },\r\n        {\r\n          item: this.$t(\"LocaleString.L30196\"),\r\n          section: 2,\r\n          value: \"\"\r\n        },\r\n        {\r\n          item: \"UserName\",\r\n          section: 3,\r\n          value: \"\"\r\n        },\r\n        {\r\n          item: \"CredentialSecret\",\r\n          section: 4,\r\n          value: \"\"\r\n        }\r\n      ],\r\n      defaultCameraConfigList: [\r\n        {\r\n          type: \"help\",\r\n          capture: false,\r\n          prefixMinute: 3,\r\n          suffixMinute: 3,\r\n          backupDirectory: \"\",\r\n          account: \"\",\r\n          password: \"\",\r\n          keepDays: 90,\r\n          positionCapture: 3\r\n        },\r\n        {\r\n          type: \"enter\",\r\n          capture: false,\r\n          prefixMinute: 3,\r\n          suffixMinute: 3,\r\n          backupDirectory: \"\",\r\n          account: \"\",\r\n          password: \"\",\r\n          keepDays: 90,\r\n          positionCapture: 3\r\n        },\r\n        {\r\n          type: \"leave\",\r\n          capture: false,\r\n          prefixMinute: 3,\r\n          suffixMinute: 3,\r\n          backupDirectory: \"\",\r\n          account: \"\",\r\n          password: \"\",\r\n          keepDays: 90,\r\n          positionCapture: 3\r\n        },\r\n        {\r\n          type: \"fall\",\r\n          capture: false,\r\n          prefixMinute: 3,\r\n          suffixMinute: 3,\r\n          backupDirectory: \"\",\r\n          account: \"\",\r\n          password: \"\",\r\n          keepDays: 90,\r\n          positionCapture: 3\r\n        }\r\n      ],\r\n      cameraConfigList: [],\r\n      kmColumnsList: [\r\n        {\r\n          title: this.$t(\"LocaleString.L00047\"),\r\n          key: \"type\",\r\n          width: 100,\r\n          render: (h, params) => {\r\n            return h(\"span\", this.getObjectType(params.row.type));\r\n          }\r\n        },\r\n        {\r\n          title: this.$t(\"LocaleString.L30108\"),\r\n          key: \"longPress_cht\"\r\n        },\r\n        {\r\n          title: this.$t(\"LocaleString.L30109\"),\r\n          key: \"longPress_en\"\r\n        },\r\n        {\r\n          title: this.$t(\"LocaleString.L30110\"),\r\n          key: \"shortClick_cht\"\r\n        },\r\n        {\r\n          title: this.$t(\"LocaleString.L30111\"),\r\n          key: \"shortClick_en\"\r\n        },\r\n        {\r\n          title: this.$t(\"LocaleString.L30112\"),\r\n          key: \"doubleClick_cht\"\r\n        },\r\n        {\r\n          title: this.$t(\"LocaleString.L30113\"),\r\n          key: \"doubleClick_en\"\r\n        }\r\n      ],\r\n      kmColumnsEdit: [\r\n        {\r\n          title: this.$t(\"LocaleString.L00047\"),\r\n          slot: \"type\",\r\n          width: 100,\r\n          render: (h, params) => {\r\n            return h(\"span\", this.getObjectType(params.row.type));\r\n          }\r\n        },\r\n        {\r\n          title: this.$t(\"LocaleString.L30108\"),\r\n          slot: \"longPress_cht\"\r\n        },\r\n        {\r\n          title: this.$t(\"LocaleString.L30109\"),\r\n          slot: \"longPress_en\"\r\n        },\r\n        {\r\n          title: this.$t(\"LocaleString.L30110\"),\r\n          slot: \"shortClick_cht\"\r\n        },\r\n        {\r\n          title: this.$t(\"LocaleString.L30111\"),\r\n          slot: \"shortClick_en\"\r\n        },\r\n        {\r\n          title: this.$t(\"LocaleString.L30112\"),\r\n          slot: \"doubleClick_cht\"\r\n        },\r\n        {\r\n          title: this.$t(\"LocaleString.L30113\"),\r\n          slot: \"doubleClick_en\"\r\n        }\r\n      ],\r\n      kmConfigList: [\r\n        {\r\n          type: \"equipment\",\r\n          longPress_cht: \"\",\r\n          longPress_en: \"\",\r\n          shortClick_cht: \"\",\r\n          shortClick_en: \"\",\r\n          doubleClick_cht: \"\",\r\n          doubleClick_en: \"\"\r\n        },\r\n        {\r\n          type: \"people\",\r\n          longPress_cht: \"\",\r\n          longPress_en: \"\",\r\n          shortClick_cht: \"\",\r\n          shortClick_en: \"\",\r\n          doubleClick_cht: \"\",\r\n          doubleClick_en: \"\"\r\n        },\r\n        {\r\n          type: \"space\",\r\n          longPress_cht: \"\",\r\n          longPress_en: \"\",\r\n          shortClick_cht: \"\",\r\n          shortClick_en: \"\",\r\n          doubleClick_cht: \"\",\r\n          doubleClick_en: \"\"\r\n        },\r\n        {\r\n          type: \"other\",\r\n          longPress_cht: \"\",\r\n          longPress_en: \"\",\r\n          shortClick_cht: \"\",\r\n          shortClick_en: \"\",\r\n          doubleClick_cht: \"\",\r\n          doubleClick_en: \"\"\r\n        }\r\n      ],\r\n      kmConfigListEdit: [\r\n        {\r\n          type: \"equipment\",\r\n          longPress_cht: \"\",\r\n          longPress_en: \"\",\r\n          shortClick_cht: \"\",\r\n          shortClick_en: \"\",\r\n          doubleClick_cht: \"\",\r\n          doubleClick_en: \"\"\r\n        },\r\n        {\r\n          type: \"people\",\r\n          longPress_cht: \"\",\r\n          longPress_en: \"\",\r\n          shortClick_cht: \"\",\r\n          shortClick_en: \"\",\r\n          doubleClick_cht: \"\",\r\n          doubleClick_en: \"\"\r\n        },\r\n        {\r\n          type: \"space\",\r\n          longPress_cht: \"\",\r\n          longPress_en: \"\",\r\n          shortClick_cht: \"\",\r\n          shortClick_en: \"\",\r\n          doubleClick_cht: \"\",\r\n          doubleClick_en: \"\"\r\n        },\r\n        {\r\n          type: \"other\",\r\n          longPress_cht: \"\",\r\n          longPress_en: \"\",\r\n          shortClick_cht: \"\",\r\n          shortClick_en: \"\",\r\n          doubleClick_cht: \"\",\r\n          doubleClick_en: \"\"\r\n        }\r\n      ],\r\n      columns: [\r\n        {\r\n          title: this.$t(\"LocaleString.L00183\"),\r\n          slot: \"item\"\r\n          // width: 360\r\n        },\r\n        {\r\n          title: this.$t(\"LocaleString.L00184\"),\r\n          slot: \"value\"\r\n        }\r\n      ],\r\n      dataSection0: [\r\n        {\r\n          item: this.$t(\"LocaleString.F00004\"),\r\n          section: 23,\r\n          value: \"\"\r\n        }\r\n      ],\r\n      dataSection01: [\r\n        {\r\n          item: this.$t(\"LocaleString.L30118\"),\r\n          section: 0,\r\n          value: \"\"\r\n        },\r\n        {\r\n          item: this.$t(\"LocaleString.L30134\"),\r\n          section: 17,\r\n          value: \"\"\r\n        },\r\n        {\r\n          item: this.$t(\"LocaleString.L00446\"),\r\n          section: 31,\r\n          value: \"\"\r\n        }\r\n      ],\r\n      data: [\r\n        {\r\n          item: this.$t(\"LocaleString.L30138\"),\r\n          section: 18,\r\n          value: \"\"\r\n        },\r\n        {\r\n          item: this.$t(\"LocaleString.L30188\"),\r\n          section: 30,\r\n          value: \"\"\r\n        },\r\n        {\r\n          item: this.$t(\"LocaleString.L30057\"),\r\n          section: 1,\r\n          value: \"\"\r\n        },\r\n        {\r\n          item: this.$t(\"LocaleString.L30055\"),\r\n          section: 2,\r\n          value: \"\"\r\n        },\r\n        {\r\n          item: this.$t(\"LocaleString.L30056\"),\r\n          section: 3,\r\n          value: \"\"\r\n        },\r\n        {\r\n          item: this.$t(\"LocaleString.L30058\"),\r\n          section: 4,\r\n          value: \"\"\r\n        },\r\n        {\r\n          item: this.$t(\"LocaleString.L30060\"),\r\n          section: 5,\r\n          value: \"\"\r\n        },\r\n        {\r\n          item: this.$t(\"LocaleString.L30061\"),\r\n          section: 6,\r\n          value: \"\"\r\n        },\r\n        {\r\n          item: this.$t(\"LocaleString.L30062\"),\r\n          section: 7,\r\n          value: 1\r\n        },\r\n        {\r\n          item: this.$t(\"LocaleString.L30085\"),\r\n          section: 8,\r\n          value: \"\"\r\n        },\r\n        {\r\n          item: this.$t(\"LocaleString.L30101\"),\r\n          section: 11,\r\n          value: \"\"\r\n        },\r\n        {\r\n          item: this.$t(\"LocaleString.L30103\"),\r\n          section: 12,\r\n          value: \"\",\r\n          valueDevices: []\r\n        },\r\n        {\r\n          item: this.$t(\"LocaleString.L30156\"),\r\n          section: 22,\r\n          value: \"\",\r\n          valueDevices: []\r\n        },\r\n        {\r\n          item: this.$t(\"LocaleString.L30104\"),\r\n          section: 13,\r\n          value: \"\",\r\n          valueDevices: []\r\n        },\r\n        {\r\n          item: this.$t(\"LocaleString.L30115\"),\r\n          section: 15,\r\n          value: \"\",\r\n          valueDevices: []\r\n        },\r\n        {\r\n          item: this.$t(\"LocaleString.L30116\"),\r\n          section: 14,\r\n          value: \"\",\r\n          valueSkipEvent: []\r\n        },\r\n        {\r\n          item: this.$t(\"LocaleString.L30129\"),\r\n          section: 16,\r\n          value: \"\",\r\n          valueDeviceSelectList: []\r\n        },\r\n        {\r\n          item: this.$t(\"LocaleString.L30136\"),\r\n          section: 19,\r\n          value: \"\",\r\n          valueM1LongPressList: []\r\n        },\r\n        {\r\n          item: this.$t(\"LocaleString.L30159\"),\r\n          section: 24,\r\n          value: \"\"\r\n        },\r\n        {\r\n          item: this.$t(\"LocaleString.L30153\"),\r\n          section: 20,\r\n          value: \"\"\r\n        },\r\n        {\r\n          item: this.$t(\"LocaleString.L30154\"),\r\n          section: 21,\r\n          value: \"\"\r\n        },\r\n        {\r\n          item: this.$t(\"LocaleString.L30182\"),\r\n          section: 25,\r\n          value: \"\"\r\n        },\r\n\r\n        {\r\n          item: this.$t(\"LocaleString.L30183\"),\r\n          section: 26,\r\n          value: \"\"\r\n        },\r\n        {\r\n          item: this.$t(\"LocaleString.L30184\"),\r\n          section: 27,\r\n          value: \"\"\r\n        },\r\n\r\n        {\r\n          item: this.$t(\"LocaleString.L30185\"),\r\n          section: 28,\r\n          value: \"\"\r\n        },\r\n\r\n        {\r\n          item: this.$t(\"LocaleString.L30186\"),\r\n          section: 29,\r\n          value: \"\"\r\n        },\r\n        {\r\n          item: this.$t(\"LocaleString.L30214\"),\r\n          section: 32,\r\n          value: \"\"\r\n        },\r\n        {\r\n          item: this.$t(\"LocaleString.L30082\"),\r\n          section: 9,\r\n          value: \"\"\r\n        },\r\n        {\r\n          item: this.$t(\"LocaleString.L30083\"),\r\n          section: 10,\r\n          value: \"\"\r\n        }\r\n      ],\r\n      iconSizeList: [\r\n        {\r\n          label: \"32*32\",\r\n          value: \"32\"\r\n        },\r\n        {\r\n          label: \"40*40\",\r\n          value: \"40\"\r\n        }\r\n      ],\r\n      m5ObjectTypeList: [\r\n        {\r\n          label: this.$t(\"LocaleString.D00002\"),\r\n          value: \"all\"\r\n        },\r\n        {\r\n          label: this.$t(\"LocaleString.D00007\"),\r\n          value: \"people\"\r\n        },\r\n        {\r\n          label: this.$t(\"LocaleString.D00008\"),\r\n          value: \"equipment\"\r\n        },\r\n        {\r\n          label: this.$t(\"LocaleString.D00009\"),\r\n          value: \"space\"\r\n        },\r\n        {\r\n          label: this.$t(\"LocaleString.D00010\"),\r\n          value: \"other\"\r\n        }\r\n      ],\r\n      pageSizeList: [\r\n        {\r\n          label: \"10/\" + this.$t(\"LocaleString.L00020\"),\r\n          value: \"10\"\r\n        },\r\n        {\r\n          label: \"25/\" + this.$t(\"LocaleString.L00020\"),\r\n          value: \"25\"\r\n        },\r\n        {\r\n          label: \"50/\" + this.$t(\"LocaleString.L00020\"),\r\n          value: \"50\"\r\n        },\r\n        {\r\n          label: \"100/\" + this.$t(\"LocaleString.L00020\"),\r\n          value: \"100\"\r\n        }\r\n      ],\r\n      showingTab: [\r\n        {\r\n          label: this.$t(\"LocaleString.D30031\"),\r\n          value: \"m1\"\r\n        },\r\n        {\r\n          label: this.$t(\"LocaleString.D30032\"),\r\n          value: \"m2\"\r\n        },\r\n        {\r\n          label: this.$t(\"LocaleString.D30033\"),\r\n          value: \"m3\"\r\n        },\r\n        {\r\n          label: this.$t(\"LocaleString.D30034\"),\r\n          value: \"m4\"\r\n        },\r\n        {\r\n          label: this.$t(\"LocaleString.D30035\"),\r\n          value: \"m5\"\r\n        },\r\n        {\r\n          label: this.$t(\"LocaleString.D30036\"),\r\n          value: \"m6\"\r\n        },\r\n        {\r\n          label: this.$t(\"LocaleString.F30016\"),\r\n          value: \"m8\"\r\n        },\r\n        {\r\n          label: this.$t(\"LocaleString.D30044\"),\r\n          value: \"m9\"\r\n        }\r\n        // {\r\n        //   label: \"軌跡監控\",\r\n        //   value: \"m7\",\r\n        // },\r\n      ],\r\n      showingType: [\r\n        {\r\n          label: this.$t(\"LocaleString.L30059\"),\r\n          value: \"temperature\"\r\n        },\r\n        {\r\n          label: this.$t(\"LocaleString.L30010\"),\r\n          value: \"heartRate\"\r\n        },\r\n        {\r\n          label: this.$t(\"LocaleString.L30013\"),\r\n          value: \"bloodOxygen\"\r\n        },\r\n        {\r\n          label: this.$t(\"LocaleString.L30037\"),\r\n          value: \"bloodPressure\"\r\n        },\r\n        {\r\n          label: this.$t(\"LocaleString.L30208\"),\r\n          value: \"step\"\r\n        },\r\n        {\r\n          label: this.$t(\"LocaleString.L30105\"),\r\n          value: \"breathe\"\r\n        }\r\n      ],\r\n      defaultTabList: [],\r\n      defaultUpdateSeconds: [\r\n        {\r\n          label: \"0\",\r\n          value: \"0\"\r\n        },\r\n        {\r\n          label: \"30\",\r\n          value: \"30\"\r\n        },\r\n        {\r\n          label: \"60\",\r\n          value: \"60\"\r\n        },\r\n        {\r\n          label: \"120\",\r\n          value: \"120\"\r\n        }\r\n      ],\r\n      defaultInfluxRange: [\r\n        {\r\n          label: \"8\",\r\n          value: 8\r\n        },\r\n        {\r\n          label: \"12\",\r\n          value: 12\r\n        },\r\n        {\r\n          label: \"24\",\r\n          value: 24\r\n        },\r\n        {\r\n          label: \"48\",\r\n          value: 48\r\n        },\r\n        {\r\n          label: \"72\",\r\n          value: 72\r\n        }\r\n      ]\r\n    };\r\n  },\r\n  created() {},\r\n  async mounted() {\r\n    console.log(\"load systemConfig\");\r\n    await this.getEventLocaleStringList();\r\n    await this.getServiceCodeMenu();\r\n    this.getDeviceTypeMenu();\r\n    this.loadKMData();\r\n    this.loadCameraData();\r\n    this.loadStatisticLineData();\r\n    this.isShow = true;\r\n    let identity = localStorage.getItem(\"sns_identity\");\r\n    this.isAdmin = identity == \"true\" ? true : false;\r\n\r\n    let permission = localStorage.getItem(\"sns_permission\");\r\n    if (permission.includes(\"[sns]-[1]-[view]\") || identity === \"true\") {\r\n      this.data = [...this.dataSection01, ...this.data];\r\n    }\r\n    if (identity === \"true\") {\r\n      this.data = [...this.dataSection0, ...this.data];\r\n    }\r\n    this.cameraConfigList = this.defaultCameraConfigList;\r\n    this.statisticLineConfigList = this.defaultStatisticLineConfigList;\r\n    window.addEventListener(\"resize\", this.resizeHeight);\r\n    //this.getSystemConfig();\r\n    // this.normalizeHeight();\r\n  },\r\n  beforeDestroy() {\r\n    window.removeEventListener(\"resize\", this.resizeHeight);\r\n  },\r\n  methods: {\r\n    async testConnection() {\r\n      let errorMsg = [\r\n        this.$t(\"LocaleString.L30196\"),\r\n        \"UserName\",\r\n        \"CredentialSecret\"\r\n      ];\r\n      if (\r\n        this.thirdPartyData.url == \"\" ||\r\n        this.thirdPartyData.userName == \"\" ||\r\n        this.thirdPartyData.secret == \"\"\r\n      ) {\r\n        this.$Notice.error({\r\n          title: this.$t(\"LocaleString.E00037\"),\r\n          desc: this.$t(\"LocaleString.W00038\", { 0: errorMsg.join(\",\") }),\r\n          duration: Config.WARNING_DURATION\r\n        });\r\n        return;\r\n      }\r\n      this.$serviceThirdParty.testConnection.requestCommon(\r\n        this.thirdPartyData.url\r\n      );\r\n      let result = await this.$serviceThirdParty.testConnection.send(\r\n        null,\r\n        null,\r\n        {\r\n          userName: this.thirdPartyData.userName,\r\n          secret: this.thirdPartyData.secret\r\n        }\r\n      );\r\n      if (result) {\r\n        this.$Notice.success({\r\n          title: this.$t(\"LocaleString.M00004\"),\r\n          desc:\r\n            this.$t(\"LocaleString.D00063\") +\r\n            \" \" +\r\n            this.$t(\"LocaleString.D00167\"),\r\n          duration: Config.SUCCESS_DURATION\r\n        });\r\n      }\r\n    },\r\n    getEditPermission() {\r\n      let isAllow = false;\r\n\r\n      let permission = localStorage.getItem(\"sns_permission\");\r\n      if (permission.includes(\"[sns]-[1]-[edit]\") || this.isAdmin) {\r\n        isAllow = true;\r\n      }\r\n      return isAllow;\r\n    },\r\n\r\n    switchloginThirdParty(entry) {\r\n      if (!entry) {\r\n        this.thirdPartyData.url = \"\";\r\n        this.thirdPartyData.userName = \"\";\r\n        this.thirdPartyData.secret = \"\";\r\n      }\r\n    },\r\n    resizeHeight() {\r\n      this.modalInnerHeight =\r\n        this.currentTab == \"system\" ? window.innerHeight - 300 : 250;\r\n    },\r\n    async getServiceCodeMenu() {\r\n      let configParams = {\r\n        // hasLicense: true,\r\n        category: \"event\"\r\n      };\r\n      let tmperviceCodeList = await this.$service.getServices.send(\r\n        configParams\r\n      );\r\n      this.serviceCodeList = tmperviceCodeList.results;\r\n      this.serviceCodeList.forEach(item => {\r\n        let transName = this.serviceCodeLocaleStringsList.some(\r\n          data => data.key == item.code\r\n        )\r\n          ? this.serviceCodeLocaleStringsList.find(\r\n              data => data.key == item.code\r\n            ).value\r\n          : item.code;\r\n\r\n        item.key = item.code;\r\n        item.value = transName;\r\n      });\r\n    },\r\n    copyToken() {\r\n      navigator.clipboard.writeText(this.token);\r\n      this.$Notice.success({\r\n        title: this.$t(\"LocaleString.M00004\"),\r\n        desc: this.$t(\"LocaleString.M30006\"),\r\n        duration: Config.SUCCESS_DURATION\r\n      });\r\n    },\r\n    async generateToken() {\r\n      let postData = {\r\n        account: this.loginAccount,\r\n        password: this.loginPassword\r\n      };\r\n\r\n      let valid = await this.$service.getAuthTokenNoload.send(postData);\r\n      if (valid) {\r\n        let res = await this.$service.generateToken.send(postData);\r\n        this.token = location.origin + \"/sns/#/user/login?direct=\" + res;\r\n\r\n        this.$Notice.success({\r\n          title: this.$t(\"LocaleString.M00004\"),\r\n          desc: this.$t(\"LocaleString.M30007\"),\r\n          duration: Config.SUCCESS_DURATION\r\n        });\r\n      }\r\n    },\r\n    async testCameraConnection(row) {\r\n      let errorMessage = \"\";\r\n      if (row.backupDirectory == \"\") {\r\n        errorMessage += this.$t(\"LocaleString.W00038\", {\r\n          0: this.$t(\"LocaleString.L30143\")\r\n        });\r\n      }\r\n      if (row.account == \"\") {\r\n        errorMessage += this.$t(\"LocaleString.W00038\", {\r\n          0: this.$t(\"LocaleString.L30144\")\r\n        });\r\n      }\r\n      if (row.password == \"\") {\r\n        errorMessage += this.$t(\"LocaleString.W00038\", {\r\n          0: this.$t(\"LocaleString.L00003\")\r\n        });\r\n      }\r\n      if (errorMessage != \"\") {\r\n        this.$Notice.warning({\r\n          title: this.$t(\"LocaleString.W00005\"),\r\n          desc: errorMessage,\r\n          duration: Config.WARNING_DURATION\r\n        });\r\n        return false;\r\n      }\r\n\r\n      let params = {\r\n        filepath: row.backupDirectory,\r\n        username: row.account,\r\n        userAuth: Base64.encode(row.password)\r\n      };\r\n\r\n      let connetResult = await this.$service.testCameraConnection.send(params);\r\n      if (connetResult == \"\") {\r\n        this.$Notice.success({\r\n          title: this.$t(\"LocaleString.M00004\"), //成功\r\n          desc:\r\n            this.$t(\"LocaleString.B30010\") +\r\n            \" \" +\r\n            this.$t(\"LocaleString.M00004\"),\r\n          duration: Config.WARNING_DURATION\r\n        });\r\n      }\r\n    },\r\n    async onCameraDataChange(type, row, index) {\r\n      switch (type) {\r\n        case \"capture\":\r\n          this.cameraConfigList[index].capture = row.capture;\r\n          break;\r\n        case \"prefixMinute\":\r\n          this.cameraConfigList[index].prefixMinute = row.prefixMinute;\r\n          break;\r\n        case \"suffixMinute\":\r\n          this.cameraConfigList[index].suffixMinute = row.suffixMinute;\r\n          break;\r\n        case \"backupDirectory\":\r\n          this.cameraConfigList[index].backupDirectory = row.backupDirectory;\r\n          break;\r\n        case \"account\":\r\n          this.cameraConfigList[index].account = row.account;\r\n          break;\r\n        case \"password\":\r\n          this.cameraConfigList[index].password = row.password;\r\n          break;\r\n        case \"keepDays\":\r\n          this.cameraConfigList[index].keepDays = row.keepDays;\r\n          break;\r\n        case \"positionCapture\":\r\n          this.cameraConfigList[index].positionCapture = row.positionCapture;\r\n          break;\r\n      }\r\n      // console.log(this.cameraConfigList);\r\n    },\r\n\r\n    async onStatisticLineDataChange(type, row, index) {\r\n      switch (type) {\r\n        case \"interval\":\r\n          this.statisticLineConfigList[index].interval = row.interval;\r\n          break;\r\n        case \"dataType\":\r\n          this.statisticLineConfigList[index].dataType = row.dataType;\r\n          break;\r\n        case \"warningLine\":\r\n          this.statisticLineConfigList[index].warningLine = row.warningLine;\r\n          break;\r\n      }\r\n      // console.log(this.cameraConfigList);\r\n    },\r\n    /**\r\n      when change device default select List action\r\n      1. default is all\r\n      2. when disable all, auto insert device type which exist from console\r\n      3. when diable a device type which is included in DefaultDeviceTypeList(from API) , it will show alert dialog and reinsert to selection\r\n     **/\r\n    openDeviceSelection(data) {\r\n      this.openDeviceSelect = data;\r\n    },\r\n    changeDeviceDefaultSelectList(item) {\r\n      if (this.openDeviceSelect) {\r\n        if (item.filter(data => data == \"all\").length > 0) {\r\n          this.configData.deviceSelectList = [\"all\"];\r\n        } else {\r\n          this.$service.getDefaultDeviceTypeList\r\n            .send(null)\r\n            .then(defaultList => {\r\n              let defaultSelectList = defaultList.deviceList.split(\",\");\r\n              if (item.length == 0) {\r\n                this.configData.deviceSelectList = defaultSelectList;\r\n              } else {\r\n                defaultSelectList.forEach(item => {\r\n                  if (!this.configData.deviceSelectList.includes(item)) {\r\n                    let deviceName = \"\";\r\n                    if (\r\n                      this.deviceDefaultSelection.some(data =>\r\n                        item.includes(data.key)\r\n                      )\r\n                    ) {\r\n                      deviceName = this.deviceDefaultSelection.find(data =>\r\n                        item.includes(data.key)\r\n                      ).value;\r\n                      this.$Notice.error({\r\n                        title: this.$t(\"LocaleString.E00037\"),\r\n                        desc: this.$t(\"LocaleString.W30011\", { 0: deviceName }),\r\n                        duration: Config.errorDuration\r\n                      });\r\n                      this.configData.deviceSelectList.push(item);\r\n                    }\r\n                  }\r\n                });\r\n              }\r\n            });\r\n        }\r\n      }\r\n    },\r\n    async getEventLocaleStringList() {\r\n      let params = {\r\n        search: \"\"\r\n      };\r\n      let tmp = [];\r\n      let res = await this.$service.getServiceCodeLocaleStrings.send(params);\r\n      res.results.forEach(r => {\r\n        tmp.push({\r\n          key: r.code,\r\n          value: this.$t(\"LocaleString.\" + r.langs.nameId)\r\n        });\r\n      });\r\n      this.serviceCodeLocaleStringsList = JSON.parse(JSON.stringify(tmp));\r\n    },\r\n    changeMultiPlanes(val) {\r\n      this.configData.selectedPlanes = val;\r\n    },\r\n    switchMultiPlanes(val) {\r\n      if (!val) {\r\n        this.configData.selectedPlanes = [];\r\n      }\r\n    },\r\n    changeTab(e) {\r\n      this.currentTab = e;\r\n      this.resizeHeight();\r\n    },\r\n    transEventName(serviceCode) {\r\n      // console.log(\r\n      //   \"transEventName: \" + JSON.stringify(this.serviceCodeLocaleStringsList)\r\n      // );\r\n      let rtnString = \"\";\r\n      if (\r\n        this.serviceCodeLocaleStringsList &&\r\n        this.serviceCodeLocaleStringsList.length > 0\r\n      ) {\r\n        rtnString = this.serviceCodeLocaleStringsList.find(\r\n          item => item.key === serviceCode\r\n        ).value;\r\n      }\r\n      return rtnString;\r\n    },\r\n    getDeviceListLossSignalLowBatteryAbnormalDevice() {\r\n      let tempDeviceListLossSignal = [];\r\n      let tempDeviceListLossSignal2 = [];\r\n      let tempDeviceListLowBattery = [];\r\n      let tempDeviceListAbnormalDevice = [];\r\n      this.allDeviceTypeMenu.forEach(item => {\r\n        if (item.supportDataEvent) {\r\n          if (\r\n            item.supportDataEvent.some(i => i.serviceCode === \"LossSignal\") &&\r\n            item.isBleDevice\r\n          ) {\r\n            tempDeviceListLossSignal.push({\r\n              key: item.type,\r\n              value: this.$t(\"LocaleString.\" + item.langs.nameId)\r\n            });\r\n          }\r\n          if (\r\n            item.supportDataEvent.some(i => i.serviceCode === \"LossSignal\") &&\r\n            !item.isBleDevice\r\n          ) {\r\n            tempDeviceListLossSignal2.push({\r\n              key: item.type,\r\n              value: this.$t(\"LocaleString.\" + item.langs.nameId)\r\n            });\r\n          }\r\n          if (item.supportDataEvent.some(i => i.serviceCode === \"LowBattery\")) {\r\n            tempDeviceListLowBattery.push({\r\n              key: item.type,\r\n              value: this.$t(\"LocaleString.\" + item.langs.nameId)\r\n            });\r\n          }\r\n          if (\r\n            item.supportDataEvent.some(i => i.serviceCode === \"AbnormalDevice\")\r\n          ) {\r\n            tempDeviceListAbnormalDevice.push({\r\n              key: item.type,\r\n              value: this.$t(\"LocaleString.\" + item.langs.nameId)\r\n            });\r\n          }\r\n        }\r\n      });\r\n      this.deviceListLossSignal = Array.from(new Set(tempDeviceListLossSignal));\r\n      this.deviceListLossSignal2 = Array.from(\r\n        new Set(tempDeviceListLossSignal2)\r\n      );\r\n      this.deviceListLowBattery = Array.from(new Set(tempDeviceListLowBattery));\r\n      this.deviceListAbnormalDevice = Array.from(\r\n        new Set(tempDeviceListAbnormalDevice)\r\n      );\r\n    },\r\n    getEventServiceCodeList() {\r\n      let tmpEventServiceCodeList = [];\r\n      this.allDeviceTypeMenu.forEach(item => {\r\n        if (item.supportDataEvent) {\r\n          let scList = item.supportDataEvent.map(e => e.serviceCode);\r\n          tmpEventServiceCodeList = [...tmpEventServiceCodeList, ...scList];\r\n        }\r\n      });\r\n\r\n      tmpEventServiceCodeList = Array.from(new Set(tmpEventServiceCodeList));\r\n      tmpEventServiceCodeList.forEach(item => {\r\n        this.eventServiceCodeList.push({\r\n          key: item,\r\n          value: this.transEventName(item)\r\n        });\r\n      });\r\n    },\r\n    async getDeviceTypeMenu() {\r\n      let deviceTypesParams = new URLSearchParams();\r\n      deviceTypesParams.append(\"active\", true);\r\n      this.allDeviceTypeMenu = await this.$service.getDeviceTypesmenu.send(\r\n        deviceTypesParams\r\n      );\r\n      this.getDeviceSelectList();\r\n      this.getEventServiceCodeList();\r\n      this.getDeviceListLossSignalLowBatteryAbnormalDevice();\r\n      this.loadLogo();\r\n      this.loadSound();\r\n      this.loadConfig();\r\n      this.loadThirdParty();\r\n    },\r\n    getDeviceSelectList() {\r\n      this.deviceDefaultSelection = this.allDeviceTypeMenu.map(item => ({\r\n        key: item.type,\r\n        value: this.$t(\"LocaleString.\" + item.langs.nameId)\r\n      }));\r\n      this.deviceDefaultSelection = [\r\n        ...[{ key: \"all\", value: this.$t(\"LocaleString.D00002\") }],\r\n        ...this.deviceDefaultSelection\r\n      ];\r\n    },\r\n    ended() {\r\n      let audio = this.$refs.audio;\r\n      this.isPlay = false;\r\n      audio.pause();\r\n      audio.currentTime = 0;\r\n      document.querySelector(\".toggle-sound\").classList.add(\"paused\");\r\n    },\r\n    play(control) {\r\n      let audio = this.$refs.audio;\r\n      if (control != null || control != undefined) {\r\n        this.isPlay = false;\r\n        audio.pause();\r\n        audio.currentTime = 0;\r\n        document.querySelector(\".toggle-sound\").classList.add(\"paused\");\r\n        return;\r\n      }\r\n\r\n      if (\r\n        audio.paused &&\r\n        document.querySelector(\".toggle-sound\").classList.contains(\"paused\")\r\n      ) {\r\n        this.isPlay = true;\r\n        document.querySelector(\".toggle-sound\").classList.remove(\"paused\");\r\n        audio.play();\r\n      } else {\r\n        this.isPlay = false;\r\n        audio.pause();\r\n        audio.currentTime = 0;\r\n        document.querySelector(\".toggle-sound\").classList.add(\"paused\");\r\n      }\r\n    },\r\n    restImage() {\r\n      const preview = document.querySelector(\"#preview\");\r\n      preview.src = \"\";\r\n      this.showCleanIcon = false;\r\n      this.imgFileURL = null;\r\n    },\r\n\r\n    handleUploadImage(imgFile) {\r\n      let imgFileurl = null;\r\n      if (\r\n        imgFile.type.toLowerCase().includes(\"png\") ||\r\n        imgFile.type.toLowerCase().includes(\"jpg\") ||\r\n        imgFile.type.toLowerCase().includes(\"jpeg\")\r\n      ) {\r\n        const reader = new FileReader();\r\n        const preview = document.querySelector(\"#preview\");\r\n\r\n        reader.addEventListener(\r\n          \"load\",\r\n          function() {\r\n            preview.src = reader.result;\r\n          },\r\n          false\r\n        );\r\n\r\n        if (imgFile) {\r\n          reader.readAsDataURL(imgFile);\r\n        }\r\n        //\r\n        // if (window.createObjectURL != undefined) {\r\n        //   imgFileurl = window.createObjectURL(imgFile);\r\n        // } else if (window.URL != undefined) {\r\n        //   imgFileurl = window.URL.createObjectURL(imgFile);\r\n        // } else if (window.webkitURL != undefined) {\r\n        //   imgFileurl = window.webitURL.createObjectURL(imgFile);\r\n        // }\r\n        this.imgFile = imgFile;\r\n        this.imgFileURL = imgFileurl;\r\n        this.showCleanIcon = true;\r\n      } else {\r\n        this.$Notice.error({\r\n          title: this.$t(\"LocaleString.E00037\"),\r\n          desc: this.$t(\"LocaleString.E00040\", {\r\n            0: this.$t(\"LocaleString.L30134\")\r\n          }),\r\n          duration: Config.errorDuration\r\n        });\r\n      }\r\n    },\r\n    handleUpload(file) {\r\n      this.isPlay = false;\r\n      let audio = this.$refs.audio;\r\n      audio.pause();\r\n      document.querySelector(\".toggle-sound\").classList.add(\"paused\");\r\n\r\n      let audioElement = new FileReader();\r\n      audioElement.readAsDataURL(file);\r\n      audioElement.addEventListener(\"load\", () => {\r\n        if (!audioElement.result.includes(\"data:audio/\")) {\r\n          this.file = null;\r\n          this.fileURL = this.fileOriURL;\r\n          this.$Notice.error({\r\n            title: this.$t(\"LocaleString.E00037\"),\r\n            desc: this.$t(\"LocaleString.E00040\", {\r\n              0: this.$t(\"LocaleString.L30055\")\r\n            }),\r\n            duration: Config.errorDuration\r\n          });\r\n        } else {\r\n          this.file = file;\r\n          this.fileURL = audioElement.result;\r\n\r\n          setTimeout(() => {\r\n            if (audio.duration > 15) {\r\n              this.file = null;\r\n              this.fileURL = this.fileOriURL;\r\n              this.$Notice.error({\r\n                title: this.$t(\"LocaleString.E00037\"),\r\n                desc: this.$t(\"LocaleString.W30008\", { 0: \"15\" }),\r\n                duration: Config.errorDuration\r\n              });\r\n            } else {\r\n              this.file = file;\r\n              this.fileURL = audioElement.result;\r\n            }\r\n          }, 100);\r\n        }\r\n      });\r\n    },\r\n    changeDefaultTabList(item) {\r\n      // console.log('Sitem:'+JSON.stringify(item))\r\n      let arr = [];\r\n      item.forEach(element => {\r\n        let v = this.showingTab.find(t => t.value == element).label;\r\n        if (v) {\r\n          arr.push({\r\n            label: v,\r\n            value: element\r\n          });\r\n        }\r\n      });\r\n      this.defaultTabList = arr;\r\n    },\r\n    loadLogo() {\r\n      let pocGroupParams = {\r\n        inlinecount: true,\r\n        search: \"active eq true and category eq @SNS@Logo\"\r\n      };\r\n      this.$service.getPOCGroupsByCategory.send(pocGroupParams).then(pocRes => {\r\n        if (\r\n          pocRes.count > 0 &&\r\n          pocRes.results[0].files &&\r\n          pocRes.results[0].files.length > 0\r\n        ) {\r\n          this.imgFileURL = pocRes.results[0].files[0].url;\r\n          this.imgFileOriURL = pocRes.results[0].files[0].url;\r\n          this.showCleanIcon = true;\r\n        }\r\n      });\r\n    },\r\n    loadSound() {\r\n      let pocGroupParams = {\r\n        inlinecount: true,\r\n        search: \"active eq true and category eq @SNS@Sound\"\r\n      };\r\n      this.$service.getPOCGroupsByCategory.send(pocGroupParams).then(pocRes => {\r\n        if (\r\n          pocRes.count > 0 &&\r\n          pocRes.results[0].files &&\r\n          pocRes.results[0].files.length > 0\r\n        ) {\r\n          this.fileURL = pocRes.results[0].files[0].url;\r\n          this.fileOriURL = pocRes.results[0].files[0].url;\r\n        }\r\n      });\r\n    },\r\n    loadConfig() {\r\n      //let loginUserName = localStorage.getItem(\"sns_loginUser\");\r\n\r\n      let params = {\r\n        inlinecount: true,\r\n        search: \"category eq @SNS@Setting\"\r\n      };\r\n\r\n      let planeStr = \"active eq true and enable eq true\";\r\n      let planeParams = {\r\n        search: planeStr,\r\n        inlinecount: true,\r\n        // page: 0,\r\n        // size: 1,\r\n        sort: \"modifiesAt,desc\"\r\n        // active: true,\r\n      };\r\n\r\n      this.$service.getPlanes.send(planeParams).then(planeData => {\r\n        if (planeData.results.length > 0) {\r\n          this.planeList = planeData.results.map(item => ({\r\n            value: item.code,\r\n            label: item.name\r\n          }));\r\n        }\r\n        this.$service.getPOCProperties.send(params).then(res => {\r\n          if (res.count > 0 && res.results[0].properties.length > 0) {\r\n            res.results[0].properties.forEach(res => {\r\n              switch (res.key) {\r\n                case \"license\":\r\n                  this.configData.license =\r\n                    res.value != \"\" ? res.value.split(\",\") : [];\r\n                  break;\r\n                case \"systemName\":\r\n                  this.configData.systemName = res.value;\r\n                  break;\r\n                case \"tabList\":\r\n                  this.configData.tabList = res.value.split(\",\");\r\n                  let arr = [];\r\n                  this.configData.tabList.forEach(element => {\r\n                    let v = this.showingTab.find(t => t.value == element).label;\r\n                    if (v) {\r\n                      arr.push({\r\n                        label: v,\r\n                        value: element\r\n                      });\r\n                    }\r\n                  });\r\n                  this.defaultTabList = arr;\r\n                  break;\r\n                // case \"sound\":\r\n                //   break;\r\n                case \"m2List\":\r\n                  this.configData.m2List = res.value.split(\",\");\r\n                  break;\r\n                case \"iconSize\":\r\n                  this.configData.iconSize = res.value;\r\n                  break;\r\n                case \"defaultTab\":\r\n                  this.configData.defaultTab = res.value;\r\n                  this.oriTab = res.value;\r\n                  break;\r\n                case \"updateSeconds\":\r\n                  this.configData.updateSeconds = res.value;\r\n                  break;\r\n                case \"grayTime\":\r\n                  this.configData.grayTime = res.value * 1;\r\n                  break;\r\n                case \"EventAuthClose\":\r\n                  this.configData.eventAuthClose =\r\n                    res.value == \"true\" ? true : false;\r\n                  break;\r\n                case \"EventKeepDays\":\r\n                  this.configData.eventKeepDays = res.value * 1;\r\n                  break;\r\n                case \"InfluxRange\":\r\n                  this.configData.influxRange = res.value * 1;\r\n                  break;\r\n                case \"lossSignal\":\r\n                  this.configData.lossSignal = res.value * 1;\r\n                  break;\r\n                case \"lossSignal2\":\r\n                  this.configData.lossSignal2 = res.value * 1;\r\n                  break;\r\n                case \"lowBattery\":\r\n                  this.configData.lowBattery = res.value * 1;\r\n                  break;\r\n                case \"lossSignalDevices\":\r\n                  this.configData.lossSignalDevices = res.value.split(\",\");\r\n                  break;\r\n                case \"lossSignalDevices2\":\r\n                  this.configData.lossSignalDevices2 = res.value.split(\",\");\r\n                  break;\r\n                case \"lowBatteryDevices\":\r\n                  this.configData.lowBatteryDevices = res.value.split(\",\");\r\n                  break;\r\n                case \"abnormalDevices\":\r\n                  this.configData.abnormalDevices = res.value.split(\",\");\r\n                  break;\r\n                case \"skipEvent\":\r\n                  this.configData.skipEvent = res.value.split(\",\");\r\n                  break;\r\n                case \"deviceSelectList\":\r\n                  this.configData.deviceSelectList = res.value.split(\",\");\r\n                  break;\r\n                case \"multiPlanes\":\r\n                  this.configData.multiPlanes =\r\n                    res.value == \"true\" ? true : false;\r\n                  break;\r\n                case \"selectedPlanes\":\r\n                  this.configData.selectedPlanes = res.value.split(\",\");\r\n                  break;\r\n                case \"stuckPlane\":\r\n                  this.configData.stuckPlane =\r\n                    res.value == \"true\" ? true : false;\r\n                  break;\r\n                case \"m1LongPress\":\r\n                  this.configData.m1LongPress = res.value.split(\",\");\r\n                  break;\r\n                case \"defaultPageSize\":\r\n                  this.configData.defaultPageSize = res.value;\r\n                  break;\r\n                case \"m5ObjectType\":\r\n                  this.configData.m5ObjectType =\r\n                    res.value == \"\" ? \"all\" : res.value;\r\n                  break;\r\n                case \"showLeaveBed\":\r\n                  this.configData.showLeaveBed =\r\n                    res.value === \"true\" ? true : false;\r\n                  break;\r\n                case \"m2Statistic\":\r\n                  this.configData.m2Statistic = res.value * 1;\r\n                  break;\r\n                case \"m3Statistic\":\r\n                  this.configData.m3Statistic = res.value * 1;\r\n                  break;\r\n                case \"m4Statistic\":\r\n                  this.configData.m4Statistic = res.value * 1;\r\n                  break;\r\n                case \"m8Statistic\":\r\n                  this.configData.m8Statistic = res.value * 1;\r\n                  break;\r\n                case \"m9Statistic\":\r\n                  this.configData.m9Statistic = res.value * 1;\r\n                  break;\r\n                case \"stateColor\":\r\n                  this.configData.stateColor = res.value;\r\n                  break;\r\n                case \"repeatSound\":\r\n                  this.configData.repeatSound = res.value * 1;\r\n                  break;\r\n                case \"logoutTime\":\r\n                  this.configData.logoutTime = res.value * 1;\r\n                  break;\r\n              }\r\n            });\r\n            this.configData.sound =\r\n              localStorage.getItem(\"sns_alertSound\") == \"true\" ? true : false;\r\n            this.configData.m2Line =\r\n              localStorage.getItem(\"sns_m2Line\") == \"true\" ? true : false;\r\n            //\r\n\r\n            this.origConfigData = JSON.parse(JSON.stringify(this.configData));\r\n            this.updateValue(this.origConfigData);\r\n          } else {\r\n            tgus;\r\n            this.editHandleSubmitNew(\"new\");\r\n          }\r\n        });\r\n      });\r\n    },\r\n    loadThirdParty() {\r\n      //let loginUserName = localStorage.getItem(\"sns_loginUser\");\r\n\r\n      let params = {\r\n        inlinecount: true,\r\n        search: \"category eq @SNS@SettingThirdParty\"\r\n      };\r\n\r\n      this.$service.getPOCProperties.send(params).then(res => {\r\n        if (res.count > 0 && res.results[0].properties.length > 0) {\r\n          res.results[0].properties.forEach(res => {\r\n            switch (res.key) {\r\n              case \"loginThirdParty\":\r\n                this.thirdPartyData.loginThirdParty =\r\n                  res.value == \"true\" ? true : false;\r\n                break;\r\n              case \"url\":\r\n                this.thirdPartyData.url = res.value;\r\n                break;\r\n              case \"userName\":\r\n                this.thirdPartyData.userName = res.value;\r\n                break;\r\n              case \"secret\":\r\n                this.thirdPartyData.secret = res.value;\r\n                break;\r\n            }\r\n          });\r\n          this.origThirdPartyData = JSON.parse(\r\n            JSON.stringify(this.thirdPartyData)\r\n          );\r\n          this.updateThirdPartyValue(this.origThirdPartyData);\r\n        }\r\n      });\r\n    },\r\n    checkEditHandleSubmitNew() {\r\n      //\r\n      let errorDesc = \"\";\r\n      let tabList = this.configData.tabList;\r\n      let m2List = this.configData.m2List;\r\n      let defaultTab = this.configData.defaultTab;\r\n      let grayTime = this.configData.grayTime;\r\n      let eventAuthClose = this.configData.eventAuthClose;\r\n      let eventKeepDays = this.configData.eventKeepDays;\r\n      let lowBattery = this.configData.lowBattery;\r\n      let lossSignal = this.configData.lossSignal;\r\n      let lossSignal2 = this.configData.lossSignal2;\r\n      let lowBatteryDevices = this.configData.lowBatteryDevices;\r\n      let lossSignalDevices = this.configData.lossSignalDevices;\r\n      let lossSignalDevices2 = this.configData.lossSignalDevices2;\r\n      let abnormalDevices = this.configData.abnormalDevices;\r\n      let skipEvent = this.configData.skipEvent;\r\n      let deviceSelectList = this.configData.deviceSelectList;\r\n      let multiPlanes = this.configData.multiPlanes;\r\n      let selectedPlanes = this.configData.selectedPlanes;\r\n      let stuckPlane = this.configData.stuckPlane;\r\n      let m1LognPress = this.configData.m1LongPress;\r\n      let showLeaveBed = this.configData.showLeaveBed;\r\n      let m2Statistic = this.configData.m2Statistic;\r\n      let m3Statistic = this.configData.m3Statistic;\r\n      let m4Statistic = this.configData.m4Statistic;\r\n      let m8Statistic = this.configData.m8Statistic;\r\n      let m9Statistic = this.configData.m9Statistic;\r\n      let stateColor = this.configData.stateColor;\r\n      let logoutTime = this.configData.logoutTime;\r\n      // let iconSize = this.configData.iconSize\r\n      if (tabList.length < 1) {\r\n        errorDesc +=\r\n          this.$t(\"LocaleString.M00010\", {\r\n            0: this.$t(\"LocaleString.L30057\")\r\n          }) + \"<br>\";\r\n      }\r\n      if (m2List.length < 1) {\r\n        errorDesc +=\r\n          this.$t(\"LocaleString.M00010\", {\r\n            0: this.$t(\"LocaleString.L30056\")\r\n          }) + \"<br>\";\r\n      }\r\n      if (m2List.length > 4) {\r\n        errorDesc += this.$t(\"LocaleString.W30002\") + \"<br>\";\r\n      }\r\n      if (!defaultTab) {\r\n        errorDesc +=\r\n          this.$t(\"LocaleString.M00010\", {\r\n            0: this.$t(\"LocaleString.L30060\")\r\n          }) + \"<br>\";\r\n      }\r\n      if (grayTime == null) {\r\n        errorDesc +=\r\n          this.$t(\"LocaleString.L00001\", {\r\n            0: this.$t(\"LocaleString.L30062\")\r\n          }) + \"<br>\";\r\n      }\r\n      if (eventAuthClose == true) {\r\n        if (eventKeepDays == null) {\r\n          errorDesc +=\r\n            this.$t(\"LocaleString.L00001\", {\r\n              0: this.$t(\"LocaleString.L30083\")\r\n            }) + \"<br>\";\r\n        }\r\n      }\r\n      if (lowBattery == null) {\r\n        errorDesc +=\r\n          this.$t(\"LocaleString.L00001\", {\r\n            0: this.$t(\"LocaleString.L30104\")\r\n          }) + \"<br>\";\r\n      } else {\r\n        if (lowBattery < 10) {\r\n          errorDesc +=\r\n            this.$t(\"LocaleString.L30104\") +\r\n            this.$t(\"LocaleString.W30009\", {\r\n              0: 10,\r\n              1: 60\r\n            }) +\r\n            \"<br>\";\r\n        }\r\n      }\r\n      if (lossSignal == null) {\r\n        errorDesc +=\r\n          this.$t(\"LocaleString.L00001\", {\r\n            0: this.$t(\"LocaleString.L30103\")\r\n          }) + \"<br>\";\r\n      } else {\r\n        if (lossSignal < 1) {\r\n          errorDesc +=\r\n            this.$t(\"LocaleString.L30103\") +\r\n            this.$t(\"LocaleString.W30009\", {\r\n              0: 1,\r\n              1: 60\r\n            }) +\r\n            \"<br>\";\r\n        }\r\n      }\r\n      if (deviceSelectList.length < 1) {\r\n        errorDesc +=\r\n          this.$t(\"LocaleString.M00010\", {\r\n            0: this.$t(\"LocaleString.L30129\")\r\n          }) + \"<br>\";\r\n      }\r\n      if (\r\n        multiPlanes &&\r\n        (selectedPlanes.length < 1 ||\r\n          (selectedPlanes.length == 1 && selectedPlanes[0] == \"\"))\r\n      ) {\r\n        errorDesc +=\r\n          this.$t(\"LocaleString.M00010\", {\r\n            0: this.$t(\"LocaleString.L30138\")\r\n          }) + \"<br>\";\r\n      }\r\n\r\n      if (m2Statistic == null) {\r\n        errorDesc +=\r\n          this.$t(\"LocaleString.L00001\", {\r\n            0: this.$t(\"LocaleString.L30182\")\r\n          }) + \"<br>\";\r\n      }\r\n      if (m3Statistic == null) {\r\n        errorDesc +=\r\n          this.$t(\"LocaleString.L00001\", {\r\n            0: this.$t(\"LocaleString.L30183\")\r\n          }) + \"<br>\";\r\n      }\r\n      if (m4Statistic == null) {\r\n        errorDesc +=\r\n          this.$t(\"LocaleString.L00001\", {\r\n            0: this.$t(\"LocaleString.L30184\")\r\n          }) + \"<br>\";\r\n      }\r\n      if (m8Statistic == null) {\r\n        errorDesc +=\r\n          this.$t(\"LocaleString.L00001\", {\r\n            0: this.$t(\"LocaleString.L30185\")\r\n          }) + \"<br>\";\r\n      }\r\n      if (m9Statistic == null) {\r\n        errorDesc +=\r\n          this.$t(\"LocaleString.L00001\", {\r\n            0: this.$t(\"LocaleString.L30186\")\r\n          }) + \"<br>\";\r\n      }\r\n      if (stateColor == \"\") {\r\n        errorDesc += this.$t(\"LocaleString.W30019\") + \"<br>\";\r\n      }\r\n      if (logoutTime == null) {\r\n        errorDesc +=\r\n          this.$t(\"LocaleString.L00001\", {\r\n            0: this.$t(\"LocaleString.L00446\")\r\n          }) + \"<br>\";\r\n      }\r\n\r\n      this.cameraConfigList.forEach(item => {\r\n        if (\r\n          item.capture &&\r\n          (item.prefixMinute == \"\" ||\r\n            item.suffixMinute == \"\" ||\r\n            item.backupDirectory == \"\" ||\r\n            item.account == \"\" ||\r\n            item.password == \"\" ||\r\n            item.keepDays == \"\" ||\r\n            item.positionCapture == \"\")\r\n        ) {\r\n          let name = this.cameraTypeList.find(item2 => item.type == item2.type)\r\n            .name;\r\n          errorDesc +=\r\n            this.$t(\"LocaleString.L00001\", {\r\n              0: this.$t(\"LocaleString.L30139\") + \" - \" + name\r\n            }) + \"<br>\";\r\n        }\r\n      });\r\n      // if (iconSize == \"\") {\r\n      //     errorDesc += '請輸入平面圖標大小<br>'\r\n      // }else {\r\n      //   let re = /^[0-9]{1,}\\*[0-9]{1,}$/\r\n      //   if (!re.test(iconSize)) {\r\n      //     errorDesc += '平面圖標大小格式錯誤<br>'\r\n      //   }\r\n      // }\r\n      //\r\n      if (errorDesc) {\r\n        this.$Notice.error({\r\n          title: this.$t(\"LocaleString.E00037\"),\r\n          desc: errorDesc,\r\n          duration: Config.errorDuration\r\n        });\r\n        return false;\r\n      } else {\r\n        return true;\r\n      }\r\n    },\r\n    editHandleSubmitNew(type) {\r\n      let requestId = this.createRequestID();\r\n      if (type === \"update\") {\r\n        this.submitLoading = true;\r\n      }\r\n      //let loginUserName = localStorage.getItem(\"sns_loginUser\");\r\n      let newLowBatteryDevices = [];\r\n      let newLossignalDevices = [];\r\n      let newLossignalDevices2 = [];\r\n      let newAbnormalDevices = [];\r\n      let submitEventArr = [];\r\n      let putPropertyDatas = [];\r\n      let valid = this.checkEditHandleSubmitNew();\r\n      if (valid) {\r\n        if (type === \"new\") {\r\n          this.configData.lossSignalDevices = this.deviceListLossSignal.map(\r\n            item => item.key\r\n          );\r\n          newLossignalDevices = this.configData.lossSignalDevices;\r\n\r\n          this.configData.lossSignalDevices2 = this.deviceListLossSignal2.map(\r\n            item => item.key\r\n          );\r\n          newLossignalDevices2 = this.configData.lossSignalDevices2;\r\n\r\n          this.configData.lowBatteryDevices = this.deviceListLowBattery.map(\r\n            item => item.key\r\n          );\r\n          newLowBatteryDevices = this.configData.lowBatteryDevices;\r\n\r\n          this.configData.abnormalDevices = this.deviceListAbnormalDevice.map(\r\n            item => item.key\r\n          );\r\n          newAbnormalDevices = this.configData.abnormalDevices;\r\n        }\r\n\r\n        let putData = {\r\n          category: \"@SNS@Setting\",\r\n          properties: [\r\n            {\r\n              key: \"license\",\r\n              value: this.configData.license.join(\",\")\r\n            },\r\n            {\r\n              key: \"systemName\",\r\n              value: this.configData.systemName\r\n            },\r\n            {\r\n              key: \"tabList\",\r\n              value: this.configData.tabList.join(\",\")\r\n            },\r\n            // {\r\n            //   key: \"sound\",\r\n            //   value: this.configData.sound == true ? \"true\" : \"false\",\r\n            // },\r\n            {\r\n              key: \"m2List\",\r\n              value: this.configData.m2List.join(\",\")\r\n            },\r\n            {\r\n              key: \"iconSize\",\r\n              value: this.configData.iconSize\r\n            },\r\n            {\r\n              key: \"defaultTab\",\r\n              value: this.configData.defaultTab\r\n            },\r\n            {\r\n              key: \"updateSeconds\",\r\n              value: this.configData.updateSeconds\r\n            },\r\n            {\r\n              key: \"grayTime\",\r\n              value: this.configData.grayTime\r\n            },\r\n            {\r\n              key: \"EventAuthClose\",\r\n              value: this.configData.eventAuthClose\r\n            },\r\n            {\r\n              key: \"EventKeepDays\",\r\n              value: this.configData.eventKeepDays\r\n            },\r\n            {\r\n              key: \"InfluxRange\",\r\n              value: this.configData.influxRange\r\n            },\r\n            {\r\n              key: \"lossSignal\",\r\n              value: this.configData.lossSignal\r\n            },\r\n            {\r\n              key: \"lossSignal2\",\r\n              value: this.configData.lossSignal2\r\n            },\r\n            {\r\n              key: \"lowBattery\",\r\n              value: this.configData.lowBattery\r\n            },\r\n            {\r\n              key: \"lossSignalDevices\",\r\n              value: this.configData.lossSignalDevices.join(\",\")\r\n            },\r\n            {\r\n              key: \"lossSignalDevices2\",\r\n              value: this.configData.lossSignalDevices2.join(\",\")\r\n            },\r\n            {\r\n              key: \"lowBatteryDevices\",\r\n              value: this.configData.lowBatteryDevices.join(\",\")\r\n            },\r\n            {\r\n              key: \"abnormalDevices\",\r\n              value: this.configData.abnormalDevices.join(\",\")\r\n            },\r\n            {\r\n              key: \"skipEvent\",\r\n              value: this.configData.skipEvent.join(\",\")\r\n            },\r\n            {\r\n              key: \"deviceSelectList\",\r\n              value: this.configData.deviceSelectList.join(\",\")\r\n            },\r\n            {\r\n              key: \"selectedPlanes\",\r\n              value: this.configData.selectedPlanes.join(\",\")\r\n            },\r\n            {\r\n              key: \"multiPlanes\",\r\n              value: this.configData.multiPlanes\r\n            },\r\n            {\r\n              key: \"stuckPlane\",\r\n              value: this.configData.stuckPlane\r\n            },\r\n            {\r\n              key: \"m1LongPress\",\r\n              value: this.configData.m1LongPress.join(\",\")\r\n            },\r\n            {\r\n              key: \"defaultPageSize\",\r\n              value: this.configData.defaultPageSize\r\n            },\r\n            {\r\n              key: \"m5ObjectType\",\r\n              value: this.configData.m5ObjectType\r\n            },\r\n            {\r\n              key: \"showLeaveBed\",\r\n              value: this.configData.showLeaveBed\r\n            },\r\n            {\r\n              key: \"m2Statistic\",\r\n              value: this.configData.m2Statistic\r\n            },\r\n            {\r\n              key: \"m3Statistic\",\r\n              value: this.configData.m3Statistic\r\n            },\r\n            {\r\n              key: \"m4Statistic\",\r\n              value: this.configData.m4Statistic\r\n            },\r\n            {\r\n              key: \"m8Statistic\",\r\n              value: this.configData.m8Statistic\r\n            },\r\n            {\r\n              key: \"m9Statistic\",\r\n              value: this.configData.m9Statistic\r\n            },\r\n            {\r\n              key: \"stateColor\",\r\n              value: this.configData.stateColor\r\n            },\r\n            {\r\n              key: \"repeatSound\",\r\n              value: this.configData.repeatSound\r\n            },\r\n            {\r\n              key: \"logoutTime\",\r\n              value: this.configData.logoutTime\r\n            }\r\n          ]\r\n        };\r\n        if (this.configData.systemName == \"\") {\r\n          localStorage.removeItem(\"sns_systemTitle\");\r\n          document.title = this.$t(\"LocaleString.S00002\");\r\n        } else {\r\n          localStorage.setItem(\"sns_systemTitle\", this.configData.systemName);\r\n          document.title = this.configData.systemName;\r\n        }\r\n        localStorage.setItem(\"sns_defaultTab\", this.configData.defaultTab);\r\n        localStorage.setItem(\r\n          \"sns_alertSound\",\r\n          this.configData.sound == true ? \"true\" : \"false\"\r\n        );\r\n        localStorage.setItem(\r\n          \"sns_m2Line\",\r\n          this.configData.m2Line == true ? \"true\" : \"false\"\r\n        );\r\n        localStorage.setItem(\r\n          \"sns_stuckPlane\",\r\n          this.configData.stuckPlane == true ? \"true\" : \"false\"\r\n        );\r\n        localStorage.setItem(\"sns_pageSize\", this.configData.defaultPageSize);\r\n        localStorage.setItem(\"sns_logoutTime\", this.configData.logoutTime);\r\n        putPropertyDatas.push(putData);\r\n\r\n        let editPOCGroups = [\r\n          {\r\n            code: \"@SNS@Sound\",\r\n            name: \"@SNS@Sound\",\r\n            category: \"@SNS@Sound\"\r\n          },\r\n          {\r\n            code: \"@SNS@Logo\",\r\n            name: \"@SNS@Logo\",\r\n            category: \"@SNS@Logo\"\r\n          }\r\n        ];\r\n        let paramsEvent = {\r\n          inlinecount: true,\r\n          search:\r\n            \"active eq true and code in @SNS@LowBattery,@SNS@LossSignal,@SNS@LossSignal2,@SNS@AbnormalDevice\"\r\n        };\r\n\r\n        let objectParams = {\r\n          search: \"active eq true and foreignKeys.key1 eq @SNS@\"\r\n        };\r\n\r\n        /////// construct km data ///////\r\n        let putKMData = {\r\n          category: \"@SNS@SettingKM\",\r\n          properties: []\r\n        };\r\n        this.kmConfigListEdit.forEach(item => {\r\n          let keyValuePairs = [\r\n            {\r\n              key: item.type + \"_longPress_cht\",\r\n              value: item.longPress_cht\r\n            },\r\n            {\r\n              key: item.type + \"_longPress_en\",\r\n              value: item.longPress_en\r\n            },\r\n            {\r\n              key: item.type + \"_shortClick_cht\",\r\n              value: item.shortClick_cht\r\n            },\r\n            {\r\n              key: item.type + \"_shortClick_en\",\r\n              value: item.shortClick_en\r\n            },\r\n            {\r\n              key: item.type + \"_doubleClick_cht\",\r\n              value: item.doubleClick_cht\r\n            },\r\n            {\r\n              key: item.type + \"_doubleClick_en\",\r\n              value: item.doubleClick_en\r\n            }\r\n          ];\r\n          putKMData.properties = [...putKMData.properties, ...keyValuePairs];\r\n        });\r\n        putPropertyDatas.push(putKMData);\r\n        /////// construct km data ///////\r\n\r\n        /////// construct camera data ///////\r\n        let putCameraData = {\r\n          category: \"@SNS@SettingCamera\",\r\n          properties: []\r\n        };\r\n        this.cameraConfigList.forEach(item => {\r\n          let keyValuePairs = [\r\n            {\r\n              key: item.type + \"_capture\",\r\n              value: item.capture\r\n            },\r\n            {\r\n              key: item.type + \"_prefixMinute\",\r\n              value: item.prefixMinute\r\n            },\r\n            {\r\n              key: item.type + \"_suffixMinute\",\r\n              value: item.suffixMinute\r\n            },\r\n            {\r\n              key: item.type + \"_backupDirectory\",\r\n              value: item.backupDirectory\r\n            },\r\n            {\r\n              key: item.type + \"_account\",\r\n              value: item.account\r\n            },\r\n            {\r\n              key: item.type + \"_password\",\r\n              value: item.password\r\n            },\r\n            {\r\n              key: item.type + \"_keepDays\",\r\n              value: item.keepDays\r\n            },\r\n            {\r\n              key: item.type + \"_positionCapture\",\r\n              value: item.positionCapture\r\n            }\r\n          ];\r\n          putCameraData.properties = [\r\n            ...putCameraData.properties,\r\n            ...keyValuePairs\r\n          ];\r\n        });\r\n        putPropertyDatas.push(putCameraData);\r\n        /////// construct camera data ///////\r\n        /////// construct statistic Line data ///////\r\n        let putStatisticLineData = {\r\n          category: \"@SNS@SettingStatisticLine\",\r\n          properties: []\r\n        };\r\n        this.statisticLineConfigList.forEach(item => {\r\n          let keyValuePairs = [\r\n            {\r\n              key: item.entry + \"_interval\",\r\n              value: item.interval\r\n            },\r\n            {\r\n              key: item.entry + \"_dataType\",\r\n              value: item.dataType\r\n            },\r\n            {\r\n              key: item.entry + \"_warningLine\",\r\n              value: item.warningLine\r\n            }\r\n          ];\r\n          putStatisticLineData.properties = [\r\n            ...putStatisticLineData.properties,\r\n            ...keyValuePairs\r\n          ];\r\n        });\r\n        putPropertyDatas.push(putStatisticLineData);\r\n        /////// construct statistic Line data ///////\r\n        /////// construct thirdParty data ///////\r\n\r\n        if (this.thirdPartyData.loginThirdParty) {\r\n          let errorMsg = [\r\n            this.$t(\"LocaleString.L30196\"),\r\n            \"UserName\",\r\n            \"CredentialSecret\"\r\n          ];\r\n          if (\r\n            this.thirdPartyData.url == \"\" ||\r\n            this.thirdPartyData.userName == \"\" ||\r\n            this.thirdPartyData.secret == \"\"\r\n          ) {\r\n            this.$Notice.error({\r\n              title: this.$t(\"LocaleString.E00037\"),\r\n              desc: this.$t(\"LocaleString.W00038\", { 0: errorMsg.join(\", \") }),\r\n              duration: Config.WARNING_DURATION\r\n            });\r\n            this.submitLoading = false;\r\n            return;\r\n          }\r\n        }\r\n        let putThirdPartyData = {\r\n          category: \"@SNS@SettingThirdParty\",\r\n          properties: [\r\n            {\r\n              key: \"loginThirdParty\",\r\n              value: this.thirdPartyData.loginThirdParty\r\n            },\r\n            {\r\n              key: \"url\",\r\n              value: this.thirdPartyData.url\r\n            },\r\n            {\r\n              key: \"userName\",\r\n              value: this.thirdPartyData.userName\r\n            },\r\n            {\r\n              key: \"secret\",\r\n              value: this.thirdPartyData.secret\r\n            }\r\n          ]\r\n        };\r\n        putPropertyDatas.push(putThirdPartyData);\r\n        /////// construct thirdParty data ///////\r\n        let createEventArr = [];\r\n        let objectDevicesLossSignal = [];\r\n        let objectDevicesLossSignal2 = [];\r\n        let objectDevicesLowBattery = [];\r\n        this.$service.getObjectsNoLoad.send(objectParams).then(objRes => {\r\n          this.$service.getEventsNoLoad.send(paramsEvent).then(eventRes => {\r\n            this.$service.editPOCGroups\r\n              .send(editPOCGroups, null, {\r\n                requestID: requestId,\r\n                requestFunction: \"systemParameter\",\r\n                requestAction: \"Update\"\r\n              })\r\n              .then(editPOCRes => {\r\n                this.$service.editPOCProperties\r\n                  .send(putPropertyDatas, null, {\r\n                    requestID: requestId,\r\n                    requestFunction: \"systemParameter\",\r\n                    requestAction: \"Update\"\r\n                  })\r\n                  .then(propertyRes => {\r\n                    console.log(propertyRes);\r\n                    if (type != \"new\") {\r\n                      this.$Notice.success({\r\n                        title: this.$t(\"LocaleString.M00004\"),\r\n                        desc: this.$t(\"LocaleString.M00002\"),\r\n                        duration: Config.SUCCESS_DURATION\r\n                      });\r\n                    }\r\n\r\n                    localStorage.setItem(\r\n                      \"sns_iconSize\",\r\n                      this.configData.iconSize\r\n                    );\r\n                    localStorage.setItem(\r\n                      \"sns_updateSeconds\",\r\n                      this.configData.updateSeconds\r\n                    );\r\n\r\n                    this.isEdit = false;\r\n                    this.origConfigData = JSON.parse(\r\n                      JSON.stringify(this.configData)\r\n                    );\r\n                    this.updateValue(this.origConfigData);\r\n\r\n                    this.oriThirdPartyData = JSON.parse(\r\n                      JSON.stringify(this.thirdPartyData)\r\n                    );\r\n                    this.updateThirdPartyValue(this.oriThirdPartyData);\r\n\r\n                    if (eventRes.results.length > 0) {\r\n                      eventRes.results.forEach(currentEvent => {\r\n                        let submitArgument = currentEvent.arguments;\r\n                        submitArgument.forEach(item => {\r\n                          if (item.key == \"threshold\") {\r\n                            item.value = currentEvent.code.includes(\r\n                              \"LowBattery\"\r\n                            )\r\n                              ? this.configData.lowBattery.toString()\r\n                              : currentEvent.code.includes(\"LossSignal2\")\r\n                              ? this.configData.lossSignal2.toString()\r\n                              : this.configData.lossSignal.toString();\r\n                          }\r\n                        });\r\n                        let updateEvent = {\r\n                          code: currentEvent.code,\r\n                          arguments: submitArgument,\r\n                          // sponsorObject: {\r\n                          //   objectCodes: total_objCodes.length\r\n                          //     ? total_objCodes\r\n                          //     : [],\r\n                          // },\r\n                          sponsorDevice: {\r\n                            devicePids: currentEvent.code.includes(\"LowBattery\")\r\n                              ? this.getEventPids(\"LowBattery\", objRes)\r\n                              : currentEvent.code.includes(\"LossSignal2\")\r\n                              ? this.getEventPids(\"LossSignal2\", objRes)\r\n                              : currentEvent.code.includes(\"AbnormalDevice\")\r\n                              ? this.getEventPids(\"AbnormalDevice\", objRes)\r\n                              : this.getEventPids(\"LossSignal\", objRes),\r\n                            deviceTypes: currentEvent.code.includes(\r\n                              \"LowBattery\"\r\n                            )\r\n                              ? this.configData.lowBatteryDevices\r\n                              : currentEvent.code.includes(\"LossSignal2\")\r\n                              ? this.configData.lossSignalDevices2\r\n                              : currentEvent.code.includes(\"AbnormalDevice\")\r\n                              ? this.configData.abnormalDevices\r\n                              : this.configData.lossSignalDevices\r\n                          }\r\n                        };\r\n                        submitEventArr.push(updateEvent);\r\n                      });\r\n                      if (\r\n                        !eventRes.results.some(\r\n                          item => item.code == \"@SNS@LossSignal\"\r\n                        )\r\n                      ) {\r\n                        // create lossSignal event\r\n                        let lossSignalEvent = this.lossSignalCreateTemplate(\r\n                          newLossignalDevices\r\n                        );\r\n                        createEventArr.push(lossSignalEvent);\r\n                      }\r\n                      if (\r\n                        !eventRes.results.some(\r\n                          item => item.code == \"@SNS@LossSignal2\"\r\n                        )\r\n                      ) {\r\n                        // create lossSignal2 event\r\n                        let lossSignalEvent2 = this.lossSignalCreateTemplate2(\r\n                          newLossignalDevices2\r\n                        );\r\n                        createEventArr.push(lossSignalEvent2);\r\n                      }\r\n                      if (\r\n                        !eventRes.results.some(\r\n                          item => item.code == \"@SNS@LowBattery\"\r\n                        )\r\n                      ) {\r\n                        // create lowBattery event\r\n                        let lowBetteryEvent = this.lowBatteryCreateTemplate(\r\n                          newLowBatteryDevices\r\n                        );\r\n                        createEventArr.push(lowBetteryEvent);\r\n                      }\r\n                      if (\r\n                        !eventRes.results.some(\r\n                          item => item.code == \"@SNS@AbnormalDevice\"\r\n                        )\r\n                      ) {\r\n                        // create abnormalDevice event\r\n                        let abnormalDeviceEvent = this.abnormalDeviceCreateTemplate(\r\n                          newAbnormalDevices\r\n                        );\r\n                        createEventArr.push(abnormalDeviceEvent);\r\n                      }\r\n                      //edit lowBattery event\r\n                      this.$service.postNewEvent\r\n                        .send(createEventArr, null, {\r\n                          requestID: requestId,\r\n                          requestFunction: \"systemParameter\",\r\n                          requestAction: \"Update\"\r\n                        })\r\n                        .then(postRes => {\r\n                          this.$service.editEvent\r\n                            .send(submitEventArr, null, {\r\n                              requestID: requestId,\r\n                              requestFunction: \"systemParameter\",\r\n                              requestAction: \"Update\"\r\n                            })\r\n                            .then(patchRes => {\r\n                              this.fileUpload(type);\r\n                            });\r\n                        });\r\n                    } else {\r\n                      // create lowBattery event\r\n                      let lowBetteryEvent = this.lowBatteryCreateTemplate(\r\n                        newLowBatteryDevices\r\n                      );\r\n                      // create lossSignal event\r\n                      let lossSignalEvent = this.lossSignalCreateTemplate(\r\n                        newLossignalDevices\r\n                      );\r\n                      // create lossSignal event\r\n                      let lossSignalEvent2 = this.lossSignalCreateTemplate2(\r\n                        newLossignalDevices2\r\n                      );\r\n                      // create lossSignal event\r\n                      let abnormalDeviceEvent = this.abnormalDeviceCreateTemplate(\r\n                        newAbnormalDevices\r\n                      );\r\n\r\n                      createEventArr.push(lowBetteryEvent);\r\n                      createEventArr.push(lossSignalEvent);\r\n                      createEventArr.push(lossSignalEvent2);\r\n                      createEventArr.push(abnormalDeviceEvent);\r\n                      this.$service.postNewEvent\r\n                        .send(createEventArr, null, {\r\n                          requestID: requestId,\r\n                          requestFunction: \"systemParameter\",\r\n                          requestAction: \"Update\"\r\n                        })\r\n                        .then(editRes => {\r\n                          this.fileUpload(type);\r\n                        });\r\n                    }\r\n                  });\r\n              });\r\n          });\r\n        });\r\n      } else {\r\n        if (type === \"update\") {\r\n          this.submitLoading = false;\r\n        }\r\n      }\r\n    },\r\n    lossSignalCreateTemplate(deviceTypes) {\r\n      let event = {\r\n        code: \"@SNS@LossSignal\",\r\n        name:\r\n          this.$t(\"LocaleString.D00033\") + \"_\" + this.$t(\"LocaleString.L30158\"),\r\n        serviceCode: \"LossSignal\",\r\n        arguments: [\r\n          {\r\n            key: \"threshold\",\r\n            value: this.configData.lossSignal.toString()\r\n          },\r\n          { key: \"autoTreated\", value: true }\r\n        ],\r\n        sponsorDevice: {\r\n          deviceTypes: deviceTypes\r\n        }\r\n      };\r\n      return event;\r\n    },\r\n    lossSignalCreateTemplate2(deviceTypes) {\r\n      let event = {\r\n        code: \"@SNS@LossSignal2\",\r\n        name:\r\n          this.$t(\"LocaleString.D00033\") + \"_\" + this.$t(\"LocaleString.L30157\"),\r\n        serviceCode: \"LossSignal\",\r\n        arguments: [\r\n          {\r\n            key: \"threshold\",\r\n            value: this.configData.lossSignal2.toString()\r\n          },\r\n          { key: \"autoTreated\", value: true }\r\n        ],\r\n        sponsorDevice: {\r\n          deviceTypes: deviceTypes\r\n        }\r\n      };\r\n      return event;\r\n    },\r\n    lowBatteryCreateTemplate(deviceTypes) {\r\n      let event = {\r\n        code: \"@SNS@LowBattery\",\r\n        name: this.$t(\"LocaleString.D00034\"),\r\n        serviceCode: \"LowBattery\",\r\n        arguments: [\r\n          {\r\n            key: \"threshold\",\r\n            value: this.configData.lowBattery.toString()\r\n          },\r\n          { key: \"autoTreated\", value: true }\r\n        ],\r\n        sponsorDevice: {\r\n          deviceTypes: deviceTypes\r\n        }\r\n      };\r\n      return event;\r\n    },\r\n    abnormalDeviceCreateTemplate(deviceTypes) {\r\n      let event = {\r\n        code: \"@SNS@AbnormalDevice\",\r\n        name: this.$t(\"LocaleString.D00026\"),\r\n        serviceCode: \"AbnormalDevice\",\r\n        arguments: [{ key: \"autoTreated\", value: true }],\r\n        sponsorDevice: {\r\n          deviceTypes: deviceTypes\r\n        }\r\n      };\r\n      return event;\r\n    },\r\n    getEventPids(type, objectResponse) {\r\n      let pids = [];\r\n      if (objectResponse.results.length > 0) {\r\n        objectResponse.results.forEach(obj => {\r\n          if (obj.devices) {\r\n            obj.devices.forEach(d => {\r\n              if (\r\n                type === \"LossSignal\" &&\r\n                this.configData.lossSignalDevices.includes(d.type)\r\n              ) {\r\n                pids.push(d.pid);\r\n              }\r\n              if (\r\n                type === \"LossSignal2\" &&\r\n                this.configData.lossSignalDevices2.includes(d.type)\r\n              ) {\r\n                pids.push(d.pid);\r\n              }\r\n              if (\r\n                type === \"LowBattery\" &&\r\n                this.configData.lowBatteryDevices.includes(d.type)\r\n              ) {\r\n                pids.push(d.pid);\r\n              }\r\n              if (\r\n                type === \"AbnormalDevice\" &&\r\n                this.configData.abnormalDevices.includes(d.type)\r\n              ) {\r\n                pids.push(d.pid);\r\n              }\r\n            });\r\n          }\r\n        });\r\n      }\r\n      return pids;\r\n    },\r\n    async fileUpload(type) {\r\n      if (this.imgFile != null) {\r\n        let imgParams = [\"@SNS@Logo\"];\r\n        let imgData = {\r\n          image: this.imgFile\r\n        };\r\n        let res2 = await this.$service.editPOCGroupsImage.fileUpload(\r\n          imgData,\r\n          imgParams\r\n        );\r\n      } else {\r\n        if (!this.showCleanIcon && !this.imgFileURL) {\r\n          let params = \"@SNS@Logo\";\r\n          let res = await this.$service.deletePOCGroupsImage.send(params);\r\n          localStorage.removeItem(\"sns_logoURL\");\r\n        }\r\n      }\r\n      if (this.file != null) {\r\n        let data = {\r\n          image: this.file\r\n        };\r\n        let params = [\"@SNS@Sound\"];\r\n        let res = await this.$service.editPOCGroupsMusic.fileUpload(\r\n          data,\r\n          params\r\n        );\r\n      }\r\n      this.goDefaultPage(type);\r\n    },\r\n    goDefaultPage(type) {\r\n      if (this.oriTab != this.configData.defaultTab) {\r\n        //重導向\r\n        this.$router.push({\r\n          path: decodeURIComponent(\r\n            \"/administrative/apps/sns/\" + this.configData.defaultTab\r\n          ) // 導頁至預設頁面\r\n        });\r\n      }\r\n      setTimeout(() => {\r\n        this.$store.commit(\"setPOCConfigChanged\", moment().valueOf());\r\n        if (type != \"new\") {\r\n          this.submitLoading = false;\r\n          this.$emit(\"closeSystemConfig\");\r\n        }\r\n      }, 1100);\r\n    },\r\n    updateThirdPartyValue(data) {\r\n      this.thirdPartyConfigList.forEach(d => {\r\n        switch (d.section) {\r\n          case 1:\r\n            d.value = data.loginThirdParty;\r\n            this.viewLoginThirdParty =\r\n              data.loginThirdParty == true\r\n                ? this.$t(\"LocaleString.B20009\")\r\n                : this.$t(\"LocaleString.B20010\");\r\n            break;\r\n          case 2:\r\n            d.value = data.url;\r\n            break;\r\n          case 3:\r\n            d.value = data.userName;\r\n            break;\r\n          case 4:\r\n            d.value = data.secret;\r\n            break;\r\n        }\r\n      });\r\n\r\n      // d.viewStuckPlane =\r\n      //         data.stuckPlane == true\r\n      //           ? this.$t(\"LocaleString.B20009\")\r\n      //           : this.$t(\"LocaleString.B20010\");\r\n    },\r\n    updateValue(data) {\r\n      let viewM2ListArr = [];\r\n      let viewTabArr = [];\r\n      let viewLicenseArr = [];\r\n      data.m2List.forEach(d => {\r\n        let v = this.showingType.find(t => t.value == d).label;\r\n        viewM2ListArr.push(v);\r\n      });\r\n      this.viewM2List = viewM2ListArr.join(\", \");\r\n\r\n      data.tabList.forEach(d => {\r\n        let v = this.showingTab.find(t => t.value == d).label;\r\n        viewTabArr.push(v);\r\n      });\r\n\r\n      data.license.forEach(d => {\r\n        let v = this.serviceCodeList.find(t => t.key == d).value;\r\n        viewLicenseArr.push(v);\r\n      });\r\n\r\n      this.iconSizeTitle = this.iconSizeList.find(\r\n        t => t.value == data.iconSize\r\n      ).label;\r\n      this.defaultPageSizeTitle =\r\n        data.defaultPageSize == \"\"\r\n          ? \"\"\r\n          : this.pageSizeList.find(t => t.value == data.defaultPageSize).label;\r\n      this.m5ObjectTypeTitle =\r\n        data.m5ObjectType == \"\"\r\n          ? \"\"\r\n          : this.m5ObjectTypeList.find(t => t.value == data.m5ObjectType).label;\r\n      this.defaultTabTitle = this.showingTab.find(\r\n        t => t.value == data.defaultTab\r\n      ).label;\r\n\r\n      this.viewTab = viewTabArr.join(\", \");\r\n      this.viewLicense = viewLicenseArr.join(\", \");\r\n      this.viewPlaySound =\r\n        localStorage.getItem(\"sns_alertSound\") == \"true\"\r\n          ? this.$t(\"LocaleString.B20009\")\r\n          : this.$t(\"LocaleString.B20010\");\r\n      this.viewM2Line =\r\n        localStorage.getItem(\"sns_m2Line\") == \"true\"\r\n          ? this.$t(\"LocaleString.B20009\")\r\n          : this.$t(\"LocaleString.B20010\");\r\n      this.data.forEach(d => {\r\n        switch (d.section) {\r\n          case 0:\r\n            d.value = data.systemName;\r\n            break;\r\n          case 1:\r\n            d.value = this.viewTab;\r\n            break;\r\n          case 2:\r\n            d.value = this.viewPlaySound;\r\n            d.valueTime = data.repeatSound;\r\n            break;\r\n          case 3:\r\n            d.value = this.viewM2List;\r\n            break;\r\n          case 4:\r\n            d.value = data.iconSize;\r\n            break;\r\n          case 5:\r\n            d.value = data.defaultTab;\r\n            break;\r\n          case 6:\r\n            d.value = data.updateSeconds;\r\n            break;\r\n          case 7:\r\n            d.value = data.grayTime * 1;\r\n            break;\r\n          case 8:\r\n            d.value = this.viewM2Line;\r\n            break;\r\n          case 9:\r\n            d.value =\r\n              data.eventAuthClose == true\r\n                ? this.$t(\"LocaleString.B20009\")\r\n                : this.$t(\"LocaleString.B20010\");\r\n            d.boolean = data.eventAuthClose == true ? true : false;\r\n            break;\r\n          case 10:\r\n            d.value = data.eventKeepDays;\r\n            break;\r\n          case 11:\r\n            d.value = data.influxRange;\r\n            break;\r\n          case 12:\r\n            d.value = data.lossSignal;\r\n            d.valueDevices = this.transformName(\r\n              data.lossSignalDevices,\r\n              \"lossSignal\"\r\n            );\r\n            break;\r\n          case 22:\r\n            d.value = data.lossSignal2;\r\n            d.valueDevices = this.transformName(\r\n              data.lossSignalDevices2,\r\n              \"lossSignal2\"\r\n            );\r\n            break;\r\n          case 13:\r\n            d.value = data.lowBattery;\r\n            d.valueDevices = this.transformName(\r\n              data.lowBatteryDevices,\r\n              \"lowBattery\"\r\n            );\r\n            break;\r\n          case 14:\r\n            d.value = data.skipEvent;\r\n            d.valueSkipEvent = this.transformName(data.skipEvent, \"skipEvent\");\r\n            break;\r\n          case 15:\r\n            d.value = data.abnormalDevice;\r\n            d.valueDevices = this.transformName(\r\n              data.abnormalDevices,\r\n              \"abnormalDevice\"\r\n            );\r\n            break;\r\n          case 16:\r\n            d.value = data.deviceSelectList;\r\n            d.valueDeviceSelectList = this.transformName(\r\n              data.deviceSelectList,\r\n              \"deviceSelectList\"\r\n            );\r\n            break;\r\n          case 17:\r\n            d.value = this.viewLogo;\r\n            break;\r\n          case 18:\r\n            d.viewMultiPlanes =\r\n              data.multiPlanes == true\r\n                ? this.$t(\"LocaleString.B20009\")\r\n                : this.$t(\"LocaleString.B20010\");\r\n            d.multiPlanes = data.multiPlanes == true ? true : false;\r\n            d.value = data.selectedPlanes;\r\n            d.valuePlaneSelectList = this.transformPlaneName(\r\n              data.selectedPlanes\r\n            );\r\n            break;\r\n          case 19:\r\n            d.value = data.m1LongPress;\r\n            d.valueM1LongPressList = this.transformName(\r\n              data.m1LongPress,\r\n              \"m1LongPress\"\r\n            );\r\n            break;\r\n          case 20:\r\n            d.value = data.defaultPageSize;\r\n            break;\r\n          case 21:\r\n            d.value = data.m5ObjectType;\r\n            break;\r\n          case 23:\r\n            d.value = this.viewLicense;\r\n            break;\r\n          case 24:\r\n            d.value =\r\n              data.showLeaveBed == true\r\n                ? this.$t(\"LocaleString.B20009\")\r\n                : this.$t(\"LocaleString.B20010\");\r\n            break;\r\n          case 25:\r\n            d.value = data.m2Statistic;\r\n            break;\r\n          case 26:\r\n            d.value = data.m3Statistic;\r\n            break;\r\n          case 27:\r\n            d.value = data.m4Statistic;\r\n            break;\r\n          case 28:\r\n            d.value = data.m8Statistic;\r\n            break;\r\n          case 29:\r\n            d.value = data.m9Statistic;\r\n            break;\r\n          case 30:\r\n            d.viewStuckPlane =\r\n              data.stuckPlane == true\r\n                ? this.$t(\"LocaleString.B20009\")\r\n                : this.$t(\"LocaleString.B20010\");\r\n            d.stuckPlane = data.stuckPlane == true ? true : false;\r\n            break;\r\n          case 31:\r\n            d.value = data.logoutTime;\r\n            break;\r\n          case 32:\r\n            d.value = data.stateColor;\r\n            break;\r\n        }\r\n      });\r\n    },\r\n    transformName(deviceArray, type) {\r\n      let tempData = [];\r\n      let deviceType = \"\";\r\n      if (deviceArray && deviceArray.length > 0) {\r\n        if (deviceArray.length == 1 && deviceArray[0] === \"\") {\r\n          return \"\";\r\n        }\r\n        switch (type) {\r\n          case \"lossSignal\":\r\n            try {\r\n              deviceArray.forEach(item => {\r\n                deviceType = item;\r\n                let d = this.deviceListLossSignal.find(data => data.key == item)\r\n                  .value;\r\n                tempData.push(d);\r\n              });\r\n            } catch (e) {\r\n              tempData.push(deviceType);\r\n            }\r\n\r\n            break;\r\n          case \"lossSignal2\":\r\n            try {\r\n              deviceArray.forEach(item => {\r\n                deviceType = item;\r\n                let d = this.deviceListLossSignal2.find(\r\n                  data => data.key == item\r\n                ).value;\r\n                tempData.push(d);\r\n              });\r\n            } catch (e) {\r\n              tempData.push(deviceType);\r\n            }\r\n\r\n            break;\r\n          case \"lowBattery\":\r\n            try {\r\n              deviceArray.forEach(item => {\r\n                deviceType = item;\r\n                let d = this.deviceListLowBattery.find(data => data.key == item)\r\n                  .value;\r\n                tempData.push(d);\r\n              });\r\n            } catch (e) {\r\n              tempData.push(deviceType);\r\n            }\r\n            break;\r\n          case \"abnormalDevice\":\r\n            try {\r\n              deviceArray.forEach(item => {\r\n                deviceType = item;\r\n                let d = this.deviceListAbnormalDevice.find(\r\n                  data => data.key == item\r\n                ).value;\r\n                tempData.push(d);\r\n              });\r\n            } catch (e) {\r\n              tempData.push(deviceType);\r\n            }\r\n            break;\r\n          case \"skipEvent\":\r\n            deviceArray.forEach(item => {\r\n              let d = this.eventServiceCodeList.find(data => data.key == item)\r\n                .value;\r\n              tempData.push(d);\r\n            });\r\n            break;\r\n          case \"deviceSelectList\":\r\n            try {\r\n              deviceArray.forEach(item => {\r\n                deviceType = item;\r\n                let d = this.deviceDefaultSelection.find(\r\n                  data => data.key == item\r\n                ).value;\r\n                tempData.push(d);\r\n              });\r\n            } catch (e) {\r\n              tempData.push(deviceType);\r\n            }\r\n            break;\r\n          case \"m1LongPress\":\r\n            try {\r\n              deviceArray.forEach(item => {\r\n                deviceType = item;\r\n                let d = this.deviceDefaultSelection.find(\r\n                  data => data.key == item\r\n                ).value;\r\n                tempData.push(d);\r\n              });\r\n            } catch (e) {\r\n              tempData.push(deviceType);\r\n            }\r\n            break;\r\n        }\r\n        return tempData.join(\", \");\r\n      }\r\n    },\r\n    transformPlaneName(planeArray) {\r\n      let tempData = [];\r\n      if (planeArray.length == 1 && planeArray[0] === \"\") {\r\n        return \"\";\r\n      }\r\n      if (planeArray && planeArray.length > 0 && this.planeList.length > 0) {\r\n        planeArray.forEach(item => {\r\n          let d = this.planeList.find(data => data.value == item).label;\r\n          tempData.push(d);\r\n        });\r\n        return tempData.join(\", \");\r\n      } else {\r\n        return \"\";\r\n      }\r\n    },\r\n    loadKMData() {\r\n      let params = {\r\n        inlinecount: true,\r\n        search: \"category eq @SNS@SettingKM\"\r\n      };\r\n\r\n      this.$service.getPOCProperties.send(params).then(res => {\r\n        if (res.count > 0) {\r\n          res.results[0].properties.forEach(res => {\r\n            switch (res.key) {\r\n              case \"equipment_longPress_cht\":\r\n                this.kmConfigList[0].longPress_cht = res.value;\r\n                break;\r\n              case \"equipment_longPress_en\":\r\n                this.kmConfigList[0].longPress_en = res.value;\r\n                break;\r\n              case \"equipment_shortClick_cht\":\r\n                this.kmConfigList[0].shortClick_cht = res.value;\r\n                break;\r\n              case \"equipment_shortClick_en\":\r\n                this.kmConfigList[0].shortClick_en = res.value;\r\n                break;\r\n              case \"equipment_doubleClick_cht\":\r\n                this.kmConfigList[0].doubleClick_cht = res.value;\r\n                break;\r\n              case \"equipment_doubleClick_en\":\r\n                this.kmConfigList[0].doubleClick_en = res.value;\r\n                break;\r\n              case \"people_longPress_cht\":\r\n                this.kmConfigList[1].longPress_cht = res.value;\r\n                break;\r\n              case \"people_longPress_en\":\r\n                this.kmConfigList[1].longPress_en = res.value;\r\n                break;\r\n              case \"people_shortClick_cht\":\r\n                this.kmConfigList[1].shortClick_cht = res.value;\r\n                break;\r\n              case \"people_shortClick_en\":\r\n                this.kmConfigList[1].shortClick_en = res.value;\r\n                break;\r\n              case \"people_doubleClick_cht\":\r\n                this.kmConfigList[1].doubleClick_cht = res.value;\r\n                break;\r\n              case \"people_doubleClick_en\":\r\n                this.kmConfigList[1].doubleClick_en = res.value;\r\n                break;\r\n              case \"space_longPress_cht\":\r\n                this.kmConfigList[2].longPress_cht = res.value;\r\n                break;\r\n              case \"space_longPress_en\":\r\n                this.kmConfigList[2].longPress_en = res.value;\r\n                break;\r\n              case \"space_shortClick_cht\":\r\n                this.kmConfigList[2].shortClick_cht = res.value;\r\n                break;\r\n              case \"space_shortClick_en\":\r\n                this.kmConfigList[2].shortClick_en = res.value;\r\n                break;\r\n              case \"space_doubleClick_cht\":\r\n                this.kmConfigList[2].doubleClick_cht = res.value;\r\n                break;\r\n              case \"space_doubleClick_en\":\r\n                this.kmConfigList[2].doubleClick_en = res.value;\r\n                break;\r\n              case \"other_longPress_cht\":\r\n                this.kmConfigList[3].longPress_cht = res.value;\r\n                break;\r\n              case \"other_longPress_en\":\r\n                this.kmConfigList[3].longPress_en = res.value;\r\n                break;\r\n              case \"other_shortClick_cht\":\r\n                this.kmConfigList[3].shortClick_cht = res.value;\r\n                break;\r\n              case \"other_shortClick_en\":\r\n                this.kmConfigList[3].shortClick_en = res.value;\r\n                break;\r\n              case \"other_doubleClick_cht\":\r\n                this.kmConfigList[3].doubleClick_cht = res.value;\r\n                break;\r\n              case \"other_doubleClick_en\":\r\n                this.kmConfigList[3].doubleClick_en = res.value;\r\n                break;\r\n            }\r\n          });\r\n        }\r\n        this.kmConfigListEdit = JSON.parse(JSON.stringify(this.kmConfigList));\r\n      });\r\n    },\r\n    loadCameraData() {\r\n      let params = {\r\n        inlinecount: true,\r\n        search: \"category eq @SNS@SettingCamera\"\r\n      };\r\n\r\n      this.$service.getPOCProperties.send(params).then(res => {\r\n        if (res.count > 0) {\r\n          res.results[0].properties.forEach(res => {\r\n            switch (res.key) {\r\n              //help\r\n              case \"help_capture\":\r\n                this.cameraConfigList[0].capture =\r\n                  res.value == \"true\" ? true : false;\r\n                break;\r\n              case \"help_prefixMinute\":\r\n                this.cameraConfigList[0].prefixMinute = res.value * 1;\r\n                break;\r\n              case \"help_suffixMinute\":\r\n                this.cameraConfigList[0].suffixMinute = res.value * 1;\r\n                break;\r\n              case \"help_backupDirectory\":\r\n                this.cameraConfigList[0].backupDirectory = res.value;\r\n                break;\r\n              case \"help_account\":\r\n                this.cameraConfigList[0].account = res.value;\r\n                break;\r\n              case \"help_password\":\r\n                this.cameraConfigList[0].password = res.value;\r\n                break;\r\n              case \"help_keepDays\":\r\n                this.cameraConfigList[0].keepDays = res.value * 1;\r\n                break;\r\n              case \"help_positionCapture\":\r\n                this.cameraConfigList[0].positionCapture = res.value * 1;\r\n                break;\r\n\r\n              //Enter\r\n              case \"enter_capture\":\r\n                this.cameraConfigList[1].capture =\r\n                  res.value == \"true\" ? true : false;\r\n                break;\r\n              case \"enter_prefixMinute\":\r\n                this.cameraConfigList[1].prefixMinute = res.value * 1;\r\n                break;\r\n              case \"enter_suffixMinute\":\r\n                this.cameraConfigList[1].suffixMinute = res.value * 1;\r\n                break;\r\n              case \"enter_backupDirectory\":\r\n                this.cameraConfigList[1].backupDirectory = res.value;\r\n                break;\r\n              case \"enter_account\":\r\n                this.cameraConfigList[1].account = res.value;\r\n                break;\r\n              case \"enter_password\":\r\n                this.cameraConfigList[1].password = res.value;\r\n                break;\r\n              case \"enter_keepDays\":\r\n                this.cameraConfigList[1].keepDays = res.value * 1;\r\n                break;\r\n              case \"enter_positionCapture\":\r\n                this.cameraConfigList[1].positionCapture = res.value * 1;\r\n                break;\r\n\r\n              //Leave\r\n              case \"leave_capture\":\r\n                this.cameraConfigList[2].capture =\r\n                  res.value == \"true\" ? true : false;\r\n                break;\r\n              case \"leave_prefixMinute\":\r\n                this.cameraConfigList[2].prefixMinute = res.value * 1;\r\n                break;\r\n              case \"leave_suffixMinute\":\r\n                this.cameraConfigList[2].suffixMinute = res.value * 1;\r\n                break;\r\n              case \"leave_backupDirectory\":\r\n                this.cameraConfigList[2].backupDirectory = res.value;\r\n                break;\r\n              case \"leave_account\":\r\n                this.cameraConfigList[2].account = res.value;\r\n                break;\r\n              case \"leave_password\":\r\n                this.cameraConfigList[2].password = res.value;\r\n                break;\r\n              case \"leave_keepDays\":\r\n                this.cameraConfigList[2].keepDays = res.value * 1;\r\n                break;\r\n              case \"leave_positionCapture\":\r\n                this.cameraConfigList[2].positionCapture = res.value * 1;\r\n                break;\r\n\r\n              //fall\r\n              case \"fall_capture\":\r\n                this.cameraConfigList[3].capture =\r\n                  res.value == \"true\" ? true : false;\r\n                break;\r\n              case \"fall_prefixMinute\":\r\n                this.cameraConfigList[3].prefixMinute = res.value * 1;\r\n                break;\r\n              case \"fall_suffixMinute\":\r\n                this.cameraConfigList[3].suffixMinute = res.value * 1;\r\n                break;\r\n              case \"fall_backupDirectory\":\r\n                this.cameraConfigList[3].backupDirectory = res.value;\r\n                break;\r\n              case \"fall_account\":\r\n                this.cameraConfigList[3].account = res.value;\r\n                break;\r\n              case \"fall_password\":\r\n                this.cameraConfigList[3].password = res.value;\r\n                break;\r\n              case \"fall_keepDays\":\r\n                this.cameraConfigList[3].keepDays = res.value * 1;\r\n                break;\r\n              case \"fall_positionCapture\":\r\n                this.cameraConfigList[3].positionCapture = res.value * 1;\r\n                break;\r\n            }\r\n          });\r\n        }\r\n        this.kmConfigListEdit = JSON.parse(JSON.stringify(this.kmConfigList));\r\n      });\r\n    },\r\n    loadStatisticLineData() {\r\n      let params = {\r\n        inlinecount: true,\r\n        search: \"category eq @SNS@SettingStatisticLine\"\r\n      };\r\n      this.$service.getPOCProperties.send(params).then(resData => {\r\n        if (resData.count > 0) {\r\n          resData.results[0].properties.forEach(res => {\r\n            let key = res.key.split(\"_\")[0];\r\n            let data = res.key.split(\"_\")[1];\r\n            // if(req)\r\n            //     this.cameraConfigList[0].capture = res.value == \"true\" ? true : false;\r\n\r\n            this.statisticLineConfigList.forEach(item => {\r\n              if (item.entry == key) {\r\n                if (!isNaN(parseInt(res.value))) {\r\n                  item[data] = res.value * 1;\r\n                } else if (res.value == \"true\" || res == \"false\") {\r\n                  item[data] =\r\n                    res.value == \"true\"\r\n                      ? true\r\n                      : res.value == \"false\"\r\n                      ? false\r\n                      : false;\r\n                } else {\r\n                  item[data] = res.value;\r\n                }\r\n              }\r\n            });\r\n          });\r\n        }\r\n      });\r\n    },\r\n    getCameraType(type) {\r\n      if (type != undefined) {\r\n        let typeData = this.cameraTypeList.find(t => t.type == type).name;\r\n        return typeData;\r\n      } else {\r\n        return \"\";\r\n      }\r\n    },\r\n    getObjectType(type) {\r\n      if (type != undefined) {\r\n        let typeData = this.kmObjectTypeList.find(t => t.type == type).name;\r\n        return typeData;\r\n      } else {\r\n        return \"\";\r\n      }\r\n    },\r\n    onEditDataChange(type, row, index) {\r\n      switch (type) {\r\n        case \"longPress_cht\":\r\n          this.kmConfigListEdit[index].longPress_cht = row.longPress_cht;\r\n          break;\r\n        case \"longPress_en\":\r\n          this.kmConfigListEdit[index].longPress_en = row.longPress_en;\r\n          break;\r\n        case \"shortClick_cht\":\r\n          this.kmConfigListEdit[index].shortClick_cht = row.shortClick_cht;\r\n          break;\r\n        case \"shortClick_en\":\r\n          this.kmConfigListEdit[index].shortClick_en = row.shortClick_en;\r\n          break;\r\n        case \"doubleClick_cht\":\r\n          this.kmConfigListEdit[index].doubleClick_cht = row.doubleClick_cht;\r\n          break;\r\n        case \"doubleClick_en\":\r\n          this.kmConfigListEdit[index].doubleClick_en = row.doubleClick_en;\r\n          break;\r\n      }\r\n    },\r\n    normalizeHeight() {\r\n      setTimeout(\r\n        function() {\r\n          const modal = document.querySelector(\".ivu-modal-body .content\");\r\n          if (modal) {\r\n            modal.style.height = window.innerHeight - 400 + \"px\";\r\n          }\r\n          const modal2 = document.querySelector(\".ivu-modal-footer\");\r\n          if (modal2) {\r\n            modal2.style.height = \"30px\";\r\n          }\r\n        }.bind(this),\r\n        50\r\n      );\r\n    },\r\n    dynamicSort(property) {\r\n      var sortOrder = 1;\r\n      if (property[0] === \"-\") {\r\n        sortOrder = -1;\r\n        property = property.substr(1);\r\n      }\r\n      return function(a, b) {\r\n        var result =\r\n          a[property] < b[property] ? -1 : a[property] > b[property] ? 1 : 0;\r\n        return result * sortOrder;\r\n      };\r\n    },\r\n    edit() {\r\n      this.isEdit = true;\r\n      this.play(\"stop\");\r\n      this.loadLogo();\r\n      this.loadSound();\r\n      //this.getSystemConfig()\r\n    },\r\n    cancelEdit() {\r\n      this.play(\"stop\");\r\n      this.file = null;\r\n      this.fileURL = null;\r\n      this.isEdit = false;\r\n      this.configData = JSON.parse(JSON.stringify(this.origConfigData));\r\n      this.thirdPartyData = JSON.parse(JSON.stringify(this.origThirdPartyData));\r\n      this.cameraConfigList = JSON.parse(\r\n        JSON.stringify(this.defaultCameraConfigList)\r\n      );\r\n      this.updateValue(this.configData);\r\n      this.loadLogo();\r\n      this.loadSound();\r\n      this.loadKMData();\r\n      this.loadCameraData();\r\n      this.loadThirdParty();\r\n      //this.getSystemConfig();\r\n    },\r\n\r\n    // editHandleSubmit() {\r\n    //   console.log(\"editHandleSubmit\");\r\n    //   console.log(this.configList);\r\n    //   console.log(this.configListToSave);\r\n    //   console.log(\"LoginUser: \" + localStorage.getItem(\"sns_loginUser\"));\r\n\r\n    //   let putdata = [];\r\n    //   let properties = [];\r\n    //   let loginUserName = localStorage.getItem(\"sns_loginUser\");\r\n    //   let appEnableAccountKey = \"appsEnable-\" + loginUserName;\r\n    //   let appEditAccountKey = \"appsEdit-\" + loginUserName;\r\n    //   let appEnableForUser = \"\";\r\n    //   let appEditForUser = \"\";\r\n\r\n    //   this.configList.forEach((x) => {\r\n    //     if (x.key == appEnableAccountKey) appEnableForUser = x.value.toString();\r\n    //     else if (x.key == appEditAccountKey)\r\n    //       appEditForUser = x.value.toString();\r\n    //   });\r\n    //   this.configListToSave.forEach((y) => {\r\n    //     switch (y.schema.type) {\r\n    //       case \"string\":\r\n    //         properties.push({\r\n    //           key: y.key,\r\n    //           value: y.value,\r\n    //         });\r\n    //         break;\r\n    //       case \"select\":\r\n    //         if (y.key != appEnableAccountKey)\r\n    //           properties.push({\r\n    //             key: y.key,\r\n    //             value: y.value.toString(),\r\n    //           });\r\n    //         break;\r\n    //       case \"boolean\":\r\n    //         properties.push({\r\n    //           key: y.key,\r\n    //           value: y.value.toString(),\r\n    //         });\r\n    //         break;\r\n    //     }\r\n    //   });\r\n    //   if (loginUserName) {\r\n    //     properties.push({\r\n    //       key: appEnableAccountKey,\r\n    //       value: appEnableForUser,\r\n    //     });\r\n    //     properties.push({\r\n    //       key: appEditAccountKey,\r\n    //       value: appEditForUser,\r\n    //     });\r\n    //   }\r\n\r\n    //   putdata.push({\r\n    //     category: \"pocConfig\",\r\n    //     properties: properties,\r\n    //   });\r\n    //   console.log(\"properties=\");\r\n    //   console.log(putdata);\r\n\r\n    //   this.$service.editPOCProperties.send(putdata).then((res) => {\r\n    //     console.log(res);\r\n    //     this.$Notice.success({\r\n    //       title: this.$t(\"LocaleString.M00004\"),\r\n    //       desc: this.$t(\"LocaleString.M00002\"),\r\n    //       duration: Config.SUCCESS_DURATION,\r\n    //     });\r\n\r\n    //     this.$store.commit(\"setPOCConfigChanged\", moment().valueOf());\r\n\r\n    //     this.isShow = false;\r\n    //     this.$emit(\"closeSystemConfig\");\r\n    //   });\r\n    // },\r\n\r\n    // computedDate(val) {\r\n    //   if (val) {\r\n    //     return moment(val).format(\"YYYY-MM-DD\");\r\n    //   }\r\n    //   return \"\";\r\n    // },\r\n    // computedDateTime(val) {\r\n    //   if (val) {\r\n    //     return moment(val).format(\"YYYY-MM-DD HH:mm:ss\");\r\n    //   }\r\n    //   return \"\";\r\n    // },\r\n    cancel() {\r\n      this.isShow = false;\r\n      this.configData = this.origConfigData;\r\n      this.thirdPartyData = this.origThirdPartyData;\r\n      setTimeout(() => {\r\n        this.$emit(\"closeSystemConfig\");\r\n      }, 500);\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n.about-data-modal {\r\n  .vertical-center-modal {\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n\r\n    .ivu-modal {\r\n      top: 0;\r\n    }\r\n  }\r\n\r\n  .ivu-modal-close {\r\n    top: 15px;\r\n    right: 20px;\r\n  }\r\n\r\n  .content {\r\n    padding: 4px 14px;\r\n    overflow-y: auto;\r\n    //height: 700px;\r\n    //height: 400px;\r\n  }\r\n\r\n  @media only screen and (min-width: 1200px) {\r\n    .ivu-modal-close {\r\n      top: 18px;\r\n    }\r\n  }\r\n\r\n  .ivu-modal-header {\r\n    padding: 15px 20px;\r\n    border-bottom: 1px solid #f7f7f7;\r\n\r\n    .header {\r\n      overflow: hidden;\r\n\r\n      .left {\r\n        float: left;\r\n        width: 6px;\r\n        height: 24px;\r\n        margin-right: 8px;\r\n        background-color: #0ac7c0;\r\n        border-radius: 3px;\r\n      }\r\n\r\n      .title {\r\n        float: left;\r\n        height: 24px;\r\n        line-height: 24px;\r\n        font-size: 14px;\r\n        font-weight: bold;\r\n        color: #999;\r\n\r\n        span {\r\n          color: #333;\r\n        }\r\n      }\r\n\r\n      .es-btn {\r\n        float: right;\r\n        margin-right: 35px;\r\n      }\r\n\r\n      @media only screen and (min-width: 1200px) {\r\n        .left {\r\n          height: 30px;\r\n        }\r\n\r\n        .title {\r\n          height: 30px;\r\n          line-height: 30px;\r\n          font-size: 18px;\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .ivu-modal-body {\r\n    .content {\r\n      padding: 4px 14px;\r\n      overflow-y: auto;\r\n\r\n      .row {\r\n        margin-bottom: 20px;\r\n\r\n        .item {\r\n          padding: 15px;\r\n          border: 1px solid #f7f7f7;\r\n          border-radius: 4px;\r\n\r\n          label {\r\n            color: #999;\r\n            font-weight: bold;\r\n          }\r\n\r\n          p {\r\n            margin-top: 10px;\r\n            color: #333;\r\n          }\r\n        }\r\n      }\r\n\r\n      .form-item {\r\n        padding: 15px 15px 22px;\r\n        margin-bottom: 16px;\r\n        border-radius: 4px;\r\n        background-color: #f7f7f7;\r\n      }\r\n    }\r\n  }\r\n\r\n  .ivu-modal-footer {\r\n    height: 50px;\r\n    border-top: none;\r\n    padding: 0;\r\n  }\r\n}\r\n</style>\r\n\r\n<style lang=\"less\" scoped>\r\n/deep/ .ivu-btn-primary {\r\n  color: #fff;\r\n  background-color: #31babb;\r\n  border-color: #31babb;\r\n}\r\n\r\n/deep/ .ivu-btn-primary:hover {\r\n  color: #fff !important;\r\n  border-color: #31babb;\r\n}\r\n\r\n/deep/ .ivu-switch-checked {\r\n  background-color: #31babb;\r\n  border-color: #31babb;\r\n}\r\n\r\n.musicIcon {\r\n  vertical-align: middle;\r\n  margin-left: 5px;\r\n  color: #31babb;\r\n  cursor: pointer;\r\n}\r\n\r\n/deep/ .ivu-tag .ivu-icon-ios-close {\r\n  display: none;\r\n}\r\n\r\n/deep/ .ivu-select-multiple .ivu-tag span:not(.ivu-select-max-tag) {\r\n  margin-right: 0px;\r\n}\r\n</style>\r\n"]}]}