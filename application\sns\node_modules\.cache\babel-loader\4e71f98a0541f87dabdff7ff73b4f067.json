{"remainingRequest": "E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\node_modules\\babel-loader\\lib\\index.js!E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\node_modules\\string-replace-loader\\index.js??ref--13-0!E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\src\\locales\\zh-CN.js", "dependencies": [{"path": "E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\src\\locales\\zh-CN.js", "mtime": 1754362736991}, {"path": "E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\node_modules\\string-replace-loader\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["E:/GitRepos/FusionNetProject/6849/frontend/application/sns/src/locales/zh-CN.js"], "names": ["locale", "lang", "common", "consumerError", "consumerSignatureError", "invalidUserOrPassword", "badGateway", "monitor20", "monitor21", "monitor22", "monitor23", "monitor24", "monitor25", "monitor26", "monitor27", "confirmDeleteSelection", "confirmDelete", "appMainTitle", "systemTitle", "indoorRealTimePositioningSystem", "traditionalChinese", "simplifiedChinese", "english", "introduction", "people", "piece", "page", "import", "export", "exportExcel", "newlyIncreased", "search", "delete", "removePair", "list", "confirm", "cancel", "homePage", "reset", "edit", "upload", "storage", "total", "pen", "date", "time", "equipment", "noDataText", "inputKeyword", "keywordEvent", "pleaseSelect", "pleaseSearch", "chooseStartTime", "chooseEndTime", "exportFormatForExcel", "seeMoreEvents", "return", "all", "marked", "notMarked", "cancelled", "hasBeenIdentified", "deleteConfirm", "deleteItemConfirm", "prompt", "selectEventToDelete", "notUsePermission", "deleteSuccessful", "removePairSuccessful", "addSuccessful", "storageSuccessful", "exportSuccessful", "importSuccessful", "searchSuccessful", "modifySuccessful", "syncSuccessful", "exportFail", "searchFail", "backgroundManagement", "personalData", "personal", "operation", "changeThePassword", "logout", "pleaseEnterOriginalPassword", "pleaseEnterNewPassword", "pleaseEnterNewPasswordAgain", "passwordError", "tooManyResourcesError", "passwordErrorPrompt", "eventRecordErrorPrompt", "samePasswordPrompt", "twoNewPasswordsAreNotConsistent", "planeNoUploadCoordinatesError", "noInformationOnFloor", "selectMap", "floorPlan", "selectDate", "successful", "fillInEventRecord", "warning", "error", "invalidFormat", "contactSystemAdministrator", "modificationSuccessfulLoginAgain", "loginPeriodExpires", "selectedEventDoesNot", "networkProblemPleaseRefreshPage", "networkProblem", "passwordUnavailableError", "accessCodeUnavailableError", "storageUnavailableError", "accountAlreadyExists", "dataAlReadyExists", "deviceObjectExistsError", "objectDeviceExistsError", "accountNotFoundError", "resourceNotFoundError", "planeNotFoundError", "operationIsNotAllowed", "selectAtLeastOneAttribute", "itNotExist", "reorganizeThePage", "selectedEventState", "fillInIncidentRecord", "invalidRequestError", "badPasswordError", "badEmailError", "badPhoneError", "invalidAccessTaskError", "uneventful", "removeEvent", "event", "trajectory", "locateObject", "templateHelp", "objectsDisplayed", "serialNumber", "name", "category", "role", "group", "currentPosition", "latestPositioningTime", "modifiesAt", "byTheTime", "plane", "eventClassification", "sponsorName", "initiator", "eventLog", "eventState", "viewTheDayTrack", "viewCurrentLocation", "monitoring", "fenceMonitoring", "mmWaveMonitoring", "monitor01", "monitor02", "monitor03", "monitor04", "managePermissions", "accountManagement", "roleManagement", "firmwareUpdate", "positioningSettings", "planePosition", "baseStation", "anchor", "guard", "no", "dataManagement", "objectFeatures", "objectData", "deviceInformation", "eventDefinition", "guardSetting", "otaUpdate", "licenseManagement", "historyReport", "temporarilyNoData", "cameraSetting", "logRecording", "inventory", "systemManagement", "systemConfig", "systemSWVersion", "mmWave", "fence", "helpEvent", "enterTheNotice", "leaveTheNotice", "theNumberOfControl", "lowBatteryWarning", "stationAbnormalWarning", "stayTimeout", "regularRound", "leaveBed", "abnormal<PERSON>uard", "sensorDataDriven", "fallDetection", "stayTimeoutMMWave", "leaveBedMMWave", "getUp", "abnormalBreath", "wetUrine", "help", "untreated", "inTheProcessing", "hasLift", "personnel", "map", "eventName", "objectsName", "order", "personnelDistribution", "equipmentDistribution", "area", "subRegion", "factory", "building", "floor", "areaName", "factoryName", "buildingName", "floorName", "subRegionName", "geoCluster", "coordinate", "hasAttachment", "offline", "planeName", "originalCoordinates", "currentCoordinates", "hasChange", "mapHint", "archives", "pairingDevice", "inputCode", "inputName", "inputCodeError", "selectObject", "selectDeleteObject", "selectGroup", "onTheGround", "underground", "okToDoThis", "okToDeleteSelection", "whetherToReset", "selectTheObject", "noData", "limitDeleteRecords", "planeNotFound", "selectPlane", "chooseType", "uploadTime", "yes", "nay", "type", "document", "enabled", "notEnabled", "selectedItems", "planeDataExists", "notAllowedDelete", "badConfiguration", "fullScreen", "exitFullScreen", "noShortcuts", "login", "account", "password", "resetPassword", "accountRequiredVerification", "passwordRequiredVerification", "passwordPatternVerification", "pleaseResetAccountPassword", "inputNewPassword", "inputNewPasswordAgain", "send", "newPasswdRequiredVerification", "newPasswdRequiredVerificationAgain", "newPasswdPatternVerification", "newPasswd<PERSON><PERSON><PERSON>", "resetPasswordSuccessful", "inputEmail", "inputVcode", "emailRequiredVerification", "emailTypeVerification", "vcodeRequiredVerification", "vcodeError", "sendEmailSuccessful", "accountNotFoundPrompt", "email", "phone", "userEmailExistsVerification", "nameRequiredVerification", "nameTypeVerification", "phoneRequiredVerification", "phonePatternVerification", "userRoleRequiredVerification", "dashBoard", "device", "totalCount", "normal", "lowBattery", "lowBatteryEvent", "stationAbnormal", "more", "guardAbnormal", "managementShortcut", "course", "deviceName", "pairObject", "battery", "latestBatteryTime", "lowBatteryNotice", "stationName", "locationRegion", "connectionState", "startsAt", "abnormalNotice", "<PERSON><PERSON><PERSON>", "operationDate", "ipPosition", "operationRecord", "noPlaneInformation", "noCoordinateInformation", "regionNotUploadPlaneMap", "monitorManagerment", "loginSystem", "logoutSystem", "edit<PERSON><PERSON>", "applicationIntegration", "clickOnClosed", "clickOnA", "dragLeftRightToResizeTheWindow", "DeEventOperation", "DetermineUnselectedEvent", "searchMonitoringConditions", "mandatoryFieldArea", "unableToDisplayTheMap", "storeFailure", "storeSuccessful", "noPlaneRelatedConsultation", "noCoordinateConsultation", "noCoordinateOrPlaneRelatedConsultation", "anUnexaminedObject", "selectProjectToTerminated", "validatePlaneErrorInfo", "noFlatMapData", "keyword", "download", "recordPreview", "fileId", "cameraCode", "planeCode", "filename", "permission", "deleteAccountPrompt", "addSystemAccount", "inputAccount", "inputPassword", "inputPhone", "selectRole", "accountTypeVerification", "userAccountExistsVerification", "accountName", "instantEventInspection", "localizingObjectInspection", "noInstantEvent", "immediateEvent", "enter", "leave", "numberControl", "abnormalStation", "selectMonitoringConditionPrompt", "pleaseSelectCategory", "pleaseSelectRole", "pleaseSelectGroup", "pleaseSelectRegion", "categoryRequiredVerification", "roleRequiredVerification", "groupRequiredVerification", "regionRequiredVerification", "addLocationObject", "deleteMonitoringConditionPrompt", "selectTypeRoleGroup", "roleCode", "<PERSON><PERSON><PERSON>", "systemRoleCode", "systemRoleName", "selectDeleteCodes", "userRoleNotFound", "userConflicts", "view", "addRole", "enterRoleCode", "enterRoleName", "permissionSetting", "codeRuleValidate", "nameRuleValidate", "permissionRuleValidate", "moduleNotFound", "userRoleCodeExists", "categoryCode", "categoryName", "icon", "groupCode", "groupName", "pleaseSelectDeleteCategory", "pleaseSelectDeleteRole", "pleaseSelectDeleteGroup", "objectTypeNotFoundVerification", "objectRoleNotFoundVerification", "objectGroupNotFoundVerification", "objectConflictsVerification", "eventConflictsVerification", "monitorConflicts", "addLocationAttribute", "pleaseSelectIcon", "pleaseSelectLocationAttribute", "locationAttributeRequiredVerification", "codeRequiredVerification", "codeTypeVerification", "iconRequiredVerification", "objectCodeExistsVerification", "code", "excelExampleDownload", "pleaseSelectUploadImportFiles", "uploadFiles", "fileFormatForxls", "systemReadsPens", "uploadFileForExcel", "pleaseSelectUploadFiles", "pleaseSelectALocationAttribute", "correctInformation", "errorInformation", "errorCode", "locationAttributeName", "stationConfiguration", "sid", "IP", "systemVersion", "appVersion", "isAlive", "correctImportedError", "planeLayerAtLeastError", "unableCoordinatePositioning", "selectCorrespondingClass", "XCoordinate", "YCoordinate", "flatArea", "inputSid", "inputSidCode", "lastConnectionTime", "fieldInformation", "selectTheArea", "selectTheUploadedFile", "addBaseStation", "baseStationIncorrectDuplicatedError", "sidCodeLengthError", "nameLengthError", "addCamera", "prioritySeq", "cameraId", "cameraNo", "cameraName", "fieldFilterToolTip", "deviceMAC", "locationObject", "locationTime", "residualBattery", "pm25", "tvoc", "temperature", "humidity", "pm25GetTime", "tvocGetTime", "temperatureGetTime", "humidityGetTime", "heartRate", "latestHeartRateTime", "sbp", "latestSbpTime", "dbp", "latestDbpTime", "softwareVersion", "deviceNotFoundVerification", "pleaseSelectDeleteDevice", "pleaseSelectRemovePairItem", "pleaseConfirmOperationCorrectness", "addDeviceInformation", "inputDeviceMAC", "inputDeviceName", "selectPairObject", "pidRequiredVerification", "pidTypeVerification", "objectNotFoundVerification", "objectDeviceExistsVerification", "devicePidExistsVerification", "currentPairDevice", "anchorConfiguration", "anchorMac", "anchorName", "inputAnchorMac", "inputPIDCode", "addAnchor", "anchorMacIncorrectDuplicatedError", "anchorMacLengthError", "guardConfiguration", "region", "detectMode", "enableOrClose", "synchronousState", "enable", "close", "synchronizing", "synchronized", "dataSynchronizationUpdate", "guardNotFoundVerification", "pleaseSelectDeleteGuard", "pleaseSelectPlaneMap", "pleaseSelectCorrespondingClass", "addG<PERSON>", "basicInformation", "pleaseSelectPairObject", "pleaseSelectDetectMode", "bodyTemp", "bodySize", "bedWidth", "<PERSON><PERSON><PERSON><PERSON>", "ceilingHeight", "stopAlarmTime", "bathroomLength", "bathroomWidth", "intervalTime", "ipSetting", "rawDataCollectionSetting", "ipCameraEnableOrClose", "ipCameraCircleFrequency", "logUploadFrequency", "logPackingSize", "bodyTempRequiredVerification", "bodySizeRequiredVerification", "bedWidthRequiredVerification", "bedLengthRequiredVerification", "ceilingHeightRequiredVerification", "stopAlarmTimeRequiredVerification", "bathroomLengthRequiredVerification", "bathroomWidthRequiredVerification", "intervalTimeRequiredVerification", "bodyTempTypeVerification", "bodySizeTypeVerification", "bedWidthTypeVerification", "bedLengthTypeVerification", "ceilingHeightTypeVerification", "stopAlarmTimeTypeVerification", "bathroomLengthTypeVerification", "bathroomWidthTypeVerification", "intervalTimeTypeVerification", "objectGuardExistsVerification", "guardCodeExistsVerification", "columnMustNumber", "columnMustPositiveInteger", "leastSocketServerIp", "enableFusionGuardMustItem", "xCoordinate", "yCoordinate", "basicInformationMustCompleteForEnableGuard", "currentPairObject", "planeNotFoundVerification", "needPortField", "objectNotFound", "objectTypeNotFound", "objectRoleNotFound", "objectGroupNotFound", "deviceNotFound", "guardNotFound", "deviceObjectExists", "guardObjectExists", "sitePlanConfig", "addSitePlanConfig", "updateFileMap", "updateOneFileMap", "flatPositionError", "flatFactoryError", "flatBuildingError", "flatFloorError", "flatRegionError", "flatCode", "flatName", "flatCodeError", "flatNameError", "selectTheDeletedObject", "delSelectItemsError", "stationConflictsError", "eventConflictsError", "planeConflictsError", "anchorConflicts", "positioningPlanePosition", "selectCorrespondingLevelError", "readjustStationAnchorPoint", "dragNoticeError", "savePromptingError", "deletePlanesChildPositionError", "isPoptip", "establishOrder", "establishAtLeastOneFactoryAreaFirst", "establishAtLeastOneBuildingAreaFirst", "establishAtLeastOneFloorAreaFirst", "establishAtLeastOneSubRegionAreaFirst", "selectAndFillInFloor", "floorInputCaveat", "fieldInformationCaveat", "zoomRatioMustTheSame", "validatePlaneCodeError", "planeCodeTypeError", "planeCodeTypeLengthError", "selectFool", "validateFloorError", "planeNameError", "planeNameLengthError", "addPlaneError", "itemHasNotSetCoordinate", "floorPlanPreview", "singleEvent", "planeEvent", "eventCode", "eventTemplate", "addEventDefinition", "selectTemplate", "enterEventCode", "enterEventName", "enterEventNameNoStar", "selectEventCategory", "selectEventCategoryNoStar", "enterValue", "selectStartTime", "selectEndTime", "selectSponsorType", "selectSponsorRole", "selectSponsorGroup", "selectParticipantType", "selectParticipantRole", "selectParticipantGroup", "selectNotifierType", "selectNotifierRole", "selectNotifierGroup", "selectNotifierAccount", "enterNotifierMsg", "selectDeleteEvent", "threshold", "below", "over", "stayOver", "batteryPercentage", "<PERSON><PERSON><PERSON><PERSON>", "min<PERSON><PERSON><PERSON>", "intervals", "between", "sensorDataDrivenData", "it", "ite", "gt", "gte", "dataDrivenRuleSource", "dataDrivenRuleComp", "enterSource", "enterComp", "dataDrivenRuleRepeat", "dataDrivenRuleCount", "dataDrivenRuleDuration", "dataDrivenRuleDurationSec", "validatePlaneCode", "validateCategory", "validateSponsor", "validateParticipant", "enterEventCodeValidate", "eventCodeValidate", "enterEventNameValidate", "eventNameValidate", "templateValidate", "enterThresholdValidate", "thresholdValidatePattern", "thresholdValidateMax", "msgValidate", "badInterval", "sponsorObjectTypeNotFound", "sponsorObjectRoleNotFound", "sponsorObjectGroupNotFound", "participantObjectTypeNotFound", "participantObjectRoleNotFound", "participantObjectGroupNotFound", "thresholdNotFound", "notifierObjectTypeNotFound", "notifierObjectRoleNotFound", "notifierObjectGroupNotFound", "notifierUserNotFound", "eventCodeExists", "warningCondition", "controlTime", "startTime", "entTime", "eventCategory", "participantType", "participantRole", "participantGroup", "sponsorType", "sponsorRole", "sponsorGroup", "notifierType", "notifierRole", "notifierGroup", "notifierUser", "notifierMsg", "addControlTime", "planeSetting", "sponsorTypeSetting", "sponsorRoleSetting", "sponsorGroupSetting", "participantTypeSetting", "participantRoleSetting", "participantGroupSetting", "notifierTypeSetting", "notifierRoleSetting", "notifierGroupSetting", "notifierUserSetting", "thresholdValidate", "searchEventTemplate", "searchSelectTemplate", "eventNotFound", "taskConflicts", "uploadRecord", "scheduleList", "fileName", "uploadOta", "station", "stationApk", "wristband", "button", "tag", "fromVersion", "toVersion", "description", "activeName", "nordicVersion", "bootloaderVersion", "stVersion", "nordicFileName", "bootloaderFileName", "stFileName", "uploadFile", "fileNameFormatReference", "uploadNordicFile", "uploadbootLoaderFile", "uploadStFile", "inputDescription", "setUpdateScheduling", "queryUpdateResults", "back", "completedQuantity", "unfinishedQuantity", "updateTime", "updateObject", "originalVersion", "newVersion", "currentVersion", "toUpdateVersion", "errorMessage", "schedulingName", "targetPlane", "count", "actionSuccess", "actionUnsuccess", "schedulingEnable", "updateType", "updateRole", "updateGroup", "noEnable", "pleaseSelectDeleteStation", "pleaseSelectDeleteStationApk", "pleaseSelectDeleteAnchor", "pleaseSelectDeleteWristband", "pleaseSelectDeleteButton", "pleaseSelectDeleteTag", "otaScheduleNotFoundVerification", "updateCycle", "sunday", "monday", "tuesday", "wednesday", "thursday", "friday", "saturday", "setUpdateSchedulingSuccessful", "inputSchedulingName", "inputSchedulingCode", "pleaseSelectStartsAt", "pleaseSelectEndsAt", "pleaseSelectUpdateType", "pleaseSelectUpdateRole", "pleaseSelectUpdateGroup", "pleaseSelectTargetPlane", "pleaseSelectNotifierUserAccounts", "targetPlaneRequiredVerification", "startsAtRequiredVerification", "typeRequiredVerification", "selectTargetPlane<PERSON>ength", "targetPlaneNotFoundVerification", "updateTypeRoleGroupVerification", "targetObjectTypeNotFoundVerification", "targetObjectRoleNotFoundVerification", "targetObjectGroupNotFoundVerification", "notifierUserNotFoundVerification", "otaScheduleCodeExistsVerification", "recursiveVersionFound", "badFilename", "badCycleVerification", "updateDate", "pleaseInputSchedulingName", "queryUpdateResultsSuccessful", "updateTimeRequiredVerification", "uploadSuccess", "licenseRequest", "enterCustomerId", "enterSerialNum", "selectService", "useRegCode", "enterRegCode", "enterCustomerIdHint", "enterSerialNumHint", "selectServiceHint", "customerIdRuleValidate", "serialNumRuleValidate", "copy", "serviceNotFound", "customerNotFound", "enterRegCodeHint", "regCodeRuleValidate", "regSuccessful", "badRegistrationCode", "customerName", "totalService", "validStation", "validDevice", "expiredTime", "status", "id", "serviceSerial", "serviceName", "serviceIp", "licenseTime", "validServiceCount", "invalidServiceCount", "slash", "copyRequestCode", "noError", "licenseExpired", "licenseExpiredSoon", "action", "eventPlane", "sponsorObjects", "startAndEndTime", "treating", "treated", "camera", "editCamera", "cameraIp", "streamUrl", "cameraVideoPath", "taskVideoPath", "enterCameraCode", "enterCameraName", "enterCameraIp", "enterStreamUrl", "enterCameraVideoPath", "enterTaskVideoPath", "cameraIpPosition", "cameraConnStatus", "addLogRecording", "chooseDevice", "choosePlane", "chooseAction", "chooseValidTime", "oneHour", "threeHour", "twelfthHour", "oneDay", "threeDay", "fiveDay", "oneWeek", "enterDescription", "devicesName", "devicesID", "devicesPID", "reportUrl", "logUrl", "message", "finishesAt", "expiresAt", "waiting", "logCollecting", "logCollected", "reportCreating", "logFileCreating", "finished", "failed", "canceled", "enterNameValidate", "eventNameValidateLength", "logNotFound", "waitForInitialization", "requestUnavailable", "isExport", "logNo", "readyRecord", "recording", "readyExport", "exportingLog", "exportingFile", "recorded", "recordFail", "stopRecord", "pleaseEnter", "pleaseUpload", "devicesPosition", "latestPositionTime", "hasCounted", "reCounted", "refresh", "latestCountingTime", "addIvnentory", "addTask", "index", "task", "iconCode", "enterNumber", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "objectCode", "enterPurchaseDate", "uploadPhoto", "serial", "inputValidateLength20", "inputValidateLength32", "inputValidateLength64", "keeperUserNotFound", "managerUserNotFound", "iconNotFound", "categoryNotFound", "objectInventoryExists", "inventoryCodeExists", "detail", "taskDetail", "createsAt", "eolAt", "active", "repairing", "calibrating", "servicing", "eol", "objectName", "position", "positionXY", "images", "taskNumber", "taskDescription", "ownerUser", "countedCount", "uncountedCount", "inProgress", "key", "value", "defaultValue", "min", "max", "max<PERSON><PERSON><PERSON>", "unit", "schemaGroupAPI", "schemaGroupLogging", "schemaGroupHelp", "schemaGroupUser", "schemaGroupEnvironment", "schemaGroupPositioning", "schemaGroupConnection", "schemaGroupWeb", "descReserveJournalDuration", "descStationOfflinePeriod", "descDeviceOfflinePeriod", "descDeviceOtaRssiThreshold", "descGatewayOfflinePeriod", "descSmtpEnable", "descSmtpHost", "descSmtpPort", "descSmtpUsername", "descSmtpPassword", "descHelpTaskDelayPeriod", "descHelpTurnOffDeviceTimer", "descHelpTurnOffDeviceRetryCount", "descHelpTaskMinRssiThreshold", "descUserRegisterCodeExpirePeriod", "descUserRegisterCodeLength", "descApiAccessTokenExpirePeriod", "descApiRefreshTokenExpirePeriod", "descApiDefaultSearchActive", "descApiDefaultMaxSize", "descReportCachePeriod", "descTrackingKalmanFilterEnable", "descTrackingKalmanFilterMeterRangePerSecond", "descTrackingKalmanFilterAccuracy", "descLocationKalmanFilterEnable", "descLocationKalmanProcessNoise", "descLocationKalmanMeasurementNoise", "descPositionPersistencePeriod", "descPositionHistoryPersistencePeriod", "descPositioningAlgorithmV2", "descPositioningPeroid", "descPositionMinRssiThreshold", "descTrackingRegionTotalCount", "descTrackingRegionMatchCount", "descMonitorRefreshSecond", "descStayTimeoutDefaultInterval", "descHistoryMatchCount", "ipAddr", "ipError", "version", "checkAt", "apps", "posParameterCode", "deviceType", "displayStations", "monitor", "object", "configSetting", "selectDelete", "add", "cancelAdd", "cancelEdit", "saveAdd", "saveEdit", "picEdit", "p<PERSON><PERSON><PERSON><PERSON>", "picUpload", "picConfirm", "planeEdit", "planeSet", "picBackgroundUpload", "iconPicEdit", "iconPicUpload", "objectDeviceConfirm", "record", "recordPlaceholder", "eventAlarm", "eventAlarmWindow", "eventStatus", "eventClean", "eventCleanConfirm", "eventStartTime", "emergencyEvent", "deviceMac", "occupy", "selectDeleteError", "abnormalDevice", "second", "deviceConnectionAbnormal", "deviceConnectionAbnormalLatestEvent", "m20", "selectMonitorPlane", "selectMonitorObject", "m21", "eFence", "confirmEdit", "fall", "positionStation", "allPositionStation", "enterAndExit", "exit", "m01", "addSpecialStatus", "<PERSON><PERSON><PERSON>", "timeoutSetting", "specialStatus", "baby", "lossSignal", "wetUrineTimeout", "noPatient", "hasPatient", "babyCare", "removeSpecialStatus", "removeSpecialStatusConfirm", "babyMove", "unknownStation", "specialEvent", "babyRegion", "diaperNormal", "babyTagLowBattery", "motherTagLowBattery", "monitorOutside", "emptyBed", "enterSetting", "entering", "eventId", "eventNo", "alertCondition", "less", "minsSignal", "addMon", "objectAttr", "addBaby", "resetDiaperComfortable", "mon", "baby2", "propertySetting", "objectSetting", "stationSetting", "stationSelect", "monRoom", "babyRoom", "controlArea", "warningArea", "normalArea", "objectName2", "objectType", "addBed", "bed", "toilet", "addRegion", "regionType", "showRegion", "addSubRegion", "m03", "overtemperature", "wristbandLowBattery", "bodyOvertemperature", "negativePressureIsolationCenter", "nowTemperature", "nowHeartRate", "nowBloodOxygen", "nowSbp", "nowDbp", "temperaturePicAndNumerical", "temperatureColor", "remind", "bodyTemperatureWarm", "bodyTemperatureDetect", "heartRateDetect", "bloodOxygenDetect", "sbpDetect", "dbpDetect", "greater", "greaterThanOrEqual", "lessThanOrEqual", "degree", "timesPerMin", "negativePressureIsolationRoom", "editConfirm", "bodyPhysicalDataSetting", "continuousMonitoringDuration", "nonContinuousMonitoringAbnormalCritical", "continuousMonitoring", "nonContinuousMonitoring", "times", "alert", "abnormal", "critical", "bloodOxygen", "bloodPressure", "bodyTemperature", "backgroundColor", "deviceLowBattery", "lowTemperature", "overTemperature", "lowHeartRate", "overHeartRate", "lowBloodOxygen", "overBloodPressure", "lowBloodPressure", "overBloodPressureSbp", "lowBloodPressureSbp", "overBloodPressureDbp", "lowBloodPressureDbp", "sensorType", "current", "history", "lying", "sit", "natureCall", "now", "breathe", "breathe5MinsRecord", "bpm", "mins", "correct", "notCorrect", "empty", "breathDetect", "stopDetect", "eventCondition", "connectStatus", "connecting", "nowObject", "allObject", "beforeIndex", "afterIndex", "searchCondition", "eventEdit", "sponsorObjectType", "preFall", "m02", "stationID", "employeeNo", "employeeName", "phoneNo", "modifyTime", "enterControlArea", "notice", "install", "remove", "leaveControlArea", "enableAirwatch", "m27"], "mappings": "AAAA;AACA,OAAO,IAAMA,MAAM,GAAG;AACpBC,EAAAA,IAAI,EAAE;AACJ;AACAC,IAAAA,MAAM,EAAE;AACN;AACAC,MAAAA,aAAa,EAAE,oBAFT;AAGNC,MAAAA,sBAAsB,EAAE,kBAHlB;AAINC,MAAAA,qBAAqB,EAAE,6BAJjB;AAKNC,MAAAA,UAAU,EAAE,eALN;AAMNC,MAAAA,SAAS,EAAE,MANL;AAONC,MAAAA,SAAS,EAAE,MAPL;AAQNC,MAAAA,SAAS,EAAE,MARL;AASNC,MAAAA,SAAS,EAAE,MATL;AAUNC,MAAAA,SAAS,EAAE,MAVL;AAWNC,MAAAA,SAAS,EAAE,MAXL;AAYNC,MAAAA,SAAS,EAAE,MAZL;AAaNC,MAAAA,SAAS,EAAE,KAbL;AAcN;AACAC,MAAAA,sBAAsB,EAAE,SAflB;AAgBNC,MAAAA,aAAa,EAAE,MAhBT;AAiBN;AACAC,MAAAA,YAAY,EAAE,SAlBR;AAmBNC,MAAAA,WAAW,EAAE,YAnBP;AAoBNC,MAAAA,+BAA+B,EAAE,YApB3B;AAqBNC,MAAAA,kBAAkB,EAAE,MArBd;AAsBNC,MAAAA,iBAAiB,EAAE,MAtBb;AAuBNC,MAAAA,OAAO,EAAE,IAvBH;AAwBNC,MAAAA,YAAY,EAAE,IAxBR;AAyBNC,MAAAA,MAAM,EAAE,GAzBF;AA0BNC,MAAAA,KAAK,EAAE,GA1BD;AA2BN;AACAC,MAAAA,IAAI,EAAE,GA5BA;AA6BNC,MAAAA,MAAM,EAAE,IA7BF;AA8BNC,MAAAA,MAAM,EAAE,IA9BF;AA+BNC,MAAAA,WAAW,EAAE,YA/BP;AAgCNC,MAAAA,cAAc,EAAE,IAhCV;AAiCNC,MAAAA,MAAM,EAAE,IAjCF;AAkCNC,MAAAA,MAAM,EAAE,IAlCF;AAmCNC,MAAAA,UAAU,EAAE,MAnCN;AAoCNC,MAAAA,IAAI,EAAE,IApCA;AAqCNC,MAAAA,OAAO,EAAE,IArCH;AAsCNC,MAAAA,MAAM,EAAE,IAtCF;AAuCNC,MAAAA,QAAQ,EAAE,IAvCJ;AAwCNC,MAAAA,KAAK,EAAE,IAxCD;AAyCNC,MAAAA,IAAI,EAAE,IAzCA;AA0CNC,MAAAA,MAAM,EAAE,IA1CF;AA2CNC,MAAAA,OAAO,EAAE,IA3CH;AA4CNC,MAAAA,KAAK,EAAE,GA5CD;AA6CNC,MAAAA,GAAG,EAAE,GA7CC;AA8CNC,MAAAA,IAAI,EAAE,IA9CA;AA+CNC,MAAAA,IAAI,EAAE,IA/CA;AAgDNC,MAAAA,SAAS,EAAE,IAhDL;AAiDNC,MAAAA,UAAU,EAAE,MAjDN;AAkDNC,MAAAA,YAAY,EAAE,QAlDR;AAmDNC,MAAAA,YAAY,EAAE,SAnDR;AAoDNC,MAAAA,YAAY,EAAE,KApDR;AAqDNC,MAAAA,YAAY,EAAE,OArDR;AAsDNC,MAAAA,eAAe,EAAE,SAtDX;AAuDNC,MAAAA,aAAa,EAAE,SAvDT;AAwDNC,MAAAA,oBAAoB,EAAE,YAxDhB;AAyDNC,MAAAA,aAAa,EAAE,OAzDT;AA0DNC,MAAAA,MAAM,EAAE,IA1DF;AA2DNC,MAAAA,GAAG,EAAE,IA3DC;AA4DNC,MAAAA,MAAM,EAAE,KA5DF;AA6DNC,MAAAA,SAAS,EAAE,KA7DL;AA8DNC,MAAAA,SAAS,EAAE,KA9DL;AA+DNC,MAAAA,iBAAiB,EAAE,KA/Db;AAgENC,MAAAA,aAAa,EAAE,MAhET;AAiENC,MAAAA,iBAAiB,EAAE,YAjEb;AAkEN;AACAC,MAAAA,MAAM,EAAE,IAnEF;AAoENC,MAAAA,mBAAmB,EAAE,WApEf;AAqENC,MAAAA,gBAAgB,EAAE,gBArEZ;AAsENC,MAAAA,gBAAgB,EAAE,OAtEZ;AAuENC,MAAAA,oBAAoB,EAAE,SAvEhB;AAwENC,MAAAA,aAAa,EAAE,OAxET;AAyENC,MAAAA,iBAAiB,EAAE,OAzEb;AA0ENC,MAAAA,gBAAgB,EAAE,OA1EZ;AA2ENC,MAAAA,gBAAgB,EAAE,OA3EZ;AA4ENC,MAAAA,gBAAgB,EAAE,OA5EZ;AA6ENC,MAAAA,gBAAgB,EAAE,OA7EZ;AA8ENC,MAAAA,cAAc,EAAE,OA9EV;AA+ENC,MAAAA,UAAU,EAAE,WA/EN;AAgFNC,MAAAA,UAAU,EAAE,WAhFN;AAiFN;AACAC,MAAAA,oBAAoB,EAAE,MAlFhB;AAmFNC,MAAAA,YAAY,EAAE,OAnFR;AAoFNC,MAAAA,QAAQ,EAAE,IApFJ;AAqFNC,MAAAA,SAAS,EAAE,IArFL;AAsFNC,MAAAA,iBAAiB,EAAE,MAtFb;AAuFNC,MAAAA,MAAM,EAAE,IAvFF;AAwFNC,MAAAA,2BAA2B,EAAE,QAxFvB;AAyFNC,MAAAA,sBAAsB,EAAE,QAzFlB;AA0FNC,MAAAA,2BAA2B,EAAE,UA1FvB;AA2FNC,MAAAA,aAAa,EAAE,gBA3FT;AA4FNC,MAAAA,qBAAqB,EAAE,sBA5FjB;AA6FNC,MAAAA,mBAAmB,EAAE,2BA7Ff;AA8FNC,MAAAA,sBAAsB,EAAE,oBA9FlB;AA+FNC,MAAAA,kBAAkB,EAAE,kBA/Fd;AAgGNC,MAAAA,+BAA+B,EAAE,cAhG3B;AAiGNC,MAAAA,6BAA6B,EAAE,qBAjGzB;AAkGNC,MAAAA,oBAAoB,EAAE,eAlGhB;AAmGNC,MAAAA,SAAS,EAAE,QAnGL;AAoGNC,MAAAA,SAAS,EAAE,KApGL;AAqGNC,MAAAA,UAAU,EAAE,OArGN;AAsGNC,MAAAA,UAAU,EAAE,IAtGN;AAuGNC,MAAAA,iBAAiB,EAAE,SAvGb;AAwGNC,MAAAA,OAAO,EAAE,IAxGH;AAyGNC,MAAAA,KAAK,EAAE,IAzGD;AA0GNC,MAAAA,aAAa,EAAE,OA1GT;AA2GNC,MAAAA,0BAA0B,EAAE,iBA3GtB;AA4GNC,MAAAA,gCAAgC,EAAE,aA5G5B;AA6GNC,MAAAA,kBAAkB,EAAE,iBA7Gd;AA8GNC,MAAAA,oBAAoB,EAAE,mBA9GhB;AA+GNC,MAAAA,+BAA+B,EAAE,eA/G3B;AAgHNC,MAAAA,cAAc,EAAE,sBAhHV;AAiHNC,MAAAA,wBAAwB,EAAE,8BAjHpB;AAkHNC,MAAAA,0BAA0B,EAAE,+BAlHtB;AAmHNC,MAAAA,uBAAuB,EAAE,yBAnHnB;AAoHNC,MAAAA,oBAAoB,EAAE,QApHhB;AAqHNC,MAAAA,iBAAiB,EAAE,QArHb;AAsHNC,MAAAA,uBAAuB,EAAE,uBAtHnB;AAuHNC,MAAAA,uBAAuB,EAAE,uBAvHnB;AAwHNC,MAAAA,oBAAoB,EAAE,SAxHhB;AAyHNC,MAAAA,qBAAqB,EAAE,OAzHjB;AA0HNC,MAAAA,kBAAkB,EAAE,mBA1Hd;AA2HNC,MAAAA,qBAAqB,EAAE,mBA3HjB;AA4HNC,MAAAA,yBAAyB,EAAE,YA5HrB;AA6HNC,MAAAA,UAAU,EAAE,WA7HN;AA8HNC,MAAAA,iBAAiB,EAAE,UA9Hb;AA+HNC,MAAAA,kBAAkB,EAAE,UA/Hd;AAgINC,MAAAA,oBAAoB,EAAE,SAhIhB;AAiINC,MAAAA,mBAAmB,EAAE,eAjIf;AAkINC,MAAAA,gBAAgB,EAAE,8BAlIZ;AAmINC,MAAAA,aAAa,EAAE,iBAnIT;AAoINC,MAAAA,aAAa,EAAE,SApIT;AAqINC,MAAAA,sBAAsB,EAAE,qBArIlB;AAsINC,MAAAA,UAAU,EAAE,SAtIN;AAuINC,MAAAA,WAAW,EAAE,MAvIP;AAwINC,MAAAA,KAAK,EAAE,IAxID;AAyINC,MAAAA,UAAU,EAAE,IAzIN;AA0INC,MAAAA,YAAY,EAAE,MA1IR;AA2INC,MAAAA,YAAY,EAAE,MA3IR;AA4IN;AACAC,MAAAA,gBAAgB,EAAE,MA7IZ;AA8INC,MAAAA,YAAY,EAAE,IA9IR;AA+INC,MAAAA,IAAI,EAAE,IA/IA;AAgJNC,MAAAA,QAAQ,EAAE,IAhJJ;AAiJNC,MAAAA,IAAI,EAAE,IAjJA;AAkJNC,MAAAA,KAAK,EAAE,IAlJD;AAmJNC,MAAAA,eAAe,EAAE,MAnJX;AAoJNC,MAAAA,qBAAqB,EAAE,QApJjB;AAqJNC,MAAAA,UAAU,EAAE,QArJN;AAsJNC,MAAAA,SAAS,EAAE,MAtJL;AAuJNC,MAAAA,KAAK,EAAE,IAvJD;AAwJNC,MAAAA,mBAAmB,EAAE,IAxJf;AAyJNC,MAAAA,WAAW,EAAE,OAzJP;AA0JNC,MAAAA,SAAS,EAAE,UA1JL;AA2JNC,MAAAA,QAAQ,EAAE,IA3JJ;AA4JNC,MAAAA,UAAU,EAAE,IA5JN;AA6JNC,MAAAA,eAAe,EAAE,QA7JX;AA8JNC,MAAAA,mBAAmB,EAAE,QA9Jf;AA+JN;AACAC,MAAAA,UAAU,EAAE,MAhKN;AAiKNC,MAAAA,eAAe,EAAE,MAjKX;AAkKNC,MAAAA,gBAAgB,EAAE,MAlKZ;AAmKNC,MAAAA,SAAS,EAAE,MAnKL;AAoKNC,MAAAA,SAAS,EAAE,MApKL;AAqKNC,MAAAA,SAAS,EAAE,MArKL;AAsKNC,MAAAA,SAAS,EAAE,MAtKL;AAuKNC,MAAAA,iBAAiB,EAAE,MAvKb;AAwKNC,MAAAA,iBAAiB,EAAE,QAxKb;AAyKNC,MAAAA,cAAc,EAAE,QAzKV;AA0KNC,MAAAA,cAAc,EAAE,MA1KV;AA2KNC,MAAAA,mBAAmB,EAAE,MA3Kf;AA4KNC,MAAAA,aAAa,EAAE,QA5KT;AA6KNC,MAAAA,WAAW,EAAE,QA7KP;AA8KNC,MAAAA,MAAM,EAAE,MA9KF;AA+KNC,MAAAA,KAAK,EAAE,OA/KD;AAgLNC,MAAAA,EAAE,EAAE,GAhLE;AAiLNC,MAAAA,cAAc,EAAE,MAjLV;AAkLNC,MAAAA,cAAc,EAAE,QAlLV;AAmLNC,MAAAA,UAAU,EAAE,QAnLN;AAoLNC,MAAAA,iBAAiB,EAAE,QApLb;AAqLNC,MAAAA,eAAe,EAAE,QArLX;AAsLNC,MAAAA,YAAY,EAAE,SAtLR;AAuLNC,MAAAA,SAAS,EAAE,MAvLL;AAwLNC,MAAAA,iBAAiB,EAAE,MAxLb;AAyLNC,MAAAA,aAAa,EAAE,MAzLT;AA0LNC,MAAAA,iBAAiB,EAAE,MA1Lb;AA2LNC,MAAAA,aAAa,EAAE,OA3LT;AA4LNC,MAAAA,YAAY,EAAE,QA5LR;AA6LNC,MAAAA,SAAS,EAAE,QA7LL;AA8LNC,MAAAA,gBAAgB,EAAE,MA9LZ;AA+LNC,MAAAA,YAAY,EAAE,QA/LR;AAgMNC,MAAAA,eAAe,EAAE,QAhMX;AAiMNC,MAAAA,MAAM,EAAE,KAjMF;AAkMNC,MAAAA,KAAK,EAAE,MAlMD;AAmMN;AACAC,MAAAA,SAAS,EAAE,MApML;AAqMNC,MAAAA,cAAc,EAAE,MArMV;AAsMNC,MAAAA,cAAc,EAAE,MAtMV;AAuMNC,MAAAA,kBAAkB,EAAE,MAvMd;AAwMNC,MAAAA,iBAAiB,EAAE,OAxMb;AAyMNC,MAAAA,sBAAsB,EAAE,QAzMlB;AA0MNC,MAAAA,WAAW,EAAE,QA1MP;AA2MNC,MAAAA,YAAY,EAAE,MA3MR;AA4MNC,MAAAA,QAAQ,EAAE,MA5MJ;AA6MNC,MAAAA,aAAa,EAAE,WA7MT;AA8MNC,MAAAA,gBAAgB,EAAE,QA9MZ;AA+MNC,MAAAA,aAAa,EAAE,UA/MT;AAgNNC,MAAAA,iBAAiB,EAAE,YAhNb;AAiNNC,MAAAA,cAAc,EAAE,UAjNV;AAkNNC,MAAAA,KAAK,EAAE,UAlND;AAmNNC,MAAAA,cAAc,EAAE,YAnNV;AAoNNC,MAAAA,QAAQ,EAAE,MApNJ;AAqNN;AACAC,MAAAA,IAAI,EAAE,IAtNA;AAuNNC,MAAAA,SAAS,EAAE,KAvNL;AAwNNC,MAAAA,eAAe,EAAE,KAxNX;AAyNNC,MAAAA,OAAO,EAAE,KAzNH;AA0NNC,MAAAA,SAAS,EAAE,IA1NL;AA2NNC,MAAAA,GAAG,EAAE,IA3NC;AA4NNC,MAAAA,SAAS,EAAE,MA5NL;AA6NNC,MAAAA,WAAW,EAAE,MA7NP;AA8NNC,MAAAA,KAAK,EAAE,IA9ND;AA+NNC,MAAAA,qBAAqB,EAAE,MA/NjB;AAgONC,MAAAA,qBAAqB,EAAE,MAhOjB;AAiONC,MAAAA,IAAI,EAAE,IAjOA;AAkONC,MAAAA,SAAS,EAAE,KAlOL;AAmONC,MAAAA,OAAO,EAAE,IAnOH;AAoONC,MAAAA,QAAQ,EAAE,KApOJ;AAqONC,MAAAA,KAAK,EAAE,IArOD;AAsONC,MAAAA,QAAQ,EAAE,MAtOJ;AAuONC,MAAAA,WAAW,EAAE,MAvOP;AAwONC,MAAAA,YAAY,EAAE,OAxOR;AAyONC,MAAAA,SAAS,EAAE,MAzOL;AA0ONC,MAAAA,aAAa,EAAE,OA1OT;AA2ONC,MAAAA,UAAU,EAAE,MA3ON;AA2Oc;AACpBC,MAAAA,UAAU,EAAE,IA5ON;AA6ONC,MAAAA,aAAa,EAAE,KA7OT;AA8ONC,MAAAA,OAAO,EAAE,IA9OH;AA+ONC,MAAAA,SAAS,EAAE,OA/OL;AAgPNC,MAAAA,mBAAmB,EAAE,MAhPf;AAiPNC,MAAAA,kBAAkB,EAAE,MAjPd;AAkPNC,MAAAA,SAAS,EAAE,KAlPL;AAmPNC,MAAAA,OAAO,EAAE,cAnPH;AAoPNC,MAAAA,QAAQ,EAAE,IApPJ;AAqPNC,MAAAA,aAAa,EAAE,MArPT;AAsPNC,MAAAA,SAAS,EAAE,OAtPL;AAuPNC,MAAAA,SAAS,EAAE,OAvPL;AAwPNC,MAAAA,cAAc,EAAE,YAxPV;AAyPNC,MAAAA,YAAY,EAAE,SAzPR;AA0PNC,MAAAA,kBAAkB,EAAE,YA1Pd;AA2PNC,MAAAA,WAAW,EAAE,OA3PP;AA4PNC,MAAAA,WAAW,EAAE,IA5PP;AA6PNC,MAAAA,WAAW,EAAE,IA7PP;AA8PNC,MAAAA,UAAU,EAAE,UA9PN;AA+PNC,MAAAA,mBAAmB,EAAE,WA/Pf;AAgQNC,MAAAA,cAAc,EAAE,OAhQV;AAiQNC,MAAAA,eAAe,EAAE,QAjQX;AAkQNC,MAAAA,MAAM,EAAE,MAlQF;AAmQNC,MAAAA,kBAAkB,EAAE,mBAnQd;AAoQNC,MAAAA,aAAa,EAAE,mBApQT;AAqQNC,MAAAA,WAAW,EAAE,OArQP;AAsQNC,MAAAA,UAAU,EAAE,OAtQN;AAuQNC,MAAAA,UAAU,EAAE,MAvQN;AAwQNC,MAAAA,GAAG,EAAE,GAxQC;AAyQNC,MAAAA,GAAG,EAAE,GAzQC;AA0QNC,MAAAA,IAAI,EAAE,IA1QA;AA2QNC,MAAAA,QAAQ,EAAE,IA3QJ;AA4QNC,MAAAA,OAAO,EAAE,IA5QH;AA6QNC,MAAAA,UAAU,EAAE,KA7QN;AA8QNC,MAAAA,aAAa,EAAE,OA9QT;AA+QNC,MAAAA,eAAe,EAAE,QA/QX;AAgRNC,MAAAA,gBAAgB,EAAE,OAhRZ;AAiRNC,MAAAA,gBAAgB,EAAE,SAjRZ;AAkRNC,MAAAA,UAAU,EAAE,KAlRN;AAmRNC,MAAAA,cAAc,EAAE,OAnRV;AAoRNC,MAAAA,WAAW,EAAE;AApRP,KAFJ;AAwRJ;AACAC,IAAAA,KAAK,EAAE;AACLC,MAAAA,OAAO,EAAE,IADJ;AAELC,MAAAA,QAAQ,EAAE,IAFL;AAGLC,MAAAA,aAAa,EAAE,MAHV;AAILH,MAAAA,KAAK,EAAE,IAJF;AAKLI,MAAAA,2BAA2B,EAAE,QALxB;AAMLC,MAAAA,4BAA4B,EAAE,QANzB;AAOLC,MAAAA,2BAA2B,EAAE;AAPxB,KAzRH;AAkSJ;AACAH,IAAAA,aAAa,EAAE;AACbA,MAAAA,aAAa,EAAE,MADF;AAEbI,MAAAA,0BAA0B,EAAE,WAFf;AAGbC,MAAAA,gBAAgB,EAAE,QAHL;AAIbC,MAAAA,qBAAqB,EAAE,UAJV;AAKbC,MAAAA,IAAI,EAAE,IALO;AAMbC,MAAAA,6BAA6B,EAAE,SANlB;AAObC,MAAAA,kCAAkC,EAAE,WAPvB;AAQbC,MAAAA,4BAA4B,EAAE,2BARjB;AASbC,MAAAA,eAAe,EAAE,cATJ;AAUbC,MAAAA,uBAAuB,EAAE,eAVZ;AAWbC,MAAAA,UAAU,EAAE,WAXC;AAYbC,MAAAA,UAAU,EAAE,SAZC;AAabC,MAAAA,yBAAyB,EAAE,UAbd;AAcbC,MAAAA,qBAAqB,EAAE,iBAdV;AAebC,MAAAA,yBAAyB,EAAE,WAfd;AAgBbC,MAAAA,UAAU,EAAE,WAhBC;AAiBbC,MAAAA,mBAAmB,EAAE,kBAjBR;AAkBbC,MAAAA,qBAAqB,EAAE;AAlBV,KAnSX;AAuTJpN,IAAAA,QAAQ,EAAE;AACR8L,MAAAA,OAAO,EAAE,IADD;AAERpI,MAAAA,IAAI,EAAE,IAFE;AAGR2J,MAAAA,KAAK,EAAE,MAHC;AAIRC,MAAAA,KAAK,EAAE,IAJC;AAKR1J,MAAAA,IAAI,EAAE,IALE;AAMR2J,MAAAA,2BAA2B,EAAE,sBANrB;AAORtB,MAAAA,2BAA2B,EAAE,QAPrB;AAQRuB,MAAAA,wBAAwB,EAAE,QARlB;AASRC,MAAAA,oBAAoB,EAAE,iBATd;AAURV,MAAAA,yBAAyB,EAAE,UAVnB;AAWRC,MAAAA,qBAAqB,EAAE,iBAXf;AAYRU,MAAAA,yBAAyB,EAAE,QAZnB;AAaRC,MAAAA,wBAAwB,EAAE,SAblB;AAcRC,MAAAA,4BAA4B,EAAE;AAdtB,KAvTN;AAuUJ;AACAC,IAAAA,SAAS,EAAE;AACTC,MAAAA,MAAM,EAAE,IADC;AAETC,MAAAA,UAAU,EAAE,IAFH;AAGTC,MAAAA,MAAM,EAAE,IAHC;AAITC,MAAAA,UAAU,EAAE,KAJH;AAKTC,MAAAA,eAAe,EAAE,OALR;AAMT3I,MAAAA,WAAW,EAAE,IANJ;AAOT+D,MAAAA,OAAO,EAAE,IAPA;AAQT5D,MAAAA,EAAE,EAAE,GARK;AASTyI,MAAAA,eAAe,EAAE,MATR;AAUTC,MAAAA,IAAI,EAAE,IAVG;AAWTC,MAAAA,aAAa,EAAE,SAXN;AAYTC,MAAAA,kBAAkB,EAAE,MAZX;AAaTC,MAAAA,MAAM,EAAE,IAbC;AAcTC,MAAAA,UAAU,EAAE,MAdH;AAeTC,MAAAA,UAAU,EAAE,MAfH;AAgBTC,MAAAA,OAAO,EAAE,IAhBA;AAiBTC,MAAAA,iBAAiB,EAAE,QAjBV;AAkBTC,MAAAA,gBAAgB,EAAE,OAlBT;AAmBTC,MAAAA,WAAW,EAAE,MAnBJ;AAoBTC,MAAAA,cAAc,EAAE,MApBP;AAqBTC,MAAAA,eAAe,EAAE,MArBR;AAsBTC,MAAAA,QAAQ,EAAE,MAtBD;AAuBTC,MAAAA,cAAc,EAAE,MAvBP;AAwBTC,MAAAA,SAAS,EAAE,SAxBF;AAyBTC,MAAAA,aAAa,EAAE,MAzBN;AA0BTC,MAAAA,UAAU,EAAE,MA1BH;AA2BTC,MAAAA,eAAe,EAAE,MA3BR;AA4BTC,MAAAA,kBAAkB,EAAE,WA5BX;AA6BTC,MAAAA,uBAAuB,EAAE,WA7BhB;AA8BTC,MAAAA,uBAAuB,EAAE,qBA9BhB;AA+BTC,MAAAA,kBAAkB,EAAE,MA/BX;AAgCTC,MAAAA,WAAW,EAAE,MAhCJ;AAiCTC,MAAAA,YAAY,EAAE,MAjCL;AAkCTC,MAAAA,SAAS,EAAE,QAlCF;AAmCTC,MAAAA,sBAAsB,EAAE;AAnCf,KAxUP;AA6WJ;AACAnL,IAAAA,UAAU,EAAE;AACVoL,MAAAA,aAAa,EAAE,MADL;AAEVC,MAAAA,QAAQ,EAAE,MAFA;AAGVC,MAAAA,8BAA8B,EAAE,aAHtB;AAIVC,MAAAA,gBAAgB,EAAE,QAJR;AAKVC,MAAAA,wBAAwB,EAAE,UALhB;AAMVC,MAAAA,0BAA0B,EAAE,SANlB;AAOVC,MAAAA,kBAAkB,EAAE,cAPV;AAQVC,MAAAA,qBAAqB,EAAE,oBARb;AASVC,MAAAA,YAAY,EAAE,MATJ;AAUVC,MAAAA,eAAe,EAAE,MAVP;AAWVC,MAAAA,0BAA0B,EAAE,UAXlB;AAYVC,MAAAA,wBAAwB,EAAE,UAZhB;AAaVC,MAAAA,sCAAsC,EAAE,aAb9B;AAcVC,MAAAA,kBAAkB,EAAE,UAdV;AAeVC,MAAAA,yBAAyB,EAAE,aAfjB;AAgBVC,MAAAA,sBAAsB,EAAE,4BAhBd;AAiBVC,MAAAA,aAAa,EAAE,SAjBL;AAkBVC,MAAAA,OAAO,EAAE,KAlBC;AAmBVC,MAAAA,QAAQ,EAAE,IAnBA;AAoBVC,MAAAA,aAAa,EAAE,OApBL;AAqBVC,MAAAA,MAAM,EAAE,MArBE;AAsBVC,MAAAA,UAAU,EAAE,OAtBF;AAuBVC,MAAAA,SAAS,EAAE,IAvBD;AAwBVC,MAAAA,QAAQ,EAAE;AAxBA,KA9WR;AAwYJ;AACAnM,IAAAA,iBAAiB,EAAE;AACjB4G,MAAAA,OAAO,EAAE,IADQ;AAEjBpI,MAAAA,IAAI,EAAE,IAFW;AAGjB2J,MAAAA,KAAK,EAAE,MAHU;AAIjBC,MAAAA,KAAK,EAAE,IAJU;AAKjB1J,MAAAA,IAAI,EAAE,IALW;AAMjBI,MAAAA,UAAU,EAAE,QANK;AAOjBsN,MAAAA,UAAU,EAAE,IAPK;AAQjBC,MAAAA,mBAAmB,EAAE,YARJ;AASjBnE,MAAAA,qBAAqB,EAAE,mBATN;AAUjBoE,MAAAA,gBAAgB,EAAE,QAVD;AAWjBC,MAAAA,YAAY,EAAE,SAXG;AAYjBC,MAAAA,aAAa,EAAE,SAZE;AAajB3H,MAAAA,SAAS,EAAE,SAbM;AAcjB8C,MAAAA,UAAU,EAAE,WAdK;AAejB8E,MAAAA,UAAU,EAAE,SAfK;AAgBjBC,MAAAA,UAAU,EAAE,WAhBK;AAiBjB3F,MAAAA,2BAA2B,EAAE,QAjBZ;AAkBjB4F,MAAAA,uBAAuB,EAAE,iBAlBR;AAmBjBC,MAAAA,6BAA6B,EAAE,kBAnBd;AAoBjB5F,MAAAA,4BAA4B,EAAE,QApBb;AAqBjBC,MAAAA,2BAA2B,EAAE,SArBZ;AAsBjBqB,MAAAA,wBAAwB,EAAE,QAtBT;AAuBjBC,MAAAA,oBAAoB,EAAE,iBAvBL;AAwBjBV,MAAAA,yBAAyB,EAAE,UAxBV;AAyBjBC,MAAAA,qBAAqB,EAAE,iBAzBN;AA0BjBO,MAAAA,2BAA2B,EAAE,sBA1BZ;AA2BjBG,MAAAA,yBAAyB,EAAE,QA3BV;AA4BjBC,MAAAA,wBAAwB,EAAE,SA5BT;AA6BjBC,MAAAA,4BAA4B,EAAE,UA7Bb;AA8BjBmE,MAAAA,WAAW,EAAE,MA9BI;AA+BjBC,MAAAA,sBAAsB,EAAE,QA/BP;AAgCjBC,MAAAA,0BAA0B,EAAE,QAhCX;AAiCjBC,MAAAA,cAAc,EAAE,QAjCC;AAkCjBC,MAAAA,cAAc,EAAE,MAlCC;AAmCjBrK,MAAAA,IAAI,EAAE,MAnCW;AAoCjBsK,MAAAA,KAAK,EAAE,MApCU;AAqCjBC,MAAAA,KAAK,EAAE,MArCU;AAsCjBC,MAAAA,aAAa,EAAE,MAtCE;AAuCjBrE,MAAAA,UAAU,EAAE,OAvCK;AAwCjBsE,MAAAA,eAAe,EAAE,QAxCA;AAyCjBlL,MAAAA,QAAQ,EAAE,MAzCO;AA0CjBF,MAAAA,WAAW,EAAE,QA1CI;AA2CjBC,MAAAA,YAAY,EAAE,MA3CG;AA4CjBG,MAAAA,gBAAgB,EAAE,QA5CD;AA6CjBiL,MAAAA,+BAA+B,EAAE,iBA7ChB;AA8CjBC,MAAAA,oBAAoB,EAAE,OA9CL;AA+CjBC,MAAAA,gBAAgB,EAAE,OA/CD;AAgDjBC,MAAAA,iBAAiB,EAAE,OAhDF;AAiDjBC,MAAAA,kBAAkB,EAAE,OAjDH;AAkDjBC,MAAAA,4BAA4B,EAAE,QAlDb;AAmDjBC,MAAAA,wBAAwB,EAAE,QAnDT;AAoDjBC,MAAAA,yBAAyB,EAAE,QApDV;AAqDjBC,MAAAA,0BAA0B,EAAE,QArDX;AAsDjBC,MAAAA,iBAAiB,EAAE,QAtDF;AAuDjBC,MAAAA,+BAA+B,EAAE,aAvDhB;AAwDjBC,MAAAA,mBAAmB,EAAE;AAxDJ,KAzYf;AAmcJ;AACAhO,IAAAA,cAAc,EAAE;AACdiO,MAAAA,QAAQ,EAAE,MADI;AAEdC,MAAAA,QAAQ,EAAE,MAFI;AAGdC,MAAAA,cAAc,EAAE,QAHF;AAIdC,MAAAA,cAAc,EAAE,QAJF;AAKdC,MAAAA,iBAAiB,EAAE,cALL;AAMdC,MAAAA,gBAAgB,EAAE,qBANJ;AAOdC,MAAAA,aAAa,EAAE,sBAPD;AAQdC,MAAAA,IAAI,EAAE,IARQ;AASdC,MAAAA,OAAO,EAAE,QATK;AAUdC,MAAAA,aAAa,EAAE,WAVD;AAWdC,MAAAA,aAAa,EAAE,WAXD;AAYdC,MAAAA,iBAAiB,EAAE,MAZL;AAadC,MAAAA,gBAAgB,EAAE,wBAbJ;AAcdC,MAAAA,gBAAgB,EAAE,wBAdJ;AAedC,MAAAA,sBAAsB,EAAE,cAfV;AAgBdC,MAAAA,cAAc,EAAE,qBAhBF;AAiBdC,MAAAA,kBAAkB,EAAE;AAjBN,KApcZ;AAudJ;AACAxO,IAAAA,cAAc,EAAE;AACdjC,MAAAA,QAAQ,EAAE,IADI;AAEdC,MAAAA,IAAI,EAAE,IAFQ;AAGdC,MAAAA,KAAK,EAAE,IAHO;AAIdwQ,MAAAA,YAAY,EAAE,MAJA;AAKdC,MAAAA,YAAY,EAAE,MALA;AAMdC,MAAAA,IAAI,EAAE,IANQ;AAOdnB,MAAAA,QAAQ,EAAE,MAPI;AAQdC,MAAAA,QAAQ,EAAE,MARI;AASdmB,MAAAA,SAAS,EAAE,MATG;AAUdC,MAAAA,SAAS,EAAE,MAVG;AAWdzQ,MAAAA,UAAU,EAAE,QAXE;AAYd0Q,MAAAA,0BAA0B,EAAE,WAZd;AAadC,MAAAA,sBAAsB,EAAE,WAbV;AAcdC,MAAAA,uBAAuB,EAAE,WAdX;AAedC,MAAAA,8BAA8B,EAAE,mBAflB;AAgBdC,MAAAA,8BAA8B,EAAE,mBAhBlB;AAiBdC,MAAAA,+BAA+B,EAAE,mBAjBnB;AAkBdC,MAAAA,2BAA2B,EAAE,sBAlBf;AAmBdC,MAAAA,0BAA0B,EAAE,sBAnBd;AAoBdC,MAAAA,gBAAgB,EAAE,wBApBJ;AAqBdC,MAAAA,oBAAoB,EAAE,QArBR;AAsBdC,MAAAA,gBAAgB,EAAE,OAtBJ;AAuBdC,MAAAA,6BAA6B,EAAE,WAvBjB;AAwBdvL,MAAAA,SAAS,EAAE,SAxBG;AAyBdC,MAAAA,SAAS,EAAE,SAzBG;AA0BduL,MAAAA,qCAAqC,EAAE,UA1BzB;AA2BdC,MAAAA,wBAAwB,EAAE,QA3BZ;AA4BdC,MAAAA,oBAAoB,EAAE,iBA5BR;AA6BdhI,MAAAA,wBAAwB,EAAE,QA7BZ;AA8BdC,MAAAA,oBAAoB,EAAE,iBA9BR;AA+BdgI,MAAAA,wBAAwB,EAAE,QA/BZ;AAgCdC,MAAAA,4BAA4B,EAAE,kBAhChB;AAiCdC,MAAAA,IAAI,EAAE,IAjCQ;AAkCdjS,MAAAA,IAAI,EAAE,IAlCQ;AAmCdkS,MAAAA,oBAAoB,EAAE,YAnCR;AAoCdC,MAAAA,6BAA6B,EAAE,aApCjB;AAqCdC,MAAAA,WAAW,EAAE,QArCC;AAsCdC,MAAAA,gBAAgB,EAAE,aAtCJ;AAuCdC,MAAAA,eAAe,EAAE,cAvCH;AAwCdC,MAAAA,kBAAkB,EAAE,eAxCN;AAyCdC,MAAAA,uBAAuB,EAAE,UAzCX;AA0CdC,MAAAA,8BAA8B,EAAE,YA1ClB;AA2CdC,MAAAA,kBAAkB,EAAE,SA3CN;AA4CdC,MAAAA,gBAAgB,EAAE,OA5CJ;AA6CdC,MAAAA,SAAS,EAAE,QA7CG;AA8CdC,MAAAA,qBAAqB,EAAE;AA9CT,KAxdZ;AAwgBJ;AACAhR,IAAAA,WAAW,EAAE;AACXiR,MAAAA,oBAAoB,EAAE,MADX;AAEXC,MAAAA,GAAG,EAAE,MAFM;AAGX/S,MAAAA,IAAI,EAAE,MAHK;AAIXgT,MAAAA,EAAE,EAAE,MAJO;AAKXnN,MAAAA,SAAS,EAAE,MALA;AAMXoN,MAAAA,aAAa,EAAE,MANJ;AAOXC,MAAAA,UAAU,EAAE,OAPD;AAQXC,MAAAA,OAAO,EAAE,MARE;AASX7S,MAAAA,UAAU,EAAE,QATD;AAUX8S,MAAAA,oBAAoB,EAAE,SAVX;AAWXT,MAAAA,gBAAgB,EAAE,OAXP;AAYXC,MAAAA,SAAS,EAAE,OAZA;AAaXhR,MAAAA,aAAa,EAAE,MAbJ;AAcXyR,MAAAA,sBAAsB,EAAE,qBAdb;AAeXC,MAAAA,2BAA2B,EAAE,oBAflB;AAgBXC,MAAAA,wBAAwB,EAAE,WAhBf;AAiBXC,MAAAA,WAAW,EAAE,KAjBF;AAkBXC,MAAAA,WAAW,EAAE,KAlBF;AAmBXC,MAAAA,QAAQ,EAAE,MAnBC;AAoBXC,MAAAA,QAAQ,EAAE,SApBC;AAqBXtN,MAAAA,SAAS,EAAE,OArBA;AAsBXuN,MAAAA,YAAY,EAAE,UAtBH;AAuBXC,MAAAA,kBAAkB,EAAE,QAvBT;AAwBXC,MAAAA,gBAAgB,EAAE,MAxBP;AAyBXC,MAAAA,aAAa,EAAE,SAzBJ;AA0BXC,MAAAA,qBAAqB,EAAE,UA1BZ;AA2BXC,MAAAA,cAAc,EAAE,MA3BL;AA4BXC,MAAAA,mCAAmC,EAAE,cA5B1B;AA6BXC,MAAAA,kBAAkB,EAAE,iBA7BT;AA8BXC,MAAAA,eAAe,EAAE,cA9BN;AA+BXC,MAAAA,SAAS,EAAE,OA/BA;AAgCXC,MAAAA,WAAW,EAAE,MAhCF;AAiCXC,MAAAA,QAAQ,EAAE,OAjCC;AAkCXC,MAAAA,QAAQ,EAAE,OAlCC;AAmCXC,MAAAA,UAAU,EAAE;AAnCD,KAzgBT;AA8iBJ;AACArS,IAAAA,iBAAiB,EAAE;AACjBsS,MAAAA,kBAAkB,EAAE,kBADH;AAEjBC,MAAAA,SAAS,EAAE,OAFM;AAGjB7J,MAAAA,UAAU,EAAE,MAHK;AAIjB8J,MAAAA,cAAc,EAAE,MAJC;AAKjB7J,MAAAA,UAAU,EAAE,MALK;AAMjBK,MAAAA,cAAc,EAAE,MANC;AAOjByJ,MAAAA,YAAY,EAAE,MAPG;AAQjBC,MAAAA,eAAe,EAAE,MARA;AASjBC,MAAAA,IAAI,EAAE,SATW;AAUjBC,MAAAA,IAAI,EAAE,QAVW;AAWjBC,MAAAA,WAAW,EAAE,IAXI;AAYjBC,MAAAA,QAAQ,EAAE,IAZO;AAajBC,MAAAA,WAAW,EAAE,aAbI;AAcjBC,MAAAA,WAAW,EAAE,YAdI;AAejBC,MAAAA,kBAAkB,EAAE,QAfH;AAgBjBC,MAAAA,eAAe,EAAE,QAhBA;AAiBjBC,MAAAA,SAAS,EAAE,IAjBM;AAkBjBC,MAAAA,mBAAmB,EAAE,QAlBJ;AAmBjBC,MAAAA,GAAG,EAAE,KAnBY;AAoBjBC,MAAAA,aAAa,EAAE,SApBE;AAqBjBC,MAAAA,GAAG,EAAE,KArBY;AAsBjBC,MAAAA,aAAa,EAAE,SAtBE;AAuBjBvK,MAAAA,eAAe,EAAE,MAvBA;AAwBjBJ,MAAAA,iBAAiB,EAAE,QAxBF;AAyBjB4K,MAAAA,eAAe,EAAE,MAzBA;AA0BjBvV,MAAAA,UAAU,EAAE,QA1BK;AA2BjBwV,MAAAA,0BAA0B,EAAE,mBA3BX;AA4BjBC,MAAAA,wBAAwB,EAAE,YA5BT;AA6BjBC,MAAAA,0BAA0B,EAAE,cA7BX;AA8BjBC,MAAAA,iCAAiC,EAAE,cA9BlB;AA+BjBnK,MAAAA,uBAAuB,EAAE,qBA/BR;AAgCjBF,MAAAA,kBAAkB,EAAE,WAhCH;AAiCjBC,MAAAA,uBAAuB,EAAE,WAjCR;AAkCjBqK,MAAAA,oBAAoB,EAAE,QAlCL;AAmCjBC,MAAAA,cAAc,EAAE,YAnCC;AAoCjBC,MAAAA,eAAe,EAAE,WApCA;AAqCjBC,MAAAA,gBAAgB,EAAE,SArCD;AAsCjBC,MAAAA,uBAAuB,EAAE,WAtCR;AAuCjBC,MAAAA,mBAAmB,EAAE,oBAvCJ;AAwCjBzM,MAAAA,wBAAwB,EAAE,UAxCT;AAyCjBC,MAAAA,oBAAoB,EAAE,mBAzCL;AA0CjByM,MAAAA,0BAA0B,EAAE,mBA1CX;AA2CjBC,MAAAA,8BAA8B,EAAE,uBA3Cf;AA4CjBC,MAAAA,2BAA2B,EAAE,wBA5CZ;AA6CjBxE,MAAAA,oBAAoB,EAAE,YA7CL;AA8CjBC,MAAAA,6BAA6B,EAAE,aA9Cd;AA+CjBC,MAAAA,WAAW,EAAE,QA/CI;AAgDjBC,MAAAA,gBAAgB,EAAE,aAhDD;AAiDjBC,MAAAA,eAAe,EAAE,cAjDA;AAkDjBC,MAAAA,kBAAkB,EAAE,eAlDH;AAmDjBC,MAAAA,uBAAuB,EAAE,UAnDR;AAoDjBE,MAAAA,kBAAkB,EAAE,SApDH;AAqDjBC,MAAAA,gBAAgB,EAAE,OArDD;AAsDjBC,MAAAA,SAAS,EAAE,QAtDM;AAuDjB+D,MAAAA,iBAAiB,EAAE;AAvDF,KA/iBf;AAwmBJ;AACA7U,IAAAA,MAAM,EAAE;AACN8U,MAAAA,mBAAmB,EAAE,MADf;AAENC,MAAAA,SAAS,EAAE,OAFL;AAGNC,MAAAA,UAAU,EAAE,MAHN;AAINC,MAAAA,cAAc,EAAE,UAJV;AAKNC,MAAAA,YAAY,EAAE,UALR;AAMNC,MAAAA,SAAS,EAAE,MANL;AAONC,MAAAA,iCAAiC,EAAE,eAP7B;AAQNC,MAAAA,oBAAoB,EAAE;AARhB,KAzmBJ;AAmnBJ;AACApV,IAAAA,KAAK,EAAE;AACLqV,MAAAA,kBAAkB,EAAE,SADf;AAELnB,MAAAA,iCAAiC,EAAE,cAF9B;AAGLD,MAAAA,0BAA0B,EAAE,cAHvB;AAIL/D,MAAAA,IAAI,EAAE,IAJD;AAKLzG,MAAAA,SAAS,EAAE,SALN;AAMLT,MAAAA,UAAU,EAAE,MANP;AAOLsM,MAAAA,MAAM,EAAE,IAPH;AAQLC,MAAAA,UAAU,EAAE,MARP;AASLC,MAAAA,aAAa,EAAE,OATV;AAULlM,MAAAA,eAAe,EAAE,MAVZ;AAWLmM,MAAAA,gBAAgB,EAAE,MAXb;AAYLlX,MAAAA,UAAU,EAAE,QAZP;AAaLqD,MAAAA,QAAQ,EAAE,MAbL;AAcLF,MAAAA,WAAW,EAAE,MAdR;AAeLC,MAAAA,YAAY,EAAE,MAfT;AAgBL+T,MAAAA,MAAM,EAAE,IAhBH;AAiBLC,MAAAA,KAAK,EAAE,IAjBF;AAkBLpN,MAAAA,MAAM,EAAE,IAlBH;AAmBL1E,MAAAA,OAAO,EAAE,IAnBJ;AAoBL+R,MAAAA,aAAa,EAAE,KApBV;AAqBLC,MAAAA,YAAY,EAAE,KArBT;AAsBLC,MAAAA,yBAAyB,EAAE,gBAtBtB;AAuBLC,MAAAA,yBAAyB,EAAE,sBAvBtB;AAwBLC,MAAAA,uBAAuB,EAAE,eAxBpB;AAyBLC,MAAAA,oBAAoB,EAAE,QAzBjB;AA0BL3e,MAAAA,MAAM,EAAE,IA1BH;AA2BLga,MAAAA,sBAAsB,EAAE,qBA3BnB;AA4BLlW,MAAAA,6BAA6B,EAAE,qBA5B1B;AA6BL8a,MAAAA,8BAA8B,EAAE,YA7B3B;AA8BL/F,MAAAA,oBAAoB,EAAE,YA9BjB;AA+BLC,MAAAA,6BAA6B,EAAE,aA/B1B;AAgCLC,MAAAA,WAAW,EAAE,QAhCR;AAiCLC,MAAAA,gBAAgB,EAAE,aAjCb;AAkCLC,MAAAA,eAAe,EAAE,cAlCZ;AAmCLC,MAAAA,kBAAkB,EAAE,eAnCf;AAoCLC,MAAAA,uBAAuB,EAAE,UApCpB;AAqCLE,MAAAA,kBAAkB,EAAE,SArCf;AAsCLC,MAAAA,gBAAgB,EAAE,OAtCb;AAuCLC,MAAAA,SAAS,EAAE,QAvCN;AAwCLsF,MAAAA,QAAQ,EAAE,SAxCL;AAyCLC,MAAAA,gBAAgB,EAAE,MAzCb;AA0CL/R,MAAAA,SAAS,EAAE,SA1CN;AA2CLC,MAAAA,SAAS,EAAE,SA3CN;AA4CL+R,MAAAA,sBAAsB,EAAE,SA5CnB;AA6CLlJ,MAAAA,kBAAkB,EAAE,OA7Cf;AA8CLmJ,MAAAA,sBAAsB,EAAE,SA9CnB;AA+CLC,MAAAA,QAAQ,EAAE,MA/CL;AAgDLC,MAAAA,QAAQ,EAAE,UAhDL;AAiDLC,MAAAA,QAAQ,EAAE,OAjDL;AAkDLC,MAAAA,SAAS,EAAE,OAlDN;AAmDLC,MAAAA,aAAa,EAAE,UAnDV;AAoDLC,MAAAA,aAAa,EAAE,WApDV;AAqDLC,MAAAA,cAAc,EAAE,QArDX;AAsDLC,MAAAA,aAAa,EAAE,QAtDV;AAuDLC,MAAAA,YAAY,EAAE,aAvDT;AAwDLC,MAAAA,SAAS,EAAE,MAxDN;AAyDLC,MAAAA,wBAAwB,EAAE,cAzDrB;AA0DLC,MAAAA,qBAAqB,EAAE,gBA1DlB;AA2DLC,MAAAA,uBAAuB,EAAE,mBA3DpB;AA4DLC,MAAAA,kBAAkB,EAAE,cA5Df;AA6DLC,MAAAA,cAAc,EAAE,aA7DX;AA8DLvH,MAAAA,wBAAwB,EAAE,QA9DrB;AA+DLC,MAAAA,oBAAoB,EAAE,iBA/DjB;AAgELhI,MAAAA,wBAAwB,EAAE,QAhErB;AAiELC,MAAAA,oBAAoB,EAAE,iBAjEjB;AAkELsP,MAAAA,4BAA4B,EAAE,UAlEzB;AAmELC,MAAAA,4BAA4B,EAAE,cAnEzB;AAoELC,MAAAA,4BAA4B,EAAE,SApEzB;AAqELC,MAAAA,6BAA6B,EAAE,SArE1B;AAsELC,MAAAA,iCAAiC,EAAE,WAtE9B;AAuELC,MAAAA,iCAAiC,EAAE,YAvE9B;AAwELC,MAAAA,kCAAkC,EAAE,UAxE/B;AAyELC,MAAAA,iCAAiC,EAAE,UAzE9B;AA0ELC,MAAAA,gCAAgC,EAAE,cA1E7B;AA2ELC,MAAAA,wBAAwB,EAAE,eA3ErB;AA4ELC,MAAAA,wBAAwB,EAAE,oBA5ErB;AA6ELC,MAAAA,wBAAwB,EAAE,cA7ErB;AA8ELC,MAAAA,yBAAyB,EAAE,cA9EtB;AA+ELC,MAAAA,6BAA6B,EAAE,gBA/E1B;AAgFLC,MAAAA,6BAA6B,EAAE,oBAhF1B;AAiFLC,MAAAA,8BAA8B,EAAE,eAjF3B;AAkFLC,MAAAA,6BAA6B,EAAE,eAlF1B;AAmFLC,MAAAA,4BAA4B,EAAE,sBAnFzB;AAoFL9D,MAAAA,0BAA0B,EAAE,mBApFvB;AAqFL+D,MAAAA,6BAA6B,EAAE,uBArF1B;AAsFLC,MAAAA,2BAA2B,EAAE,kBAtFxB;AAuFLC,MAAAA,gBAAgB,EAAE,WAvFb;AAwFLC,MAAAA,yBAAyB,EAAE,YAxFtB;AAyFLC,MAAAA,mBAAmB,EAAE,yBAzFhB;AA0FLC,MAAAA,yBAAyB,EAAE,uBA1FtB;AA2FLC,MAAAA,WAAW,EAAE,KA3FR;AA4FLC,MAAAA,WAAW,EAAE,KA5FR;AA6FLC,MAAAA,0CAA0C,EAAE,uBA7FvC;AA8FLC,MAAAA,iBAAiB,EAAE,SA9Fd;AA+FLC,MAAAA,yBAAyB,EAAE,mBA/FtB;AAgGLC,MAAAA,aAAa,EAAE;AAhGV,KApnBH;AAstBJ;AACA/Y,IAAAA,UAAU,EAAE;AACVJ,MAAAA,KAAK,EAAE,SADG;AAEVoZ,MAAAA,cAAc,EAAE,aAFN;AAGVC,MAAAA,kBAAkB,EAAE,WAHV;AAIVC,MAAAA,kBAAkB,EAAE,WAJV;AAKVC,MAAAA,mBAAmB,EAAE,WALX;AAMVC,MAAAA,cAAc,EAAE,aANN;AAOVC,MAAAA,aAAa,EAAE,gBAPL;AAQVC,MAAAA,kBAAkB,EAAE,oBARV;AASVC,MAAAA,iBAAiB,EAAE;AATT,KAvtBR;AAkuBJ;AACA9Z,IAAAA,aAAa,EAAE;AACb+Z,MAAAA,cAAc,EAAE,MADH;AAEbC,MAAAA,iBAAiB,EAAE,QAFN;AAGbC,MAAAA,aAAa,EAAE,QAHF;AAIbC,MAAAA,gBAAgB,EAAE,UAJL;AAKbC,MAAAA,iBAAiB,EAAE,aALN;AAMbC,MAAAA,gBAAgB,EAAE,WANL;AAObC,MAAAA,iBAAiB,EAAE,YAPN;AAQbC,MAAAA,cAAc,EAAE,WARH;AASbC,MAAAA,eAAe,EAAE,WATJ;AAUbC,MAAAA,QAAQ,EAAE,OAVG;AAWbC,MAAAA,QAAQ,EAAE,OAXG;AAYbC,MAAAA,aAAa,EAAE,WAZF;AAabC,MAAAA,aAAa,EAAE,WAbF;AAcbC,MAAAA,sBAAsB,EAAE,UAdX;AAebC,MAAAA,mBAAmB,EAAE,iCAfR;AAgBbC,MAAAA,qBAAqB,EAAE,oBAhBV;AAiBbC,MAAAA,mBAAmB,EAAE,sBAjBR;AAkBbC,MAAAA,mBAAmB,EAAE,oBAlBR;AAmBbC,MAAAA,eAAe,EAAE,oBAnBJ;AAoBbrL,MAAAA,gBAAgB,EAAE,uBApBL;AAqBbsL,MAAAA,wBAAwB,EAAE,QArBb;AAsBbC,MAAAA,6BAA6B,EAAE,YAtBlB;AAuBbC,MAAAA,0BAA0B,EAAE,yBAvBf;AAwBbC,MAAAA,eAAe,EAAE,6BAxBJ;AAyBbC,MAAAA,kBAAkB,EAAE,kCAzBP;AA0BbC,MAAAA,8BAA8B,EAAE,oBA1BnB;AA2BbC,MAAAA,QAAQ,EAAE,YA3BG;AA4BbC,MAAAA,cAAc,EAAE,uBA5BH;AA6BbC,MAAAA,mCAAmC,EAAE,WA7BxB;AA8BbC,MAAAA,oCAAoC,EAAE,YA9BzB;AA+BbC,MAAAA,iCAAiC,EAAE,WA/BtB;AAgCbC,MAAAA,qCAAqC,EAAE,WAhC1B;AAiCbC,MAAAA,oBAAoB,EAAE,UAjCT;AAkCbC,MAAAA,gBAAgB,EAAE,aAlCL;AAmCbC,MAAAA,sBAAsB,EAAE,SAnCX;AAoCbC,MAAAA,oBAAoB,EAAE,eApCT;AAqCbC,MAAAA,sBAAsB,EAAE,UArCX;AAsCbC,MAAAA,kBAAkB,EAAE,eAtCP;AAuCbC,MAAAA,wBAAwB,EAAE,uBAvCb;AAwCbC,MAAAA,UAAU,EAAE,OAxCC;AAyCbC,MAAAA,kBAAkB,EAAE,qBAzCP;AA0CbC,MAAAA,cAAc,EAAE,UA1CH;AA2CbC,MAAAA,oBAAoB,EAAE,uBA3CT;AA4CbC,MAAAA,aAAa,EAAE,mBA5CF;AA6CbC,MAAAA,uBAAuB,EAAE,WA7CZ;AA8CbC,MAAAA,gBAAgB,EAAE;AA9CL,KAnuBX;AAmxBJ;AACAlc,IAAAA,eAAe,EAAE;AACfmc,MAAAA,WAAW,EAAE,MADE;AAEfC,MAAAA,UAAU,EAAE,MAFG;AAGfC,MAAAA,SAAS,EAAE,MAHI;AAIfC,MAAAA,aAAa,EAAE,OAJA;AAKfC,MAAAA,kBAAkB,EAAE,QALL;AAMfC,MAAAA,cAAc,EAAE,SAND;AAOfC,MAAAA,cAAc,EAAE,WAPD;AAQfC,MAAAA,cAAc,EAAE,WARD;AASfC,MAAAA,oBAAoB,EAAE,SATP;AAUfC,MAAAA,mBAAmB,EAAE,WAVN;AAWfC,MAAAA,yBAAyB,EAAE,SAXZ;AAYf/X,MAAAA,WAAW,EAAE,SAZE;AAafgY,MAAAA,UAAU,EAAE,OAbG;AAcfC,MAAAA,eAAe,EAAE,SAdF;AAefC,MAAAA,aAAa,EAAE,SAfA;AAgBfC,MAAAA,iBAAiB,EAAE,UAhBJ;AAiBfC,MAAAA,iBAAiB,EAAE,UAjBJ;AAkBfC,MAAAA,kBAAkB,EAAE,UAlBL;AAmBfC,MAAAA,qBAAqB,EAAE,UAnBR;AAoBfC,MAAAA,qBAAqB,EAAE,UApBR;AAqBfC,MAAAA,sBAAsB,EAAE,UArBT;AAsBfC,MAAAA,kBAAkB,EAAE,SAtBL;AAuBfC,MAAAA,kBAAkB,EAAE,SAvBL;AAwBfC,MAAAA,mBAAmB,EAAE,SAxBN;AAyBfC,MAAAA,qBAAqB,EAAE,SAzBR;AA0BfC,MAAAA,gBAAgB,EAAE,SA1BH;AA2BfC,MAAAA,iBAAiB,EAAE,YA3BJ;AA4BfC,MAAAA,SAAS,EAAE,QA5BI;AA6BfC,MAAAA,KAAK,EAAE,IA7BQ;AA8BfC,MAAAA,IAAI,EAAE,IA9BS;AA+BfC,MAAAA,QAAQ,EAAE,MA/BK;AAgCfC,MAAAA,iBAAiB,EAAE,MAhCJ;AAiCfC,MAAAA,QAAQ,EAAE,SAjCK;AAkCfC,MAAAA,SAAS,EAAE,YAlCI;AAmCfC,MAAAA,SAAS,EAAE,QAnCI;AAoCfC,MAAAA,OAAO,EAAE,GApCM;AAqCf7c,MAAAA,gBAAgB,EAAE,UArCH;AAsCf8c,MAAAA,oBAAoB,EAAE,IAtCP;AAuCfC,MAAAA,EAAE,EAAE,GAvCW;AAwCfC,MAAAA,GAAG,EAAE,IAxCU;AAyCfC,MAAAA,EAAE,EAAE,GAzCW;AA0CfC,MAAAA,GAAG,EAAE,IA1CU;AA2CfC,MAAAA,oBAAoB,EAAE,IA3CP;AA4CfC,MAAAA,kBAAkB,EAAE,IA5CL;AA6CfC,MAAAA,WAAW,EAAE,SA7CE;AA8CfC,MAAAA,SAAS,EAAE,OA9CI;AA+CfC,MAAAA,oBAAoB,EAAE,MA/CP;AAgDfC,MAAAA,mBAAmB,EAAE,GAhDN;AAiDfC,MAAAA,sBAAsB,EAAE,IAjDT;AAkDfC,MAAAA,yBAAyB,EAAE,GAlDZ;AAmDfC,MAAAA,iBAAiB,EAAE,0BAnDJ;AAoDfC,MAAAA,gBAAgB,EAAE,UApDH;AAqDfC,MAAAA,eAAe,EAAE,mBArDF;AAsDfC,MAAAA,mBAAmB,EAAE,mBAtDN;AAuDfC,MAAAA,sBAAsB,EAAE,UAvDT;AAwDfC,MAAAA,iBAAiB,EAAE,sBAxDJ;AAyDfC,MAAAA,sBAAsB,EAAE,UAzDT;AA0DfC,MAAAA,iBAAiB,EAAE,sBA1DJ;AA2DfC,MAAAA,gBAAgB,EAAE,QA3DH;AA4DfC,MAAAA,sBAAsB,EAAE,UA5DT;AA6DfC,MAAAA,wBAAwB,EAAE,iBA7DX;AA8DfC,MAAAA,oBAAoB,EAAE,iBA9DP;AA+DfC,MAAAA,WAAW,EAAE,uBA/DE;AAgEfC,MAAAA,WAAW,EAAE,wBAhEE;AAiEfC,MAAAA,yBAAyB,EAAE,sBAjEZ;AAkEfC,MAAAA,yBAAyB,EAAE,sBAlEZ;AAmEfC,MAAAA,0BAA0B,EAAE,sBAnEb;AAoEfC,MAAAA,6BAA6B,EAAE,sBApEhB;AAqEfC,MAAAA,6BAA6B,EAAE,sBArEhB;AAsEfC,MAAAA,8BAA8B,EAAE,sBAtEjB;AAuEfC,MAAAA,iBAAiB,EAAE,qBAvEJ;AAwEfC,MAAAA,0BAA0B,EAAE,qBAxEb;AAyEfC,MAAAA,0BAA0B,EAAE,qBAzEb;AA0EfC,MAAAA,2BAA2B,EAAE,qBA1Ed;AA2EfC,MAAAA,oBAAoB,EAAE,qBA3EP;AA4EfC,MAAAA,eAAe,EAAE,sBA5EF;AA6EfC,MAAAA,gBAAgB,EAAE,MA7EH;AA8EfC,MAAAA,WAAW,EAAE,MA9EE;AA+EfC,MAAAA,SAAS,EAAE,MA/EI;AAgFfC,MAAAA,OAAO,EAAE,MAhFM;AAiFfC,MAAAA,aAAa,EAAE,MAjFA;AAkFf5V,MAAAA,SAAS,EAAE,MAlFI;AAmFf6V,MAAAA,eAAe,EAAE,OAnFF;AAoFfC,MAAAA,eAAe,EAAE,OApFF;AAqFfC,MAAAA,gBAAgB,EAAE,OArFH;AAsFfC,MAAAA,WAAW,EAAE,OAtFE;AAuFfC,MAAAA,WAAW,EAAE,OAvFE;AAwFfC,MAAAA,YAAY,EAAE,OAxFC;AAyFfC,MAAAA,YAAY,EAAE,MAzFC;AA0FfC,MAAAA,YAAY,EAAE,MA1FC;AA2FfC,MAAAA,aAAa,EAAE,MA3FA;AA4FfC,MAAAA,YAAY,EAAE,MA5FC;AA6FfC,MAAAA,WAAW,EAAE,MA7FE;AA8FfC,MAAAA,cAAc,EAAE,cA9FD;AA+FfC,MAAAA,YAAY,EAAE,MA/FC;AAgGfC,MAAAA,kBAAkB,EAAE,SAhGL;AAiGfC,MAAAA,kBAAkB,EAAE,SAjGL;AAkGfC,MAAAA,mBAAmB,EAAE,SAlGN;AAmGfC,MAAAA,sBAAsB,EAAE,SAnGT;AAoGfC,MAAAA,sBAAsB,EAAE,SApGT;AAqGfC,MAAAA,uBAAuB,EAAE,SArGV;AAsGfC,MAAAA,mBAAmB,EAAE,QAtGN;AAuGfC,MAAAA,mBAAmB,EAAE,QAvGN;AAwGfC,MAAAA,oBAAoB,EAAE,QAxGP;AAyGfC,MAAAA,mBAAmB,EAAE,QAzGN;AA0GfC,MAAAA,iBAAiB,EAAE,mBA1GJ;AA2GfC,MAAAA,mBAAmB,EAAE,MA3GN;AA4GfC,MAAAA,oBAAoB,EAAE,OA5GP;AA6GfC,MAAAA,aAAa,EAAE,qBA7GA;AA8GfC,MAAAA,aAAa,EAAE,qBA9GA;AA+Gf9f,MAAAA,QAAQ,EAAE;AA/GK,KApxBb;AAq4BJ;AACA7C,IAAAA,SAAS,EAAE;AACT4iB,MAAAA,YAAY,EAAE,MADL;AAETC,MAAAA,YAAY,EAAE,MAFL;AAGTC,MAAAA,QAAQ,EAAE,MAHD;AAITrlB,MAAAA,IAAI,EAAE,MAJG;AAKTqH,MAAAA,UAAU,EAAE,MALH;AAMTie,MAAAA,SAAS,EAAE,QANF;AAOTC,MAAAA,OAAO,EAAE,QAPA;AAQTC,MAAAA,UAAU,EAAE,SARH;AAST1jB,MAAAA,MAAM,EAAE,IATC;AAUT2jB,MAAAA,SAAS,EAAE,IAVF;AAWTC,MAAAA,MAAM,EAAE,IAXC;AAYTC,MAAAA,GAAG,EAAE,MAZI;AAaTC,MAAAA,WAAW,EAAE,KAbJ;AAcTC,MAAAA,SAAS,EAAE,KAdF;AAeTC,MAAAA,WAAW,EAAE,IAfJ;AAgBTC,MAAAA,UAAU,EAAE,MAhBH;AAiBT9T,MAAAA,IAAI,EAAE,IAjBG;AAkBT+T,MAAAA,aAAa,EAAE,UAlBN;AAmBTC,MAAAA,iBAAiB,EAAE,cAnBV;AAoBTC,MAAAA,SAAS,EAAE,MApBF;AAqBTC,MAAAA,cAAc,EAAE,YArBP;AAsBTC,MAAAA,kBAAkB,EAAE,gBAtBX;AAuBTC,MAAAA,UAAU,EAAE,QAvBH;AAwBTC,MAAAA,UAAU,EAAE,OAxBH;AAyBTC,MAAAA,uBAAuB,EAAE,UAzBhB;AA0BTC,MAAAA,gBAAgB,EAAE,aA1BT;AA2BTC,MAAAA,oBAAoB,EAAE,iBA3Bb;AA4BTC,MAAAA,YAAY,EAAE,SA5BL;AA6BTC,MAAAA,gBAAgB,EAAE,SA7BT;AA8BTC,MAAAA,mBAAmB,EAAE,QA9BZ;AA+BTC,MAAAA,kBAAkB,EAAE,QA/BX;AAgCTC,MAAAA,IAAI,EAAE,IAhCG;AAiCTC,MAAAA,iBAAiB,EAAE,OAjCV;AAkCTC,MAAAA,kBAAkB,EAAE,OAlCX;AAmCTC,MAAAA,UAAU,EAAE,MAnCH;AAoCTnc,MAAAA,UAAU,EAAE,MApCH;AAqCToc,MAAAA,YAAY,EAAE,MArCL;AAsCTC,MAAAA,eAAe,EAAE,MAtCR;AAuCTC,MAAAA,UAAU,EAAE,OAvCH;AAwCTC,MAAAA,cAAc,EAAE,MAxCP;AAyCTC,MAAAA,eAAe,EAAE,OAzCR;AA0CTC,MAAAA,YAAY,EAAE,QA1CL;AA2CTC,MAAAA,cAAc,EAAE,MA3CP;AA4CTC,MAAAA,WAAW,EAAE,IA5CJ;AA6CTC,MAAAA,KAAK,EAAE,OA7CE;AA8CTC,MAAAA,aAAa,EAAE,OA9CN;AA+CTC,MAAAA,eAAe,EAAE,OA/CR;AAgDTC,MAAAA,gBAAgB,EAAE,MAhDT;AAiDTC,MAAAA,UAAU,EAAE,MAjDH;AAkDTC,MAAAA,UAAU,EAAE,MAlDH;AAmDTC,MAAAA,WAAW,EAAE,MAnDJ;AAoDTvQ,MAAAA,MAAM,EAAE,IApDC;AAqDTC,MAAAA,KAAK,EAAE,IArDE;AAsDTuQ,MAAAA,QAAQ,EAAE,KAtDD;AAuDTC,MAAAA,yBAAyB,EAAE,iBAvDlB;AAwDTC,MAAAA,4BAA4B,EAAE,kBAxDrB;AAyDTC,MAAAA,wBAAwB,EAAE,aAzDjB;AA0DTC,MAAAA,2BAA2B,EAAE,aA1DpB;AA2DTC,MAAAA,wBAAwB,EAAE,aA3DjB;AA4DTC,MAAAA,qBAAqB,EAAE,eA5Dd;AA6DTC,MAAAA,+BAA+B,EAAE,mBA7DxB;AA8DTC,MAAAA,WAAW,EAAE,MA9DJ;AA+DTC,MAAAA,MAAM,EAAE,KA/DC;AAgETC,MAAAA,MAAM,EAAE,KAhEC;AAiETC,MAAAA,OAAO,EAAE,KAjEA;AAkETC,MAAAA,SAAS,EAAE,KAlEF;AAmETC,MAAAA,QAAQ,EAAE,KAnED;AAoETC,MAAAA,MAAM,EAAE,KApEC;AAqETC,MAAAA,QAAQ,EAAE,KArED;AAsETC,MAAAA,6BAA6B,EAAE,WAtEtB;AAuETC,MAAAA,mBAAmB,EAAE,WAvEZ;AAwETC,MAAAA,mBAAmB,EAAE,WAxEZ;AAyETC,MAAAA,oBAAoB,EAAE,SAzEb;AA0ETC,MAAAA,kBAAkB,EAAE,SA1EX;AA2ETta,MAAAA,oBAAoB,EAAE,OA3Eb;AA4ETua,MAAAA,sBAAsB,EAAE,SA5Ef;AA6ETC,MAAAA,sBAAsB,EAAE,SA7Ef;AA8ETC,MAAAA,uBAAuB,EAAE,SA9EhB;AA+ETC,MAAAA,uBAAuB,EAAE,OA/EhB;AAgFTC,MAAAA,gCAAgC,EAAE,SAhFzB;AAiFTC,MAAAA,+BAA+B,EAAE,QAjFxB;AAkFT7f,MAAAA,wBAAwB,EAAE,UAlFjB;AAmFTC,MAAAA,oBAAoB,EAAE,mBAnFb;AAoFT8H,MAAAA,wBAAwB,EAAE,UApFjB;AAqFTC,MAAAA,oBAAoB,EAAE,mBArFb;AAsFT8X,MAAAA,4BAA4B,EAAE,UAtFrB;AAuFTC,MAAAA,wBAAwB,EAAE,QAvFjB;AAwFTC,MAAAA,uBAAuB,EAAE,qBAxFhB;AAyFTC,MAAAA,+BAA+B,EAAE,mBAzFxB;AA0FTC,MAAAA,+BAA+B,EAAE,mBA1FxB;AA2FTC,MAAAA,oCAAoC,EAAE,qBA3F7B;AA4FTC,MAAAA,oCAAoC,EAAE,qBA5F7B;AA6FTC,MAAAA,qCAAqC,EAAE,qBA7F9B;AA8FTC,MAAAA,gCAAgC,EAAE,qBA9FzB;AA+FTC,MAAAA,iCAAiC,EAAE,sBA/F1B;AAgGTC,MAAAA,qBAAqB,EAAE,uBAhGd;AAiGTC,MAAAA,WAAW,EAAE,6BAjGJ;AAkGTC,MAAAA,oBAAoB,EAAE,SAlGb;AAmGTC,MAAAA,UAAU,EAAE,QAnGH;AAoGTC,MAAAA,yBAAyB,EAAE,SApGlB;AAqGTC,MAAAA,4BAA4B,EAAE,WArGrB;AAsGTC,MAAAA,8BAA8B,EAAE,UAtGvB;AAuGTC,MAAAA,aAAa,EAAE;AAvGN,KAt4BP;AA++BJ;AACAroB,IAAAA,iBAAiB,EAAE;AACjBsoB,MAAAA,cAAc,EAAE,QADC;AAEjBC,MAAAA,eAAe,EAAE,WAFA;AAGjBC,MAAAA,cAAc,EAAE,SAHC;AAIjBC,MAAAA,aAAa,EAAE,SAJE;AAKjBC,MAAAA,UAAU,EAAE,OALK;AAMjBC,MAAAA,YAAY,EAAE,UANG;AAOjBlb,MAAAA,IAAI,EAAE,IAPW;AAQjBmb,MAAAA,mBAAmB,EAAE,SARJ;AASjBC,MAAAA,kBAAkB,EAAE,OATH;AAUjBC,MAAAA,iBAAiB,EAAE,SAVF;AAWjBC,MAAAA,sBAAsB,EAAE,sBAXP;AAYjBC,MAAAA,qBAAqB,EAAE,qBAZN;AAajBC,MAAAA,IAAI,EAAE,IAbW;AAcjBC,MAAAA,eAAe,EAAE,qBAdA;AAejBC,MAAAA,gBAAgB,EAAE,mBAfD;AAgBjBC,MAAAA,gBAAgB,EAAE,QAhBD;AAiBjBC,MAAAA,mBAAmB,EAAE,uBAjBJ;AAkBjBC,MAAAA,aAAa,EAAE,UAlBE;AAmBjBC,MAAAA,mBAAmB,EAAE,eAnBJ;AAoBjBC,MAAAA,YAAY,EAAE,MApBG;AAqBjBC,MAAAA,YAAY,EAAE,QArBG;AAsBjBC,MAAAA,YAAY,EAAE,QAtBG;AAuBjBC,MAAAA,WAAW,EAAE,QAvBI;AAwBjBC,MAAAA,WAAW,EAAE,OAxBI;AAyBjBC,MAAAA,MAAM,EAAE,MAzBS;AA0BjBC,MAAAA,EAAE,EAAE,IA1Ba;AA2BjBC,MAAAA,aAAa,EAAE,QA3BE;AA4BjBC,MAAAA,WAAW,EAAE,QA5BI;AA6BjBC,MAAAA,SAAS,EAAE,MA7BM;AA8BjBC,MAAAA,WAAW,EAAE,MA9BI;AA+BjBC,MAAAA,iBAAiB,EAAE,IA/BF;AAgCjBC,MAAAA,mBAAmB,EAAE,IAhCJ;AAiCjBC,MAAAA,KAAK,EAAE,KAjCU;AAkCjBC,MAAAA,eAAe,EAAE,SAlCA;AAmCjBC,MAAAA,OAAO,EAAE,SAnCQ;AAoCjBC,MAAAA,cAAc,EAAE,QApCC;AAqCjBC,MAAAA,kBAAkB,EAAE;AArCH,KAh/Bf;AAuhCJ;AACAxqB,IAAAA,aAAa,EAAE;AACbkc,MAAAA,aAAa,EAAE,MADF;AAEbrT,MAAAA,QAAQ,EAAE,MAFG;AAGb4hB,MAAAA,MAAM,EAAE,MAHK;AAIbxoB,MAAAA,SAAS,EAAE,MAJE;AAKbyoB,MAAAA,UAAU,EAAE,QALC;AAMbC,MAAAA,cAAc,EAAE,OANH;AAObtH,MAAAA,WAAW,EAAE,MAPA;AAQbuH,MAAAA,eAAe,EAAE,aARJ;AASbhpB,MAAAA,SAAS,EAAE,KATE;AAUbipB,MAAAA,QAAQ,EAAE,KAVG;AAWbC,MAAAA,OAAO,EAAE;AAXI,KAxhCX;AAqiCJ;AACAC,IAAAA,MAAM,EAAE;AACNnZ,MAAAA,SAAS,EAAE,OADL;AAENoZ,MAAAA,UAAU,EAAE,OAFN;AAGNhgB,MAAAA,UAAU,EAAE,OAHN;AAINgH,MAAAA,UAAU,EAAE,OAJN;AAKNiZ,MAAAA,QAAQ,EAAE,OALJ;AAMNC,MAAAA,SAAS,EAAE,SANL;AAONC,MAAAA,eAAe,EAAE,WAPX;AAQNC,MAAAA,aAAa,EAAE,UART;AASNC,MAAAA,eAAe,EAAE,UATX;AAUNC,MAAAA,eAAe,EAAE,UAVX;AAWNC,MAAAA,aAAa,EAAE,UAXT;AAYNC,MAAAA,cAAc,EAAE,YAZV;AAaNC,MAAAA,oBAAoB,EAAE,cAbhB;AAcNC,MAAAA,kBAAkB,EAAE,aAdd;AAeN3Z,MAAAA,QAAQ,EAAE,OAfJ;AAgBN4Z,MAAAA,gBAAgB,EAAE,SAhBZ;AAiBNC,MAAAA,gBAAgB,EAAE;AAjBZ,KAtiCJ;AAyjCJ;AACAzrB,IAAAA,YAAY,EAAE;AACZ0rB,MAAAA,eAAe,EAAE,UADL;AAEZtuB,MAAAA,IAAI,EAAE,MAFM;AAGZuuB,MAAAA,YAAY,EAAE,OAHF;AAIZC,MAAAA,WAAW,EAAE,OAJD;AAKZC,MAAAA,YAAY,EAAE,OALF;AAMZC,MAAAA,eAAe,EAAE,SANL;AAOZC,MAAAA,OAAO,EAAE,KAPG;AAQZC,MAAAA,SAAS,EAAE,KARC;AASZC,MAAAA,WAAW,EAAE,MATD;AAUZC,MAAAA,MAAM,EAAE,IAVI;AAWZC,MAAAA,QAAQ,EAAE,IAXE;AAYZC,MAAAA,OAAO,EAAE,IAZG;AAaZC,MAAAA,OAAO,EAAE,IAbG;AAcZC,MAAAA,gBAAgB,EAAE,OAdN;AAeZC,MAAAA,WAAW,EAAE,MAfD;AAgBZC,MAAAA,SAAS,EAAE,MAhBC;AAiBZC,MAAAA,UAAU,EAAE,OAjBA;AAkBZC,MAAAA,SAAS,EAAE,QAlBC;AAmBZC,MAAAA,MAAM,EAAE,MAnBI;AAoBZlD,MAAAA,MAAM,EAAE,IApBI;AAqBZ7rB,MAAAA,KAAK,EAAE,IArBK;AAsBZgvB,MAAAA,OAAO,EAAE,IAtBG;AAuBZ1J,MAAAA,WAAW,EAAE,IAvBD;AAwBZxa,MAAAA,QAAQ,EAAE,MAxBE;AAyBZmkB,MAAAA,UAAU,EAAE,MAzBA;AA0BZC,MAAAA,SAAS,EAAE,MA1BC;AA2BZpvB,MAAAA,UAAU,EAAE,QA3BA;AA4BZ4sB,MAAAA,MAAM,EAAE,IA5BI;AA6BZyC,MAAAA,OAAO,EAAE,OA7BG;AA8BZC,MAAAA,aAAa,EAAE,KA9BH;AA+BZC,MAAAA,YAAY,EAAE,MA/BF;AAgCZC,MAAAA,cAAc,EAAE,UAhCJ;AAiCZC,MAAAA,eAAe,EAAE,SAjCL;AAkCZC,MAAAA,QAAQ,EAAE,KAlCE;AAmCZC,MAAAA,MAAM,EAAE,MAnCI;AAoCZC,MAAAA,QAAQ,EAAE,MApCE;AAqCZC,MAAAA,iBAAiB,EAAE,SArCP;AAsCZC,MAAAA,uBAAuB,EAAE,qBAtCb;AAuCZhO,MAAAA,WAAW,EAAE,oBAvCD;AAwCZtM,MAAAA,0BAA0B,EAAE,mBAxChB;AAyCZlX,MAAAA,kBAAkB,EAAE,mBAzCR;AA0CZyxB,MAAAA,WAAW,EAAE,mBA1CD;AA2CZC,MAAAA,qBAAqB,EAAE,sBA3CX;AA4CZC,MAAAA,kBAAkB,EAAE,eA5CR;AA6CZC,MAAAA,QAAQ,EAAE,QA7CE;AA8CZC,MAAAA,KAAK,EAAE,MA9CK;AA+CZnpB,MAAAA,GAAG,EAAE,GA/CO;AAgDZtF,MAAAA,EAAE,EAAE,GAhDQ;AAiDZ0uB,MAAAA,WAAW,EAAE,OAjDD;AAkDZC,MAAAA,SAAS,EAAE,KAlDC;AAmDZC,MAAAA,WAAW,EAAE,SAnDD;AAoDZC,MAAAA,YAAY,EAAE,UApDF;AAqDZC,MAAAA,aAAa,EAAE,SArDH;AAsDZC,MAAAA,QAAQ,EAAE,KAtDE;AAuDZC,MAAAA,UAAU,EAAE,MAvDA;AAwDZt3B,MAAAA,MAAM,EAAE,KAxDI;AAyDZu3B,MAAAA,UAAU,EAAE;AAzDA,KA1jCV;AAqnCJ;AACApuB,IAAAA,SAAS,EAAE;AACTquB,MAAAA,WAAW,EAAE,KADJ;AAET12B,MAAAA,YAAY,EAAE,KAFL;AAGT22B,MAAAA,YAAY,EAAE,IAHL;AAIT/qB,MAAAA,SAAS,EAAE,IAJF;AAKT+oB,MAAAA,WAAW,EAAE,MALJ;AAMTC,MAAAA,SAAS,EAAE,MANF;AAOTC,MAAAA,UAAU,EAAE,OAPH;AAQT+B,MAAAA,eAAe,EAAE,MARR;AAST5d,MAAAA,WAAW,EAAE,KATJ;AAUTC,MAAAA,WAAW,EAAE,KAVJ;AAWT7T,MAAAA,YAAY,EAAE,MAXL;AAYTwL,MAAAA,cAAc,EAAE,MAZP;AAaTimB,MAAAA,kBAAkB,EAAE,QAbX;AAcTC,MAAAA,UAAU,EAAE,MAdH;AAeTC,MAAAA,SAAS,EAAE,MAfF;AAgBTC,MAAAA,OAAO,EAAE,MAhBA;AAiBTC,MAAAA,kBAAkB,EAAE,QAjBX;AAkBTnxB,MAAAA,UAAU,EAAE,QAlBH;AAmBT4sB,MAAAA,MAAM,EAAE,IAnBC;AAoBTwE,MAAAA,YAAY,EAAE,MApBL;AAqBTC,MAAAA,OAAO,EAAE,QArBA;AAsBTC,MAAAA,KAAK,EAAE,MAtBE;AAuBTC,MAAAA,IAAI,EAAE,MAvBG;AAwBT/L,MAAAA,WAAW,EAAE,MAxBJ;AAyBTnV,MAAAA,YAAY,EAAE,MAzBL;AA0BTmhB,MAAAA,QAAQ,EAAE,MA1BD;AA2BTC,MAAAA,WAAW,EAAE,MA3BJ;AA4BTC,MAAAA,YAAY,EAAE,KA5BL;AA6BTC,MAAAA,aAAa,EAAE,KA7BN;AA8BTC,MAAAA,UAAU,EAAE,IA9BH;AA+BTC,MAAAA,iBAAiB,EAAE,MA/BV;AAgCTC,MAAAA,WAAW,EAAE,MAhCJ;AAiCTC,MAAAA,MAAM,EAAE,MAjCC;AAkCTC,MAAAA,qBAAqB,EAAE,iBAlCd;AAmCTC,MAAAA,qBAAqB,EAAE,iBAnCd;AAoCTC,MAAAA,qBAAqB,EAAE,iBApCd;AAqCTC,MAAAA,kBAAkB,EAAE,YArCX;AAsCTC,MAAAA,mBAAmB,EAAE,YAtCZ;AAuCTC,MAAAA,YAAY,EAAE,WAvCL;AAwCTC,MAAAA,gBAAgB,EAAE,aAxCT;AAyCTzX,MAAAA,cAAc,EAAE,WAzCP;AA0CT0X,MAAAA,qBAAqB,EAAE,YA1Cd;AA2CTC,MAAAA,mBAAmB,EAAE,UA3CZ;AA4CTC,MAAAA,MAAM,EAAE,MA5CC;AA6CTC,MAAAA,UAAU,EAAE,MA7CH;AA8CT3G,MAAAA,MAAM,EAAE,IA9CC;AA+CT4G,MAAAA,SAAS,EAAE,MA/CF;AAgDTC,MAAAA,KAAK,EAAE,UAhDE;AAiDTC,MAAAA,MAAM,EAAE,QAjDC;AAkDTC,MAAAA,SAAS,EAAE,WAlDF;AAmDTC,MAAAA,WAAW,EAAE,aAnDJ;AAoDTC,MAAAA,SAAS,EAAE,WApDF;AAqDTC,MAAAA,GAAG,EAAE,KArDI;AAsDTC,MAAAA,UAAU,EAAE,QAtDH;AAuDTC,MAAAA,QAAQ,EAAE,MAvDD;AAwDTC,MAAAA,UAAU,EAAE,MAxDH;AAyDTC,MAAAA,MAAM,EAAE,MAzDC;AA0DTC,MAAAA,UAAU,EAAE,MA1DH;AA2DTC,MAAAA,eAAe,EAAE,MA3DR;AA4DTC,MAAAA,SAAS,EAAE,KA5DF;AA6DTrE,MAAAA,UAAU,EAAE,MA7DH;AA8DTsE,MAAAA,YAAY,EAAE,KA9DL;AA+DTC,MAAAA,cAAc,EAAE,KA/DP;AAgETC,MAAAA,UAAU,EAAE,KAhEH;AAiETjE,MAAAA,QAAQ,EAAE;AAjED,KAtnCP;AAyrCJ;AACAjtB,IAAAA,YAAY,EAAE;AACZ5C,MAAAA,KAAK,EAAE,IADK;AAEZ+zB,MAAAA,GAAG,EAAE,MAFO;AAGZC,MAAAA,KAAK,EAAE,KAHK;AAIZC,MAAAA,YAAY,EAAE,KAJF;AAKZC,MAAAA,GAAG,EAAE,KALO;AAMZC,MAAAA,GAAG,EAAE,KANO;AAOZC,MAAAA,SAAS,EAAE,MAPC;AAQZC,MAAAA,IAAI,EAAE,IARM;AASZ1O,MAAAA,WAAW,EAAE,MATD;AAUZ2O,MAAAA,cAAc,EAAE,OAVJ;AAWZC,MAAAA,kBAAkB,EAAE,IAXR;AAYZC,MAAAA,eAAe,EAAE,IAZL;AAaZC,MAAAA,eAAe,EAAE,MAbL;AAcZC,MAAAA,sBAAsB,EAAE,MAdZ;AAeZC,MAAAA,sBAAsB,EAAE,IAfZ;AAgBZC,MAAAA,qBAAqB,EAAE,MAhBX;AAiBZC,MAAAA,cAAc,EAAE,MAjBJ;AAkBZC,MAAAA,0BAA0B,EAAE,6BAlBhB;AAmBZC,MAAAA,wBAAwB,EAAE,oCAnBd;AAoBZC,MAAAA,uBAAuB,EAAE,oCApBb;AAqBZC,MAAAA,0BAA0B,EAAE,aArBhB;AAsBZC,MAAAA,wBAAwB,EAAE,iCAtBd;AAuBZC,MAAAA,cAAc,EAAE,eAvBJ;AAwBZC,MAAAA,YAAY,EAAE,aAxBF;AAyBZC,MAAAA,YAAY,EAAE,eAzBF;AA0BZC,MAAAA,gBAAgB,EAAE,YA1BN;AA2BZC,MAAAA,gBAAgB,EAAE,YA3BN;AA4BZC,MAAAA,uBAAuB,EAAE,YA5Bb;AA6BZC,MAAAA,0BAA0B,EAAE,UA7BhB;AA8BZC,MAAAA,+BAA+B,EAAE,UA9BrB;AA+BZC,MAAAA,4BAA4B,EAAE,YA/BlB;AAgCZC,MAAAA,gCAAgC,EAAE,aAhCtB;AAiCZC,MAAAA,0BAA0B,EAAE,WAjChB;AAkCZC,MAAAA,8BAA8B,EAAE,aAlCpB;AAmCZC,MAAAA,+BAA+B,EAAE,aAnCrB;AAoCZC,MAAAA,0BAA0B,EAAE,qCApChB;AAqCZC,MAAAA,qBAAqB,EAAE,qBArCX;AAsCZC,MAAAA,qBAAqB,EAAE,UAtCX;AAuCZC,MAAAA,8BAA8B,EAAE,wBAvCpB;AAwCZC,MAAAA,2CAA2C,EAAE,wBAxCjC;AAyCZC,MAAAA,gCAAgC,EAAE,sBAzCtB;AA0CZC,MAAAA,8BAA8B,EAAE,sBA1CpB;AA2CZC,MAAAA,8BAA8B,EAAE,2BA3CpB;AA4CZC,MAAAA,kCAAkC,EAAE,+BA5CxB;AA6CZC,MAAAA,6BAA6B,EAAE,WA7CnB;AA8CZC,MAAAA,oCAAoC,EAAE,eA9C1B;AA+CZC,MAAAA,0BAA0B,EAAE,OA/ChB;AAgDZC,MAAAA,qBAAqB,EAAE,MAhDX;AAiDZC,MAAAA,4BAA4B,EAAE,UAjDlB;AAkDZC,MAAAA,4BAA4B,EAAE,iBAlDlB;AAmDZC,MAAAA,4BAA4B,EAAE,iBAnDlB;AAoDZC,MAAAA,wBAAwB,EAAE,UApDd;AAqDZC,MAAAA,8BAA8B,EAAE,gBArDpB;AAsDZC,MAAAA,qBAAqB,EAAE,WAtDX;AAuDZ33B,MAAAA,KAAK,EAAE;AAvDK,KA1rCV;AAmvCJ;AACA43B,IAAAA,MAAM,EAAE;AACNC,MAAAA,OAAO,EAAE;AADH,KApvCJ;AAuvCJ;AACAv0B,IAAAA,eAAe,EAAE;AACfhD,MAAAA,IAAI,EAAE,MADS;AAEfw3B,MAAAA,OAAO,EAAE,IAFM;AAGfnL,MAAAA,MAAM,EAAE,IAHO;AAIfoL,MAAAA,OAAO,EAAE;AAJM,KAxvCb;AA8vCJ;AACAC,IAAAA,IAAI,EAAE;AACJlgC,MAAAA,MAAM,EAAE;AACNmgC,QAAAA,gBAAgB,EAAE,OADZ;AAENC,QAAAA,UAAU,EAAE,UAFN;AAGNC,QAAAA,eAAe,EAAE,MAHX;AAINC,QAAAA,OAAO,EAAE,MAJH;AAKNC,QAAAA,MAAM,EAAE,MALF;AAMN5T,QAAAA,YAAY,EAAE,MANR;AAON6T,QAAAA,aAAa,EAAE,MAPT;AAQNv+B,QAAAA,OAAO,EAAE,IARH;AASNC,QAAAA,MAAM,EAAE,IATF;AAUNjB,QAAAA,+BAA+B,EAAE,UAV3B;AAWN2R,QAAAA,MAAM,EAAE,MAXF;AAYN6tB,QAAAA,YAAY,EAAE,SAZR;AAaNC,QAAAA,GAAG,EAAE,IAbC;AAcNr+B,QAAAA,IAAI,EAAE,IAdA;AAeNP,QAAAA,MAAM,EAAE,IAfF;AAgBN6+B,QAAAA,SAAS,EAAE,MAhBL;AAiBNC,QAAAA,UAAU,EAAE,MAjBN;AAkBNC,QAAAA,OAAO,EAAE,MAlBH;AAmBNC,QAAAA,QAAQ,EAAE,MAnBJ;AAoBNvlB,QAAAA,GAAG,EAAE,OApBC;AAqBNvS,QAAAA,KAAK,EAAE,MArBD;AAsBN2S,QAAAA,OAAO,EAAE,MAtBH;AAuBNolB,QAAAA,OAAO,EAAE,QAvBH;AAwBNC,QAAAA,SAAS,EAAE,MAxBL;AAyBNC,QAAAA,SAAS,EAAE,MAzBL;AA0BNC,QAAAA,UAAU,EAAE,UA1BN;AA2BNC,QAAAA,SAAS,EAAE,QA3BL;AA4BNC,QAAAA,QAAQ,EAAE,QA5BJ;AA6BNC,QAAAA,mBAAmB,EAAE,OA7Bf;AA8BNC,QAAAA,WAAW,EAAE,UA9BP;AA+BNC,QAAAA,aAAa,EAAE,UA/BT;AAgCNC,QAAAA,mBAAmB,EAAE,gBAhCf;AAiCN3M,QAAAA,MAAM,EAAE,IAjCF;AAkCN4M,QAAAA,MAAM,EAAE,IAlCF;AAmCNC,QAAAA,iBAAiB,EAAE,UAnCb;AAoCNC,QAAAA,UAAU,EAAE,OApCN;AAqCNC,QAAAA,gBAAgB,EAAE,QArCZ;AAsCNC,QAAAA,WAAW,EAAE,MAtCP;AAuCNC,QAAAA,UAAU,EAAE,QAvCN;AAwCNC,QAAAA,iBAAiB,EAAE,SAxCb;AAyCNC,QAAAA,cAAc,EAAE,UAzCV;AA0CN/5B,QAAAA,WAAW,EAAE,IA1CP;AA2CNg6B,QAAAA,cAAc,EAAE,QA3CV;AA4CNjG,QAAAA,UAAU,EAAE,QA5CN;AA6CNkG,QAAAA,SAAS,EAAE,SA7CL;AA8CNC,QAAAA,MAAM,EAAE,IA9CF;AA+CNj6B,QAAAA,KAAK,EAAE,IA/CD;AAgDN5G,QAAAA,MAAM,EAAE,GAhDF;AAiDN8gC,QAAAA,iBAAiB,EAAE,SAjDb;AAkDNC,QAAAA,cAAc,EAAE,MAlDV;AAmDNC,QAAAA,MAAM,EAAE,IAnDF;AAoDNC,QAAAA,wBAAwB,EAAE,SApDpB;AAqDNC,QAAAA,mCAAmC,EAAE;AArD/B,OADJ;AAwDJC,MAAAA,GAAG,EAAE;AACHC,QAAAA,kBAAkB,EAAE,MADjB;AAEHC,QAAAA,mBAAmB,EAAE;AAFlB,OAxDD;AA4DJC,MAAAA,GAAG,EAAE;AACHnP,QAAAA,aAAa,EAAE;AADZ,OA5DD;AAgEJoP,MAAAA,MAAM,EAAE;AACNjgC,QAAAA,SAAS,EAAE,IADL;AAEN4F,QAAAA,IAAI,EAAE,MAFA;AAGNwH,QAAAA,IAAI,EAAE,MAHA;AAIN4C,QAAAA,MAAM,EAAE,SAJF;AAKNkwB,QAAAA,WAAW,EAAE,OALP;AAMNxhC,QAAAA,MAAM,EAAE,GANF;AAONyhC,QAAAA,IAAI,EAAE,IAPA;AAQNjwB,QAAAA,MAAM,EAAE,IARF;AASNkwB,QAAAA,eAAe,EAAE,QATX;AAUN96B,QAAAA,KAAK,EAAE,IAVD;AAWN+6B,QAAAA,kBAAkB,EAAE,QAXd;AAYNC,QAAAA,YAAY,EAAE,OAZR;AAaNhsB,QAAAA,KAAK,EAAE,IAbD;AAcNisB,QAAAA,IAAI,EAAE;AAdA,OAhEJ;AAgFJC,MAAAA,GAAG,EAAE;AACHxgC,QAAAA,SAAS,EAAE,IADR;AAEHygC,QAAAA,gBAAgB,EAAE,QAFf;AAGHC,QAAAA,QAAQ,EAAE,OAHP;AAIHzjB,QAAAA,MAAM,EAAE,IAJL;AAKHyO,QAAAA,WAAW,EAAE,IALV;AAMHiV,QAAAA,cAAc,EAAE,MANb;AAOH5gC,QAAAA,IAAI,EAAE,IAPH;AAQHsJ,QAAAA,WAAW,EAAE,OARV;AASHu3B,QAAAA,aAAa,EAAE,MATZ;AAUHC,QAAAA,IAAI,EAAE,KAVH;AAWHrsB,QAAAA,aAAa,EAAE,MAXZ;AAYHF,QAAAA,KAAK,EAAE,OAZJ;AAaHnE,QAAAA,UAAU,EAAE,OAbT;AAeHpG,QAAAA,QAAQ,EAAE,MAfP;AAgBH+2B,QAAAA,UAAU,EAAE,MAhBT;AAiBHC,QAAAA,eAAe,EAAE,MAjBd;AAkBHC,QAAAA,SAAS,EAAE,OAlBR;AAmBHC,QAAAA,UAAU,EAAE,MAnBT;AAoBHC,QAAAA,QAAQ,EAAE,OApBP;AAqBHC,QAAAA,mBAAmB,EAAE,QArBlB;AAsBHC,QAAAA,0BAA0B,EAAE,WAtBzB;AAuBHC,QAAAA,QAAQ,EAAE,IAvBP;AAwBHC,QAAAA,cAAc,EAAE,MAxBb;AAyBHC,QAAAA,YAAY,EAAE,MAzBX;AA0BHC,QAAAA,UAAU,EAAE,MA1BT;AA2BHC,QAAAA,YAAY,EAAE,MA3BX;AA6BHC,QAAAA,iBAAiB,EAAE,UA7BhB;AA8BHC,QAAAA,mBAAmB,EAAE,SA9BlB;AA+BHC,QAAAA,cAAc,EAAE,MA/Bb;AAgCHC,QAAAA,QAAQ,EAAE,IAhCP;AAiCHC,QAAAA,YAAY,EAAE,MAjCX;AAkCHC,QAAAA,QAAQ,EAAE,KAlCP;AAmCHC,QAAAA,OAAO,EAAE,MAnCN;AAoCHC,QAAAA,OAAO,EAAE,MApCN;AAqCH33B,QAAAA,SAAS,EAAE,MArCR;AAsCH43B,QAAAA,cAAc,EAAE,MAtCb;AAuCHC,QAAAA,IAAI,EAAE,IAvCH;AAwCHvxB,QAAAA,OAAO,EAAE,IAxCN;AAyCHoV,QAAAA,IAAI,EAAE,IAzCH;AA0CHoc,QAAAA,UAAU,EAAE,OA1CT;AA2CH1C,QAAAA,MAAM,EAAE,IA3CL;AA4CH2C,QAAAA,MAAM,EAAE,MA5CL;AA6CHC,QAAAA,UAAU,EAAE,QA7CT;AA8CHC,QAAAA,OAAO,EAAE,MA9CN;AA+CHC,QAAAA,sBAAsB,EAAE,QA/CrB;AAgDHC,QAAAA,GAAG,EAAE,IAhDF;AAiDHC,QAAAA,KAAK,EAAE,IAjDJ;AAkDHC,QAAAA,eAAe,EAAE,MAlDd;AAmDHC,QAAAA,aAAa,EAAE,MAnDZ;AAoDHC,QAAAA,cAAc,EAAE,MApDb;AAqDHC,QAAAA,aAAa,EAAE,MArDZ;AAsDHC,QAAAA,OAAO,EAAE,OAtDN;AAuDHC,QAAAA,QAAQ,EAAE,MAvDP;AAwDHC,QAAAA,WAAW,EAAE,QAxDV;AAyDHC,QAAAA,WAAW,EAAE,KAzDV;AA0DHC,QAAAA,UAAU,EAAE,KA1DT;AA2DHC,QAAAA,WAAW,EAAE,MA3DV;AA4DHC,QAAAA,UAAU,EAAE,MA5DT;AA6DHC,QAAAA,MAAM,EAAE,KA7DL;AA8DHC,QAAAA,GAAG,EAAE,GA9DF;AA+DHC,QAAAA,MAAM,EAAE,IA/DL;AAgEHC,QAAAA,SAAS,EAAE,MAhER;AAiEHC,QAAAA,UAAU,EAAE,MAjET;AAkEHC,QAAAA,UAAU,EAAE,MAlET;AAmEHC,QAAAA,YAAY,EAAE;AAnEX,OAhFD;AAsJJC,MAAAA,GAAG,EAAE;AACH7jC,QAAAA,SAAS,EAAE,IADR;AAEH8jC,QAAAA,eAAe,EAAE,MAFd;AAGHC,QAAAA,mBAAmB,EAAE,OAHlB;AAIH/C,QAAAA,SAAS,EAAE,KAJR;AAKH7wB,QAAAA,UAAU,EAAE,OALT;AAMH6zB,QAAAA,mBAAmB,EAAE,MANlB;AAOHC,QAAAA,+BAA+B,EAAE,QAP9B;AAQH/C,QAAAA,QAAQ,EAAE,OARP;AASHgD,QAAAA,cAAc,EAAE,MATb;AAUHC,QAAAA,YAAY,EAAE,MAVX;AAWHC,QAAAA,cAAc,EAAE,MAXb;AAYHC,QAAAA,MAAM,EAAE,OAZL;AAaHC,QAAAA,MAAM,EAAE,OAbL;AAcHpK,QAAAA,GAAG,EAAE,KAdF;AAeHrf,QAAAA,WAAW,EAAE,IAfV;AAgBH0pB,QAAAA,0BAA0B,EAAE,SAhBzB;AAiBHC,QAAAA,gBAAgB,EAAE,WAjBf;AAkBHt0B,QAAAA,MAAM,EAAE,IAlBL;AAmBHu0B,QAAAA,MAAM,EAAE,IAnBL;AAoBHC,QAAAA,mBAAmB,EAAE,MApBlB;AAqBHC,QAAAA,qBAAqB,EAAE,MArBpB;AAsBHC,QAAAA,eAAe,EAAE,MAtBd;AAuBHC,QAAAA,iBAAiB,EAAE,MAvBhB;AAwBHC,QAAAA,SAAS,EAAE,QAxBR;AAyBHC,QAAAA,SAAS,EAAE,QAzBR;AA0BH/e,QAAAA,IAAI,EAAE,IA1BH;AA2BHgc,QAAAA,OAAO,EAAE,MA3BN;AA4BHC,QAAAA,OAAO,EAAE,MA5BN;AA6BH33B,QAAAA,SAAS,EAAE,MA7BR;AA8BH43B,QAAAA,cAAc,EAAE,MA9Bb;AA+BHtxB,QAAAA,OAAO,EAAE,IA/BN;AAgCHyK,QAAAA,GAAG,EAAE,KAhCF;AAiCHE,QAAAA,GAAG,EAAE,KAjCF;AAkCHypB,QAAAA,OAAO,EAAE,IAlCN;AAmCHC,QAAAA,kBAAkB,EAAE,IAnCjB;AAoCH9C,QAAAA,IAAI,EAAE,IApCH;AAqCH+C,QAAAA,eAAe,EAAE,MArCd;AAsCHC,QAAAA,MAAM,EAAE,GAtCL;AAuCHC,QAAAA,WAAW,EAAE,KAvCV;AAwCHxC,QAAAA,aAAa,EAAE,MAxCZ;AAyCHyC,QAAAA,6BAA6B,EAAE,QAzC5B;AA0CHjM,QAAAA,UAAU,EAAE,MA1CT;AA2CHiK,QAAAA,UAAU,EAAE,MA3CT;AA4CHC,QAAAA,MAAM,EAAE,KA5CL;AA6CHC,QAAAA,GAAG,EAAE,GA7CF;AA8CHC,QAAAA,MAAM,EAAE,IA9CL;AA+CHC,QAAAA,SAAS,EAAE,MA/CR;AAgDHC,QAAAA,UAAU,EAAE,MAhDT;AAiDHC,QAAAA,UAAU,EAAE,MAjDT;AAkDHC,QAAAA,YAAY,EAAE,OAlDX;AAmDH0B,QAAAA,WAAW,EAAE,MAnDV;AAoDHC,QAAAA,uBAAuB,EAAE,UApDtB;AAqDHC,QAAAA,4BAA4B,EAAE,WArD3B;AAsDHC,QAAAA,uCAAuC,EAAE,kBAtDtC;AAuDHC,QAAAA,oBAAoB,EAAE,MAvDnB;AAwDHC,QAAAA,uBAAuB,EAAE,OAxDtB;AAyDHC,QAAAA,KAAK,EAAE,GAzDJ;AA0DHC,QAAAA,KAAK,EAAE,MA1DJ;AA2DHC,QAAAA,QAAQ,EAAE,IA3DP;AA4DHC,QAAAA,QAAQ,EAAE,IA5DP;AA6DHC,QAAAA,WAAW,EAAE,IA7DV;AA8DHC,QAAAA,aAAa,EAAE,IA9DZ;AA+DH9qB,QAAAA,SAAS,EAAE,IA/DR;AAgEH+qB,QAAAA,eAAe,EAAE,IAhEd;AAiEHC,QAAAA,eAAe,EAAE,KAjEd;AAkEHC,QAAAA,gBAAgB,EAAE,OAlEf;AAmEHC,QAAAA,cAAc,EAAE,MAnEb;AAoEHC,QAAAA,eAAe,EAAE,MApEd;AAqEHC,QAAAA,YAAY,EAAE,MArEX;AAsEHC,QAAAA,aAAa,EAAE,MAtEZ;AAuEHC,QAAAA,cAAc,EAAE,MAvEb;AAwEHC,QAAAA,iBAAiB,EAAE,MAxEhB;AAyEHC,QAAAA,gBAAgB,EAAE,MAzEf;AA0EHC,QAAAA,oBAAoB,EAAE,OA1EnB;AA2EHC,QAAAA,mBAAmB,EAAE,OA3ElB;AA4EHC,QAAAA,oBAAoB,EAAE,OA5EnB;AA6EHC,QAAAA,mBAAmB,EAAE,OA7ElB;AA8EHC,QAAAA,UAAU,EAAE,UA9ET;AA+EHC,QAAAA,OAAO,EAAE;AA/EN,OAtJD;AAuOJp+B,MAAAA,MAAM,EAAE;AACN7I,QAAAA,SAAS,EAAE,IADL;AAENknC,QAAAA,OAAO,EAAE,MAFH;AAGN/G,QAAAA,IAAI,EAAE,IAHA;AAINgH,QAAAA,KAAK,EAAE,IAJD;AAKNC,QAAAA,GAAG,EAAE,IALC;AAMNC,QAAAA,UAAU,EAAE,IANN;AAONC,QAAAA,GAAG,EAAE,IAPC;AAQNrV,QAAAA,MAAM,EAAE,IARF;AASNsV,QAAAA,OAAO,EAAE,IATH;AAUN1I,QAAAA,MAAM,EAAE,IAVF;AAWN3uB,QAAAA,MAAM,EAAE,IAXF;AAYNs3B,QAAAA,kBAAkB,EAAE,SAZd;AAaNC,QAAAA,GAAG,EAAE,OAbC;AAcNvN,QAAAA,GAAG,EAAE,KAdC;AAeNyG,QAAAA,cAAc,EAAE,MAfV;AAgBN+G,QAAAA,IAAI,EAAE,IAhBA;AAiBNr+B,QAAAA,WAAW,EAAE,MAjBP;AAkBNs+B,QAAAA,OAAO,EAAE,IAlBH;AAmBNC,QAAAA,UAAU,EAAE,IAnBN;AAoBN99B,QAAAA,cAAc,EAAE,MApBV;AAqBNP,QAAAA,QAAQ,EAAE,IArBJ;AAsBNM,QAAAA,KAAK,EAAE,IAtBD;AAuBNg4B,QAAAA,QAAQ,EAAE,IAvBJ;AAwBNj7B,QAAAA,UAAU,EAAE,KAxBN;AAyBNihC,QAAAA,KAAK,EAAE,IAzBD;AA0BNtI,QAAAA,MAAM,EAAE,KA1BF;AA2BNuC,QAAAA,YAAY,EAAE,MA3BR;AA4BNC,QAAAA,QAAQ,EAAE,KA5BJ;AA6BNztB,QAAAA,KAAK,EAAE,IA7BD;AA8BNwzB,QAAAA,YAAY,EAAE,MA9BR;AA+BNC,QAAAA,UAAU,EAAE,MA/BN;AAgCN/F,QAAAA,OAAO,EAAE,MAhCH;AAiCNC,QAAAA,OAAO,EAAE,MAjCH;AAkCN33B,QAAAA,SAAS,EAAE,MAlCL;AAmCN43B,QAAAA,cAAc,EAAE,MAnCV;AAoCN90B,QAAAA,IAAI,EAAE,MApCA;AAqCN4C,QAAAA,MAAM,EAAE,SArCF;AAsCN9C,QAAAA,GAAG,EAAE,GAtCC;AAuCNtF,QAAAA,EAAE,EAAE,GAvCE;AAwCN06B,QAAAA,UAAU,EAAE,QAxCN;AAyCN0F,QAAAA,cAAc,EAAE,MAzCV;AA0CNC,QAAAA,aAAa,EAAE,UA1CT;AA2CNC,QAAAA,UAAU,EAAE,KA3CN;AA4CNC,QAAAA,SAAS,EAAE,QA5CL;AA6CNC,QAAAA,SAAS,EAAE,QA7CL;AA8CNC,QAAAA,WAAW,EAAE,QA9CP;AA+CNC,QAAAA,UAAU,EAAE,QA/CN;AAgDNC,QAAAA,eAAe,EAAE,MAhDX;AAiDNC,QAAAA,SAAS,EAAE,MAjDL;AAkDN/oC,QAAAA,IAAI,EAAE,IAlDA;AAmDNu+B,QAAAA,UAAU,EAAE,MAnDN;AAoDNE,QAAAA,QAAQ,EAAE,MApDJ;AAqDNuK,QAAAA,iBAAiB,EAAE,OArDb;AAsDNC,QAAAA,OAAO,EAAE;AAtDH,OAvOJ;AAgSJC,MAAAA,GAAG,EAAE;AACH3oC,QAAAA,SAAS,EAAE,IADR;AAEH4oC,QAAAA,SAAS,EAAE,OAFR;AAGHhjC,QAAAA,IAAI,EAAE,MAHH;AAIHwH,QAAAA,IAAI,EAAE,MAJH;AAKH4C,QAAAA,MAAM,EAAE,SALL;AAMHkwB,QAAAA,WAAW,EAAE,OANV;AAOHxhC,QAAAA,MAAM,EAAE,GAPL;AAQHyhC,QAAAA,IAAI,EAAE,IARH;AASHjwB,QAAAA,MAAM,EAAE,IATL;AAUHkwB,QAAAA,eAAe,EAAE,QAVd;AAWH96B,QAAAA,KAAK,EAAE,IAXJ;AAYH+6B,QAAAA,kBAAkB,EAAE,QAZjB;AAaHC,QAAAA,YAAY,EAAE,OAbX;AAcHhsB,QAAAA,KAAK,EAAE,IAdJ;AAeHisB,QAAAA,IAAI,EAAE,IAfH;AAgBHsI,QAAAA,UAAU,EAAE,IAhBT;AAiBHC,QAAAA,YAAY,EAAE,IAjBX;AAkBHC,QAAAA,OAAO,EAAE,MAlBN;AAmBH1rB,QAAAA,MAAM,EAAE,IAnBL;AAoBHC,QAAAA,KAAK,EAAE,IApBJ;AAqBH0rB,QAAAA,UAAU,EAAE,MArBT;AAsBHj9B,QAAAA,aAAa,EAAE,MAtBZ;AAuBHk3B,QAAAA,WAAW,EAAE,KAvBV;AAwBHE,QAAAA,UAAU,EAAE,MAxBT;AAyBH8F,QAAAA,gBAAgB,EAAE,OAzBf;AA0BHC,QAAAA,MAAM,EAAE,IA1BL;AA2BHC,QAAAA,OAAO,EAAE,IA3BN;AA4BH9pC,QAAAA,OAAO,EAAE,IA5BN;AA6BH+pC,QAAAA,MAAM,EAAE,IA7BL;AA8BHC,QAAAA,gBAAgB,EAAE,OA9Bf;AA+BHC,QAAAA,cAAc,EAAE;AA/Bb,OAhSD;AAiUJC,MAAAA,GAAG,EAAE;AACHvpC,QAAAA,SAAS,EAAE,IADR;AAEH8jC,QAAAA,eAAe,EAAE,MAFd;AAGHC,QAAAA,mBAAmB,EAAE,OAHlB;AAIH/C,QAAAA,SAAS,EAAE,KAJR;AAKH7wB,QAAAA,UAAU,EAAE,OALT;AAMH6zB,QAAAA,mBAAmB,EAAE,MANlB;AAOHC,QAAAA,+BAA+B,EAAE,QAP9B;AAQH/C,QAAAA,QAAQ,EAAE,OARP;AASHgD,QAAAA,cAAc,EAAE,MATb;AAUHC,QAAAA,YAAY,EAAE,MAVX;AAWHC,QAAAA,cAAc,EAAE,MAXb;AAYHC,QAAAA,MAAM,EAAE,OAZL;AAaHC,QAAAA,MAAM,EAAE,OAbL;AAcHpK,QAAAA,GAAG,EAAE,KAdF;AAeHrf,QAAAA,WAAW,EAAE,IAfV;AAgBH0pB,QAAAA,0BAA0B,EAAE,SAhBzB;AAiBHC,QAAAA,gBAAgB,EAAE,WAjBf;AAkBHt0B,QAAAA,MAAM,EAAE,IAlBL;AAmBHu0B,QAAAA,MAAM,EAAE,IAnBL;AAoBHC,QAAAA,mBAAmB,EAAE,MApBlB;AAqBHC,QAAAA,qBAAqB,EAAE,MArBpB;AAsBHC,QAAAA,eAAe,EAAE,MAtBd;AAuBHC,QAAAA,iBAAiB,EAAE,MAvBhB;AAwBHC,QAAAA,SAAS,EAAE,QAxBR;AAyBHC,QAAAA,SAAS,EAAE,QAzBR;AA0BH/e,QAAAA,IAAI,EAAE,IA1BH;AA2BHgc,QAAAA,OAAO,EAAE,MA3BN;AA4BHC,QAAAA,OAAO,EAAE,MA5BN;AA6BH33B,QAAAA,SAAS,EAAE,MA7BR;AA8BH43B,QAAAA,cAAc,EAAE,MA9Bb;AA+BHtxB,QAAAA,OAAO,EAAE,IA/BN;AAgCHyK,QAAAA,GAAG,EAAE,KAhCF;AAiCHE,QAAAA,GAAG,EAAE,KAjCF;AAkCHypB,QAAAA,OAAO,EAAE,IAlCN;AAmCHC,QAAAA,kBAAkB,EAAE,IAnCjB;AAoCH9C,QAAAA,IAAI,EAAE,IApCH;AAqCH+C,QAAAA,eAAe,EAAE,MArCd;AAsCHC,QAAAA,MAAM,EAAE,GAtCL;AAuCHC,QAAAA,WAAW,EAAE,KAvCV;AAwCHxC,QAAAA,aAAa,EAAE,MAxCZ;AAyCHyC,QAAAA,6BAA6B,EAAE,QAzC5B;AA0CHjM,QAAAA,UAAU,EAAE,MA1CT;AA2CHiK,QAAAA,UAAU,EAAE,MA3CT;AA4CHC,QAAAA,MAAM,EAAE,KA5CL;AA6CHC,QAAAA,GAAG,EAAE,GA7CF;AA8CHC,QAAAA,MAAM,EAAE,IA9CL;AA+CHC,QAAAA,SAAS,EAAE,MA/CR;AAgDHC,QAAAA,UAAU,EAAE,MAhDT;AAiDHC,QAAAA,UAAU,EAAE,MAjDT;AAkDHC,QAAAA,YAAY,EAAE,OAlDX;AAmDH0B,QAAAA,WAAW,EAAE,MAnDV;AAoDHC,QAAAA,uBAAuB,EAAE,UApDtB;AAqDHC,QAAAA,4BAA4B,EAAE,WArD3B;AAsDHC,QAAAA,uCAAuC,EAAE,kBAtDtC;AAuDHC,QAAAA,oBAAoB,EAAE,MAvDnB;AAwDHC,QAAAA,uBAAuB,EAAE,OAxDtB;AAyDHC,QAAAA,KAAK,EAAE,GAzDJ;AA0DHC,QAAAA,KAAK,EAAE,MA1DJ;AA2DHC,QAAAA,QAAQ,EAAE,IA3DP;AA4DHC,QAAAA,QAAQ,EAAE,IA5DP;AA6DHC,QAAAA,WAAW,EAAE,IA7DV;AA8DHC,QAAAA,aAAa,EAAE,IA9DZ;AA+DH9qB,QAAAA,SAAS,EAAE,IA/DR;AAgEH+qB,QAAAA,eAAe,EAAE,IAhEd;AAiEHC,QAAAA,eAAe,EAAE,KAjEd;AAkEHC,QAAAA,gBAAgB,EAAE,OAlEf;AAmEHC,QAAAA,cAAc,EAAE,MAnEb;AAoEHC,QAAAA,eAAe,EAAE,MApEd;AAqEHC,QAAAA,YAAY,EAAE,MArEX;AAsEHC,QAAAA,aAAa,EAAE,MAtEZ;AAuEHC,QAAAA,cAAc,EAAE,MAvEb;AAwEHC,QAAAA,iBAAiB,EAAE,MAxEhB;AAyEHC,QAAAA,gBAAgB,EAAE,MAzEf;AA0EHC,QAAAA,oBAAoB,EAAE,OA1EnB;AA2EHC,QAAAA,mBAAmB,EAAE,OA3ElB;AA4EHC,QAAAA,oBAAoB,EAAE,OA5EnB;AA6EHC,QAAAA,mBAAmB,EAAE,OA7ElB;AA8EHC,QAAAA,UAAU,EAAE,UA9ET;AA+EHC,QAAAA,OAAO,EAAE;AA/EN;AAjUD;AA/vCF;AADc,CAAf", "sourcesContent": ["//export default {\r\nexport const locale = {\r\n  lang: {\r\n    // 公共\r\n    common: {\r\n      //============== getway ========\r\n      consumerError: '不允許授權此操作，請聯絡系統管理員。',\r\n      consumerSignatureError: '簽名驗證失敗，請聯絡系統管理員。',\r\n      invalidUserOrPassword: 'User or password is invalid',\r\n      badGateway: 'service error',\r\n      monitor20: '軌跡監控',\r\n      monitor21: '事件監控',\r\n      monitor22: '母嬰同室',\r\n      monitor23: '長照中心',\r\n      monitor24: '生理偵測',\r\n      monitor25: '病房監控',\r\n      monitor26: '管制監控',\r\n      monitor27: 'IEQ',\r\n      //==============\r\n      confirmDeleteSelection: '删除勾选选项?',\r\n      confirmDelete: '删除确认',\r\n      //==============\r\n      appMainTitle: '智匯網護理支援',\r\n      systemTitle: '智匯網護理支援標準版',\r\n      indoorRealTimePositioningSystem: '智匯網護理支援標準版',\r\n      traditionalChinese: '繁体中文',\r\n      simplifiedChinese: '简体中文',\r\n      english: '英文',\r\n      introduction: '简介',\r\n      people: '人',\r\n      piece: '件',\r\n      // 公共\r\n      page: '页',\r\n      import: '汇入',\r\n      export: '汇出',\r\n      exportExcel: '汇出格式为Excel',\r\n      newlyIncreased: '新增',\r\n      search: '搜寻',\r\n      delete: '删除',\r\n      removePair: '解除配对',\r\n      list: '列表',\r\n      confirm: '确定',\r\n      cancel: '取消',\r\n      homePage: '首页',\r\n      reset: '重置',\r\n      edit: '编辑',\r\n      upload: '上传',\r\n      storage: '储存',\r\n      total: '共',\r\n      pen: '笔',\r\n      date: '日期',\r\n      time: '时间',\r\n      equipment: '设备',\r\n      noDataText: '暂无数据',\r\n      inputKeyword: '请输入关键字',\r\n      keywordEvent: '请输入事件记录',\r\n      pleaseSelect: '请选择',\r\n      pleaseSearch: '搜寻...',\r\n      chooseStartTime: '请选择起始日期',\r\n      chooseEndTime: '请选择结束日期',\r\n      exportFormatForExcel: '汇出格式爲Excel',\r\n      seeMoreEvents: '看更多事件',\r\n      return: '返回',\r\n      all: '全部',\r\n      marked: '已标记',\r\n      notMarked: '未标记',\r\n      cancelled: '已取消',\r\n      hasBeenIdentified: '已确定',\r\n      deleteConfirm: '删除确认',\r\n      deleteItemConfirm: '是否确认删除此项目？',\r\n      // 提示\r\n      prompt: '提示',\r\n      selectEventToDelete: '请选择要删除的事件',\r\n      notUsePermission: '您没有使用此功能所需的权限。',\r\n      deleteSuccessful: '删除成功。',\r\n      removePairSuccessful: '解除配对成功。',\r\n      addSuccessful: '新增成功。',\r\n      storageSuccessful: '储存成功。',\r\n      exportSuccessful: '汇出成功。',\r\n      importSuccessful: '汇入成功。',\r\n      searchSuccessful: '搜寻成功。',\r\n      modifySuccessful: '修改成功。',\r\n      syncSuccessful: '同步成功。',\r\n      exportFail: '汇出栏位验证失败。',\r\n      searchFail: '搜寻栏位验证失败。',\r\n      // header\r\n      backgroundManagement: '后台管理',\r\n      personalData: '的个人资料',\r\n      personal: '个人',\r\n      operation: '操作',\r\n      changeThePassword: '变更密码',\r\n      logout: '登出',\r\n      pleaseEnterOriginalPassword: '请输入原密码',\r\n      pleaseEnterNewPassword: '请输入新密码',\r\n      pleaseEnterNewPasswordAgain: '请再次输入新密码',\r\n      passwordError: '原密码输入错误，请再次确认。',\r\n      tooManyResourcesError: '抱歉，汇出笔数超过限制，请调整查询条件。',\r\n      passwordErrorPrompt: '新密码长度为8-15字元，至少须含英文字母及数字。',\r\n      eventRecordErrorPrompt: '事件记录字数长度必须介于0和256。',\r\n      samePasswordPrompt: '新密码与原密码相同，请重新设定。',\r\n      twoNewPasswordsAreNotConsistent: '两次填入的新密码不一致。',\r\n      planeNoUploadCoordinatesError: '此平面没有上传地图，无法进行座标设定。',\r\n      noInformationOnFloor: '此楼层目前没有资料可显示。',\r\n      selectMap: '请选择平面图',\r\n      floorPlan: '平面图',\r\n      selectDate: '请选择日期',\r\n      successful: '成功',\r\n      fillInEventRecord: '请填入事件记录',\r\n      warning: '警告',\r\n      error: '错误',\r\n      invalidFormat: '格式错误。',\r\n      contactSystemAdministrator: '系统忙碌中，请联络系统管理员。',\r\n      modificationSuccessfulLoginAgain: '修改成功，请重新登录。',\r\n      loginPeriodExpires: '登入有效期限已过，请重新登入。',\r\n      selectedEventDoesNot: '选择的事件不存在，请重新整理页面。',\r\n      networkProblemPleaseRefreshPage: '网路问题，请重新整理页面。',\r\n      networkProblem: '网路连线缓慢，请确认网路状态或稍后再试。',\r\n      passwordUnavailableError: '目前邮件伺服器异常，无法送帐号通知信件给您，请稍后再试。',\r\n      accessCodeUnavailableError: '目前邮件伺服器异常，无法送重设密码的信件给您，请稍后再试。',\r\n      storageUnavailableError: '图片上传失败，请确认格式或是档案大小是否正确。',\r\n      accountAlreadyExists: '帐号已经存在',\r\n      dataAlReadyExists: '资料已经存在',\r\n      deviceObjectExistsError: '选择的装置已经被绑定使用，请试试其他装置。',\r\n      objectDeviceExistsError: '选择的物件已经被绑定使用，请试试其他物件。',\r\n      accountNotFoundError: '帐号或密码错误',\r\n      resourceNotFoundError: '资料不存在',\r\n      planeNotFoundError: '选择的区域不存在，请重新整理页面。',\r\n      operationIsNotAllowed: '不允许此项操作，请联络系统管理员。',\r\n      selectAtLeastOneAttribute: '请选择至少一种属性。',\r\n      itNotExist: '选择的事件不存在。',\r\n      reorganizeThePage: '请重新整理页面。',\r\n      selectedEventState: '请选择事件状态。',\r\n      fillInIncidentRecord: '请填写事件记录',\r\n      invalidRequestError: '参数错误或上传文件格式错误',\r\n      badPasswordError: '密码长度须介于8-15字元之间，至少须含英文字母及数字。',\r\n      badEmailError: '电子邮件必须符合电子邮件格式。',\r\n      badPhoneError: '电话格式错误。',\r\n      invalidAccessTaskError: '此任务无效或已过期，请重新执行此操作。',\r\n      uneventful: '解除事件成功。',\r\n      removeEvent: '解除事件',\r\n      event: '事件',\r\n      trajectory: '轨迹',\r\n      locateObject: '定位对象',\r\n      templateHelp: '求救模板',\r\n      // table\r\n      objectsDisplayed: '物件图示',\r\n      serialNumber: '编号',\r\n      name: '名称',\r\n      category: '类别',\r\n      role: '角色',\r\n      group: '群组',\r\n      currentPosition: '目前位置',\r\n      latestPositioningTime: '最新定位时间',\r\n      modifiesAt: '最近更新时间',\r\n      byTheTime: '發起时间',\r\n      plane: '位置',\r\n      eventClassification: '分类',\r\n      sponsorName: '發起者名称',\r\n      initiator: '發起者类别/角色',\r\n      eventLog: '记录',\r\n      eventState: '状态',\r\n      viewTheDayTrack: '检视当日轨迹',\r\n      viewCurrentLocation: '检视目前位置',\r\n      // menu\r\n      monitoring: '即时监控',\r\n      fenceMonitoring: '轨迹监控',\r\n      mmWaveMonitoring: '病房监控',\r\n      monitor01: '母婴监控',\r\n      monitor02: '管制监控',\r\n      monitor03: '生理监控',\r\n      monitor04: '感染监控',\r\n      managePermissions: '管理权限',\r\n      accountManagement: '系统帐号设定',\r\n      roleManagement: '系统角色设定',\r\n      firmwareUpdate: '韧体更新',\r\n      positioningSettings: '场域管理',\r\n      planePosition: '平面图资设定',\r\n      baseStation: '无线基站设定',\r\n      anchor: '锚点设定',\r\n      guard: 'Guard',\r\n      no: '无',\r\n      dataManagement: '资料管理',\r\n      objectFeatures: '定位属性设定',\r\n      objectData: '定位对象设定',\r\n      deviceInformation: '装置资料设定',\r\n      eventDefinition: '事件定义设定',\r\n      guardSetting: 'Guard设定',\r\n      otaUpdate: '韧体更新',\r\n      licenseManagement: '授权管理',\r\n      historyReport: '历史报表',\r\n      temporarilyNoData: '暂无数据',\r\n      cameraSetting: '摄影机设定',\r\n      logRecording: '装置定位日誌',\r\n      inventory: '资产盘点系统',\r\n      systemManagement: '系统管理',\r\n      systemConfig: '系统参数设定',\r\n      systemSWVersion: '系统软体版本',\r\n      mmWave: '毫米波',\r\n      fence: '电子围篱',\r\n      // event\r\n      helpEvent: '求救警示',\r\n      enterTheNotice: '进入通知',\r\n      leaveTheNotice: '离开通知',\r\n      theNumberOfControl: '人数管制',\r\n      lowBatteryWarning: '低电量警示',\r\n      stationAbnormalWarning: '基站异常警示',\r\n      stayTimeout: '停留逾时警示',\r\n      regularRound: '巡查提醒',\r\n      leaveBed: '离床警示',\r\n      abnormalGuard: 'Guard异常警示',\r\n      sensorDataDriven: '数据自订事件',\r\n      fallDetection: '跌倒警示_毫米波',\r\n      stayTimeoutMMWave: '停留逾时警示_毫米波',\r\n      leaveBedMMWave: '离床提醒_毫米波',\r\n      getUp: '起身提醒_毫米波',\r\n      abnormalBreath: '呼吸异常警示_毫米波',\r\n      wetUrine: '尿湿提醒',\r\n      // other\r\n      help: '求救',\r\n      untreated: '未处理',\r\n      inTheProcessing: '处理中',\r\n      hasLift: '已解除',\r\n      personnel: '人员',\r\n      map: '地图',\r\n      eventName: '事件名称',\r\n      objectsName: '物件名称',\r\n      order: '顺序',\r\n      personnelDistribution: '人员分佈',\r\n      equipmentDistribution: '设备分佈',\r\n      area: '区域',\r\n      subRegion: '子区域',\r\n      factory: '厂区',\r\n      building: '建筑物',\r\n      floor: '楼层',\r\n      areaName: '区域名称',\r\n      factoryName: '厂区名称',\r\n      buildingName: '建筑物名称',\r\n      floorName: '楼层名称',\r\n      subRegionName: '子区域名称',\r\n      geoCluster: '地理群集', // Yujin\r\n      coordinate: '座标',\r\n      hasAttachment: '已连线',\r\n      offline: '离线',\r\n      planeName: '平面图名称',\r\n      originalCoordinates: '原始座标',\r\n      currentCoordinates: '目前座标',\r\n      hasChange: '已更动',\r\n      mapHint: '无地图，请进行平面图搜索',\r\n      archives: '档案',\r\n      pairingDevice: '配对装置',\r\n      inputCode: '请输入编号',\r\n      inputName: '请输入名称',\r\n      inputCodeError: '输入的编号有误或重複',\r\n      selectObject: '请选择定位对象',\r\n      selectDeleteObject: '请选择删除的定位对象',\r\n      selectGroup: '请选择群组',\r\n      onTheGround: '地上',\r\n      underground: '地下',\r\n      okToDoThis: '确定执行此操作？',\r\n      okToDeleteSelection: '确定删除选择项目？',\r\n      whetherToReset: '是否重置?',\r\n      selectTheObject: '请选择物件。',\r\n      noData: '暂无资料',\r\n      limitDeleteRecords: '批次删除的笔数限制最多为100笔。',\r\n      planeNotFound: '选择的区域不存在，请重新整理页面。',\r\n      selectPlane: '请选择区域',\r\n      chooseType: '请选择类型',\r\n      uploadTime: '上传时间',\r\n      yes: '是',\r\n      nay: '否',\r\n      type: '类型',\r\n      document: '文档',\r\n      enabled: '啓用',\r\n      notEnabled: '不啓用',\r\n      selectedItems: '选择的项目',\r\n      planeDataExists: '存在平面资料',\r\n      notAllowedDelete: '不允许删除',\r\n      badConfiguration: '参数格式不正确',\r\n      fullScreen: '全萤幕',\r\n      exitFullScreen: '退出全萤幕',\r\n      noShortcuts: '暂无捷径'\r\n    },\r\n    // 登录\r\n    login: {\r\n      account: '帐号',\r\n      password: '密码',\r\n      resetPassword: '重置密码',\r\n      login: '登入',\r\n      accountRequiredVerification: '请填入帐号。',\r\n      passwordRequiredVerification: '请填入密码。',\r\n      passwordPatternVerification: '密码格式错误。'\r\n    },\r\n    // resetPassword\r\n    resetPassword: {\r\n      resetPassword: '重置密码',\r\n      pleaseResetAccountPassword: '请重设您的帐号密码',\r\n      inputNewPassword: '请输入新密码',\r\n      inputNewPasswordAgain: '请再次输入新密码',\r\n      send: '送出',\r\n      newPasswdRequiredVerification: '请填入新密码。',\r\n      newPasswdRequiredVerificationAgain: '请再次填入新密码。',\r\n      newPasswdPatternVerification: '新密码长度为8-15字元，至少须含英文字母及数字。',\r\n      newPasswdDiffer: '两次填入的新密码不一致。',\r\n      resetPasswordSuccessful: '重置密码成功，请重新登入。',\r\n      inputEmail: '请输入您的电子邮件',\r\n      inputVcode: '输入图片验证码',\r\n      emailRequiredVerification: '请填入电子邮件。',\r\n      emailTypeVerification: '电子邮件必须符合电子邮件格式。',\r\n      vcodeRequiredVerification: '请填入图片验证码。',\r\n      vcodeError: '图片验证码不正确。',\r\n      sendEmailSuccessful: '我们已经寄送重置密码的信件给您！',\r\n      accountNotFoundPrompt: '选择的帐号不存在，请重新整理页面。'\r\n    },\r\n    personal: {\r\n      account: '帐号',\r\n      name: '姓名',\r\n      email: '电子邮件',\r\n      phone: '电话',\r\n      role: '角色',\r\n      userEmailExistsVerification: '电子邮件已经被使用，请试试其他电子邮件。',\r\n      accountRequiredVerification: '请填入帐号。',\r\n      nameRequiredVerification: '请填入姓名。',\r\n      nameTypeVerification: '姓名字数长度必须介于0和64。',\r\n      emailRequiredVerification: '请填入电子邮件。',\r\n      emailTypeVerification: '电子邮件必须符合电子邮件格式。',\r\n      phoneRequiredVerification: '请填入电话。',\r\n      phonePatternVerification: '电话格式错误。',\r\n      userRoleRequiredVerification: '请选择系统角色。'\r\n    },\r\n    // dashBoard\r\n    dashBoard: {\r\n      device: '装置',\r\n      totalCount: '总数',\r\n      normal: '正常',\r\n      lowBattery: '低电量',\r\n      lowBatteryEvent: '低电量事件',\r\n      baseStation: '基站',\r\n      offline: '离线',\r\n      no: '无',\r\n      stationAbnormal: '基站异常',\r\n      more: '更多',\r\n      guardAbnormal: 'Guard异常',\r\n      managementShortcut: '管理捷径',\r\n      course: '历程',\r\n      deviceName: '装置名称',\r\n      pairObject: '配对物件',\r\n      battery: '电量',\r\n      latestBatteryTime: '电量回报时间',\r\n      lowBatteryNotice: '低电量通知',\r\n      stationName: '基站名称',\r\n      locationRegion: '所在区域',\r\n      connectionState: '连线状态',\r\n      startsAt: '發起时间',\r\n      abnormalNotice: '异常通知',\r\n      guardName: 'Guard名称',\r\n      operationDate: '操作日期',\r\n      ipPosition: 'IP位置',\r\n      operationRecord: '操作记录',\r\n      noPlaneInformation: '暂无平面相关讯息。',\r\n      noCoordinateInformation: '暂无座标相关讯息。',\r\n      regionNotUploadPlaneMap: '选择区域没有上传平面图，无法显示地图。',\r\n      monitorManagerment: '监控管理',\r\n      loginSystem: '登入系统',\r\n      logoutSystem: '退出系统',\r\n      editPlane: '编辑平面位置',\r\n      applicationIntegration: '应用集成'\r\n    },\r\n    // 即时监控\r\n    monitoring: {\r\n      clickOnClosed: '点选闭合',\r\n      clickOnA: '点选展开',\r\n      dragLeftRightToResizeTheWindow: '左右拖拉可调整视窗大小',\r\n      DeEventOperation: '解除事件操作',\r\n      DetermineUnselectedEvent: '确定解除选择事件',\r\n      searchMonitoringConditions: '请搜寻监控条件',\r\n      mandatoryFieldArea: '区域、物件属性为必选栏位',\r\n      unableToDisplayTheMap: '选择区域没有上传平面图，无法显示地图',\r\n      storeFailure: '储存失败',\r\n      storeSuccessful: '储存成功',\r\n      noPlaneRelatedConsultation: '暂无平面相关资讯',\r\n      noCoordinateConsultation: '暂无座标相关资讯',\r\n      noCoordinateOrPlaneRelatedConsultation: '暂无座标或平面相关资讯',\r\n      anUnexaminedObject: '暂无可查看的物件',\r\n      selectProjectToTerminated: '请选择要解除事件的项目',\r\n      validatePlaneErrorInfo: '区域阶层请选择 \"厂区\" 或 \"楼层\" 或 \"区域\"',\r\n      noFlatMapData: '无平面地图资料',\r\n      keyword: '关键字',\r\n      download: '下载',\r\n      recordPreview: '录影档预览',\r\n      fileId: '档案编号',\r\n      cameraCode: '摄影机编号',\r\n      planeCode: '位置',\r\n      filename: '档案名称'\r\n    },\r\n    // 系统帐号设定\r\n    accountManagement: {\r\n      account: '帐号',\r\n      name: '姓名',\r\n      email: '电子邮件',\r\n      phone: '电话',\r\n      role: '角色',\r\n      modifiesAt: '最近更新时间',\r\n      permission: '权限',\r\n      deleteAccountPrompt: '请选择要删除的帐号。',\r\n      accountNotFoundPrompt: '选择的帐号不存在，请重新整理页面。',\r\n      addSystemAccount: '新增系统帐号',\r\n      inputAccount: '请输入帐号 *',\r\n      inputPassword: '请输入密码 *',\r\n      inputName: '请输入姓名 *',\r\n      inputEmail: '请输入电子邮件 *',\r\n      inputPhone: '请输入电话 *',\r\n      selectRole: '请选择系统角色 *',\r\n      accountRequiredVerification: '请填入帐号。',\r\n      accountTypeVerification: '帐号字数长度必须介于0和20。',\r\n      userAccountExistsVerification: '帐号已经被使用，请试试其他帐号。',\r\n      passwordRequiredVerification: '请填入密码。',\r\n      passwordPatternVerification: '密码格式错误。',\r\n      nameRequiredVerification: '请填入姓名。',\r\n      nameTypeVerification: '姓名字数长度必须介于0和64。',\r\n      emailRequiredVerification: '请填入电子邮件。',\r\n      emailTypeVerification: '电子邮件必须符合电子邮件格式。',\r\n      userEmailExistsVerification: '电子邮件已经被使用，请试试其他电子邮件。',\r\n      phoneRequiredVerification: '请填入电话。',\r\n      phonePatternVerification: '电话格式错误。',\r\n      userRoleRequiredVerification: '请选择系统角色。',\r\n      accountName: '帐号名称',\r\n      instantEventInspection: '即时事件检视',\r\n      localizingObjectInspection: '定位对象检视',\r\n      noInstantEvent: '暂无即时事件',\r\n      immediateEvent: '即时事件',\r\n      help: '求救警示',\r\n      enter: '进入通知',\r\n      leave: '离开通知',\r\n      numberControl: '人数管制',\r\n      lowBattery: '低电量警示',\r\n      abnormalStation: '基站异常警示',\r\n      leaveBed: '离床警示',\r\n      stayTimeout: '停留逾时警示',\r\n      regularRound: '巡查提醒',\r\n      sensorDataDriven: '数据自订事件',\r\n      selectMonitoringConditionPrompt: '至少选择一项监控条件，可複选！',\r\n      pleaseSelectCategory: '请选择类别',\r\n      pleaseSelectRole: '请选择角色',\r\n      pleaseSelectGroup: '请选择群组',\r\n      pleaseSelectRegion: '请选择区域',\r\n      categoryRequiredVerification: '请选择类别。',\r\n      roleRequiredVerification: '请选择角色。',\r\n      groupRequiredVerification: '请选择群组。',\r\n      regionRequiredVerification: '请选择区域。',\r\n      addLocationObject: '新增定位对象',\r\n      deleteMonitoringConditionPrompt: '至少要有一项监控条件。',\r\n      selectTypeRoleGroup: '类别/角色/群组，请择一输入。'\r\n    },\r\n    // 系统角色设定\r\n    roleManagement: {\r\n      roleCode: '角色编号',\r\n      roleName: '角色名称',\r\n      systemRoleCode: '系统角色编号',\r\n      systemRoleName: '系统角色名称',\r\n      selectDeleteCodes: '请勾选要删除的系统角色。',\r\n      userRoleNotFound: '选择的系统角色不存在，请重新整理页面。',\r\n      userConflicts: '选择的项目存在系统帐号资料，不允许删除。',\r\n      view: '显示',\r\n      addRole: '新增系统角色',\r\n      enterRoleCode: '请填入系统角色编号',\r\n      enterRoleName: '请填入系统角色名称',\r\n      permissionSetting: '权限设定',\r\n      codeRuleValidate: '系统角色编号字数长度必须介于 0 和 20。',\r\n      nameRuleValidate: '系统角色名称字数长度必须介于 0 和 64。',\r\n      permissionRuleValidate: '请至少选择一个权限设定。',\r\n      moduleNotFound: '选择的权限设定不存在，请重新整理页面。',\r\n      userRoleCodeExists: '系统角色编号已经被使用，请试试其他系统角色编号。'\r\n    },\r\n    // 定位属性设定\r\n    objectFeatures: {\r\n      category: '类别',\r\n      role: '角色',\r\n      group: '群组',\r\n      categoryCode: '类别编号',\r\n      categoryName: '类别名称',\r\n      icon: '图示',\r\n      roleCode: '角色编号',\r\n      roleName: '角色名称',\r\n      groupCode: '群组编号',\r\n      groupName: '群组名称',\r\n      modifiesAt: '最近更新时间',\r\n      pleaseSelectDeleteCategory: '请选择要删除的类别',\r\n      pleaseSelectDeleteRole: '请选择要删除的角色',\r\n      pleaseSelectDeleteGroup: '请选择要删除的群组',\r\n      objectTypeNotFoundVerification: '选择的类别不存在，请重新整理页面。',\r\n      objectRoleNotFoundVerification: '选择的角色不存在，请重新整理页面。',\r\n      objectGroupNotFoundVerification: '选择的群组不存在，请重新整理页面。',\r\n      objectConflictsVerification: '选择的项目存在定位对象资料，不允许删除。',\r\n      eventConflictsVerification: '选择的项目存在事件定义资料，不允许删除。',\r\n      monitorConflicts: '选择的项目存在帐号权限设定资料，不允许删除。',\r\n      addLocationAttribute: '新增定位属性',\r\n      pleaseSelectIcon: '请选择图示',\r\n      pleaseSelectLocationAttribute: '请选择定位属性 *',\r\n      inputCode: '请输入编号 *',\r\n      inputName: '请输入名称 *',\r\n      locationAttributeRequiredVerification: '请选择定位属性。',\r\n      codeRequiredVerification: '请填入编号。',\r\n      codeTypeVerification: '编号字数长度必须介于0和20。',\r\n      nameRequiredVerification: '请填入名称。',\r\n      nameTypeVerification: '名称字数长度必须介于0和64。',\r\n      iconRequiredVerification: '请选择图示。',\r\n      objectCodeExistsVerification: '编号已经被使用，请试试其他编号。',\r\n      code: '编号',\r\n      name: '名称',\r\n      excelExampleDownload: 'Excel范例档下载',\r\n      pleaseSelectUploadImportFiles: '请选择要上传汇入的档案',\r\n      uploadFiles: '待上传档案：',\r\n      fileFormatForxls: '档案格式必须爲.xls',\r\n      systemReadsPens: '(系统最多读取200笔)',\r\n      uploadFileForExcel: '上传档案必须为Excel。',\r\n      pleaseSelectUploadFiles: '请选择上传档案。',\r\n      pleaseSelectALocationAttribute: '请选择一项定位属性。',\r\n      correctInformation: '正确汇入资料有',\r\n      errorInformation: '错误资料有',\r\n      errorCode: '错误编号有：',\r\n      locationAttributeName: '定位属性名称'\r\n    },\r\n    // 无线基站设定\r\n    baseStation: {\r\n      stationConfiguration: '基站配置',\r\n      sid: '基站序号',\r\n      name: '基站名称',\r\n      IP: 'IP位置',\r\n      planeName: '所在区域',\r\n      systemVersion: '系统版本',\r\n      appVersion: 'APK版本',\r\n      isAlive: '连线状态',\r\n      modifiesAt: '系统更新时间',\r\n      correctImportedError: '正确汇入资料有',\r\n      errorInformation: '错误资料有',\r\n      errorCode: '错误编号有',\r\n      planePosition: '平面位置',\r\n      planeLayerAtLeastError: '平面图层至少选择至 楼层/区域/子区域',\r\n      unableCoordinatePositioning: '此平面没有上传地图，无法进行座标设定',\r\n      selectCorrespondingClass: '请先选择对应的阶层',\r\n      XCoordinate: 'X座标',\r\n      YCoordinate: 'Y座标',\r\n      flatArea: '平面地区',\r\n      inputSid: '请输入基站序号',\r\n      inputName: '请输入名称',\r\n      inputSidCode: '请输入SID编号',\r\n      lastConnectionTime: '最后连线时间',\r\n      fieldInformation: '栏位资料',\r\n      selectTheArea: '请选择所在区域',\r\n      selectTheUploadedFile: '请选择上传的档案',\r\n      addBaseStation: '新增基站',\r\n      baseStationIncorrectDuplicatedError: '输入的基站序号有误或重複',\r\n      sidCodeLengthError: 'SID编号长度必须介于0和20',\r\n      nameLengthError: '名称长度必须介于0和20',\r\n      addCamera: '新增摄影机',\r\n      prioritySeq: '优先顺序',\r\n      cameraId: '摄影机ID',\r\n      cameraNo: '摄影机编号',\r\n      cameraName: '摄影机名称'\r\n    },\r\n    // 装置资料设定\r\n    deviceInformation: {\r\n      fieldFilterToolTip: '下拉框中没有被选中的列会被隐藏掉',\r\n      deviceMAC: '装置MAC',\r\n      deviceName: '装置名称',\r\n      locationObject: '定位对象',\r\n      pairObject: '配对物件',\r\n      locationRegion: '所在区域',\r\n      locationTime: '定位时间',\r\n      residualBattery: '剩馀电量',\r\n      pm25: 'PM2.5浓度',\r\n      tvoc: 'TVOC浓度',\r\n      temperature: '温度',\r\n      humidity: '湿度',\r\n      pm25GetTime: 'PM2.5浓度回报时间',\r\n      tvocGetTime: 'TVOC浓度回报时间',\r\n      temperatureGetTime: '温度回报时间',\r\n      humidityGetTime: '湿度回报时间',\r\n      heartRate: '心律',\r\n      latestHeartRateTime: '心律回报时间',\r\n      sbp: '收缩压',\r\n      latestSbpTime: '收缩压回报时间',\r\n      dbp: '舒张压',\r\n      latestDbpTime: '舒张压回报时间',\r\n      connectionState: '连线状态',\r\n      latestBatteryTime: '电量回报时间',\r\n      softwareVersion: '软体版本',\r\n      modifiesAt: '最近更新时间',\r\n      deviceNotFoundVerification: '选择的装置不存在，请重新整理页面。',\r\n      pleaseSelectDeleteDevice: '请选择要删除的装置。',\r\n      pleaseSelectRemovePairItem: '请选择要解除配对的项目。',\r\n      pleaseConfirmOperationCorrectness: '请确认项目操作是否正确。',\r\n      regionNotUploadPlaneMap: '选择区域没有上传平面图，无法显示地图。',\r\n      noPlaneInformation: '暂无平面相关讯息。',\r\n      noCoordinateInformation: '暂无座标相关讯息。',\r\n      addDeviceInformation: '新增装置资料',\r\n      inputDeviceMAC: '请输入装置MAC *',\r\n      inputDeviceName: '请输入装置名称 *',\r\n      selectPairObject: '请选择配对物件',\r\n      pidRequiredVerification: '请填入装置MAC。',\r\n      pidTypeVerification: '装置MAC字数长度必须介于0和20。',\r\n      nameRequiredVerification: '请填入装置名称。',\r\n      nameTypeVerification: '装置名称字数长度必须介于0和32。',\r\n      objectNotFoundVerification: '选择的物件不存在，请重新整理页面。',\r\n      objectDeviceExistsVerification: '选取的物件已经被绑定使用，请试试其他物件。',\r\n      devicePidExistsVerification: '装置MAC已经被使用，请试试其他装置MAC。',\r\n      excelExampleDownload: 'Excel范例档下载',\r\n      pleaseSelectUploadImportFiles: '请选择要上传汇入的档案',\r\n      uploadFiles: '待上传档案：',\r\n      fileFormatForxls: '档案格式必须爲.xls',\r\n      systemReadsPens: '(系统最多读取200笔)',\r\n      uploadFileForExcel: '上传档案必须为Excel。',\r\n      pleaseSelectUploadFiles: '请选择上传档案。',\r\n      correctInformation: '正确汇入资料有',\r\n      errorInformation: '错误资料有',\r\n      errorCode: '错误编号有：',\r\n      currentPairDevice: '目前配对装置：'\r\n    },\r\n    // 锚地设定\r\n    anchor: {\r\n      anchorConfiguration: '锚点配置',\r\n      anchorMac: '锚点Mac',\r\n      anchorName: '锚点名称',\r\n      inputAnchorMac: '请输入锚点Mac',\r\n      inputPIDCode: '请输入PID编号',\r\n      addAnchor: '新增锚点',\r\n      anchorMacIncorrectDuplicatedError: '输入的PID编号有误或重複',\r\n      anchorMacLengthError: '锚点Mac长度必须介于0和20'\r\n    },\r\n    // Guard\r\n    guard: {\r\n      guardConfiguration: 'Guard配置',\r\n      pleaseConfirmOperationCorrectness: '请确认项目操作是否正确。',\r\n      pleaseSelectRemovePairItem: '请选择要解除配对的项目。',\r\n      code: '序号',\r\n      guardName: 'Guard名称',\r\n      pairObject: '配对对象',\r\n      region: '区域',\r\n      detectMode: '侦测模式',\r\n      enableOrClose: '启用／关闭',\r\n      connectionState: '连线状态',\r\n      synchronousState: '同步状态',\r\n      modifiesAt: '最近更新时间',\r\n      leaveBed: '离床侦测',\r\n      stayTimeout: '逾时侦测',\r\n      regularRound: '巡察提醒',\r\n      enable: '启用',\r\n      close: '关闭',\r\n      normal: '正常',\r\n      offline: '离线',\r\n      synchronizing: '同步中',\r\n      synchronized: '已同步',\r\n      dataSynchronizationUpdate: '资料同步更新中，请稍后再试。',\r\n      guardNotFoundVerification: '选择的Guard不存在，请重新整理页面。',\r\n      pleaseSelectDeleteGuard: '请选择要删除的Guard。',\r\n      pleaseSelectPlaneMap: '请选择平面图',\r\n      search: '搜索',\r\n      planeLayerAtLeastError: '平面图层至少选择至 楼层/区域/子区域',\r\n      planeNoUploadCoordinatesError: '此平面没有上传地图，无法进行座标设定。',\r\n      pleaseSelectCorrespondingClass: '请先选择对应的阶层。',\r\n      excelExampleDownload: 'Excel范例档下载',\r\n      pleaseSelectUploadImportFiles: '请选择要上传汇入的档案',\r\n      uploadFiles: '待上传档案：',\r\n      fileFormatForxls: '档案格式必须爲.xls',\r\n      systemReadsPens: '(系统最多读取200笔)',\r\n      uploadFileForExcel: '上传档案必须为Excel。',\r\n      pleaseSelectUploadFiles: '请选择上传档案。',\r\n      correctInformation: '正确汇入资料有',\r\n      errorInformation: '错误资料有',\r\n      errorCode: '错误编号有：',\r\n      addGuard: '新增Guard',\r\n      basicInformation: '基本资料',\r\n      inputCode: '请输入序号 *',\r\n      inputName: '请输入名称 *',\r\n      pleaseSelectPairObject: '请选择配对对象',\r\n      pleaseSelectRegion: '请选择区域',\r\n      pleaseSelectDetectMode: '请选择侦测模式',\r\n      bodyTemp: '温差阀值',\r\n      bodySize: '人／物体大小阀值',\r\n      bedWidth: '床宽(m)',\r\n      bedLength: '床长(m)',\r\n      ceilingHeight: '天花板高度(m)',\r\n      stopAlarmTime: '停留报警时间(s)',\r\n      bathroomLength: '浴室长(m)',\r\n      bathroomWidth: '浴室宽(m)',\r\n      intervalTime: '每次巡视时间间隔(s)',\r\n      ipSetting: 'IP设定',\r\n      rawDataCollectionSetting: 'Raw Data收集设定',\r\n      ipCameraEnableOrClose: 'IP Camera启用／关闭',\r\n      ipCameraCircleFrequency: 'IP Camera取圆频率(ms)',\r\n      logUploadFrequency: 'log上传频率(sec)',\r\n      logPackingSize: 'log打包大小(KB)',\r\n      codeRequiredVerification: '请填入序号。',\r\n      codeTypeVerification: '序号字数长度必须介于0和20。',\r\n      nameRequiredVerification: '请填入名称。',\r\n      nameTypeVerification: '名称字数长度必须介于0和64。',\r\n      bodyTempRequiredVerification: '请填入温差阀值。',\r\n      bodySizeRequiredVerification: '请填入人／物体大小阀值。',\r\n      bedWidthRequiredVerification: '请填入床宽度。',\r\n      bedLengthRequiredVerification: '请填入床长度。',\r\n      ceilingHeightRequiredVerification: '请填入天花板高度。',\r\n      stopAlarmTimeRequiredVerification: '请填入停留报警时间。',\r\n      bathroomLengthRequiredVerification: '请填入浴室长度。',\r\n      bathroomWidthRequiredVerification: '请填入浴室宽度。',\r\n      intervalTimeRequiredVerification: '请填入每次巡视时间间隔。',\r\n      bodyTempTypeVerification: '温差阀值必须介于0和15。',\r\n      bodySizeTypeVerification: '人／物体大小阀值必须介于0和225。',\r\n      bedWidthTypeVerification: '床宽度必须介于0和15。',\r\n      bedLengthTypeVerification: '床长度必须介于0和15。',\r\n      ceilingHeightTypeVerification: '天花板高度必须介于0和15。',\r\n      stopAlarmTimeTypeVerification: '停留报警时间必须介于0和65535。',\r\n      bathroomLengthTypeVerification: '浴室长度必须介于0和15。',\r\n      bathroomWidthTypeVerification: '浴室宽度必须介于0和15。',\r\n      intervalTimeTypeVerification: '每次巡视时间间隔必须介于0和65535。',\r\n      objectNotFoundVerification: '选择的对象不存在，请重新整理页面。',\r\n      objectGuardExistsVerification: '选取的对象已经被绑定使用，请试试其他对象。',\r\n      guardCodeExistsVerification: '序号已经被使用，请试试其他序号。',\r\n      columnMustNumber: '该栏位必须是数字。',\r\n      columnMustPositiveInteger: '该栏位必须是正整数。',\r\n      leastSocketServerIp: '至少要有一项Socket Server IP。',\r\n      enableFusionGuardMustItem: '为启用Fusion Guard的必填项目！',\r\n      xCoordinate: 'X座标',\r\n      yCoordinate: 'Y座标',\r\n      basicInformationMustCompleteForEnableGuard: '基本资料必须填写完整，才能启用Guard！',\r\n      currentPairObject: '目前配对对象：',\r\n      planeNotFoundVerification: '选择的区域不存在，请重新整理页面。',\r\n      needPortField: '格式错误，需要带有port栏位。'\r\n    },\r\n    // 定位对象设定\r\n    objectData: {\r\n      guard: '配对Guard',\r\n      objectNotFound: '选择的定位对象不存在。',\r\n      objectTypeNotFound: '选择的类别不存在。',\r\n      objectRoleNotFound: '选择的角色不存在。',\r\n      objectGroupNotFound: '选择的群组不存在。',\r\n      deviceNotFound: '选择的配对装置不存在。',\r\n      guardNotFound: '选择的配对Guard不存在。',\r\n      deviceObjectExists: '选择的配对装置已经绑定其他定位对象。',\r\n      guardObjectExists: '选择的配对Guard已经绑定其他定位对象。'\r\n    },\r\n    // 平面资图配置\r\n    planePosition: {\r\n      sitePlanConfig: '平面配置',\r\n      addSitePlanConfig: '新增平面位置',\r\n      updateFileMap: '请上传平面图',\r\n      updateOneFileMap: '请上传一张平面图',\r\n      flatPositionError: '请选择要建立的平面位置',\r\n      flatFactoryError: '请选择所属对应厂区',\r\n      flatBuildingError: '请选择所属对应建筑物',\r\n      flatFloorError: '请选择所属对应楼层',\r\n      flatRegionError: '请选择所属对应区域',\r\n      flatCode: '平面图编号',\r\n      flatName: '平面图名称',\r\n      flatCodeError: '平面图编号不能为空',\r\n      flatNameError: '平面图名称不能为空',\r\n      selectTheDeletedObject: '请选择删除的物件',\r\n      delSelectItemsError: '这个平面已经有绑定基站，要删除这个平面之前要先删除绑定的基站。',\r\n      stationConflictsError: '选择的项目存在基站资料，不允许删除。',\r\n      eventConflictsError: '选择的项目存在事件定义资料，不允许删除。',\r\n      planeConflictsError: '选择的项目存在平面资料，不允许删除。',\r\n      anchorConflicts: '选择的项目存在锚点资料，不允许删除。',\r\n      monitorConflicts: '选择的项目存在使用者监控资料，不允许删除。',\r\n      positioningPlanePosition: '定位平面位置',\r\n      selectCorrespondingLevelError: '请选择所属对应的阶层',\r\n      readjustStationAnchorPoint: '平面地区已更动，需要重新调整基站和锚点的配置。',\r\n      dragNoticeError: '请注意，平面位置更动后，需要重新调整基站和锚点的配置。',\r\n      savePromptingError: '执行此动作后，您将需要重新调整基站和锚点的配置，请确认是否执行？',\r\n      deletePlanesChildPositionError: '请注意，已经移除平面上的基站和锚点。',\r\n      isPoptip: '您确定要重置此项目?',\r\n      establishOrder: '建立顺序为厂区>建筑物>楼层>区域>子区域',\r\n      establishAtLeastOneFactoryAreaFirst: '请先至少建立一厂区',\r\n      establishAtLeastOneBuildingAreaFirst: '请先至少建立一建筑物',\r\n      establishAtLeastOneFloorAreaFirst: '请先至少建立一楼层',\r\n      establishAtLeastOneSubRegionAreaFirst: '请先至少建立一区域',\r\n      selectAndFillInFloor: '请选择并填入楼层',\r\n      floorInputCaveat: '请填入1-99任一数字',\r\n      fieldInformationCaveat: '请填写栏位资料',\r\n      zoomRatioMustTheSame: '图档放大缩小的倍率必须一致',\r\n      validatePlaneCodeError: '请填入平面图编号',\r\n      planeCodeTypeError: '输入的平面图编号有误或重複',\r\n      planeCodeTypeLengthError: '平面图编号字数长度必须介于 0 和 20。',\r\n      selectFool: '请选择楼层',\r\n      validateFloorError: '选择的楼层已存在，请试试建立其他楼层。',\r\n      planeNameError: '请填入平面图名称',\r\n      planeNameLengthError: '平面图名称字数长度必须介于 0 和 64。',\r\n      addPlaneError: '新增平面图成功，但无法上载平面图。',\r\n      itemHasNotSetCoordinate: '此项目尚未设定座标',\r\n      floorPlanPreview: '平面图预览'\r\n    },\r\n    // 事件定义设定\r\n    eventDefinition: {\r\n      singleEvent: '独立事件',\r\n      planeEvent: '区域事件',\r\n      eventCode: '事件编号',\r\n      eventTemplate: '模板名称 ',\r\n      addEventDefinition: '新增事件定义',\r\n      selectTemplate: '请选择模板 *',\r\n      enterEventCode: '请输入事件编号 *',\r\n      enterEventName: '请输入事件名称 *',\r\n      enterEventNameNoStar: '请输入事件名称',\r\n      selectEventCategory: '请选择事件类别 *',\r\n      selectEventCategoryNoStar: '请选择事件类别',\r\n      selectPlane: '请选择区域 *',\r\n      enterValue: '请输入数值',\r\n      selectStartTime: '请选择起始时间',\r\n      selectEndTime: '请选择结束时间',\r\n      selectSponsorType: '请选择發起者属性',\r\n      selectSponsorRole: '请选择發起者角色',\r\n      selectSponsorGroup: '请选择發起者群组',\r\n      selectParticipantType: '请选择参与者属性',\r\n      selectParticipantRole: '请选择参与者角色',\r\n      selectParticipantGroup: '请选择参与者群组',\r\n      selectNotifierType: '请选择通知属性',\r\n      selectNotifierRole: '请选择通知角色',\r\n      selectNotifierGroup: '请选择通知群组',\r\n      selectNotifierAccount: '请选择通知帐号',\r\n      enterNotifierMsg: '请输入通知讯息',\r\n      selectDeleteEvent: '请勾选要删除的事件。',\r\n      threshold: '警示条件设定',\r\n      below: '低于',\r\n      over: '超过',\r\n      stayOver: '停留超过',\r\n      batteryPercentage: '% 电量',\r\n      minAlert: '分钟，进行警示',\r\n      minRemind: '分钟未巡查，进行提醒',\r\n      intervals: '管制时间设定',\r\n      between: '到',\r\n      sensorDataDriven: '数据自订事件设定',\r\n      sensorDataDrivenData: '数据',\r\n      it: '<',\r\n      ite: '<=',\r\n      gt: '>',\r\n      gte: '>=',\r\n      dataDrivenRuleSource: '来源',\r\n      dataDrivenRuleComp: '条件',\r\n      enterSource: '请选择数据来源',\r\n      enterComp: '请选择条件',\r\n      dataDrivenRuleRepeat: '重複出现',\r\n      dataDrivenRuleCount: '次',\r\n      dataDrivenRuleDuration: '持续',\r\n      dataDrivenRuleDurationSec: '秒',\r\n      validatePlaneCode: '区域阶层至少请选择至 楼层 / 区域 / 子区域',\r\n      validateCategory: '请选择事件类别。',\r\n      validateSponsor: '發起者属性/角色/群组，请择一输入',\r\n      validateParticipant: '参与者属性/角色/群组，请择一输入',\r\n      enterEventCodeValidate: '请填入事件编号。',\r\n      eventCodeValidate: '事件编号字数长度必须介于 0 和 20。',\r\n      enterEventNameValidate: '请填入事件名称。',\r\n      eventNameValidate: '事件名称字数长度必须介于 0 和 64。',\r\n      templateValidate: '请选择模板。',\r\n      enterThresholdValidate: '请填入警示条件。',\r\n      thresholdValidatePattern: '警示条件必须为数字(正整数)。',\r\n      thresholdValidateMax: '警示条件不能大于：20 字元。',\r\n      msgValidate: '通知讯息字数长度必须介于 0 和 256。',\r\n      badInterval: '管制时间必须符合以下格式：00:00:00。',\r\n      sponsorObjectTypeNotFound: '选择的發起者属性不存在，请重新整理页面。',\r\n      sponsorObjectRoleNotFound: '选择的發起者角色不存在，请重新整理页面。',\r\n      sponsorObjectGroupNotFound: '选择的發起者群组不存在，请重新整理页面。',\r\n      participantObjectTypeNotFound: '选择的参与者属性不存在，请重新整理页面。',\r\n      participantObjectRoleNotFound: '选择的参与者角色不存在，请重新整理页面。',\r\n      participantObjectGroupNotFound: '选择的参与者群组不存在，请重新整理页面。',\r\n      thresholdNotFound: '选择的警示条件不存在，请重新整理页面。',\r\n      notifierObjectTypeNotFound: '选择的通知属性不存在，请重新整理页面。',\r\n      notifierObjectRoleNotFound: '选择的通知角色不存在，请重新整理页面。',\r\n      notifierObjectGroupNotFound: '选择的通知群组不存在，请重新整理页面。',\r\n      notifierUserNotFound: '选择的通知帐号不存在，请重新整理页面。',\r\n      eventCodeExists: '事件编号已经被使用，请试试其他事件编号。',\r\n      warningCondition: '警示条件',\r\n      controlTime: '管制时间',\r\n      startTime: '起始时间',\r\n      entTime: '结束时间',\r\n      eventCategory: '事件类别',\r\n      planeCode: '区域编号',\r\n      participantType: '参与者属性',\r\n      participantRole: '参与者角色',\r\n      participantGroup: '参与者群组',\r\n      sponsorType: '發起者属性',\r\n      sponsorRole: '發起者角色',\r\n      sponsorGroup: '發起者群组',\r\n      notifierType: '通知属性',\r\n      notifierRole: '通知角色',\r\n      notifierGroup: '通知群组',\r\n      notifierUser: '通知帐号',\r\n      notifierMsg: '通知讯息',\r\n      addControlTime: '请点选 + 新增管制时间',\r\n      planeSetting: '区域设定',\r\n      sponsorTypeSetting: '發起者属性设定',\r\n      sponsorRoleSetting: '發起者角色设定',\r\n      sponsorGroupSetting: '發起者群组设定',\r\n      participantTypeSetting: '参与者属性设定',\r\n      participantRoleSetting: '参与者角色设定',\r\n      participantGroupSetting: '参与者群组设定',\r\n      notifierTypeSetting: '通知属性设定',\r\n      notifierRoleSetting: '通知角色设定',\r\n      notifierGroupSetting: '通知群组设定',\r\n      notifierUserSetting: '通知帐号设定',\r\n      thresholdValidate: '警示条件必须为数字且长度为：20。',\r\n      searchEventTemplate: '事件模板',\r\n      searchSelectTemplate: '请选择模板',\r\n      eventNotFound: '选择的事件定义不存在，请重新整理页面。',\r\n      taskConflicts: '求救事件尚未解除，无法删除此事件定义。',\r\n      areaName: '区域名称'\r\n    },\r\n    // 韧体更新\r\n    otaUpdate: {\r\n      uploadRecord: '上传纪录',\r\n      scheduleList: '排程清单',\r\n      fileName: '档案名称',\r\n      name: '韧体更新',\r\n      uploadTime: '上传时间',\r\n      uploadOta: '上传韧体档案',\r\n      station: '基站(系统)',\r\n      stationApk: '基站(APK)',\r\n      anchor: '锚点',\r\n      wristband: '手环',\r\n      button: '按钮',\r\n      tag: '定位设备',\r\n      fromVersion: '旧版本',\r\n      toVersion: '新版本',\r\n      description: '描述',\r\n      activeName: '是否启用',\r\n      code: '版号',\r\n      nordicVersion: 'nordic版号',\r\n      bootloaderVersion: 'Bootloader版号',\r\n      stVersion: 'st版号',\r\n      nordicFileName: 'nordic档案名称',\r\n      bootloaderFileName: 'bootloader档案名称',\r\n      stFileName: 'st档案名称',\r\n      uploadFile: '请上传档案',\r\n      fileNameFormatReference: '档案名称格式参考',\r\n      uploadNordicFile: '请上传nordic档案',\r\n      uploadbootLoaderFile: '请上传bootloader档案',\r\n      uploadStFile: '请上传st档案',\r\n      inputDescription: '请输入档案说明',\r\n      setUpdateScheduling: '建立更新排程',\r\n      queryUpdateResults: '查询更新结果',\r\n      back: '返回',\r\n      completedQuantity: '已完成数量',\r\n      unfinishedQuantity: '未完成数量',\r\n      updateTime: '更新时间',\r\n      deviceName: '装置名称',\r\n      updateObject: '更新对象',\r\n      originalVersion: '原始版本',\r\n      newVersion: '更新后版本',\r\n      currentVersion: '目前版本',\r\n      toUpdateVersion: '应更新版本',\r\n      errorMessage: '更新失败原因',\r\n      schedulingName: '排程名称',\r\n      targetPlane: '区域',\r\n      count: '需更新数量',\r\n      actionSuccess: '已更新数量',\r\n      actionUnsuccess: '未更新数量',\r\n      schedulingEnable: '排程启用',\r\n      updateType: '更新类别',\r\n      updateRole: '更新角色',\r\n      updateGroup: '更新群组',\r\n      enable: '启用',\r\n      close: '关闭',\r\n      noEnable: '不启用',\r\n      pleaseSelectDeleteStation: '请选择要删除的基站(系统)排程',\r\n      pleaseSelectDeleteStationApk: '请选择要删除的基站(APK)排程',\r\n      pleaseSelectDeleteAnchor: '请选择要删除的锚点排程',\r\n      pleaseSelectDeleteWristband: '请选择要删除的手环排程',\r\n      pleaseSelectDeleteButton: '请选择要删除的按钮排程',\r\n      pleaseSelectDeleteTag: '请选择要删除的定位设备排程',\r\n      otaScheduleNotFoundVerification: '选择的排程不存在，请重新整理页面。',\r\n      updateCycle: '更新週期',\r\n      sunday: '星期日',\r\n      monday: '星期一',\r\n      tuesday: '星期二',\r\n      wednesday: '星期三',\r\n      thursday: '星期四',\r\n      friday: '星期五',\r\n      saturday: '星期六',\r\n      setUpdateSchedulingSuccessful: '建立更新排程成功。',\r\n      inputSchedulingName: '请输入排程名称 *',\r\n      inputSchedulingCode: '请输入排程代号 *',\r\n      pleaseSelectStartsAt: '请选择开始时间',\r\n      pleaseSelectEndsAt: '请选择结束时间',\r\n      pleaseSelectCategory: '请选择类别',\r\n      pleaseSelectUpdateType: '请选择更新类别',\r\n      pleaseSelectUpdateRole: '请选择更新角色',\r\n      pleaseSelectUpdateGroup: '请选择更新群组',\r\n      pleaseSelectTargetPlane: '请选择区域',\r\n      pleaseSelectNotifierUserAccounts: '请选择更新帐号',\r\n      targetPlaneRequiredVerification: '请选择区域。',\r\n      nameRequiredVerification: '请填入排程名称。',\r\n      nameTypeVerification: '排程名称字数长度必须介于0和64。',\r\n      codeRequiredVerification: '请填入排程代号。',\r\n      codeTypeVerification: '排程代号字数长度必须介于0和20。',\r\n      startsAtRequiredVerification: '请选择开始时间。',\r\n      typeRequiredVerification: '请选择类别。',\r\n      selectTargetPlaneLength: '区域阶层至少选择至 楼层/区域/子区域',\r\n      targetPlaneNotFoundVerification: '选择的区域不存在，请重新整理页面。',\r\n      updateTypeRoleGroupVerification: '更新类别/角色/群组，请择一输入。',\r\n      targetObjectTypeNotFoundVerification: '选择的更新类别不存在，请重新整理页面。',\r\n      targetObjectRoleNotFoundVerification: '选择的更新角色不存在，请重新整理页面。',\r\n      targetObjectGroupNotFoundVerification: '选择的更新群组不存在，请重新整理页面。',\r\n      notifierUserNotFoundVerification: '选择的更新帐号不存在，请重新整理页面。',\r\n      otaScheduleCodeExistsVerification: '排程代号已经被使用，请试试其他排程代号。',\r\n      recursiveVersionFound: '档案上传失败，请确认上传档案版本是否正确。',\r\n      badFilename: '档案上传失败，档案名称格式不正确，请确认后再上传档案。',\r\n      badCycleVerification: '週期格式错误。',\r\n      updateDate: '更新起讫日期',\r\n      pleaseInputSchedulingName: '请输入排程名称',\r\n      queryUpdateResultsSuccessful: '查询更新结果成功。',\r\n      updateTimeRequiredVerification: '请选择更新时间。',\r\n      uploadSuccess: '档案上传成功。'\r\n    },\r\n    // 授权管理\r\n    licenseManagement: {\r\n      licenseRequest: '产生授权请求',\r\n      enterCustomerId: '* 请输入客户帐号',\r\n      enterSerialNum: '* 请输入序号',\r\n      selectService: '请选择授权主机',\r\n      useRegCode: '套用註册码',\r\n      enterRegCode: '* 请输入註册码',\r\n      view: '查看',\r\n      enterCustomerIdHint: '请输入客户帐号',\r\n      enterSerialNumHint: '请输入序号',\r\n      selectServiceHint: '请选择授权主机',\r\n      customerIdRuleValidate: '客户帐号字数长度必须介于 0 和 64。',\r\n      serialNumRuleValidate: '序号字数长度必须介于 0 和 256。',\r\n      copy: '複製',\r\n      serviceNotFound: '选择的授权主机不存在，请重新整理页面。',\r\n      customerNotFound: '输入的客户帐号不存在，请重新输入。',\r\n      enterRegCodeHint: '请输入註册码',\r\n      regCodeRuleValidate: '註册码字数长度必须介于 0 和 2048。',\r\n      regSuccessful: '套用註册码成功。',\r\n      badRegistrationCode: '註册码不正确，请重新输入。',\r\n      customerName: '客户名称',\r\n      totalService: '授权主机数量',\r\n      validStation: '授权基站数量',\r\n      validDevice: '授权装置数量',\r\n      expiredTime: '授权到期日',\r\n      status: '授权状态',\r\n      id: '编号',\r\n      serviceSerial: '授权主机序号',\r\n      serviceName: '授权主机名称',\r\n      serviceIp: '主机IP',\r\n      licenseTime: '启用时间',\r\n      validServiceCount: '有效',\r\n      invalidServiceCount: '无效',\r\n      slash: ' / ',\r\n      copyRequestCode: '複製授权请求码',\r\n      noError: '主机授权无异常',\r\n      licenseExpired: '主机授权失效',\r\n      licenseExpiredSoon: '主机授权即将逾期'\r\n    },\r\n    // 历史报表\r\n    historyReport: {\r\n      eventTemplate: '事件分类',\r\n      startsAt: '發起时间',\r\n      action: '事件状态',\r\n      eventName: '事件名称',\r\n      eventPlane: '事件發生区域',\r\n      sponsorObjects: '事件發起者',\r\n      description: '事件纪录',\r\n      startAndEndTime: '请选择事件發生起讫日期',\r\n      untreated: '未解除',\r\n      treating: '解除中',\r\n      treated: '已解除'\r\n    },\r\n    // 摄影机设定\r\n    camera: {\r\n      addCamera: '新增摄影机',\r\n      editCamera: '编辑摄影机',\r\n      cameraCode: '摄影机编号',\r\n      cameraName: '摄影机名称',\r\n      cameraIp: '摄影机IP',\r\n      streamUrl: '摄影机串流路径',\r\n      cameraVideoPath: '摄影机录影存档路径',\r\n      taskVideoPath: '事件录影存档路径',\r\n      enterCameraCode: '请输入摄影机编号',\r\n      enterCameraName: '请输入摄影机名称',\r\n      enterCameraIp: '请输入摄影机IP',\r\n      enterStreamUrl: '请输入摄影机串流路径',\r\n      enterCameraVideoPath: '请输入摄影机录影存档路径',\r\n      enterTaskVideoPath: '请输入事件录影存档路径',\r\n      cameraNo: '摄影机编号',\r\n      cameraIpPosition: '摄影机IP位址',\r\n      cameraConnStatus: '连线状态'\r\n    },\r\n    // Log录製\r\n    logRecording: {\r\n      addLogRecording: '新增装置定位日誌',\r\n      name: '日誌名称',\r\n      chooseDevice: '请选择装置',\r\n      choosePlane: '请选择区域',\r\n      chooseAction: '请选择状态',\r\n      chooseValidTime: '请选择有效时间',\r\n      oneHour: '一小时',\r\n      threeHour: '三小时',\r\n      twelfthHour: '十二小时',\r\n      oneDay: '一天',\r\n      threeDay: '三天',\r\n      fiveDay: '五天',\r\n      oneWeek: '一週',\r\n      enterDescription: '请输入描述',\r\n      devicesName: '装置名称',\r\n      devicesID: '装置ID',\r\n      devicesPID: '装置MAC',\r\n      reportUrl: '日誌分析地址',\r\n      logUrl: '日誌地址',\r\n      status: '状态',\r\n      plane: '区域',\r\n      message: '讯息',\r\n      description: '描述',\r\n      startsAt: '开始时间',\r\n      finishesAt: '结束时间',\r\n      expiresAt: '过期时间',\r\n      modifiesAt: '最近更新时间',\r\n      action: '操作',\r\n      waiting: '准备录製中',\r\n      logCollecting: '录製中',\r\n      logCollected: '停止录製',\r\n      reportCreating: '正在产生分析报表',\r\n      logFileCreating: '正在产生日誌档',\r\n      finished: '已录製',\r\n      failed: '录製失败',\r\n      canceled: '取消录製',\r\n      enterNameValidate: '请填入日誌名称',\r\n      eventNameValidateLength: '日誌名称字数长度必须介于 0 和 32',\r\n      msgValidate: '描述字数长度必须介于 0 和 512',\r\n      deviceNotFoundVerification: '选择的装置不存在，请重新整理页面。',\r\n      planeNotFoundError: '选择的区域不存在，请重新整理页面。',\r\n      logNotFound: '选择的日誌不存在，请重新整理页面。',\r\n      waitForInitialization: '日誌初始化中，请5秒钟后再点击停止录製。',\r\n      requestUnavailable: '请求失败，请重新整理页面。',\r\n      isExport: '是否产生报表',\r\n      logNo: '日誌编号',\r\n      yes: '是',\r\n      no: '否',\r\n      readyRecord: '准备录製中',\r\n      recording: '录製中',\r\n      readyExport: '准备产生日誌中',\r\n      exportingLog: '正在产生分析报表',\r\n      exportingFile: '正在产生日誌档',\r\n      recorded: '已录製',\r\n      recordFail: '录製失败',\r\n      cancel: '已取消',\r\n      stopRecord: '停止录製'\r\n    },\r\n    // 资产盘点系统\r\n    inventory: {\r\n      pleaseEnter: '请输入',\r\n      pleaseSelect: '请选择',\r\n      pleaseUpload: '上传',\r\n      inputCode: '编号',\r\n      devicesName: '装置名称',\r\n      devicesID: '装置ID',\r\n      devicesPID: '装置MAC',\r\n      devicesPosition: '装置位置',\r\n      XCoordinate: 'X座标',\r\n      YCoordinate: 'Y座标',\r\n      locateObject: '定位对象',\r\n      locationRegion: '所在区域',\r\n      latestPositionTime: '最近定位时间',\r\n      hasCounted: '盘点状态',\r\n      reCounted: '重新盘点',\r\n      refresh: '重新整理',\r\n      latestCountingTime: '最近盘点时间',\r\n      modifiesAt: '最近更新时间',\r\n      action: '操作',\r\n      addIvnentory: '新增资产',\r\n      addTask: '新增盘点任务',\r\n      index: '资产状态',\r\n      task: '任务清单',\r\n      description: '资产描述',\r\n      categoryCode: '资产类型',\r\n      iconCode: '资产图示',\r\n      enterNumber: '资产编号',\r\n      chooseKeeper: '保管人',\r\n      chooseManager: '管理人',\r\n      objectCode: '物件',\r\n      enterPurchaseDate: '购买日期',\r\n      uploadPhoto: '上传图片',\r\n      serial: '资产序号',\r\n      inputValidateLength20: '字数长度必须介于 1 和 20',\r\n      inputValidateLength32: '字数长度必须介于 1 和 32',\r\n      inputValidateLength64: '字数长度必须介于 1 和 64',\r\n      keeperUserNotFound: '保管人不存在于系统中',\r\n      managerUserNotFound: '管理人不存在于系统中',\r\n      iconNotFound: '图示不存在于系统中',\r\n      categoryNotFound: '资产类型不存在于系统中',\r\n      objectNotFound: '物件不存在于系统中',\r\n      objectInventoryExists: '已存在相同的物件资产',\r\n      inventoryCodeExists: '已存在相同的编号',\r\n      detail: '资产详情',\r\n      taskDetail: '任务详情',\r\n      status: '状态',\r\n      createsAt: '创建时间',\r\n      eolAt: '产品寿命结束时间',\r\n      active: 'Active',\r\n      repairing: 'Repairing',\r\n      calibrating: 'Calibrating',\r\n      servicing: 'Servicing',\r\n      eol: 'EOL',\r\n      objectName: '定位对象名称',\r\n      position: '所在区域',\r\n      positionXY: '定位座标',\r\n      images: '资产图片',\r\n      taskNumber: '任务号码',\r\n      taskDescription: '任务描述',\r\n      ownerUser: '盘点人',\r\n      finishesAt: '结束时间',\r\n      countedCount: '已盘点',\r\n      uncountedCount: '未盘点',\r\n      inProgress: '进行中',\r\n      finished: '已结束'\r\n    },\r\n    // 系统参数\r\n    systemConfig: {\r\n      group: '群组',\r\n      key: '参数名称',\r\n      value: '参数值',\r\n      defaultValue: '预设值',\r\n      min: '最小值',\r\n      max: '最大值',\r\n      maxLength: '最大长度',\r\n      unit: '单位',\r\n      description: '参数说明',\r\n      schemaGroupAPI: 'API接口',\r\n      schemaGroupLogging: '日誌',\r\n      schemaGroupHelp: '求救',\r\n      schemaGroupUser: '系统帐号',\r\n      schemaGroupEnvironment: '系统环境',\r\n      schemaGroupPositioning: '定位',\r\n      schemaGroupConnection: '连线管理',\r\n      schemaGroupWeb: '前端网页',\r\n      descReserveJournalDuration: '日誌保留天数 (i.e. DBA_CleanData)',\r\n      descStationOfflinePeriod: '基站离线判断阀值: 超过此阀值未收到scan result则判定离线',\r\n      descDeviceOfflinePeriod: '装置离线判断阀值: 超过此阀值未收到scan result则判定离线',\r\n      descDeviceOtaRssiThreshold: 'OTA装置最低讯号强度',\r\n      descGatewayOfflinePeriod: 'Gateway离线判断阀值: 超过此阀值未收到???则判定离线',\r\n      descSmtpEnable: '是否启用外部SMTP伺服器',\r\n      descSmtpHost: '外部SMTP伺服器地址',\r\n      descSmtpPort: '外部SMTP伺服器Port',\r\n      descSmtpUsername: '外部SMTP连线帐号',\r\n      descSmtpPassword: '外部SMTP连线密码',\r\n      descHelpTaskDelayPeriod: '求救任务延迟开始间隔',\r\n      descHelpTurnOffDeviceTimer: '求救关灯排程间隔',\r\n      descHelpTurnOffDeviceRetryCount: '求救关灯重试次数',\r\n      descHelpTaskMinRssiThreshold: '求救任务延迟开始间隔',\r\n      descUserRegisterCodeExpirePeriod: '用户註册验证码有效间隔',\r\n      descUserRegisterCodeLength: '用户註册验证码长度',\r\n      descApiAccessTokenExpirePeriod: 'API访问令牌有效间隔',\r\n      descApiRefreshTokenExpirePeriod: 'API更新令牌有效间隔',\r\n      descApiDefaultSearchActive: 'API预设查询参数: 查询结果只含 active 为 true 的资料',\r\n      descApiDefaultMaxSize: 'API预设查询参数: 查询结果最大笔数',\r\n      descReportCachePeriod: '装置讯号取样间隔',\r\n      descTrackingKalmanFilterEnable: '是否启用定位结东KF(减缓定位座标飘移距离)',\r\n      descTrackingKalmanFilterMeterRangePerSecond: '提供定位结果KF参数: 每秒移动距离(公尺)',\r\n      descTrackingKalmanFilterAccuracy: '提供定位结果KF参数: Accuracy',\r\n      descLocationKalmanFilterEnable: '是否启用装置讯号KF(平滑装置讯号强度)',\r\n      descLocationKalmanProcessNoise: '提供装置讯号KF参数: Process Noise',\r\n      descLocationKalmanMeasurementNoise: '提供装置讯号KF参数: Measurement Noise',\r\n      descPositionPersistencePeriod: '定位结果持久化间隔',\r\n      descPositionHistoryPersistencePeriod: '定位历史记录结果持久化间隔',\r\n      descPositioningAlgorithmV2: '定位演算法',\r\n      descPositioningPeroid: '定位週期',\r\n      descPositionMinRssiThreshold: '定位讯号过滤阈值',\r\n      descTrackingRegionTotalCount: '定位结果区域判定参数: 样本数',\r\n      descTrackingRegionMatchCount: '定位结果区域判定参数: 命中数',\r\n      descMonitorRefreshSecond: '监控资料更新週期',\r\n      descStayTimeoutDefaultInterval: '停留逾时事件预设逾时时间间隔',\r\n      descHistoryMatchCount: '方向一致的向量个数',\r\n      event: '事件'\r\n    },\r\n    // 系统管条理 - ipAddr\r\n    ipAddr: {\r\n      ipError: '请输入正确的IP地址'\r\n    },\r\n    // 系统版本\r\n    systemSWVersion: {\r\n      name: '模组名称',\r\n      version: '版本',\r\n      status: '状态',\r\n      checkAt: '回报时间'\r\n    },\r\n    // apps\r\n    apps: {\r\n      common: {\r\n        posParameterCode: '定位演算法',\r\n        deviceType: '配对装置Type',\r\n        displayStations: '顯示基站',\r\n        monitor: '监控平面',\r\n        object: '定位对象',\r\n        planeSetting: '平面设定',\r\n        configSetting: '参数设定',\r\n        confirm: '确认',\r\n        cancel: '取消',\r\n        indoorRealTimePositioningSystem: '室内即时定位系统',\r\n        device: '配对装置',\r\n        selectDelete: '删除选取的项目',\r\n        add: '新增',\r\n        edit: '编辑',\r\n        delete: '删除',\r\n        cancelAdd: '取消新增',\r\n        cancelEdit: '取消编辑',\r\n        saveAdd: '储存新增',\r\n        saveEdit: '储存编辑',\r\n        sid: '基站SID',\r\n        plane: '所在区域',\r\n        isAlive: '连线状态',\r\n        picEdit: '背景图档编辑',\r\n        picRemove: '移除图档',\r\n        picUpload: '上传图档',\r\n        picConfirm: '确定删除背景图?',\r\n        planeEdit: '编辑监控平面',\r\n        planeSet: '配置监控平面',\r\n        picBackgroundUpload: '上传背景图',\r\n        iconPicEdit: 'Icon图档编辑',\r\n        iconPicUpload: '上传Icon图档',\r\n        objectDeviceConfirm: '装置已配对,是否解绑定位对象',\r\n        status: '状况',\r\n        record: '纪录',\r\n        recordPlaceholder: '请输入纪录...',\r\n        eventAlarm: '事件警报音',\r\n        eventAlarmWindow: '事件通知视窗',\r\n        eventStatus: '状况处理',\r\n        eventClean: '清除所有事件',\r\n        eventCleanConfirm: '确定清除事件?',\r\n        eventStartTime: '事件纪录起始时间',\r\n        removeEvent: '解除',\r\n        emergencyEvent: '紧急事件通知',\r\n        objectName: '定位对象名称',\r\n        deviceMac: '配对装置MAC',\r\n        occupy: '入住',\r\n        event: '事件',\r\n        people: '人',\r\n        selectDeleteError: '请选取删除项目',\r\n        abnormalDevice: '装置异常',\r\n        second: '秒钟',\r\n        deviceConnectionAbnormal: '装置连线异常!',\r\n        deviceConnectionAbnormalLatestEvent: '连线异常前状态：'\r\n      },\r\n      m20: {\r\n        selectMonitorPlane: '偵測平面',\r\n        selectMonitorObject: '偵測對像',\r\n      },\r\n      m21: {\r\n        selectService: '發起服務'\r\n\r\n      },\r\n      eFence: {\r\n        equipment: '设备',\r\n        name: '定位对象',\r\n        type: '定位类别',\r\n        device: '配对装置MAC',\r\n        confirmEdit: '装置已配对',\r\n        people: '人',\r\n        fall: '跌倒',\r\n        normal: '正常',\r\n        positionStation: '目前定位基站',\r\n        event: '事件',\r\n        allPositionStation: '全部定位基站',\r\n        enterAndExit: '进入/离开',\r\n        enter: '进入',\r\n        exit: '离开'\r\n      },\r\n      m01: {\r\n        equipment: '设备',\r\n        addSpecialStatus: '新增特殊状况',\r\n        babyName: '新生儿名称',\r\n        region: '地点',\r\n        description: '原因',\r\n        timeoutSetting: '逾时设定',\r\n        time: '分钟',\r\n        stayTimeout: '停留逾时x',\r\n        specialStatus: '特殊状况',\r\n        baby: '新生儿',\r\n        numberControl: '母婴同室',\r\n        enter: '进入警示区',\r\n        lowBattery: '低电量警示',\r\n\r\n        wetUrine: '尿湿提示',\r\n        lossSignal: '婴儿走失',\r\n        wetUrineTimeout: '尿湿逾时',\r\n        noPatient: '无产妇入住',\r\n        hasPatient: '产妇入住',\r\n        babyCare: '新生儿照护',\r\n        removeSpecialStatus: '解除特殊状况',\r\n        removeSpecialStatusConfirm: '确定解除特殊状况?',\r\n        babyMove: '移动',\r\n        unknownStation: '未知基站',\r\n        specialEvent: '特殊事件',\r\n        babyRegion: '婴儿位置',\r\n        diaperNormal: '尿布正常',\r\n\r\n        babyTagLowBattery: '婴儿Tag低电量',\r\n        motherTagLowBattery: '母亲手环低电量',\r\n        monitorOutside: '管制区外',\r\n        emptyBed: '空床',\r\n        enterSetting: '入住设定',\r\n        entering: '入住中',\r\n        eventId: '事件ID',\r\n        eventNo: '事件编号',\r\n        eventName: '事件名称',\r\n        alertCondition: '警示条件',\r\n        less: '低于',\r\n        battery: '电量',\r\n        over: '超过',\r\n        minsSignal: '分钟没讯号',\r\n        second: '秒钟',\r\n        addMon: '新增母亲',\r\n        objectAttr: '定位对象属性',\r\n        addBaby: '新增婴儿',\r\n        resetDiaperComfortable: '重置尿布湿度',\r\n        mon: '母亲',\r\n        baby2: '婴儿',\r\n        propertySetting: '属性设定',\r\n        objectSetting: '物件配置',\r\n        stationSetting: '基站配置',\r\n        stationSelect: '选择基站',\r\n        monRoom: '妇产科病房',\r\n        babyRoom: '婴儿中心',\r\n        controlArea: '管制区出入口',\r\n        warningArea: '警示区',\r\n        normalArea: '一般区',\r\n        objectName2: '物件名称',\r\n        objectType: '物件属性',\r\n        addBed: '新增床',\r\n        bed: '床',\r\n        toilet: '浴厕',\r\n        addRegion: '新增区域',\r\n        regionType: '区域类别',\r\n        showRegion: '显示区域',\r\n        addSubRegion: '新增子区域'\r\n\r\n      },\r\n      m03: {\r\n        equipment: '设备',\r\n        overtemperature: '体温过高',\r\n        wristbandLowBattery: '手环低电量',\r\n        noPatient: '无入住',\r\n        lowBattery: '低电量警示',\r\n        bodyOvertemperature: '体温过高',\r\n        negativePressureIsolationCenter: '负压隔离中心',\r\n        babyCare: '新生儿照护',\r\n        nowTemperature: '现在体温',\r\n        nowHeartRate: '现在脉搏',\r\n        nowBloodOxygen: '现在血氧',\r\n        nowSbp: '现在收缩压',\r\n        nowDbp: '现在舒张压',\r\n        max: '最大值',\r\n        temperature: '体温',\r\n        temperaturePicAndNumerical: '体温图例与数值',\r\n        temperatureColor: '体温区间与颜色设定',\r\n        normal: '正常',\r\n        remind: '提醒',\r\n        bodyTemperatureWarm: '体温稍高',\r\n        bodyTemperatureDetect: '体温侦测',\r\n        heartRateDetect: '脉搏侦测',\r\n        bloodOxygenDetect: '血氧侦测',\r\n        sbpDetect: '血压过高侦测',\r\n        dbpDetect: '血压过低侦测',\r\n        over: '过高',\r\n        eventId: '事件ID',\r\n        eventNo: '事件编号',\r\n        eventName: '事件名称',\r\n        alertCondition: '警示条件',\r\n        battery: '电量',\r\n        sbp: '收缩压',\r\n        dbp: '舒张压',\r\n        greater: '大于',\r\n        greaterThanOrEqual: '大于',\r\n        less: '低于',\r\n        lessThanOrEqual: '小於等于',\r\n        degree: '度',\r\n        timesPerMin: '次/分',\r\n        objectSetting: '物件配置',\r\n        negativePressureIsolationRoom: '负压隔离病房',\r\n        objectName: '物件名称',\r\n        objectType: '物件属性',\r\n        addBed: '新增床',\r\n        bed: '床',\r\n        toilet: '浴厕',\r\n        addRegion: '新增区域',\r\n        regionType: '区域类别',\r\n        showRegion: '显示区域',\r\n        addSubRegion: '新增子区域',\r\n        editConfirm: '修改确认',\r\n        bodyPhysicalDataSetting: '生理讯号监测设定',\r\n        continuousMonitoringDuration: '连续监测 持续时间',\r\n        nonContinuousMonitoringAbnormalCritical: '非连续监测 监测数值异常/危急达',\r\n        continuousMonitoring: '连续监测',\r\n        nonContinuousMonitoring: '非连续监测',\r\n        times: '次',\r\n        alert: '回报系统',\r\n        abnormal: '异常',\r\n        critical: '危急',\r\n        bloodOxygen: '血氧',\r\n        bloodPressure: '血压',\r\n        heartRate: '脉搏',\r\n        bodyTemperature: '体温',\r\n        backgroundColor: '背景色',\r\n        deviceLowBattery: '装置低电量',\r\n        lowTemperature: '体温过低',\r\n        overTemperature: '体温过高',\r\n        lowHeartRate: '脉搏过低',\r\n        overHeartRate: '脉搏过高',\r\n        lowBloodOxygen: '血氧过低',\r\n        overBloodPressure: '血压过高',\r\n        lowBloodPressure: '血压过低',\r\n        overBloodPressureSbp: '收缩压过高',\r\n        lowBloodPressureSbp: '收缩压过低',\r\n        overBloodPressureDbp: '舒张压过高',\r\n        lowBloodPressureDbp: '舒张压过低',\r\n        sensorType: '配对装置感测类别',\r\n        current: '现在'\r\n      },\r\n      mmWave: {\r\n        equipment: '设备',\r\n        history: '历史纪录',\r\n        fall: '跌倒',\r\n        lying: '卧躺',\r\n        sit: '坐着',\r\n        natureCall: '如厕',\r\n        now: '现在',\r\n        status: '状况',\r\n        breathe: '呼吸',\r\n        record: '纪录',\r\n        normal: '正常',\r\n        breathe5MinsRecord: '呼吸5分钟趋势',\r\n        bpm: '现在BPM',\r\n        max: '最大值',\r\n        timeoutSetting: '逾时设定',\r\n        mins: '分钟',\r\n        stayTimeout: '停留逾时',\r\n        correct: '正确',\r\n        notCorrect: '误判',\r\n        abnormalBreath: '呼吸异常',\r\n        leaveBed: '离床',\r\n        getUp: '起身',\r\n        emptyBed: '空床',\r\n        monitoring: '监测中',\r\n        empty: '閒置',\r\n        occupy: '使用中',\r\n        enterSetting: '入住设定',\r\n        entering: '入住中',\r\n        enter: '入住',\r\n        breathDetect: '呼吸侦测',\r\n        stopDetect: '停止侦测',\r\n        eventId: '事件ID',\r\n        eventNo: '事件编号',\r\n        eventName: '事件名称',\r\n        alertCondition: '警示条件',\r\n        type: '定位类别',\r\n        device: '配对装置MAC',\r\n        yes: '是',\r\n        no: '否',\r\n        objectAttr: '定位对象属性',\r\n        eventCondition: '事件条件',\r\n        connectStatus: '配对装置连线状态',\r\n        connecting: '连线中',\r\n        nowObject: '目前定位对象',\r\n        allObject: '全部定位对象',\r\n        beforeIndex: '拖动前的索引',\r\n        afterIndex: '拖动后的索引',\r\n        searchCondition: '查询条件',\r\n        eventEdit: '事件编辑',\r\n        edit: '编辑',\r\n        cancelEdit: '取消编辑',\r\n        saveEdit: '储存编辑',\r\n        sponsorObjectType: '發起者属性',\r\n        preFall: '跌倒侦测'\r\n\r\n      },\r\n      m02: {\r\n        equipment: '设备',\r\n        stationID: '基站SID',\r\n        name: '定位对象',\r\n        type: '定位类别',\r\n        device: '配对装置MAC',\r\n        confirmEdit: '装置已配对',\r\n        people: '人',\r\n        fall: '跌倒',\r\n        normal: '正常',\r\n        positionStation: '目前定位基站',\r\n        event: '事件',\r\n        allPositionStation: '全部定位基站',\r\n        enterAndExit: '进入/离开',\r\n        enter: '进入',\r\n        exit: '离开',\r\n        employeeNo: '工号',\r\n        employeeName: '姓名',\r\n        phoneNo: '手机序号',\r\n        enable: '启用',\r\n        close: '关闭',\r\n        modifyTime: '修改时间',\r\n        pairingDevice: '配对装置',\r\n        controlArea: '管制区',\r\n        normalArea: '非管制区',\r\n        enterControlArea: '进入管制区',\r\n        notice: '通知',\r\n        install: '安装',\r\n        confirm: '确认',\r\n        remove: '移除',\r\n        leaveControlArea: '离开管制区',\r\n        enableAirwatch: '启用Airwatch'\r\n      },\r\n      m27: {\r\n        equipment: '设备',\r\n        overtemperature: '体温过高',\r\n        wristbandLowBattery: '手环低电量',\r\n        noPatient: '无入住',\r\n        lowBattery: '低电量警示',\r\n        bodyOvertemperature: '体温过高',\r\n        negativePressureIsolationCenter: '负压隔离中心',\r\n        babyCare: '新生儿照护',\r\n        nowTemperature: '现在体温',\r\n        nowHeartRate: '现在脉搏',\r\n        nowBloodOxygen: '现在血氧',\r\n        nowSbp: '现在收缩压',\r\n        nowDbp: '现在舒张压',\r\n        max: '最大值',\r\n        temperature: '体温',\r\n        temperaturePicAndNumerical: '体温图例与数值',\r\n        temperatureColor: '体温区间与颜色设定',\r\n        normal: '正常',\r\n        remind: '提醒',\r\n        bodyTemperatureWarm: '体温稍高',\r\n        bodyTemperatureDetect: '体温侦测',\r\n        heartRateDetect: '脉搏侦测',\r\n        bloodOxygenDetect: '血氧侦测',\r\n        sbpDetect: '血压过高侦测',\r\n        dbpDetect: '血压过低侦测',\r\n        over: '过高',\r\n        eventId: '事件ID',\r\n        eventNo: '事件编号',\r\n        eventName: '事件名称',\r\n        alertCondition: '警示条件',\r\n        battery: '电量',\r\n        sbp: '收缩压',\r\n        dbp: '舒张压',\r\n        greater: '大于',\r\n        greaterThanOrEqual: '大于',\r\n        less: '低于',\r\n        lessThanOrEqual: '小於等于',\r\n        degree: '度',\r\n        timesPerMin: '次/分',\r\n        objectSetting: '物件配置',\r\n        negativePressureIsolationRoom: '负压隔离病房',\r\n        objectName: '物件名称',\r\n        objectType: '物件属性',\r\n        addBed: '新增床',\r\n        bed: '床',\r\n        toilet: '浴厕',\r\n        addRegion: '新增区域',\r\n        regionType: '区域类别',\r\n        showRegion: '显示区域',\r\n        addSubRegion: '新增子区域',\r\n        editConfirm: '修改确认',\r\n        bodyPhysicalDataSetting: '生理讯号监测设定',\r\n        continuousMonitoringDuration: '连续监测 持续时间',\r\n        nonContinuousMonitoringAbnormalCritical: '非连续监测 监测数值异常/危急达',\r\n        continuousMonitoring: '连续监测',\r\n        nonContinuousMonitoring: '非连续监测',\r\n        times: '次',\r\n        alert: '回报系统',\r\n        abnormal: '异常',\r\n        critical: '危急',\r\n        bloodOxygen: '血氧',\r\n        bloodPressure: '血压',\r\n        heartRate: '脉搏',\r\n        bodyTemperature: '体温',\r\n        backgroundColor: '背景色',\r\n        deviceLowBattery: '装置低电量',\r\n        lowTemperature: '体温过低',\r\n        overTemperature: '体温过高',\r\n        lowHeartRate: '脉搏过低',\r\n        overHeartRate: '脉搏过高',\r\n        lowBloodOxygen: '血氧过低',\r\n        overBloodPressure: '血压过高',\r\n        lowBloodPressure: '血压过低',\r\n        overBloodPressureSbp: '收缩压过高',\r\n        lowBloodPressureSbp: '收缩压过低',\r\n        overBloodPressureDbp: '舒张压过高',\r\n        lowBloodPressureDbp: '舒张压过低',\r\n        sensorType: '配对装置感测类别',\r\n        current: '现在'\r\n      }\r\n    }\r\n  }\r\n}\r\n"]}]}