{"remainingRequest": "E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\node_modules\\babel-loader\\lib\\index.js!E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\node_modules\\string-replace-loader\\index.js??ref--13-0!E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\src\\components\\administrative\\systemManagement\\kmConfig.vue?vue&type=script&lang=js&", "dependencies": [{"path": "E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\src\\components\\administrative\\systemManagement\\kmConfig.vue", "mtime": 1754362736975}, {"path": "E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\node_modules\\string-replace-loader\\index.js", "mtime": 499162500000}, {"path": "E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["kmConfig.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqHA,OAAA,MAAA,MAAA,iBAAA;;AACA,IAAA,MAAA,GAAA,OAAA,CAAA,QAAA,CAAA;;AACA,eAAA;AACA,EAAA,IADA,kBACA;AAAA;;AACA,WAAA;AACA,MAAA,MAAA,EAAA,KADA;AAEA,MAAA,MAAA,EAAA,KAFA;AAGA,MAAA,UAAA,EAAA,EAHA;AAIA,MAAA,cAAA,EAAA,CACA;AACA,QAAA,IAAA,EAAA,WADA;AAEA,QAAA,IAAA,EAAA,KAAA,EAAA,CAAA,qBAAA;AAFA,OADA,EAKA;AACA,QAAA,IAAA,EAAA,QADA;AAEA,QAAA,IAAA,EAAA,KAAA,EAAA,CAAA,qBAAA;AAFA,OALA,EASA;AACA,QAAA,IAAA,EAAA,OADA;AAEA,QAAA,IAAA,EAAA,KAAA,EAAA,CAAA,qBAAA;AAFA,OATA,EAaA;AACA,QAAA,IAAA,EAAA,OADA;AAEA,QAAA,IAAA,EAAA,KAAA,EAAA,CAAA,qBAAA;AAFA,OAbA,CAJA;AAsBA,MAAA,UAAA,EAAA,KAAA,EAAA,CAAA,qBAAA,CAtBA;AAuBA,MAAA,WAAA,EAAA,CACA;AACA,QAAA,KAAA,EAAA,KAAA,EAAA,CAAA,qBAAA,CADA;AAEA,QAAA,GAAA,EAAA,MAFA;AAGA,QAAA,KAAA,EAAA,GAHA;AAIA,QAAA,MAAA,EAAA,gBAAA,CAAA,EAAA,MAAA,EAAA;AACA,iBAAA,CAAA,CAAA,MAAA,EAAA,MAAA,CAAA,aAAA,CAAA,MAAA,CAAA,GAAA,CAAA,IAAA,CAAA,CAAA;AACA;AANA,OADA,EASA;AACA,QAAA,KAAA,EAAA,KAAA,EAAA,CAAA,qBAAA,CADA;AAEA,QAAA,GAAA,EAAA;AAFA,OATA,EAaA;AACA,QAAA,KAAA,EAAA,KAAA,EAAA,CAAA,qBAAA,CADA;AAEA,QAAA,GAAA,EAAA;AAFA,OAbA,EAiBA;AACA,QAAA,KAAA,EAAA,KAAA,EAAA,CAAA,qBAAA,CADA;AAEA,QAAA,GAAA,EAAA;AAFA,OAjBA,EAqBA;AACA,QAAA,KAAA,EAAA,KAAA,EAAA,CAAA,qBAAA,CADA;AAEA,QAAA,GAAA,EAAA;AAFA,OArBA,EAyBA;AACA,QAAA,KAAA,EAAA,KAAA,EAAA,CAAA,qBAAA,CADA;AAEA,QAAA,GAAA,EAAA;AAFA,OAzBA,EA6BA;AACA,QAAA,KAAA,EAAA,KAAA,EAAA,CAAA,qBAAA,CADA;AAEA,QAAA,GAAA,EAAA;AAFA,OA7BA,CAvBA;AAyDA,MAAA,WAAA,EAAA,CACA;AACA,QAAA,KAAA,EAAA,KAAA,EAAA,CAAA,qBAAA,CADA;AAEA,QAAA,IAAA,EAAA,MAFA;AAGA,QAAA,KAAA,EAAA,GAHA;AAIA,QAAA,MAAA,EAAA,gBAAA,CAAA,EAAA,MAAA,EAAA;AACA,iBAAA,CAAA,CAAA,MAAA,EAAA,MAAA,CAAA,aAAA,CAAA,MAAA,CAAA,GAAA,CAAA,IAAA,CAAA,CAAA;AACA;AANA,OADA,EASA;AACA,QAAA,KAAA,EAAA,KAAA,EAAA,CAAA,qBAAA,CADA;AAEA,QAAA,IAAA,EAAA;AAFA,OATA,EAaA;AACA,QAAA,KAAA,EAAA,KAAA,EAAA,CAAA,qBAAA,CADA;AAEA,QAAA,IAAA,EAAA;AAFA,OAbA,EAiBA;AACA,QAAA,KAAA,EAAA,KAAA,EAAA,CAAA,qBAAA,CADA;AAEA,QAAA,IAAA,EAAA;AAFA,OAjBA,EAqBA;AACA,QAAA,KAAA,EAAA,KAAA,EAAA,CAAA,qBAAA,CADA;AAEA,QAAA,IAAA,EAAA;AAFA,OArBA,EAyBA;AACA,QAAA,KAAA,EAAA,KAAA,EAAA,CAAA,qBAAA,CADA;AAEA,QAAA,IAAA,EAAA;AAFA,OAzBA,EA6BA;AACA,QAAA,KAAA,EAAA,KAAA,EAAA,CAAA,qBAAA,CADA;AAEA,QAAA,IAAA,EAAA;AAFA,OA7BA,CAzDA;AA2FA,MAAA,UAAA,EAAA,CACA;AACA,QAAA,IAAA,EAAA,WADA;AAEA,QAAA,aAAA,EAAA,EAFA;AAGA,QAAA,YAAA,EAAA,EAHA;AAIA,QAAA,cAAA,EAAA,EAJA;AAKA,QAAA,aAAA,EAAA,EALA;AAMA,QAAA,eAAA,EAAA,EANA;AAOA,QAAA,cAAA,EAAA;AAPA,OADA,EAUA;AACA,QAAA,IAAA,EAAA,QADA;AAEA,QAAA,aAAA,EAAA,EAFA;AAGA,QAAA,YAAA,EAAA,EAHA;AAIA,QAAA,cAAA,EAAA,EAJA;AAKA,QAAA,aAAA,EAAA,EALA;AAMA,QAAA,eAAA,EAAA,EANA;AAOA,QAAA,cAAA,EAAA;AAPA,OAVA,EAmBA;AACA,QAAA,IAAA,EAAA,OADA;AAEA,QAAA,aAAA,EAAA,EAFA;AAGA,QAAA,YAAA,EAAA,EAHA;AAIA,QAAA,cAAA,EAAA,EAJA;AAKA,QAAA,aAAA,EAAA,EALA;AAMA,QAAA,eAAA,EAAA,EANA;AAOA,QAAA,cAAA,EAAA;AAPA,OAnBA,EA4BA;AACA,QAAA,IAAA,EAAA,OADA;AAEA,QAAA,aAAA,EAAA,EAFA;AAGA,QAAA,YAAA,EAAA,EAHA;AAIA,QAAA,cAAA,EAAA,EAJA;AAKA,QAAA,aAAA,EAAA,EALA;AAMA,QAAA,eAAA,EAAA,EANA;AAOA,QAAA,cAAA,EAAA;AAPA,OA5BA,CA3FA;AAiIA,MAAA,cAAA,EAAA,CACA;AACA,QAAA,IAAA,EAAA,WADA;AAEA,QAAA,aAAA,EAAA,EAFA;AAGA,QAAA,YAAA,EAAA,EAHA;AAIA,QAAA,cAAA,EAAA,EAJA;AAKA,QAAA,aAAA,EAAA,EALA;AAMA,QAAA,eAAA,EAAA,EANA;AAOA,QAAA,cAAA,EAAA;AAPA,OADA,EAUA;AACA,QAAA,IAAA,EAAA,QADA;AAEA,QAAA,aAAA,EAAA,EAFA;AAGA,QAAA,YAAA,EAAA,EAHA;AAIA,QAAA,cAAA,EAAA,EAJA;AAKA,QAAA,aAAA,EAAA,EALA;AAMA,QAAA,eAAA,EAAA,EANA;AAOA,QAAA,cAAA,EAAA;AAPA,OAVA,EAmBA;AACA,QAAA,IAAA,EAAA,OADA;AAEA,QAAA,aAAA,EAAA,EAFA;AAGA,QAAA,YAAA,EAAA,EAHA;AAIA,QAAA,cAAA,EAAA,EAJA;AAKA,QAAA,aAAA,EAAA,EALA;AAMA,QAAA,eAAA,EAAA,EANA;AAOA,QAAA,cAAA,EAAA;AAPA,OAnBA,EA4BA;AACA,QAAA,IAAA,EAAA,OADA;AAEA,QAAA,aAAA,EAAA,EAFA;AAGA,QAAA,YAAA,EAAA,EAHA;AAIA,QAAA,cAAA,EAAA,EAJA;AAKA,QAAA,aAAA,EAAA,EALA;AAMA,QAAA,eAAA,EAAA,EANA;AAOA,QAAA,cAAA,EAAA;AAPA,OA5BA;AAjIA,KAAA;AAwKA,GA1KA;AA2KA,EAAA,OA3KA,qBA2KA;AACA,SAAA,MAAA,GAAA,IAAA,CADA,CAEA;;AACA,SAAA,eAAA;AACA,SAAA,QAAA;AACA,GAhLA;AAiLA,EAAA,OAAA,EAAA;AACA,IAAA,aADA,yBACA,IADA,EACA;AACA,UAAA,IAAA,IAAA,SAAA,EAAA;AACA,YAAA,QAAA,GAAA,KAAA,cAAA,CAAA,IAAA,CAAA,UAAA,CAAA;AAAA,iBAAA,CAAA,CAAA,IAAA,IAAA,IAAA;AAAA,SAAA,EAAA,IAAA;AACA,eAAA,QAAA;AACA,OAHA,MAGA;AACA,eAAA,EAAA;AACA;AACA,KARA;AASA,IAAA,gBATA,4BASA,IATA,EASA,GATA,EASA,KATA,EASA;AACA,cAAA,IAAA;AACA,aAAA,eAAA;AACA,eAAA,cAAA,CAAA,KAAA,EAAA,aAAA,GAAA,GAAA,CAAA,aAAA;AACA;;AACA,aAAA,cAAA;AACA,eAAA,cAAA,CAAA,KAAA,EAAA,YAAA,GAAA,GAAA,CAAA,YAAA;AACA;;AACA,aAAA,gBAAA;AACA,eAAA,cAAA,CAAA,KAAA,EAAA,cAAA,GAAA,GAAA,CAAA,cAAA;AACA;;AACA,aAAA,eAAA;AACA,eAAA,cAAA,CAAA,KAAA,EAAA,aAAA,GAAA,GAAA,CAAA,aAAA;AACA;;AACA,aAAA,iBAAA;AACA,eAAA,cAAA,CAAA,KAAA,EAAA,eAAA,GAAA,GAAA,CAAA,eAAA;AACA;;AACA,aAAA,gBAAA;AACA,eAAA,cAAA,CAAA,KAAA,EAAA,cAAA,GAAA,GAAA,CAAA,cAAA;AACA;AAlBA;AAoBA,KA9BA;AA+BA,IAAA,QA/BA,sBA+BA;AAAA;;AACA,UAAA,MAAA,GAAA;AACA,QAAA,WAAA,EAAA,IADA;AAEA,QAAA,MAAA,EAAA;AAFA,OAAA;AAKA,WAAA,QAAA,CAAA,gBAAA,CAAA,IAAA,CAAA,MAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,YAAA,GAAA,CAAA,KAAA,GAAA,CAAA,EAAA;AACA,UAAA,GAAA,CAAA,OAAA,CAAA,CAAA,EAAA,UAAA,CAAA,OAAA,CAAA,UAAA,GAAA,EAAA;AACA,oBAAA,GAAA,CAAA,GAAA;AACA,mBAAA,yBAAA;AACA,gBAAA,MAAA,CAAA,UAAA,CAAA,CAAA,EAAA,aAAA,GAAA,GAAA,CAAA,KAAA;AACA;;AACA,mBAAA,wBAAA;AACA,gBAAA,MAAA,CAAA,UAAA,CAAA,CAAA,EAAA,YAAA,GAAA,GAAA,CAAA,KAAA;AACA;;AACA,mBAAA,0BAAA;AACA,gBAAA,MAAA,CAAA,UAAA,CAAA,CAAA,EAAA,cAAA,GAAA,GAAA,CAAA,KAAA;AACA;;AACA,mBAAA,yBAAA;AACA,gBAAA,MAAA,CAAA,UAAA,CAAA,CAAA,EAAA,aAAA,GAAA,GAAA,CAAA,KAAA;AACA;;AACA,mBAAA,2BAAA;AACA,gBAAA,MAAA,CAAA,UAAA,CAAA,CAAA,EAAA,eAAA,GAAA,GAAA,CAAA,KAAA;AACA;;AACA,mBAAA,0BAAA;AACA,gBAAA,MAAA,CAAA,UAAA,CAAA,CAAA,EAAA,cAAA,GAAA,GAAA,CAAA,KAAA;AACA;;AACA,mBAAA,sBAAA;AACA,gBAAA,MAAA,CAAA,UAAA,CAAA,CAAA,EAAA,aAAA,GAAA,GAAA,CAAA,KAAA;AACA;;AACA,mBAAA,qBAAA;AACA,gBAAA,MAAA,CAAA,UAAA,CAAA,CAAA,EAAA,YAAA,GAAA,GAAA,CAAA,KAAA;AACA;;AACA,mBAAA,uBAAA;AACA,gBAAA,MAAA,CAAA,UAAA,CAAA,CAAA,EAAA,cAAA,GAAA,GAAA,CAAA,KAAA;AACA;;AACA,mBAAA,sBAAA;AACA,gBAAA,MAAA,CAAA,UAAA,CAAA,CAAA,EAAA,aAAA,GAAA,GAAA,CAAA,KAAA;AACA;;AACA,mBAAA,wBAAA;AACA,gBAAA,MAAA,CAAA,UAAA,CAAA,CAAA,EAAA,eAAA,GAAA,GAAA,CAAA,KAAA;AACA;;AACA,mBAAA,uBAAA;AACA,gBAAA,MAAA,CAAA,UAAA,CAAA,CAAA,EAAA,cAAA,GAAA,GAAA,CAAA,KAAA;AACA;;AACA,mBAAA,qBAAA;AACA,gBAAA,MAAA,CAAA,UAAA,CAAA,CAAA,EAAA,aAAA,GAAA,GAAA,CAAA,KAAA;AACA;;AACA,mBAAA,oBAAA;AACA,gBAAA,MAAA,CAAA,UAAA,CAAA,CAAA,EAAA,YAAA,GAAA,GAAA,CAAA,KAAA;AACA;;AACA,mBAAA,sBAAA;AACA,gBAAA,MAAA,CAAA,UAAA,CAAA,CAAA,EAAA,cAAA,GAAA,GAAA,CAAA,KAAA;AACA;;AACA,mBAAA,qBAAA;AACA,gBAAA,MAAA,CAAA,UAAA,CAAA,CAAA,EAAA,aAAA,GAAA,GAAA,CAAA,KAAA;AACA;;AACA,mBAAA,uBAAA;AACA,gBAAA,MAAA,CAAA,UAAA,CAAA,CAAA,EAAA,eAAA,GAAA,GAAA,CAAA,KAAA;AACA;;AACA,mBAAA,sBAAA;AACA,gBAAA,MAAA,CAAA,UAAA,CAAA,CAAA,EAAA,cAAA,GAAA,GAAA,CAAA,KAAA;AACA;;AACA,mBAAA,qBAAA;AACA,gBAAA,MAAA,CAAA,UAAA,CAAA,CAAA,EAAA,aAAA,GAAA,GAAA,CAAA,KAAA;AACA;;AACA,mBAAA,oBAAA;AACA,gBAAA,MAAA,CAAA,UAAA,CAAA,CAAA,EAAA,YAAA,GAAA,GAAA,CAAA,KAAA;AACA;;AACA,mBAAA,sBAAA;AACA,gBAAA,MAAA,CAAA,UAAA,CAAA,CAAA,EAAA,cAAA,GAAA,GAAA,CAAA,KAAA;AACA;;AACA,mBAAA,qBAAA;AACA,gBAAA,MAAA,CAAA,UAAA,CAAA,CAAA,EAAA,aAAA,GAAA,GAAA,CAAA,KAAA;AACA;;AACA,mBAAA,uBAAA;AACA,gBAAA,MAAA,CAAA,UAAA,CAAA,CAAA,EAAA,eAAA,GAAA,GAAA,CAAA,KAAA;AACA;;AACA,mBAAA,sBAAA;AACA,gBAAA,MAAA,CAAA,UAAA,CAAA,CAAA,EAAA,cAAA,GAAA,GAAA,CAAA,KAAA;AACA;AAxEA;AA0EA,WA3EA;AA4EA;;AACA,QAAA,MAAA,CAAA,cAAA,GAAA,IAAA,CAAA,KAAA,CAAA,IAAA,CAAA,SAAA,CAAA,MAAA,CAAA,UAAA,CAAA,CAAA;AACA,OAhFA;AAiFA,KAtHA;AAuHA,IAAA,QAvHA,sBAuHA;AAAA;;AACA,UAAA,QAAA,GAAA,EAAA;AACA,UAAA,OAAA,GAAA;AACA,QAAA,QAAA,EAAA,gBADA;AAEA,QAAA,UAAA,EAAA;AAFA,OAAA;AAIA,WAAA,cAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,YAAA,aAAA,GAAA,CACA;AACA,UAAA,GAAA,EAAA,IAAA,CAAA,IAAA,GAAA,gBADA;AAEA,UAAA,KAAA,EAAA,IAAA,CAAA;AAFA,SADA,EAKA;AACA,UAAA,GAAA,EAAA,IAAA,CAAA,IAAA,GAAA,eADA;AAEA,UAAA,KAAA,EAAA,IAAA,CAAA;AAFA,SALA,EASA;AACA,UAAA,GAAA,EAAA,IAAA,CAAA,IAAA,GAAA,iBADA;AAEA,UAAA,KAAA,EAAA,IAAA,CAAA;AAFA,SATA,EAaA;AACA,UAAA,GAAA,EAAA,IAAA,CAAA,IAAA,GAAA,gBADA;AAEA,UAAA,KAAA,EAAA,IAAA,CAAA;AAFA,SAbA,EAiBA;AACA,UAAA,GAAA,EAAA,IAAA,CAAA,IAAA,GAAA,kBADA;AAEA,UAAA,KAAA,EAAA,IAAA,CAAA;AAFA,SAjBA,EAqBA;AACA,UAAA,GAAA,EAAA,IAAA,CAAA,IAAA,GAAA,iBADA;AAEA,UAAA,KAAA,EAAA,IAAA,CAAA;AAFA,SArBA,CAAA;AA0BA,QAAA,OAAA,CAAA,UAAA,gCAAA,OAAA,CAAA,UAAA,GAAA,aAAA;AACA,OA5BA;AA6BA,MAAA,QAAA,CAAA,IAAA,CAAA,OAAA;AACA,WAAA,QAAA,CAAA,iBAAA,CAAA,IAAA,CAAA,QAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,QAAA,MAAA,CAAA,MAAA,GAAA,KAAA;;AACA,QAAA,MAAA,CAAA,QAAA;;AACA,QAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,oBAAA,EAAA,MAAA,GAAA,OAAA,EAAA;AACA,OAJA;AAKA,KAhKA;AAiKA,IAAA,SAjKA,uBAiKA;AACA,WAAA,MAAA,GAAA,KAAA;AACA,KAnKA;AAoKA,IAAA,QApKA,sBAoKA;AACA,WAAA,MAAA,GAAA,IAAA;AACA,KAtKA;AAuKA,IAAA,eAvKA,6BAuKA;AACA,MAAA,UAAA,CACA,YAAA;AACA,YAAA,KAAA,GAAA,QAAA,CAAA,aAAA,CAAA,0BAAA,CAAA;;AACA,YAAA,KAAA,EAAA;AACA,UAAA,KAAA,CAAA,KAAA,CAAA,MAAA,GAAA,OAAA;AACA;AACA,OALA,CAKA,IALA,CAKA,IALA,CADA,EAOA,EAPA,CAAA;AASA,KAjLA;AAkLA,IAAA,WAlLA,uBAkLA,QAlLA,EAkLA;AACA,UAAA,SAAA,GAAA,CAAA;;AACA,UAAA,QAAA,CAAA,CAAA,CAAA,KAAA,GAAA,EAAA;AACA,QAAA,SAAA,GAAA,CAAA,CAAA;AACA,QAAA,QAAA,GAAA,QAAA,CAAA,MAAA,CAAA,CAAA,CAAA;AACA;;AACA,aAAA,UAAA,CAAA,EAAA,CAAA,EAAA;AACA,YAAA,MAAA,GACA,CAAA,CAAA,QAAA,CAAA,GAAA,CAAA,CAAA,QAAA,CAAA,GAAA,CAAA,CAAA,GAAA,CAAA,CAAA,QAAA,CAAA,GAAA,CAAA,CAAA,QAAA,CAAA,GAAA,CAAA,GAAA,CADA;AAEA,eAAA,MAAA,GAAA,SAAA;AACA,OAJA;AAKA,KA7LA;AA8LA,IAAA,UA9LA,wBA8LA;AACA,WAAA,cAAA;AACA,KAhMA;AAiMA,IAAA,cAjMA,4BAiMA;AACA,UAAA,KAAA,GAAA,IAAA;;AACA,MAAA,KAAA,CAAA,UAAA,CAAA,IAAA,CAAA;AACA,QAAA,IAAA,EAAA,KAAA,EAAA,CAAA,qBAAA,CADA;AAEA,QAAA,OAAA,EAAA,MAAA,CAAA;AAFA,OAAA;AAIA,KAvMA;AAwMA,IAAA,YAxMA,wBAwMA,GAxMA,EAwMA;AACA,UAAA,GAAA,EAAA;AACA,eAAA,MAAA,CAAA,GAAA,CAAA,CAAA,MAAA,CAAA,YAAA,CAAA;AACA;;AACA,aAAA,EAAA;AACA,KA7MA;AA8MA,IAAA,gBA9MA,4BA8MA,GA9MA,EA8MA;AACA,UAAA,GAAA,EAAA;AACA,eAAA,MAAA,CAAA,GAAA,CAAA,CAAA,MAAA,CAAA,qBAAA,CAAA;AACA;;AACA,aAAA,EAAA;AACA,KAnNA;AAoNA,IAAA,MApNA,oBAoNA;AAAA;;AACA,WAAA,MAAA,GAAA,KAAA;AACA,MAAA,UAAA,CAAA,YAAA;AACA,QAAA,MAAA,CAAA,KAAA,CAAA,eAAA;AACA,OAFA,EAEA,GAFA,CAAA;AAGA;AAzNA;AAjLA,CAAA", "sourcesContent": ["<template>\r\n  <div class=\"personal-data\">\r\n    <Modal\r\n      class=\"about-data-modal\"\r\n      :closable=\"false\"\r\n      :mask-closable=\"false\"\r\n      v-model=\"isShow\"\r\n      class-name=\"vertical-center-modal\"\r\n      width=\"1100\"\r\n    >\r\n      <div slot=\"header\" class=\"header\">\r\n        <div>\r\n          <div style=\"float: left\">\r\n            <span class=\"title\">{{ $t(\"LocaleString.L30107\") }}</span>\r\n          </div>\r\n          <div style=\"float: right; margin-bottom: 10px\">\r\n            <Button\r\n              v-show=\"!isEdit\"\r\n              type=\"primary\"\r\n              class=\"font-small\"\r\n              @click=\"editData\"\r\n              >{{ $t(\"LocaleString.B20014\") }}</Button\r\n            >\r\n            <Button class=\"font-small\" @click=\"cancel\" v-show=\"!isEdit\">{{\r\n              $t(\"LocaleString.B00044\")\r\n            }}</Button>\r\n            <Button\r\n              v-show=\"isEdit\"\r\n              type=\"primary\"\r\n              class=\"font-small\"\r\n              @click=\"saveData\"\r\n              >{{ $t(\"LocaleString.B00012\") }}</Button\r\n            >\r\n            <Button v-show=\"isEdit\" class=\"font-small\" @click=\"clearData\">{{\r\n              $t(\"LocaleString.B00015\")\r\n            }}</Button>\r\n          </div>\r\n          <div style=\"clear: both\"></div>\r\n        </div>\r\n      </div>\r\n      <div class=\"content\">\r\n        <Table\r\n          v-if=\"!isEdit\"\r\n          class=\"config-table\"\r\n          :columns=\"columnsList\"\r\n          :data=\"configList\"\r\n          :no-data-text=\"noDataText\"\r\n        >\r\n          <template slot-scope=\"{ row, index }\" slot=\"type\">\r\n            <span> {{ row.type }} </span>\r\n          </template>\r\n        </Table>\r\n        <Table\r\n          v-if=\"isEdit\"\r\n          class=\"config-table\"\r\n          :columns=\"columnsEdit\"\r\n          :data=\"configListEdit\"\r\n          :no-data-text=\"noDataText\"\r\n        >\r\n          <template slot-scope=\"{ row, index }\" slot=\"type\">\r\n            <span> {{ getObjectType(row.type) }} </span>\r\n          </template>\r\n          <template slot-scope=\"{ row, index }\" slot=\"longPress_cht\">\r\n            <Input\r\n              type=\"text\"\r\n              v-model=\"row.longPress_cht\"\r\n              maxlength=\"20\"\r\n              @on-change=\"onEditDataChange('longPress_cht', row, index)\"\r\n            />\r\n          </template>\r\n          <template slot-scope=\"{ row, index }\" slot=\"longPress_en\">\r\n            <Input\r\n              type=\"text\"\r\n              v-model=\"row.longPress_en\"\r\n              maxlength=\"20\"\r\n              @on-change=\"onEditDataChange('longPress_en', row, index)\"\r\n            />\r\n          </template>\r\n          <template slot-scope=\"{ row, index }\" slot=\"shortClick_cht\">\r\n            <Input\r\n              type=\"text\"\r\n              v-model=\"row.shortClick_cht\"\r\n              maxlength=\"20\"\r\n              @on-change=\"onEditDataChange('shortClick_cht', row, index)\"\r\n            />\r\n          </template>\r\n          <template slot-scope=\"{ row, index }\" slot=\"shortClick_en\">\r\n            <Input\r\n              type=\"text\"\r\n              v-model=\"row.shortClick_en\"\r\n              maxlength=\"20\"\r\n              @on-change=\"onEditDataChange('shortClick_en', row, index)\"\r\n            />\r\n          </template>\r\n          <template slot-scope=\"{ row, index }\" slot=\"doubleClick_cht\">\r\n            <Input\r\n              type=\"text\"\r\n              v-model=\"row.doubleClick_cht\"\r\n              maxlength=\"20\"\r\n              @on-change=\"onEditDataChange('doubleClick_cht', row, index)\"\r\n            />\r\n          </template>\r\n          <template slot-scope=\"{ row, index }\" slot=\"doubleClick_en\">\r\n            <Input\r\n              type=\"text\"\r\n              v-model=\"row.doubleClick_en\"\r\n              maxlength=\"20\"\r\n              @on-change=\"onEditDataChange('doubleClick_en', row, index)\"\r\n            />\r\n          </template>\r\n        </Table>\r\n      </div>\r\n      <div slot=\"footer\" style=\"text-align: center; margin-top: 10px\"></div>\r\n    </Modal>\r\n  </div>\r\n</template>\r\n<script type='es6'>\r\nimport Config from \"@/common/config\";\r\nlet moment = require(\"moment\");\r\nexport default {\r\n  data() {\r\n    return {\r\n      isShow: false,\r\n      isEdit: false,\r\n      systemInfo: [],\r\n      objectTypeList: [\r\n        {\r\n          type: \"equipment\",\r\n          name: this.$t(\"LocaleString.D00008\"),\r\n        },\r\n        {\r\n          type: \"people\",\r\n          name: this.$t(\"LocaleString.D00007\"),\r\n        },\r\n        {\r\n          type: \"space\",\r\n          name: this.$t(\"LocaleString.D00009\"),\r\n        },\r\n        {\r\n          type: \"other\",\r\n          name: this.$t(\"LocaleString.D00010\"),\r\n        },\r\n      ],\r\n      noDataText: this.$t(\"LocaleString.L00081\"),\r\n      columnsList: [\r\n        {\r\n          title: this.$t(\"LocaleString.L00047\"),\r\n          key: \"type\",\r\n          width: 100,\r\n          render: (h, params) => {\r\n            return h(\"span\", this.getObjectType(params.row.type));\r\n          },\r\n        },\r\n        {\r\n          title: this.$t(\"LocaleString.L30108\"),\r\n          key: \"longPress_cht\",\r\n        },\r\n        {\r\n          title: this.$t(\"LocaleString.L30109\"),\r\n          key: \"longPress_en\",\r\n        },\r\n        {\r\n          title: this.$t(\"LocaleString.L30110\"),\r\n          key: \"shortClick_cht\",\r\n        },\r\n        {\r\n          title: this.$t(\"LocaleString.L30111\"),\r\n          key: \"shortClick_en\",\r\n        },\r\n        {\r\n          title: this.$t(\"LocaleString.L30112\"),\r\n          key: \"doubleClick_cht\",\r\n        },\r\n        {\r\n          title: this.$t(\"LocaleString.L30113\"),\r\n          key: \"doubleClick_en\",\r\n        },\r\n      ],\r\n      columnsEdit: [\r\n        {\r\n          title: this.$t(\"LocaleString.L00047\"),\r\n          slot: \"type\",\r\n          width: 100,\r\n          render: (h, params) => {\r\n            return h(\"span\", this.getObjectType(params.row.type));\r\n          },\r\n        },\r\n        {\r\n          title: this.$t(\"LocaleString.L30108\"),\r\n          slot: \"longPress_cht\",\r\n        },\r\n        {\r\n          title: this.$t(\"LocaleString.L30109\"),\r\n          slot: \"longPress_en\",\r\n        },\r\n        {\r\n          title: this.$t(\"LocaleString.L30110\"),\r\n          slot: \"shortClick_cht\",\r\n        },\r\n        {\r\n          title: this.$t(\"LocaleString.L30111\"),\r\n          slot: \"shortClick_en\",\r\n        },\r\n        {\r\n          title: this.$t(\"LocaleString.L30112\"),\r\n          slot: \"doubleClick_cht\",\r\n        },\r\n        {\r\n          title: this.$t(\"LocaleString.L30113\"),\r\n          slot: \"doubleClick_en\",\r\n        },\r\n      ],\r\n      configList: [\r\n        {\r\n          type: \"equipment\",\r\n          longPress_cht: \"\",\r\n          longPress_en: \"\",\r\n          shortClick_cht: \"\",\r\n          shortClick_en: \"\",\r\n          doubleClick_cht: \"\",\r\n          doubleClick_en: \"\",\r\n        },\r\n        {\r\n          type: \"people\",\r\n          longPress_cht: \"\",\r\n          longPress_en: \"\",\r\n          shortClick_cht: \"\",\r\n          shortClick_en: \"\",\r\n          doubleClick_cht: \"\",\r\n          doubleClick_en: \"\",\r\n        },\r\n        {\r\n          type: \"space\",\r\n          longPress_cht: \"\",\r\n          longPress_en: \"\",\r\n          shortClick_cht: \"\",\r\n          shortClick_en: \"\",\r\n          doubleClick_cht: \"\",\r\n          doubleClick_en: \"\",\r\n        },\r\n        {\r\n          type: \"other\",\r\n          longPress_cht: \"\",\r\n          longPress_en: \"\",\r\n          shortClick_cht: \"\",\r\n          shortClick_en: \"\",\r\n          doubleClick_cht: \"\",\r\n          doubleClick_en: \"\",\r\n        },\r\n      ],\r\n      configListEdit: [\r\n        {\r\n          type: \"equipment\",\r\n          longPress_cht: \"\",\r\n          longPress_en: \"\",\r\n          shortClick_cht: \"\",\r\n          shortClick_en: \"\",\r\n          doubleClick_cht: \"\",\r\n          doubleClick_en: \"\",\r\n        },\r\n        {\r\n          type: \"people\",\r\n          longPress_cht: \"\",\r\n          longPress_en: \"\",\r\n          shortClick_cht: \"\",\r\n          shortClick_en: \"\",\r\n          doubleClick_cht: \"\",\r\n          doubleClick_en: \"\",\r\n        },\r\n        {\r\n          type: \"space\",\r\n          longPress_cht: \"\",\r\n          longPress_en: \"\",\r\n          shortClick_cht: \"\",\r\n          shortClick_en: \"\",\r\n          doubleClick_cht: \"\",\r\n          doubleClick_en: \"\",\r\n        },\r\n        {\r\n          type: \"other\",\r\n          longPress_cht: \"\",\r\n          longPress_en: \"\",\r\n          shortClick_cht: \"\",\r\n          shortClick_en: \"\",\r\n          doubleClick_cht: \"\",\r\n          doubleClick_en: \"\",\r\n        },\r\n      ],\r\n    };\r\n  },\r\n  mounted() {\r\n    this.isShow = true;\r\n    //this.getSystemAbout();\r\n    this.normalizeHeight();\r\n    this.loadData();\r\n  },\r\n  methods: {\r\n    getObjectType(type) {\r\n      if (type != undefined) {\r\n        let typeData = this.objectTypeList.find((t) => t.type == type).name;\r\n        return typeData;\r\n      } else {\r\n        return \"\";\r\n      }\r\n    },\r\n    onEditDataChange(type, row, index) {\r\n      switch (type) {\r\n        case \"longPress_cht\":\r\n          this.configListEdit[index].longPress_cht = row.longPress_cht;\r\n          break;\r\n        case \"longPress_en\":\r\n          this.configListEdit[index].longPress_en = row.longPress_en;\r\n          break;\r\n        case \"shortClick_cht\":\r\n          this.configListEdit[index].shortClick_cht = row.shortClick_cht;\r\n          break;\r\n        case \"shortClick_en\":\r\n          this.configListEdit[index].shortClick_en = row.shortClick_en;\r\n          break;\r\n        case \"doubleClick_cht\":\r\n          this.configListEdit[index].doubleClick_cht = row.doubleClick_cht;\r\n          break;\r\n        case \"doubleClick_en\":\r\n          this.configListEdit[index].doubleClick_en = row.doubleClick_en;\r\n          break;\r\n      }\r\n    },\r\n    loadData() {\r\n      let params = {\r\n        inlinecount: true,\r\n        search: \"category eq @SNS@SettingKM\",\r\n      };\r\n\r\n      this.$service.getPOCProperties.send(params).then((res) => {\r\n        if (res.count > 0) {\r\n          res.results[0].properties.forEach((res) => {\r\n            switch (res.key) {\r\n              case \"equipment_longPress_cht\":\r\n                this.configList[0].longPress_cht = res.value;\r\n                break;\r\n              case \"equipment_longPress_en\":\r\n                this.configList[0].longPress_en = res.value;\r\n                break;\r\n              case \"equipment_shortClick_cht\":\r\n                this.configList[0].shortClick_cht = res.value;\r\n                break;\r\n              case \"equipment_shortClick_en\":\r\n                this.configList[0].shortClick_en = res.value;\r\n                break;\r\n              case \"equipment_doubleClick_cht\":\r\n                this.configList[0].doubleClick_cht = res.value;\r\n                break;\r\n              case \"equipment_doubleClick_en\":\r\n                this.configList[0].doubleClick_en = res.value;\r\n                break;\r\n              case \"people_longPress_cht\":\r\n                this.configList[1].longPress_cht = res.value;\r\n                break;\r\n              case \"people_longPress_en\":\r\n                this.configList[1].longPress_en = res.value;\r\n                break;\r\n              case \"people_shortClick_cht\":\r\n                this.configList[1].shortClick_cht = res.value;\r\n                break;\r\n              case \"people_shortClick_en\":\r\n                this.configList[1].shortClick_en = res.value;\r\n                break;\r\n              case \"people_doubleClick_cht\":\r\n                this.configList[1].doubleClick_cht = res.value;\r\n                break;\r\n              case \"people_doubleClick_en\":\r\n                this.configList[1].doubleClick_en = res.value;\r\n                break;\r\n              case \"space_longPress_cht\":\r\n                this.configList[2].longPress_cht = res.value;\r\n                break;\r\n              case \"space_longPress_en\":\r\n                this.configList[2].longPress_en = res.value;\r\n                break;\r\n              case \"space_shortClick_cht\":\r\n                this.configList[2].shortClick_cht = res.value;\r\n                break;\r\n              case \"space_shortClick_en\":\r\n                this.configList[2].shortClick_en = res.value;\r\n                break;\r\n              case \"space_doubleClick_cht\":\r\n                this.configList[2].doubleClick_cht = res.value;\r\n                break;\r\n              case \"space_doubleClick_en\":\r\n                this.configList[2].doubleClick_en = res.value;\r\n                break;\r\n              case \"other_longPress_cht\":\r\n                this.configList[3].longPress_cht = res.value;\r\n                break;\r\n              case \"other_longPress_en\":\r\n                this.configList[3].longPress_en = res.value;\r\n                break;\r\n              case \"other_shortClick_cht\":\r\n                this.configList[3].shortClick_cht = res.value;\r\n                break;\r\n              case \"other_shortClick_en\":\r\n                this.configList[3].shortClick_en = res.value;\r\n                break;\r\n              case \"other_doubleClick_cht\":\r\n                this.configList[3].doubleClick_cht = res.value;\r\n                break;\r\n              case \"other_doubleClick_en\":\r\n                this.configList[3].doubleClick_en = res.value;\r\n                break;\r\n            }\r\n          });\r\n        }\r\n        this.configListEdit = JSON.parse(JSON.stringify(this.configList));\r\n      });\r\n    },\r\n    saveData() {\r\n      let putDatas = [];\r\n      let putData = {\r\n        category: \"@SNS@SettingKM\",\r\n        properties: [],\r\n      };\r\n      this.configListEdit.forEach((item) => {\r\n        let keyValuePairs = [\r\n          {\r\n            key: item.type + \"_longPress_cht\",\r\n            value: item.longPress_cht,\r\n          },\r\n          {\r\n            key: item.type + \"_longPress_en\",\r\n            value: item.longPress_en,\r\n          },\r\n          {\r\n            key: item.type + \"_shortClick_cht\",\r\n            value: item.shortClick_cht,\r\n          },\r\n          {\r\n            key: item.type + \"_shortClick_en\",\r\n            value: item.shortClick_en,\r\n          },\r\n          {\r\n            key: item.type + \"_doubleClick_cht\",\r\n            value: item.doubleClick_cht,\r\n          },\r\n          {\r\n            key: item.type + \"_doubleClick_en\",\r\n            value: item.doubleClick_en,\r\n          },\r\n        ];\r\n        putData.properties = [...putData.properties, ...keyValuePairs];\r\n      });\r\n      putDatas.push(putData);\r\n      this.$service.editPOCProperties.send(putDatas).then((res) => {\r\n        this.isEdit = false;\r\n        this.loadData();\r\n        this.$store.commit(\"setKmConfigChanged\", moment().valueOf());\r\n      });\r\n    },\r\n    clearData() {\r\n      this.isEdit = false;\r\n    },\r\n    editData() {\r\n      this.isEdit = true;\r\n    },\r\n    normalizeHeight() {\r\n      setTimeout(\r\n        function () {\r\n          const modal = document.querySelector(\".ivu-modal-body .content\");\r\n          if (modal) {\r\n            modal.style.height = \"250px\";\r\n          }\r\n        }.bind(this),\r\n        50\r\n      );\r\n    },\r\n    dynamicSort(property) {\r\n      var sortOrder = 1;\r\n      if (property[0] === \"-\") {\r\n        sortOrder = -1;\r\n        property = property.substr(1);\r\n      }\r\n      return function (a, b) {\r\n        var result =\r\n          a[property] < b[property] ? -1 : a[property] > b[property] ? 1 : 0;\r\n        return result * sortOrder;\r\n      };\r\n    },\r\n    refreshBtn() {\r\n      this.getSystemAbout();\r\n    },\r\n    getSystemAbout() {\r\n      let _this = this;\r\n      _this.systemInfo.push({\r\n        name: this.$t(\"LocaleString.S00002\"),\r\n        version: Config.WEB_VERSION,\r\n      });\r\n    },\r\n    computedDate(val) {\r\n      if (val) {\r\n        return moment(val).format(\"YYYY-MM-DD\");\r\n      }\r\n      return \"\";\r\n    },\r\n    computedDateTime(val) {\r\n      if (val) {\r\n        return moment(val).format(\"YYYY-MM-DD HH:mm:ss\");\r\n      }\r\n      return \"\";\r\n    },\r\n    cancel() {\r\n      this.isShow = false;\r\n      setTimeout(() => {\r\n        this.$emit(\"closeKMConfig\");\r\n      }, 500);\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n.about-data-modal {\r\n  // .vertical-center-modal {\r\n  //   display: flex;\r\n  //   align-items: center;\r\n  //   justify-content: center;\r\n  //   .ivu-modal {\r\n  //     top: 0;\r\n  //   }\r\n  // }\r\n  .ivu-modal-close {\r\n    top: 15px;\r\n    right: 20px;\r\n  }\r\n  .content {\r\n    padding: 4px 14px;\r\n    overflow-y: auto;\r\n    //height: 700px;\r\n    //height: 400px;\r\n  }\r\n\r\n  @media only screen and (min-width: 1200px) {\r\n    .ivu-modal-close {\r\n      top: 18px;\r\n    }\r\n  }\r\n  .ivu-modal-header {\r\n    padding: 15px 20px;\r\n    border-bottom: 1px solid #f7f7f7;\r\n    .header {\r\n      overflow: hidden;\r\n      .left {\r\n        float: left;\r\n        width: 6px;\r\n        height: 24px;\r\n        margin-right: 8px;\r\n        background-color: #0ac7c0;\r\n        border-radius: 3px;\r\n      }\r\n      .title {\r\n        float: left;\r\n        height: 24px;\r\n        line-height: 24px;\r\n        font-size: 14px;\r\n        font-weight: bold;\r\n        color: #999;\r\n        span {\r\n          color: #333;\r\n        }\r\n      }\r\n      .es-btn {\r\n        float: right;\r\n        margin-right: 35px;\r\n      }\r\n      @media only screen and (min-width: 1200px) {\r\n        .left {\r\n          height: 30px;\r\n        }\r\n        .title {\r\n          height: 30px;\r\n          line-height: 30px;\r\n          font-size: 18px;\r\n        }\r\n      }\r\n    }\r\n  }\r\n  .ivu-modal-body {\r\n    .content {\r\n      padding: 4px 14px;\r\n      overflow-y: auto;\r\n      .row {\r\n        margin-bottom: 20px;\r\n        .item {\r\n          padding: 15px;\r\n          border: 1px solid #f7f7f7;\r\n          border-radius: 4px;\r\n          label {\r\n            color: #999;\r\n            font-weight: bold;\r\n          }\r\n          p {\r\n            margin-top: 10px;\r\n            color: #333;\r\n          }\r\n        }\r\n      }\r\n      .form-item {\r\n        padding: 15px 15px 22px;\r\n        margin-bottom: 16px;\r\n        border-radius: 4px;\r\n        background-color: #f7f7f7;\r\n      }\r\n    }\r\n  }\r\n  .ivu-modal-footer {\r\n    border-top: none;\r\n    padding: 0;\r\n  }\r\n}\r\n</style>\r\n<style lang=\"less\" scoped>\r\n/deep/ .ivu-btn-primary {\r\n  color: #fff;\r\n  background-color: #31babb;\r\n  border-color: #31babb;\r\n}\r\n\r\n/deep/ .ivu-btn-primary:hover {\r\n  color: #fff !important;\r\n  border-color: #31babb;\r\n}\r\n\r\n/deep/ .ivu-switch-checked {\r\n  background-color: #31babb;\r\n  border-color: #31babb;\r\n}\r\n.musicIcon {\r\n  vertical-align: middle;\r\n  margin-left: 5px;\r\n  color: #31babb;\r\n  cursor: pointer;\r\n}\r\n</style>\r\n"], "sourceRoot": "src/components/administrative/systemManagement"}]}