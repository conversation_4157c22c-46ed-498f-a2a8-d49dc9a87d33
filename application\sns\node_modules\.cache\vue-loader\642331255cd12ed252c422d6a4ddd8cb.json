{"remainingRequest": "E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\src\\components\\administrative\\systemManagement\\kmConfig.vue?vue&type=style&index=0&lang=scss&", "dependencies": [{"path": "E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\src\\components\\administrative\\systemManagement\\kmConfig.vue", "mtime": 1754362736975}, {"path": "E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoNCi5hYm91dC1kYXRhLW1vZGFsIHsNCiAgLy8gLnZlcnRpY2FsLWNlbnRlci1tb2RhbCB7DQogIC8vICAgZGlzcGxheTogZmxleDsNCiAgLy8gICBhbGlnbi1pdGVtczogY2VudGVyOw0KICAvLyAgIGp1c3RpZnktY29udGVudDogY2VudGVyOw0KICAvLyAgIC5pdnUtbW9kYWwgew0KICAvLyAgICAgdG9wOiAwOw0KICAvLyAgIH0NCiAgLy8gfQ0KICAuaXZ1LW1vZGFsLWNsb3NlIHsNCiAgICB0b3A6IDE1cHg7DQogICAgcmlnaHQ6IDIwcHg7DQogIH0NCiAgLmNvbnRlbnQgew0KICAgIHBhZGRpbmc6IDRweCAxNHB4Ow0KICAgIG92ZXJmbG93LXk6IGF1dG87DQogICAgLy9oZWlnaHQ6IDcwMHB4Ow0KICAgIC8vaGVpZ2h0OiA0MDBweDsNCiAgfQ0KDQogIEBtZWRpYSBvbmx5IHNjcmVlbiBhbmQgKG1pbi13aWR0aDogMTIwMHB4KSB7DQogICAgLml2dS1tb2RhbC1jbG9zZSB7DQogICAgICB0b3A6IDE4cHg7DQogICAgfQ0KICB9DQogIC5pdnUtbW9kYWwtaGVhZGVyIHsNCiAgICBwYWRkaW5nOiAxNXB4IDIwcHg7DQogICAgYm9yZGVyLWJvdHRvbTogMXB4IHNvbGlkICNmN2Y3Zjc7DQogICAgLmhlYWRlciB7DQogICAgICBvdmVyZmxvdzogaGlkZGVuOw0KICAgICAgLmxlZnQgew0KICAgICAgICBmbG9hdDogbGVmdDsNCiAgICAgICAgd2lkdGg6IDZweDsNCiAgICAgICAgaGVpZ2h0OiAyNHB4Ow0KICAgICAgICBtYXJnaW4tcmlnaHQ6IDhweDsNCiAgICAgICAgYmFja2dyb3VuZC1jb2xvcjogIzBhYzdjMDsNCiAgICAgICAgYm9yZGVyLXJhZGl1czogM3B4Ow0KICAgICAgfQ0KICAgICAgLnRpdGxlIHsNCiAgICAgICAgZmxvYXQ6IGxlZnQ7DQogICAgICAgIGhlaWdodDogMjRweDsNCiAgICAgICAgbGluZS1oZWlnaHQ6IDI0cHg7DQogICAgICAgIGZvbnQtc2l6ZTogMTRweDsNCiAgICAgICAgZm9udC13ZWlnaHQ6IGJvbGQ7DQogICAgICAgIGNvbG9yOiAjOTk5Ow0KICAgICAgICBzcGFuIHsNCiAgICAgICAgICBjb2xvcjogIzMzMzsNCiAgICAgICAgfQ0KICAgICAgfQ0KICAgICAgLmVzLWJ0biB7DQogICAgICAgIGZsb2F0OiByaWdodDsNCiAgICAgICAgbWFyZ2luLXJpZ2h0OiAzNXB4Ow0KICAgICAgfQ0KICAgICAgQG1lZGlhIG9ubHkgc2NyZWVuIGFuZCAobWluLXdpZHRoOiAxMjAwcHgpIHsNCiAgICAgICAgLmxlZnQgew0KICAgICAgICAgIGhlaWdodDogMzBweDsNCiAgICAgICAgfQ0KICAgICAgICAudGl0bGUgew0KICAgICAgICAgIGhlaWdodDogMzBweDsNCiAgICAgICAgICBsaW5lLWhlaWdodDogMzBweDsNCiAgICAgICAgICBmb250LXNpemU6IDE4cHg7DQogICAgICAgIH0NCiAgICAgIH0NCiAgICB9DQogIH0NCiAgLml2dS1tb2RhbC1ib2R5IHsNCiAgICAuY29udGVudCB7DQogICAgICBwYWRkaW5nOiA0cHggMTRweDsNCiAgICAgIG92ZXJmbG93LXk6IGF1dG87DQogICAgICAucm93IHsNCiAgICAgICAgbWFyZ2luLWJvdHRvbTogMjBweDsNCiAgICAgICAgLml0ZW0gew0KICAgICAgICAgIHBhZGRpbmc6IDE1cHg7DQogICAgICAgICAgYm9yZGVyOiAxcHggc29saWQgI2Y3ZjdmNzsNCiAgICAgICAgICBib3JkZXItcmFkaXVzOiA0cHg7DQogICAgICAgICAgbGFiZWwgew0KICAgICAgICAgICAgY29sb3I6ICM5OTk7DQogICAgICAgICAgICBmb250LXdlaWdodDogYm9sZDsNCiAgICAgICAgICB9DQogICAgICAgICAgcCB7DQogICAgICAgICAgICBtYXJnaW4tdG9wOiAxMHB4Ow0KICAgICAgICAgICAgY29sb3I6ICMzMzM7DQogICAgICAgICAgfQ0KICAgICAgICB9DQogICAgICB9DQogICAgICAuZm9ybS1pdGVtIHsNCiAgICAgICAgcGFkZGluZzogMTVweCAxNXB4IDIycHg7DQogICAgICAgIG1hcmdpbi1ib3R0b206IDE2cHg7DQogICAgICAgIGJvcmRlci1yYWRpdXM6IDRweDsNCiAgICAgICAgYmFja2dyb3VuZC1jb2xvcjogI2Y3ZjdmNzsNCiAgICAgIH0NCiAgICB9DQogIH0NCiAgLml2dS1tb2RhbC1mb290ZXIgew0KICAgIGJvcmRlci10b3A6IG5vbmU7DQogICAgcGFkZGluZzogMDsNCiAgfQ0KfQ0K"}, {"version": 3, "sources": ["kmConfig.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAugBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "kmConfig.vue", "sourceRoot": "src/components/administrative/systemManagement", "sourcesContent": ["<template>\r\n  <div class=\"personal-data\">\r\n    <Modal\r\n      class=\"about-data-modal\"\r\n      :closable=\"false\"\r\n      :mask-closable=\"false\"\r\n      v-model=\"isShow\"\r\n      class-name=\"vertical-center-modal\"\r\n      width=\"1100\"\r\n    >\r\n      <div slot=\"header\" class=\"header\">\r\n        <div>\r\n          <div style=\"float: left\">\r\n            <span class=\"title\">{{ $t(\"LocaleString.L30107\") }}</span>\r\n          </div>\r\n          <div style=\"float: right; margin-bottom: 10px\">\r\n            <Button\r\n              v-show=\"!isEdit\"\r\n              type=\"primary\"\r\n              class=\"font-small\"\r\n              @click=\"editData\"\r\n              >{{ $t(\"LocaleString.B20014\") }}</Button\r\n            >\r\n            <Button class=\"font-small\" @click=\"cancel\" v-show=\"!isEdit\">{{\r\n              $t(\"LocaleString.B00044\")\r\n            }}</Button>\r\n            <Button\r\n              v-show=\"isEdit\"\r\n              type=\"primary\"\r\n              class=\"font-small\"\r\n              @click=\"saveData\"\r\n              >{{ $t(\"LocaleString.B00012\") }}</Button\r\n            >\r\n            <Button v-show=\"isEdit\" class=\"font-small\" @click=\"clearData\">{{\r\n              $t(\"LocaleString.B00015\")\r\n            }}</Button>\r\n          </div>\r\n          <div style=\"clear: both\"></div>\r\n        </div>\r\n      </div>\r\n      <div class=\"content\">\r\n        <Table\r\n          v-if=\"!isEdit\"\r\n          class=\"config-table\"\r\n          :columns=\"columnsList\"\r\n          :data=\"configList\"\r\n          :no-data-text=\"noDataText\"\r\n        >\r\n          <template slot-scope=\"{ row, index }\" slot=\"type\">\r\n            <span> {{ row.type }} </span>\r\n          </template>\r\n        </Table>\r\n        <Table\r\n          v-if=\"isEdit\"\r\n          class=\"config-table\"\r\n          :columns=\"columnsEdit\"\r\n          :data=\"configListEdit\"\r\n          :no-data-text=\"noDataText\"\r\n        >\r\n          <template slot-scope=\"{ row, index }\" slot=\"type\">\r\n            <span> {{ getObjectType(row.type) }} </span>\r\n          </template>\r\n          <template slot-scope=\"{ row, index }\" slot=\"longPress_cht\">\r\n            <Input\r\n              type=\"text\"\r\n              v-model=\"row.longPress_cht\"\r\n              maxlength=\"20\"\r\n              @on-change=\"onEditDataChange('longPress_cht', row, index)\"\r\n            />\r\n          </template>\r\n          <template slot-scope=\"{ row, index }\" slot=\"longPress_en\">\r\n            <Input\r\n              type=\"text\"\r\n              v-model=\"row.longPress_en\"\r\n              maxlength=\"20\"\r\n              @on-change=\"onEditDataChange('longPress_en', row, index)\"\r\n            />\r\n          </template>\r\n          <template slot-scope=\"{ row, index }\" slot=\"shortClick_cht\">\r\n            <Input\r\n              type=\"text\"\r\n              v-model=\"row.shortClick_cht\"\r\n              maxlength=\"20\"\r\n              @on-change=\"onEditDataChange('shortClick_cht', row, index)\"\r\n            />\r\n          </template>\r\n          <template slot-scope=\"{ row, index }\" slot=\"shortClick_en\">\r\n            <Input\r\n              type=\"text\"\r\n              v-model=\"row.shortClick_en\"\r\n              maxlength=\"20\"\r\n              @on-change=\"onEditDataChange('shortClick_en', row, index)\"\r\n            />\r\n          </template>\r\n          <template slot-scope=\"{ row, index }\" slot=\"doubleClick_cht\">\r\n            <Input\r\n              type=\"text\"\r\n              v-model=\"row.doubleClick_cht\"\r\n              maxlength=\"20\"\r\n              @on-change=\"onEditDataChange('doubleClick_cht', row, index)\"\r\n            />\r\n          </template>\r\n          <template slot-scope=\"{ row, index }\" slot=\"doubleClick_en\">\r\n            <Input\r\n              type=\"text\"\r\n              v-model=\"row.doubleClick_en\"\r\n              maxlength=\"20\"\r\n              @on-change=\"onEditDataChange('doubleClick_en', row, index)\"\r\n            />\r\n          </template>\r\n        </Table>\r\n      </div>\r\n      <div slot=\"footer\" style=\"text-align: center; margin-top: 10px\"></div>\r\n    </Modal>\r\n  </div>\r\n</template>\r\n<script type='es6'>\r\nimport Config from \"@/common/config\";\r\nlet moment = require(\"moment\");\r\nexport default {\r\n  data() {\r\n    return {\r\n      isShow: false,\r\n      isEdit: false,\r\n      systemInfo: [],\r\n      objectTypeList: [\r\n        {\r\n          type: \"equipment\",\r\n          name: this.$t(\"LocaleString.D00008\"),\r\n        },\r\n        {\r\n          type: \"people\",\r\n          name: this.$t(\"LocaleString.D00007\"),\r\n        },\r\n        {\r\n          type: \"space\",\r\n          name: this.$t(\"LocaleString.D00009\"),\r\n        },\r\n        {\r\n          type: \"other\",\r\n          name: this.$t(\"LocaleString.D00010\"),\r\n        },\r\n      ],\r\n      noDataText: this.$t(\"LocaleString.L00081\"),\r\n      columnsList: [\r\n        {\r\n          title: this.$t(\"LocaleString.L00047\"),\r\n          key: \"type\",\r\n          width: 100,\r\n          render: (h, params) => {\r\n            return h(\"span\", this.getObjectType(params.row.type));\r\n          },\r\n        },\r\n        {\r\n          title: this.$t(\"LocaleString.L30108\"),\r\n          key: \"longPress_cht\",\r\n        },\r\n        {\r\n          title: this.$t(\"LocaleString.L30109\"),\r\n          key: \"longPress_en\",\r\n        },\r\n        {\r\n          title: this.$t(\"LocaleString.L30110\"),\r\n          key: \"shortClick_cht\",\r\n        },\r\n        {\r\n          title: this.$t(\"LocaleString.L30111\"),\r\n          key: \"shortClick_en\",\r\n        },\r\n        {\r\n          title: this.$t(\"LocaleString.L30112\"),\r\n          key: \"doubleClick_cht\",\r\n        },\r\n        {\r\n          title: this.$t(\"LocaleString.L30113\"),\r\n          key: \"doubleClick_en\",\r\n        },\r\n      ],\r\n      columnsEdit: [\r\n        {\r\n          title: this.$t(\"LocaleString.L00047\"),\r\n          slot: \"type\",\r\n          width: 100,\r\n          render: (h, params) => {\r\n            return h(\"span\", this.getObjectType(params.row.type));\r\n          },\r\n        },\r\n        {\r\n          title: this.$t(\"LocaleString.L30108\"),\r\n          slot: \"longPress_cht\",\r\n        },\r\n        {\r\n          title: this.$t(\"LocaleString.L30109\"),\r\n          slot: \"longPress_en\",\r\n        },\r\n        {\r\n          title: this.$t(\"LocaleString.L30110\"),\r\n          slot: \"shortClick_cht\",\r\n        },\r\n        {\r\n          title: this.$t(\"LocaleString.L30111\"),\r\n          slot: \"shortClick_en\",\r\n        },\r\n        {\r\n          title: this.$t(\"LocaleString.L30112\"),\r\n          slot: \"doubleClick_cht\",\r\n        },\r\n        {\r\n          title: this.$t(\"LocaleString.L30113\"),\r\n          slot: \"doubleClick_en\",\r\n        },\r\n      ],\r\n      configList: [\r\n        {\r\n          type: \"equipment\",\r\n          longPress_cht: \"\",\r\n          longPress_en: \"\",\r\n          shortClick_cht: \"\",\r\n          shortClick_en: \"\",\r\n          doubleClick_cht: \"\",\r\n          doubleClick_en: \"\",\r\n        },\r\n        {\r\n          type: \"people\",\r\n          longPress_cht: \"\",\r\n          longPress_en: \"\",\r\n          shortClick_cht: \"\",\r\n          shortClick_en: \"\",\r\n          doubleClick_cht: \"\",\r\n          doubleClick_en: \"\",\r\n        },\r\n        {\r\n          type: \"space\",\r\n          longPress_cht: \"\",\r\n          longPress_en: \"\",\r\n          shortClick_cht: \"\",\r\n          shortClick_en: \"\",\r\n          doubleClick_cht: \"\",\r\n          doubleClick_en: \"\",\r\n        },\r\n        {\r\n          type: \"other\",\r\n          longPress_cht: \"\",\r\n          longPress_en: \"\",\r\n          shortClick_cht: \"\",\r\n          shortClick_en: \"\",\r\n          doubleClick_cht: \"\",\r\n          doubleClick_en: \"\",\r\n        },\r\n      ],\r\n      configListEdit: [\r\n        {\r\n          type: \"equipment\",\r\n          longPress_cht: \"\",\r\n          longPress_en: \"\",\r\n          shortClick_cht: \"\",\r\n          shortClick_en: \"\",\r\n          doubleClick_cht: \"\",\r\n          doubleClick_en: \"\",\r\n        },\r\n        {\r\n          type: \"people\",\r\n          longPress_cht: \"\",\r\n          longPress_en: \"\",\r\n          shortClick_cht: \"\",\r\n          shortClick_en: \"\",\r\n          doubleClick_cht: \"\",\r\n          doubleClick_en: \"\",\r\n        },\r\n        {\r\n          type: \"space\",\r\n          longPress_cht: \"\",\r\n          longPress_en: \"\",\r\n          shortClick_cht: \"\",\r\n          shortClick_en: \"\",\r\n          doubleClick_cht: \"\",\r\n          doubleClick_en: \"\",\r\n        },\r\n        {\r\n          type: \"other\",\r\n          longPress_cht: \"\",\r\n          longPress_en: \"\",\r\n          shortClick_cht: \"\",\r\n          shortClick_en: \"\",\r\n          doubleClick_cht: \"\",\r\n          doubleClick_en: \"\",\r\n        },\r\n      ],\r\n    };\r\n  },\r\n  mounted() {\r\n    this.isShow = true;\r\n    //this.getSystemAbout();\r\n    this.normalizeHeight();\r\n    this.loadData();\r\n  },\r\n  methods: {\r\n    getObjectType(type) {\r\n      if (type != undefined) {\r\n        let typeData = this.objectTypeList.find((t) => t.type == type).name;\r\n        return typeData;\r\n      } else {\r\n        return \"\";\r\n      }\r\n    },\r\n    onEditDataChange(type, row, index) {\r\n      switch (type) {\r\n        case \"longPress_cht\":\r\n          this.configListEdit[index].longPress_cht = row.longPress_cht;\r\n          break;\r\n        case \"longPress_en\":\r\n          this.configListEdit[index].longPress_en = row.longPress_en;\r\n          break;\r\n        case \"shortClick_cht\":\r\n          this.configListEdit[index].shortClick_cht = row.shortClick_cht;\r\n          break;\r\n        case \"shortClick_en\":\r\n          this.configListEdit[index].shortClick_en = row.shortClick_en;\r\n          break;\r\n        case \"doubleClick_cht\":\r\n          this.configListEdit[index].doubleClick_cht = row.doubleClick_cht;\r\n          break;\r\n        case \"doubleClick_en\":\r\n          this.configListEdit[index].doubleClick_en = row.doubleClick_en;\r\n          break;\r\n      }\r\n    },\r\n    loadData() {\r\n      let params = {\r\n        inlinecount: true,\r\n        search: \"category eq @SNS@SettingKM\",\r\n      };\r\n\r\n      this.$service.getPOCProperties.send(params).then((res) => {\r\n        if (res.count > 0) {\r\n          res.results[0].properties.forEach((res) => {\r\n            switch (res.key) {\r\n              case \"equipment_longPress_cht\":\r\n                this.configList[0].longPress_cht = res.value;\r\n                break;\r\n              case \"equipment_longPress_en\":\r\n                this.configList[0].longPress_en = res.value;\r\n                break;\r\n              case \"equipment_shortClick_cht\":\r\n                this.configList[0].shortClick_cht = res.value;\r\n                break;\r\n              case \"equipment_shortClick_en\":\r\n                this.configList[0].shortClick_en = res.value;\r\n                break;\r\n              case \"equipment_doubleClick_cht\":\r\n                this.configList[0].doubleClick_cht = res.value;\r\n                break;\r\n              case \"equipment_doubleClick_en\":\r\n                this.configList[0].doubleClick_en = res.value;\r\n                break;\r\n              case \"people_longPress_cht\":\r\n                this.configList[1].longPress_cht = res.value;\r\n                break;\r\n              case \"people_longPress_en\":\r\n                this.configList[1].longPress_en = res.value;\r\n                break;\r\n              case \"people_shortClick_cht\":\r\n                this.configList[1].shortClick_cht = res.value;\r\n                break;\r\n              case \"people_shortClick_en\":\r\n                this.configList[1].shortClick_en = res.value;\r\n                break;\r\n              case \"people_doubleClick_cht\":\r\n                this.configList[1].doubleClick_cht = res.value;\r\n                break;\r\n              case \"people_doubleClick_en\":\r\n                this.configList[1].doubleClick_en = res.value;\r\n                break;\r\n              case \"space_longPress_cht\":\r\n                this.configList[2].longPress_cht = res.value;\r\n                break;\r\n              case \"space_longPress_en\":\r\n                this.configList[2].longPress_en = res.value;\r\n                break;\r\n              case \"space_shortClick_cht\":\r\n                this.configList[2].shortClick_cht = res.value;\r\n                break;\r\n              case \"space_shortClick_en\":\r\n                this.configList[2].shortClick_en = res.value;\r\n                break;\r\n              case \"space_doubleClick_cht\":\r\n                this.configList[2].doubleClick_cht = res.value;\r\n                break;\r\n              case \"space_doubleClick_en\":\r\n                this.configList[2].doubleClick_en = res.value;\r\n                break;\r\n              case \"other_longPress_cht\":\r\n                this.configList[3].longPress_cht = res.value;\r\n                break;\r\n              case \"other_longPress_en\":\r\n                this.configList[3].longPress_en = res.value;\r\n                break;\r\n              case \"other_shortClick_cht\":\r\n                this.configList[3].shortClick_cht = res.value;\r\n                break;\r\n              case \"other_shortClick_en\":\r\n                this.configList[3].shortClick_en = res.value;\r\n                break;\r\n              case \"other_doubleClick_cht\":\r\n                this.configList[3].doubleClick_cht = res.value;\r\n                break;\r\n              case \"other_doubleClick_en\":\r\n                this.configList[3].doubleClick_en = res.value;\r\n                break;\r\n            }\r\n          });\r\n        }\r\n        this.configListEdit = JSON.parse(JSON.stringify(this.configList));\r\n      });\r\n    },\r\n    saveData() {\r\n      let putDatas = [];\r\n      let putData = {\r\n        category: \"@SNS@SettingKM\",\r\n        properties: [],\r\n      };\r\n      this.configListEdit.forEach((item) => {\r\n        let keyValuePairs = [\r\n          {\r\n            key: item.type + \"_longPress_cht\",\r\n            value: item.longPress_cht,\r\n          },\r\n          {\r\n            key: item.type + \"_longPress_en\",\r\n            value: item.longPress_en,\r\n          },\r\n          {\r\n            key: item.type + \"_shortClick_cht\",\r\n            value: item.shortClick_cht,\r\n          },\r\n          {\r\n            key: item.type + \"_shortClick_en\",\r\n            value: item.shortClick_en,\r\n          },\r\n          {\r\n            key: item.type + \"_doubleClick_cht\",\r\n            value: item.doubleClick_cht,\r\n          },\r\n          {\r\n            key: item.type + \"_doubleClick_en\",\r\n            value: item.doubleClick_en,\r\n          },\r\n        ];\r\n        putData.properties = [...putData.properties, ...keyValuePairs];\r\n      });\r\n      putDatas.push(putData);\r\n      this.$service.editPOCProperties.send(putDatas).then((res) => {\r\n        this.isEdit = false;\r\n        this.loadData();\r\n        this.$store.commit(\"setKmConfigChanged\", moment().valueOf());\r\n      });\r\n    },\r\n    clearData() {\r\n      this.isEdit = false;\r\n    },\r\n    editData() {\r\n      this.isEdit = true;\r\n    },\r\n    normalizeHeight() {\r\n      setTimeout(\r\n        function () {\r\n          const modal = document.querySelector(\".ivu-modal-body .content\");\r\n          if (modal) {\r\n            modal.style.height = \"250px\";\r\n          }\r\n        }.bind(this),\r\n        50\r\n      );\r\n    },\r\n    dynamicSort(property) {\r\n      var sortOrder = 1;\r\n      if (property[0] === \"-\") {\r\n        sortOrder = -1;\r\n        property = property.substr(1);\r\n      }\r\n      return function (a, b) {\r\n        var result =\r\n          a[property] < b[property] ? -1 : a[property] > b[property] ? 1 : 0;\r\n        return result * sortOrder;\r\n      };\r\n    },\r\n    refreshBtn() {\r\n      this.getSystemAbout();\r\n    },\r\n    getSystemAbout() {\r\n      let _this = this;\r\n      _this.systemInfo.push({\r\n        name: this.$t(\"LocaleString.S00002\"),\r\n        version: Config.WEB_VERSION,\r\n      });\r\n    },\r\n    computedDate(val) {\r\n      if (val) {\r\n        return moment(val).format(\"YYYY-MM-DD\");\r\n      }\r\n      return \"\";\r\n    },\r\n    computedDateTime(val) {\r\n      if (val) {\r\n        return moment(val).format(\"YYYY-MM-DD HH:mm:ss\");\r\n      }\r\n      return \"\";\r\n    },\r\n    cancel() {\r\n      this.isShow = false;\r\n      setTimeout(() => {\r\n        this.$emit(\"closeKMConfig\");\r\n      }, 500);\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n.about-data-modal {\r\n  // .vertical-center-modal {\r\n  //   display: flex;\r\n  //   align-items: center;\r\n  //   justify-content: center;\r\n  //   .ivu-modal {\r\n  //     top: 0;\r\n  //   }\r\n  // }\r\n  .ivu-modal-close {\r\n    top: 15px;\r\n    right: 20px;\r\n  }\r\n  .content {\r\n    padding: 4px 14px;\r\n    overflow-y: auto;\r\n    //height: 700px;\r\n    //height: 400px;\r\n  }\r\n\r\n  @media only screen and (min-width: 1200px) {\r\n    .ivu-modal-close {\r\n      top: 18px;\r\n    }\r\n  }\r\n  .ivu-modal-header {\r\n    padding: 15px 20px;\r\n    border-bottom: 1px solid #f7f7f7;\r\n    .header {\r\n      overflow: hidden;\r\n      .left {\r\n        float: left;\r\n        width: 6px;\r\n        height: 24px;\r\n        margin-right: 8px;\r\n        background-color: #0ac7c0;\r\n        border-radius: 3px;\r\n      }\r\n      .title {\r\n        float: left;\r\n        height: 24px;\r\n        line-height: 24px;\r\n        font-size: 14px;\r\n        font-weight: bold;\r\n        color: #999;\r\n        span {\r\n          color: #333;\r\n        }\r\n      }\r\n      .es-btn {\r\n        float: right;\r\n        margin-right: 35px;\r\n      }\r\n      @media only screen and (min-width: 1200px) {\r\n        .left {\r\n          height: 30px;\r\n        }\r\n        .title {\r\n          height: 30px;\r\n          line-height: 30px;\r\n          font-size: 18px;\r\n        }\r\n      }\r\n    }\r\n  }\r\n  .ivu-modal-body {\r\n    .content {\r\n      padding: 4px 14px;\r\n      overflow-y: auto;\r\n      .row {\r\n        margin-bottom: 20px;\r\n        .item {\r\n          padding: 15px;\r\n          border: 1px solid #f7f7f7;\r\n          border-radius: 4px;\r\n          label {\r\n            color: #999;\r\n            font-weight: bold;\r\n          }\r\n          p {\r\n            margin-top: 10px;\r\n            color: #333;\r\n          }\r\n        }\r\n      }\r\n      .form-item {\r\n        padding: 15px 15px 22px;\r\n        margin-bottom: 16px;\r\n        border-radius: 4px;\r\n        background-color: #f7f7f7;\r\n      }\r\n    }\r\n  }\r\n  .ivu-modal-footer {\r\n    border-top: none;\r\n    padding: 0;\r\n  }\r\n}\r\n</style>\r\n<style lang=\"less\" scoped>\r\n/deep/ .ivu-btn-primary {\r\n  color: #fff;\r\n  background-color: #31babb;\r\n  border-color: #31babb;\r\n}\r\n\r\n/deep/ .ivu-btn-primary:hover {\r\n  color: #fff !important;\r\n  border-color: #31babb;\r\n}\r\n\r\n/deep/ .ivu-switch-checked {\r\n  background-color: #31babb;\r\n  border-color: #31babb;\r\n}\r\n.musicIcon {\r\n  vertical-align: middle;\r\n  margin-left: 5px;\r\n  color: #31babb;\r\n  cursor: pointer;\r\n}\r\n</style>\r\n"]}]}