{"remainingRequest": "E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\src\\components\\administrative\\apps\\sns\\planeSetting\\sitePlan\\pointDrag.vue?vue&type=script&lang=js&", "dependencies": [{"path": "E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\src\\components\\administrative\\apps\\sns\\planeSetting\\sitePlan\\pointDrag.vue", "mtime": 1754362736894}, {"path": "E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\node_modules\\string-replace-loader\\index.js", "mtime": 499162500000}, {"path": "E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgcG9pbnREcmFnIGZyb20gIi4vcG9pbnREcmFnIjsNCmltcG9ydCBwb2ludCBmcm9tICJAL2NvbXBvbmVudHMvYWRtaW5pc3RyYXRpdmUvY29tbW9uL2ltYWdlcy9wb2ludFBsYW5lL2ZpbGwtZ3BzX29rLnN2ZyI7DQppbXBvcnQgcG9pbnRSZWQgZnJvbSAiQC9jb21wb25lbnRzL2FkbWluaXN0cmF0aXZlL2NvbW1vbi9pbWFnZXMvcG9pbnRQbGFuZS9maWxsLWdwc19yZWQuc3ZnIjsNCg0KZXhwb3J0IGRlZmF1bHQgew0KICBwcm9wczogWw0KICAgICJib3giLA0KICAgICJwb2ludEltZyIsDQogICAgIml0ZW0iLA0KICAgICJwb2ludEFjdGl2ZSIsDQogICAgInBvaW50VHlwZSIsDQogICAgImN1cnJlbnRBY3RpdmVQb2ludCIsDQogICAgInB4UmF0ZSIsDQogICAgInB5UmF0ZSIsDQogICAgImRyYWdnYWJsZSIsDQogIF0sDQogIGRhdGEoKSB7DQogICAgc2V0VGltZW91dCgoKSA9PiB7DQogICAgICAvLyBjb25zb2xlLmxvZygndGhpcy5pdGVtLmNvb3JkaW5hdGVzLnk6Jyt0aGlzLml0ZW0uY29vcmRpbmF0ZXMueSkNCiAgICAgIC8vIGNvbnNvbGUubG9nKCd0aGlzLnB5UmF0ZTonK3RoaXMucHlSYXRlKQ0KICAgICAgLy8gY29uc29sZS5sb2coJ2ljb25IZWlnaHQ6Jyt0aGlzLmljb25IZWlnaHQpDQogICAgICB0aGlzLnBvaW50U3R5bGUgPSB7DQogICAgICAgIGJhY2tncm91bmQ6ICJ1cmwoIiArIHBvaW50ICsgIikgbm8tcmVwZWF0IiwNCiAgICAgICAgYmFja2dyb3VuZFNpemU6ICJjb3ZlciIsDQogICAgICAgIC8vIOWcljog5a+sPTY5OXB4LCDpq5g9NTI0cHgNCiAgICAgICAgLy8g5a+m6Zqb5YWs5bC677yaIOWvrD0xNTIuMTY2NO+8jCDpq5g9MTE0LjEyNDgNCiAgICAgICAgLy8g5q+U5L6LIDogICA2OTkvMTUyLjE2NjQ9NC41OTM2IHB4L20gICwgMTUyLjE2NjQvNjk5PTAuMjE3Ng0KICAgICAgICAvLyDnlavpnaLplbflr6zvvJ0g5a+m6Zqb5YWs5bC6KiA0LjU5MzYgKHB4KQ0KICAgICAgICAvLyDlr6bpmpvluqfmqJnlhazlsLogPSDnlavpnaLplbflr6wgKiAwLjIxNzYgKG0pDQogICAgICAgIC8vIHRvcDogKHRoaXMuaXRlbS5jb29yZGluYXRlcy55ICogdGhpcy5weFJhdGUpICArICdweCcsDQogICAgICAgIC8vIGxlZnQ6ICh0aGlzLml0ZW0uY29vcmRpbmF0ZXMueCAqIHRoaXMucHhSYXRlKSArICAncHgnLA0KICAgICAgICB0b3A6DQogICAgICAgICAgdGhpcy5pdGVtLmNvb3JkaW5hdGVzLnkgKiB0aGlzLnB5UmF0ZSAtDQogICAgICAgICAgcGFyc2VJbnQodGhpcy5pY29uSGVpZ2h0KSArDQogICAgICAgICAgInB4IiwNCiAgICAgICAgbGVmdDoNCiAgICAgICAgICB0aGlzLml0ZW0uY29vcmRpbmF0ZXMueCAqIHRoaXMucHhSYXRlIC0NCiAgICAgICAgICBwYXJzZUludCh0aGlzLmljb25XaWR0aCkgLyAyICsNCiAgICAgICAgICAxICsNCiAgICAgICAgICAicHgiLCAvLyA354K6c3Zn5bem5Y+z55WZ6YKKcHgNCiAgICAgICAgd2lkdGg6IHRoaXMuaWNvbldpZHRoICsgInB4IiwNCiAgICAgICAgaGVpZ2h0OiB0aGlzLmljb25IZWlnaHQgKyAicHgiLA0KICAgICAgfTsNCiAgICAgIC8vIHRoaXMuY3Jvc3MgPSB7DQogICAgICAvLyAgICAgYmFja2dyb3VuZDogJ3VybCgnICsgY3Jvc3MgKyAnKSBuby1yZXBlYXQnLA0KICAgICAgLy8gICAgIGJhY2tncm91bmRTaXplOiAnY292ZXInLA0KICAgICAgLy8gfQ0KICAgICAgLy8gaWYgKGRvY3VtZW50LmJvZHkuY2xpZW50V2lkdGggPCAxMjAwKSB7DQogICAgICAvLyAgICAgdGhpcy5wb2ludFN0eWxlLndpZHRoID0gJzIwcHgnDQogICAgICAvLyAgICAgdGhpcy5wb2ludFN0eWxlLmhlaWdodCA9ICcyMHB4Jw0KICAgICAgLy8gICAgIHRoaXMucG9pbnRTdHlsZS5mb250U2l6ZSA9ICcxNHB4Jw0KICAgICAgLy8gICAgIC8vIHRoaXMuY3Jvc3MuaGVpZ2h0ID0gJzEwcHgnDQogICAgICAvLyAgICAgLy8gdGhpcy5jcm9zcy53aWR0aCA9ICcxMHB4Jw0KICAgICAgLy8gICAgIC8vIHRoaXMuY3Jvc3MudG9wID0gJy01cHgnDQogICAgICAvLyAgICAgLy8gdGhpcy5jcm9zcy5sZWZ0ID0gJy01cHgnDQogICAgICAvLyB9DQogICAgfSwgMzAwKTsNCiAgICByZXR1cm4gew0KICAgICAgaWNvbldpZHRoOiAiNDAiLA0KICAgICAgaWNvbkhlaWdodDogIjQwIiwNCiAgICAgIHBvaW50U3R5bGU6IHt9LA0KICAgICAgLy8gY3Jvc3M6IHt9DQogICAgfTsNCiAgfSwNCiAgbW91bnRlZCgpIHsNCiAgICBpZiAodGhpcy5kcmFnZ2FibGUpIHsNCiAgICAgIHBvaW50RHJhZyh0aGlzLl9wcm9wcy5ib3gsIHRoaXMuaXRlbS5pZCwgKHZhbHVlKSA9PiB7DQogICAgICAgIHRoaXMuJGVtaXQoInBvaW50UG9zaXRpb24iLCB2YWx1ZSk7DQogICAgICB9KTsNCiAgICB9DQogICAgLy8gY29uc29sZS5sb2coJ2ljb25TaXplOicrbG9jYWxTdG9yYWdlLmdldEl0ZW0oJ3Nuc19pY29uU2l6ZScpKQ0KICAgIGxldCBpY29uU2l6ZSA9IGxvY2FsU3RvcmFnZS5nZXRJdGVtKCJzbnNfaWNvblNpemUiKTsNCiAgICB0aGlzLmljb25XaWR0aCA9IGljb25TaXplOw0KICAgIHRoaXMuaWNvbkhlaWdodCA9IGljb25TaXplOw0KICAgIC8vIGxldCB0aGlzaXRlbSA9IGRvY3VtZW50LmdldEVsZW1lbnRCeUlkKCcnKQ0KICB9LA0KICBtZXRob2RzOiB7DQogICAgcG9pbnREcmFnUGl0Y2hVcChpdGVtKSB7DQogICAgICBpZiAoYXJndW1lbnRzWzFdICE9PSAwKSB7DQogICAgICAgIGRvY3VtZW50LmdldEVsZW1lbnRCeUlkKGFyZ3VtZW50c1sxXSkuc3R5bGUuYmFja2dyb3VuZCA9DQogICAgICAgICAgInVybCgiICsgcG9pbnQgKyAiKSAwJSAwJSAvIGNvdmVyIG5vLXJlcGVhdCI7DQogICAgICB9DQogICAgICBkb2N1bWVudC5nZXRFbGVtZW50QnlJZChpdGVtLmlkKS5zdHlsZS5iYWNrZ3JvdW5kID0NCiAgICAgICAgInVybCgiICsgcG9pbnRSZWQgKyAiKSAwJSAwJSAvIGNvdmVyIG5vLXJlcGVhdCI7DQogICAgfSwNCiAgICBjbGVhck9iamVjdCgpIHsNCiAgICAgIC8vIGNvbnNvbGUubG9nKCdEREREOicrdGhpcy5pdGVtLmlkKQ0KICAgICAgbGV0IGNvbnRhaW5lciA9IGRvY3VtZW50LmdldEVsZW1lbnRCeUlkKHRoaXMuaXRlbS5pZCk7DQogICAgICAvLyBjb25zb2xlLmxvZygnY29udGFpbmVyOicrY29udGFpbmVyKQ0KICAgICAgY29udGFpbmVyLm9ubW91c2Vkb3duID0gZnVuY3Rpb24gKCkge307DQogICAgICBjb250YWluZXIuc3R5bGUuY3Vyc29yID0gIiI7DQogICAgfSwNCiAgfSwNCiAgd2F0Y2g6IHsNCiAgICBkcmFnZ2FibGU6IHsNCiAgICAgIGhhbmRsZXIoKSB7DQogICAgICAgIGlmICh0aGlzLmRyYWdnYWJsZSkgew0KICAgICAgICAgIHBvaW50RHJhZyh0aGlzLl9wcm9wcy5ib3gsIHRoaXMuaXRlbS5pZCwgKHZhbHVlKSA9PiB7DQogICAgICAgICAgICB0aGlzLiRlbWl0KCJwb2ludFBvc2l0aW9uIiwgdmFsdWUpOw0KICAgICAgICAgIH0pOw0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIHRoaXMuY2xlYXJPYmplY3QoKTsNCiAgICAgICAgfQ0KICAgICAgfSwNCiAgICB9LA0KICAgIGl0ZW06IGZ1bmN0aW9uIChuZXdJdGVtLCBvbGRJdGVtKSB7DQogICAgICB0aGlzLml0ZW0gPSBuZXdJdGVtOw0KICAgICAgLy8gY29uc29sZS5sb2coJ3RoaXMuaXRlbS5jb29yZGluYXRlcy54OicrdGhpcy5pdGVtLmNvb3JkaW5hdGVzLngpDQoNCiAgICAgIGRvY3VtZW50LmdldEVsZW1lbnRCeUlkKG5ld0l0ZW0uaWQpLnN0eWxlLmxlZnQgPQ0KICAgICAgICB0aGlzLml0ZW0uY29vcmRpbmF0ZXMueCAqIHRoaXMucHhSYXRlIC0NCiAgICAgICAgcGFyc2VJbnQodGhpcy5pY29uV2lkdGgpIC8gMiArDQogICAgICAgIDEgKw0KICAgICAgICAicHgiOw0KICAgICAgZG9jdW1lbnQuZ2V0RWxlbWVudEJ5SWQobmV3SXRlbS5pZCkuc3R5bGUudG9wID0NCiAgICAgICAgdGhpcy5pdGVtLmNvb3JkaW5hdGVzLnkgKiB0aGlzLnB5UmF0ZSAtDQogICAgICAgIHBhcnNlSW50KHRoaXMuaWNvbkhlaWdodCkgKw0KICAgICAgICAicHgiOw0KICAgICAgLy8gaWYgKG9sZEl0ZW0uaWQpIHsNCiAgICAgIC8vICAgICBkb2N1bWVudC5nZXRFbGVtZW50QnlJZChvbGRJdGVtLmlkKS5zdHlsZS5iYWNrZ3JvdW5kID0gJ3VybCgnICsgdGhpcy5wb2ludEltZyArICcpIDAlIDAlIC8gY292ZXIgbm8tcmVwZWF0Jw0KICAgICAgLy8gfQ0KICAgICAgLy8gZG9jdW1lbnQuZ2V0RWxlbWVudEJ5SWQobmV3SXRlbS5pZCkuc3R5bGUuYmFja2dyb3VuZCA9ICd1cmwoJyArIHRoaXMucG9pbnRBY3RpdmUgKyAnKSAwJSAwJSAvIGNvdmVyIG5vLXJlcGVhdCcNCg0KICAgICAgLy8gZG9jdW1lbnQuZ2V0RWxlbWVudEJ5SWQobmV3SXRlbS5pZCkuc3R5bGUuYmFja2dyb3VuZCA9ICd1cmwoJyArIHRoaXMucG9pbnRBY3RpdmUgKyAnKSAwJSAwJSAvIGNvdmVyIG5vLXJlcGVhdCcNCiAgICAgIC8vIHRoaXMuJGZvcmNlVXBkYXRlKCkNCiAgICAgIC8vIHBvaW50RHJhZyh0aGlzLl9wcm9wcy5ib3gsIHRoaXMuaXRlbS5pZCwgKHZhbHVlKSA9PiB7DQogICAgICAvLyAgICAgdGhpcy4kZW1pdCgncG9pbnRQb3NpdGlvbicsIHZhbHVlKQ0KICAgICAgLy8gfSkNCiAgICB9LA0KICAgIGN1cnJlbnRBY3RpdmVQb2ludDogZnVuY3Rpb24gKG5ld0l0ZW0sIG9sZEl0ZW0pIHsNCiAgICAgIGlmIChvbGRJdGVtKSB7DQogICAgICAgIGRvY3VtZW50LmdldEVsZW1lbnRCeUlkKG9sZEl0ZW0pLnN0eWxlLmJhY2tncm91bmQgPQ0KICAgICAgICAgICJ1cmwoIiArIHBvaW50ICsgIikgMCUgMCUgLyBjb3ZlciBuby1yZXBlYXQiOw0KICAgICAgICBkb2N1bWVudC5nZXRFbGVtZW50QnlJZChvbGRJdGVtKS5zdHlsZS56SW5kZXggPSAiMSI7DQogICAgICB9DQogICAgICBpZiAobmV3SXRlbSAmJiB0aGlzLnBvaW50QWN0aXZlKSB7DQogICAgICAgIGRvY3VtZW50LmdldEVsZW1lbnRCeUlkKG5ld0l0ZW0pLnN0eWxlLmJhY2tncm91bmQgPQ0KICAgICAgICAgICJ1cmwoIiArIHBvaW50UmVkICsgIikgMCUgMCUgLyBjb3ZlciBuby1yZXBlYXQiOw0KICAgICAgICBkb2N1bWVudC5nZXRFbGVtZW50QnlJZChuZXdJdGVtKS5zdHlsZS56SW5kZXggPSAiOTk5IjsNCiAgICAgIH0NCiAgICB9LA0KICB9LA0KfTsNCg=="}, {"version": 3, "sources": ["pointDrag.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8CA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "pointDrag.vue", "sourceRoot": "src/components/administrative/apps/sns/planeSetting/sitePlan", "sourcesContent": ["<style lang=\"scss\">\r\n.dragContainPoint {\r\n  // width: 27px;\r\n  // height: 38px;\r\n  position: absolute;\r\n  top: 0;\r\n  // opacity: 0.7;\r\n  word-break: break-all;\r\n  text-align: center;\r\n  font-size: 16px;\r\n  z-index: 9999;\r\n\r\n  .cross {\r\n    position: absolute;\r\n    height: 20px;\r\n    width: 20px;\r\n    top: -10px;\r\n    left: -10px;\r\n  }\r\n\r\n  .stationText {\r\n    // position: absolute;\r\n    width: 40px;\r\n    height: 40px;\r\n    color: transparent;\r\n  }\r\n}\r\n\r\n.dragContainPointHeight {\r\n  height: 37px !important;\r\n}\r\n</style>\r\n<template>\r\n  <div\r\n    :id=\"item.id\"\r\n    class=\"animated dragContainPoint zoomIn\"\r\n    :class=\"{ dragContainPointHeight: pointType === 'guard' }\"\r\n    :style=\"pointStyle\"\r\n  >\r\n    <Tooltip :content=\"item.name\" placement=\"bottom\" :transfer=\"true\">\r\n      <div class=\"stationText\">1</div>\r\n    </Tooltip>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport pointDrag from \"./pointDrag\";\r\nimport point from \"@/components/administrative/common/images/pointPlane/fill-gps_ok.svg\";\r\nimport pointRed from \"@/components/administrative/common/images/pointPlane/fill-gps_red.svg\";\r\n\r\nexport default {\r\n  props: [\r\n    \"box\",\r\n    \"pointImg\",\r\n    \"item\",\r\n    \"pointActive\",\r\n    \"pointType\",\r\n    \"currentActivePoint\",\r\n    \"pxRate\",\r\n    \"pyRate\",\r\n    \"draggable\",\r\n  ],\r\n  data() {\r\n    setTimeout(() => {\r\n      // console.log('this.item.coordinates.y:'+this.item.coordinates.y)\r\n      // console.log('this.pyRate:'+this.pyRate)\r\n      // console.log('iconHeight:'+this.iconHeight)\r\n      this.pointStyle = {\r\n        background: \"url(\" + point + \") no-repeat\",\r\n        backgroundSize: \"cover\",\r\n        // 圖: 寬=699px, 高=524px\r\n        // 實際公尺： 寬=152.1664， 高=114.1248\r\n        // 比例 :   699/152.1664=4.5936 px/m  , 152.1664/699=0.2176\r\n        // 畫面長寬＝ 實際公尺* 4.5936 (px)\r\n        // 實際座標公尺 = 畫面長寬 * 0.2176 (m)\r\n        // top: (this.item.coordinates.y * this.pxRate)  + 'px',\r\n        // left: (this.item.coordinates.x * this.pxRate) +  'px',\r\n        top:\r\n          this.item.coordinates.y * this.pyRate -\r\n          parseInt(this.iconHeight) +\r\n          \"px\",\r\n        left:\r\n          this.item.coordinates.x * this.pxRate -\r\n          parseInt(this.iconWidth) / 2 +\r\n          1 +\r\n          \"px\", // 7為svg左右留邊px\r\n        width: this.iconWidth + \"px\",\r\n        height: this.iconHeight + \"px\",\r\n      };\r\n      // this.cross = {\r\n      //     background: 'url(' + cross + ') no-repeat',\r\n      //     backgroundSize: 'cover',\r\n      // }\r\n      // if (document.body.clientWidth < 1200) {\r\n      //     this.pointStyle.width = '20px'\r\n      //     this.pointStyle.height = '20px'\r\n      //     this.pointStyle.fontSize = '14px'\r\n      //     // this.cross.height = '10px'\r\n      //     // this.cross.width = '10px'\r\n      //     // this.cross.top = '-5px'\r\n      //     // this.cross.left = '-5px'\r\n      // }\r\n    }, 300);\r\n    return {\r\n      iconWidth: \"40\",\r\n      iconHeight: \"40\",\r\n      pointStyle: {},\r\n      // cross: {}\r\n    };\r\n  },\r\n  mounted() {\r\n    if (this.draggable) {\r\n      pointDrag(this._props.box, this.item.id, (value) => {\r\n        this.$emit(\"pointPosition\", value);\r\n      });\r\n    }\r\n    // console.log('iconSize:'+localStorage.getItem('sns_iconSize'))\r\n    let iconSize = localStorage.getItem(\"sns_iconSize\");\r\n    this.iconWidth = iconSize;\r\n    this.iconHeight = iconSize;\r\n    // let thisitem = document.getElementById('')\r\n  },\r\n  methods: {\r\n    pointDragPitchUp(item) {\r\n      if (arguments[1] !== 0) {\r\n        document.getElementById(arguments[1]).style.background =\r\n          \"url(\" + point + \") 0% 0% / cover no-repeat\";\r\n      }\r\n      document.getElementById(item.id).style.background =\r\n        \"url(\" + pointRed + \") 0% 0% / cover no-repeat\";\r\n    },\r\n    clearObject() {\r\n      // console.log('DDDD:'+this.item.id)\r\n      let container = document.getElementById(this.item.id);\r\n      // console.log('container:'+container)\r\n      container.onmousedown = function () {};\r\n      container.style.cursor = \"\";\r\n    },\r\n  },\r\n  watch: {\r\n    draggable: {\r\n      handler() {\r\n        if (this.draggable) {\r\n          pointDrag(this._props.box, this.item.id, (value) => {\r\n            this.$emit(\"pointPosition\", value);\r\n          });\r\n        } else {\r\n          this.clearObject();\r\n        }\r\n      },\r\n    },\r\n    item: function (newItem, oldItem) {\r\n      this.item = newItem;\r\n      // console.log('this.item.coordinates.x:'+this.item.coordinates.x)\r\n\r\n      document.getElementById(newItem.id).style.left =\r\n        this.item.coordinates.x * this.pxRate -\r\n        parseInt(this.iconWidth) / 2 +\r\n        1 +\r\n        \"px\";\r\n      document.getElementById(newItem.id).style.top =\r\n        this.item.coordinates.y * this.pyRate -\r\n        parseInt(this.iconHeight) +\r\n        \"px\";\r\n      // if (oldItem.id) {\r\n      //     document.getElementById(oldItem.id).style.background = 'url(' + this.pointImg + ') 0% 0% / cover no-repeat'\r\n      // }\r\n      // document.getElementById(newItem.id).style.background = 'url(' + this.pointActive + ') 0% 0% / cover no-repeat'\r\n\r\n      // document.getElementById(newItem.id).style.background = 'url(' + this.pointActive + ') 0% 0% / cover no-repeat'\r\n      // this.$forceUpdate()\r\n      // pointDrag(this._props.box, this.item.id, (value) => {\r\n      //     this.$emit('pointPosition', value)\r\n      // })\r\n    },\r\n    currentActivePoint: function (newItem, oldItem) {\r\n      if (oldItem) {\r\n        document.getElementById(oldItem).style.background =\r\n          \"url(\" + point + \") 0% 0% / cover no-repeat\";\r\n        document.getElementById(oldItem).style.zIndex = \"1\";\r\n      }\r\n      if (newItem && this.pointActive) {\r\n        document.getElementById(newItem).style.background =\r\n          \"url(\" + pointRed + \") 0% 0% / cover no-repeat\";\r\n        document.getElementById(newItem).style.zIndex = \"999\";\r\n      }\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n/deep/ .ivu-tooltip-content {\r\n  margin-left: 0px;\r\n}\r\n\r\n/deep/ .ivu-tooltip-content > .ivu-tooltip-arrow {\r\n  left: 50%;\r\n  margin-left: -5px;\r\n}\r\n\r\n/deep/ .ivu-tooltip-content > .ivu-tooltip-inner {\r\n  margin-left: 0px;\r\n}\r\n</style>"]}]}