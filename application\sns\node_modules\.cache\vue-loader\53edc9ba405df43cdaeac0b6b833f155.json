{"remainingRequest": "E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\src\\components\\administrative\\apps\\sns\\planeSetting\\sitePlan\\dragContainNS2.vue?vue&type=style&index=0&lang=scss&", "dependencies": [{"path": "E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\src\\components\\administrative\\apps\\sns\\planeSetting\\sitePlan\\dragContainNS2.vue", "mtime": 1754362736892}, {"path": "E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQouZHJhZ0NvbnRhaW4gew0KICAgIHdpZHRoOiAyMHB4Ow0KICAgIGhlaWdodDogMjBweDsNCiAgICBwb3NpdGlvbjogYWJzb2x1dGUNCn0NCg0KLmRyYWcgew0KICAgIG9wYWNpdHk6IDAuNzsNCiAgICB3aWR0aDogMTAwJTsNCiAgICBoZWlnaHQ6IDEwMCU7DQogICAgYm9yZGVyOiAxcHggc29saWQgI2JiY2FiZjsNCiAgICBib3gtc2hhZG93OiAwIDNweCA2cHggcmdiYSgwLCAwLCAwLCAuMik7DQogICAgd29yZC1icmVhazogYnJlYWstYWxsOw0KICAgIG92ZXJmbG93OiBoaWRkZW47DQogICAgYmFja2dyb3VuZC1jb2xvcjogI2UzYzFjMTsNCiAgICB0ZXh0LWFsaWduOiBjZW50ZXI7DQogICAgZm9udC1zaXplOiAxNnB4Ow0KfQ0KDQouY2lyY2xlQmFzZSB7DQogICAgb3BhY2l0eTogMC43Ow0KICAgIGJvcmRlci1yYWRpdXM6IDUwJTsNCiAgICB3aWR0aDogMTAwJTsNCiAgICBoZWlnaHQ6IDEwMCU7DQogICAgYm9yZGVyOiAxcHggc29saWQgI2JiY2FiZjsNCiAgICBib3gtc2hhZG93OiAwIDNweCA2cHggcmdiYSgwLCAwLCAwLCAuMik7DQogICAgd29yZC1icmVhazogYnJlYWstYWxsOw0KICAgIG92ZXJmbG93OiBoaWRkZW47DQogICAgdGV4dC1hbGlnbjogY2VudGVyOw0KICAgIGJhY2tncm91bmQ6ICMwMGFmZmYgIWltcG9ydGFudDsNCiAgICBjb2xvcjogd2hpdGU7DQogICAgZGlzcGxheTogZmxleDsNCiAgICBhbGlnbi1pdGVtczogY2VudGVyOw0KfQ0KDQouZHJhZ1pvb20gew0KICAgIHBvc2l0aW9uOiBhYnNvbHV0ZTsNCiAgICB3aWR0aDogMTBweDsNCiAgICBoZWlnaHQ6IDEwcHg7DQogICAgYmFja2dyb3VuZC1jb2xvcjogI2M1YTJhMjsNCiAgICByaWdodDogMDsNCiAgICBib3R0b206IDA7DQp9DQoNCi5jcm9zcyB7DQogICAgcG9zaXRpb246IGFic29sdXRlOw0KICAgIGhlaWdodDogNDBweDsNCiAgICB3aWR0aDogNDBweDsNCiAgICB0b3A6IC0yMHB4Ow0KICAgIGxlZnQ6IC0yMHB4Ow0KfQ0KDQoucmVzZXQgew0KICAgIGhlaWdodDogMjRweDsNCiAgICB3aWR0aDogMjRweDsNCiAgICBjdXJzb3I6IHBvaW50ZXI7DQp9DQoNCi5pdnUtcG9wdGlwLWNvbmZpcm0gew0KICAgIHBvc2l0aW9uOiBhYnNvbHV0ZTsNCiAgICB0b3A6IC0xMnB4Ow0KICAgIHJpZ2h0OiAtMTJweDsNCn0NCg=="}, {"version": 3, "sources": ["dragContainNS2.vue"], "names": [], "mappings": ";AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA", "file": "dragContainNS2.vue", "sourceRoot": "src/components/administrative/apps/sns/planeSetting/sitePlan", "sourcesContent": ["<style lang=\"scss\">\r\n.dragContain {\r\n    width: 20px;\r\n    height: 20px;\r\n    position: absolute\r\n}\r\n\r\n.drag {\r\n    opacity: 0.7;\r\n    width: 100%;\r\n    height: 100%;\r\n    border: 1px solid #bbcabf;\r\n    box-shadow: 0 3px 6px rgba(0, 0, 0, .2);\r\n    word-break: break-all;\r\n    overflow: hidden;\r\n    background-color: #e3c1c1;\r\n    text-align: center;\r\n    font-size: 16px;\r\n}\r\n\r\n.circleBase {\r\n    opacity: 0.7;\r\n    border-radius: 50%;\r\n    width: 100%;\r\n    height: 100%;\r\n    border: 1px solid #bbcabf;\r\n    box-shadow: 0 3px 6px rgba(0, 0, 0, .2);\r\n    word-break: break-all;\r\n    overflow: hidden;\r\n    text-align: center;\r\n    background: #00afff !important;\r\n    color: white;\r\n    display: flex;\r\n    align-items: center;\r\n}\r\n\r\n.dragZoom {\r\n    position: absolute;\r\n    width: 10px;\r\n    height: 10px;\r\n    background-color: #c5a2a2;\r\n    right: 0;\r\n    bottom: 0;\r\n}\r\n\r\n.cross {\r\n    position: absolute;\r\n    height: 40px;\r\n    width: 40px;\r\n    top: -20px;\r\n    left: -20px;\r\n}\r\n\r\n.reset {\r\n    height: 24px;\r\n    width: 24px;\r\n    cursor: pointer;\r\n}\r\n\r\n.ivu-poptip-confirm {\r\n    position: absolute;\r\n    top: -12px;\r\n    right: -12px;\r\n}\r\n</style>\r\n<template>\r\n<div :id=\"childDrag.id\" class=\"animated dragContain zoomIn\" v-bind:style=\"styleObject\">\r\n    <div :id=\"childDrag.child.dID\" :class=\"[childDrag.isEntity?'circleBase':'drag']\" v-bind:style=\"styleColor\">\r\n        <p style=\"position: absolute;width: 100%;\">{{childDrag.name}}</p>\r\n    </div>\r\n    <div :id=\"childDrag.child.zID\" class=\"dragZoom\"></div>\r\n    <img class=\"cross\" :src=\"cross\" />\r\n    <!--\r\n    <Poptip\r\n      placement=\"bottom-end\"\r\n      confirm\r\n      @on-ok=\"resetItem(childDrag)\"\r\n      :title=\"$t('lang.common.whetherToReset')\">\r\n      <img class=\"reset\" :src=\"reset\" :title=\"$t('lang.common.reset')\"/>\r\n    </Poptip>\r\n    -->\r\n\r\n</div>\r\n</template>\r\n\r\n<script>\r\nimport dragDiv from '@/components/administrative/common/dragDiv'\r\nimport cross from '@/components/administrative/common/images/cross.png'\r\nimport reset from '@/components/administrative/common/images/reset.png'\r\n\r\nexport default {\r\n    props: ['childDrag', 'box', 'name', 'isDraggableRegion'],\r\n    data() {\r\n        // setTimeout(() => {\r\n        //   let oBox = document.getElementById(this._props.box)\r\n        //   console.log('this._props.box:'+JSON.stringify(this._props.box))\r\n        //   console.log('oBox.clientWidth:'+JSON.stringify(oBox.clientWidth))\r\n        //   console.log('oBox.clientHeight:'+JSON.stringify(oBox.clientHeight))\r\n        //   if (!oBox) {\r\n        //     return\r\n        //   }\r\n        //   let width = oBox.clientWidth\r\n        //   let height = oBox.clientHeight\r\n        //   this.height = height / width\r\n        // }, 300)\r\n\r\n        return {\r\n            position: null,\r\n            height: 0,\r\n            styleObject: {\r\n                width: (this._props.childDrag.child.coordinates.lowRight.x * 100 - this._props.childDrag.child.coordinates.upperLeft.x * 100) + '%',\r\n                height: 0,\r\n                left: this._props.childDrag.child.coordinates.upperLeft.x * 100 + '%',\r\n                //width:'10%',\r\n                //height: '10%',\r\n                //left: '10%',\r\n                top: 0,\r\n                'z-index': 1,\r\n                display: this._props.childDrag.isShow ? '' : 'none'\r\n            },\r\n            cross: cross,\r\n            reset: reset,\r\n            styleColor: {\r\n              backgroundColor: ''\r\n            }\r\n        }\r\n    },\r\n    watch: {\r\n        isDraggableRegion() {\r\n              this.init()         \r\n        }\r\n    },\r\n\r\n    mounted() {\r\n        this.styleObject.top = '5%'\r\n        setTimeout(() => {\r\n\r\n            this.init()\r\n\r\n        }, 500)\r\n    },\r\n    methods: {\r\n        init() {\r\n            let oBox = document.getElementById(this._props.box)\r\n            // console.log('this._props.box:' + JSON.stringify(this._props.box))\r\n            // console.log('oBox.clientWidth:' + JSON.stringify(oBox.clientWidth))\r\n            // console.log('oBox.clientHeight:' + JSON.stringify(oBox.clientHeight))\r\n            if (!oBox) {\r\n                return\r\n            }\r\n            let width = oBox.clientWidth\r\n            let height = oBox.clientHeight\r\n            this.height = height / width\r\n\r\n            // console.log('AAAA' + JSON.stringify(this._props))\r\n            if (this.isDraggableRegion) {\r\n                dragDiv(this._props.box, this._props.childDrag.child.dID, this._props.childDrag.id, this._props.childDrag.child.zID, this.dragValue)\r\n            }\r\n            this.styleObject.height = ((this._props.childDrag.child.coordinates.lowRight.y - this._props.childDrag.child.coordinates.upperLeft.y) / this.height) * 100 + '%'\r\n            this.styleObject.top = (this._props.childDrag.child.coordinates.upperLeft.y / this.height) * 100 + '%'\r\n            this.styleColor.backgroundColor = this._props.childDrag.color\r\n        },\r\n        dragValue(value) {\r\n            // this.position=value // debug\r\n\r\n            if (this.childDrag.isEntity) {\r\n                value.entityNodeId = this.childDrag.entityNodeId\r\n                value.code = this.childDrag.code\r\n            }\r\n\r\n            value.lowRight.y = value.lowRight.y * this.height\r\n            value.upperLeft.y = value.upperLeft.y * this.height\r\n            this.$emit('dragContainer', value)\r\n        },\r\n        // 重置\r\n        itemChange(item) {\r\n            document.getElementById(item.id).style.width = (item.child.oldCoordinates.lowRight.x * 100 - item.child.oldCoordinates.upperLeft.x * 100) + '%'\r\n            document.getElementById(item.id).style.height = ((item.child.oldCoordinates.lowRight.y - item.child.oldCoordinates.upperLeft.y) / this.height) * 100 + '%'\r\n            document.getElementById(item.id).style.left = item.child.oldCoordinates.upperLeft.x * 100 + '%'\r\n            document.getElementById(item.id).style.top = (item.child.oldCoordinates.upperLeft.y / this.height) * 100 + '%'\r\n        },\r\n        // 重置操作\r\n        resetItem(item) {\r\n            this.$emit('okReset', item)\r\n        },\r\n        // 选中\r\n        pitchUp(item) {\r\n            if (arguments[1] !== 0) {\r\n                document.getElementById(arguments[1]).style.opacity = 0.7\r\n            }\r\n            document.getElementById(item.child.dID).style.opacity = 0.95\r\n        }\r\n    }\r\n}\r\n</script>\r\n"]}]}