{"remainingRequest": "E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\src\\components\\administrative\\systemManagement\\kmConfig.vue?vue&type=script&lang=js&", "dependencies": [{"path": "E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\src\\components\\administrative\\systemManagement\\kmConfig.vue", "mtime": 1754362736975}, {"path": "E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\node_modules\\string-replace-loader\\index.js", "mtime": 499162500000}, {"path": "E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgQ29uZmlnIGZyb20gIkAvY29tbW9uL2NvbmZpZyI7DQpsZXQgbW9tZW50ID0gcmVxdWlyZSgibW9tZW50Iik7DQpleHBvcnQgZGVmYXVsdCB7DQogIGRhdGEoKSB7DQogICAgcmV0dXJuIHsNCiAgICAgIGlzU2hvdzogZmFsc2UsDQogICAgICBpc0VkaXQ6IGZhbHNlLA0KICAgICAgc3lzdGVtSW5mbzogW10sDQogICAgICBvYmplY3RUeXBlTGlzdDogWw0KICAgICAgICB7DQogICAgICAgICAgdHlwZTogImVxdWlwbWVudCIsDQogICAgICAgICAgbmFtZTogdGhpcy4kdCgiTG9jYWxlU3RyaW5nLkQwMDAwOCIpLA0KICAgICAgICB9LA0KICAgICAgICB7DQogICAgICAgICAgdHlwZTogInBlb3BsZSIsDQogICAgICAgICAgbmFtZTogdGhpcy4kdCgiTG9jYWxlU3RyaW5nLkQwMDAwNyIpLA0KICAgICAgICB9LA0KICAgICAgICB7DQogICAgICAgICAgdHlwZTogInNwYWNlIiwNCiAgICAgICAgICBuYW1lOiB0aGlzLiR0KCJMb2NhbGVTdHJpbmcuRDAwMDA5IiksDQogICAgICAgIH0sDQogICAgICAgIHsNCiAgICAgICAgICB0eXBlOiAib3RoZXIiLA0KICAgICAgICAgIG5hbWU6IHRoaXMuJHQoIkxvY2FsZVN0cmluZy5EMDAwMTAiKSwNCiAgICAgICAgfSwNCiAgICAgIF0sDQogICAgICBub0RhdGFUZXh0OiB0aGlzLiR0KCJMb2NhbGVTdHJpbmcuTDAwMDgxIiksDQogICAgICBjb2x1bW5zTGlzdDogWw0KICAgICAgICB7DQogICAgICAgICAgdGl0bGU6IHRoaXMuJHQoIkxvY2FsZVN0cmluZy5MMDAwNDciKSwNCiAgICAgICAgICBrZXk6ICJ0eXBlIiwNCiAgICAgICAgICB3aWR0aDogMTAwLA0KICAgICAgICAgIHJlbmRlcjogKGgsIHBhcmFtcykgPT4gew0KICAgICAgICAgICAgcmV0dXJuIGgoInNwYW4iLCB0aGlzLmdldE9iamVjdFR5cGUocGFyYW1zLnJvdy50eXBlKSk7DQogICAgICAgICAgfSwNCiAgICAgICAgfSwNCiAgICAgICAgew0KICAgICAgICAgIHRpdGxlOiB0aGlzLiR0KCJMb2NhbGVTdHJpbmcuTDMwMTA4IiksDQogICAgICAgICAga2V5OiAibG9uZ1ByZXNzX2NodCIsDQogICAgICAgIH0sDQogICAgICAgIHsNCiAgICAgICAgICB0aXRsZTogdGhpcy4kdCgiTG9jYWxlU3RyaW5nLkwzMDEwOSIpLA0KICAgICAgICAgIGtleTogImxvbmdQcmVzc19lbiIsDQogICAgICAgIH0sDQogICAgICAgIHsNCiAgICAgICAgICB0aXRsZTogdGhpcy4kdCgiTG9jYWxlU3RyaW5nLkwzMDExMCIpLA0KICAgICAgICAgIGtleTogInNob3J0Q2xpY2tfY2h0IiwNCiAgICAgICAgfSwNCiAgICAgICAgew0KICAgICAgICAgIHRpdGxlOiB0aGlzLiR0KCJMb2NhbGVTdHJpbmcuTDMwMTExIiksDQogICAgICAgICAga2V5OiAic2hvcnRDbGlja19lbiIsDQogICAgICAgIH0sDQogICAgICAgIHsNCiAgICAgICAgICB0aXRsZTogdGhpcy4kdCgiTG9jYWxlU3RyaW5nLkwzMDExMiIpLA0KICAgICAgICAgIGtleTogImRvdWJsZUNsaWNrX2NodCIsDQogICAgICAgIH0sDQogICAgICAgIHsNCiAgICAgICAgICB0aXRsZTogdGhpcy4kdCgiTG9jYWxlU3RyaW5nLkwzMDExMyIpLA0KICAgICAgICAgIGtleTogImRvdWJsZUNsaWNrX2VuIiwNCiAgICAgICAgfSwNCiAgICAgIF0sDQogICAgICBjb2x1bW5zRWRpdDogWw0KICAgICAgICB7DQogICAgICAgICAgdGl0bGU6IHRoaXMuJHQoIkxvY2FsZVN0cmluZy5MMDAwNDciKSwNCiAgICAgICAgICBzbG90OiAidHlwZSIsDQogICAgICAgICAgd2lkdGg6IDEwMCwNCiAgICAgICAgICByZW5kZXI6IChoLCBwYXJhbXMpID0+IHsNCiAgICAgICAgICAgIHJldHVybiBoKCJzcGFuIiwgdGhpcy5nZXRPYmplY3RUeXBlKHBhcmFtcy5yb3cudHlwZSkpOw0KICAgICAgICAgIH0sDQogICAgICAgIH0sDQogICAgICAgIHsNCiAgICAgICAgICB0aXRsZTogdGhpcy4kdCgiTG9jYWxlU3RyaW5nLkwzMDEwOCIpLA0KICAgICAgICAgIHNsb3Q6ICJsb25nUHJlc3NfY2h0IiwNCiAgICAgICAgfSwNCiAgICAgICAgew0KICAgICAgICAgIHRpdGxlOiB0aGlzLiR0KCJMb2NhbGVTdHJpbmcuTDMwMTA5IiksDQogICAgICAgICAgc2xvdDogImxvbmdQcmVzc19lbiIsDQogICAgICAgIH0sDQogICAgICAgIHsNCiAgICAgICAgICB0aXRsZTogdGhpcy4kdCgiTG9jYWxlU3RyaW5nLkwzMDExMCIpLA0KICAgICAgICAgIHNsb3Q6ICJzaG9ydENsaWNrX2NodCIsDQogICAgICAgIH0sDQogICAgICAgIHsNCiAgICAgICAgICB0aXRsZTogdGhpcy4kdCgiTG9jYWxlU3RyaW5nLkwzMDExMSIpLA0KICAgICAgICAgIHNsb3Q6ICJzaG9ydENsaWNrX2VuIiwNCiAgICAgICAgfSwNCiAgICAgICAgew0KICAgICAgICAgIHRpdGxlOiB0aGlzLiR0KCJMb2NhbGVTdHJpbmcuTDMwMTEyIiksDQogICAgICAgICAgc2xvdDogImRvdWJsZUNsaWNrX2NodCIsDQogICAgICAgIH0sDQogICAgICAgIHsNCiAgICAgICAgICB0aXRsZTogdGhpcy4kdCgiTG9jYWxlU3RyaW5nLkwzMDExMyIpLA0KICAgICAgICAgIHNsb3Q6ICJkb3VibGVDbGlja19lbiIsDQogICAgICAgIH0sDQogICAgICBdLA0KICAgICAgY29uZmlnTGlzdDogWw0KICAgICAgICB7DQogICAgICAgICAgdHlwZTogImVxdWlwbWVudCIsDQogICAgICAgICAgbG9uZ1ByZXNzX2NodDogIiIsDQogICAgICAgICAgbG9uZ1ByZXNzX2VuOiAiIiwNCiAgICAgICAgICBzaG9ydENsaWNrX2NodDogIiIsDQogICAgICAgICAgc2hvcnRDbGlja19lbjogIiIsDQogICAgICAgICAgZG91YmxlQ2xpY2tfY2h0OiAiIiwNCiAgICAgICAgICBkb3VibGVDbGlja19lbjogIiIsDQogICAgICAgIH0sDQogICAgICAgIHsNCiAgICAgICAgICB0eXBlOiAicGVvcGxlIiwNCiAgICAgICAgICBsb25nUHJlc3NfY2h0OiAiIiwNCiAgICAgICAgICBsb25nUHJlc3NfZW46ICIiLA0KICAgICAgICAgIHNob3J0Q2xpY2tfY2h0OiAiIiwNCiAgICAgICAgICBzaG9ydENsaWNrX2VuOiAiIiwNCiAgICAgICAgICBkb3VibGVDbGlja19jaHQ6ICIiLA0KICAgICAgICAgIGRvdWJsZUNsaWNrX2VuOiAiIiwNCiAgICAgICAgfSwNCiAgICAgICAgew0KICAgICAgICAgIHR5cGU6ICJzcGFjZSIsDQogICAgICAgICAgbG9uZ1ByZXNzX2NodDogIiIsDQogICAgICAgICAgbG9uZ1ByZXNzX2VuOiAiIiwNCiAgICAgICAgICBzaG9ydENsaWNrX2NodDogIiIsDQogICAgICAgICAgc2hvcnRDbGlja19lbjogIiIsDQogICAgICAgICAgZG91YmxlQ2xpY2tfY2h0OiAiIiwNCiAgICAgICAgICBkb3VibGVDbGlja19lbjogIiIsDQogICAgICAgIH0sDQogICAgICAgIHsNCiAgICAgICAgICB0eXBlOiAib3RoZXIiLA0KICAgICAgICAgIGxvbmdQcmVzc19jaHQ6ICIiLA0KICAgICAgICAgIGxvbmdQcmVzc19lbjogIiIsDQogICAgICAgICAgc2hvcnRDbGlja19jaHQ6ICIiLA0KICAgICAgICAgIHNob3J0Q2xpY2tfZW46ICIiLA0KICAgICAgICAgIGRvdWJsZUNsaWNrX2NodDogIiIsDQogICAgICAgICAgZG91YmxlQ2xpY2tfZW46ICIiLA0KICAgICAgICB9LA0KICAgICAgXSwNCiAgICAgIGNvbmZpZ0xpc3RFZGl0OiBbDQogICAgICAgIHsNCiAgICAgICAgICB0eXBlOiAiZXF1aXBtZW50IiwNCiAgICAgICAgICBsb25nUHJlc3NfY2h0OiAiIiwNCiAgICAgICAgICBsb25nUHJlc3NfZW46ICIiLA0KICAgICAgICAgIHNob3J0Q2xpY2tfY2h0OiAiIiwNCiAgICAgICAgICBzaG9ydENsaWNrX2VuOiAiIiwNCiAgICAgICAgICBkb3VibGVDbGlja19jaHQ6ICIiLA0KICAgICAgICAgIGRvdWJsZUNsaWNrX2VuOiAiIiwNCiAgICAgICAgfSwNCiAgICAgICAgew0KICAgICAgICAgIHR5cGU6ICJwZW9wbGUiLA0KICAgICAgICAgIGxvbmdQcmVzc19jaHQ6ICIiLA0KICAgICAgICAgIGxvbmdQcmVzc19lbjogIiIsDQogICAgICAgICAgc2hvcnRDbGlja19jaHQ6ICIiLA0KICAgICAgICAgIHNob3J0Q2xpY2tfZW46ICIiLA0KICAgICAgICAgIGRvdWJsZUNsaWNrX2NodDogIiIsDQogICAgICAgICAgZG91YmxlQ2xpY2tfZW46ICIiLA0KICAgICAgICB9LA0KICAgICAgICB7DQogICAgICAgICAgdHlwZTogInNwYWNlIiwNCiAgICAgICAgICBsb25nUHJlc3NfY2h0OiAiIiwNCiAgICAgICAgICBsb25nUHJlc3NfZW46ICIiLA0KICAgICAgICAgIHNob3J0Q2xpY2tfY2h0OiAiIiwNCiAgICAgICAgICBzaG9ydENsaWNrX2VuOiAiIiwNCiAgICAgICAgICBkb3VibGVDbGlja19jaHQ6ICIiLA0KICAgICAgICAgIGRvdWJsZUNsaWNrX2VuOiAiIiwNCiAgICAgICAgfSwNCiAgICAgICAgew0KICAgICAgICAgIHR5cGU6ICJvdGhlciIsDQogICAgICAgICAgbG9uZ1ByZXNzX2NodDogIiIsDQogICAgICAgICAgbG9uZ1ByZXNzX2VuOiAiIiwNCiAgICAgICAgICBzaG9ydENsaWNrX2NodDogIiIsDQogICAgICAgICAgc2hvcnRDbGlja19lbjogIiIsDQogICAgICAgICAgZG91YmxlQ2xpY2tfY2h0OiAiIiwNCiAgICAgICAgICBkb3VibGVDbGlja19lbjogIiIsDQogICAgICAgIH0sDQogICAgICBdLA0KICAgIH07DQogIH0sDQogIG1vdW50ZWQoKSB7DQogICAgdGhpcy5pc1Nob3cgPSB0cnVlOw0KICAgIC8vdGhpcy5nZXRTeXN0ZW1BYm91dCgpOw0KICAgIHRoaXMubm9ybWFsaXplSGVpZ2h0KCk7DQogICAgdGhpcy5sb2FkRGF0YSgpOw0KICB9LA0KICBtZXRob2RzOiB7DQogICAgZ2V0T2JqZWN0VHlwZSh0eXBlKSB7DQogICAgICBpZiAodHlwZSAhPSB1bmRlZmluZWQpIHsNCiAgICAgICAgbGV0IHR5cGVEYXRhID0gdGhpcy5vYmplY3RUeXBlTGlzdC5maW5kKCh0KSA9PiB0LnR5cGUgPT0gdHlwZSkubmFtZTsNCiAgICAgICAgcmV0dXJuIHR5cGVEYXRhOw0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgcmV0dXJuICIiOw0KICAgICAgfQ0KICAgIH0sDQogICAgb25FZGl0RGF0YUNoYW5nZSh0eXBlLCByb3csIGluZGV4KSB7DQogICAgICBzd2l0Y2ggKHR5cGUpIHsNCiAgICAgICAgY2FzZSAibG9uZ1ByZXNzX2NodCI6DQogICAgICAgICAgdGhpcy5jb25maWdMaXN0RWRpdFtpbmRleF0ubG9uZ1ByZXNzX2NodCA9IHJvdy5sb25nUHJlc3NfY2h0Ow0KICAgICAgICAgIGJyZWFrOw0KICAgICAgICBjYXNlICJsb25nUHJlc3NfZW4iOg0KICAgICAgICAgIHRoaXMuY29uZmlnTGlzdEVkaXRbaW5kZXhdLmxvbmdQcmVzc19lbiA9IHJvdy5sb25nUHJlc3NfZW47DQogICAgICAgICAgYnJlYWs7DQogICAgICAgIGNhc2UgInNob3J0Q2xpY2tfY2h0IjoNCiAgICAgICAgICB0aGlzLmNvbmZpZ0xpc3RFZGl0W2luZGV4XS5zaG9ydENsaWNrX2NodCA9IHJvdy5zaG9ydENsaWNrX2NodDsNCiAgICAgICAgICBicmVhazsNCiAgICAgICAgY2FzZSAic2hvcnRDbGlja19lbiI6DQogICAgICAgICAgdGhpcy5jb25maWdMaXN0RWRpdFtpbmRleF0uc2hvcnRDbGlja19lbiA9IHJvdy5zaG9ydENsaWNrX2VuOw0KICAgICAgICAgIGJyZWFrOw0KICAgICAgICBjYXNlICJkb3VibGVDbGlja19jaHQiOg0KICAgICAgICAgIHRoaXMuY29uZmlnTGlzdEVkaXRbaW5kZXhdLmRvdWJsZUNsaWNrX2NodCA9IHJvdy5kb3VibGVDbGlja19jaHQ7DQogICAgICAgICAgYnJlYWs7DQogICAgICAgIGNhc2UgImRvdWJsZUNsaWNrX2VuIjoNCiAgICAgICAgICB0aGlzLmNvbmZpZ0xpc3RFZGl0W2luZGV4XS5kb3VibGVDbGlja19lbiA9IHJvdy5kb3VibGVDbGlja19lbjsNCiAgICAgICAgICBicmVhazsNCiAgICAgIH0NCiAgICB9LA0KICAgIGxvYWREYXRhKCkgew0KICAgICAgbGV0IHBhcmFtcyA9IHsNCiAgICAgICAgaW5saW5lY291bnQ6IHRydWUsDQogICAgICAgIHNlYXJjaDogImNhdGVnb3J5IGVxIEBTTlNAU2V0dGluZ0tNIiwNCiAgICAgIH07DQoNCiAgICAgIHRoaXMuJHNlcnZpY2UuZ2V0UE9DUHJvcGVydGllcy5zZW5kKHBhcmFtcykudGhlbigocmVzKSA9PiB7DQogICAgICAgIGlmIChyZXMuY291bnQgPiAwKSB7DQogICAgICAgICAgcmVzLnJlc3VsdHNbMF0ucHJvcGVydGllcy5mb3JFYWNoKChyZXMpID0+IHsNCiAgICAgICAgICAgIHN3aXRjaCAocmVzLmtleSkgew0KICAgICAgICAgICAgICBjYXNlICJlcXVpcG1lbnRfbG9uZ1ByZXNzX2NodCI6DQogICAgICAgICAgICAgICAgdGhpcy5jb25maWdMaXN0WzBdLmxvbmdQcmVzc19jaHQgPSByZXMudmFsdWU7DQogICAgICAgICAgICAgICAgYnJlYWs7DQogICAgICAgICAgICAgIGNhc2UgImVxdWlwbWVudF9sb25nUHJlc3NfZW4iOg0KICAgICAgICAgICAgICAgIHRoaXMuY29uZmlnTGlzdFswXS5sb25nUHJlc3NfZW4gPSByZXMudmFsdWU7DQogICAgICAgICAgICAgICAgYnJlYWs7DQogICAgICAgICAgICAgIGNhc2UgImVxdWlwbWVudF9zaG9ydENsaWNrX2NodCI6DQogICAgICAgICAgICAgICAgdGhpcy5jb25maWdMaXN0WzBdLnNob3J0Q2xpY2tfY2h0ID0gcmVzLnZhbHVlOw0KICAgICAgICAgICAgICAgIGJyZWFrOw0KICAgICAgICAgICAgICBjYXNlICJlcXVpcG1lbnRfc2hvcnRDbGlja19lbiI6DQogICAgICAgICAgICAgICAgdGhpcy5jb25maWdMaXN0WzBdLnNob3J0Q2xpY2tfZW4gPSByZXMudmFsdWU7DQogICAgICAgICAgICAgICAgYnJlYWs7DQogICAgICAgICAgICAgIGNhc2UgImVxdWlwbWVudF9kb3VibGVDbGlja19jaHQiOg0KICAgICAgICAgICAgICAgIHRoaXMuY29uZmlnTGlzdFswXS5kb3VibGVDbGlja19jaHQgPSByZXMudmFsdWU7DQogICAgICAgICAgICAgICAgYnJlYWs7DQogICAgICAgICAgICAgIGNhc2UgImVxdWlwbWVudF9kb3VibGVDbGlja19lbiI6DQogICAgICAgICAgICAgICAgdGhpcy5jb25maWdMaXN0WzBdLmRvdWJsZUNsaWNrX2VuID0gcmVzLnZhbHVlOw0KICAgICAgICAgICAgICAgIGJyZWFrOw0KICAgICAgICAgICAgICBjYXNlICJwZW9wbGVfbG9uZ1ByZXNzX2NodCI6DQogICAgICAgICAgICAgICAgdGhpcy5jb25maWdMaXN0WzFdLmxvbmdQcmVzc19jaHQgPSByZXMudmFsdWU7DQogICAgICAgICAgICAgICAgYnJlYWs7DQogICAgICAgICAgICAgIGNhc2UgInBlb3BsZV9sb25nUHJlc3NfZW4iOg0KICAgICAgICAgICAgICAgIHRoaXMuY29uZmlnTGlzdFsxXS5sb25nUHJlc3NfZW4gPSByZXMudmFsdWU7DQogICAgICAgICAgICAgICAgYnJlYWs7DQogICAgICAgICAgICAgIGNhc2UgInBlb3BsZV9zaG9ydENsaWNrX2NodCI6DQogICAgICAgICAgICAgICAgdGhpcy5jb25maWdMaXN0WzFdLnNob3J0Q2xpY2tfY2h0ID0gcmVzLnZhbHVlOw0KICAgICAgICAgICAgICAgIGJyZWFrOw0KICAgICAgICAgICAgICBjYXNlICJwZW9wbGVfc2hvcnRDbGlja19lbiI6DQogICAgICAgICAgICAgICAgdGhpcy5jb25maWdMaXN0WzFdLnNob3J0Q2xpY2tfZW4gPSByZXMudmFsdWU7DQogICAgICAgICAgICAgICAgYnJlYWs7DQogICAgICAgICAgICAgIGNhc2UgInBlb3BsZV9kb3VibGVDbGlja19jaHQiOg0KICAgICAgICAgICAgICAgIHRoaXMuY29uZmlnTGlzdFsxXS5kb3VibGVDbGlja19jaHQgPSByZXMudmFsdWU7DQogICAgICAgICAgICAgICAgYnJlYWs7DQogICAgICAgICAgICAgIGNhc2UgInBlb3BsZV9kb3VibGVDbGlja19lbiI6DQogICAgICAgICAgICAgICAgdGhpcy5jb25maWdMaXN0WzFdLmRvdWJsZUNsaWNrX2VuID0gcmVzLnZhbHVlOw0KICAgICAgICAgICAgICAgIGJyZWFrOw0KICAgICAgICAgICAgICBjYXNlICJzcGFjZV9sb25nUHJlc3NfY2h0IjoNCiAgICAgICAgICAgICAgICB0aGlzLmNvbmZpZ0xpc3RbMl0ubG9uZ1ByZXNzX2NodCA9IHJlcy52YWx1ZTsNCiAgICAgICAgICAgICAgICBicmVhazsNCiAgICAgICAgICAgICAgY2FzZSAic3BhY2VfbG9uZ1ByZXNzX2VuIjoNCiAgICAgICAgICAgICAgICB0aGlzLmNvbmZpZ0xpc3RbMl0ubG9uZ1ByZXNzX2VuID0gcmVzLnZhbHVlOw0KICAgICAgICAgICAgICAgIGJyZWFrOw0KICAgICAgICAgICAgICBjYXNlICJzcGFjZV9zaG9ydENsaWNrX2NodCI6DQogICAgICAgICAgICAgICAgdGhpcy5jb25maWdMaXN0WzJdLnNob3J0Q2xpY2tfY2h0ID0gcmVzLnZhbHVlOw0KICAgICAgICAgICAgICAgIGJyZWFrOw0KICAgICAgICAgICAgICBjYXNlICJzcGFjZV9zaG9ydENsaWNrX2VuIjoNCiAgICAgICAgICAgICAgICB0aGlzLmNvbmZpZ0xpc3RbMl0uc2hvcnRDbGlja19lbiA9IHJlcy52YWx1ZTsNCiAgICAgICAgICAgICAgICBicmVhazsNCiAgICAgICAgICAgICAgY2FzZSAic3BhY2VfZG91YmxlQ2xpY2tfY2h0IjoNCiAgICAgICAgICAgICAgICB0aGlzLmNvbmZpZ0xpc3RbMl0uZG91YmxlQ2xpY2tfY2h0ID0gcmVzLnZhbHVlOw0KICAgICAgICAgICAgICAgIGJyZWFrOw0KICAgICAgICAgICAgICBjYXNlICJzcGFjZV9kb3VibGVDbGlja19lbiI6DQogICAgICAgICAgICAgICAgdGhpcy5jb25maWdMaXN0WzJdLmRvdWJsZUNsaWNrX2VuID0gcmVzLnZhbHVlOw0KICAgICAgICAgICAgICAgIGJyZWFrOw0KICAgICAgICAgICAgICBjYXNlICJvdGhlcl9sb25nUHJlc3NfY2h0IjoNCiAgICAgICAgICAgICAgICB0aGlzLmNvbmZpZ0xpc3RbM10ubG9uZ1ByZXNzX2NodCA9IHJlcy52YWx1ZTsNCiAgICAgICAgICAgICAgICBicmVhazsNCiAgICAgICAgICAgICAgY2FzZSAib3RoZXJfbG9uZ1ByZXNzX2VuIjoNCiAgICAgICAgICAgICAgICB0aGlzLmNvbmZpZ0xpc3RbM10ubG9uZ1ByZXNzX2VuID0gcmVzLnZhbHVlOw0KICAgICAgICAgICAgICAgIGJyZWFrOw0KICAgICAgICAgICAgICBjYXNlICJvdGhlcl9zaG9ydENsaWNrX2NodCI6DQogICAgICAgICAgICAgICAgdGhpcy5jb25maWdMaXN0WzNdLnNob3J0Q2xpY2tfY2h0ID0gcmVzLnZhbHVlOw0KICAgICAgICAgICAgICAgIGJyZWFrOw0KICAgICAgICAgICAgICBjYXNlICJvdGhlcl9zaG9ydENsaWNrX2VuIjoNCiAgICAgICAgICAgICAgICB0aGlzLmNvbmZpZ0xpc3RbM10uc2hvcnRDbGlja19lbiA9IHJlcy52YWx1ZTsNCiAgICAgICAgICAgICAgICBicmVhazsNCiAgICAgICAgICAgICAgY2FzZSAib3RoZXJfZG91YmxlQ2xpY2tfY2h0IjoNCiAgICAgICAgICAgICAgICB0aGlzLmNvbmZpZ0xpc3RbM10uZG91YmxlQ2xpY2tfY2h0ID0gcmVzLnZhbHVlOw0KICAgICAgICAgICAgICAgIGJyZWFrOw0KICAgICAgICAgICAgICBjYXNlICJvdGhlcl9kb3VibGVDbGlja19lbiI6DQogICAgICAgICAgICAgICAgdGhpcy5jb25maWdMaXN0WzNdLmRvdWJsZUNsaWNrX2VuID0gcmVzLnZhbHVlOw0KICAgICAgICAgICAgICAgIGJyZWFrOw0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0pOw0KICAgICAgICB9DQogICAgICAgIHRoaXMuY29uZmlnTGlzdEVkaXQgPSBKU09OLnBhcnNlKEpTT04uc3RyaW5naWZ5KHRoaXMuY29uZmlnTGlzdCkpOw0KICAgICAgfSk7DQogICAgfSwNCiAgICBzYXZlRGF0YSgpIHsNCiAgICAgIGxldCBwdXREYXRhcyA9IFtdOw0KICAgICAgbGV0IHB1dERhdGEgPSB7DQogICAgICAgIGNhdGVnb3J5OiAiQFNOU0BTZXR0aW5nS00iLA0KICAgICAgICBwcm9wZXJ0aWVzOiBbXSwNCiAgICAgIH07DQogICAgICB0aGlzLmNvbmZpZ0xpc3RFZGl0LmZvckVhY2goKGl0ZW0pID0+IHsNCiAgICAgICAgbGV0IGtleVZhbHVlUGFpcnMgPSBbDQogICAgICAgICAgew0KICAgICAgICAgICAga2V5OiBpdGVtLnR5cGUgKyAiX2xvbmdQcmVzc19jaHQiLA0KICAgICAgICAgICAgdmFsdWU6IGl0ZW0ubG9uZ1ByZXNzX2NodCwNCiAgICAgICAgICB9LA0KICAgICAgICAgIHsNCiAgICAgICAgICAgIGtleTogaXRlbS50eXBlICsgIl9sb25nUHJlc3NfZW4iLA0KICAgICAgICAgICAgdmFsdWU6IGl0ZW0ubG9uZ1ByZXNzX2VuLA0KICAgICAgICAgIH0sDQogICAgICAgICAgew0KICAgICAgICAgICAga2V5OiBpdGVtLnR5cGUgKyAiX3Nob3J0Q2xpY2tfY2h0IiwNCiAgICAgICAgICAgIHZhbHVlOiBpdGVtLnNob3J0Q2xpY2tfY2h0LA0KICAgICAgICAgIH0sDQogICAgICAgICAgew0KICAgICAgICAgICAga2V5OiBpdGVtLnR5cGUgKyAiX3Nob3J0Q2xpY2tfZW4iLA0KICAgICAgICAgICAgdmFsdWU6IGl0ZW0uc2hvcnRDbGlja19lbiwNCiAgICAgICAgICB9LA0KICAgICAgICAgIHsNCiAgICAgICAgICAgIGtleTogaXRlbS50eXBlICsgIl9kb3VibGVDbGlja19jaHQiLA0KICAgICAgICAgICAgdmFsdWU6IGl0ZW0uZG91YmxlQ2xpY2tfY2h0LA0KICAgICAgICAgIH0sDQogICAgICAgICAgew0KICAgICAgICAgICAga2V5OiBpdGVtLnR5cGUgKyAiX2RvdWJsZUNsaWNrX2VuIiwNCiAgICAgICAgICAgIHZhbHVlOiBpdGVtLmRvdWJsZUNsaWNrX2VuLA0KICAgICAgICAgIH0sDQogICAgICAgIF07DQogICAgICAgIHB1dERhdGEucHJvcGVydGllcyA9IFsuLi5wdXREYXRhLnByb3BlcnRpZXMsIC4uLmtleVZhbHVlUGFpcnNdOw0KICAgICAgfSk7DQogICAgICBwdXREYXRhcy5wdXNoKHB1dERhdGEpOw0KICAgICAgdGhpcy4kc2VydmljZS5lZGl0UE9DUHJvcGVydGllcy5zZW5kKHB1dERhdGFzKS50aGVuKChyZXMpID0+IHsNCiAgICAgICAgdGhpcy5pc0VkaXQgPSBmYWxzZTsNCiAgICAgICAgdGhpcy5sb2FkRGF0YSgpOw0KICAgICAgICB0aGlzLiRzdG9yZS5jb21taXQoInNldEttQ29uZmlnQ2hhbmdlZCIsIG1vbWVudCgpLnZhbHVlT2YoKSk7DQogICAgICB9KTsNCiAgICB9LA0KICAgIGNsZWFyRGF0YSgpIHsNCiAgICAgIHRoaXMuaXNFZGl0ID0gZmFsc2U7DQogICAgfSwNCiAgICBlZGl0RGF0YSgpIHsNCiAgICAgIHRoaXMuaXNFZGl0ID0gdHJ1ZTsNCiAgICB9LA0KICAgIG5vcm1hbGl6ZUhlaWdodCgpIHsNCiAgICAgIHNldFRpbWVvdXQoDQogICAgICAgIGZ1bmN0aW9uICgpIHsNCiAgICAgICAgICBjb25zdCBtb2RhbCA9IGRvY3VtZW50LnF1ZXJ5U2VsZWN0b3IoIi5pdnUtbW9kYWwtYm9keSAuY29udGVudCIpOw0KICAgICAgICAgIGlmIChtb2RhbCkgew0KICAgICAgICAgICAgbW9kYWwuc3R5bGUuaGVpZ2h0ID0gIjI1MHB4IjsNCiAgICAgICAgICB9DQogICAgICAgIH0uYmluZCh0aGlzKSwNCiAgICAgICAgNTANCiAgICAgICk7DQogICAgfSwNCiAgICBkeW5hbWljU29ydChwcm9wZXJ0eSkgew0KICAgICAgdmFyIHNvcnRPcmRlciA9IDE7DQogICAgICBpZiAocHJvcGVydHlbMF0gPT09ICItIikgew0KICAgICAgICBzb3J0T3JkZXIgPSAtMTsNCiAgICAgICAgcHJvcGVydHkgPSBwcm9wZXJ0eS5zdWJzdHIoMSk7DQogICAgICB9DQogICAgICByZXR1cm4gZnVuY3Rpb24gKGEsIGIpIHsNCiAgICAgICAgdmFyIHJlc3VsdCA9DQogICAgICAgICAgYVtwcm9wZXJ0eV0gPCBiW3Byb3BlcnR5XSA/IC0xIDogYVtwcm9wZXJ0eV0gPiBiW3Byb3BlcnR5XSA/IDEgOiAwOw0KICAgICAgICByZXR1cm4gcmVzdWx0ICogc29ydE9yZGVyOw0KICAgICAgfTsNCiAgICB9LA0KICAgIHJlZnJlc2hCdG4oKSB7DQogICAgICB0aGlzLmdldFN5c3RlbUFib3V0KCk7DQogICAgfSwNCiAgICBnZXRTeXN0ZW1BYm91dCgpIHsNCiAgICAgIGxldCBfdGhpcyA9IHRoaXM7DQogICAgICBfdGhpcy5zeXN0ZW1JbmZvLnB1c2goew0KICAgICAgICBuYW1lOiB0aGlzLiR0KCJMb2NhbGVTdHJpbmcuUzAwMDAyIiksDQogICAgICAgIHZlcnNpb246IENvbmZpZy5XRUJfVkVSU0lPTiwNCiAgICAgIH0pOw0KICAgIH0sDQogICAgY29tcHV0ZWREYXRlKHZhbCkgew0KICAgICAgaWYgKHZhbCkgew0KICAgICAgICByZXR1cm4gbW9tZW50KHZhbCkuZm9ybWF0KCJZWVlZLU1NLUREIik7DQogICAgICB9DQogICAgICByZXR1cm4gIiI7DQogICAgfSwNCiAgICBjb21wdXRlZERhdGVUaW1lKHZhbCkgew0KICAgICAgaWYgKHZhbCkgew0KICAgICAgICByZXR1cm4gbW9tZW50KHZhbCkuZm9ybWF0KCJZWVlZLU1NLUREIEhIOm1tOnNzIik7DQogICAgICB9DQogICAgICByZXR1cm4gIiI7DQogICAgfSwNCiAgICBjYW5jZWwoKSB7DQogICAgICB0aGlzLmlzU2hvdyA9IGZhbHNlOw0KICAgICAgc2V0VGltZW91dCgoKSA9PiB7DQogICAgICAgIHRoaXMuJGVtaXQoImNsb3NlS01Db25maWciKTsNCiAgICAgIH0sIDUwMCk7DQogICAgfSwNCiAgfSwNCn07DQo="}, {"version": 3, "sources": ["kmConfig.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqHA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "kmConfig.vue", "sourceRoot": "src/components/administrative/systemManagement", "sourcesContent": ["<template>\r\n  <div class=\"personal-data\">\r\n    <Modal\r\n      class=\"about-data-modal\"\r\n      :closable=\"false\"\r\n      :mask-closable=\"false\"\r\n      v-model=\"isShow\"\r\n      class-name=\"vertical-center-modal\"\r\n      width=\"1100\"\r\n    >\r\n      <div slot=\"header\" class=\"header\">\r\n        <div>\r\n          <div style=\"float: left\">\r\n            <span class=\"title\">{{ $t(\"LocaleString.L30107\") }}</span>\r\n          </div>\r\n          <div style=\"float: right; margin-bottom: 10px\">\r\n            <Button\r\n              v-show=\"!isEdit\"\r\n              type=\"primary\"\r\n              class=\"font-small\"\r\n              @click=\"editData\"\r\n              >{{ $t(\"LocaleString.B20014\") }}</Button\r\n            >\r\n            <Button class=\"font-small\" @click=\"cancel\" v-show=\"!isEdit\">{{\r\n              $t(\"LocaleString.B00044\")\r\n            }}</Button>\r\n            <Button\r\n              v-show=\"isEdit\"\r\n              type=\"primary\"\r\n              class=\"font-small\"\r\n              @click=\"saveData\"\r\n              >{{ $t(\"LocaleString.B00012\") }}</Button\r\n            >\r\n            <Button v-show=\"isEdit\" class=\"font-small\" @click=\"clearData\">{{\r\n              $t(\"LocaleString.B00015\")\r\n            }}</Button>\r\n          </div>\r\n          <div style=\"clear: both\"></div>\r\n        </div>\r\n      </div>\r\n      <div class=\"content\">\r\n        <Table\r\n          v-if=\"!isEdit\"\r\n          class=\"config-table\"\r\n          :columns=\"columnsList\"\r\n          :data=\"configList\"\r\n          :no-data-text=\"noDataText\"\r\n        >\r\n          <template slot-scope=\"{ row, index }\" slot=\"type\">\r\n            <span> {{ row.type }} </span>\r\n          </template>\r\n        </Table>\r\n        <Table\r\n          v-if=\"isEdit\"\r\n          class=\"config-table\"\r\n          :columns=\"columnsEdit\"\r\n          :data=\"configListEdit\"\r\n          :no-data-text=\"noDataText\"\r\n        >\r\n          <template slot-scope=\"{ row, index }\" slot=\"type\">\r\n            <span> {{ getObjectType(row.type) }} </span>\r\n          </template>\r\n          <template slot-scope=\"{ row, index }\" slot=\"longPress_cht\">\r\n            <Input\r\n              type=\"text\"\r\n              v-model=\"row.longPress_cht\"\r\n              maxlength=\"20\"\r\n              @on-change=\"onEditDataChange('longPress_cht', row, index)\"\r\n            />\r\n          </template>\r\n          <template slot-scope=\"{ row, index }\" slot=\"longPress_en\">\r\n            <Input\r\n              type=\"text\"\r\n              v-model=\"row.longPress_en\"\r\n              maxlength=\"20\"\r\n              @on-change=\"onEditDataChange('longPress_en', row, index)\"\r\n            />\r\n          </template>\r\n          <template slot-scope=\"{ row, index }\" slot=\"shortClick_cht\">\r\n            <Input\r\n              type=\"text\"\r\n              v-model=\"row.shortClick_cht\"\r\n              maxlength=\"20\"\r\n              @on-change=\"onEditDataChange('shortClick_cht', row, index)\"\r\n            />\r\n          </template>\r\n          <template slot-scope=\"{ row, index }\" slot=\"shortClick_en\">\r\n            <Input\r\n              type=\"text\"\r\n              v-model=\"row.shortClick_en\"\r\n              maxlength=\"20\"\r\n              @on-change=\"onEditDataChange('shortClick_en', row, index)\"\r\n            />\r\n          </template>\r\n          <template slot-scope=\"{ row, index }\" slot=\"doubleClick_cht\">\r\n            <Input\r\n              type=\"text\"\r\n              v-model=\"row.doubleClick_cht\"\r\n              maxlength=\"20\"\r\n              @on-change=\"onEditDataChange('doubleClick_cht', row, index)\"\r\n            />\r\n          </template>\r\n          <template slot-scope=\"{ row, index }\" slot=\"doubleClick_en\">\r\n            <Input\r\n              type=\"text\"\r\n              v-model=\"row.doubleClick_en\"\r\n              maxlength=\"20\"\r\n              @on-change=\"onEditDataChange('doubleClick_en', row, index)\"\r\n            />\r\n          </template>\r\n        </Table>\r\n      </div>\r\n      <div slot=\"footer\" style=\"text-align: center; margin-top: 10px\"></div>\r\n    </Modal>\r\n  </div>\r\n</template>\r\n<script type='es6'>\r\nimport Config from \"@/common/config\";\r\nlet moment = require(\"moment\");\r\nexport default {\r\n  data() {\r\n    return {\r\n      isShow: false,\r\n      isEdit: false,\r\n      systemInfo: [],\r\n      objectTypeList: [\r\n        {\r\n          type: \"equipment\",\r\n          name: this.$t(\"LocaleString.D00008\"),\r\n        },\r\n        {\r\n          type: \"people\",\r\n          name: this.$t(\"LocaleString.D00007\"),\r\n        },\r\n        {\r\n          type: \"space\",\r\n          name: this.$t(\"LocaleString.D00009\"),\r\n        },\r\n        {\r\n          type: \"other\",\r\n          name: this.$t(\"LocaleString.D00010\"),\r\n        },\r\n      ],\r\n      noDataText: this.$t(\"LocaleString.L00081\"),\r\n      columnsList: [\r\n        {\r\n          title: this.$t(\"LocaleString.L00047\"),\r\n          key: \"type\",\r\n          width: 100,\r\n          render: (h, params) => {\r\n            return h(\"span\", this.getObjectType(params.row.type));\r\n          },\r\n        },\r\n        {\r\n          title: this.$t(\"LocaleString.L30108\"),\r\n          key: \"longPress_cht\",\r\n        },\r\n        {\r\n          title: this.$t(\"LocaleString.L30109\"),\r\n          key: \"longPress_en\",\r\n        },\r\n        {\r\n          title: this.$t(\"LocaleString.L30110\"),\r\n          key: \"shortClick_cht\",\r\n        },\r\n        {\r\n          title: this.$t(\"LocaleString.L30111\"),\r\n          key: \"shortClick_en\",\r\n        },\r\n        {\r\n          title: this.$t(\"LocaleString.L30112\"),\r\n          key: \"doubleClick_cht\",\r\n        },\r\n        {\r\n          title: this.$t(\"LocaleString.L30113\"),\r\n          key: \"doubleClick_en\",\r\n        },\r\n      ],\r\n      columnsEdit: [\r\n        {\r\n          title: this.$t(\"LocaleString.L00047\"),\r\n          slot: \"type\",\r\n          width: 100,\r\n          render: (h, params) => {\r\n            return h(\"span\", this.getObjectType(params.row.type));\r\n          },\r\n        },\r\n        {\r\n          title: this.$t(\"LocaleString.L30108\"),\r\n          slot: \"longPress_cht\",\r\n        },\r\n        {\r\n          title: this.$t(\"LocaleString.L30109\"),\r\n          slot: \"longPress_en\",\r\n        },\r\n        {\r\n          title: this.$t(\"LocaleString.L30110\"),\r\n          slot: \"shortClick_cht\",\r\n        },\r\n        {\r\n          title: this.$t(\"LocaleString.L30111\"),\r\n          slot: \"shortClick_en\",\r\n        },\r\n        {\r\n          title: this.$t(\"LocaleString.L30112\"),\r\n          slot: \"doubleClick_cht\",\r\n        },\r\n        {\r\n          title: this.$t(\"LocaleString.L30113\"),\r\n          slot: \"doubleClick_en\",\r\n        },\r\n      ],\r\n      configList: [\r\n        {\r\n          type: \"equipment\",\r\n          longPress_cht: \"\",\r\n          longPress_en: \"\",\r\n          shortClick_cht: \"\",\r\n          shortClick_en: \"\",\r\n          doubleClick_cht: \"\",\r\n          doubleClick_en: \"\",\r\n        },\r\n        {\r\n          type: \"people\",\r\n          longPress_cht: \"\",\r\n          longPress_en: \"\",\r\n          shortClick_cht: \"\",\r\n          shortClick_en: \"\",\r\n          doubleClick_cht: \"\",\r\n          doubleClick_en: \"\",\r\n        },\r\n        {\r\n          type: \"space\",\r\n          longPress_cht: \"\",\r\n          longPress_en: \"\",\r\n          shortClick_cht: \"\",\r\n          shortClick_en: \"\",\r\n          doubleClick_cht: \"\",\r\n          doubleClick_en: \"\",\r\n        },\r\n        {\r\n          type: \"other\",\r\n          longPress_cht: \"\",\r\n          longPress_en: \"\",\r\n          shortClick_cht: \"\",\r\n          shortClick_en: \"\",\r\n          doubleClick_cht: \"\",\r\n          doubleClick_en: \"\",\r\n        },\r\n      ],\r\n      configListEdit: [\r\n        {\r\n          type: \"equipment\",\r\n          longPress_cht: \"\",\r\n          longPress_en: \"\",\r\n          shortClick_cht: \"\",\r\n          shortClick_en: \"\",\r\n          doubleClick_cht: \"\",\r\n          doubleClick_en: \"\",\r\n        },\r\n        {\r\n          type: \"people\",\r\n          longPress_cht: \"\",\r\n          longPress_en: \"\",\r\n          shortClick_cht: \"\",\r\n          shortClick_en: \"\",\r\n          doubleClick_cht: \"\",\r\n          doubleClick_en: \"\",\r\n        },\r\n        {\r\n          type: \"space\",\r\n          longPress_cht: \"\",\r\n          longPress_en: \"\",\r\n          shortClick_cht: \"\",\r\n          shortClick_en: \"\",\r\n          doubleClick_cht: \"\",\r\n          doubleClick_en: \"\",\r\n        },\r\n        {\r\n          type: \"other\",\r\n          longPress_cht: \"\",\r\n          longPress_en: \"\",\r\n          shortClick_cht: \"\",\r\n          shortClick_en: \"\",\r\n          doubleClick_cht: \"\",\r\n          doubleClick_en: \"\",\r\n        },\r\n      ],\r\n    };\r\n  },\r\n  mounted() {\r\n    this.isShow = true;\r\n    //this.getSystemAbout();\r\n    this.normalizeHeight();\r\n    this.loadData();\r\n  },\r\n  methods: {\r\n    getObjectType(type) {\r\n      if (type != undefined) {\r\n        let typeData = this.objectTypeList.find((t) => t.type == type).name;\r\n        return typeData;\r\n      } else {\r\n        return \"\";\r\n      }\r\n    },\r\n    onEditDataChange(type, row, index) {\r\n      switch (type) {\r\n        case \"longPress_cht\":\r\n          this.configListEdit[index].longPress_cht = row.longPress_cht;\r\n          break;\r\n        case \"longPress_en\":\r\n          this.configListEdit[index].longPress_en = row.longPress_en;\r\n          break;\r\n        case \"shortClick_cht\":\r\n          this.configListEdit[index].shortClick_cht = row.shortClick_cht;\r\n          break;\r\n        case \"shortClick_en\":\r\n          this.configListEdit[index].shortClick_en = row.shortClick_en;\r\n          break;\r\n        case \"doubleClick_cht\":\r\n          this.configListEdit[index].doubleClick_cht = row.doubleClick_cht;\r\n          break;\r\n        case \"doubleClick_en\":\r\n          this.configListEdit[index].doubleClick_en = row.doubleClick_en;\r\n          break;\r\n      }\r\n    },\r\n    loadData() {\r\n      let params = {\r\n        inlinecount: true,\r\n        search: \"category eq @SNS@SettingKM\",\r\n      };\r\n\r\n      this.$service.getPOCProperties.send(params).then((res) => {\r\n        if (res.count > 0) {\r\n          res.results[0].properties.forEach((res) => {\r\n            switch (res.key) {\r\n              case \"equipment_longPress_cht\":\r\n                this.configList[0].longPress_cht = res.value;\r\n                break;\r\n              case \"equipment_longPress_en\":\r\n                this.configList[0].longPress_en = res.value;\r\n                break;\r\n              case \"equipment_shortClick_cht\":\r\n                this.configList[0].shortClick_cht = res.value;\r\n                break;\r\n              case \"equipment_shortClick_en\":\r\n                this.configList[0].shortClick_en = res.value;\r\n                break;\r\n              case \"equipment_doubleClick_cht\":\r\n                this.configList[0].doubleClick_cht = res.value;\r\n                break;\r\n              case \"equipment_doubleClick_en\":\r\n                this.configList[0].doubleClick_en = res.value;\r\n                break;\r\n              case \"people_longPress_cht\":\r\n                this.configList[1].longPress_cht = res.value;\r\n                break;\r\n              case \"people_longPress_en\":\r\n                this.configList[1].longPress_en = res.value;\r\n                break;\r\n              case \"people_shortClick_cht\":\r\n                this.configList[1].shortClick_cht = res.value;\r\n                break;\r\n              case \"people_shortClick_en\":\r\n                this.configList[1].shortClick_en = res.value;\r\n                break;\r\n              case \"people_doubleClick_cht\":\r\n                this.configList[1].doubleClick_cht = res.value;\r\n                break;\r\n              case \"people_doubleClick_en\":\r\n                this.configList[1].doubleClick_en = res.value;\r\n                break;\r\n              case \"space_longPress_cht\":\r\n                this.configList[2].longPress_cht = res.value;\r\n                break;\r\n              case \"space_longPress_en\":\r\n                this.configList[2].longPress_en = res.value;\r\n                break;\r\n              case \"space_shortClick_cht\":\r\n                this.configList[2].shortClick_cht = res.value;\r\n                break;\r\n              case \"space_shortClick_en\":\r\n                this.configList[2].shortClick_en = res.value;\r\n                break;\r\n              case \"space_doubleClick_cht\":\r\n                this.configList[2].doubleClick_cht = res.value;\r\n                break;\r\n              case \"space_doubleClick_en\":\r\n                this.configList[2].doubleClick_en = res.value;\r\n                break;\r\n              case \"other_longPress_cht\":\r\n                this.configList[3].longPress_cht = res.value;\r\n                break;\r\n              case \"other_longPress_en\":\r\n                this.configList[3].longPress_en = res.value;\r\n                break;\r\n              case \"other_shortClick_cht\":\r\n                this.configList[3].shortClick_cht = res.value;\r\n                break;\r\n              case \"other_shortClick_en\":\r\n                this.configList[3].shortClick_en = res.value;\r\n                break;\r\n              case \"other_doubleClick_cht\":\r\n                this.configList[3].doubleClick_cht = res.value;\r\n                break;\r\n              case \"other_doubleClick_en\":\r\n                this.configList[3].doubleClick_en = res.value;\r\n                break;\r\n            }\r\n          });\r\n        }\r\n        this.configListEdit = JSON.parse(JSON.stringify(this.configList));\r\n      });\r\n    },\r\n    saveData() {\r\n      let putDatas = [];\r\n      let putData = {\r\n        category: \"@SNS@SettingKM\",\r\n        properties: [],\r\n      };\r\n      this.configListEdit.forEach((item) => {\r\n        let keyValuePairs = [\r\n          {\r\n            key: item.type + \"_longPress_cht\",\r\n            value: item.longPress_cht,\r\n          },\r\n          {\r\n            key: item.type + \"_longPress_en\",\r\n            value: item.longPress_en,\r\n          },\r\n          {\r\n            key: item.type + \"_shortClick_cht\",\r\n            value: item.shortClick_cht,\r\n          },\r\n          {\r\n            key: item.type + \"_shortClick_en\",\r\n            value: item.shortClick_en,\r\n          },\r\n          {\r\n            key: item.type + \"_doubleClick_cht\",\r\n            value: item.doubleClick_cht,\r\n          },\r\n          {\r\n            key: item.type + \"_doubleClick_en\",\r\n            value: item.doubleClick_en,\r\n          },\r\n        ];\r\n        putData.properties = [...putData.properties, ...keyValuePairs];\r\n      });\r\n      putDatas.push(putData);\r\n      this.$service.editPOCProperties.send(putDatas).then((res) => {\r\n        this.isEdit = false;\r\n        this.loadData();\r\n        this.$store.commit(\"setKmConfigChanged\", moment().valueOf());\r\n      });\r\n    },\r\n    clearData() {\r\n      this.isEdit = false;\r\n    },\r\n    editData() {\r\n      this.isEdit = true;\r\n    },\r\n    normalizeHeight() {\r\n      setTimeout(\r\n        function () {\r\n          const modal = document.querySelector(\".ivu-modal-body .content\");\r\n          if (modal) {\r\n            modal.style.height = \"250px\";\r\n          }\r\n        }.bind(this),\r\n        50\r\n      );\r\n    },\r\n    dynamicSort(property) {\r\n      var sortOrder = 1;\r\n      if (property[0] === \"-\") {\r\n        sortOrder = -1;\r\n        property = property.substr(1);\r\n      }\r\n      return function (a, b) {\r\n        var result =\r\n          a[property] < b[property] ? -1 : a[property] > b[property] ? 1 : 0;\r\n        return result * sortOrder;\r\n      };\r\n    },\r\n    refreshBtn() {\r\n      this.getSystemAbout();\r\n    },\r\n    getSystemAbout() {\r\n      let _this = this;\r\n      _this.systemInfo.push({\r\n        name: this.$t(\"LocaleString.S00002\"),\r\n        version: Config.WEB_VERSION,\r\n      });\r\n    },\r\n    computedDate(val) {\r\n      if (val) {\r\n        return moment(val).format(\"YYYY-MM-DD\");\r\n      }\r\n      return \"\";\r\n    },\r\n    computedDateTime(val) {\r\n      if (val) {\r\n        return moment(val).format(\"YYYY-MM-DD HH:mm:ss\");\r\n      }\r\n      return \"\";\r\n    },\r\n    cancel() {\r\n      this.isShow = false;\r\n      setTimeout(() => {\r\n        this.$emit(\"closeKMConfig\");\r\n      }, 500);\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n.about-data-modal {\r\n  // .vertical-center-modal {\r\n  //   display: flex;\r\n  //   align-items: center;\r\n  //   justify-content: center;\r\n  //   .ivu-modal {\r\n  //     top: 0;\r\n  //   }\r\n  // }\r\n  .ivu-modal-close {\r\n    top: 15px;\r\n    right: 20px;\r\n  }\r\n  .content {\r\n    padding: 4px 14px;\r\n    overflow-y: auto;\r\n    //height: 700px;\r\n    //height: 400px;\r\n  }\r\n\r\n  @media only screen and (min-width: 1200px) {\r\n    .ivu-modal-close {\r\n      top: 18px;\r\n    }\r\n  }\r\n  .ivu-modal-header {\r\n    padding: 15px 20px;\r\n    border-bottom: 1px solid #f7f7f7;\r\n    .header {\r\n      overflow: hidden;\r\n      .left {\r\n        float: left;\r\n        width: 6px;\r\n        height: 24px;\r\n        margin-right: 8px;\r\n        background-color: #0ac7c0;\r\n        border-radius: 3px;\r\n      }\r\n      .title {\r\n        float: left;\r\n        height: 24px;\r\n        line-height: 24px;\r\n        font-size: 14px;\r\n        font-weight: bold;\r\n        color: #999;\r\n        span {\r\n          color: #333;\r\n        }\r\n      }\r\n      .es-btn {\r\n        float: right;\r\n        margin-right: 35px;\r\n      }\r\n      @media only screen and (min-width: 1200px) {\r\n        .left {\r\n          height: 30px;\r\n        }\r\n        .title {\r\n          height: 30px;\r\n          line-height: 30px;\r\n          font-size: 18px;\r\n        }\r\n      }\r\n    }\r\n  }\r\n  .ivu-modal-body {\r\n    .content {\r\n      padding: 4px 14px;\r\n      overflow-y: auto;\r\n      .row {\r\n        margin-bottom: 20px;\r\n        .item {\r\n          padding: 15px;\r\n          border: 1px solid #f7f7f7;\r\n          border-radius: 4px;\r\n          label {\r\n            color: #999;\r\n            font-weight: bold;\r\n          }\r\n          p {\r\n            margin-top: 10px;\r\n            color: #333;\r\n          }\r\n        }\r\n      }\r\n      .form-item {\r\n        padding: 15px 15px 22px;\r\n        margin-bottom: 16px;\r\n        border-radius: 4px;\r\n        background-color: #f7f7f7;\r\n      }\r\n    }\r\n  }\r\n  .ivu-modal-footer {\r\n    border-top: none;\r\n    padding: 0;\r\n  }\r\n}\r\n</style>\r\n<style lang=\"less\" scoped>\r\n/deep/ .ivu-btn-primary {\r\n  color: #fff;\r\n  background-color: #31babb;\r\n  border-color: #31babb;\r\n}\r\n\r\n/deep/ .ivu-btn-primary:hover {\r\n  color: #fff !important;\r\n  border-color: #31babb;\r\n}\r\n\r\n/deep/ .ivu-switch-checked {\r\n  background-color: #31babb;\r\n  border-color: #31babb;\r\n}\r\n.musicIcon {\r\n  vertical-align: middle;\r\n  margin-left: 5px;\r\n  color: #31babb;\r\n  cursor: pointer;\r\n}\r\n</style>\r\n"]}]}