{"remainingRequest": "E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\src\\components\\administrative\\apps\\sns\\monitoring\\m6\\trajectories\\searchModal.vue?vue&type=style&index=0&id=f30d4e86&lang=scss&scoped=true&", "dependencies": [{"path": "E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\src\\components\\administrative\\apps\\sns\\monitoring\\m6\\trajectories\\searchModal.vue", "mtime": 1754362736682}, {"path": "E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKDQovZGVlcC8gLml2dS1idG4tc3VjY2VzcyB7DQogIGNvbG9yOiAjZmZmOw0KICBiYWNrZ3JvdW5kLWNvbG9yOiAjMzFiYWJiOw0KICBib3JkZXItY29sb3I6ICMzMWJhYmI7DQp9DQoNCi9kZWVwLyAuaXZ1LWJ0bi1zdWNjZXNzOmhvdmVyIHsNCiAgY29sb3I6ICNmZmYgIWltcG9ydGFudDsNCiAgYm9yZGVyLWNvbG9yOiAjMzFiYWJiOw0KfQ0K"}, {"version": 3, "sources": ["searchModal.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAobA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA", "file": "searchModal.vue", "sourceRoot": "src/components/administrative/apps/sns/monitoring/m6/trajectories", "sourcesContent": ["<template>\r\n  <div>\r\n    <Row style=\"margin: 15px 0px\">\r\n      {{ openModal }}\r\n      <Col span=\"24\" style=\"text-align: left\" v-if=\"!searchFirstTime\">\r\n      <span style=\"padding-left: 5px\" v-if=\"searchFormValidateSearch.objectName != '' ||\r\n        searchFormValidateSearch.startsAt != '' ||\r\n        searchFormValidateSearch.finishesAt != '' ||\r\n        searchFormValidateSearch.stationName != ''\r\n        \">{{ $t(\"LocaleString.L00262\") }}&nbsp;:</span>\r\n      <span style=\"padding-left: 10px; font-weight: bolder\" v-if=\"searchFormValidateSearch.startsAt != ''\">{{\r\n        $t(\"LocaleString.L00141\") + '(' + parseTime(searchFormValidateSearch.startsAt) + ') ' }}</span>\r\n      <span style=\"padding-left: 10px; font-weight: bolder\" v-if=\"searchFormValidateSearch.finishesAt != ''\">{{\r\n        $t(\"LocaleString.L00142\") + '(' + parseTime(searchFormValidateSearch.finishesAt) + ') ' }}</span>\r\n      <span style=\"padding-left: 10px; font-weight: bolder\" v-if=\"searchFormValidateSearch.objectName != ''\">\r\n        <Tooltip v-if=\"searchFormValidateSearch.objectName.length > 1\"> {{\r\n        $t(\"LocaleString.L00092\") + '(' +\r\n        translateCondition(searchFormValidateSearch.objectName, 'objectName') + ') ' }}\r\n          <div slot=\"content\">\r\n            <p v-for=\"(item, index) in searchFormValidateSearch.objectName\" v-bind:key=\"index\">\r\n              {{ \"• \" + objectMenu.find(i => i.code == item).name }}\r\n            </p>\r\n          </div>\r\n        </Tooltip>\r\n        <span v-else>{{\r\n        $t(\"LocaleString.L00092\") + '(' + translateCondition(searchFormValidateSearch.objectName, 'objectName') +\r\n        ')' }}</span>\r\n      </span>\r\n      <span style=\"padding-left: 10px; font-weight: bolder\" v-if=\"searchFormValidateSearch.stationName != ''\">\r\n        <Tooltip v-if=\"searchFormValidateSearch.stationName.length > 1\">{{\r\n        $t(\"LocaleString.L00161\") + '(' + translateCondition(searchFormValidateSearch.stationName, 'stationName') +\r\n        ')' }}\r\n          <div slot=\"content\">\r\n            <p v-for=\"(item, index) in searchFormValidateSearch.stationName\" v-bind:key=\"index\">\r\n              {{ \"• \" + stationMenu.find(i => i.sid == item).name }}\r\n            </p>\r\n          </div>\r\n        </Tooltip>\r\n        <span v-else>{{\r\n        $t(\"LocaleString.L00161\") + '(' + translateCondition(searchFormValidateSearch.stationName, 'stationName') +\r\n        ')' }}</span>\r\n      </span>\r\n      <span style=\"padding-left: 10px; font-weight: bolder\">{{\r\n        $t(\"LocaleString.L30170\") + '(' + translateCondition(searchFormValidateSearch.noSignalRecord, 'noSignalRecord')\r\n        +\r\n        ')' }}</span>\r\n      <!-- <Button ghost shape=\"circle\" style=\"width: 20px; margin-left: 10px\" @click=\"handleReset('searchFormValidate')\"\r\n        v-if=\"searchFormValidateSearch.objectName != '' ||\r\n        searchFormValidateSearch.stationName != '' ||\r\n        searchFormValidateSearch.startsAt != '' ||\r\n        searchFormValidateSearch.finishesAt != ''\r\n        \"><img :src=\"resetIcon\" style=\"width: 30.4px; margin-left: -14.8px\" /></Button> -->\r\n      </Col>\r\n      <Col span=\"3\" :offset=\"!searchFirstTime ? 0 : 21\" style=\"text-align: right\">\r\n      <!-- <Button ghost shape=\"circle\" style=\"width: 20px\" @click=\"openSearchObject\"><img :src=\"searchIcon\"\r\n          style=\"width: 30.4px; margin-left: -14.8px\" /></Button>\r\n      <Button type=\"success\" icon=\"ios-download-outline\" @click=\"exportHandleSubmit()\">{{ $t(\"LocaleString.B00009\")\r\n        }}</Button> -->\r\n      </Col>\r\n    </Row>\r\n    <Modal v-model=\"modalSearch\" :closable=\"false\" :mask-closable=\"false\">\r\n      <Form ref=\"searchFormValidate\" :model=\"searchFormValidate\" :rules=\"searchRuleValidate\" label-position=\"top\">\r\n        <Row :class=\"rowHeight\" :gutter=\"16\">\r\n          <Col span=\"12\">\r\n          <FormItem class=\"search-form-item\" :label=\"$t('LocaleString.L00141')\" prop=\"startsAt\">\r\n            <DatePicker type=\"datetime\" v-model.trim=\"searchFormValidate.startsAt\" :placeholder=\"$t('LocaleString.M00010', {\r\n        0: $t('LocaleString.L00141'),\r\n      })\r\n        \" :transfer=\"true\" @on-change=\"startsAtChange()\"></DatePicker>\r\n          </FormItem>\r\n          </Col>\r\n          <Col span=\"12\">\r\n          <FormItem class=\"search-form-item\" :label=\"$t('LocaleString.L00142')\" prop=\"finishesAt\">\r\n            <DatePicker type=\"datetime\" v-model.trim=\"searchFormValidate.finishesAt\" :placeholder=\"$t('LocaleString.M00010', {\r\n        0: $t('LocaleString.L00142'),\r\n      })\r\n        \" :transfer=\"true\" @on-change=\"finishesAtChange()\"></DatePicker>\r\n          </FormItem>\r\n          </Col>\r\n          <Col span=\"12\">\r\n          <FormItem class=\"search-form-item\" :label=\"$t('LocaleString.L00092')\" prop=\"objectName\">\r\n            <Select :placeholder=\"$t('LocaleString.D00001')\" v-model=\"searchFormValidate.objectName\" :transfer=\"true\"\r\n              filterable multiple :not-found-text=\"notFoundText\" @on-change=\"changeObject\" :max-tag-count=\"1\">\r\n              <Option value=\"all\" :disabled=\"diabledObjectNameAll\">{{ $t(\"LocaleString.D00002\") }}</Option>\r\n              <Option v-for=\"item in objectMenu\" :value=\"item.code\" :key=\"item.code\" :disabled=\"item.disabled\">{{\r\n        item.name }}</Option>\r\n            </Select>\r\n          </FormItem>\r\n          </Col>\r\n          <Col span=\"12\">\r\n          <FormItem class=\"search-form-item\" :label=\"$t('LocaleString.L00161')\" prop=\"stationName\">\r\n            <Select :placeholder=\"$t('LocaleString.D00001')\" v-model=\"searchFormValidate.stationName\" :transfer=\"true\"\r\n              filterable multiple :not-found-text=\"notFoundText\" @on-change=\"changeStation\" :max-tag-count=\"1\">\r\n              <Option value=\"all\" :disabled=\"diabledStationNameAll\">{{ $t(\"LocaleString.D00002\") }}</Option>\r\n              <Option v-for=\"item in stationMenu\" :value=\"item.sid\" :key=\"item.sid\" :disabled=\"item.disabled\">{{\r\n        item.name }}</Option>\r\n            </Select>\r\n          </FormItem>\r\n          </Col>\r\n          <Col span=\"12\">\r\n          <FormItem class=\"search-form-item\" :label=\"$t('LocaleString.L30170')\" prop=\"noSignalRecord\">\r\n            <div>\r\n              <i-switch v-model=\"searchFormValidate.noSignalRecord\" style=\"margin-left: 10px\" />\r\n            </div>\r\n          </FormItem>\r\n          </Col>\r\n        </Row>\r\n      </Form>\r\n      <div slot=\"footer\">\r\n        <div class=\"search-submit\">\r\n          <Button type=\"success\" icon=\"ios-search\" @click=\"searchHandleSubmit('searchFormValidate')\">{{\r\n        $t(\"LocaleString.B20017\") }}</Button>\r\n          <Button @click=\"cancelModal\">{{ $t(\"LocaleString.B00015\") }}</Button>\r\n        </div>\r\n      </div>\r\n    </Modal>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport resetIcon from \"@/assets/images/ic_reset.svg\";\r\nimport searchIcon from \"@/assets/images/ic_search.svg\";\r\nimport Config from \"@/common/config\";\r\nlet moment = require(\"moment\");\r\n\r\nexport default {\r\n  props: [\"serviceCodeMenu\", \"stationMenu\", \"objectMenu\", \"openSearchStatus\"],\r\n  data() {\r\n    const validateObjectName = (rule, value, callback) => {\r\n      let _this = this;\r\n      if (value.length == 0) {\r\n        callback(\r\n          new Error(\r\n            this.$t(\"LocaleString.M00010\", {\r\n              0: this.$t(\"LocaleString.L00092\"),\r\n            })\r\n          )\r\n        );\r\n      }\r\n      else if (value.includes(\"all\") && _this.searchFormValidate.stationName.includes(\"all\")) {\r\n        callback(new Error(\r\n          this.$t(\"LocaleString.W30014\")\r\n        ));\r\n      } else {\r\n        callback()\r\n      }\r\n    };\r\n\r\n    const validateStationName = (rule, value, callback) => {\r\n      let _this = this;\r\n      if (value.length == 0) {\r\n        callback(\r\n          new Error(\r\n            this.$t(\"LocaleString.M00010\", {\r\n              0: this.$t(\"LocaleString.L00161\"),\r\n            })\r\n          )\r\n        );\r\n      } else if (value.includes(\"all\") && _this.searchFormValidate.objectName.includes(\"all\")) {\r\n        callback(new Error(\r\n          this.$t(\"LocaleString.W30014\")\r\n        ));\r\n      } else {\r\n        callback()\r\n      }\r\n    };\r\n    return {\r\n      searchFirstTime: true,\r\n      diabledStationNameAll: false,\r\n      diabledObjectNameAll: false,\r\n      searchStartDate: \"\",\r\n      searchEndDate: \"\",\r\n      modalSearch: false,\r\n      resetIcon: resetIcon,\r\n      searchIcon: searchIcon,\r\n      defaultSelection: [],\r\n      notFoundText: this.$t(\"LocaleString.L00081\"),\r\n      rowHeight: \"row-height\",\r\n      searchFormValidate: {\r\n        objectName: ['all'],\r\n        startsAt: this.getStartsAtDefault(),\r\n        finishesAt: this.getFinishesAtDefault(),\r\n        stationName: ['all'],\r\n        noSignalRecord: false,\r\n      },\r\n      searchFormValidateSearch: null,\r\n      dateOptions: {\r\n        disabledDate(date) {\r\n          let startsAt = new Date(\r\n            Date.now() - 1000 * 60 * 60 * 24 * Config.SEARCH_ENABLE_DATES\r\n          );\r\n          startsAt.setHours(0, 0, 0, 0);\r\n          let finishesAt = new Date(Date.now() + 1000 * 60 * 60 * 24);\r\n          finishesAt.setHours(0, 0, 0, 0);\r\n\r\n          return date && (date < startsAt || date > finishesAt);\r\n        },\r\n      },\r\n      searchRuleValidate:\r\n      {\r\n        startsAt: [\r\n          {\r\n            required: true,\r\n            message: this.$t(\"LocaleString.M00010\", {\r\n              0: this.$t(\"LocaleString.L00141\"),\r\n            }),\r\n          },\r\n          {\r\n            type: \"date\",\r\n            validator: (rule, value) =>\r\n              !value ||\r\n              !this.searchFormValidate.finishesAt ||\r\n              value <= this.searchFormValidate.finishesAt,\r\n            message: this.$t(\"LocaleString.W00036\"),\r\n          },\r\n        ],\r\n        finishesAt: [\r\n          {\r\n            required: true,\r\n            message: this.$t(\"LocaleString.M00010\", {\r\n              0: this.$t(\"LocaleString.L00142\"),\r\n            }),\r\n          },\r\n          {\r\n            type: \"date\",\r\n            validator: (rule, value) =>\r\n              !value ||\r\n              !this.searchFormValidate.startsAt ||\r\n              value >= this.searchFormValidate.startsAt,\r\n            message: this.$t(\"LocaleString.W00036\"),\r\n          },\r\n        ],\r\n        objectName: [\r\n          { validator: validateObjectName, trigger: 'blur' }\r\n        ],\r\n        stationName: [\r\n          { validator: validateStationName, trigger: 'blur' }\r\n        ],\r\n\r\n      }\r\n    };\r\n  },\r\n  computed: {\r\n\r\n    openModal() {\r\n      if (this.openSearchStatus) {\r\n        this.openSearchObject();\r\n      }\r\n      return null;\r\n    }\r\n  },\r\n  created() {\r\n    this.searchFormValidateSearch = JSON.parse(\r\n      JSON.stringify(this.searchFormValidate)\r\n    );\r\n  },\r\n  async mounted() {\r\n  },\r\n  methods: {\r\n    changeStation(data) {\r\n      if (this.stationMenu) {\r\n        if (this.searchFormValidate.stationName.includes('all')) {\r\n          this.stationMenu.forEach(item => { item.disabled = true });\r\n          this.searchFormValidate.stationName = ['all'];\r\n        }\r\n        else {\r\n          this.stationMenu.forEach(item => { item.disabled = false });\r\n          if (data.length > 20) {\r\n            data.pop();\r\n          }\r\n        }\r\n      }\r\n    },\r\n\r\n    changeObject(data) {\r\n      if (this.objectMenu) {\r\n        if (this.searchFormValidate.objectName.includes('all')) {\r\n          this.objectMenu.forEach(item => { item.disabled = true });\r\n          this.searchFormValidate.objectName = ['all'];\r\n        } else {\r\n          this.objectMenu.forEach(item => { item.disabled = false });\r\n          if (data.length > 20) {\r\n            data.pop();\r\n          }\r\n        }\r\n      }\r\n    },\r\n\r\n    parseTime(day) {\r\n      let timedifference = (new Date().getTimezoneOffset() / 60) * -1;\r\n      let tmpOffsetDay = moment(day, \"YYYY-MM-DD HH:mm:ss\")\r\n        .add(timedifference, \"hours\")\r\n        .format(\"YYYY-MM-DD HH:mm:ss\");\r\n      return tmpOffsetDay;\r\n    },\r\n    translateCondition(item, type) {\r\n      if (item == undefined) {\r\n        return '';\r\n      }\r\n      if (item == \"all\") {\r\n        return this.$t(\"LocaleString.D00002\");\r\n      }\r\n\r\n      let translateName = \"\";\r\n      switch (type) {\r\n        case \"noSignalRecord\":\r\n          translateName = item ? this.$t(\"LocaleString.D30029\") : this.$t(\"LocaleString.D30030\");\r\n          break;\r\n        case \"objectName\":\r\n          translateName = item.length > 1 ? this.objectMenu.find(i => i.code == item[0]).name + \" ... \" : this.objectMenu.find(i => i.code == item).name;\r\n          break;\r\n        case \"stationName\":\r\n          translateName = item.length > 1 ? this.stationMenu.find(i => i.sid == item[0]).name + \" ... \" : this.stationMenu.find(i => i.sid == item).name;\r\n          break;\r\n\r\n      }\r\n      return translateName;\r\n    },\r\n    handleReset(name) {\r\n      this.$refs[name].resetFields();\r\n      this.searchFormValidateSearch = this.searchFormValidate;\r\n      this.searchHandleSubmit(\"searchFormValidate\");\r\n    },\r\n    cancelModal() {\r\n      this.$refs[\"searchFormValidate\"].resetFields();\r\n      if (!this.searchFirstTime) {\r\n        let newStartsAt = new Date(this.searchFormValidateSearch.startedTime);\r\n        let newFinishsAt = new Date(this.searchFormValidateSearch.endedTime);\r\n        this.searchFormValidate = JSON.parse(\r\n          JSON.stringify(this.searchFormValidateSearch)\r\n        );\r\n        this.searchFormValidate.startedTime = newStartsAt;\r\n        this.searchFormValidate.endedTime = newFinishsAt;\r\n      }\r\n\r\n      this.modalSearch = false;\r\n      this.$emit(\"closeSearchModal\");\r\n    },\r\n    openSearchObject() {\r\n      this.modalSearch = true;\r\n    },\r\n\r\n    getStartsAtDefault() {\r\n      let result = new Date(\r\n        Date.now() -\r\n        1000 * 3600 * 24 * (Config.SEARCH_POSTGRESQL_DATA_DATES - 1)\r\n      );\r\n      result.setHours(0, 0, 0, 0);\r\n      return result;\r\n    },\r\n    getFinishesAtDefault() {\r\n      let result = new Date(Date.now() + 1000 * 3600 * 24);\r\n      result.setHours(0, 0, 0, 0);\r\n      return result;\r\n    },\r\n    startsAtChange() {\r\n      this.$refs[\"searchFormValidate\"].validateField(\"finishesAt\");\r\n    },\r\n    finishesAtChange() {\r\n      this.$refs[\"searchFormValidate\"].validateField(\"startsAt\");\r\n    },\r\n    searchHandleSubmit(name) {\r\n      this.$refs[name].validate((valid) => {\r\n        if (valid) {\r\n          this.searchFormValidateSearch = JSON.parse(\r\n            JSON.stringify(this.searchFormValidate)\r\n          );\r\n          this.searchFirstTime = false\r\n          this.$emit(\"searchRequest\", {\r\n            isUserSubmit: true,\r\n            searchParams: this.getSearchParams(),\r\n          });\r\n          this.modalSearch = false;\r\n\r\n        }\r\n      });\r\n    },\r\n    getSearchParams() {\r\n      let str = \"\";\r\n\r\n      if (this.searchFormValidate.startsAt !== \"\" && this.searchFormValidate.finishesAt !== \"\") {\r\n        let timedifferenceStartDay =\r\n          (new Date().getTimezoneOffset() / 60) * -1 - 8;\r\n        let tmpOffsetStartDay = moment(\r\n          this.searchFormValidate.startsAt,\r\n          \"YYYY-MM-DD HH:mm:ss\"\r\n        )\r\n          .add(timedifferenceStartDay * -1, \"hours\")\r\n          .format(\"YYYY-MM-DD HH:mm:ss\");\r\n\r\n        let startsAt = tmpOffsetStartDay;\r\n\r\n        let timedifferencefinishDay =\r\n          (new Date().getTimezoneOffset() / 60) * -1 - 8;\r\n        let tmpOffsetFinishDay = moment(\r\n          this.searchFormValidate.finishesAt,\r\n          \"YYYY-MM-DD HH:mm:ss\"\r\n        )\r\n          .add(timedifferencefinishDay * -1, \"hours\")\r\n          .format(\"YYYY-MM-DD HH:mm:ss\");\r\n        let finishesAt = moment(tmpOffsetFinishDay).format(\r\n          \"YYYY-MM-DD HH:mm:ss\"\r\n        );\r\n\r\n        str += \"positionTime between '\" + startsAt + \",\" + finishesAt + \"' \";\r\n        this.searchStartDate = startsAt;\r\n        this.searchEndDate = finishesAt;\r\n      }\r\n\r\n      if (this.searchFormValidate.objectName.length > 0 && !this.searchFormValidate.objectName.includes(\"all\")) {\r\n        str +=\r\n          \" and object.code in \" +\r\n          this.searchFormValidate.objectName.join(\",\") +\r\n          \" \";\r\n      }\r\n      if (this.searchFormValidate.stationName.length > 0 && !this.searchFormValidate.stationName.includes(\"all\")) {\r\n        str +=\r\n          \" and toStation.sid in \" +\r\n          this.searchFormValidate.stationName.join(\",\") +\r\n          \" \";\r\n      }\r\n\r\n      if (!this.searchFormValidate.noSignalRecord) {\r\n        str +=\r\n          \" and toStation.sid ne offline\";\r\n      }\r\n      return { search: str };\r\n    },\r\n    exportHandleSubmit() {\r\n      this.$emit(\"exportConfirm\");\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n/deep/ .ivu-btn-success {\r\n  color: #fff;\r\n  background-color: #31babb;\r\n  border-color: #31babb;\r\n}\r\n\r\n/deep/ .ivu-btn-success:hover {\r\n  color: #fff !important;\r\n  border-color: #31babb;\r\n}\r\n</style>\r\n"]}]}