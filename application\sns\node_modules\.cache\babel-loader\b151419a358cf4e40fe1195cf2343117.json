{"remainingRequest": "E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\node_modules\\babel-loader\\lib\\index.js!E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\node_modules\\string-replace-loader\\index.js??ref--13-0!E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\src\\components\\administrative\\apps\\sns\\planeSetting\\sitePlan\\pointDrag.js", "dependencies": [{"path": "E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\src\\components\\administrative\\apps\\sns\\planeSetting\\sitePlan\\pointDrag.js", "mtime": 1754362736893}, {"path": "E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\node_modules\\string-replace-loader\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["E:/GitRepos/FusionNetProject/6849/frontend/application/sns/src/components/administrative/apps/sns/planeSetting/sitePlan/pointDrag.js"], "names": ["dragDiv", "box", "dragContainer", "callBack", "oBox", "document", "getElementById", "pointDrag", "drag", "style", "cursor", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "onmousedown", "e", "ev", "window", "event", "oldLeft", "offsetLeft", "oldTop", "offsetTop", "oldX", "clientX", "oldY", "clientY", "<PERSON><PERSON><PERSON><PERSON>", "mouseX", "mouseY", "newX", "newY", "left", "top", "parseInt", "clientWidth", "clientHeight", "onmouseup", "x", "formatting", "y", "id", "src", "pos", "varx", "Math", "round", "pow", "floor"], "mappings": "AAAA,IAAIA,OAAO,GAAG,SAAVA,OAAU,CAAUC,GAAV,EAAeC,aAAf,EAA8BC,QAA9B,EAAwC;AACpD,MAAIC,IAAI,GAAGC,QAAQ,CAACC,cAAT,CAAwBL,GAAxB,CAAX;AACA,MAAIM,SAAS,GAAG;AACdL,IAAAA,aAAa,EAAE,EADD;AAEdM,IAAAA,IAFc,gBAERN,aAFQ,EAEO;AACnB,WAAKA,aAAL,GAAqBG,QAAQ,CAACC,cAAT,CAAwBJ,aAAxB,CAArB;AACA,WAAKA,aAAL,CAAmBO,KAAnB,CAAyBC,MAAzB,GAAkC,SAAlC;AACAH,MAAAA,SAAS,CAACI,WAAV;AACD,KANa;AAOdA,IAAAA,WAPc,yBAOC;AAAA;;AACbN,MAAAA,QAAQ,CAACC,cAAT,CAAwBJ,aAAxB,EAAuCU,WAAvC,GAAqD,UAACC,CAAD,EAAO;AAC1D,YAAIC,EAAE,GAAGD,CAAC,IAAIE,MAAM,CAACC,KAArB;AACA,YAAIC,OAAO,GAAG,KAAI,CAACf,aAAL,CAAmBgB,UAAjC;AACA,YAAIC,MAAM,GAAG,KAAI,CAACjB,aAAL,CAAmBkB,SAAhC;AACA,YAAIC,IAAI,GAAGP,EAAE,CAACQ,OAAd;AACA,YAAIC,IAAI,GAAGT,EAAE,CAACU,OAAd;;AACAnB,QAAAA,QAAQ,CAACoB,WAAT,GAAuB,UAACZ,CAAD,EAAO;AAC5B,cAAIC,EAAE,GAAGD,CAAC,IAAIE,MAAM,CAACC,KAArB;AACA,cAAIU,MAAM,GAAGZ,EAAE,CAACQ,OAAhB;AACA,cAAIK,MAAM,GAAGb,EAAE,CAACU,OAAhB;AACA,cAAII,IAAI,GAAGF,MAAM,GAAGL,IAAT,GAAgBJ,OAA3B;AACA,cAAIY,IAAI,GAAGF,MAAM,GAAGJ,IAAT,GAAgBJ,MAA3B;AACA,UAAA,KAAI,CAACjB,aAAL,CAAmBO,KAAnB,CAAyBqB,IAAzB,GAAgCF,IAAI,GAAG,IAAvC;AACA,UAAA,KAAI,CAAC1B,aAAL,CAAmBO,KAAnB,CAAyBsB,GAAzB,GAA+BF,IAAI,GAAG,IAAtC;;AAEA,cAAIG,QAAQ,CAAC,KAAI,CAAC9B,aAAL,CAAmBO,KAAnB,CAAyBqB,IAA1B,CAAR,GAA0C,CAA9C,EAAiD;AAC/C,YAAA,KAAI,CAAC5B,aAAL,CAAmBO,KAAnB,CAAyBqB,IAAzB,GAAgC,CAAhC;AACD;;AACD,cAAIE,QAAQ,CAAC,KAAI,CAAC9B,aAAL,CAAmBO,KAAnB,CAAyBsB,GAA1B,CAAR,GAAyC,CAA7C,EAAgD;AAC9C,YAAA,KAAI,CAAC7B,aAAL,CAAmBO,KAAnB,CAAyBsB,GAAzB,GAA+B,CAA/B;AACD;;AACD,cAAIC,QAAQ,CAAC,KAAI,CAAC9B,aAAL,CAAmBO,KAAnB,CAAyBqB,IAA1B,CAAR,GAA0C1B,IAAI,CAAC6B,WAAL,GAAmB,KAAI,CAAC/B,aAAL,CAAmB+B,WAAtC,GAAoD,EAAlG,EAAsG;AACpG,YAAA,KAAI,CAAC/B,aAAL,CAAmBO,KAAnB,CAAyBqB,IAAzB,GAAiC1B,IAAI,CAAC6B,WAAL,GAAmB,KAAI,CAAC/B,aAAL,CAAmB+B,WAAvC,GAAsD,IAAtF;AACD;;AACD,cAAID,QAAQ,CAAC,KAAI,CAAC9B,aAAL,CAAmBO,KAAnB,CAAyBsB,GAA1B,CAAR,GAAyC3B,IAAI,CAAC8B,YAAL,GAAoB,KAAI,CAAChC,aAAL,CAAmBgC,YAAvC,GAAqD,EAAlG,EAAsG;AACpG,YAAA,KAAI,CAAChC,aAAL,CAAmBO,KAAnB,CAAyBsB,GAAzB,GAAgC3B,IAAI,CAAC8B,YAAL,GAAoB,KAAI,CAAChC,aAAL,CAAmBgC,YAAxC,GAAwD,IAAvF;AACD;AACF,SArBD;;AAsBA7B,QAAAA,QAAQ,CAAC8B,SAAT,GAAqB,UAACtB,CAAD,EAAO;AAC1BR,UAAAA,QAAQ,CAACoB,WAAT,GAAuB,IAAvB;AACApB,UAAAA,QAAQ,CAAC8B,SAAT,GAAqB,IAArB;AAEAhC,UAAAA,QAAQ,CAAC;AACPiC,YAAAA,CAAC,EAAE7B,SAAS,CAAC8B,UAAV,CAAqB,KAAI,CAACnC,aAAL,CAAmBgB,UAAxC,EAAoD,CAApD,CADI;AAEPoB,YAAAA,CAAC,EAAE/B,SAAS,CAAC8B,UAAV,CAAqB,KAAI,CAACnC,aAAL,CAAmBkB,SAAxC,EAAmD,CAAnD,CAFI;AAGP;AACA;AACAmB,YAAAA,EAAE,EAAE,KAAI,CAACrC,aAAL,CAAmBqC;AALhB,WAAD,CAAR;AAOD,SAXD;AAYD,OAxCD;AAyCD,KAjDa;AAkDdF,IAAAA,UAlDc,sBAkDFG,GAlDE,EAkDGC,GAlDH,EAkDQ;AACpB,UAAIC,IAAI,GAAGC,IAAI,CAACC,KAAL,CAAWJ,GAAG,GAAGG,IAAI,CAACE,GAAL,CAAS,EAAT,EAAaJ,GAAb,CAAjB,IAAsCE,IAAI,CAACE,GAAL,CAAS,EAAT,EAAaJ,GAAb,CAAjD;AACA,aAAOE,IAAI,CAACG,KAAL,CAAWJ,IAAI,GAAG,GAAlB,IAAyB,GAAhC,CAFoB,CAGpB;AACD;AAtDa,GAAhB;AAwDAnC,EAAAA,SAAS,CAACC,IAAV,CAAeN,aAAf;AACD,CA3DD;;AA4DA,eAAeF,OAAf", "sourcesContent": ["let dragDiv = function (box, dragContainer, callBack) {\r\n  let oBox = document.getElementById(box)\r\n  let pointDrag = {\r\n    dragContainer: '',\r\n    drag (dragContainer) {\r\n      this.dragContainer = document.getElementById(dragContainer)\r\n      this.dragContainer.style.cursor = 'pointer'\r\n      pointDrag.DragHandler()\r\n    },\r\n    Drag<PERSON><PERSON><PERSON> () {\r\n      document.getElementById(dragContainer).onmousedown = (e) => {\r\n        let ev = e || window.event\r\n        let oldLeft = this.dragContainer.offsetLeft\r\n        let oldTop = this.dragContainer.offsetTop\r\n        let oldX = ev.clientX \r\n        let oldY = ev.clientY \r\n        document.onmousemove = (e) => {\r\n          let ev = e || window.event\r\n          let mouseX = ev.clientX\r\n          let mouseY = ev.clientY\r\n          let newX = mouseX - oldX + oldLeft \r\n          let newY = mouseY - oldY + oldTop \r\n          this.dragContainer.style.left = newX + 'px'\r\n          this.dragContainer.style.top = newY + 'px'\r\n\r\n          if (parseInt(this.dragContainer.style.left) < 0) {\r\n            this.dragContainer.style.left = 0\r\n          }\r\n          if (parseInt(this.dragContainer.style.top) < 0) {\r\n            this.dragContainer.style.top = 0\r\n          }\r\n          if (parseInt(this.dragContainer.style.left) > oBox.clientWidth - this.dragContainer.clientWidth + 30) {\r\n            this.dragContainer.style.left = (oBox.clientWidth - this.dragContainer.clientWidth) + 'px'\r\n          }\r\n          if (parseInt(this.dragContainer.style.top) > oBox.clientHeight - this.dragContainer.clientHeight +30) {\r\n            this.dragContainer.style.top = (oBox.clientHeight - this.dragContainer.clientHeight) + 'px'\r\n          }\r\n        }\r\n        document.onmouseup = (e) => {\r\n          document.onmousemove = null\r\n          document.onmouseup = null\r\n          \r\n          callBack({\r\n            x: pointDrag.formatting(this.dragContainer.offsetLeft, 2),\r\n            y: pointDrag.formatting(this.dragContainer.offsetTop, 2),\r\n            // x: Math.floor(this.dragContainer.offsetLeft * 100) / 100 ,     \r\n            // y: Math.floor(this.dragContainer.offsetTop * 100) / 100 ,                 \r\n            id: this.dragContainer.id\r\n          })\r\n        }\r\n      }\r\n    },\r\n    formatting (src, pos) {\r\n      let varx = Math.round(src * Math.pow(10, pos)) / Math.pow(10, pos)\r\n      return Math.floor(varx * 100) / 100\r\n      // return Math.round(src * Math.pow(10, pos)) / Math.pow(10, pos)\r\n    }\r\n  }\r\n  pointDrag.drag(dragContainer)\r\n}\r\nexport default dragDiv\r\n\r\n\r\n"]}]}