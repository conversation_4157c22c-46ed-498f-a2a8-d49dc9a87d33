{"remainingRequest": "E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\src\\components\\administrative\\apps\\sns\\monitoring\\m6\\task\\searchModal.vue?vue&type=template&id=5fed9bdd&scoped=true&", "dependencies": [{"path": "E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\src\\components\\administrative\\apps\\sns\\monitoring\\m6\\task\\searchModal.vue", "mtime": 1754362736681}, {"path": "E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}]}