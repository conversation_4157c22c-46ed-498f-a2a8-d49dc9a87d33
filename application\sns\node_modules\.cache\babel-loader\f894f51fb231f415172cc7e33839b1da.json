{"remainingRequest": "E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\node_modules\\babel-loader\\lib\\index.js!E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\node_modules\\string-replace-loader\\index.js??ref--13-0!E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\src\\components\\administrative\\apps\\sns\\monitoring\\m6\\physiological\\searchModal.vue?vue&type=script&lang=js&", "dependencies": [{"path": "E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\src\\components\\administrative\\apps\\sns\\monitoring\\m6\\physiological\\searchModal.vue", "mtime": 1754362736680}, {"path": "E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\node_modules\\string-replace-loader\\index.js", "mtime": 499162500000}, {"path": "E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["searchModal.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2PA,OAAA,SAAA,MAAA,8BAAA;AACA,OAAA,UAAA,MAAA,+BAAA;AACA,OAAA,MAAA,MAAA,iBAAA;;AACA,IAAA,MAAA,GAAA,OAAA,CAAA,QAAA,CAAA;;AACA,eAAA;AACA,EAAA,KAAA,EAAA,CACA,YADA,EAEA,YAFA,EAGA,gBAHA,EAIA,kBAJA,EAKA,gBALA,CADA;AAQA,EAAA,IARA,kBAQA;AACA,WAAA;AACA,MAAA,YAAA,EAAA,CACA;AAAA,QAAA,GAAA,EAAA,QAAA;AAAA,QAAA,KAAA,EAAA,KAAA,EAAA,CAAA,qBAAA;AAAA,OADA,EAEA;AAAA,QAAA,GAAA,EAAA,MAAA;AAAA,QAAA,KAAA,EAAA,KAAA,EAAA,CAAA,qBAAA;AAAA,OAFA,EAGA;AAAA,QAAA,GAAA,EAAA,MAAA;AAAA,QAAA,KAAA,EAAA,KAAA,EAAA,CAAA,qBAAA;AAAA,OAHA,EAIA;AAAA,QAAA,GAAA,EAAA,KAAA;AAAA,QAAA,KAAA,EAAA,KAAA,EAAA,CAAA,qBAAA;AAAA,OAJA,CADA;AAOA,MAAA,mBAAA,EAAA,EAPA;AAQA,MAAA,eAAA,EAAA,EARA;AASA,MAAA,aAAA,EAAA,EATA;AAUA,MAAA,eAAA,EAAA,IAVA;AAWA,MAAA,WAAA,EAAA,KAXA;AAYA,MAAA,SAAA,EAAA,SAZA;AAaA,MAAA,UAAA,EAAA,UAbA;AAcA,MAAA,gBAAA,EAAA,EAdA;AAeA,MAAA,cAAA,EAAA,EAfA;AAgBA,MAAA,gBAAA,EAAA,EAhBA;AAiBA,MAAA,oBAAA,EAAA,EAjBA;AAkBA,MAAA,2BAAA,EAAA,EAlBA;AAmBA,MAAA,YAAA,EAAA,KAAA,EAAA,CAAA,qBAAA,CAnBA;AAoBA,MAAA,SAAA,EAAA,YApBA;AAqBA,MAAA,kBAAA,EAAA;AACA,QAAA,UAAA,EAAA,KAAA,gBAAA,IAAA,EAAA,GAAA,KAAA,gBAAA,GAAA,EADA;AAEA,QAAA,UAAA,EAAA,EAFA;AAGA,QAAA,UAAA,EAAA,KAAA,cAAA,IAAA,EAAA,GAAA,KAAA,cAAA,GAAA,EAHA;AAIA,QAAA,WAAA,EAAA,KAJA;AAKA,QAAA,WAAA,EAAA,KAAA,qBAAA,EALA;AAMA,QAAA,SAAA,EAAA,KAAA,mBAAA,EANA;AAOA,QAAA,QAAA,EAAA,CAPA;AAQA,QAAA,QAAA,EAAA;AARA,OArBA;AA+BA,MAAA,wBAAA,EAAA,IA/BA;AAgCA,MAAA,WAAA,EAAA;AACA,QAAA,YADA,wBACA,IADA,EACA;AACA,cAAA,WAAA,GAAA,IAAA,IAAA,CACA,IAAA,CAAA,GAAA,KAAA,OAAA,EAAA,GAAA,EAAA,GAAA,EAAA,GAAA,MAAA,CAAA,mBADA,CAAA;AAGA,UAAA,WAAA,CAAA,QAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA,CAAA;AACA,cAAA,SAAA,GAAA,IAAA,IAAA,CAAA,IAAA,CAAA,GAAA,KAAA,OAAA,EAAA,GAAA,EAAA,GAAA,EAAA,CAAA;AACA,UAAA,SAAA,CAAA,QAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA,CAAA;AAEA,iBAAA,IAAA,KAAA,IAAA,GAAA,WAAA,IAAA,IAAA,GAAA,SAAA,CAAA;AACA;AAVA;AAhCA,KAAA;AA6CA,GAtDA;AAuDA,EAAA,QAAA,EAAA;AACA,IAAA,kBADA,gCACA;AAAA;;AACA,aAAA;AACA,QAAA,UAAA,EAAA;AACA,UAAA,QAAA,EAAA,IADA;AAEA,UAAA,OAAA,EAAA,KAAA,EAAA,CAAA,qBAAA,EAAA;AACA,eAAA,KAAA,EAAA,CAAA,qBAAA;AADA,WAAA;AAFA,SADA;AAOA,QAAA,UAAA,EAAA;AACA,UAAA,QAAA,EAAA,IADA;AAEA,UAAA,OAAA,EAAA,KAAA,EAAA,CAAA,qBAAA,EAAA;AACA,eAAA,KAAA,EAAA,CAAA,qBAAA;AADA,WAAA;AAFA,SAPA;AAaA,QAAA,WAAA,EAAA,CACA;AACA,UAAA,QAAA,EAAA,IADA;AAEA,UAAA,OAAA,EAAA,KAAA,EAAA,CAAA,qBAAA,EAAA;AACA,eAAA,KAAA,EAAA,CAAA,qBAAA;AADA,WAAA;AAFA,SADA,EAOA;AACA,UAAA,IAAA,EAAA,MADA;AAEA,UAAA,SAAA,EAAA,mBAAA,IAAA,EAAA,KAAA;AAAA,mBACA,CAAA,KAAA,IACA,CAAA,MAAA,CAAA,kBAAA,CAAA,SADA,IAEA,KAAA,IAAA,MAAA,CAAA,kBAAA,CAAA,SAHA;AAAA,WAFA;AAMA,UAAA,OAAA,EAAA,KAAA,EAAA,CAAA,qBAAA;AANA,SAPA,CAbA;AA6BA,QAAA,SAAA,EAAA,CACA;AACA,UAAA,QAAA,EAAA,IADA;AAEA,UAAA,OAAA,EAAA,KAAA,EAAA,CAAA,qBAAA,EAAA;AACA,eAAA,KAAA,EAAA,CAAA,qBAAA;AADA,WAAA;AAFA,SADA,EAOA;AACA,UAAA,IAAA,EAAA,MADA;AAEA,UAAA,SAAA,EAAA,mBAAA,IAAA,EAAA,KAAA;AAAA,mBACA,CAAA,KAAA,IACA,CAAA,MAAA,CAAA,kBAAA,CAAA,WADA,IAEA,KAAA,IAAA,MAAA,CAAA,kBAAA,CAAA,WAHA;AAAA,WAFA;AAMA,UAAA,OAAA,EAAA,KAAA,EAAA,CAAA,qBAAA;AANA,SAPA;AA7BA,OAAA;AA8CA;AAhDA,GAvDA;AAyGA,EAAA,OAzGA,qBAyGA;AACA,SAAA,wBAAA,GAAA,IAAA,CAAA,KAAA,CACA,IAAA,CAAA,SAAA,CAAA,KAAA,kBAAA,CADA,CAAA;AAGA,GA7GA;AA8GA,EAAA,OA9GA,qBA8GA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,qBACA,MAAA,CAAA,oBAAA,EADA;;AAAA;AAAA;AAAA,qBAEA,MAAA,CAAA,aAAA,EAFA;;AAAA;AAGA,cAAA,MAAA,CAAA,qBAAA;;AACA,cAAA,MAAA,CAAA,oBAAA;;AAEA,kBAAA,MAAA,CAAA,cAAA,IAAA,EAAA,IAAA,MAAA,CAAA,gBAAA,IAAA,EAAA,EAAA;AACA,gBAAA,MAAA,CAAA,gBAAA;;AACA,gBAAA,MAAA,CAAA,kBAAA,CAAA,UAAA,GAAA,MAAA,CAAA,cAAA;;AACA,gBAAA,MAAA,CAAA,kBAAA,CAAA,oBAAA;AACA;;AAVA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAWA,GAzHA;AA0HA,EAAA,OAAA,EAAA;AACA,IAAA,SADA,qBACA,GADA,EACA;AACA,UAAA,cAAA,GAAA,IAAA,IAAA,GAAA,iBAAA,KAAA,EAAA,GAAA,CAAA,CAAA;AACA,UAAA,YAAA,GAAA,MAAA,CAAA,GAAA,EAAA,qBAAA,CAAA,CACA,GADA,CACA,cADA,EACA,OADA,EAEA,MAFA,CAEA,qBAFA,CAAA;AAGA,aAAA,YAAA;AACA,KAPA;AAQA,IAAA,kBARA,8BAQA,IARA,EAQA,IARA,EAQA;AACA,UAAA,IAAA,IAAA,SAAA,EAAA;AACA,eAAA,EAAA;AACA;;AACA,UAAA,IAAA,IAAA,KAAA,EAAA;AACA,eAAA,KAAA,EAAA,CAAA,qBAAA,CAAA;AACA;;AAEA,UAAA,aAAA,GAAA,EAAA;;AACA,cAAA,IAAA;AACA,aAAA,UAAA;AACA,UAAA,aAAA,GAAA,KAAA,YAAA,CAAA,IAAA,CAAA,UAAA,IAAA;AAAA,mBAAA,IAAA,CAAA,GAAA,IAAA,IAAA;AAAA,WAAA,EACA,KADA;AAEA;;AACA,aAAA,YAAA;AACA,UAAA,aAAA,GAAA,KAAA,cAAA,CAAA,IAAA,CAAA,UAAA,IAAA;AAAA,mBAAA,IAAA,CAAA,IAAA,IAAA,IAAA;AAAA,WAAA,EACA,IADA;AAEA;;AACA,aAAA,aAAA;AACA,UAAA,aAAA,GAAA,KAAA,2BAAA,CAAA,IAAA,CACA,UAAA,IAAA;AAAA,mBAAA,IAAA,CAAA,EAAA,IAAA,IAAA;AAAA,WADA,EAEA,IAFA;AAGA;AAbA;;AAeA,aAAA,aAAA;AACA,KAjCA;AAkCA,IAAA,WAlCA,uBAkCA,IAlCA,EAkCA;AACA,WAAA,KAAA,CAAA,IAAA,EAAA,WAAA;AACA,WAAA,wBAAA,GAAA,KAAA,kBAAA;AACA,WAAA,kBAAA,CAAA,oBAAA;AACA,KAtCA;AAuCA,IAAA,WAvCA,yBAuCA;AACA,WAAA,KAAA,CAAA,oBAAA,EAAA,WAAA;;AACA,UAAA,CAAA,KAAA,eAAA,EAAA;AACA,YAAA,WAAA,GAAA,IAAA,IAAA,CAAA,KAAA,wBAAA,CAAA,WAAA,CAAA;AACA,YAAA,YAAA,GAAA,IAAA,IAAA,CAAA,KAAA,wBAAA,CAAA,SAAA,CAAA;AACA,aAAA,kBAAA,GAAA,IAAA,CAAA,KAAA,CACA,IAAA,CAAA,SAAA,CAAA,KAAA,wBAAA,CADA,CAAA;AAGA,aAAA,kBAAA,CAAA,WAAA,GAAA,WAAA;AACA,aAAA,kBAAA,CAAA,SAAA,GAAA,YAAA;AACA;;AACA,WAAA,WAAA,GAAA,KAAA;AACA,KAnDA;AAoDA,IAAA,gBApDA,8BAoDA;AACA,WAAA,WAAA,GAAA,IAAA;AACA,KAtDA;AAuDA,IAAA,oBAvDA,kCAuDA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA,gBAAA,YADA,GACA;AACA,kBAAA,WAAA,EAAA,IADA;AAEA,kBAAA,MAAA,EAAA;AAFA,iBADA;AAAA;AAAA,uBAKA,MAAA,CAAA,QAAA,CAAA,gBAAA,CAAA,IAAA,CAAA,YAAA,CALA;;AAAA;AAKA,gBAAA,MALA;;AAMA,oBAAA,MAAA,CAAA,KAAA,GAAA,CAAA,EAAA;AACA,kBAAA,MAAA,CAAA,OAAA,CAAA,CAAA,EAAA,UAAA,CAAA,OAAA,CAAA,UAAA,GAAA,EAAA;AACA,4BAAA,GAAA,CAAA,GAAA;AACA,2BAAA,kBAAA;AACA,wBAAA,MAAA,CAAA,gBAAA,GAAA,GAAA,CAAA,KAAA,CAAA,QAAA,CAAA,KAAA,IACA,CAAA,KAAA,CADA,GAEA,GAAA,CAAA,KAAA,CAAA,KAAA,CAAA,GAAA,CAFA;AAGA;AALA;AAOA,mBARA;AASA;;AAhBA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAiBA,KAxEA;AAyEA,IAAA,aAzEA,2BAyEA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA,gBAAA,MADA,GACA;AACA,kBAAA,WAAA,EAAA,IADA;AAEA,kBAAA,MAAA,EAAA;AAFA,iBADA;AAAA;AAAA,uBAKA,MAAA,CAAA,QAAA,CAAA,kBAAA,CAAA,IAAA,CAAA,MAAA,CALA;;AAAA;AAKA,gBAAA,GALA;;AAMA,oBAAA,GAAA,CAAA,OAAA,IAAA,GAAA,CAAA,OAAA,CAAA,MAAA,GAAA,CAAA,EAAA;AACA,kBAAA,MAAA,CAAA,mBAAA,GAAA,GAAA,CAAA,OAAA;AACA,kBAAA,GAAA,CAAA,OAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,wBAAA,IAAA,CAAA,aAAA,EAAA;AACA,0BAAA,IAAA,GAAA;AACA,wBAAA,IAAA,EAAA,IAAA,CAAA,IADA;AAEA,wBAAA,IAAA,EAAA,MAAA,CAAA,EAAA,CAAA,kBAAA,IAAA,CAAA,sBAAA;AAFA,uBAAA,CADA,CAMA;;AACA,0BAAA,MAAA,CAAA,gBAAA,CAAA,QAAA,CAAA,KAAA,CAAA,EAAA;AACA,wBAAA,MAAA,CAAA,cAAA,CAAA,IAAA,CAAA,IAAA;AACA,uBAFA,MAEA;AACA,4BAAA,MAAA,CAAA,gBAAA,CAAA,QAAA,CAAA,IAAA,CAAA,IAAA,CAAA,EAAA;AACA,0BAAA,MAAA,CAAA,cAAA,CAAA,IAAA,CAAA,IAAA;AACA;AACA;AACA;AACA,mBAhBA,EAFA,CAoBA;AACA;AACA;AACA;;AAEA,gBAAA,MAAA,CAAA,cAAA,CAAA,OAAA,CAAA,UAAA,GAAA,EAAA;AACA,kBAAA,GAAA,CAAA,KAAA,GAAA,KAAA,CAAA,IAAA,CAAA,IAAA,GAAA,CAAA,GAAA,CAAA,KAAA,CAAA,CAAA;AACA,iBAFA;;AA/BA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAkCA,KA3GA;AA4GA,IAAA,qBA5GA,mCA4GA;AACA,UAAA,MAAA,GAAA,IAAA,IAAA,CACA,IAAA,CAAA,GAAA,KAAA,OAAA,IAAA,GAAA,EAAA,IAAA,MAAA,CAAA,0BAAA,GAAA,CAAA,CADA,CAAA,CADA,CAIA;AACA;AACA;;AACA,MAAA,MAAA,CAAA,QAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA,CAAA;AACA,aAAA,MAAA;AACA,KArHA;AAsHA,IAAA,mBAtHA,iCAsHA;AACA,UAAA,MAAA,GAAA,IAAA,IAAA,CAAA,IAAA,CAAA,GAAA,KAAA,OAAA,IAAA,GAAA,EAAA,CAAA;AACA,MAAA,MAAA,CAAA,QAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA,CAAA;AACA,aAAA,MAAA;AACA,KA1HA;AA2HA,IAAA,iBA3HA,+BA2HA;AACA,WAAA,KAAA,CAAA,oBAAA,EAAA,aAAA,CAAA,WAAA;AACA,KA7HA;AA8HA,IAAA,eA9HA,6BA8HA;AACA,WAAA,KAAA,CAAA,oBAAA,EAAA,aAAA,CAAA,aAAA;AACA,KAhIA;AAiIA,IAAA,gBAjIA,8BAiIA;AACA,UAAA,CAAA,KAAA,kBAAA,CAAA,UAAA,EAAA;AACA,aAAA,kBAAA,CAAA,UAAA,GAAA,EAAA;AACA;;AACA,WAAA,kBAAA,CAAA,UAAA,GAAA,EAAA;AACA,WAAA,qBAAA;AACA,WAAA,kBAAA,CAAA,WAAA,GAAA,KAAA;AACA,WAAA,oBAAA;AACA,KAzIA;AA0IA,IAAA,gBA1IA,8BA0IA;AACA,UAAA,CAAA,KAAA,kBAAA,CAAA,UAAA,EAAA;AACA,aAAA,kBAAA,CAAA,UAAA,GAAA,EAAA;AACA;;AACA,WAAA,kBAAA,CAAA,UAAA,GAAA,EAAA;AACA,WAAA,qBAAA;AACA,KAhJA;AAiJA,IAAA,qBAjJA,mCAiJA;AACA,UAAA,MAAA,GAAA,IAAA,eAAA,EAAA;AACA,MAAA,MAAA,CAAA,MAAA,CAAA,QAAA,EAAA,IAAA;;AACA,UAAA,KAAA,4BAAA,KAAA,EAAA,EAAA;AACA,QAAA,MAAA,CAAA,MAAA,CAAA,MAAA,EAAA,KAAA,kBAAA,CAAA,UAAA;AACA;;AACA,UAAA,KAAA,kBAAA,CAAA,UAAA,KAAA,EAAA,EAAA;AACA,QAAA,MAAA,CAAA,MAAA,CAAA,YAAA,EAAA,KAAA,kBAAA,CAAA,UAAA;AACA;;AAEA,UAAA,KAAA,GAAA,IAAA;;AACA,WAAA,QAAA,CAAA,oBAAA,CAAA,IAAA,CAAA,MAAA,EAAA,IAAA,CAAA,UAAA,IAAA,EAAA;AACA,QAAA,KAAA,CAAA,gBAAA,GAAA,IAAA;;AAEA,YAAA,IAAA,CAAA,MAAA,IAAA,CAAA,EAAA;AACA,UAAA,KAAA,CAAA,kBAAA,CAAA,UAAA,GAAA,IAAA,CAAA,CAAA,CAAA,CAAA,GAAA;AACA;AACA,OANA;AAOA,KAnKA;AAoKA,IAAA,oBApKA,kCAoKA;AAAA;;AACA,UAAA,cAAA,GAAA,EAAA;AACA,UAAA,UAAA,GAAA,KAAA,mBAAA,CAAA,IAAA,CACA,UAAA,IAAA;AAAA,eACA,IAAA,CAAA,IAAA,IAAA,MAAA,CAAA,kBAAA,CAAA,UAAA,IAAA,IAAA,CAAA,aADA;AAAA,OADA,CAAA;AAIA,UAAA,OAAA,GAAA,UAAA,GACA,UAAA,CAAA,gBAAA,CAAA,MAAA,CAAA,UAAA,IAAA;AAAA,eAAA,IAAA,CAAA,WAAA;AAAA,OAAA,CADA,GAEA,EAFA;;AAGA,UAAA,OAAA,CAAA,MAAA,GAAA,CAAA,EAAA;AACA,QAAA,OAAA,CAAA,CAAA,CAAA,CAAA,WAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,cACA,CAAA,cAAA,CAAA,IAAA,CACA,UAAA,CAAA;AAAA,mBAAA,CAAA,CAAA,EAAA,IAAA,IAAA,CAAA,EAAA,CAAA,SAAA,CAAA,CAAA,EAAA,IAAA,CAAA,EAAA,CAAA,MAAA,CAAA;AAAA,WADA,CADA,EAIA;AACA,YAAA,cAAA,CAAA,IAAA,CAAA;AACA,cAAA,EAAA,EAAA,IAAA,CAAA,EAAA,CAAA,SAAA,CAAA,CAAA,EAAA,IAAA,CAAA,EAAA,CAAA,MAAA,CADA;AAEA,cAAA,IAAA,EAAA,MAAA,CAAA,EAAA,CAAA,kBAAA,IAAA,CAAA,OAAA;AAFA,aAAA;AAIA;AACA,SAXA;AAYA;;AACA,WAAA,oBAAA,GAAA,cAAA;AACA,KA5LA;AA6LA,IAAA,kBA7LA,gCA6LA;AACA,UAAA,MAAA,GAAA,EAAA;AACA,WAAA,oBAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,QAAA,MAAA,IAAA,IAAA,CAAA,EAAA,GAAA,GAAA;AACA,OAFA;AAGA,aAAA,MAAA,CAAA,SAAA,CAAA,CAAA,EAAA,MAAA,CAAA,MAAA,GAAA,CAAA,CAAA;AACA,KAnMA;AAoMA,IAAA,kBApMA,8BAoMA,IApMA,EAoMA;AAAA;;AACA,WAAA,KAAA,CAAA,IAAA,EAAA,QAAA,CAAA,UAAA,KAAA,EAAA;AACA,YAAA,KAAA,EAAA;AACA,UAAA,MAAA,CAAA,wBAAA,GAAA,IAAA,CAAA,KAAA,CACA,IAAA,CAAA,SAAA,CAAA,MAAA,CAAA,kBAAA,CADA,CAAA;AAGA,UAAA,MAAA,CAAA,eAAA,GAAA,KAAA;;AACA,UAAA,MAAA,CAAA,KAAA,CAAA,eAAA,EAAA;AACA,YAAA,YAAA,EAAA,IADA;AAEA,YAAA,YAAA,EAAA,MAAA,CAAA,eAAA;AAFA,WAAA;;AAIA,UAAA,MAAA,CAAA,2BAAA,GAAA,IAAA,CAAA,KAAA,CACA,IAAA,CAAA,SAAA,CAAA,MAAA,CAAA,oBAAA,CADA,CAAA;AAGA,UAAA,MAAA,CAAA,WAAA,GAAA,KAAA;AACA;AACA,OAfA;AAgBA,KArNA;AAsNA,IAAA,eAtNA,6BAsNA;AACA,UAAA,MAAA,GAAA,EAAA;;AAEA,UAAA,KAAA,kBAAA,CAAA,UAAA,KAAA,EAAA,EAAA;AACA,QAAA,MAAA,CAAA,YAAA,CAAA,GAAA,KAAA,kBAAA,CAAA,UAAA;AACA;;AACA,UAAA,KAAA,kBAAA,CAAA,UAAA,KAAA,EAAA,EAAA;AACA,QAAA,MAAA,CAAA,eAAA,CAAA,GAAA,KAAA,kBAAA,CAAA,UAAA;AACA;;AACA,UAAA,KAAA,kBAAA,CAAA,WAAA,KAAA,KAAA,EAAA;AACA,QAAA,MAAA,CAAA,aAAA,CAAA,GAAA,KAAA,kBAAA,CAAA,WAAA;AACA,OAFA,MAEA;AACA,QAAA,MAAA,CAAA,aAAA,CAAA,GAAA,KAAA,kBAAA,EAAA;AACA;;AACA,UAAA,KAAA,kBAAA,CAAA,WAAA,KAAA,EAAA,EAAA;AACA,QAAA,MAAA,CAAA,aAAA,CAAA,GAAA,KAAA,kBAAA,CAAA,WAAA,CAAA,OAAA,EAAA;AACA,YAAA,sBAAA,GACA,IAAA,IAAA,GAAA,iBAAA,KAAA,EAAA,GAAA,CAAA,CAAA,GAAA,CADA;AAEA,YAAA,iBAAA,GAAA,MAAA,CACA,KAAA,kBAAA,CAAA,WADA,EAEA,qBAFA,CAAA,CAIA,GAJA,CAIA,sBAAA,GAAA,CAAA,CAJA,EAIA,OAJA,EAKA,MALA,CAKA,qBALA,CAAA;AAOA,YAAA,QAAA,GAAA,iBAAA;AACA,aAAA,eAAA,GAAA,QAAA;AACA;;AACA,UAAA,KAAA,kBAAA,CAAA,SAAA,KAAA,EAAA,EAAA;AACA,QAAA,MAAA,CAAA,WAAA,CAAA,GAAA,KAAA,kBAAA,CAAA,SAAA,CAAA,OAAA,EAAA;AACA,YAAA,uBAAA,GACA,IAAA,IAAA,GAAA,iBAAA,KAAA,EAAA,GAAA,CAAA,CAAA,GAAA,CADA;AAEA,YAAA,kBAAA,GAAA,MAAA,CACA,KAAA,kBAAA,CAAA,SADA,EAEA,qBAFA,CAAA,CAIA,GAJA,CAIA,uBAAA,GAAA,CAAA,CAJA,EAIA,OAJA,EAKA,MALA,CAKA,qBALA,CAAA;AAMA,YAAA,UAAA,GAAA,MAAA,CAAA,kBAAA,CAAA,CAAA,MAAA,CACA,qBADA,CAAA;AAGA,aAAA,aAAA,GAAA,UAAA;AACA,OAdA,MAcA;AACA,QAAA,MAAA,CAAA,WAAA,CAAA,GAAA,IAAA,CAAA,GAAA,KAAA,KAAA,oBAAA,EAAA;AACA;;AAEA,MAAA,MAAA,CAAA,UAAA,CAAA,GAAA,KAAA,kBAAA,CAAA,QAAA;AACA,MAAA,MAAA,CAAA,UAAA,CAAA,GAAA,KAAA,kBAAA,CAAA,QAAA;AACA,aAAA,MAAA;AACA,KAvQA;AAwQA,IAAA,oBAxQA,kCAwQA;AACA,aAAA,IAAA,EAAA,GAAA,EAAA,GAAA,EAAA,GAAA,IAAA;AACA,KA1QA;AA2QA,IAAA,kBA3QA,gCA2QA;AACA,WAAA,KAAA,CAAA,eAAA;AACA;AA7QA;AA1HA,CAAA", "sourcesContent": ["<template>\r\n  <div>\r\n    <Row>\r\n      <Col span=\"21\" style=\"text-align: left\" v-if=\"!searchFirstTime\">\r\n        <span\r\n          style=\"padding-left: 5px\"\r\n          v-if=\"searchFormValidateSearch.deviceType != '' ||\r\n        searchFormValidateSearch.objectName != '' ||\r\n        searchFormValidateSearch.devicePids != '' ||\r\n        searchFormValidateSearch.resourceIds != '' ||\r\n        searchFormValidateSearch.startedTime != '' ||\r\n        searchFormValidateSearch.endedTime != ''\r\n        \"\r\n        >{{ $t(\"LocaleString.L00262\") }}&nbsp;:</span>\r\n        <span\r\n          style=\"padding-left: 10px; font-weight: bolder\"\r\n          v-if=\"searchFormValidateSearch.startedTime != ''\"\r\n        >\r\n          {{\r\n          $t(\"LocaleString.L00141\") + '(' + parseTime(searchFormValidateSearch.startedTime) + ') ' }}\r\n        </span>\r\n        <span\r\n          style=\"padding-left: 10px; font-weight: bolder\"\r\n          v-if=\"searchFormValidateSearch.endedTime != ''\"\r\n        >\r\n          {{\r\n          $t(\"LocaleString.L00142\") + '(' + parseTime(searchFormValidateSearch.endedTime) + ') ' }}\r\n        </span>\r\n        <span\r\n          style=\"padding-left: 10px; font-weight: bolder\"\r\n          v-if=\"searchFormValidateSearch.deviceType != ''\"\r\n        >\r\n          {{\r\n          $t(\"LocaleString.L00223\") + '(' + translateCondition(searchFormValidateSearch.deviceType, 'deviceType') +\r\n          ')' }}\r\n        </span>\r\n        <span\r\n          style=\"padding-left: 10px; font-weight: bolder\"\r\n          v-if=\"searchFormValidateSearch.objectName != ''\"\r\n        >\r\n          {{\r\n          $t(\"LocaleString.L00092\") + '(' + searchFormValidateSearch.objectName + ') ' }}\r\n        </span>\r\n        <span\r\n          style=\"padding-left: 10px; font-weight: bolder\"\r\n          v-if=\"searchFormValidateSearch.devicePids != ''\"\r\n        >\r\n          {{\r\n          $t(\"LocaleString.L00090\") + '(' + searchFormValidateSearch.devicePids + ') ' }}\r\n        </span>\r\n        <span\r\n          style=\"padding-left: 10px; font-weight: bolder\"\r\n          v-if=\"searchFormValidateSearch.resourceIds != ''\"\r\n        >\r\n          {{\r\n          $t(\"LocaleString.L00158\") + '(' + translateCondition(searchFormValidateSearch.resourceIds, 'resourceIds') + ')'}}\r\n        </span>\r\n        <span style=\"padding-left: 10px; font-weight: bolder\">\r\n          {{\r\n          $t(\"LocaleString.L00157\") + '(' + translateCondition(searchFormValidateSearch.lineType, 'lineType') + ')'}}\r\n        </span>\r\n        <span\r\n          v-if=\"searchFormValidateSearch.lineType != 'RAW'\"\r\n          style=\"padding-left: 10px; font-weight: bolder\"\r\n        >{{$t(\"LocaleString.L30150\") + '(' + searchFormValidateSearch.interval + $t(\"LocaleString.L30027\") + ')'}}</span>\r\n\r\n        <!-- <Button\r\n          ghost\r\n          shape=\"circle\"\r\n          style=\"width: 20px; margin-left: 10px\"\r\n          @click=\"handleReset('searchFormValidate')\"\r\n          v-if=\"\r\n            searchFormValidateSearch.deviceType != '' ||\r\n            searchFormValidateSearch.objectName != '' ||\r\n            searchFormValidateSearch.devicePids != '' ||\r\n            searchFormValidateSearch.resourceIds != '' ||\r\n            searchFormValidateSearch.startedTime != '' ||\r\n            searchFormValidateSearch.endedTime != ''\r\n          \"\r\n        >\r\n          <img :src=\"resetIcon\" style=\"width: 30.4px; margin-left: -14.8px\" />\r\n        </Button>-->\r\n      </Col>\r\n      <Col span=\"3\" :offset=\"!searchFirstTime ? 0 : 21\" style=\"text-align: right\">\r\n        <Button ghost shape=\"circle\" style=\"width: 20px\" @click=\"openSearchObject\">\r\n          <img :src=\"searchIcon\" style=\"width: 30.4px; margin-left: -14.8px\" />\r\n        </Button>\r\n        <Button type=\"success\" icon=\"ios-download-outline\" @click=\"exportHandleSubmit()\">\r\n          {{ $t(\"LocaleString.B00009\")\r\n          }}\r\n        </Button>\r\n      </Col>\r\n    </Row>\r\n    <Modal v-model=\"modalSearch\" :closable=\"false\" :mask-closable=\"false\">\r\n      <Form\r\n        ref=\"searchFormValidate\"\r\n        :model=\"searchFormValidate\"\r\n        :rules=\"searchRuleValidate\"\r\n        label-position=\"top\"\r\n      >\r\n        <Row :class=\"rowHeight\" :gutter=\"16\">\r\n          <Col span=\"12\">\r\n            <FormItem\r\n              class=\"search-form-item\"\r\n              :label=\"$t('LocaleString.L00141')\"\r\n              prop=\"startedTime\"\r\n            >\r\n              <DatePicker\r\n                type=\"datetime\"\r\n                v-model.trim=\"searchFormValidate.startedTime\"\r\n                :placeholder=\"$t('LocaleString.M00010', { 0: $t('LocaleString.L00141') })\r\n              \"\r\n                :transfer=\"true\"\r\n                @on-change=\"startedTimeChange()\"\r\n              ></DatePicker>\r\n            </FormItem>\r\n          </Col>\r\n          <Col span=\"12\">\r\n            <FormItem class=\"search-form-item\" :label=\"$t('LocaleString.L00142')\" prop=\"endedTime\">\r\n              <DatePicker\r\n                type=\"datetime\"\r\n                v-model.trim=\"searchFormValidate.endedTime\"\r\n                :placeholder=\"$t('LocaleString.M00010', { 0: $t('LocaleString.L00142') })\r\n              \"\r\n                :transfer=\"true\"\r\n                @on-change=\"endedTimeChange()\"\r\n              ></DatePicker>\r\n            </FormItem>\r\n          </Col>\r\n          <Col span=\"12\">\r\n            <FormItem class=\"search-form-item\" :label=\"$t('LocaleString.L00223')\" prop=\"deviceType\">\r\n              <Select\r\n                :placeholder=\"$t('LocaleString.D00001')\"\r\n                v-model=\"searchFormValidate.deviceType\"\r\n                :transfer=\"true\"\r\n                clearable\r\n                :not-found-text=\"notFoundText\"\r\n                @on-change=\"deviceTypeChange()\"\r\n              >\r\n                <Option\r\n                  v-for=\"item in deviceTypeMenu\"\r\n                  :value=\"item.type\"\r\n                  :key=\"item.type\"\r\n                >{{ item.name }}</Option>\r\n              </Select>\r\n            </FormItem>\r\n          </Col>\r\n          <Col span=\"12\">\r\n            <FormItem class=\"search-form-item\" :label=\"$t('LocaleString.L00090')\" prop=\"devicePids\">\r\n              <Select\r\n                :placeholder=\"$t('LocaleString.D00001')\"\r\n                v-model=\"searchFormValidate.devicePids\"\r\n                :transfer=\"true\"\r\n                filterable\r\n                clearable\r\n                :not-found-text=\"notFoundText\"\r\n              >\r\n                <Option\r\n                  v-for=\"item in objectDeviceMenu\"\r\n                  :value=\"item.pid\"\r\n                  :key=\"item.pid\"\r\n                >{{ item.pid }}</Option>\r\n              </Select>\r\n            </FormItem>\r\n          </Col>\r\n          <Col span=\"12\">\r\n            <FormItem class=\"search-form-item\" :label=\"$t('LocaleString.L00092')\" prop=\"objectName\">\r\n              <Select\r\n                :placeholder=\"$t('LocaleString.D00001')\"\r\n                v-model=\"searchFormValidate.objectName\"\r\n                :transfer=\"true\"\r\n                filterable\r\n                clearable\r\n                :not-found-text=\"notFoundText\"\r\n                @on-change=\"objectNameChange()\"\r\n              >\r\n                <Option\r\n                  v-for=\"item in objectMenu\"\r\n                  :value=\"item.name\"\r\n                  :key=\"item.code\"\r\n                >{{ item.name }}</Option>\r\n              </Select>\r\n            </FormItem>\r\n          </Col>\r\n\r\n          <Col span=\"12\">\r\n            <FormItem\r\n              class=\"search-form-item\"\r\n              :label=\"$t('LocaleString.L00158')\"\r\n              prop=\"resourceIds\"\r\n            >\r\n              <Select\r\n                v-model=\"searchFormValidate.resourceIds\"\r\n                :transfer=\"true\"\r\n                :not-found-text=\"notFoundText\"\r\n              >\r\n                <Option value=\"all\">{{ $t(\"LocaleString.D00002\") }}</Option>\r\n                <Option\r\n                  v-for=\"item in deviceResourceIdMenu\"\r\n                  :value=\"item.id\"\r\n                  :key=\"item.id\"\r\n                >{{ item.name }}</Option>\r\n              </Select>\r\n            </FormItem>\r\n          </Col>\r\n          <Col span=\"12\">\r\n            <FormItem class=\"search-form-item\" :label=\"$t('LocaleString.L00157')\" prop=\"type\">\r\n              <Select\r\n                v-model=\"searchFormValidate.lineType\"\r\n                :transfer=\"true\"\r\n                :not-found-text=\"notFoundText\"\r\n              >\r\n                <Option\r\n                  v-for=\"item in lineTypeList\"\r\n                  :value=\"item.key\"\r\n                  :key=\"item.key\"\r\n                >{{ item.value }}</Option>\r\n              </Select>\r\n            </FormItem>\r\n          </Col>\r\n          <Col span=\"12\" v-if=\"searchFormValidate.lineType !== 'RAW'\">\r\n            <FormItem class=\"search-form-item\" :label=\"$t('LocaleString.L30150')\" prop=\"interval\">\r\n              <Select\r\n                v-model=\"searchFormValidate.interval\"\r\n                :transfer=\"true\"\r\n                :not-found-text=\"notFoundText\"\r\n              >\r\n                <Option v-for=\"item in 10\" :value=\"item\" :key=\"item\">{{ item }}</Option>\r\n              </Select>\r\n            </FormItem>\r\n          </Col>\r\n        </Row>\r\n      </Form>\r\n      <div slot=\"footer\">\r\n        <div class=\"search-submit\">\r\n          <Button\r\n            type=\"success\"\r\n            icon=\"ios-search\"\r\n            @click=\"searchHandleSubmit('searchFormValidate')\"\r\n          >\r\n            {{\r\n            $t(\"LocaleString.B20017\") }}\r\n          </Button>\r\n          <Button @click=\"cancelModal\">{{ $t(\"LocaleString.B00015\") }}</Button>\r\n        </div>\r\n      </div>\r\n    </Modal>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport resetIcon from \"@/assets/images/ic_reset.svg\";\r\nimport searchIcon from \"@/assets/images/ic_search.svg\";\r\nimport Config from \"@/common/config\";\r\nlet moment = require(\"moment\");\r\nexport default {\r\n  props: [\r\n    \"deviceType\",\r\n    \"objectMenu\",\r\n    \"resourceIdMenu\",\r\n    \"targetDeviceType\",\r\n    \"targetDeviceId\"\r\n  ],\r\n  data() {\r\n    return {\r\n      lineTypeList: [\r\n        { key: \"MEDIAN\", value: this.$t(\"LocaleString.L30178\") },\r\n        { key: \"MEAN\", value: this.$t(\"LocaleString.L30179\") },\r\n        { key: \"LAST\", value: this.$t(\"LocaleString.L30180\") },\r\n        { key: \"RAW\", value: this.$t(\"LocaleString.L30181\") }\r\n      ],\r\n      deviceEventTypeList: [],\r\n      searchStartDate: \"\",\r\n      searchEndDate: \"\",\r\n      searchFirstTime: true,\r\n      modalSearch: false,\r\n      resetIcon: resetIcon,\r\n      searchIcon: searchIcon,\r\n      defaultSelection: [],\r\n      deviceTypeMenu: [],\r\n      objectDeviceMenu: [],\r\n      deviceResourceIdMenu: [],\r\n      deviceResourceIdMenuforView: [],\r\n      notFoundText: this.$t(\"LocaleString.L00081\"),\r\n      rowHeight: \"row-height\",\r\n      searchFormValidate: {\r\n        deviceType: this.targetDeviceType != \"\" ? this.targetDeviceType : \"\",\r\n        objectName: \"\",\r\n        devicePids: this.targetDeviceId != \"\" ? this.targetDeviceId : \"\",\r\n        resourceIds: \"all\",\r\n        startedTime: this.getStartedTimeDefault(),\r\n        endedTime: this.getEndedTimeDefault(),\r\n        interval: 1,\r\n        lineType: \"MEDIAN\"\r\n      },\r\n      searchFormValidateSearch: null,\r\n      dateOptions: {\r\n        disabledDate(date) {\r\n          let startedTime = new Date(\r\n            Date.now() - 1000 * 60 * 60 * 24 * Config.SEARCH_ENABLE_DATES\r\n          );\r\n          startedTime.setHours(0, 0, 0, 0);\r\n          let endedTime = new Date(Date.now() + 1000 * 60 * 60 * 24);\r\n          endedTime.setHours(0, 0, 0, 0);\r\n\r\n          return date && (date < startedTime || date > endedTime);\r\n        }\r\n      }\r\n    };\r\n  },\r\n  computed: {\r\n    searchRuleValidate() {\r\n      return {\r\n        deviceType: {\r\n          required: true,\r\n          message: this.$t(\"LocaleString.M00010\", {\r\n            0: this.$t(\"LocaleString.L00223\")\r\n          })\r\n        },\r\n        devicePids: {\r\n          required: true,\r\n          message: this.$t(\"LocaleString.M00010\", {\r\n            0: this.$t(\"LocaleString.L00090\")\r\n          })\r\n        },\r\n        startedTime: [\r\n          {\r\n            required: true,\r\n            message: this.$t(\"LocaleString.M00010\", {\r\n              0: this.$t(\"LocaleString.L00141\")\r\n            })\r\n          },\r\n          {\r\n            type: \"date\",\r\n            validator: (rule, value) =>\r\n              !value ||\r\n              !this.searchFormValidate.endedTime ||\r\n              value <= this.searchFormValidate.endedTime,\r\n            message: this.$t(\"LocaleString.W00036\")\r\n          }\r\n        ],\r\n        endedTime: [\r\n          {\r\n            required: true,\r\n            message: this.$t(\"LocaleString.M00010\", {\r\n              0: this.$t(\"LocaleString.L00142\")\r\n            })\r\n          },\r\n          {\r\n            type: \"date\",\r\n            validator: (rule, value) =>\r\n              !value ||\r\n              !this.searchFormValidate.startedTime ||\r\n              value >= this.searchFormValidate.startedTime,\r\n            message: this.$t(\"LocaleString.W00036\")\r\n          }\r\n        ]\r\n      };\r\n    }\r\n  },\r\n  created() {\r\n    this.searchFormValidateSearch = JSON.parse(\r\n      JSON.stringify(this.searchFormValidate)\r\n    );\r\n  },\r\n  async mounted() {\r\n    await this.getDeviceDefaultList();\r\n    await this.getSelectList();\r\n    this.resetObjectDeviceMenu();\r\n    this.resetResourceIdsMenu();\r\n\r\n    if (this.targetDeviceId != \"\" && this.targetDeviceType != \"\") {\r\n      this.deviceTypeChange();\r\n      this.searchFormValidate.devicePids = this.targetDeviceId;\r\n      this.searchHandleSubmit(\"searchFormValidate\");\r\n    }\r\n  },\r\n  methods: {\r\n    parseTime(day) {\r\n      let timedifference = (new Date().getTimezoneOffset() / 60) * -1;\r\n      let tmpOffsetDay = moment(day, \"YYYY-MM-DD HH:mm:ss\")\r\n        .add(timedifference, \"hours\")\r\n        .format(\"YYYY-MM-DD HH:mm:ss\");\r\n      return tmpOffsetDay;\r\n    },\r\n    translateCondition(item, type) {\r\n      if (item == undefined) {\r\n        return \"\";\r\n      }\r\n      if (item == \"all\") {\r\n        return this.$t(\"LocaleString.D00002\");\r\n      }\r\n\r\n      let translateName = \"\";\r\n      switch (type) {\r\n        case \"lineType\":\r\n          translateName = this.lineTypeList.find(data => data.key == item)\r\n            .value;\r\n          break;\r\n        case \"deviceType\":\r\n          translateName = this.deviceTypeMenu.find(data => data.type == item)\r\n            .name;\r\n          break;\r\n        case \"resourceIds\":\r\n          translateName = this.deviceResourceIdMenuforView.find(\r\n            data => data.id == item\r\n          ).name;\r\n          break;\r\n      }\r\n      return translateName;\r\n    },\r\n    handleReset(name) {\r\n      this.$refs[name].resetFields();\r\n      this.searchFormValidateSearch = this.searchFormValidate;\r\n      this.searchHandleSubmit(\"searchFormValidate\");\r\n    },\r\n    cancelModal() {\r\n      this.$refs[\"searchFormValidate\"].resetFields();\r\n      if (!this.searchFirstTime) {\r\n        let newStartsAt = new Date(this.searchFormValidateSearch.startedTime);\r\n        let newFinishsAt = new Date(this.searchFormValidateSearch.endedTime);\r\n        this.searchFormValidate = JSON.parse(\r\n          JSON.stringify(this.searchFormValidateSearch)\r\n        );\r\n        this.searchFormValidate.startedTime = newStartsAt;\r\n        this.searchFormValidate.endedTime = newFinishsAt;\r\n      }\r\n      this.modalSearch = false;\r\n    },\r\n    openSearchObject() {\r\n      this.modalSearch = true;\r\n    },\r\n    async getDeviceDefaultList() {\r\n      let configParams = {\r\n        inlinecount: true,\r\n        search: \"category in @SNS@Setting\"\r\n      };\r\n      let pocRes = await this.$service.getPOCProperties.send(configParams);\r\n      if (pocRes.count > 0) {\r\n        pocRes.results[0].properties.forEach(res => {\r\n          switch (res.key) {\r\n            case \"deviceSelectList\":\r\n              this.defaultSelection = res.value.includes(\"all\")\r\n                ? [\"all\"]\r\n                : res.value.split(\",\");\r\n              break;\r\n          }\r\n        });\r\n      }\r\n    },\r\n    async getSelectList() {\r\n      let params = {\r\n        inlinecount: true,\r\n        search: \"active eq true\"\r\n      };\r\n      let res = await this.$service.getDeviceEventType.send(params);\r\n      if (res.results && res.results.length > 0) {\r\n        this.deviceEventTypeList = res.results;\r\n        res.results.forEach(item => {\r\n          if (item.isPhysiologic) {\r\n            let data = {\r\n              type: item.type,\r\n              name: this.$t(\"LocaleString.\" + item.deviceTypeNameStringId)\r\n            };\r\n\r\n            // this.deviceTypeMenu.push(data);\r\n            if (this.defaultSelection.includes(\"all\")) {\r\n              this.deviceTypeMenu.push(data);\r\n            } else {\r\n              if (this.defaultSelection.includes(item.type)) {\r\n                this.deviceTypeMenu.push(data);\r\n              }\r\n            }\r\n          }\r\n        });\r\n\r\n        // this.deviceTypeMenu.sort((a, b) =>\r\n        //   a.name.localeCompare(b.name, \"zh-Hant\")\r\n        // );\r\n      }\r\n\r\n      this.resourceIdMenu.forEach(obj => {\r\n        obj.types = Array.from(new Set(obj.types));\r\n      });\r\n    },\r\n    getStartedTimeDefault() {\r\n      let result = new Date(\r\n        Date.now() - 1000 * 3600 * 24 * (Config.SEARCH_INFLUXDB_DATA_DATES - 1)\r\n      );\r\n      // let result = new Date(\r\n      //   Date.now() - 1000 * 3600 * 24 * (Config.SEARCH_INFLUXDB_DATA_DATES - 1)\r\n      // );\r\n      result.setHours(0, 0, 0, 0);\r\n      return result;\r\n    },\r\n    getEndedTimeDefault() {\r\n      let result = new Date(Date.now() + 1000 * 3600 * 24);\r\n      result.setHours(0, 0, 0, 0);\r\n      return result;\r\n    },\r\n    startedTimeChange() {\r\n      this.$refs[\"searchFormValidate\"].validateField(\"endedTime\");\r\n    },\r\n    endedTimeChange() {\r\n      this.$refs[\"searchFormValidate\"].validateField(\"startedTime\");\r\n    },\r\n    deviceTypeChange() {\r\n      if (!this.searchFormValidate.deviceType) {\r\n        this.searchFormValidate.deviceType = \"\";\r\n      }\r\n      this.searchFormValidate.devicePids = \"\";\r\n      this.resetObjectDeviceMenu();\r\n      this.searchFormValidate.resourceIds = \"all\";\r\n      this.resetResourceIdsMenu();\r\n    },\r\n    objectNameChange() {\r\n      if (!this.searchFormValidate.objectName) {\r\n        this.searchFormValidate.objectName = \"\";\r\n      }\r\n      this.searchFormValidate.devicePids = \"\";\r\n      this.resetObjectDeviceMenu();\r\n    },\r\n    resetObjectDeviceMenu() {\r\n      let params = new URLSearchParams();\r\n      params.append(\"active\", true);\r\n      if (this.searchFormValidatedeviceType !== \"\") {\r\n        params.append(\"type\", this.searchFormValidate.deviceType);\r\n      }\r\n      if (this.searchFormValidate.objectName !== \"\") {\r\n        params.append(\"objectName\", this.searchFormValidate.objectName);\r\n      }\r\n\r\n      let _this = this;\r\n      this.$service.getDevicesMenuNoLoad.send(params).then(data => {\r\n        _this.objectDeviceMenu = data;\r\n\r\n        if (data.length == 1) {\r\n          _this.searchFormValidate.devicePids = data[0].pid;\r\n        }\r\n      });\r\n    },\r\n    resetResourceIdsMenu() {\r\n      let resourceIdList = [];\r\n      let deviceData = this.deviceEventTypeList.find(\r\n        item =>\r\n          item.type == this.searchFormValidate.deviceType && item.isPhysiologic\r\n      );\r\n      let resList = deviceData\r\n        ? deviceData.supportDataEvent.filter(item => item.sddResource)\r\n        : [];\r\n      if (resList.length > 0) {\r\n        resList[0].sddResource.forEach(item => {\r\n          if (\r\n            !resourceIdList.some(\r\n              r => r.id == item.id.substring(1, item.id.length)\r\n            )\r\n          ) {\r\n            resourceIdList.push({\r\n              id: item.id.substring(1, item.id.length),\r\n              name: this.$t(\"LocaleString.\" + item.langsId)\r\n            });\r\n          }\r\n        });\r\n      }\r\n      this.deviceResourceIdMenu = resourceIdList;\r\n    },\r\n    joinAllResourceIds() {\r\n      let result = \"\";\r\n      this.deviceResourceIdMenu.forEach(item => {\r\n        result += item.id + \",\";\r\n      });\r\n      return result.substring(0, result.length - 1);\r\n    },\r\n    searchHandleSubmit(name) {\r\n      this.$refs[name].validate(valid => {\r\n        if (valid) {\r\n          this.searchFormValidateSearch = JSON.parse(\r\n            JSON.stringify(this.searchFormValidate)\r\n          );\r\n          this.searchFirstTime = false;\r\n          this.$emit(\"searchRequest\", {\r\n            isUserSubmit: true,\r\n            searchParams: this.getSearchParams()\r\n          });\r\n          this.deviceResourceIdMenuforView = JSON.parse(\r\n            JSON.stringify(this.deviceResourceIdMenu)\r\n          );\r\n          this.modalSearch = false;\r\n        }\r\n      });\r\n    },\r\n    getSearchParams() {\r\n      let result = {};\r\n\r\n      if (this.searchFormValidate.deviceType !== \"\") {\r\n        result[\"deviceType\"] = this.searchFormValidate.deviceType;\r\n      }\r\n      if (this.searchFormValidate.devicePids !== \"\") {\r\n        result[\"devicePidLike\"] = this.searchFormValidate.devicePids;\r\n      }\r\n      if (this.searchFormValidate.resourceIds !== \"all\") {\r\n        result[\"resourceIds\"] = this.searchFormValidate.resourceIds;\r\n      } else {\r\n        result[\"resourceIds\"] = this.joinAllResourceIds();\r\n      }\r\n      if (this.searchFormValidate.startedTime !== \"\") {\r\n        result[\"startedTime\"] = this.searchFormValidate.startedTime.getTime();\r\n        let timedifferenceStartDay =\r\n          (new Date().getTimezoneOffset() / 60) * -1 - 8;\r\n        let tmpOffsetStartDay = moment(\r\n          this.searchFormValidate.startedTime,\r\n          \"YYYY-MM-DD HH:mm:ss\"\r\n        )\r\n          .add(timedifferenceStartDay * -1, \"hours\")\r\n          .format(\"YYYY-MM-DD HH:mm:ss\");\r\n\r\n        let startsAt = tmpOffsetStartDay;\r\n        this.searchStartDate = startsAt;\r\n      }\r\n      if (this.searchFormValidate.endedTime !== \"\") {\r\n        result[\"endedTime\"] = this.searchFormValidate.endedTime.getTime();\r\n        let timedifferencefinishDay =\r\n          (new Date().getTimezoneOffset() / 60) * -1 - 8;\r\n        let tmpOffsetFinishDay = moment(\r\n          this.searchFormValidate.endedTime,\r\n          \"YYYY-MM-DD HH:mm:ss\"\r\n        )\r\n          .add(timedifferencefinishDay * -1, \"hours\")\r\n          .format(\"YYYY-MM-DD HH:mm:ss\");\r\n        let finishesAt = moment(tmpOffsetFinishDay).format(\r\n          \"YYYY-MM-DD HH:mm:ss\"\r\n        );\r\n        this.searchEndDate = finishesAt;\r\n      } else {\r\n        result[\"endedTime\"] = Date.now() + this.getNextDayOffsetTime();\r\n      }\r\n\r\n      result[\"interval\"] = this.searchFormValidate.interval;\r\n      result[\"lineType\"] = this.searchFormValidate.lineType;\r\n      return result;\r\n    },\r\n    getNextDayOffsetTime() {\r\n      return 1 * 24 * 60 * 60 * 1000;\r\n    },\r\n    exportHandleSubmit() {\r\n      this.$emit(\"exportConfirm\");\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n/deep/ .ivu-btn-success {\r\n  color: #fff;\r\n  background-color: #31babb;\r\n  border-color: #31babb;\r\n}\r\n\r\n/deep/ .ivu-btn-success:hover {\r\n  color: #fff !important;\r\n  border-color: #31babb;\r\n}\r\n</style>\r\n"], "sourceRoot": "src/components/administrative/apps/sns/monitoring/m6/physiological"}]}