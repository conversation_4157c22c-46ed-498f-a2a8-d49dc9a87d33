{"remainingRequest": "E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\node_modules\\babel-loader\\lib\\index.js!E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\node_modules\\string-replace-loader\\index.js??ref--13-0!E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\src\\locales\\en-US.js", "dependencies": [{"path": "E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\src\\locales\\en-US.js", "mtime": 1754362736988}, {"path": "E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\node_modules\\string-replace-loader\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["E:/GitRepos/FusionNetProject/6849/frontend/application/sns/src/locales/en-US.js"], "names": ["locale", "lang", "common", "consumerError", "consumerSignatureError", "invalidUserOrPassword", "badGateway", "monitor20", "monitor21", "monitor22", "monitor23", "monitor24", "monitor25", "monitor26", "monitor27", "confirmDeleteSelection", "confirmDelete", "appMainTitle", "systemTitle", "indoorRealTimePositioningSystem", "traditionalChinese", "simplifiedChinese", "english", "introduction", "people", "piece", "page", "import", "export", "exportExcel", "newlyIncreased", "search", "delete", "removePair", "list", "confirm", "cancel", "homePage", "reset", "edit", "upload", "storage", "total", "pen", "date", "time", "equipment", "noDataText", "inputKeyword", "keywordEvent", "pleaseSelect", "pleaseSearch", "chooseStartTime", "chooseEndTime", "exportFormatForExcel", "seeMoreEvents", "return", "all", "marked", "notMarked", "cancelled", "hasBeenIdentified", "deleteConfirm", "deleteItemConfirm", "prompt", "selectEventToDelete", "notUsePermission", "deleteSuccessful", "removePairSuccessful", "addSuccessful", "storageSuccessful", "exportSuccessful", "importSuccessful", "searchSuccessful", "modifySuccessful", "syncSuccessful", "exportFail", "searchFail", "backgroundManagement", "personalData", "personal", "operation", "changeThePassword", "logout", "pleaseEnterOriginalPassword", "pleaseEnterNewPassword", "pleaseEnterNewPasswordAgain", "passwordError", "tooManyResourcesError", "passwordErrorPrompt", "eventRecordErrorPrompt", "samePasswordPrompt", "twoNewPasswordsAreNotConsistent", "planeNoUploadCoordinatesError", "noInformationOnFloor", "selectMap", "floorPlan", "selectDate", "successful", "fillInEventRecord", "warning", "error", "invalidFormat", "contactSystemAdministrator", "modificationSuccessfulLoginAgain", "loginPeriodExpires", "selectedEventDoesNot", "networkProblemPleaseRefreshPage", "networkProblem", "passwordUnavailableError", "accessCodeUnavailableError", "storageUnavailableError", "accountAlreadyExists", "dataAlReadyExists", "deviceObjectExistsError", "objectDeviceExistsError", "accountNotFoundError", "resourceNotFoundError", "planeNotFoundError", "operationIsNotAllowed", "selectAtLeastOneAttribute", "itNotExist", "reorganizeThePage", "selectedEventState", "fillInIncidentRecord", "invalidRequestError", "badPasswordError", "badEmailError", "badPhoneError", "invalidAccessTaskError", "uneventful", "removeEvent", "event", "trajectory", "locateObject", "templateHelp", "objectsDisplayed", "serialNumber", "name", "category", "role", "group", "currentPosition", "latestPositioningTime", "modifiesAt", "byTheTime", "plane", "eventClassification", "sponsorName", "initiator", "eventLog", "eventState", "viewTheDayTrack", "viewCurrentLocation", "monitoring", "fenceMonitoring", "mmWaveMonitoring", "monitor01", "monitor02", "monitor03", "monitor04", "monitor05", "managePermissions", "accountManagement", "roleManagement", "firmwareUpdate", "positioningSettings", "planePosition", "baseStation", "anchor", "guard", "no", "dataManagement", "objectFeatures", "objectData", "deviceInformation", "eventDefinition", "guardSetting", "otaUpdate", "licenseManagement", "historyReport", "temporarilyNoData", "cameraSetting", "logRecording", "inventory", "systemManagement", "systemConfig", "systemSWVersion", "mmWave", "fence", "helpEvent", "enterTheNotice", "leaveTheNotice", "theNumberOfControl", "lowBatteryWarning", "stationAbnormalWarning", "stayTimeout", "regularRound", "leaveBed", "abnormal<PERSON>uard", "sensorDataDriven", "fallDetection", "stayTimeoutMMWave", "leaveBedMMWave", "getUp", "abnormalBreath", "wetUrine", "help", "untreated", "inTheProcessing", "hasLift", "personnel", "map", "eventName", "objectsName", "order", "personnelDistribution", "equipmentDistribution", "area", "subRegion", "factory", "building", "floor", "areaName", "factoryName", "buildingName", "floorName", "subRegionName", "geoCluster", "coordinate", "hasAttachment", "offline", "planeName", "originalCoordinates", "currentCoordinates", "hasChange", "mapHint", "archives", "pairingDevice", "inputCode", "inputName", "inputCodeError", "selectObject", "selectDeleteObject", "selectGroup", "onTheGround", "underground", "okToDoThis", "okToDeleteSelection", "whetherToReset", "selectTheObject", "noData", "limitDeleteRecords", "planeNotFound", "selectPlane", "chooseType", "uploadTime", "yes", "nay", "type", "document", "enabled", "notEnabled", "selectedItems", "planeDataExists", "notAllowedDelete", "badConfiguration", "fullScreen", "exitFullScreen", "noShortcuts", "login", "account", "password", "resetPassword", "accountRequiredVerification", "passwordRequiredVerification", "passwordPatternVerification", "pleaseResetAccountPassword", "inputNewPassword", "inputNewPasswordAgain", "send", "newPasswdRequiredVerification", "newPasswdRequiredVerificationAgain", "newPasswdPatternVerification", "newPasswd<PERSON><PERSON><PERSON>", "resetPasswordSuccessful", "inputEmail", "inputVcode", "emailRequiredVerification", "emailTypeVerification", "vcodeRequiredVerification", "vcodeError", "sendEmailSuccessful", "accountNotFoundPrompt", "email", "phone", "userEmailExistsVerification", "nameRequiredVerification", "nameTypeVerification", "phoneRequiredVerification", "phonePatternVerification", "userRoleRequiredVerification", "singleEvent", "planeEvent", "eventCode", "eventTemplate", "addEventDefinition", "selectTemplate", "enterEventCode", "enterEventName", "enterEventNameNoStar", "selectEventCategory", "selectEventCategoryNoStar", "enterValue", "selectStartTime", "selectEndTime", "selectSponsorType", "selectSponsorRole", "selectSponsorGroup", "selectParticipantType", "selectParticipantRole", "selectParticipantGroup", "selectNotifierType", "selectNotifierRole", "selectNotifierGroup", "selectNotifierAccount", "enterNotifierMsg", "selectDeleteEvent", "threshold", "below", "over", "stayOver", "batteryPercentage", "<PERSON><PERSON><PERSON><PERSON>", "min<PERSON><PERSON><PERSON>", "intervals", "between", "sensorDataDrivenData", "it", "ite", "gt", "gte", "dataDrivenRuleSource", "dataDrivenRuleComp", "enterSource", "enterComp", "dataDrivenRuleRepeat", "dataDrivenRuleCount", "dataDrivenRuleDuration", "dataDrivenRuleDurationSec", "validatePlaneCode", "validateCategory", "validateSponsor", "validateParticipant", "enterEventCodeValidate", "eventCodeValidate", "enterEventNameValidate", "eventNameValidate", "templateValidate", "enterThresholdValidate", "thresholdValidatePattern", "thresholdValidateMax", "msgValidate", "badInterval", "sponsorObjectTypeNotFound", "sponsorObjectRoleNotFound", "sponsorObjectGroupNotFound", "participantObjectTypeNotFound", "participantObjectRoleNotFound", "participantObjectGroupNotFound", "thresholdNotFound", "notifierObjectTypeNotFound", "notifierObjectRoleNotFound", "notifierObjectGroupNotFound", "notifierUserNotFound", "eventCodeExists", "warningCondition", "controlTime", "startTime", "entTime", "eventCategory", "planeCode", "participantType", "participantRole", "participantGroup", "sponsorType", "sponsorRole", "sponsorGroup", "notifierType", "notifierRole", "notifierGroup", "notifierUser", "notifierMsg", "addControlTime", "planeSetting", "sponsorTypeSetting", "sponsorRoleSetting", "sponsorGroupSetting", "participantTypeSetting", "participantRoleSetting", "participantGroupSetting", "notifierTypeSetting", "notifierRoleSetting", "notifierGroupSetting", "notifierUserSetting", "thresholdValidate", "searchEventTemplate", "searchSelectTemplate", "eventNotFound", "taskConflicts", "key", "value", "defaultValue", "min", "max", "max<PERSON><PERSON><PERSON>", "unit", "description", "schemaGroupAPI", "schemaGroupLogging", "schemaGroupHelp", "schemaGroupUser", "schemaGroupEnvironment", "schemaGroupPositioning", "schemaGroupConnection", "schemaGroupWeb", "descReserveJournalDuration", "descStationOfflinePeriod", "descDeviceOfflinePeriod", "descDeviceOtaRssiThreshold", "descGatewayOfflinePeriod", "descSmtpEnable", "descSmtpHost", "descSmtpPort", "descSmtpUsername", "descSmtpPassword", "descHelpTaskDelayPeriod", "descHelpTurnOffDeviceTimer", "descHelpTurnOffDeviceRetryCount", "descHelpTaskMinRssiThreshold", "descUserRegisterCodeExpirePeriod", "descUserRegisterCodeLength", "descApiAccessTokenExpirePeriod", "descApiRefreshTokenExpirePeriod", "descApiDefaultSearchActive", "descApiDefaultMaxSize", "descReportCachePeriod", "descTrackingKalmanFilterEnable", "descTrackingKalmanFilterMeterRangePerSecond", "descTrackingKalmanFilterAccuracy", "descLocationKalmanFilterEnable", "descLocationKalmanProcessNoise", "descLocationKalmanMeasurementNoise", "descPositionPersistencePeriod", "descPositionHistoryPersistencePeriod", "descPositioningAlgorithmV2", "descPositioningPeroid", "descPositionMinRssiThreshold", "descTrackingRegionTotalCount", "descTrackingRegionMatchCount", "descMonitorRefreshSecond", "descStayTimeoutDefaultInterval", "descHistoryMatchCount", "ipAddr", "ipError", "version", "status", "checkAt", "apps", "posParameterCode", "deviceType", "displayStations", "monitor", "object", "configSetting", "device", "selectDelete", "add", "cancelAdd", "cancelEdit", "saveAdd", "saveEdit", "sid", "isAlive", "picEdit", "p<PERSON><PERSON><PERSON><PERSON>", "picUpload", "picConfirm", "planeEdit", "planeSet", "picBackgroundUpload", "iconPicEdit", "iconPicUpload", "objectDeviceConfirm", "record", "recordPlaceholder", "eventAlarm", "eventAlarmWindow", "eventStatus", "eventClean", "eventCleanConfirm", "eventStartTime", "emergencyEvent", "objectName", "deviceMac", "occupy", "selectDeleteError", "abnormalDevice", "second", "deviceConnectionAbnormal", "deviceConnectionAbnormalLatestEvent", "m20", "selectMonitorPlane", "selectMonitorObject", "m21", "selectService", "eFence", "confirmEdit", "fall", "normal", "positionStation", "allPositionStation", "enterAndExit", "enter", "exit", "m01", "addSpecialStatus", "<PERSON><PERSON><PERSON>", "region", "timeoutSetting", "specialStatus", "baby", "numberControl", "lowBattery", "lossSignal", "wetUrineTimeout", "noPatient", "hasPatient", "babyCare", "removeSpecialStatus", "removeSpecialStatusConfirm", "babyMove", "unknownStation", "specialEvent", "babyRegion", "diaperNormal", "babyTagLowBattery", "motherTagLowBattery", "monitorOutside", "emptyBed", "enterSetting", "entering", "eventId", "eventNo", "alertCondition", "less", "battery", "minsSignal", "addMon", "objectAttr", "addBaby", "resetDiaperComfortable", "mon", "baby2", "propertySetting", "objectSetting", "stationSetting", "stationSelect", "monRoom", "babyRoom", "controlArea", "warningArea", "normalArea", "objectName2", "objectType", "addBed", "bed", "toilet", "addRegion", "regionType", "showRegion", "addSubRegion", "m03", "overtemperature", "wristbandLowBattery", "bodyOvertemperature", "negativePressureIsolationCenter", "nowTemperature", "nowHeartRate", "nowBloodOxygen", "nowSbp", "nowDbp", "temperature", "temperaturePicAndNumerical", "temperatureColor", "remind", "bodyTemperatureWarm", "bodyTemperatureDetect", "heartRateDetect", "bloodOxygenDetect", "sbpDetect", "dbpDetect", "sbp", "dbp", "greater", "greaterThanOrEqual", "lessThanOrEqual", "degree", "timesPerMin", "negativePressureIsolationRoom", "editConfirm", "bodyPhysicalDataSetting", "continuousMonitoringDuration", "nonContinuousMonitoringAbnormalCritical", "continuousMonitoring", "nonContinuousMonitoring", "times", "alert", "abnormal", "critical", "bloodOxygen", "bloodPressure", "heartRate", "bodyTemperature", "backgroundColor", "deviceLowBattery", "lowTemperature", "overTemperature", "lowHeartRate", "overHeartRate", "lowBloodOxygen", "overBloodPressure", "lowBloodPressure", "overBloodPressureSbp", "lowBloodPressureSbp", "overBloodPressureDbp", "lowBloodPressureDbp", "sensorType", "current", "history", "lying", "sit", "natureCall", "now", "breathe", "breathe5MinsRecord", "bpm", "mins", "correct", "notCorrect", "empty", "breathDetect", "stopDetect", "eventCondition", "connectStatus", "connecting", "nowObject", "allObject", "beforeIndex", "afterIndex", "searchCondition", "eventEdit", "sponsorObjectType", "preFall", "m02", "stationID", "employeeNo", "employeeName", "phoneNo", "enable", "close", "modifyTime", "enterControlArea", "notice", "install", "remove", "leaveControlArea", "enableAirwatch", "m05", "m27"], "mappings": "AAAA;AACA,OAAO,IAAMA,MAAM,GAAG;AACpBC,EAAAA,IAAI,EAAE;AACJ;AACAC,IAAAA,MAAM,EAAE;AACN;AACAC,MAAAA,aAAa,EAAE,oBAFT;AAGNC,MAAAA,sBAAsB,EAAE,kBAHlB;AAINC,MAAAA,qBAAqB,EAAE,6BAJjB;AAKNC,MAAAA,UAAU,EAAE,eALN;AAMNC,MAAAA,SAAS,EAAE,MANL;AAONC,MAAAA,SAAS,EAAE,MAPL;AAQNC,MAAAA,SAAS,EAAE,MARL;AASNC,MAAAA,SAAS,EAAE,MATL;AAUNC,MAAAA,SAAS,EAAE,MAVL;AAWNC,MAAAA,SAAS,EAAE,MAXL;AAYNC,MAAAA,SAAS,EAAE,MAZL;AAaNC,MAAAA,SAAS,EAAE,KAbL;AAcN;AACAC,MAAAA,sBAAsB,EAAC,yBAfjB;AAgBNC,MAAAA,aAAa,EAAC,eAhBR;AAiBN;AACAC,MAAAA,YAAY,EAAC,eAlBP;AAmBNC,MAAAA,WAAW,EAAC,eAnBN;AAoBNC,MAAAA,+BAA+B,EAAE,eApB3B;AAqBNC,MAAAA,kBAAkB,EAAE,qBArBd;AAsBNC,MAAAA,iBAAiB,EAAE,oBAtBb;AAuBNC,MAAAA,OAAO,EAAE,SAvBH;AAwBNC,MAAAA,YAAY,EAAE,cAxBR;AAyBNC,MAAAA,MAAM,EAAE,QAzBF;AA0BNC,MAAAA,KAAK,EAAE,OA1BD;AA2BN;AACAC,MAAAA,IAAI,EAAE,MA5BA;AA6BNC,MAAAA,MAAM,EAAE,QA7BF;AA8BNC,MAAAA,MAAM,EAAE,QA9BF;AA+BNC,MAAAA,WAAW,EAAE,iBA/BP;AAgCNC,MAAAA,cAAc,EAAE,KAhCV;AAiCNC,MAAAA,MAAM,EAAE,QAjCF;AAkCNC,MAAAA,MAAM,EAAE,QAlCF;AAmCNC,MAAAA,UAAU,EAAE,UAnCN;AAoCNC,MAAAA,IAAI,EAAE,MApCA;AAqCNC,MAAAA,OAAO,EAAE,MArCH;AAsCNC,MAAAA,MAAM,EAAE,QAtCF;AAuCNC,MAAAA,QAAQ,EAAE,MAvCJ;AAwCNC,MAAAA,KAAK,EAAE,OAxCD;AAyCNC,MAAAA,IAAI,EAAE,MAzCA;AA0CNC,MAAAA,MAAM,EAAE,QA1CF;AA2CNC,MAAAA,OAAO,EAAE,MA3CH;AA4CNC,MAAAA,KAAK,EAAE,OA5CD;AA6CNC,MAAAA,GAAG,EAAE,MA7CC;AA8CNC,MAAAA,IAAI,EAAE,MA9CA;AA+CNC,MAAAA,IAAI,EAAE,MA/CA;AAgDNC,MAAAA,SAAS,EAAE,WAhDL;AAiDNC,MAAAA,UAAU,EAAE,mBAjDN;AAkDNC,MAAAA,YAAY,EAAE,sBAlDR;AAmDNC,MAAAA,YAAY,EAAE,gCAnDR;AAoDNC,MAAAA,YAAY,EAAE,eApDR;AAqDNC,MAAAA,YAAY,EAAE,cArDR;AAsDNC,MAAAA,eAAe,EAAE,0BAtDX;AAuDNC,MAAAA,aAAa,EAAE,wBAvDT;AAwDNC,MAAAA,oBAAoB,EAAE,iBAxDhB;AAyDNC,MAAAA,aAAa,EAAE,aAzDT;AA0DNC,MAAAA,MAAM,EAAE,QA1DF;AA2DNC,MAAAA,GAAG,EAAE,KA3DC;AA4DNC,MAAAA,MAAM,EAAE,QA5DF;AA6DNC,MAAAA,SAAS,EAAE,UA7DL;AA8DNC,MAAAA,SAAS,EAAE,WA9DL;AA+DNC,MAAAA,iBAAiB,EAAE,WA/Db;AAgENC,MAAAA,aAAa,EAAE,wBAhET;AAiENC,MAAAA,iBAAiB,EAAE,mCAjEb;AAkEN;AACAC,MAAAA,MAAM,EAAE,QAnEF;AAoENC,MAAAA,mBAAmB,EAAE,mCApEf;AAqENC,MAAAA,gBAAgB,EAAE,+DArEZ;AAsENC,MAAAA,gBAAgB,EAAE,mBAtEZ;AAuENC,MAAAA,oBAAoB,EAAE,qBAvEhB;AAwENC,MAAAA,aAAa,EAAE,oBAxET;AAyENC,MAAAA,iBAAiB,EAAE,iBAzEb;AA0ENC,MAAAA,gBAAgB,EAAE,mBA1EZ;AA2ENC,MAAAA,gBAAgB,EAAE,mBA3EZ;AA4ENC,MAAAA,gBAAgB,EAAE,mBA5EZ;AA6ENC,MAAAA,gBAAgB,EAAE,mBA7EZ;AA8ENC,MAAAA,cAAc,EAAE,iBA9EV;AA+ENC,MAAAA,UAAU,EAAE,eA/EN;AAgFNC,MAAAA,UAAU,EAAE,eAhFN;AAiFN;AACAC,MAAAA,oBAAoB,EAAE,oBAlFhB;AAmFNC,MAAAA,YAAY,EAAE,eAnFR;AAoFNC,MAAAA,QAAQ,EAAE,UApFJ;AAqFNC,MAAAA,SAAS,EAAE,WArFL;AAsFNC,MAAAA,iBAAiB,EAAE,iBAtFb;AAuFNC,MAAAA,MAAM,EAAE,QAvFF;AAwFNC,MAAAA,2BAA2B,EAAE,mBAxFvB;AAyFNC,MAAAA,sBAAsB,EAAE,cAzFlB;AA0FNC,MAAAA,2BAA2B,EAAE,sBA1FvB;AA2FNC,MAAAA,aAAa,EAAE,6CA3FT;AA4FNC,MAAAA,qBAAqB,EAAE,yFA5FjB;AA6FNC,MAAAA,mBAAmB,EAAE,uFA7Ff;AA8FNC,MAAAA,sBAAsB,EAAE,oDA9FlB;AA+FNC,MAAAA,kBAAkB,EAAE,wEA/Fd;AAgGNC,MAAAA,+BAA+B,EAAE,gDAhG3B;AAiGNC,MAAAA,6BAA6B,EAAE,6DAjGzB;AAkGNC,MAAAA,oBAAoB,EAAE,sDAlGhB;AAmGNC,MAAAA,SAAS,EAAE,YAnGL;AAoGNC,MAAAA,SAAS,EAAE,KApGL;AAqGNC,MAAAA,UAAU,EAAE,aArGN;AAsGNC,MAAAA,UAAU,EAAE,YAtGN;AAuGNC,MAAAA,iBAAiB,EAAE,gCAvGb;AAwGNC,MAAAA,OAAO,EAAE,SAxGH;AAyGNC,MAAAA,KAAK,EAAE,OAzGD;AA0GNC,MAAAA,aAAa,EAAE,cA1GT;AA2GNC,MAAAA,0BAA0B,EAAE,2CA3GtB;AA4GNC,MAAAA,gCAAgC,EAAE,uCA5G5B;AA6GNC,MAAAA,kBAAkB,EAAE,mCA7Gd;AA8GNC,MAAAA,oBAAoB,EAAE,4DA9GhB;AA+GNC,MAAAA,+BAA+B,EAAE,0CA/G3B;AAgHNC,MAAAA,cAAc,EAAE,0CAhHV;AAiHNC,MAAAA,wBAAwB,EAAE,+GAjHpB;AAkHNC,MAAAA,0BAA0B,EAAE,6GAlHtB;AAmHNC,MAAAA,uBAAuB,EAAE,wEAnHnB;AAoHNC,MAAAA,oBAAoB,EAAE,oBApHhB;AAqHNC,MAAAA,iBAAiB,EAAE,gBArHb;AAsHNC,MAAAA,uBAAuB,EAAE,kDAtHnB;AAuHNC,MAAAA,uBAAuB,EAAE,oDAvHnB;AAwHNC,MAAAA,oBAAoB,EAAE,2BAxHhB;AAyHNC,MAAAA,qBAAqB,EAAE,qBAzHjB;AA0HNC,MAAAA,kBAAkB,EAAE,2DA1Hd;AA2HNC,MAAAA,qBAAqB,EAAE,sEA3HjB;AA4HNC,MAAAA,yBAAyB,EAAE,iCA5HrB;AA6HNC,MAAAA,UAAU,EAAE,mCA7HN;AA8HNC,MAAAA,iBAAiB,EAAE,qBA9Hb;AA+HNC,MAAAA,kBAAkB,EAAE,4BA/Hd;AAgINC,MAAAA,oBAAoB,EAAE,oCAhIhB;AAiINC,MAAAA,mBAAmB,EAAE,uCAjIf;AAkINC,MAAAA,gBAAgB,EAAE,wGAlIZ;AAmINC,MAAAA,aAAa,EAAE,sCAnIT;AAoINC,MAAAA,aAAa,EAAE,oBApIT;AAqINC,MAAAA,sBAAsB,EAAE,6EArIlB;AAsINC,MAAAA,UAAU,EAAE,yBAtIN;AAuINC,MAAAA,WAAW,EAAE,aAvIP;AAwINC,MAAAA,KAAK,EAAE,OAxID;AAyINC,MAAAA,UAAU,EAAE,YAzIN;AA0INC,MAAAA,YAAY,EAAE,iBA1IR;AA2INC,MAAAA,YAAY,EAAE,eA3IR;AA4IN;AACAC,MAAAA,gBAAgB,EAAE,aA7IZ;AA8INC,MAAAA,YAAY,EAAE,IA9IR;AA+INC,MAAAA,IAAI,EAAE,MA/IA;AAgJNC,MAAAA,QAAQ,EAAE,UAhJJ;AAiJNC,MAAAA,IAAI,EAAE,MAjJA;AAkJNC,MAAAA,KAAK,EAAE,OAlJD;AAmJNC,MAAAA,eAAe,EAAE,kBAnJX;AAoJNC,MAAAA,qBAAqB,EAAE,sBApJjB;AAqJNC,MAAAA,UAAU,EAAE,cArJN;AAsJNC,MAAAA,SAAS,EAAE,WAtJL;AAuJNC,MAAAA,KAAK,EAAE,UAvJD;AAwJNC,MAAAA,mBAAmB,EAAE,gBAxJf;AAyJNC,MAAAA,WAAW,EAAE,cAzJP;AA0JNC,MAAAA,SAAS,EAAE,kBA1JL;AA2JNC,MAAAA,QAAQ,EAAE,KA3JJ;AA4JNC,MAAAA,UAAU,EAAE,QA5JN;AA6JNC,MAAAA,eAAe,EAAE,uBA7JX;AA8JNC,MAAAA,mBAAmB,EAAE,uBA9Jf;AA+JN;AACAC,MAAAA,UAAU,EAAE,sBAhKN;AAiKNC,MAAAA,eAAe,EAAE,kBAjKX;AAkKNC,MAAAA,gBAAgB,EAAE,gBAlKZ;AAmKNC,MAAAA,SAAS,EAAE,0BAnKL;AAoKNC,MAAAA,SAAS,EAAE,yBApKL;AAqKNC,MAAAA,SAAS,EAAE,8BArKL;AAsKNC,MAAAA,SAAS,EAAE,sBAtKL;AAuKNC,MAAAA,SAAS,EAAE,kBAvKL;AAwKNC,MAAAA,iBAAiB,EAAE,sBAxKb;AAyKNC,MAAAA,iBAAiB,EAAE,yBAzKb;AA0KNC,MAAAA,cAAc,EAAE,sBA1KV;AA2KNC,MAAAA,cAAc,EAAE,cA3KV;AA4KNC,MAAAA,mBAAmB,EAAE,mBA5Kf;AA6KNC,MAAAA,aAAa,EAAE,cA7KT;AA8KNC,MAAAA,WAAW,EAAE,kBA9KP;AA+KNC,MAAAA,MAAM,EAAE,iBA/KF;AAgLNC,MAAAA,KAAK,EAAE,gBAhLD;AAiLNC,MAAAA,EAAE,EAAE,IAjLE;AAkLNC,MAAAA,cAAc,EAAE,iBAlLV;AAmLNC,MAAAA,cAAc,EAAE,8BAnLV;AAoLNC,MAAAA,UAAU,EAAE,iBApLN;AAqLNC,MAAAA,iBAAiB,EAAE,iBArLb;AAsLNC,MAAAA,eAAe,EAAE,2BAtLX;AAuLNC,MAAAA,YAAY,EAAE,gBAvLR;AAwLNC,MAAAA,SAAS,EAAE,YAxLL;AAyLNC,MAAAA,iBAAiB,EAAE,oBAzLb;AA0LNC,MAAAA,aAAa,EAAE,gBA1LT;AA2LNC,MAAAA,iBAAiB,EAAE,mBA3Lb;AA4LNC,MAAAA,aAAa,EAAE,gBA5LT;AA6LNC,MAAAA,YAAY,EAAE,eA7LR;AA8LNC,MAAAA,SAAS,EAAE,kBA9LL;AA+LNC,MAAAA,gBAAgB,EAAE,mBA/LZ;AAgMNC,MAAAA,YAAY,EAAE,eAhMR;AAiMNC,MAAAA,eAAe,EAAE,gBAjMX;AAkMNC,MAAAA,MAAM,EAAE,QAlMF;AAmMNC,MAAAA,KAAK,EAAE,OAnMD;AAoMN;AACAC,MAAAA,SAAS,EAAE,cArML;AAsMNC,MAAAA,cAAc,EAAE,uBAtMV;AAuMNC,MAAAA,cAAc,EAAE,sBAvMV;AAwMNC,MAAAA,kBAAkB,EAAE,gBAxMd;AAyMNC,MAAAA,iBAAiB,EAAE,qBAzMb;AA0MNC,MAAAA,sBAAsB,EAAE,0BA1MlB;AA2MNC,MAAAA,WAAW,EAAE,uBA3MP;AA4MNC,MAAAA,YAAY,EAAE,mBA5MR;AA6MNC,MAAAA,QAAQ,EAAE,qBA7MJ;AA8MNC,MAAAA,aAAa,EAAE,wBA9MT;AA+MNC,MAAAA,gBAAgB,EAAE,oBA/MZ;AAgNNC,MAAAA,aAAa,EAAE,2BAhNT;AAiNNC,MAAAA,iBAAiB,EAAE,0BAjNb;AAkNNC,MAAAA,cAAc,EAAE,wBAlNV;AAmNNC,MAAAA,KAAK,EAAE,mBAnND;AAoNNC,MAAAA,cAAc,EAAE,4BApNV;AAqNNC,MAAAA,QAAQ,EAAE,YArNJ;AAsNN;AACAC,MAAAA,IAAI,EAAE,MAvNA;AAwNNC,MAAAA,SAAS,EAAE,eAxNL;AAyNNC,MAAAA,eAAe,EAAE,YAzNX;AA0NNC,MAAAA,OAAO,EAAE,QA1NH;AA2NNC,MAAAA,SAAS,EAAE,QA3NL;AA4NNC,MAAAA,GAAG,EAAE,KA5NC;AA6NNC,MAAAA,SAAS,EAAE,YA7NL;AA8NNC,MAAAA,WAAW,EAAE,MA9NP;AA+NNC,MAAAA,KAAK,EAAE,SA/ND;AAgONC,MAAAA,qBAAqB,EAAE,wBAhOjB;AAiONC,MAAAA,qBAAqB,EAAE,wBAjOjB;AAkONC,MAAAA,IAAI,EAAE,MAlOA;AAmONC,MAAAA,SAAS,EAAE,YAnOL;AAoONC,MAAAA,OAAO,EAAE,SApOH;AAqONC,MAAAA,QAAQ,EAAE,UArOJ;AAsONC,MAAAA,KAAK,EAAE,OAtOD;AAuONC,MAAAA,QAAQ,EAAE,WAvOJ;AAwONC,MAAAA,WAAW,EAAE,cAxOP;AAyONC,MAAAA,YAAY,EAAE,eAzOR;AA0ONC,MAAAA,SAAS,EAAE,YA1OL;AA2ONC,MAAAA,aAAa,EAAE,iBA3OT;AA4ONC,MAAAA,UAAU,EAAE,YA5ON;AA4OoB;AAC1BC,MAAAA,UAAU,EAAE,YA7ON;AA8ONC,MAAAA,aAAa,EAAE,QA9OT;AA+ONC,MAAAA,OAAO,EAAE,UA/OH;AAgPNC,MAAAA,SAAS,EAAE,YAhPL;AAiPNC,MAAAA,mBAAmB,EAAE,qBAjPf;AAkPNC,MAAAA,kBAAkB,EAAE,oBAlPd;AAmPNC,MAAAA,SAAS,EAAE,SAnPL;AAoPNC,MAAAA,OAAO,EAAE,6CApPH;AAqPNC,MAAAA,QAAQ,EAAE,MArPJ;AAsPNC,MAAAA,aAAa,EAAE,eAtPT;AAuPNC,MAAAA,SAAS,EAAE,iBAvPL;AAwPNC,MAAAA,SAAS,EAAE,mBAxPL;AAyPNC,MAAAA,cAAc,EAAE,gCAzPV;AA0PNC,MAAAA,YAAY,EAAE,wBA1PR;AA2PNC,MAAAA,kBAAkB,EAAE,oCA3Pd;AA4PNC,MAAAA,WAAW,EAAE,qBA5PP;AA6PNC,MAAAA,WAAW,EAAE,eA7PP;AA8PNC,MAAAA,WAAW,EAAE,aA9PP;AA+PNC,MAAAA,UAAU,EAAE,0BA/PN;AAgQNC,MAAAA,mBAAmB,EAAE,2CAhQf;AAiQNC,MAAAA,cAAc,EAAE,8BAjQV;AAkQNC,MAAAA,eAAe,EAAE,wBAlQX;AAmQNC,MAAAA,MAAM,EAAE,mBAnQF;AAoQNC,MAAAA,kBAAkB,EAAE,2CApQd;AAqQNC,MAAAA,aAAa,EAAE,2DArQT;AAsQNC,MAAAA,WAAW,EAAE,uBAtQP;AAuQNC,MAAAA,UAAU,EAAE,oBAvQN;AAwQNC,MAAAA,UAAU,EAAE,aAxQN;AAyQNC,MAAAA,GAAG,EAAE,KAzQC;AA0QNC,MAAAA,GAAG,EAAE,IA1QC;AA2QNC,MAAAA,IAAI,EAAE,MA3QA;AA4QNC,MAAAA,QAAQ,EAAE,UA5QJ;AA6QNC,MAAAA,OAAO,EAAE,SA7QH;AA8QNC,MAAAA,UAAU,EAAE,aA9QN;AA+QNC,MAAAA,aAAa,EAAE,gBA/QT;AAgRNC,MAAAA,eAAe,EAAE,mBAhRX;AAiRNC,MAAAA,gBAAgB,EAAE,uBAjRZ;AAkRNC,MAAAA,gBAAgB,EAAE,mBAlRZ;AAmRNC,MAAAA,UAAU,EAAE,aAnRN;AAoRNC,MAAAA,cAAc,EAAE,kBApRV;AAqRNC,MAAAA,WAAW,EAAE;AArRP,KAFJ;AAyRJ;AACAC,IAAAA,KAAK,EAAE;AACLC,MAAAA,OAAO,EAAE,SADJ;AAELC,MAAAA,QAAQ,EAAE,UAFL;AAGLC,MAAAA,aAAa,EAAE,gBAHV;AAILH,MAAAA,KAAK,EAAE,OAJF;AAKLI,MAAAA,2BAA2B,EAAE,kBALxB;AAMLC,MAAAA,4BAA4B,EAAE,oBANzB;AAOLC,MAAAA,2BAA2B,EAAE;AAPxB,KA1RH;AAmSJ;AACAH,IAAAA,aAAa,EAAE;AACbA,MAAAA,aAAa,EAAE,gBADF;AAEbI,MAAAA,0BAA0B,EAAE,wCAFf;AAGbC,MAAAA,gBAAgB,EAAE,cAHL;AAIbC,MAAAA,qBAAqB,EAAE,sBAJV;AAKbC,MAAAA,IAAI,EAAE,QALO;AAMbC,MAAAA,6BAA6B,EAAE,2BANlB;AAObC,MAAAA,kCAAkC,EAAE,mCAPvB;AAQbC,MAAAA,4BAA4B,EAAE,+FARjB;AASbC,MAAAA,eAAe,EAAE,gDATJ;AAUbC,MAAAA,uBAAuB,EAAE,iDAVZ;AAWbC,MAAAA,UAAU,EAAE,yBAXC;AAYbC,MAAAA,UAAU,EAAE,mCAZC;AAabC,MAAAA,yBAAyB,EAAE,2BAbd;AAcbC,MAAAA,qBAAqB,EAAE,wBAdV;AAebC,MAAAA,yBAAyB,EAAE,8CAfd;AAgBbC,MAAAA,UAAU,EAAE,sCAhBC;AAiBbC,MAAAA,mBAAmB,EAAE,4CAjBR;AAkBbC,MAAAA,qBAAqB,EAAE;AAlBV,KApSX;AAwTJrN,IAAAA,QAAQ,EAAE;AACR+L,MAAAA,OAAO,EAAE,SADD;AAERrI,MAAAA,IAAI,EAAE,MAFE;AAGR4J,MAAAA,KAAK,EAAE,OAHC;AAIRC,MAAAA,KAAK,EAAE,OAJC;AAKR3J,MAAAA,IAAI,EAAE,MALE;AAMR4J,MAAAA,2BAA2B,EAAE,kDANrB;AAORtB,MAAAA,2BAA2B,EAAE,kBAPrB;AAQRuB,MAAAA,wBAAwB,EAAE,eARlB;AASRC,MAAAA,oBAAoB,EAAE,6CATd;AAURV,MAAAA,yBAAyB,EAAE,gBAVnB;AAWRC,MAAAA,qBAAqB,EAAE,wBAXf;AAYRU,MAAAA,yBAAyB,EAAE,gBAZnB;AAaRC,MAAAA,wBAAwB,EAAE,oBAblB;AAcRC,MAAAA,4BAA4B,EAAE;AAdtB,KAxTN;AAwUJ;AACA7H,IAAAA,eAAe,EAAE;AACf8H,MAAAA,WAAW,EAAE,cADE;AAEfC,MAAAA,UAAU,EAAE,cAFG;AAGfC,MAAAA,SAAS,EAAE,UAHI;AAIfC,MAAAA,aAAa,EAAE,gBAJA;AAKfC,MAAAA,kBAAkB,EAAE,0BALL;AAMfC,MAAAA,cAAc,EAAE,0BAND;AAOfC,MAAAA,cAAc,EAAE,yBAPD;AAQfC,MAAAA,cAAc,EAAE,2BARD;AASfC,MAAAA,oBAAoB,EAAE,yBATP;AAUfC,MAAAA,mBAAmB,EAAE,4BAVN;AAWfC,MAAAA,yBAAyB,EAAE,0BAXZ;AAYf1D,MAAAA,WAAW,EAAE,0BAZE;AAaf2D,MAAAA,UAAU,EAAE,2BAbG;AAcfC,MAAAA,eAAe,EAAE,0BAdF;AAefC,MAAAA,aAAa,EAAE,wBAfA;AAgBfC,MAAAA,iBAAiB,EAAE,2BAhBJ;AAiBfC,MAAAA,iBAAiB,EAAE,2BAjBJ;AAkBfC,MAAAA,kBAAkB,EAAE,4BAlBL;AAmBfC,MAAAA,qBAAqB,EAAE,gCAnBR;AAoBfC,MAAAA,qBAAqB,EAAE,gCApBR;AAqBfC,MAAAA,sBAAsB,EAAE,iCArBT;AAsBfC,MAAAA,kBAAkB,EAAE,6BAtBL;AAuBfC,MAAAA,kBAAkB,EAAE,6BAvBL;AAwBfC,MAAAA,mBAAmB,EAAE,kCAxBN;AAyBfC,MAAAA,qBAAqB,EAAE,oCAzBR;AA0BfC,MAAAA,gBAAgB,EAAE,mCA1BH;AA2BfC,MAAAA,iBAAiB,EAAE,2CA3BJ;AA4BfC,MAAAA,SAAS,EAAE,4BA5BI;AA6BfC,MAAAA,KAAK,EAAE,OA7BQ;AA8BfC,MAAAA,IAAI,EAAE,MA9BS;AA+BfC,MAAAA,QAAQ,EAAE,WA/BK;AAgCfC,MAAAA,iBAAiB,EAAE,iBAhCJ;AAiCfC,MAAAA,QAAQ,EAAE,wBAjCK;AAkCfC,MAAAA,SAAS,EAAE,yBAlCI;AAmCfC,MAAAA,SAAS,EAAE,uBAnCI;AAoCfC,MAAAA,OAAO,EAAE,SApCM;AAqCfxI,MAAAA,gBAAgB,EAAE,oBArCH;AAsCfyI,MAAAA,oBAAoB,EAAE,oBAtCP;AAuCfC,MAAAA,EAAE,EAAE,GAvCW;AAwCfC,MAAAA,GAAG,EAAE,IAxCU;AAyCfC,MAAAA,EAAE,EAAE,GAzCW;AA0CfC,MAAAA,GAAG,EAAE,IA1CU;AA2CfC,MAAAA,oBAAoB,EAAE,QA3CP;AA4CfC,MAAAA,kBAAkB,EAAE,WA5CL;AA6CfC,MAAAA,WAAW,EAAE,2BA7CE;AA8CfC,MAAAA,SAAS,EAAE,yBA9CI;AA+CfC,MAAAA,oBAAoB,EAAE,cA/CP;AAgDfC,MAAAA,mBAAmB,EAAE,OAhDN;AAiDfC,MAAAA,sBAAsB,EAAE,UAjDT;AAkDfC,MAAAA,yBAAyB,EAAE,iBAlDZ;AAmDfC,MAAAA,iBAAiB,EAAE,4CAnDJ;AAoDfC,MAAAA,gBAAgB,EAAE,0BApDH;AAqDfC,MAAAA,eAAe,EAAE,2CArDF;AAsDfC,MAAAA,mBAAmB,EAAE,gDAtDN;AAuDfC,MAAAA,sBAAsB,EAAE,uBAvDT;AAwDfC,MAAAA,iBAAiB,EAAE,uDAxDJ;AAyDfC,MAAAA,sBAAsB,EAAE,yBAzDT;AA0DfC,MAAAA,iBAAiB,EAAE,iDA1DJ;AA2DfC,MAAAA,gBAAgB,EAAE,0BA3DH;AA4DfC,MAAAA,sBAAsB,EAAE,iCA5DT;AA6DfC,MAAAA,wBAAwB,EAAE,uDA7DX;AA8DfC,MAAAA,oBAAoB,EAAE,2DA9DP;AA+DfC,MAAAA,WAAW,EAAE,kEA/DE;AAgEfC,MAAAA,WAAW,EAAE,oEAhEE;AAiEfC,MAAAA,yBAAyB,EAAE,gEAjEZ;AAkEfC,MAAAA,yBAAyB,EAAE,gEAlEZ;AAmEfC,MAAAA,0BAA0B,EAAE,iEAnEb;AAoEfC,MAAAA,6BAA6B,EAAE,uEApEhB;AAqEfC,MAAAA,6BAA6B,EAAE,uEArEhB;AAsEfC,MAAAA,8BAA8B,EAAE,wEAtEjB;AAuEfC,MAAAA,iBAAiB,EAAE,wEAvEJ;AAwEfC,MAAAA,0BAA0B,EAAE,wEAxEb;AAyEfC,MAAAA,0BAA0B,EAAE,wEAzEb;AA0EfC,MAAAA,2BAA2B,EAAE,yEA1Ed;AA2EfC,MAAAA,oBAAoB,EAAE,2EA3EP;AA4EfC,MAAAA,eAAe,EAAE,+DA5EF;AA6EfC,MAAAA,gBAAgB,EAAE,mBA7EH;AA8EfC,MAAAA,WAAW,EAAE,cA9EE;AA+EfC,MAAAA,SAAS,EAAE,YA/EI;AAgFfC,MAAAA,OAAO,EAAE,UAhFM;AAiFfC,MAAAA,aAAa,EAAE,YAjFA;AAkFfC,MAAAA,SAAS,EAAE,WAlFI;AAmFfC,MAAAA,eAAe,EAAE,kBAnFF;AAoFfC,MAAAA,eAAe,EAAE,kBApFF;AAqFfC,MAAAA,gBAAgB,EAAE,mBArFH;AAsFfC,MAAAA,WAAW,EAAE,aAtFE;AAuFfC,MAAAA,WAAW,EAAE,aAvFE;AAwFfC,MAAAA,YAAY,EAAE,cAxFC;AAyFfC,MAAAA,YAAY,EAAE,mBAzFC;AA0FfC,MAAAA,YAAY,EAAE,mBA1FC;AA2FfC,MAAAA,aAAa,EAAE,oBA3FA;AA4FfC,MAAAA,YAAY,EAAE,sBA5FC;AA6FfC,MAAAA,WAAW,EAAE,sBA7FE;AA8FfC,MAAAA,cAAc,EAAE,oCA9FD;AA+FfC,MAAAA,YAAY,EAAE,cA/FC;AAgGfC,MAAAA,kBAAkB,EAAE,sBAhGL;AAiGfC,MAAAA,kBAAkB,EAAE,sBAjGL;AAkGfC,MAAAA,mBAAmB,EAAE,uBAlGN;AAmGfC,MAAAA,sBAAsB,EAAE,2BAnGT;AAoGfC,MAAAA,sBAAsB,EAAE,2BApGT;AAqGfC,MAAAA,uBAAuB,EAAE,4BArGV;AAsGfC,MAAAA,mBAAmB,EAAE,4BAtGN;AAuGfC,MAAAA,mBAAmB,EAAE,4BAvGN;AAwGfC,MAAAA,oBAAoB,EAAE,6BAxGP;AAyGfC,MAAAA,mBAAmB,EAAE,+BAzGN;AA0GfC,MAAAA,iBAAiB,EAAE,mDA1GJ;AA2GfC,MAAAA,mBAAmB,EAAE,gBA3GN;AA4GfC,MAAAA,oBAAoB,EAAE,wBA5GP;AA6GfC,MAAAA,aAAa,EAAE,uEA7GA;AA8GfC,MAAAA,aAAa,EAAE,0EA9GA;AA+Gf1L,MAAAA,QAAQ,EAAE;AA/GK,KAzUb;AA0bJ;AACArC,IAAAA,YAAY,EAAE;AACZ7C,MAAAA,KAAK,EAAE,OADK;AAEZ6Q,MAAAA,GAAG,EAAE,KAFO;AAGZC,MAAAA,KAAK,EAAE,OAHK;AAIZC,MAAAA,YAAY,EAAE,eAJF;AAKZC,MAAAA,GAAG,EAAE,KALO;AAMZC,MAAAA,GAAG,EAAE,KANO;AAOZC,MAAAA,SAAS,EAAE,YAPC;AAQZC,MAAAA,IAAI,EAAE,MARM;AASZC,MAAAA,WAAW,EAAE,aATD;AAUZC,MAAAA,cAAc,EAAE,KAVJ;AAWZC,MAAAA,kBAAkB,EAAE,KAXR;AAYZC,MAAAA,eAAe,EAAE,MAZL;AAaZC,MAAAA,eAAe,EAAE,MAbL;AAcZC,MAAAA,sBAAsB,EAAE,aAdZ;AAeZC,MAAAA,sBAAsB,EAAE,UAfZ;AAgBZC,MAAAA,qBAAqB,EAAE,YAhBX;AAiBZC,MAAAA,cAAc,EAAE,KAjBJ;AAkBZC,MAAAA,0BAA0B,EAAE,oCAlBhB;AAmBZC,MAAAA,wBAAwB,EAAE,kDAnBd;AAoBZC,MAAAA,uBAAuB,EAAE,iDApBb;AAqBZC,MAAAA,0BAA0B,EAAE,yBArBhB;AAsBZC,MAAAA,wBAAwB,EAAE,kDAtBd;AAuBZC,MAAAA,cAAc,EAAE,aAvBJ;AAwBZC,MAAAA,YAAY,EAAE,WAxBF;AAyBZC,MAAAA,YAAY,EAAE,WAzBF;AA0BZC,MAAAA,gBAAgB,EAAE,eA1BN;AA2BZC,MAAAA,gBAAgB,EAAE,eA3BN;AA4BZC,MAAAA,uBAAuB,EAAE,wBA5Bb;AA6BZC,MAAAA,0BAA0B,EAAE,gCA7BhB;AA8BZC,MAAAA,+BAA+B,EAAE,gCA9BrB;AA+BZC,MAAAA,4BAA4B,EAAE,8BA/BlB;AAgCZC,MAAAA,gCAAgC,EAAE,kCAhCtB;AAiCZC,MAAAA,0BAA0B,EAAE,2BAjChB;AAkCZC,MAAAA,8BAA8B,EAAE,gCAlCpB;AAmCZC,MAAAA,+BAA+B,EAAE,iCAnCrB;AAoCZC,MAAAA,0BAA0B,EAAE,qEApChB;AAqCZC,MAAAA,qBAAqB,EAAE,sCArCX;AAsCZC,MAAAA,qBAAqB,EAAE,qBAtCX;AAuCZC,MAAAA,8BAA8B,EAAE,+BAvCpB;AAwCZC,MAAAA,2CAA2C,EAAE,uDAxCjC;AAyCZC,MAAAA,gCAAgC,EAAE,kCAzCtB;AA0CZC,MAAAA,8BAA8B,EAAE,+BA1CpB;AA2CZC,MAAAA,8BAA8B,EAAE,gCA3CpB;AA4CZC,MAAAA,kCAAkC,EAAE,oCA5CxB;AA6CZC,MAAAA,6BAA6B,EAAE,6BA7CnB;AA8CZC,MAAAA,oCAAoC,EAAE,qCA9C1B;AA+CZC,MAAAA,0BAA0B,EAAE,0BA/ChB;AAgDZC,MAAAA,qBAAqB,EAAE,oBAhDX;AAiDZC,MAAAA,4BAA4B,EAAE,6BAjDlB;AAkDZC,MAAAA,4BAA4B,EAAE,8BAlDlB;AAmDZC,MAAAA,4BAA4B,EAAE,6BAnDlB;AAoDZC,MAAAA,wBAAwB,EAAE,wBApDd;AAqDZC,MAAAA,8BAA8B,EAAE,+BArDpB;AAsDZC,MAAAA,qBAAqB,EAAE,qBAtDX;AAuDZ1U,MAAAA,KAAK,EAAE;AAvDK,KA3bV;AAofJ;AACA2U,IAAAA,MAAM,EAAE;AACNC,MAAAA,OAAO,EAAE;AADH,KArfJ;AAwfJ;AACArR,IAAAA,eAAe,EAAE;AACfjD,MAAAA,IAAI,EAAE,MADS;AAEfuU,MAAAA,OAAO,EAAE,SAFM;AAGfC,MAAAA,MAAM,EAAE,QAHO;AAIfC,MAAAA,OAAO,EAAE;AAJM,KAzfb;AA+fJ;AACAC,IAAAA,IAAI,EAAE;AACJld,MAAAA,MAAM,EAAE;AACNmd,QAAAA,gBAAgB,EAAC,2BADX;AAENC,QAAAA,UAAU,EAAE,aAFN;AAGNC,QAAAA,eAAe,EAAC,kBAHV;AAINC,QAAAA,OAAO,EAAE,WAJH;AAKNC,QAAAA,MAAM,EAAE,QALF;AAMN/E,QAAAA,YAAY,EAAE,cANR;AAONgF,QAAAA,aAAa,EAAE,gBAPT;AAQNvb,QAAAA,OAAO,EAAE,SARH;AASNC,QAAAA,MAAM,EAAE,QATF;AAUNjB,QAAAA,+BAA+B,EAAE,sCAV3B;AAWNwc,QAAAA,MAAM,EAAE,QAXF;AAYNC,QAAAA,YAAY,EAAE,uBAZR;AAaNC,QAAAA,GAAG,EAAE,KAbC;AAcNtb,QAAAA,IAAI,EAAE,MAdA;AAeNP,QAAAA,MAAM,EAAE,QAfF;AAgBN8b,QAAAA,SAAS,EAAE,YAhBL;AAiBNC,QAAAA,UAAU,EAAE,aAjBN;AAkBNC,QAAAA,OAAO,EAAE,UAlBH;AAmBNC,QAAAA,QAAQ,EAAE,WAnBJ;AAoBNC,QAAAA,GAAG,EAAE,aApBC;AAqBNhV,QAAAA,KAAK,EAAE,OArBD;AAsBNiV,QAAAA,OAAO,EAAE,aAtBH;AAuBNC,QAAAA,OAAO,EAAE,yBAvBH;AAwBNC,QAAAA,SAAS,EAAE,2BAxBL;AAyBNC,QAAAA,SAAS,EAAE,2BAzBL;AA0BNC,QAAAA,UAAU,EAAE,4CA1BN;AA2BNC,QAAAA,SAAS,EAAE,MA3BL;AA4BNC,QAAAA,QAAQ,EAAE,WA5BJ;AA6BNC,QAAAA,mBAAmB,EAAE,2BA7Bf;AA8BNC,QAAAA,WAAW,EAAE,mBA9BP;AA+BNC,QAAAA,aAAa,EAAE,qBA/BT;AAgCNC,QAAAA,mBAAmB,EAAE,yDAhCf;AAiCN3B,QAAAA,MAAM,EAAE,QAjCF;AAkCN4B,QAAAA,MAAM,EAAE,QAlCF;AAmCNC,QAAAA,iBAAiB,EAAE,4BAnCb;AAoCNC,QAAAA,UAAU,EAAE,aApCN;AAqCNC,QAAAA,gBAAgB,EAAE,oBArCZ;AAsCNC,QAAAA,WAAW,EAAE,cAtCP;AAuCNC,QAAAA,UAAU,EAAE,WAvCN;AAwCNC,QAAAA,iBAAiB,EAAE,gCAxCb;AAyCNC,QAAAA,cAAc,EAAE,yBAzCV;AA0CNlX,QAAAA,WAAW,EAAE,cA1CP;AA2CNmX,QAAAA,cAAc,EAAE,iBA3CV;AA4CNC,QAAAA,UAAU,EAAE,MA5CN;AA6CNC,QAAAA,SAAS,EAAE,YA7CL;AA8CNC,QAAAA,MAAM,EAAE,MA9CF;AA+CNrX,QAAAA,KAAK,EAAE,OA/CD;AAgDN5G,QAAAA,MAAM,EAAE,QAhDF;AAiDNke,QAAAA,iBAAiB,EAAE,4BAjDb;AAkDNC,QAAAA,cAAc,EAAE,iBAlDV;AAmDNC,QAAAA,MAAM,EAAE,QAnDF;AAoDNC,QAAAA,wBAAwB,EAAE,yBApDpB;AAqDNC,QAAAA,mCAAmC,EAAE;AArD/B,OADJ;AAwDJC,MAAAA,GAAG,EAAE;AACHC,QAAAA,kBAAkB,EAAC,MADhB;AAEHC,QAAAA,mBAAmB,EAAE;AAFlB,OAxDD;AA4DJC,MAAAA,GAAG,EAAE;AACHC,QAAAA,aAAa,EAAC;AADX,OA5DD;AA+DJC,MAAAA,MAAM,EAAE;AACNtd,QAAAA,SAAS,EAAE,WADL;AAEN4F,QAAAA,IAAI,EAAE,MAFA;AAGNyH,QAAAA,IAAI,EAAE,MAHA;AAINwN,QAAAA,MAAM,EAAE,KAJF;AAKN0C,QAAAA,WAAW,EAAE,wBALP;AAMN7e,QAAAA,MAAM,EAAE,QANF;AAON8e,QAAAA,IAAI,EAAE,MAPA;AAQNC,QAAAA,MAAM,EAAE,QARF;AASNC,QAAAA,eAAe,EAAE,kBATX;AAUNpY,QAAAA,KAAK,EAAE,OAVD;AAWNqY,QAAAA,kBAAkB,EAAE,sBAXd;AAYNC,QAAAA,YAAY,EAAE,YAZR;AAaNC,QAAAA,KAAK,EAAE,OAbD;AAcNC,QAAAA,IAAI,EAAE;AAdA,OA/DJ;AA+EJC,MAAAA,GAAG,EAAE;AACH/d,QAAAA,SAAS,EAAE,WADR;AAEHge,QAAAA,gBAAgB,EAAE,oBAFf;AAGHC,QAAAA,QAAQ,EAAE,WAHP;AAIHC,QAAAA,MAAM,EAAE,QAJL;AAKH/G,QAAAA,WAAW,EAAE,aALV;AAMHgH,QAAAA,cAAc,EAAE,iBANb;AAOHpe,QAAAA,IAAI,EAAE,MAPH;AAQHuJ,QAAAA,WAAW,EAAE,SARV;AASH8U,QAAAA,aAAa,EAAE,gBATZ;AAUHC,QAAAA,IAAI,EAAE,MAVH;AAWHC,QAAAA,aAAa,EAAE,2BAXZ;AAYHT,QAAAA,KAAK,EAAE,mBAZJ;AAaHU,QAAAA,UAAU,EAAE,aAbT;AAcHvU,QAAAA,QAAQ,EAAE,cAdP;AAeHwU,QAAAA,UAAU,EAAE,WAfT;AAgBHC,QAAAA,eAAe,EAAE,sBAhBd;AAiBHC,QAAAA,SAAS,EAAE,aAjBR;AAkBHC,QAAAA,UAAU,EAAE,mBAlBT;AAmBHC,QAAAA,QAAQ,EAAE,eAnBP;AAoBHC,QAAAA,mBAAmB,EAAE,uBApBlB;AAqBHC,QAAAA,0BAA0B,EAAE,sCArBzB;AAsBHC,QAAAA,QAAQ,EAAE,aAtBP;AAuBHC,QAAAA,cAAc,EAAE,iBAvBb;AAwBHC,QAAAA,YAAY,EAAE,eAxBX;AAyBHC,QAAAA,UAAU,EAAE,eAzBT;AA0BHC,QAAAA,YAAY,EAAE,eA1BX;AA2BHC,QAAAA,iBAAiB,EAAE,sBA3BhB;AA4BHC,QAAAA,mBAAmB,EAAE,0BA5BlB;AA6BHC,QAAAA,cAAc,EAAE,sBA7Bb;AA8BHC,QAAAA,QAAQ,EAAE,KA9BP;AA+BHC,QAAAA,YAAY,EAAE,mBA/BX;AAgCHC,QAAAA,QAAQ,EAAE,aAhCP;AAiCHC,QAAAA,OAAO,EAAE,UAjCN;AAkCHC,QAAAA,OAAO,EAAE,UAlCN;AAmCHpV,QAAAA,SAAS,EAAE,YAnCR;AAoCHqV,QAAAA,cAAc,EAAE,iBApCb;AAqCHC,QAAAA,IAAI,EAAE,MArCH;AAsCHC,QAAAA,OAAO,EAAE,SAtCN;AAuCHlO,QAAAA,IAAI,EAAE,MAvCH;AAwCHmO,QAAAA,UAAU,EAAE,aAxCT;AAyCHjD,QAAAA,MAAM,EAAE,QAzCL;AA0CHkD,QAAAA,MAAM,EAAE,cA1CL;AA2CHC,QAAAA,UAAU,EAAE,WA3CT;AA4CHC,QAAAA,OAAO,EAAE,UA5CN;AA6CHC,QAAAA,sBAAsB,EAAE,0BA7CrB;AA8CHC,QAAAA,GAAG,EAAE,UA9CF;AA+CHC,QAAAA,KAAK,EAAE,MA/CJ;AAgDHC,QAAAA,eAAe,EAAE,kBAhDd;AAiDHC,QAAAA,aAAa,EAAE,aAjDZ;AAkDHC,QAAAA,cAAc,EAAE,iBAlDb;AAmDHC,QAAAA,aAAa,EAAE,gBAnDZ;AAoDHC,QAAAA,OAAO,EAAE,gCApDN;AAqDHC,QAAAA,QAAQ,EAAE,aArDP;AAsDHC,QAAAA,WAAW,EAAE,uBAtDV;AAuDHC,QAAAA,WAAW,EAAE,cAvDV;AAwDHC,QAAAA,UAAU,EAAE,aAxDT;AAyDHC,QAAAA,WAAW,EAAE,MAzDV;AA0DHC,QAAAA,UAAU,EAAE,aA1DT;AA2DHC,QAAAA,MAAM,EAAE,SA3DL;AA4DHC,QAAAA,GAAG,EAAE,KA5DF;AA6DHC,QAAAA,MAAM,EAAE,QA7DL;AA8DHC,QAAAA,SAAS,EAAE,YA9DR;AA+DHC,QAAAA,UAAU,EAAE,aA/DT;AAgEHC,QAAAA,UAAU,EAAE,gBAhET;AAiEHC,QAAAA,YAAY,EAAE;AAjEX,OA/ED;AAmJJC,MAAAA,GAAG,EAAE;AACHxhB,QAAAA,SAAS,EAAE,WADR;AAEHyhB,QAAAA,eAAe,EAAE,kBAFd;AAGHC,QAAAA,mBAAmB,EAAE,uBAHlB;AAIHhD,QAAAA,SAAS,EAAE,QAJR;AAKHH,QAAAA,UAAU,EAAE,aALT;AAMHoD,QAAAA,mBAAmB,EAAE,uBANlB;AAOHC,QAAAA,+BAA+B,EAAE,oCAP9B;AAQHhD,QAAAA,QAAQ,EAAE,WARP;AASHiD,QAAAA,cAAc,EAAE,iBATb;AAUHC,QAAAA,YAAY,EAAE,eAVX;AAWHC,QAAAA,cAAc,EAAE,iBAXb;AAYHC,QAAAA,MAAM,EAAE,SAZL;AAaHC,QAAAA,MAAM,EAAE,SAbL;AAcHjL,QAAAA,GAAG,EAAE,KAdF;AAeHkL,QAAAA,WAAW,EAAE,aAfV;AAgBHC,QAAAA,0BAA0B,EAAE,mCAhBzB;AAiBHC,QAAAA,gBAAgB,EAAE,mBAjBf;AAkBH3E,QAAAA,MAAM,EAAE,QAlBL;AAmBH4E,QAAAA,MAAM,EAAE,QAnBL;AAoBHC,QAAAA,mBAAmB,EAAE,uBApBlB;AAqBHC,QAAAA,qBAAqB,EAAE,yBArBpB;AAsBHC,QAAAA,eAAe,EAAE,kBAtBd;AAuBHC,QAAAA,iBAAiB,EAAE,oBAvBhB;AAwBHC,QAAAA,SAAS,EAAE,yBAxBR;AAyBHC,QAAAA,SAAS,EAAE,0BAzBR;AA0BH/Q,QAAAA,IAAI,EAAE,MA1BH;AA2BH8N,QAAAA,OAAO,EAAE,UA3BN;AA4BHC,QAAAA,OAAO,EAAE,UA5BN;AA6BHpV,QAAAA,SAAS,EAAE,YA7BR;AA8BHqV,QAAAA,cAAc,EAAE,iBA9Bb;AA+BHE,QAAAA,OAAO,EAAE,SA/BN;AAgCH8C,QAAAA,GAAG,EAAE,KAhCF;AAiCHC,QAAAA,GAAG,EAAE,KAjCF;AAkCHC,QAAAA,OAAO,EAAE,SAlCN;AAmCHC,QAAAA,kBAAkB,EAAE,IAnCjB;AAoCHlD,QAAAA,IAAI,EAAE,MApCH;AAqCHmD,QAAAA,eAAe,EAAE,IArCd;AAsCHC,QAAAA,MAAM,EAAE,QAtCL;AAuCHC,QAAAA,WAAW,EAAE,WAvCV;AAwCH3C,QAAAA,aAAa,EAAE,gBAxCZ;AAyCH4C,QAAAA,6BAA6B,EAAE,oCAzC5B;AA0CH1G,QAAAA,UAAU,EAAE,MA1CT;AA2CHuE,QAAAA,UAAU,EAAE,aA3CT;AA4CHC,QAAAA,MAAM,EAAE,SA5CL;AA6CHC,QAAAA,GAAG,EAAE,KA7CF;AA8CHC,QAAAA,MAAM,EAAE,QA9CL;AA+CHC,QAAAA,SAAS,EAAE,YA/CR;AAgDHC,QAAAA,UAAU,EAAE,aAhDT;AAiDHC,QAAAA,UAAU,EAAE,gBAjDT;AAkDHC,QAAAA,YAAY,EAAE,gBAlDX;AAmDH6B,QAAAA,WAAW,EAAE,SAnDV;AAoDHC,QAAAA,uBAAuB,EAAE,2BApDtB;AAqDHC,QAAAA,4BAA4B,EAAE,2BArD3B;AAsDHC,QAAAA,uCAAuC,EAAE,kDAtDtC;AAuDHC,QAAAA,oBAAoB,EAAE,kBAvDnB;AAwDHC,QAAAA,uBAAuB,EAAE,sBAxDtB;AAyDHC,QAAAA,KAAK,EAAE,OAzDJ;AA0DHC,QAAAA,KAAK,EAAE,OA1DJ;AA2DHC,QAAAA,QAAQ,EAAE,UA3DP;AA4DHC,QAAAA,QAAQ,EAAE,UA5DP;AA6DHC,QAAAA,WAAW,EAAE,aA7DV;AA8DHC,QAAAA,aAAa,EAAE,eA9DZ;AA+DHC,QAAAA,SAAS,EAAE,WA/DR;AAgEHC,QAAAA,eAAe,EAAE,iBAhEd;AAiEHC,QAAAA,eAAe,EAAE,iBAjEd;AAkEHC,QAAAA,gBAAgB,EAAE,kBAlEf;AAmEHC,QAAAA,cAAc,EAAE,gBAnEb;AAoEHC,QAAAA,eAAe,EAAE,iBApEd;AAqEHC,QAAAA,YAAY,EAAE,cArEX;AAsEHC,QAAAA,aAAa,EAAE,iBAtEZ;AAuEHC,QAAAA,cAAc,EAAE,gBAvEb;AAwEHC,QAAAA,iBAAiB,EAAE,mBAxEhB;AAyEHC,QAAAA,gBAAgB,EAAE,kBAzEf;AA0EHC,QAAAA,oBAAoB,EAAE,sBA1EnB;AA2EHC,QAAAA,mBAAmB,EAAE,qBA3ElB;AA4EHC,QAAAA,oBAAoB,EAAE,sBA5EnB;AA6EHC,QAAAA,mBAAmB,EAAE,qBA7ElB;AA8EHC,QAAAA,UAAU,EAAE,YA9ET;AA+EHC,QAAAA,OAAO,EAAE;AA/EN,OAnJD;AAoOJlc,MAAAA,MAAM,EAAE;AACN9I,QAAAA,SAAS,EAAE,WADL;AAENilB,QAAAA,OAAO,EAAE,SAFH;AAGNzH,QAAAA,IAAI,EAAE,MAHA;AAIN0H,QAAAA,KAAK,EAAE,OAJD;AAKNC,QAAAA,GAAG,EAAE,KALC;AAMNC,QAAAA,UAAU,EAAE,aANN;AAONC,QAAAA,GAAG,EAAE,KAPC;AAQNjL,QAAAA,MAAM,EAAE,QARF;AASNkL,QAAAA,OAAO,EAAE,SATH;AAUNtJ,QAAAA,MAAM,EAAE,QAVF;AAWNyB,QAAAA,MAAM,EAAE,QAXF;AAYN8H,QAAAA,kBAAkB,EAAE,uBAZd;AAaNC,QAAAA,GAAG,EAAE,KAbC;AAcNxO,QAAAA,GAAG,EAAE,KAdC;AAeNmH,QAAAA,cAAc,EAAE,iBAfV;AAgBNsH,QAAAA,IAAI,EAAE,MAhBA;AAiBNnc,QAAAA,WAAW,EAAE,SAjBP;AAkBNoc,QAAAA,OAAO,EAAE,SAlBH;AAmBNC,QAAAA,UAAU,EAAE,aAnBN;AAoBN5b,QAAAA,cAAc,EAAE,kBApBV;AAqBNP,QAAAA,QAAQ,EAAE,WArBJ;AAsBNM,QAAAA,KAAK,EAAE,QAtBD;AAuBNyV,QAAAA,QAAQ,EAAE,WAvBJ;AAwBN3Y,QAAAA,UAAU,EAAE,YAxBN;AAyBNgf,QAAAA,KAAK,EAAE,OAzBD;AA0BNjJ,QAAAA,MAAM,EAAE,MA1BF;AA2BN6C,QAAAA,YAAY,EAAE,kBA3BR;AA4BNC,QAAAA,QAAQ,EAAE,WA5BJ;AA6BN5B,QAAAA,KAAK,EAAE,UA7BD;AA8BNgI,QAAAA,YAAY,EAAE,SA9BR;AA+BNC,QAAAA,UAAU,EAAE,aA/BN;AAgCNpG,QAAAA,OAAO,EAAE,UAhCH;AAiCNC,QAAAA,OAAO,EAAE,UAjCH;AAkCNpV,QAAAA,SAAS,EAAE,YAlCL;AAmCNqV,QAAAA,cAAc,EAAE,iBAnCV;AAoCNvS,QAAAA,IAAI,EAAE,aApCA;AAqCNwN,QAAAA,MAAM,EAAE,YArCF;AAsCN1N,QAAAA,GAAG,EAAE,KAtCC;AAuCNtF,QAAAA,EAAE,EAAE,IAvCE;AAwCNoY,QAAAA,UAAU,EAAE,WAxCN;AAyCN8F,QAAAA,cAAc,EAAE,WAzCV;AA0CNC,QAAAA,aAAa,EAAE,gBA1CT;AA2CNC,QAAAA,UAAU,EAAE,YA3CN;AA4CNC,QAAAA,SAAS,EAAE,gBA5CL;AA6CNC,QAAAA,SAAS,EAAE,YA7CL;AA8CNC,QAAAA,WAAW,EAAE,uBA9CP;AA+CNC,QAAAA,UAAU,EAAE,sBA/CN;AAgDNC,QAAAA,eAAe,EAAE,kBAhDX;AAiDNC,QAAAA,SAAS,EAAE,YAjDL;AAkDN9mB,QAAAA,IAAI,EAAE,MAlDA;AAmDNwb,QAAAA,UAAU,EAAE,aAnDN;AAoDNE,QAAAA,QAAQ,EAAE,WApDJ;AAqDNqL,QAAAA,iBAAiB,EAAE,qBArDb;AAsDNC,QAAAA,OAAO,EAAE;AAtDH,OApOJ;AA4RJC,MAAAA,GAAG,EAAE;AACH1mB,QAAAA,SAAS,EAAE,WADR;AAEH2mB,QAAAA,SAAS,EAAE,aAFR;AAGH/gB,QAAAA,IAAI,EAAE,QAHH;AAIHyH,QAAAA,IAAI,EAAE,aAJH;AAKHwN,QAAAA,MAAM,EAAE,YALL;AAMH0C,QAAAA,WAAW,EAAE,wBANV;AAOH7e,QAAAA,MAAM,EAAE,QAPL;AAQH8e,QAAAA,IAAI,EAAE,MARH;AASHC,QAAAA,MAAM,EAAE,QATL;AAUHC,QAAAA,eAAe,EAAE,kBAVd;AAWHpY,QAAAA,KAAK,EAAE,OAXJ;AAYHqY,QAAAA,kBAAkB,EAAE,sBAZjB;AAaHC,QAAAA,YAAY,EAAE,YAbX;AAcHC,QAAAA,KAAK,EAAE,OAdJ;AAeHC,QAAAA,IAAI,EAAE,MAfH;AAgBH8I,QAAAA,UAAU,EAAE,aAhBT;AAiBHC,QAAAA,YAAY,EAAE,eAjBX;AAkBHC,QAAAA,OAAO,EAAE,cAlBN;AAmBHC,QAAAA,MAAM,EAAE,QAnBL;AAoBHC,QAAAA,KAAK,EAAE,OApBJ;AAqBHC,QAAAA,UAAU,EAAE,aArBT;AAsBHjb,QAAAA,aAAa,EAAE,gBAtBZ;AAuBH4U,QAAAA,WAAW,EAAE,cAvBV;AAwBHE,QAAAA,UAAU,EAAE,aAxBT;AAyBHoG,QAAAA,gBAAgB,EAAE,mBAzBf;AA0BHC,QAAAA,MAAM,EAAE,QA1BL;AA2BHC,QAAAA,OAAO,EAAE,SA3BN;AA4BH/nB,QAAAA,OAAO,EAAE,SA5BN;AA6BHgoB,QAAAA,MAAM,EAAE,QA7BL;AA8BHC,QAAAA,gBAAgB,EAAE,mBA9Bf;AA+BHC,QAAAA,cAAc,EAAE;AA/Bb,OA5RD;AA6TJC,MAAAA,GAAG,EAAE;AACHxnB,QAAAA,SAAS,EAAE,WADR;AAEHge,QAAAA,gBAAgB,EAAE,oBAFf;AAGHC,QAAAA,QAAQ,EAAE,WAHP;AAIHC,QAAAA,MAAM,EAAE,QAJL;AAKH/G,QAAAA,WAAW,EAAE,aALV;AAMHgH,QAAAA,cAAc,EAAE,iBANb;AAOHpe,QAAAA,IAAI,EAAE,MAPH;AAQHuJ,QAAAA,WAAW,EAAE,SARV;AASH8U,QAAAA,aAAa,EAAE,gBATZ;AAUHC,QAAAA,IAAI,EAAE,MAVH;AAWHC,QAAAA,aAAa,EAAE,2BAXZ;AAYHT,QAAAA,KAAK,EAAE,mBAZJ;AAaHU,QAAAA,UAAU,EAAE,aAbT;AAcHvU,QAAAA,QAAQ,EAAE,cAdP;AAeHwU,QAAAA,UAAU,EAAE,WAfT;AAgBHC,QAAAA,eAAe,EAAE,sBAhBd;AAiBHC,QAAAA,SAAS,EAAE,aAjBR;AAkBHC,QAAAA,UAAU,EAAE,mBAlBT;AAmBHC,QAAAA,QAAQ,EAAE,WAnBP;AAoBHC,QAAAA,mBAAmB,EAAE,uBApBlB;AAqBHC,QAAAA,0BAA0B,EAAE,sCArBzB;AAsBHC,QAAAA,QAAQ,EAAE,aAtBP;AAuBHC,QAAAA,cAAc,EAAE,iBAvBb;AAwBHC,QAAAA,YAAY,EAAE,eAxBX;AAyBHC,QAAAA,UAAU,EAAE,eAzBT;AA0BHC,QAAAA,YAAY,EAAE,eA1BX;AA2BHC,QAAAA,iBAAiB,EAAE,sBA3BhB;AA4BHC,QAAAA,mBAAmB,EAAE,0BA5BlB;AA6BHC,QAAAA,cAAc,EAAE,uBA7Bb;AA8BHC,QAAAA,QAAQ,EAAE,YA9BP;AA+BHC,QAAAA,YAAY,EAAE,mBA/BX;AAgCHC,QAAAA,QAAQ,EAAE,aAhCP;AAiCHC,QAAAA,OAAO,EAAE,UAjCN;AAkCHC,QAAAA,OAAO,EAAE,UAlCN;AAmCHpV,QAAAA,SAAS,EAAE,YAnCR;AAoCHqV,QAAAA,cAAc,EAAE,iBApCb;AAqCHC,QAAAA,IAAI,EAAE,MArCH;AAsCHC,QAAAA,OAAO,EAAE,SAtCN;AAuCHlO,QAAAA,IAAI,EAAE,MAvCH;AAwCHmO,QAAAA,UAAU,EAAE,aAxCT;AAyCHjD,QAAAA,MAAM,EAAE,QAzCL;AA0CHkD,QAAAA,MAAM,EAAE,cA1CL;AA2CHC,QAAAA,UAAU,EAAE,WA3CT;AA4CHC,QAAAA,OAAO,EAAE,UA5CN;AA6CHC,QAAAA,sBAAsB,EAAE,0BA7CrB;AA8CHC,QAAAA,GAAG,EAAE,UA9CF;AA+CHC,QAAAA,KAAK,EAAE,MA/CJ;AAgDHC,QAAAA,eAAe,EAAE,kBAhDd;AAiDHC,QAAAA,aAAa,EAAE,gBAjDZ;AAkDHC,QAAAA,cAAc,EAAE,iBAlDb;AAmDHC,QAAAA,aAAa,EAAE,gBAnDZ;AAoDHC,QAAAA,OAAO,EAAE,gCApDN;AAqDHC,QAAAA,QAAQ,EAAE,aArDP;AAsDHC,QAAAA,WAAW,EAAE,uBAtDV;AAuDHC,QAAAA,WAAW,EAAE,cAvDV;AAwDHC,QAAAA,UAAU,EAAE,aAxDT;AAyDHC,QAAAA,WAAW,EAAE,MAzDV;AA0DHC,QAAAA,UAAU,EAAE,aA1DT;AA2DHC,QAAAA,MAAM,EAAE,SA3DL;AA4DHC,QAAAA,GAAG,EAAE,KA5DF;AA6DHC,QAAAA,MAAM,EAAE,QA7DL;AA8DHC,QAAAA,SAAS,EAAE,YA9DR;AA+DHC,QAAAA,UAAU,EAAE,aA/DT;AAgEHC,QAAAA,UAAU,EAAE,gBAhET;AAiEHC,QAAAA,YAAY,EAAE;AAjEX,OA7TD;AAiYJkG,MAAAA,GAAG,EAAE;AACHznB,QAAAA,SAAS,EAAE,WADR;AAEHyhB,QAAAA,eAAe,EAAE,kBAFd;AAGHC,QAAAA,mBAAmB,EAAE,uBAHlB;AAIHhD,QAAAA,SAAS,EAAE,QAJR;AAKHH,QAAAA,UAAU,EAAE,aALT;AAMHoD,QAAAA,mBAAmB,EAAE,uBANlB;AAOHC,QAAAA,+BAA+B,EAAE,oCAP9B;AAQHhD,QAAAA,QAAQ,EAAE,WARP;AASHiD,QAAAA,cAAc,EAAE,iBATb;AAUHC,QAAAA,YAAY,EAAE,eAVX;AAWHC,QAAAA,cAAc,EAAE,iBAXb;AAYHC,QAAAA,MAAM,EAAE,SAZL;AAaHC,QAAAA,MAAM,EAAE,SAbL;AAcHjL,QAAAA,GAAG,EAAE,KAdF;AAeHkL,QAAAA,WAAW,EAAE,aAfV;AAgBHC,QAAAA,0BAA0B,EAAE,mCAhBzB;AAiBHC,QAAAA,gBAAgB,EAAE,mBAjBf;AAkBH3E,QAAAA,MAAM,EAAE,QAlBL;AAmBH4E,QAAAA,MAAM,EAAE,QAnBL;AAoBHC,QAAAA,mBAAmB,EAAE,uBApBlB;AAqBHC,QAAAA,qBAAqB,EAAE,yBArBpB;AAsBHC,QAAAA,eAAe,EAAE,kBAtBd;AAuBHC,QAAAA,iBAAiB,EAAE,oBAvBhB;AAwBHC,QAAAA,SAAS,EAAE,yBAxBR;AAyBHC,QAAAA,SAAS,EAAE,0BAzBR;AA0BH/Q,QAAAA,IAAI,EAAE,MA1BH;AA2BH8N,QAAAA,OAAO,EAAE,UA3BN;AA4BHC,QAAAA,OAAO,EAAE,UA5BN;AA6BHpV,QAAAA,SAAS,EAAE,YA7BR;AA8BHqV,QAAAA,cAAc,EAAE,iBA9Bb;AA+BHE,QAAAA,OAAO,EAAE,SA/BN;AAgCH8C,QAAAA,GAAG,EAAE,KAhCF;AAiCHC,QAAAA,GAAG,EAAE,KAjCF;AAkCHC,QAAAA,OAAO,EAAE,SAlCN;AAmCHC,QAAAA,kBAAkB,EAAE,IAnCjB;AAoCHlD,QAAAA,IAAI,EAAE,MApCH;AAqCHmD,QAAAA,eAAe,EAAE,IArCd;AAsCHC,QAAAA,MAAM,EAAE,QAtCL;AAuCHC,QAAAA,WAAW,EAAE,WAvCV;AAwCH3C,QAAAA,aAAa,EAAE,gBAxCZ;AAyCH4C,QAAAA,6BAA6B,EAAE,oCAzC5B;AA0CH1G,QAAAA,UAAU,EAAE,MA1CT;AA2CHuE,QAAAA,UAAU,EAAE,aA3CT;AA4CHC,QAAAA,MAAM,EAAE,SA5CL;AA6CHC,QAAAA,GAAG,EAAE,KA7CF;AA8CHC,QAAAA,MAAM,EAAE,QA9CL;AA+CHC,QAAAA,SAAS,EAAE,YA/CR;AAgDHC,QAAAA,UAAU,EAAE,aAhDT;AAiDHC,QAAAA,UAAU,EAAE,gBAjDT;AAkDHC,QAAAA,YAAY,EAAE,gBAlDX;AAmDH6B,QAAAA,WAAW,EAAE,SAnDV;AAoDHC,QAAAA,uBAAuB,EAAE,2BApDtB;AAqDHC,QAAAA,4BAA4B,EAAE,2BArD3B;AAsDHC,QAAAA,uCAAuC,EAAE,kDAtDtC;AAuDHC,QAAAA,oBAAoB,EAAE,kBAvDnB;AAwDHC,QAAAA,uBAAuB,EAAE,sBAxDtB;AAyDHC,QAAAA,KAAK,EAAE,OAzDJ;AA0DHC,QAAAA,KAAK,EAAE,OA1DJ;AA2DHC,QAAAA,QAAQ,EAAE,UA3DP;AA4DHC,QAAAA,QAAQ,EAAE,UA5DP;AA6DHC,QAAAA,WAAW,EAAE,aA7DV;AA8DHC,QAAAA,aAAa,EAAE,eA9DZ;AA+DHC,QAAAA,SAAS,EAAE,WA/DR;AAgEHC,QAAAA,eAAe,EAAE,iBAhEd;AAiEHC,QAAAA,eAAe,EAAE,iBAjEd;AAkEHC,QAAAA,gBAAgB,EAAE,kBAlEf;AAmEHC,QAAAA,cAAc,EAAE,gBAnEb;AAoEHC,QAAAA,eAAe,EAAE,iBApEd;AAqEHC,QAAAA,YAAY,EAAE,cArEX;AAsEHC,QAAAA,aAAa,EAAE,iBAtEZ;AAuEHC,QAAAA,cAAc,EAAE,gBAvEb;AAwEHC,QAAAA,iBAAiB,EAAE,mBAxEhB;AAyEHC,QAAAA,gBAAgB,EAAE,kBAzEf;AA0EHC,QAAAA,oBAAoB,EAAE,sBA1EnB;AA2EHC,QAAAA,mBAAmB,EAAE,qBA3ElB;AA4EHC,QAAAA,oBAAoB,EAAE,sBA5EnB;AA6EHC,QAAAA,mBAAmB,EAAE,qBA7ElB;AA8EHC,QAAAA,UAAU,EAAE,YA9ET;AA+EHC,QAAAA,OAAO,EAAE;AA/EN;AAjYD;AAhgBF;AADc,CAAf", "sourcesContent": ["//export default {\r\nexport const locale = {\r\n  lang: {\r\n    // 公共\r\n    common: {\r\n      //============== getway ========\r\n      consumerError: '不允許授權此操作，請聯絡系統管理員。',\r\n      consumerSignatureError: '簽名驗證失敗，請聯絡系統管理員。',\r\n      invalidUserOrPassword: 'User or password is invalid',       \r\n      badGateway: 'service error',     \r\n      monitor20: '軌跡監控',\r\n      monitor21: '事件監控',\r\n      monitor22: '母嬰同室',\r\n      monitor23: '長照中心',    \r\n      monitor24: '生理偵測',\r\n      monitor25: '病房監控',       \r\n      monitor26: '管制監控',     \r\n      monitor27: 'IEQ', \r\n      //==============\r\n      confirmDeleteSelection:'confirmDeleteSelection?',\r\n      confirmDelete:'confirmDelete',\r\n      //==============\r\n      appMainTitle:'FusionAIOT-NS',\r\n      systemTitle:'FusionAIOT-NS',\r\n      indoorRealTimePositioningSystem: 'FusionAIOT-NS',\r\n      traditionalChinese: 'Traditional Chinese',\r\n      simplifiedChinese: 'Simplified Chinese',\r\n      english: 'English',\r\n      introduction: 'Introduction',\r\n      people: 'people',\r\n      piece: 'piece',\r\n      // 公共\r\n      page: 'page',\r\n      import: 'Import',\r\n      export: 'Export',\r\n      exportExcel: 'Output as excel',\r\n      newlyIncreased: 'Add',\r\n      search: 'Search',\r\n      delete: 'Delete',\r\n      removePair: 'Untagged',\r\n      list: 'List',\r\n      confirm: 'Sure',\r\n      cancel: 'Cancel',\r\n      homePage: 'Home',\r\n      reset: 'Reset',\r\n      edit: 'Edit',\r\n      upload: 'Upload',\r\n      storage: 'Save',\r\n      total: 'Total',\r\n      pen: 'Rows',\r\n      date: 'Date',\r\n      time: 'Time',\r\n      equipment: 'Equipment',\r\n      noDataText: 'No data available',\r\n      inputKeyword: 'Please Input Keyword',\r\n      keywordEvent: 'Please input event description',\r\n      pleaseSelect: 'Please Select',\r\n      pleaseSearch: 'Searching...',\r\n      chooseStartTime: 'Plesae Select Start Date',\r\n      chooseEndTime: 'Plesae Select End Date',\r\n      exportFormatForExcel: 'Output as excel',\r\n      seeMoreEvents: 'More Events',\r\n      return: 'Return',\r\n      all: 'All',\r\n      marked: 'Marked',\r\n      notMarked: 'Unmarked',\r\n      cancelled: 'cancelled',\r\n      hasBeenIdentified: 'Confirmed',\r\n      deleteConfirm: 'Are you sure to delete',\r\n      deleteItemConfirm: 'Are you sure to delete this item？',\r\n      // 提示\r\n      prompt: 'Prompt',\r\n      selectEventToDelete: 'Please select the event to delete',\r\n      notUsePermission: 'You do not have the necessary permissions to use this feature',\r\n      deleteSuccessful: 'Delete Successful',\r\n      removePairSuccessful: 'Untagged Successful',\r\n      addSuccessful: 'Create Successful。',\r\n      storageSuccessful: 'Save Successful',\r\n      exportSuccessful: 'Export Successful',\r\n      importSuccessful: 'Import Successful',\r\n      searchSuccessful: 'Search Successful',\r\n      modifySuccessful: 'Modify Successful',\r\n      syncSuccessful: 'Sync Successful',\r\n      exportFail: 'Export failed',\r\n      searchFail: 'Search failed',\r\n      // header\r\n      backgroundManagement: 'Backend management',\r\n      personalData: 'personal data',\r\n      personal: 'personal',\r\n      operation: 'Operation',\r\n      changeThePassword: 'Change Password',\r\n      logout: 'Logout',\r\n      pleaseEnterOriginalPassword: 'Original Password',\r\n      pleaseEnterNewPassword: 'New Password',\r\n      pleaseEnterNewPasswordAgain: 'Confirm New Password',\r\n      passwordError: 'Wrong original password, please check again',\r\n      tooManyResourcesError: 'Sorry, the number of export exceeds the limitation, please adjust the search conditions',\r\n      passwordErrorPrompt: 'The new password length of 8-15 characters, at least must contain letters and numbers',\r\n      eventRecordErrorPrompt: 'Event record word length must be between 0 and 256',\r\n      samePasswordPrompt: 'The new password is the same as the original password, please reset it',\r\n      twoNewPasswordsAreNotConsistent: 'The new password entered twice is inconsistent',\r\n      planeNoUploadCoordinatesError: 'This plane does not upload maps, coordinates can not be set',\r\n      noInformationOnFloor: 'There are currently no data to display on this floor',\r\n      selectMap: 'Select Map',\r\n      floorPlan: 'Map',\r\n      selectDate: 'Choose Date',\r\n      successful: 'Successful',\r\n      fillInEventRecord: 'Please input event description',\r\n      warning: 'Warning',\r\n      error: 'Error',\r\n      invalidFormat: 'Error Format',\r\n      contactSystemAdministrator: 'System busy, please contact administrator',\r\n      modificationSuccessfulLoginAgain: 'Update successful, please login again',\r\n      loginPeriodExpires: 'Login expired, please login again',\r\n      selectedEventDoesNot: 'The selected event does not exist. Please refresh the page',\r\n      networkProblemPleaseRefreshPage: 'Network problem, please refresh the page',\r\n      networkProblem: 'Network problem, please refresh the page',\r\n      passwordUnavailableError: 'Oops, the mail server is abnormal and can not send you an account notification letter. Please try again later',\r\n      accessCodeUnavailableError: 'Oops, the mail server is abnormal and can not send the password reset letter to you. Please try again later',\r\n      storageUnavailableError: 'Image upload failed, please confirm the format or file size is correct',\r\n      accountAlreadyExists: 'This account exist',\r\n      dataAlReadyExists: 'The data exist',\r\n      deviceObjectExistsError: 'This device already used, please try another one',\r\n      objectDeviceExistsError: 'THis client already tagger, please try another one',\r\n      accountNotFoundError: 'Wrong account or password',\r\n      resourceNotFoundError: 'Data does not exist',\r\n      planeNotFoundError: 'The selected area does not exist. Please refresh the page',\r\n      operationIsNotAllowed: 'Do not allow this operation, please contact the system administrator',\r\n      selectAtLeastOneAttribute: 'Please select at least one type',\r\n      itNotExist: 'The selected event does not exist',\r\n      reorganizeThePage: 'Please refresh page',\r\n      selectedEventState: 'Please select event status',\r\n      fillInIncidentRecord: 'Please fill in the incident record',\r\n      invalidRequestError: 'Wrong parameters or wrong file format',\r\n      badPasswordError: 'The password length must be between 8-15 characters, at least must contain English letters and numbers',\r\n      badEmailError: 'E-mail must conform to e-mail format',\r\n      badPhoneError: 'Wrong phone format',\r\n      invalidAccessTaskError: 'This voucher is invalid or has expired. Please perform this operation again',\r\n      uneventful: 'Close Event Successful。',\r\n      removeEvent: 'Close Event',\r\n      event: 'Event',\r\n      trajectory: 'Trajectory',\r\n      locateObject: 'Location Client',\r\n      templateHelp: 'Help Template',\r\n      // table\r\n      objectsDisplayed: 'Object Icon',\r\n      serialNumber: 'No',\r\n      name: 'Name',\r\n      category: 'Category',\r\n      role: 'Role',\r\n      group: 'Group',\r\n      currentPosition: 'Current Position',\r\n      latestPositioningTime: 'Latest locating time',\r\n      modifiesAt: 'Last Updated',\r\n      byTheTime: 'Send time',\r\n      plane: 'Position',\r\n      eventClassification: 'Classification',\r\n      sponsorName: 'Sponsor name',\r\n      initiator: 'Sender Type/Role',\r\n      eventLog: 'Log',\r\n      eventState: 'Status',\r\n      viewTheDayTrack: 'View daily trajectory',\r\n      viewCurrentLocation: 'View current position',\r\n      // menu\r\n      monitoring: 'Real Time Monitoring',\r\n      fenceMonitoring: 'Fence Monitoring',\r\n      mmWaveMonitoring: 'Patient safety',\r\n      monitor01: 'Mother-Infant protection',\r\n      monitor02: 'Control Area Monitoring',\r\n      monitor03: 'BodyPhysical Data Monitoring',\r\n      monitor04: 'Infection Monitoring',\r\n      monitor05: 'Long Care Center',\r\n      managePermissions: 'Management authority',\r\n      accountManagement: 'System Account Settings',\r\n      roleManagement: 'System Role Settings',\r\n      firmwareUpdate: 'Firmware OTA',\r\n      positioningSettings: 'Region Management',\r\n      planePosition: 'Map Settings',\r\n      baseStation: 'Station Settings',\r\n      anchor: 'Anchor Settings',\r\n      guard: 'Guard Settings',\r\n      no: 'No',\r\n      dataManagement: 'Data Management',\r\n      objectFeatures: 'Locating attributes settings',\r\n      objectData: 'Client settings',\r\n      deviceInformation: 'Device Settings',\r\n      eventDefinition: 'Event Definition Settings',\r\n      guardSetting: 'Guard settings',\r\n      otaUpdate: 'OTA Update',\r\n      licenseManagement: 'License Management',\r\n      historyReport: 'History Report',\r\n      temporarilyNoData: 'No data available',\r\n      cameraSetting: 'Camera Setting',\r\n      logRecording: 'Log Recording',\r\n      inventory: 'Inventory System',\r\n      systemManagement: 'System Management',\r\n      systemConfig: 'System Config',\r\n      systemSWVersion: 'System Version',\r\n      mmWave: 'mmWave',\r\n      fence: 'fence',\r\n      // event\r\n      helpEvent: 'Help warning',\r\n      enterTheNotice: 'Entering Notification',\r\n      leaveTheNotice: 'leaving Notification',\r\n      theNumberOfControl: 'Number control',\r\n      lowBatteryWarning: 'Low Battery warning',\r\n      stationAbnormalWarning: 'Abnormal Station warning',\r\n      stayTimeout: 'Stay time out warning',\r\n      regularRound: 'Remind inspection',\r\n      leaveBed: 'Bed-leaving warning',\r\n      abnormalGuard: 'Abnormal guard warning',\r\n      sensorDataDriven: 'Sensor Data Driven',\r\n      fallDetection: 'Fall detection for mmWave',\r\n      stayTimeoutMMWave: 'Stay time out for mmWave',\r\n      leaveBedMMWave: 'Bed-leaving for mmWave',\r\n      getUp: 'Get up for mmWave',\r\n      abnormalBreath: 'Abnormal Breath for mmWave',\r\n      wetUrine: 'Diaper wet',\r\n      // other\r\n      help: 'Help',\r\n      untreated: 'Not processed',\r\n      inTheProcessing: 'Processing',\r\n      hasLift: 'Solved',\r\n      personnel: 'People',\r\n      map: 'Map',\r\n      eventName: 'Event Name',\r\n      objectsName: 'Name',\r\n      order: 'Prioity',\r\n      personnelDistribution: 'Personnel distribution',\r\n      equipmentDistribution: 'Equipment distribution',\r\n      area: 'Area',\r\n      subRegion: 'Sub Region',\r\n      factory: 'Factory',\r\n      building: 'Building',\r\n      floor: 'Floor',\r\n      areaName: 'Area Name',\r\n      factoryName: 'Factory Name',\r\n      buildingName: 'Building Name',\r\n      floorName: 'Floor Name',\r\n      subRegionName: 'Sub-Region Name',\r\n      geoCluster: 'geoCluster', // Yujin\r\n      coordinate: 'Coordinate',\r\n      hasAttachment: 'Online',\r\n      offline: 'Offlince',\r\n      planeName: 'Plane name',\r\n      originalCoordinates: 'Original Coordinate',\r\n      currentCoordinates: 'Current Coordinate',\r\n      hasChange: 'Changed',\r\n      mapHint: 'No available map found, please search again',\r\n      archives: 'File',\r\n      pairingDevice: 'Tagged Device',\r\n      inputCode: 'Please Input No',\r\n      inputName: 'Please Input Name',\r\n      inputCodeError: 'The number is wrong or existed',\r\n      selectObject: 'Please select a client',\r\n      selectDeleteObject: 'Please select the target to delete',\r\n      selectGroup: 'Please Select Group',\r\n      onTheGround: 'On the ground',\r\n      underground: 'Underground',\r\n      okToDoThis: 'Are you sure to do this?',\r\n      okToDeleteSelection: 'Are you sure to delete the selected item?',\r\n      whetherToReset: 'Do you want to reset or not?',\r\n      selectTheObject: 'Please select a client',\r\n      noData: 'No data available',\r\n      limitDeleteRecords: 'The maximum number of batch delete is 100',\r\n      planeNotFound: 'The selected area does not exist. Please refresh the page',\r\n      selectPlane: 'Please select a plane',\r\n      chooseType: 'Please select type',\r\n      uploadTime: 'upload time',\r\n      yes: 'yes',\r\n      nay: 'no',\r\n      type: 'type',\r\n      document: 'document',\r\n      enabled: 'enabled',\r\n      notEnabled: 'not enabled',\r\n      selectedItems: 'selected items',\r\n      planeDataExists: 'plane data exists',\r\n      notAllowedDelete: 'not allowed to delete',\r\n      badConfiguration: 'Bad configuration',\r\n      fullScreen: 'Full Screen',\r\n      exitFullScreen: 'Exit Full Screen',\r\n      noShortcuts: 'No Shortcuts'\r\n    },\r\n    // 登录\r\n    login: {\r\n      account: 'Account',\r\n      password: 'Password',\r\n      resetPassword: 'Reset Password',\r\n      login: 'Login',\r\n      accountRequiredVerification: 'Account Required',\r\n      passwordRequiredVerification: 'Password Required。',\r\n      passwordPatternVerification: 'Error Password Format'\r\n    },\r\n    // resetPassword\r\n    resetPassword: {\r\n      resetPassword: 'Reset Password',\r\n      pleaseResetAccountPassword: 'Please reset your account and password',\r\n      inputNewPassword: 'New Password',\r\n      inputNewPasswordAgain: 'Confirm New Password',\r\n      send: 'Submit',\r\n      newPasswdRequiredVerification: 'Please input new password',\r\n      newPasswdRequiredVerificationAgain: 'Please enter a new password again',\r\n      newPasswdPatternVerification: 'The new password length of 8-15 characters, at least must contain English letters and numbers',\r\n      newPasswdDiffer: 'The new password entered twice is inconsistent',\r\n      resetPasswordSuccessful: 'Reset password successfully, please login again',\r\n      inputEmail: 'Please input your email',\r\n      inputVcode: 'Enter the image verification code',\r\n      emailRequiredVerification: 'Please input mail address',\r\n      emailTypeVerification: 'E-mail format is wrong',\r\n      vcodeRequiredVerification: 'Please fill in the picture verification code',\r\n      vcodeError: 'Image verification code is incorrect',\r\n      sendEmailSuccessful: 'We have e-mailed your password reset link！',\r\n      accountNotFoundPrompt: 'The selected account does not exist. Please refresh the page'\r\n    },\r\n    personal: {\r\n      account: 'Account',\r\n      name: 'Name',\r\n      email: 'Email',\r\n      phone: 'Phone',\r\n      role: 'Role',\r\n      userEmailExistsVerification: 'E-mail already in use, please try another e-mail',\r\n      accountRequiredVerification: 'Account required',\r\n      nameRequiredVerification: 'Name required',\r\n      nameTypeVerification: 'The name must be between 0 and 64 in length',\r\n      emailRequiredVerification: 'Email required',\r\n      emailTypeVerification: 'E-mail format is wrong',\r\n      phoneRequiredVerification: 'Phone required',\r\n      phonePatternVerification: 'Wrong phone format',\r\n      userRoleRequiredVerification: 'Please select system role'\r\n    },\r\n    // 事件定義設定\r\n    eventDefinition: {\r\n      singleEvent: 'Single Event',\r\n      planeEvent: 'Region Event',\r\n      eventCode: 'Event No',\r\n      eventTemplate: 'Template Name ',\r\n      addEventDefinition: 'Add New Event Definition',\r\n      selectTemplate: 'Please Select Template *',\r\n      enterEventCode: 'Please Input Event No *',\r\n      enterEventName: 'Please Input Event Name *',\r\n      enterEventNameNoStar: 'Please Input Event Name',\r\n      selectEventCategory: 'Please Select Event Type *',\r\n      selectEventCategoryNoStar: 'Please select event type',\r\n      selectPlane: 'Please choose a region *',\r\n      enterValue: 'The value must be numeric',\r\n      selectStartTime: 'Please select start time',\r\n      selectEndTime: 'Please select end time',\r\n      selectSponsorType: 'Please select sender type',\r\n      selectSponsorRole: 'Please select sender role',\r\n      selectSponsorGroup: 'Please select sender group',\r\n      selectParticipantType: 'Please select participant type',\r\n      selectParticipantRole: 'Please select participant role',\r\n      selectParticipantGroup: 'Please select participant group',\r\n      selectNotifierType: 'Please select receiver type',\r\n      selectNotifierRole: 'Please select receiver role',\r\n      selectNotifierGroup: 'Please select notification group',\r\n      selectNotifierAccount: 'Please select notification account',\r\n      enterNotifierMsg: 'Please input notification message',\r\n      selectDeleteEvent: 'Please check the event you want to delete',\r\n      threshold: 'Warning condition settings',\r\n      below: 'Lower',\r\n      over: 'Over',\r\n      stayOver: 'Stay over',\r\n      batteryPercentage: '% Battery level',\r\n      minAlert: 'Alarm after xx minutes',\r\n      minRemind: 'Remind after xx minutes',\r\n      intervals: 'Control time settings',\r\n      between: 'between',\r\n      sensorDataDriven: 'Sensor Data Driven',\r\n      sensorDataDrivenData: 'Sensor Data Driven',\r\n      it: '<',\r\n      ite: '<=',\r\n      gt: '>',\r\n      gte: '>=',\r\n      dataDrivenRuleSource: 'Source',\r\n      dataDrivenRuleComp: 'Condition',\r\n      enterSource: 'Please select data source',\r\n      enterComp: 'Please select condition',\r\n      dataDrivenRuleRepeat: 'Repeat Error',\r\n      dataDrivenRuleCount: 'Count',\r\n      dataDrivenRuleDuration: 'Duration',\r\n      dataDrivenRuleDurationSec: 'Duration Second',\r\n      validatePlaneCode: 'At least select floor, region or subregion',\r\n      validateCategory: 'Please select event type',\r\n      validateSponsor: 'At least input sender type, role or group',\r\n      validateParticipant: 'At least input participant type, role or group',\r\n      enterEventCodeValidate: 'Please input event No',\r\n      eventCodeValidate: 'The event number word length must be between 0 and 20',\r\n      enterEventNameValidate: 'Please input event name',\r\n      eventNameValidate: 'Event name word length must be between 0 and 64',\r\n      templateValidate: 'Please select a template',\r\n      enterThresholdValidate: 'Please input warning conditions',\r\n      thresholdValidatePattern: 'Warning conditions must be numeric (positive integer)',\r\n      thresholdValidateMax: 'Warning conditions can not be greater than: 20 characters',\r\n      msgValidate: 'The length of the notification message must be between 0 and 256',\r\n      badInterval: 'The control time format must fulfill as following format: 00:00:00',\r\n      sponsorObjectTypeNotFound: 'The selected send type does not exist. Please refresh the page',\r\n      sponsorObjectRoleNotFound: 'The selected send role does not exist. Please refresh the page',\r\n      sponsorObjectGroupNotFound: 'The selected send group does not exist. Please refresh the page',\r\n      participantObjectTypeNotFound: 'The selected participant type does not exist. Please refresh the page',\r\n      participantObjectRoleNotFound: 'The selected participant role does not exist. Please refresh the page',\r\n      participantObjectGroupNotFound: 'The selected participant group does not exist. Please refresh the page',\r\n      thresholdNotFound: 'The selected warning condition does not exist. Please refresh the page',\r\n      notifierObjectTypeNotFound: 'The selected notification type does not exist. Please refresh the page',\r\n      notifierObjectRoleNotFound: 'The selected notification role does not exist. Please refresh the page',\r\n      notifierObjectGroupNotFound: 'The selected notification group does not exist. Please refresh the page',\r\n      notifierUserNotFound: 'The selected notification account does not exist. Please refresh the page',\r\n      eventCodeExists: 'The event number has been used, please try other event number',\r\n      warningCondition: 'Warning condition',\r\n      controlTime: 'Control time',\r\n      startTime: 'Start time',\r\n      entTime: 'End time',\r\n      eventCategory: 'Event Type',\r\n      planeCode: 'Region No',\r\n      participantType: 'Participant Type',\r\n      participantRole: 'Participant Role',\r\n      participantGroup: 'Participant Group',\r\n      sponsorType: 'Sender Type',\r\n      sponsorRole: 'Sender Role',\r\n      sponsorGroup: 'Sender Group',\r\n      notifierType: 'Notification Type',\r\n      notifierRole: 'Notification Role',\r\n      notifierGroup: 'Notification Group',\r\n      notifierUser: 'Notification account',\r\n      notifierMsg: 'Notification message',\r\n      addControlTime: 'Please click + to add control time',\r\n      planeSetting: 'Ward Setting',\r\n      sponsorTypeSetting: 'Sender type settings',\r\n      sponsorRoleSetting: 'Sender role settings',\r\n      sponsorGroupSetting: 'Sender group settings',\r\n      participantTypeSetting: 'Participant type settings',\r\n      participantRoleSetting: 'Participant role settings',\r\n      participantGroupSetting: 'Participant group settings',\r\n      notifierTypeSetting: 'Notification type settings',\r\n      notifierRoleSetting: 'Notification role settings',\r\n      notifierGroupSetting: 'Notification group settings',\r\n      notifierUserSetting: 'Notification account settings',\r\n      thresholdValidate: 'Warning conditions must be numeric and length: 20',\r\n      searchEventTemplate: 'Event Template',\r\n      searchSelectTemplate: 'Please Select Template',\r\n      eventNotFound: 'The selected event definition does not exist. Please refresh the page',\r\n      taskConflicts: 'The selected event can\\'t delete. Due to help event was not yet finished',\r\n      areaName: 'Area Name'\r\n    },    \r\n    // 系統參數\r\n    systemConfig: {\r\n      group: 'Group',\r\n      key: 'Key',\r\n      value: 'Value',\r\n      defaultValue: 'Default Value',\r\n      min: 'Min',\r\n      max: 'Max',\r\n      maxLength: 'Max Length',\r\n      unit: 'Unit',\r\n      description: 'Description',\r\n      schemaGroupAPI: 'API',\r\n      schemaGroupLogging: 'Log',\r\n      schemaGroupHelp: 'Help',\r\n      schemaGroupUser: 'User',\r\n      schemaGroupEnvironment: 'Environment',\r\n      schemaGroupPositioning: 'Position',\r\n      schemaGroupConnection: 'Connection',\r\n      schemaGroupWeb: 'Web',\r\n      descReserveJournalDuration: 'Keep Log Days (i.e. DBA_CleanData)',\r\n      descStationOfflinePeriod: 'Station Offline Period: Over period will offline',\r\n      descDeviceOfflinePeriod: 'Device Offline Period: Over period will offline',\r\n      descDeviceOtaRssiThreshold: 'OTA device the min Rssi',\r\n      descGatewayOfflinePeriod: 'Gateway Offline Period: Over period will offline',\r\n      descSmtpEnable: 'SMTP Enable',\r\n      descSmtpHost: 'SMTP Host',\r\n      descSmtpPort: 'SMTP Port',\r\n      descSmtpUsername: 'SMTP Username',\r\n      descSmtpPassword: 'SMTP Password',\r\n      descHelpTaskDelayPeriod: 'Help Task Delay Period',\r\n      descHelpTurnOffDeviceTimer: 'Help Task Turn Off Delay Timer',\r\n      descHelpTurnOffDeviceRetryCount: 'Help Task Turn Off Retry Count',\r\n      descHelpTaskMinRssiThreshold: 'Help Task Min Rssi Threshold',\r\n      descUserRegisterCodeExpirePeriod: 'User Register Code Expire Period',\r\n      descUserRegisterCodeLength: 'User Register Code Length',\r\n      descApiAccessTokenExpirePeriod: 'API Access Token Expire Period',\r\n      descApiRefreshTokenExpirePeriod: 'API Refresh Token Expire Period',\r\n      descApiDefaultSearchActive: 'API Default Search Active: Query result only contain active eq true',\r\n      descApiDefaultMaxSize: 'API Default Max Size: Query max size',\r\n      descReportCachePeriod: 'Report Cache Period',\r\n      descTrackingKalmanFilterEnable: 'Tracking Kalman Filter Enable',\r\n      descTrackingKalmanFilterMeterRangePerSecond: 'Tracking Kalman Filter: Meter Range Per Second(Meter)',\r\n      descTrackingKalmanFilterAccuracy: 'Tracking Kalman Filter: Accuracy',\r\n      descLocationKalmanFilterEnable: 'Location Kalman Filter Enable',\r\n      descLocationKalmanProcessNoise: 'Location Kalman: Process Noise',\r\n      descLocationKalmanMeasurementNoise: 'Location Kalman: Measurement Noise',\r\n      descPositionPersistencePeriod: 'Position Persistence Period',\r\n      descPositionHistoryPersistencePeriod: 'Position History Persistence Period',\r\n      descPositioningAlgorithmV2: 'Positioning Algorithm V2',\r\n      descPositioningPeroid: 'Positioning Peroid',\r\n      descPositionMinRssiThreshold: 'Position Min Rssi Threshold',\r\n      descTrackingRegionTotalCount: 'Tracking Region: Total Count',\r\n      descTrackingRegionMatchCount: 'Tracking Region: Match Cout',\r\n      descMonitorRefreshSecond: 'Monitor Refresh Second',\r\n      descStayTimeoutDefaultInterval: 'Stay Timeout Default Interval',\r\n      descHistoryMatchCount: 'History Match Count',\r\n      event: 'Event'\r\n    },\r\n    // 系統管理 - ipAddr\r\n    ipAddr: {\r\n      ipError: 'ip Error'\r\n    },\r\n    // 系統版本\r\n    systemSWVersion: {\r\n      name: 'Name',\r\n      version: 'Version',\r\n      status: 'Status',\r\n      checkAt: 'Check Time'\r\n    },\r\n    // apps\r\n    apps: {\r\n      common: {\r\n        posParameterCode:'Positioning ParameterCode',\r\n        deviceType: 'Device Type',\r\n        displayStations:'Display Stations',\r\n        monitor: 'Dashboard',\r\n        object: 'Object',\r\n        planeSetting: 'Ward Setting',\r\n        configSetting: 'Config Setting',\r\n        confirm: 'Confirm',\r\n        cancel: 'Cancel',\r\n        indoorRealTimePositioningSystem: 'Fusion Net Real-Time Location System',\r\n        device: 'Device',\r\n        selectDelete: 'Delete selected items',\r\n        add: 'Add',\r\n        edit: 'Edit',\r\n        delete: 'Delete',\r\n        cancelAdd: 'Cancel Add',\r\n        cancelEdit: 'Cancel Edit',\r\n        saveAdd: 'Save Add',\r\n        saveEdit: 'Save Edit',\r\n        sid: 'Station Sid',\r\n        plane: 'Plane',\r\n        isAlive: 'Link Status',\r\n        picEdit: 'Background Picture Edit',\r\n        picRemove: 'Background Picture Delete',\r\n        picUpload: 'Background Picture Upload',\r\n        picConfirm: 'Are you sure to delete background picture?',\r\n        planeEdit: 'Edit',\r\n        planeSet: 'Configure',\r\n        picBackgroundUpload: 'Upload Background Picture',\r\n        iconPicEdit: 'Edit Icon Picture',\r\n        iconPicUpload: 'Upload Icon Picture',\r\n        objectDeviceConfirm: 'Device has been binned the object. Are you sure unbind?',\r\n        status: 'Status',\r\n        record: 'Record',\r\n        recordPlaceholder: 'Please input the record...',\r\n        eventAlarm: 'Event Alarm',\r\n        eventAlarmWindow: 'Event Alarm Window',\r\n        eventStatus: 'Event Status',\r\n        eventClean: 'Clean All',\r\n        eventCleanConfirm: 'Are you sure clean all events?',\r\n        eventStartTime: 'Event record start time',\r\n        removeEvent: 'Remove event',\r\n        emergencyEvent: 'Emergency Event',\r\n        objectName: 'Name',\r\n        deviceMac: 'Device MAC',\r\n        occupy: 'Room',\r\n        event: 'Event',\r\n        people: 'People',\r\n        selectDeleteError: 'Please select delete items',\r\n        abnormalDevice: 'Abnormal Device',\r\n        second: 'second',\r\n        deviceConnectionAbnormal: 'Device Connect Abnormal',\r\n        deviceConnectionAbnormalLatestEvent: 'State Abnormal before make connection'\r\n      },\r\n      m20: {\r\n        selectMonitorPlane:'偵測平面',\r\n        selectMonitorObject: '偵測對像',        \r\n      },\r\n      m21: {\r\n        selectService:'發起服務'\r\n      },\r\n      eFence: {\r\n        equipment: 'Equipment',\r\n        name: 'Name',\r\n        type: 'Type',\r\n        device: 'MAC',\r\n        confirmEdit: 'Device has been binned',\r\n        people: 'People',\r\n        fall: 'Fall',\r\n        normal: 'Normal',\r\n        positionStation: 'Position Station',\r\n        event: 'Event',\r\n        allPositionStation: 'All Position Station',\r\n        enterAndExit: 'Enter/Exit',\r\n        enter: 'Enter',\r\n        exit: 'Exit'\r\n      },\r\n      m01: {\r\n        equipment: 'Equipment',\r\n        addSpecialStatus: 'Add Special Status',\r\n        babyName: 'Baby Name',\r\n        region: 'Region',\r\n        description: 'Description',\r\n        timeoutSetting: 'Timeout Setting',\r\n        time: 'mins',\r\n        stayTimeout: 'Overdue',\r\n        specialStatus: 'Special Status',\r\n        baby: 'Baby',\r\n        numberControl: 'Room of mother and infant',\r\n        enter: 'Restriction ctrl.',\r\n        lowBattery: 'Low Battery',\r\n        wetUrine: 'Diaper alert',\r\n        lossSignal: 'Baby loss',\r\n        wetUrineTimeout: 'Diaper alert timeout',\r\n        noPatient: 'No Puerpera',\r\n        hasPatient: 'Puerpera check-in',\r\n        babyCare: 'Infant Status',\r\n        removeSpecialStatus: 'Remove Special Status',\r\n        removeSpecialStatusConfirm: 'Confirm to remove special status?!!!',\r\n        babyMove: 'Baby moving',\r\n        unknownStation: 'Unknown Station',\r\n        specialEvent: 'Special Event',\r\n        babyRegion: 'Baby Position',\r\n        diaperNormal: 'Diaper normal',\r\n        babyTagLowBattery: 'Baby tag low battery',\r\n        motherTagLowBattery: 'Puerpera tag low battery',\r\n        monitorOutside: 'Out Of Controal Area',\r\n        emptyBed: 'Bed',\r\n        enterSetting: 'Check-in settinng',\r\n        entering: 'Checking-in',\r\n        eventId: 'Event ID',\r\n        eventNo: 'Event No',\r\n        eventName: 'Event Name',\r\n        alertCondition: 'Alert Condition',\r\n        less: 'less',\r\n        battery: 'battery',\r\n        over: 'over',\r\n        minsSignal: 'mins signal',\r\n        second: 'second',\r\n        addMon: 'Add Puerpera',\r\n        objectAttr: 'Attribute',\r\n        addBaby: 'Add Baby',\r\n        resetDiaperComfortable: 'Reset Diaper Comfortable',\r\n        mon: 'Puerpera',\r\n        baby2: 'Baby',\r\n        propertySetting: 'Property Setting',\r\n        objectSetting: 'Tag Setting',\r\n        stationSetting: 'Station Setting',\r\n        stationSelect: 'select station',\r\n        monRoom: 'Obstetrics and Gynecology Ward',\r\n        babyRoom: 'Baby Center',\r\n        controlArea: 'Control Area Entrance',\r\n        warningArea: 'Warning Area',\r\n        normalArea: 'Normal Area',\r\n        objectName2: 'Name',\r\n        objectType: 'Object Type',\r\n        addBed: 'Add Bed',\r\n        bed: 'bed',\r\n        toilet: 'toilet',\r\n        addRegion: 'Add Region',\r\n        regionType: 'Region Type',\r\n        showRegion: 'Display Region',\r\n        addSubRegion: 'Add Sub-Region'\r\n\r\n      },\r\n      m03: {\r\n        equipment: 'Equipment',\r\n        overtemperature: 'Over temperature',\r\n        wristbandLowBattery: 'Wristband low battery',\r\n        noPatient: 'vacant',\r\n        lowBattery: 'low battery',\r\n        bodyOvertemperature: 'baby over temperature',\r\n        negativePressureIsolationCenter: 'Negative Pressure Isolation Center',\r\n        babyCare: 'Baby Care',\r\n        nowTemperature: 'Now Temperature',\r\n        nowHeartRate: 'Now HeartRate',\r\n        nowBloodOxygen: 'Now BloodOxygen',\r\n        nowSbp: 'Now SBP',\r\n        nowDbp: 'Now DBP',\r\n        max: 'max',\r\n        temperature: 'temperature',\r\n        temperaturePicAndNumerical: 'temperature picture and numerical',\r\n        temperatureColor: 'temperature color',\r\n        normal: 'normal',\r\n        remind: 'remind',\r\n        bodyTemperatureWarm: 'body temperature warm',\r\n        bodyTemperatureDetect: 'body temperature detect',\r\n        heartRateDetect: 'heartRate detect',\r\n        bloodOxygenDetect: 'BloodOxygen detect',\r\n        sbpDetect: 'systolic blood pressure',\r\n        dbpDetect: 'diastolic blood pressure',\r\n        over: 'over',\r\n        eventId: 'Event ID',\r\n        eventNo: 'Event No',\r\n        eventName: 'Event Name',\r\n        alertCondition: 'Alert Condition',\r\n        battery: 'battery',\r\n        sbp: 'sbp',\r\n        dbp: 'dbp',\r\n        greater: 'greater',\r\n        greaterThanOrEqual: '>=',\r\n        less: 'less',\r\n        lessThanOrEqual: '<=',\r\n        degree: 'degree',\r\n        timesPerMin: 'Times/Min',\r\n        objectSetting: 'Object Setting',\r\n        negativePressureIsolationRoom: 'Negative Pressure Isolation Center',\r\n        objectName: 'Name',\r\n        objectType: 'Object Type',\r\n        addBed: 'Add Bed',\r\n        bed: 'bed',\r\n        toilet: 'toilet',\r\n        addRegion: 'Add Region',\r\n        regionType: 'Region Type',\r\n        showRegion: 'Display Region',\r\n        addSubRegion: 'Add Sub-Region',\r\n        editConfirm: 'Confirm',\r\n        bodyPhysicalDataSetting: 'BodyPhysical Data Setting',\r\n        continuousMonitoringDuration: 'Cont. Monitoring Duration',\r\n        nonContinuousMonitoringAbnormalCritical: 'nonContinuous Monitoring Abnormal/Critical value',\r\n        continuousMonitoring: 'Cont. Monitoring',\r\n        nonContinuousMonitoring: 'Non-Cont. Monitoring',\r\n        times: 'times',\r\n        alert: 'alert',\r\n        abnormal: 'abnormal',\r\n        critical: 'critical',\r\n        bloodOxygen: 'BloodOxygen',\r\n        bloodPressure: 'BloodPressure',\r\n        heartRate: 'HeartRate',\r\n        bodyTemperature: 'bodyTemperature',\r\n        backgroundColor: 'backgroundColor',\r\n        deviceLowBattery: 'deviceLowBattery',\r\n        lowTemperature: 'lowTemperature',\r\n        overTemperature: 'overTemperature',\r\n        lowHeartRate: 'lowHeartRate',\r\n        overHeartRate: 'HeartRate Alert',\r\n        lowBloodOxygen: 'lowBloodOxygen',\r\n        overBloodPressure: 'overBloodPressure',\r\n        lowBloodPressure: 'lowBloodPressure',\r\n        overBloodPressureSbp: 'overBloodPressureSbp',\r\n        lowBloodPressureSbp: 'lowBloodPressureSbp',\r\n        overBloodPressureDbp: 'overBloodPressureDbp',\r\n        lowBloodPressureDbp: 'lowBloodPressureDbp',\r\n        sensorType: 'sensorType',\r\n        current: 'current'\r\n      },\r\n      mmWave: {\r\n        equipment: 'Equipment',\r\n        history: 'history',\r\n        fall: 'fall',\r\n        lying: 'lying',\r\n        sit: 'sit',\r\n        natureCall: 'Nature Call',\r\n        now: 'now',\r\n        status: 'status',\r\n        breathe: 'breathe',\r\n        record: 'record',\r\n        normal: 'normal',\r\n        breathe5MinsRecord: 'Breathe 5 mins record',\r\n        bpm: 'BPM',\r\n        max: 'max',\r\n        timeoutSetting: 'Timeout Setting',\r\n        mins: 'mins',\r\n        stayTimeout: 'Overdue',\r\n        correct: 'correct',\r\n        notCorrect: 'not correct',\r\n        abnormalBreath: 'Abnormal Breathe',\r\n        leaveBed: 'Leave Bed',\r\n        getUp: 'Get Up',\r\n        emptyBed: 'Empty Bed',\r\n        monitoring: 'Monitoring',\r\n        empty: 'Empty',\r\n        occupy: 'Room',\r\n        enterSetting: 'Check-in setting',\r\n        entering: 'Check-ing',\r\n        enter: 'Check-in',\r\n        breathDetect: 'Breathe',\r\n        stopDetect: 'Stop Detect',\r\n        eventId: 'Event ID',\r\n        eventNo: 'Event No',\r\n        eventName: 'Event Name',\r\n        alertCondition: 'Alert Condition',\r\n        type: 'Object Type',\r\n        device: 'Device MAC',\r\n        yes: 'yes',\r\n        no: 'no',\r\n        objectAttr: 'Attribute',\r\n        eventCondition: 'Condition',\r\n        connectStatus: 'Connect Status',\r\n        connecting: 'Connecting',\r\n        nowObject: 'Current Object',\r\n        allObject: 'All Object',\r\n        beforeIndex: 'Before dragging index',\r\n        afterIndex: 'After dragging index',\r\n        searchCondition: 'search condition',\r\n        eventEdit: 'Event Edit',\r\n        edit: 'edit',\r\n        cancelEdit: 'Cancel edit',\r\n        saveEdit: 'Save edit',\r\n        sponsorObjectType: 'Sponsor object Type',\r\n        preFall: 'Fall detection'\r\n      },\r\n      m02: {\r\n        equipment: 'Equipment',\r\n        stationID: 'station SID',\r\n        name: 'Object',\r\n        type: 'Object type',\r\n        device: 'Device MAC',\r\n        confirmEdit: 'Device has been binned',\r\n        people: 'people',\r\n        fall: 'fall',\r\n        normal: 'normal',\r\n        positionStation: 'Position Station',\r\n        event: 'Event',\r\n        allPositionStation: 'All Position Station',\r\n        enterAndExit: 'Enter/Exit',\r\n        enter: 'Enter',\r\n        exit: 'Exit',\r\n        employeeNo: 'Employee No',\r\n        employeeName: 'Employee Name',\r\n        phoneNo: 'Cellphone No',\r\n        enable: 'Enable',\r\n        close: 'Close',\r\n        modifyTime: 'Modify Time',\r\n        pairingDevice: 'Paiting Device',\r\n        controlArea: 'Control Area',\r\n        normalArea: 'Normal Area',\r\n        enterControlArea: 'Restriction ctrl.',\r\n        notice: 'notice',\r\n        install: 'install',\r\n        confirm: 'confirm',\r\n        remove: 'remove',\r\n        leaveControlArea: 'Exit Control Area',\r\n        enableAirwatch: 'Enable Airwatch'\r\n      },\r\n      m05: {\r\n        equipment: 'Equipment',\r\n        addSpecialStatus: 'Add Special Status',\r\n        babyName: 'Baby Name',\r\n        region: 'Region',\r\n        description: 'Description',\r\n        timeoutSetting: 'Timeout Setting',\r\n        time: 'mins',\r\n        stayTimeout: 'Overdue',\r\n        specialStatus: 'Special Status',\r\n        baby: 'Baby',\r\n        numberControl: 'Room of mother and infant',\r\n        enter: 'Restriction ctrl.',\r\n        lowBattery: 'Low Battery',\r\n        wetUrine: 'Diaper alert',\r\n        lossSignal: 'Baby loss',\r\n        wetUrineTimeout: 'Diaper alert timeout',\r\n        noPatient: 'No Puerpera',\r\n        hasPatient: 'Puerpera check-in',\r\n        babyCare: 'Baby Care',\r\n        removeSpecialStatus: 'Remove Special Status',\r\n        removeSpecialStatusConfirm: 'Confirm to remove special status?!!!',\r\n        babyMove: 'Baby moving',\r\n        unknownStation: 'Unknown Station',\r\n        specialEvent: 'Special Event',\r\n        babyRegion: 'Baby Position',\r\n        diaperNormal: 'Diaper normal',\r\n        babyTagLowBattery: 'Baby tag low battery',\r\n        motherTagLowBattery: 'Puerpera tag low battery',\r\n        monitorOutside: 'Controal Area Outside',\r\n        emptyBed: 'vacant bed',\r\n        enterSetting: 'Check-in settinng',\r\n        entering: 'Checking-in',\r\n        eventId: 'Event ID',\r\n        eventNo: 'Event No',\r\n        eventName: 'Event Name',\r\n        alertCondition: 'Alert Condition',\r\n        less: 'less',\r\n        battery: 'battery',\r\n        over: 'over',\r\n        minsSignal: 'mins signal',\r\n        second: 'second',\r\n        addMon: 'Add Puerpera',\r\n        objectAttr: 'Attribute',\r\n        addBaby: 'Add Baby',\r\n        resetDiaperComfortable: 'Reset Diaper Comfortable',\r\n        mon: 'Puerpera',\r\n        baby2: 'Baby',\r\n        propertySetting: 'Property Setting',\r\n        objectSetting: 'Object Setting',\r\n        stationSetting: 'Station Setting',\r\n        stationSelect: 'select station',\r\n        monRoom: 'Obstetrics and Gynecology Ward',\r\n        babyRoom: 'Baby Center',\r\n        controlArea: 'Control Area Entrance',\r\n        warningArea: 'Warning Area',\r\n        normalArea: 'Normal Area',\r\n        objectName2: 'Name',\r\n        objectType: 'Object Type',\r\n        addBed: 'Add Bed',\r\n        bed: 'bed',\r\n        toilet: 'toilet',\r\n        addRegion: 'Add Region',\r\n        regionType: 'Region Type',\r\n        showRegion: 'Display Region',\r\n        addSubRegion: 'Add Sub-Region'\r\n\r\n      },\r\n      m27: {\r\n        equipment: 'Equipment',\r\n        overtemperature: 'Over temperature',\r\n        wristbandLowBattery: 'Wristband low battery',\r\n        noPatient: 'vacant',\r\n        lowBattery: 'low battery',\r\n        bodyOvertemperature: 'baby over temperature',\r\n        negativePressureIsolationCenter: 'Negative Pressure Isolation Center',\r\n        babyCare: 'Baby Care',\r\n        nowTemperature: 'Now Temperature',\r\n        nowHeartRate: 'Now HeartRate',\r\n        nowBloodOxygen: 'Now BloodOxygen',\r\n        nowSbp: 'Now SBP',\r\n        nowDbp: 'Now DBP',\r\n        max: 'max',\r\n        temperature: 'temperature',\r\n        temperaturePicAndNumerical: 'temperature picture and numerical',\r\n        temperatureColor: 'temperature color',\r\n        normal: 'normal',\r\n        remind: 'remind',\r\n        bodyTemperatureWarm: 'body temperature warm',\r\n        bodyTemperatureDetect: 'body temperature detect',\r\n        heartRateDetect: 'heartRate detect',\r\n        bloodOxygenDetect: 'BloodOxygen detect',\r\n        sbpDetect: 'systolic blood pressure',\r\n        dbpDetect: 'diastolic blood pressure',\r\n        over: 'over',\r\n        eventId: 'Event ID',\r\n        eventNo: 'Event No',\r\n        eventName: 'Event Name',\r\n        alertCondition: 'Alert Condition',\r\n        battery: 'battery',\r\n        sbp: 'sbp',\r\n        dbp: 'dbp',\r\n        greater: 'greater',\r\n        greaterThanOrEqual: '>=',\r\n        less: 'less',\r\n        lessThanOrEqual: '<=',\r\n        degree: 'degree',\r\n        timesPerMin: 'Times/Min',\r\n        objectSetting: 'Object Setting',\r\n        negativePressureIsolationRoom: 'Negative Pressure Isolation Center',\r\n        objectName: 'Name',\r\n        objectType: 'Object Type',\r\n        addBed: 'Add Bed',\r\n        bed: 'bed',\r\n        toilet: 'toilet',\r\n        addRegion: 'Add Region',\r\n        regionType: 'Region Type',\r\n        showRegion: 'Display Region',\r\n        addSubRegion: 'Add Sub-Region',\r\n        editConfirm: 'Confirm',\r\n        bodyPhysicalDataSetting: 'BodyPhysical Data Setting',\r\n        continuousMonitoringDuration: 'Cont. Monitoring Duration',\r\n        nonContinuousMonitoringAbnormalCritical: 'nonContinuous Monitoring Abnormal/Critical value',\r\n        continuousMonitoring: 'Cont. Monitoring',\r\n        nonContinuousMonitoring: 'Non-Cont. Monitoring',\r\n        times: 'times',\r\n        alert: 'alert',\r\n        abnormal: 'abnormal',\r\n        critical: 'critical',\r\n        bloodOxygen: 'BloodOxygen',\r\n        bloodPressure: 'BloodPressure',\r\n        heartRate: 'HeartRate',\r\n        bodyTemperature: 'bodyTemperature',\r\n        backgroundColor: 'backgroundColor',\r\n        deviceLowBattery: 'deviceLowBattery',\r\n        lowTemperature: 'lowTemperature',\r\n        overTemperature: 'overTemperature',\r\n        lowHeartRate: 'lowHeartRate',\r\n        overHeartRate: 'HeartRate Alert',\r\n        lowBloodOxygen: 'lowBloodOxygen',\r\n        overBloodPressure: 'overBloodPressure',\r\n        lowBloodPressure: 'lowBloodPressure',\r\n        overBloodPressureSbp: 'overBloodPressureSbp',\r\n        lowBloodPressureSbp: 'lowBloodPressureSbp',\r\n        overBloodPressureDbp: 'overBloodPressureDbp',\r\n        lowBloodPressureDbp: 'lowBloodPressureDbp',\r\n        sensorType: 'sensorType',\r\n        current: 'current'\r\n      }      \r\n    }\r\n  }\r\n}\r\n"]}]}