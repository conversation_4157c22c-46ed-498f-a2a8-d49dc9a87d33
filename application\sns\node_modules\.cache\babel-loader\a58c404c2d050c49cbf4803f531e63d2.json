{"remainingRequest": "E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\node_modules\\babel-loader\\lib\\index.js!E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\node_modules\\string-replace-loader\\index.js??ref--13-0!E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\src\\components\\administrative\\common\\SecondHeader.vue?vue&type=script&lang=js&", "dependencies": [{"path": "E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\src\\components\\administrative\\common\\SecondHeader.vue", "mtime": 1754362736907}, {"path": "E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\node_modules\\string-replace-loader\\index.js", "mtime": 499162500000}, {"path": "E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["SecondHeader.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAwFA,OAAA,KAAA,MAAA,wDAAA;AACA,OAAA,YAAA,MAAA,+DAAA;AAEA,OAAA,MAAA,MAAA,iBAAA,C,CACA;AACA;AACA;;AACA,OAAA,aAAA,MAAA,gDAAA;AACA,OAAA,gBAAA,MAAA,mDAAA;AACA,OAAA,YAAA,MAAA,+CAAA;AAEA,SAAA,UAAA,QAAA,MAAA;AAEA,SAAA,MAAA,IAAA,IAAA,QAAA,iBAAA;AACA,SAAA,MAAA,IAAA,IAAA,QAAA,iBAAA;AACA,SAAA,MAAA,IAAA,IAAA,QAAA,iBAAA;AAEA,eAAA;AACA,EAAA,IAAA,EAAA,aADA;AAEA,EAAA,KAAA,EAAA,CAAA,WAAA,EAAA,kBAAA,CAFA;AAGA,EAAA,IAHA,kBAGA;AACA,WAAA;AACA,MAAA,MAAA,EAAA,IADA;AAEA,MAAA,YAAA,EAAA,YAFA;AAGA,MAAA,gBAAA,EAAA,gBAHA;AAIA;AACA,MAAA,MAAA,EAAA,SALA;AAMA,MAAA,MAAA,EAAA,SANA;AAOA,MAAA,MAAA,EAAA,SAPA;AAQA,MAAA,MAAA,EAAA,SARA;AASA,MAAA,MAAA,EAAA,SATA;AAUA,MAAA,UAAA,EAAA,MAAA,CAAA,WAVA;AAWA,MAAA,QAAA,EAAA,YAAA,CAAA,OAAA,CAAA,eAAA,CAXA;AAYA,MAAA,QAAA,EAAA,KAAA,KAAA,CAAA,MAZA;AAaA,MAAA,WAAA,EAAA,EAbA;AAcA,MAAA,SAAA,EAAA,SAdA;AAgBA,MAAA,SAAA,EAAA,KAhBA;AAiBA,MAAA,gBAAA,EAAA,KAjBA;AAkBA,MAAA,QAAA,EAAA;AACA;;;;;;;;;AAnBA,KAAA;AA4BA,GAhCA;AAiCA,EAAA,UAAA,EAAA;AACA;AACA;AACA,IAAA,KAAA,EAAA,KAHA;AAIA,IAAA,YAAA,EAAA;AAJA,GAjCA;AAwCA,EAAA,QAAA,kCACA,UAAA,CAAA,CAAA,gBAAA,CAAA,CADA;AAGA,IAAA,gBAHA,8BAGA;AACA,aAAA,KAAA,MAAA,CAAA,IAAA;AACA,KALA,CAOA;AACA;AACA;;AATA,IAxCA;AAoDA,EAAA,KAAA,EAAA;AACA,IAAA,MAAA,EAAA,eADA;AAEA,IAAA,cAFA,0BAEA,GAFA,EAEA;AACA;AACA,WAAA,WAAA,GAAA,GAAA;AACA,KALA;AAMA,IAAA,gBANA,4BAMA,GANA,EAMA;AACA,MAAA,OAAA,CAAA,GAAA,CAAA,kBAAA;AACA,MAAA,OAAA,CAAA,GAAA,CAAA,GAAA;AACA,WAAA,eAAA;AACA;AAVA,GApDA;AAgEA,EAAA,OAhEA,qBAgEA;AACA,SAAA,eAAA;AACA,SAAA,aAAA;AACA,GAnEA;AAoEA,EAAA,OAAA,EAAA;AACA,IAAA,eADA,2BACA,GADA,EACA;AAAA;;AACA,UAAA,aAAA,GAAA,YAAA,CAAA,OAAA,CAAA,eAAA,CAAA;AACA,UAAA,iBAAA,GAAA,cAAA,aAAA;AAEA,MAAA,aAAA,CAAA,gBAAA,CACA,WADA,EAEA,iBAFA,EAGA,UAAA,KAAA,EAAA;AACA,YAAA,KAAA,KAAA,EAAA,EAAA;AACA,UAAA,KAAA,CAAA,QAAA,GAAA,IAAA;AACA,SAFA,MAEA,IAAA,KAAA,KAAA,MAAA,EAAA;AACA,UAAA,KAAA,CAAA,QAAA,GAAA,IAAA;AACA,SAFA,MAEA;AACA,UAAA,KAAA,CAAA,QAAA,GAAA,KAAA;AACA;AACA,OAXA;AAaA,KAlBA;AAmBA,IAAA,OAnBA,qBAmBA;AACA,WAAA,SAAA,GAAA,IAAA;AACA,KArBA;AAsBA,IAAA,UAtBA,wBAsBA;AACA,WAAA,SAAA,GAAA,KAAA;AACA,KAxBA;AAyBA,IAAA,cAzBA,4BAyBA;AACA,WAAA,gBAAA,GAAA,IAAA;AACA,KA3BA;AA6BA,IAAA,iBA7BA,+BA6BA;AACA,WAAA,gBAAA,GAAA,KAAA;AACA,KA/BA;AAgCA,IAAA,QAhCA,oBAgCA,GAhCA,EAgCA;AAAA;;AACA,MAAA,OAAA,CAAA,GAAA,CAAA,UAAA,GAAA;AACA,WAAA,SAAA,GAAA,GAAA;AACA,WAAA,MAAA,CAAA,MAAA,CAAA,WAAA,EAAA,EAAA;AACA,WAAA,SAAA,CAAA,YAAA;AACA,QAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,WAAA,EAAA,MAAA,CAAA,SAAA;AACA,OAFA;AAGA,KAvCA;AAwCA,IAAA,aAxCA,2BAwCA;AACA,MAAA,OAAA,CAAA,GAAA,CAAA,2BAAA,KAAA,gBAAA;;AACA,UAAA,KAAA,gBAAA,IAAA,KAAA,gBAAA,KAAA,WAAA,EAAA,CACA;AACA;AACA,OAHA,MAGA;AACA,aAAA,SAAA,GAAA,SAAA;AACA;;AAEA,cAAA,KAAA,gBAAA;AACA,aAAA,SAAA;AACA,eAAA,MAAA,GAAA,WAAA;AACA,eAAA,MAAA,GAAA,KAAA,MAAA,GAAA,KAAA,MAAA,GAAA,KAAA,MAAA,GAAA,EAAA;AACA;;AACA,aAAA,SAAA;AACA,eAAA,MAAA,GAAA,WAAA;AACA,eAAA,MAAA,GAAA,KAAA,MAAA,GAAA,KAAA,MAAA,GAAA,KAAA,MAAA,GAAA,EAAA;AACA;;AACA,aAAA,SAAA;AACA,eAAA,MAAA,GAAA,WAAA;AACA,eAAA,MAAA,GAAA,KAAA,MAAA,GAAA,KAAA,MAAA,GAAA,KAAA,MAAA,GAAA,EAAA;AACA;;AACA,aAAA,SAAA;AACA,eAAA,MAAA,GAAA,WAAA;AACA,eAAA,MAAA,GAAA,KAAA,MAAA,GAAA,KAAA,MAAA,GAAA,KAAA,MAAA,GAAA,EAAA;AACA;;AACA;AACA,eAAA,MAAA,GAAA,WAAA;AACA,eAAA,MAAA,GAAA,KAAA,MAAA,GAAA,KAAA,MAAA,GAAA,KAAA,MAAA,GAAA,EAAA;AACA;AApBA;AAsBA,KAvEA;AAwEA,IAAA,MAxEA,oBAwEA;AACA,UAAA,KAAA,gBAAA,IAAA,KAAA,gBAAA,KAAA,WAAA,EAAA;AACA,aAAA,OAAA,CAAA,IAAA,CAAA;AACA,UAAA,IAAA,EAAA,kBAAA,CACA,KAAA,MAAA,CAAA,KAAA,CAAA,QAAA,IAAA,2BADA,CAAA,CAEA,KAFA,CAEA,UAAA,GAAA,EAAA;AACA,YAAA,GAAA;AACA,WAJA,CADA,CAKA;;AALA,SAAA;AAOA;AACA,KAlFA;AAoFA,IAAA,qBApFA,iCAoFA,KApFA,EAoFA;AACA,WAAA,KAAA,CAAA,gBAAA,EAAA,KAAA;AACA,KAtFA;AAuFA,IAAA,aAvFA,2BAuFA;AACA,UAAA,KAAA,MAAA,EAAA;AACA,aAAA,MAAA,GAAA,KAAA;AACA,OAFA,MAEA;AACA,aAAA,MAAA,GAAA,IAAA;AACA;;AACA,WAAA,KAAA,CAAA,eAAA,EAAA,KAAA,MAAA;AACA,KA9FA;AAgGA;AACA,IAAA,UAjGA,sBAiGA,MAjGA,EAiGA;AACA,WAAA,KAAA,CAAA,kBAAA,CAAA,KAAA,QAAA,EAAA,MAAA,EAAA,UAAA,GAAA,EAAA,CAAA,CAAA;AACA;;;;;AAKA,KAxGA;AA0GA,IAAA,YA1GA,0BA0GA;AACA,WAAA,OAAA,CAAA,IAAA,CAAA;AACA,QAAA,IAAA,EAAA;AADA,OAAA;AAGA,KA9GA;AA+GA,IAAA,gBA/GA,8BA+GA;AACA,WAAA,OAAA,CAAA,IAAA,CAAA;AACA,QAAA,IAAA,EAAA,6BADA;AAEA,QAAA,KAAA,EAAA;AACA,UAAA,MAAA,EAAA;AADA;AAFA,OAAA;AAMA,KAtHA;AAuHA,IAAA,QAvHA,sBAuHA;AACA,UAAA,eAAA,GAAA,YAAA,CAAA,OAAA,CAAA,qBAAA,KAAA,EAAA;AACA,MAAA,YAAA,CAAA,UAAA,CAAA,oBAAA,eAAA;AACA,MAAA,YAAA,CAAA,UAAA,CAAA,qBAAA,eAAA;AAEA,MAAA,YAAA,CAAA,UAAA,CAAA,eAAA;AACA,MAAA,YAAA,CAAA,UAAA,CAAA,gBAAA;AACA,MAAA,YAAA,CAAA,UAAA,CAAA,iBAAA,EAPA,CASA;AACA;AACA;AACA;AACA;;AACA,WAAA,OAAA,CAAA,IAAA,CAAA,aAAA;AACA,KAtIA;AAuIA,IAAA,QAvIA,oBAuIA,IAvIA,EAuIA;AACA,WAAA,OAAA,CACA,IADA,CACA;AACA,QAAA,IAAA,EAAA,kBAAA,CACA,KAAA,MAAA,CAAA,KAAA,CAAA,QAAA,IAAA,0BAAA,IADA;AADA,OADA,EAMA,KANA,CAMA,UAAA,GAAA,EAAA;AACA,QAAA,GAAA;AACA,OARA;AAUA;;;;AAIA,KAtJA;AAuJA,IAAA,WAvJA,yBAuJA;AACA,UAAA,KAAA,MAAA,EAAA;AACA,aAAA,MAAA,GAAA,KAAA;AACA,OAFA,MAEA;AACA,aAAA,MAAA,GAAA,IAAA;AACA;;AACA,WAAA,MAAA,CAAA,MAAA,CAAA,gBAAA,EAAA,KAAA,MAAA;AACA;AA9JA;AApEA,CAAA", "sourcesContent": ["<template>\r\n  <Row class=\"header-menu\">\r\n    <Col span=\"18\" style=\"text-align: left; padding-left: 40px\">\r\n      <ButtonGroup size=\"large\">\r\n        <Button\r\n          type=\"default\"\r\n          :class=\"tabm28\"\r\n          style=\"margin-right: 0px\"\r\n          @click=\"onAction('m28')\"\r\n          >場域維護監控</Button\r\n        >\r\n        <Button\r\n          type=\"default\"\r\n          :class=\"tabm24\"\r\n          style=\"margin-right: 0px\"\r\n          @click=\"onAction('m24')\"\r\n          >生理趨勢</Button\r\n        >\r\n        <Button\r\n          type=\"default\"\r\n          :class=\"tabm30\"\r\n          style=\"margin-right: 0px\"\r\n          @click=\"onAction('m30')\"\r\n          >跌倒偵測</Button\r\n        >\r\n        <Button\r\n          type=\"default\"\r\n          :class=\"tabm27\"\r\n          style=\"margin-right: 0px\"\r\n          @click=\"onAction('m27')\"\r\n          >環境淨化</Button\r\n        >\r\n        <Button\r\n          type=\"default\"\r\n          :class=\"tabm29\"\r\n          style=\"margin-right: 0px\"\r\n          @click=\"onAction('m29')\"\r\n          >SNS</Button\r\n        >\r\n      </ButtonGroup>\r\n    </Col>\r\n    <Col span=\"6\" style=\"text-align: right; padding-right: 20px\">\r\n      <ul class=\"first\">\r\n        <li v-on:click=\"toggleEvent\">\r\n          <Badge :count=\"12\" overflow-count=\"99\" :offset=\"[12, 0]\">\r\n            <a href=\"#\" class=\"demo-badge\">\r\n              <img\r\n                :src=\"notificationIcon\"\r\n                style=\"vertical-align: bottom\"\r\n                width=\"36\"\r\n              />\r\n            </a>\r\n          </Badge>\r\n          <Icon\r\n            v-if=\"!isOpen\"\r\n            type=\"ios-arrow-down\"\r\n            style=\"padding-left: 10px; font-weight: bold\"\r\n            size=\"24\"\r\n          />\r\n          <Icon\r\n            v-if=\"isOpen\"\r\n            type=\"ios-arrow-up\"\r\n            style=\"padding-left: 10px; font-weight: bold\"\r\n            size=\"24\"\r\n          />\r\n        </li>\r\n        <li class=\"active\">\r\n          <Dropdown @on-click=\"onAppTab\">\r\n            <a href=\"javascript:void(0)\">\r\n              <img\r\n                :src=\"unselectIcon\"\r\n                style=\"vertical-align: bottom\"\r\n                width=\"36\"\r\n              />\r\n            </a>\r\n            <DropdownMenu slot=\"list\" style=\"text-align: center\">\r\n              <!-- <DropdownItem name=\"Monitor\">監控平面</DropdownItem> -->\r\n              <DropdownItem name=\"PlaneSetting\">圖面管理</DropdownItem>\r\n              <DropdownItem name=\"ObjectSetting\">對象管理</DropdownItem>\r\n            </DropdownMenu>\r\n          </Dropdown>\r\n        </li>\r\n      </ul>\r\n    </Col>\r\n  </Row>\r\n</template>\r\n\r\n<script>\r\nimport About from \"@/components/administrative/systemManagement/about.vue\";\r\nimport SystemConfig from \"@/components/administrative/systemManagement/systemConfig.vue\";\r\n\r\nimport Config from \"@/common/config\";\r\n// import WebStorage from '@/store/web-storage'\r\n// import siderTrigger from './sider-trigger'\r\n// import customBreadCrumb from './custom-bread-crumb'\r\nimport CommonService from \"@/components/administrative/apps/commonService\";\r\nimport notificationIcon from \"@/assets/images/icon-light-24-ic-notification.png\";\r\nimport unselectIcon from \"@/assets/images/icon-ic-settings-unselect.png\";\r\n\r\nimport { mapGetters } from \"vuex\";\r\n\r\nimport { locale as enUS } from \"@/locales/en-US\";\r\nimport { locale as zhCN } from \"@/locales/zh-CN\";\r\nimport { locale as zhTW } from \"@/locales/zh-TW\";\r\n\r\nexport default {\r\n  name: \"AdminHeader\",\r\n  props: [\"collapsed\", \"isShowRightSider\"],\r\n  data() {\r\n    return {\r\n      isOpen: true,\r\n      unselectIcon: unselectIcon,\r\n      notificationIcon: notificationIcon,\r\n      // openEvent: false,\r\n      tabm28: \"primary\",\r\n      tabm24: \"default\",\r\n      tabm30: \"default\",\r\n      tabm27: \"default\",\r\n      tabm29: \"default\",\r\n      webVersion: Config.WEB_VERSION,\r\n      userName: localStorage.getItem(\"sns_loginUser\"),\r\n      language: this.$i18n.locale,\r\n      breadCrumbs: [],\r\n      selAppTab: \"Monitor\",\r\n\r\n      showAbout: false,\r\n      showSystemConfig: false,\r\n      appsEdit: true,\r\n      /*\r\n            appTabs:[\r\n                {code:'Monitor',name:this.$t('lang.apps.common.monitor'),config:false},\r\n                {code:'PlaneSetting',name:this.$t('lang.apps.common.planeSetting'),config:false},\r\n                {code:'ObjectSetting',name:this.$t('lang.apps.common.object'),config:false},\r\n                {code:'ConfigSetting',name:this.$t('lang.apps.common.configSetting'),config:false},        \r\n              ]      \r\n              */\r\n    };\r\n  },\r\n  components: {\r\n    // siderTrigger //,\r\n    // customBreadCrumb\r\n    About,\r\n    SystemConfig,\r\n  },\r\n\r\n  computed: {\r\n    ...mapGetters([\"breadCrumbList\"]),\r\n\r\n    currentRouteName() {\r\n      return this.$route.name;\r\n    },\r\n\r\n    // toggleEvent () {\r\n    //   return this.$store.state.app.breadCrumbList\r\n    // }\r\n  },\r\n\r\n  watch: {\r\n    $route: \"routingChange\",\r\n    breadCrumbList(val) {\r\n      //   this.breadCrumb=val.toString()\r\n      this.breadCrumbs = val;\r\n    },\r\n    pocConfigChanged(val) {\r\n      console.log(\"pocConfigChanged\");\r\n      console.log(val);\r\n      this.getAppsEditable();\r\n    },\r\n  },\r\n  mounted() {\r\n    this.getAppsEditable();\r\n    this.routingChange();\r\n  },\r\n  methods: {\r\n    getAppsEditable(val) {\r\n      let loginUserName = localStorage.getItem(\"sns_loginUser\");\r\n      let appEditAccountKey = \"appsEdit-\" + loginUserName;\r\n\r\n      CommonService.getPOCProperties(\r\n        \"pocConfig\",\r\n        appEditAccountKey,\r\n        (value) => {\r\n          if (value === \"\") {\r\n            this.appsEdit = true;\r\n          } else if (value === \"true\") {\r\n            this.appsEdit = true;\r\n          } else {\r\n            this.appsEdit = false;\r\n          }\r\n        }\r\n      );\r\n    },\r\n    onAbout() {\r\n      this.showAbout = true;\r\n    },\r\n    closeAbout() {\r\n      this.showAbout = false;\r\n    },\r\n    onSystemConfig() {\r\n      this.showSystemConfig = true;\r\n    },\r\n\r\n    closeSystemConfig() {\r\n      this.showSystemConfig = false;\r\n    },\r\n    onAppTab(val) {\r\n      console.log(\"name:\" + val);\r\n      this.selAppTab = val;\r\n      this.$store.commit(\"setAppTab\", \"\");\r\n      this.$nextTick(() => {\r\n        this.$store.commit(\"setAppTab\", this.selAppTab);\r\n      });\r\n    },\r\n    routingChange() {\r\n      console.log(\"this.currentRouteName:\" + this.currentRouteName);\r\n      if (this.currentRouteName && this.currentRouteName !== \"dashBoard\") {\r\n        //this.selAppTab='Monitor'\r\n        //this.$store.commit('setAppTab', this.selAppTab)\r\n      } else {\r\n        this.selAppTab = \"Monitor\";\r\n      }\r\n\r\n      switch (this.currentRouteName) {\r\n        case \"appsM24\":\r\n          this.tabm24 = \"activeTab\";\r\n          this.tabm28 = this.tabm30 = this.tabm27 = this.tabm29 = \"\";\r\n          break;\r\n        case \"appsM30\":\r\n          this.tabm30 = \"activeTab\";\r\n          this.tabm28 = this.tabm24 = this.tabm27 = this.tabm29 = \"\";\r\n          break;\r\n        case \"appsM27\":\r\n          this.tabm27 = \"activeTab\";\r\n          this.tabm28 = this.tabm30 = this.tabm24 = this.tabm29 = \"\";\r\n          break;\r\n        case \"appsM29\":\r\n          this.tabm29 = \"activeTab\";\r\n          this.tabm28 = this.tabm30 = this.tabm24 = this.tabm27 = \"\";\r\n          break;\r\n        default:\r\n          this.tabm28 = \"activeTab\";\r\n          this.tabm24 = this.tabm30 = this.tabm27 = this.tabm29 = \"\";\r\n          break;\r\n      }\r\n    },\r\n    goHome() {\r\n      if (this.currentRouteName && this.currentRouteName !== \"dashBoard\") {\r\n        this.$router.push({\r\n          path: decodeURIComponent(\r\n            this.$route.query.redirect || \"/administrative/dashBoard\"\r\n          ).catch((err) => {\r\n            err;\r\n          }), // 導頁至預設頁面\r\n        });\r\n      }\r\n    },\r\n\r\n    handleCollpasedChange(state) {\r\n      this.$emit(\"on-coll-change\", state);\r\n    },\r\n    onChangeSider() {\r\n      if (this.isOpen) {\r\n        this.isOpen = false;\r\n      } else {\r\n        this.isOpen = true;\r\n      }\r\n      this.$emit(\"onChangeSider\", this.isOpen);\r\n    },\r\n\r\n    // 語言切換\r\n    changeLang(locale) {\r\n      this.$i18n.loadLocaleMessages(this.$service, locale, (msg) => {});\r\n      /*\r\n            this.$i18n.locale = locale\r\n            localStorage.setItem('sns_lang', locale)\r\n            location.reload()\r\n            */\r\n    },\r\n\r\n    onPersonData() {\r\n      this.$router.push({\r\n        path: \"/administrative/userAccount\",\r\n      });\r\n    },\r\n    onChangePassword() {\r\n      this.$router.push({\r\n        path: \"/administrative/userAccount\",\r\n        query: {\r\n          action: \"password\",\r\n        },\r\n      });\r\n    },\r\n    onLogout() {\r\n      let applicationCode = localStorage.getItem(\"sns_applicationCode\") || \"\";\r\n      localStorage.removeItem(\"sns_accessToken\" + applicationCode);\r\n      localStorage.removeItem(\"sns_refreshToken\" + applicationCode);\r\n\r\n      localStorage.removeItem(\"sns_loginUser\");\r\n      localStorage.removeItem(\"sns_permission\");\r\n      localStorage.removeItem(\"sns_moduleCodes\");\r\n\r\n      // this.$Notice.success({\r\n      //   title: this.$t('lang.common.successful'),\r\n      //   desc: this.$t('lang.common.logout') + this.$t('lang.common.successful'),\r\n      //   duration: Config.SUCCESS_DURATION\r\n      // })\r\n      this.$router.push(\"/user/login\");\r\n    },\r\n    onAction(item) {\r\n      this.$router\r\n        .push({\r\n          path: decodeURIComponent(\r\n            this.$route.query.redirect || \"/administrative/apps/\" + item\r\n          ),\r\n        })\r\n        .catch((err) => {\r\n          err;\r\n        });\r\n\r\n      /*\r\n            let route = this.$router.resolve({path: this.$route.query.redirect || '/administrative/apps/m03'});\r\n            window.open(route.href, '_blank');\r\n            */\r\n    },\r\n    toggleEvent() {\r\n      if (this.isOpen) {\r\n        this.isOpen = false;\r\n      } else {\r\n        this.isOpen = true;\r\n      }\r\n      this.$store.commit(\"setToggleEvent\", this.isOpen);\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"less\">\r\n.my-header {\r\n  padding-left: 5px !important;\r\n  padding-right: 5px !important;\r\n\r\n  .left-header {\r\n    float: left;\r\n    color: white;\r\n    height: 56px;\r\n    //margin:0 auto;\r\n    font-size: 18px !important;\r\n    font-weight: bold;\r\n    width: 100%;\r\n\r\n    span {\r\n      float: left;\r\n      margin-left: 5px;\r\n      overflow: hidden;\r\n      white-space: nowrap;\r\n      text-overflow: ellipsis;\r\n      font-size: 32px;\r\n    }\r\n  }\r\n\r\n  .center-header {\r\n    .appTab {\r\n      border-radius: 10px 10px 0px 0px;\r\n      font-size: 20px;\r\n      // width:120px;\r\n      height: 40px;\r\n      margin-left: 5px;\r\n      margin-top: 10px;\r\n    }\r\n\r\n    .appTabNormal {\r\n      background: #dddddd;\r\n    }\r\n\r\n    .appTabActive {\r\n      background: white;\r\n    }\r\n  }\r\n\r\n  .right-header {\r\n    float: right;\r\n    margin-right: 5px;\r\n    margin-top: 3px;\r\n\r\n    //margin-right: -10px;\r\n    //background-color: red;\r\n    &-select {\r\n      width: 100px;\r\n      align-self: center;\r\n      float: left;\r\n      margin-top: 14px;\r\n      margin-right: 32px;\r\n\r\n      .ivu-select-selection {\r\n        height: 28px;\r\n      }\r\n    }\r\n\r\n    &-user {\r\n      .user-name {\r\n        display: flex;\r\n\r\n        i {\r\n          line-height: 56px;\r\n        }\r\n\r\n        span {\r\n          display: block;\r\n          width: 160px;\r\n          overflow: hidden;\r\n          white-space: nowrap;\r\n          text-overflow: ellipsis;\r\n          text-align: left;\r\n        }\r\n      }\r\n\r\n      width: 143px;\r\n      float: left;\r\n      font-size: 16px;\r\n      padding-right: 10px;\r\n\r\n      .ivu-dropdown-rel {\r\n        text-align: center;\r\n\r\n        a {\r\n          color: #ffffff;\r\n        }\r\n\r\n        i {\r\n          font-size: 25px;\r\n          padding-right: 10px;\r\n        }\r\n\r\n        height: 56px;\r\n        line-height: 56px;\r\n      }\r\n\r\n      .logout {\r\n        border-top: 1px solid #ccc;\r\n        display: flex;\r\n\r\n        .logout-text {\r\n          flex: 1;\r\n        }\r\n      }\r\n\r\n      :hover {\r\n        color: #49accf;\r\n      }\r\n    }\r\n\r\n    &-action {\r\n      background: #1f3c50;\r\n      height: 56px;\r\n      width: 56px;\r\n      float: left;\r\n\r\n      .arrow {\r\n        margin-top: 18px;\r\n        margin-left: 18px;\r\n        width: 20px;\r\n        height: 20px;\r\n        display: block;\r\n        background-size: 100%;\r\n      }\r\n\r\n      .left-arrow {\r\n        background: url(\"~@/assets/images/icon_left-arrow.svg\") no-repeat center;\r\n      }\r\n\r\n      .right-arrow {\r\n        background: url(\"~@/assets/images/icon_right-arrow.svg\") no-repeat\r\n          center;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.header-menu {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: left;\r\n  color: #5f6f81;\r\n  background-color: #ffffff;\r\n  // text-align: center;\r\n  height: 56px;\r\n  // line-height: 56px;\r\n  font-size: 18px;\r\n  font-weight: bold;\r\n  box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.15);\r\n  margin-bottom: 5px;\r\n}\r\n\r\n.header-menu ul {\r\n  width: 100%;\r\n  list-style-type: none;\r\n  margin: 0;\r\n  padding: 0;\r\n  overflow: hidden;\r\n  // text-align: left;\r\n  .active {\r\n    color: #0d5594;\r\n  }\r\n}\r\n\r\n.header-menu ul.first > li {\r\n  display: inline-block;\r\n  /* You can also add some margins here to make it look prettier */\r\n  // margin-left: 40px;\r\n  zoom: 1;\r\n  cursor: pointer;\r\n  padding-right: 20px;\r\n\r\n  .menuIcon {\r\n    width: 24px;\r\n    vertical-align: -6px;\r\n    margin-right: 8px;\r\n  }\r\n}\r\n\r\n.activeTab {\r\n  color: #ffffff;\r\n  background-color: #31babb;\r\n  border-color: #31babb;\r\n}\r\n</style>\r\n\r\n<style lang=\"scss\" scoped>\r\n/deep/ .ivu-btn:hover {\r\n  color: #31babb;\r\n  // background-color: #31babb;\r\n  border-color: #31babb;\r\n}\r\n\r\n/deep/ .ivu-btn-group-large > .ivu-btn {\r\n  height: 40px;\r\n  padding: 0 15px;\r\n  font-size: 16px;\r\n  border-radius: 10px;\r\n}\r\n</style>"], "sourceRoot": "src/components/administrative/common"}]}