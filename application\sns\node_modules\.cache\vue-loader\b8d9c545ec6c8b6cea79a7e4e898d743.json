{"remainingRequest": "E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\src\\components\\administrative\\apps\\sns\\monitoring\\m6\\task\\searchModal.vue?vue&type=style&index=0&id=5fed9bdd&lang=scss&scoped=true&", "dependencies": [{"path": "E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\src\\components\\administrative\\apps\\sns\\monitoring\\m6\\task\\searchModal.vue", "mtime": 1754362736681}, {"path": "E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoNCi9kZWVwLyAuaXZ1LWJ0bi1zdWNjZXNzIHsNCiAgY29sb3I6ICNmZmY7DQogIGJhY2tncm91bmQtY29sb3I6ICMzMWJhYmI7DQogIGJvcmRlci1jb2xvcjogIzMxYmFiYjsNCn0NCg0KL2RlZXAvIC5pdnUtYnRuLXN1Y2Nlc3M6aG92ZXIgew0KICBjb2xvcjogI2ZmZiAhaW1wb3J0YW50Ow0KICBib3JkZXItY29sb3I6ICMzMWJhYmI7DQp9DQo="}, {"version": 3, "sources": ["searchModal.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAquBA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA", "file": "searchModal.vue", "sourceRoot": "src/components/administrative/apps/sns/monitoring/m6/task", "sourcesContent": ["<template>\r\n  <div>\r\n    <Row>\r\n      <Col span=\"21\" style=\"text-align: left\">\r\n        <span\r\n          style=\"padding-left: 5px\"\r\n          v-if=\"searchFormValidateSearch.action != '' ||\r\n        searchFormValidateSearch.eventName != '' ||\r\n        searchFormValidateSearch.serviceCode != '' ||\r\n        searchFormValidateSearch.resourceId != '' ||\r\n        searchFormValidateSearch.deviceType != '' ||\r\n        searchFormValidateSearch.objectName != '' ||\r\n        searchFormValidateSearch.description != '' ||\r\n        searchFormValidateSearch.startsAt != '' ||\r\n        searchFormValidateSearch.finishesAt != '' ||\r\n        searchFormValidateSearch.stationName != ''\r\n        \"\r\n        >{{ $t(\"LocaleString.L00262\") }}&nbsp;:</span>\r\n        <span\r\n          style=\"padding-left: 10px; font-weight: bolder\"\r\n          v-if=\"searchFormValidateSearch.startsAt != ''\"\r\n        >\r\n          {{\r\n          $t(\"LocaleString.L00141\") + '(' + parseTime(searchFormValidateSearch.startsAt) + ') ' }}\r\n        </span>\r\n        <span\r\n          style=\"padding-left: 10px; font-weight: bolder\"\r\n          v-if=\"searchFormValidateSearch.finishesAt != ''\"\r\n        >\r\n          {{\r\n          $t(\"LocaleString.L00142\") + '(' + parseTime(searchFormValidateSearch.finishesAt) + ') ' }}\r\n        </span>\r\n        <span\r\n          style=\"padding-left: 10px; font-weight: bolder\"\r\n          v-if=\"searchFormValidateSearch.action != ''\"\r\n        >\r\n          {{\r\n          $t(\"LocaleString.L00013\") + '(' + translateCondition(searchFormValidateSearch.action, 'action') + ') ' }}\r\n        </span>\r\n        <span\r\n          style=\"padding-left: 10px; font-weight: bolder\"\r\n          v-if=\"searchFormValidateSearch.eventName != ''\"\r\n        >\r\n          {{\r\n          $t(\"LocaleString.L00197\") + '(' + searchFormValidateSearch.eventName + ') ' }}\r\n        </span>\r\n        <span\r\n          style=\"padding-left: 10px; font-weight: bolder\"\r\n          v-if=\"searchFormValidateSearch.serviceCode != ''\"\r\n        >\r\n          {{\r\n          $t(\"LocaleString.L00101\") + '(' + translateCondition(searchFormValidateSearch.serviceCode, 'serviceCode') +\r\n          ')' }}\r\n        </span>\r\n        <span\r\n          style=\"padding-left: 10px; font-weight: bolder\"\r\n          v-if=\"searchFormValidateSearch.resourceId != ''\"\r\n        >\r\n          {{\r\n          $t(\"LocaleString.L00157\") + '(' + translateCondition(searchFormValidateSearch.resourceId, 'resourceId') +\r\n          ')' }}\r\n        </span>\r\n        <span\r\n          style=\"padding-left: 10px; font-weight: bolder\"\r\n          v-if=\"searchFormValidateSearch.deviceType != ''\"\r\n        >\r\n          {{\r\n          $t(\"LocaleString.L00223\") + '(' + translateCondition(searchFormValidateSearch.deviceType, 'deviceType') +\r\n          ')' }}\r\n        </span>\r\n        <span\r\n          style=\"padding-left: 10px; font-weight: bolder\"\r\n          v-if=\"searchFormValidateSearch.stationName != ''\"\r\n        >\r\n          {{\r\n          $t(\"LocaleString.L00161\") + '(' + translateCondition(searchFormValidateSearch.stationName, 'stationName') + ') '\r\n          }}\r\n        </span>\r\n        <span\r\n          style=\"padding-left: 10px; font-weight: bolder\"\r\n          v-if=\"searchFormValidateSearch.objectName != ''\"\r\n        >\r\n          {{\r\n          $t(\"LocaleString.L00200\") + '(' + searchFormValidateSearch.objectName + ') ' }}\r\n        </span>\r\n        <span\r\n          style=\"padding-left: 10px; font-weight: bolder\"\r\n          v-if=\"searchFormValidateSearch.description != ''\"\r\n        >\r\n          {{\r\n          $t(\"LocaleString.L00263\") + '(' + searchFormValidateSearch.description + ') ' }}\r\n        </span>\r\n        <Button\r\n          ghost\r\n          shape=\"circle\"\r\n          style=\"width: 20px; margin-left: 10px\"\r\n          @click=\"handleReset('searchFormValidate')\"\r\n          v-if=\"searchFormValidateSearch.action != '' ||\r\n        searchFormValidateSearch.eventName != '' ||\r\n        searchFormValidateSearch.serviceCode != '' ||\r\n        searchFormValidateSearch.resourceId != '' ||\r\n        searchFormValidateSearch.deviceType != '' ||\r\n        searchFormValidateSearch.objectName != '' ||\r\n        searchFormValidateSearch.description != '' ||\r\n        searchFormValidateSearch.startsAt != '' ||\r\n        searchFormValidateSearch.finishesAt != '' ||\r\n        searchFormValidateSearch.stationName != ''\r\n        \"\r\n        >\r\n          <img :src=\"resetIcon\" style=\"width: 30.4px; margin-left: -14.8px\" />\r\n        </Button>\r\n      </Col>\r\n      <Col span=\"3\" style=\"text-align: right\">\r\n        <Button ghost shape=\"circle\" style=\"width: 20px\" @click=\"openSearchObject\">\r\n          <img :src=\"searchIcon\" style=\"width: 30.4px; margin-left: -14.8px\" />\r\n        </Button>\r\n        <Button type=\"success\" icon=\"ios-download-outline\" @click=\"exportHandleSubmit()\">\r\n          {{ $t(\"LocaleString.B00009\")\r\n          }}\r\n        </Button>\r\n      </Col>\r\n    </Row>\r\n    <Modal v-model=\"modalSearch\" :closable=\"false\" :mask-closable=\"false\">\r\n      <Form\r\n        ref=\"searchFormValidate\"\r\n        :model=\"searchFormValidate\"\r\n        :rules=\"searchRuleValidate\"\r\n        label-position=\"top\"\r\n      >\r\n        <Row :class=\"rowHeight\" :gutter=\"16\">\r\n          <Col span=\"12\">\r\n            <FormItem class=\"search-form-item\" :label=\"$t('LocaleString.L00141')\" prop=\"startsAt\">\r\n              <DatePicker\r\n                type=\"datetime\"\r\n                v-model.trim=\"searchFormValidate.startsAt\"\r\n                :placeholder=\"$t('LocaleString.M00010', {\r\n              0: $t('LocaleString.L00141'),\r\n            })\r\n              \"\r\n                :transfer=\"true\"\r\n                @on-change=\"startsAtChange()\"\r\n              ></DatePicker>\r\n            </FormItem>\r\n          </Col>\r\n          <Col span=\"12\">\r\n            <FormItem class=\"search-form-item\" :label=\"$t('LocaleString.L00142')\" prop=\"finishesAt\">\r\n              <DatePicker\r\n                type=\"datetime\"\r\n                v-model.trim=\"searchFormValidate.finishesAt\"\r\n                :placeholder=\"$t('LocaleString.M00010', {\r\n              0: $t('LocaleString.L00142'),\r\n            })\r\n              \"\r\n                :transfer=\"true\"\r\n                @on-change=\"finishesAtChange()\"\r\n              ></DatePicker>\r\n            </FormItem>\r\n          </Col>\r\n          <Col span=\"12\">\r\n            <FormItem class=\"search-form-item\" :label=\"$t('LocaleString.L00013')\" prop=\"action\">\r\n              <Select v-model=\"searchFormValidate.action\" :transfer=\"true\">\r\n                <Option\r\n                  v-for=\"item in actionList\"\r\n                  :value=\"item.type\"\r\n                  :key=\"item.type\"\r\n                >{{ item.name }}</Option>\r\n              </Select>\r\n            </FormItem>\r\n          </Col>\r\n          <Col span=\"12\">\r\n            <FormItem class=\"search-form-item\" :label=\"$t('LocaleString.L00197')\" prop=\"eventName\">\r\n              <Input\r\n                v-model.trim=\"searchFormValidate.eventName\"\r\n                maxlength=\"64\"\r\n                :placeholder=\"$t('LocaleString.L00179')\"\r\n              ></Input>\r\n            </FormItem>\r\n          </Col>\r\n          <Col span=\"12\">\r\n            <FormItem\r\n              class=\"search-form-item\"\r\n              :label=\"$t('LocaleString.L00101')\"\r\n              prop=\"serviceCode\"\r\n            >\r\n              <Select\r\n                v-model=\"searchFormValidate.serviceCode\"\r\n                :transfer=\"true\"\r\n                :not-found-text=\"notFoundText\"\r\n              >\r\n                <Option value=\"all\">{{ $t(\"LocaleString.D00002\") }}</Option>\r\n                <Option\r\n                  v-for=\"item in serviceCodeList\"\r\n                  :value=\"item.code\"\r\n                  :key=\"item.code\"\r\n                >{{ item.name }}</Option>\r\n              </Select>\r\n            </FormItem>\r\n          </Col>\r\n          <Col span=\"12\">\r\n            <FormItem class=\"search-form-item\" :label=\"$t('LocaleString.L00157')\" prop=\"resourceId\">\r\n              <Select\r\n                v-model=\"searchFormValidate.resourceId\"\r\n                :transfer=\"true\"\r\n                :not-found-text=\"notFoundText\"\r\n              >\r\n                <Option value=\"all\">{{ $t(\"LocaleString.D00002\") }}</Option>\r\n                <Option\r\n                  v-for=\"item in resourceIdList\"\r\n                  :value=\"item.resourceId\"\r\n                  :key=\"item.resourceId\"\r\n                >{{ $t(\"LocaleString.\" + item.langsId) }}</Option>\r\n              </Select>\r\n            </FormItem>\r\n          </Col>\r\n          <Col span=\"12\">\r\n            <FormItem class=\"search-form-item\" :label=\"$t('LocaleString.L00223')\" prop=\"deviceType\">\r\n              <Select\r\n                v-model=\"searchFormValidate.deviceType\"\r\n                :transfer=\"true\"\r\n                :not-found-text=\"notFoundText\"\r\n              >\r\n                <Option value=\"all\">{{ $t(\"LocaleString.D00002\") }}</Option>\r\n                <Option\r\n                  v-for=\"item in deviceTypeMenu\"\r\n                  :value=\"item.type\"\r\n                  :key=\"item.type\"\r\n                >{{ item.name }}</Option>\r\n              </Select>\r\n            </FormItem>\r\n          </Col>\r\n          <Col span=\"12\">\r\n            <FormItem\r\n              class=\"search-form-item\"\r\n              :label=\"$t('LocaleString.L00161')\"\r\n              prop=\"stationName\"\r\n            >\r\n              <Select\r\n                v-model=\"searchFormValidate.stationName\"\r\n                :transfer=\"true\"\r\n                filterable\r\n                :not-found-text=\"notFoundText\"\r\n              >\r\n                <Option value=\"all\">{{ $t(\"LocaleString.D00002\") }}</Option>\r\n                <Option\r\n                  v-for=\"item in stationMenu\"\r\n                  :value=\"item.name\"\r\n                  :key=\"item.sid\"\r\n                >{{ item.name }}</Option>\r\n              </Select>\r\n            </FormItem>\r\n          </Col>\r\n\r\n          <Col span=\"12\">\r\n            <FormItem class=\"search-form-item\" :label=\"$t('LocaleString.L00200')\" prop=\"objectName\">\r\n              <Input\r\n                v-model.trim=\"searchFormValidate.objectName\"\r\n                maxlength=\"64\"\r\n                :placeholder=\"$t('LocaleString.L00179')\"\r\n              ></Input>\r\n            </FormItem>\r\n          </Col>\r\n          <Col span=\"12\">\r\n            <FormItem\r\n              class=\"search-form-item\"\r\n              :label=\"$t('LocaleString.L00263')\"\r\n              prop=\"description\"\r\n            >\r\n              <Input\r\n                v-model.trim=\"searchFormValidate.description\"\r\n                maxlength=\"64\"\r\n                :placeholder=\"$t('LocaleString.L00179')\"\r\n              ></Input>\r\n            </FormItem>\r\n          </Col>\r\n          <!-- <Col span=\"4\">\r\n                <div class=\"search-submit-x\">\r\n                  <Button\r\n                    type=\"success\"\r\n                    icon=\"ios-search\"\r\n                    long\r\n                    @click=\"searchHandleSubmit('searchFormValidate')\"\r\n                    >{{ $t(\"LocaleString.B20017\") }}</Button\r\n                  >\r\n                </div>\r\n                <div class=\"export-submit-x\" style=\"padding-top: 4px\">\r\n                  <Button\r\n                    type=\"success\"\r\n                    icon=\"ios-download-outline\"\r\n                    long\r\n                    @click=\"exportHandleSubmit()\"\r\n                    >{{ $t(\"LocaleString.B00009\") }}</Button\r\n                  >\r\n                </div>\r\n          </Col>-->\r\n        </Row>\r\n      </Form>\r\n      <div slot=\"footer\">\r\n        <div class=\"search-submit\">\r\n          <Button\r\n            type=\"success\"\r\n            icon=\"ios-search\"\r\n            @click=\"searchHandleSubmit('searchFormValidate')\"\r\n          >\r\n            {{\r\n            $t(\"LocaleString.B20017\") }}\r\n          </Button>\r\n          <Button @click=\"cancelModal\">{{ $t(\"LocaleString.B00015\") }}</Button>\r\n        </div>\r\n      </div>\r\n    </Modal>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport resetIcon from \"@/assets/images/ic_reset.svg\";\r\nimport searchIcon from \"@/assets/images/ic_search.svg\";\r\nimport Config from \"@/common/config\";\r\nlet moment = require(\"moment\");\r\n\r\nexport default {\r\n  props: [\r\n    \"serviceCodeMenu\",\r\n    \"stationMenu\",\r\n    \"targetDeviceType\",\r\n    \"targetObjName\",\r\n    \"targetServiceCode\"\r\n  ],\r\n  data() {\r\n    return {\r\n      searchStartDate: \"\",\r\n      searchEndDate: \"\",\r\n      modalSearch: false,\r\n      resetIcon: resetIcon,\r\n      searchIcon: searchIcon,\r\n      serviceCodeLocaleStringsList: [],\r\n      serviceCodeList: [],\r\n      resourceIdList: [],\r\n      defaultSelection: [],\r\n      notFoundText: this.$t(\"LocaleString.L00081\"),\r\n      rowHeight: \"row-height\",\r\n      searchFormValidate: {\r\n        action: \"all\",\r\n        eventName: \"\",\r\n        serviceCode:\r\n          this.targetServiceCode != \"\" ? this.targetServiceCode : \"all\",\r\n        resourceId: \"all\",\r\n        deviceType: this.targetDeviceType != \"\" ? this.targetDeviceType : \"all\",\r\n        objectName: this.targetObjName != \"\" ? this.targetObjName : \"\",\r\n        description: \"\",\r\n        startsAt: this.getStartsAtDefault(),\r\n        finishesAt: this.getFinishesAtDefault(),\r\n        stationName: \"all\"\r\n      },\r\n      actionList: [\r\n        {\r\n          type: \"all\",\r\n          name: this.$t(\"LocaleString.D00002\"),\r\n          index: \"1\"\r\n        },\r\n        {\r\n          type: \"10\",\r\n          name: this.$t(\"LocaleString.D00137\"),\r\n          index: \"2\"\r\n        },\r\n        {\r\n          type: \"20\",\r\n          name: this.$t(\"LocaleString.D00138\"),\r\n          index: \"3\"\r\n        },\r\n        {\r\n          type: \"30\",\r\n          name: this.$t(\"LocaleString.D00139\"),\r\n          index: \"4\"\r\n        }\r\n      ],\r\n      searchFormValidateSearch: null,\r\n      dateOptions: {\r\n        disabledDate(date) {\r\n          let startsAt = new Date(\r\n            Date.now() - 1000 * 60 * 60 * 24 * Config.SEARCH_ENABLE_DATES\r\n          );\r\n          startsAt.setHours(0, 0, 0, 0);\r\n          let finishesAt = new Date(Date.now() + 1000 * 60 * 60 * 24);\r\n          finishesAt.setHours(0, 0, 0, 0);\r\n\r\n          return date && (date < startsAt || date > finishesAt);\r\n        }\r\n      },\r\n      deviceTypeMenu: []\r\n    };\r\n  },\r\n  computed: {\r\n    searchRuleValidate() {\r\n      return {\r\n        startsAt: [\r\n          {\r\n            required: true,\r\n            message: this.$t(\"LocaleString.M00010\", {\r\n              0: this.$t(\"LocaleString.L00141\")\r\n            })\r\n          },\r\n          {\r\n            type: \"date\",\r\n            validator: (rule, value) =>\r\n              !value ||\r\n              !this.searchFormValidate.finishesAt ||\r\n              value <= this.searchFormValidate.finishesAt,\r\n            message: this.$t(\"LocaleString.W00036\")\r\n          }\r\n        ],\r\n        finishesAt: [\r\n          {\r\n            required: true,\r\n            message: this.$t(\"LocaleString.M00010\", {\r\n              0: this.$t(\"LocaleString.L00142\")\r\n            })\r\n          },\r\n          {\r\n            type: \"date\",\r\n            validator: (rule, value) =>\r\n              !value ||\r\n              !this.searchFormValidate.startsAt ||\r\n              value >= this.searchFormValidate.startsAt,\r\n            message: this.$t(\"LocaleString.W00036\")\r\n          }\r\n        ]\r\n      };\r\n    }\r\n  },\r\n  created() {\r\n    this.searchFormValidateSearch = JSON.parse(\r\n      JSON.stringify(this.searchFormValidate)\r\n    );\r\n  },\r\n  async mounted() {\r\n    await this.getServiceCodeMenu();\r\n    await this.getServiceCodeI18n();\r\n    await this.getDeviceDefaultList();\r\n    await this.getSelectList();\r\n    this.$emit(\"searchRequest\", {\r\n      isUserSubmit: false,\r\n      searchParams: this.getSearchParams()\r\n    });\r\n  },\r\n  methods: {\r\n    parseTime(day) {\r\n      let timedifference = (new Date().getTimezoneOffset() / 60) * -1;\r\n      let tmpOffsetDay = moment(day, \"YYYY-MM-DD HH:mm:ss\")\r\n        .add(timedifference, \"hours\")\r\n        .format(\"YYYY-MM-DD HH:mm:ss\");\r\n      return tmpOffsetDay;\r\n    },\r\n    translateCondition(item, type) {\r\n      if (item == undefined) {\r\n        return \"\";\r\n      }\r\n      if (item == \"all\") {\r\n        return this.$t(\"LocaleString.D00002\");\r\n      }\r\n\r\n      let translateName = \"\";\r\n      switch (type) {\r\n        case \"action\":\r\n          translateName = this.actionList.find(data => data.type == item).name;\r\n          break;\r\n        case \"objectType\":\r\n          translateName = this.objectTypeList.find(data => data.type == item)\r\n            .name;\r\n          break;\r\n        case \"deviceType\":\r\n          translateName = this.deviceTypeMenu.find(data => data.type == item)\r\n            .name;\r\n          break;\r\n        case \"stationName\":\r\n          translateName = item;\r\n          break;\r\n        case \"serviceCode\":\r\n          translateName = this.serviceCodeList.find(data => data.code == item)\r\n            .name;\r\n          break;\r\n        case \"resourceId\":\r\n          translateName = this.$t(\r\n            \"LocaleString.\" +\r\n              this.resourceIdList.find(data => data.resourceId == item).langsId\r\n          );\r\n          break;\r\n      }\r\n      return translateName;\r\n    },\r\n    handleReset(name) {\r\n      this.$refs[name].resetFields();\r\n      this.searchFormValidateSearch = this.searchFormValidate;\r\n      this.searchHandleSubmit(\"searchFormValidate\");\r\n    },\r\n    cancelModal() {\r\n      this.$refs[\"searchFormValidate\"].resetFields();\r\n      let newStartsAt = new Date(this.searchFormValidateSearch.startsAt);\r\n      let newFinishsAt = new Date(this.searchFormValidateSearch.finishesAt);\r\n      this.searchFormValidate = JSON.parse(\r\n        JSON.stringify(this.searchFormValidateSearch)\r\n      );\r\n      this.searchFormValidate.startsAt = newStartsAt;\r\n      this.searchFormValidate.finishesAt = newFinishsAt;\r\n\r\n      this.modalSearch = false;\r\n    },\r\n    openSearchObject() {\r\n      this.modalSearch = true;\r\n    },\r\n    async getServiceCodeMenu() {\r\n      let configParams = {\r\n        hasLicense: true,\r\n        category: \"event\"\r\n      };\r\n      this.serviceCodeList = await this.$service.getServicesMenu.send(\r\n        configParams\r\n      );\r\n\r\n      this.serviceCodeList = this.serviceCodeList.filter(\r\n        item => item.code !== \"NumberControl\"\r\n      );\r\n    },\r\n    async getServiceCodeI18n() {\r\n      let params = {\r\n        search: \"\"\r\n      };\r\n\r\n      let res = await this.$service.getServiceCodeLocaleStrings.send(params);\r\n      this.serviceCodeList.forEach(item => {\r\n        let targetData = res.results.filter(data => data.code == item.code);\r\n        if (targetData.length > 0) {\r\n          item.name = this.$t(\"LocaleString.\" + targetData[0].langs.nameId);\r\n        }\r\n      });\r\n    },\r\n    async getDeviceDefaultList() {\r\n      let configParams = {\r\n        inlinecount: true,\r\n        search: \"category in @SNS@Setting\"\r\n      };\r\n      let pocRes = await this.$service.getPOCProperties.send(configParams);\r\n      if (pocRes.count > 0) {\r\n        pocRes.results[0].properties.forEach(res => {\r\n          switch (res.key) {\r\n            case \"deviceSelectList\":\r\n              this.defaultSelection = res.value.includes(\"all\")\r\n                ? [\"all\"]\r\n                : res.value.split(\",\");\r\n              break;\r\n          }\r\n        });\r\n      }\r\n    },\r\n    async getSelectList() {\r\n      let resourceIdList = [];\r\n      let params = {\r\n        inlinecount: true,\r\n        search: \"active eq true\"\r\n      };\r\n      let res = await this.$service.getDeviceTypesmenu.send(params);\r\n      if (res && res.length > 0) {\r\n        res.forEach(item => {\r\n          let data = {\r\n            type: item.type,\r\n            name: this.$t(\"LocaleString.\" + item.langs.nameId)\r\n          };\r\n          // this.deviceTypeMenu.push(data);\r\n          if (this.defaultSelection.includes(\"all\")) {\r\n            this.deviceTypeMenu.push(data);\r\n          } else {\r\n            if (this.defaultSelection.includes(item.type)) {\r\n              this.deviceTypeMenu.push(data);\r\n            }\r\n          }\r\n\r\n          if (\r\n            item.supportDataEvent &&\r\n            item.supportDataEvent.some(e => e.serviceCode == \"SensorDataDriven\")\r\n          ) {\r\n            let listItem = item.supportDataEvent.find(\r\n              e => e.serviceCode == \"SensorDataDriven\"\r\n            ).sddResource;\r\n            resourceIdList = [\r\n              ...resourceIdList,\r\n              ...listItem.map(i => ({ langsId: i.langsId, resourceId: i.id }))\r\n            ];\r\n          }\r\n        });\r\n        let list = Array.from(new Set(resourceIdList.map(JSON.stringify))).map(\r\n          JSON.parse\r\n        );\r\n        list.sort((a, b) => {\r\n          const nameA = a.resourceId;\r\n          const nameB = b.resourceId;\r\n          if (nameA < nameB) {\r\n            return -1;\r\n          }\r\n          if (nameA > nameB) {\r\n            return 1;\r\n          }\r\n          // names must be equal\r\n          return 0;\r\n        });\r\n\r\n        this.resourceIdList = list.filter(\r\n          l => !l.resourceId.includes(\"1087\") && !l.resourceId.includes(\"1086\")\r\n        );\r\n        // this.deviceTypeMenu.sort((a, b) =>\r\n        //   a.name.localeCompare(b.name, \"zh-Hant\")\r\n        // );\r\n      }\r\n    },\r\n    getStartsAtDefault() {\r\n      let result = new Date(\r\n        Date.now() -\r\n          1000 * 3600 * 24 * (Config.SEARCH_POSTGRESQL_DATA_DATES - 1)\r\n      );\r\n      result.setHours(0, 0, 0, 0);\r\n      return result;\r\n    },\r\n    getFinishesAtDefault() {\r\n      let result = new Date(Date.now() + 1000 * 3600 * 24);\r\n      result.setHours(0, 0, 0, 0);\r\n      return result;\r\n    },\r\n    startsAtChange() {\r\n      this.$refs[\"searchFormValidate\"].validateField(\"finishesAt\");\r\n    },\r\n    finishesAtChange() {\r\n      this.$refs[\"searchFormValidate\"].validateField(\"startsAt\");\r\n    },\r\n    searchHandleSubmit(name) {\r\n      this.$refs[name].validate(valid => {\r\n        if (valid) {\r\n          this.searchFormValidateSearch = JSON.parse(\r\n            JSON.stringify(this.searchFormValidate)\r\n          );\r\n\r\n          this.$emit(\"searchRequest\", {\r\n            isUserSubmit: true,\r\n            searchParams: this.getSearchParams()\r\n          });\r\n\r\n          this.modalSearch = false;\r\n        }\r\n      });\r\n    },\r\n    getSearchParams() {\r\n      let str = \"active eq true and eventCode like @SNS@\";\r\n      let allDeviceTypeMenu = this.deviceTypeMenu.map(item => {\r\n        return item.type;\r\n      });\r\n      let allServiceCodeMenu = this.serviceCodeList.map(item => {\r\n        return item.code;\r\n      });\r\n      if (this.searchFormValidate.action !== \"all\") {\r\n        str += \" and action eq \" + this.searchFormValidate.action;\r\n      }\r\n      if (this.searchFormValidate.eventName !== \"\") {\r\n        str +=\r\n          \" and eventName like '\" + this.searchFormValidate.eventName + \"'  \";\r\n      }\r\n      if (this.searchFormValidate.serviceCode !== \"all\") {\r\n        str +=\r\n          \" and serviceCode eq '\" + this.searchFormValidate.serviceCode + \"'  \";\r\n      } else {\r\n        str += \" and serviceCode in \" + allServiceCodeMenu.join(\",\");\r\n      }\r\n\r\n      if (this.searchFormValidate.objectName !== \"\") {\r\n        str +=\r\n          \" and sponsorObjects.name like '\" +\r\n          this.searchFormValidate.objectName +\r\n          \"'  \";\r\n      }\r\n      if (this.searchFormValidate.description !== \"\") {\r\n        str +=\r\n          \" and description like '\" +\r\n          this.searchFormValidate.description +\r\n          \"'  \";\r\n      }\r\n      if (this.searchFormValidate.startsAt !== \"\") {\r\n        let timedifferenceStartDay =\r\n          (new Date().getTimezoneOffset() / 60) * -1 - 8;\r\n        let tmpOffsetStartDay = moment(\r\n          this.searchFormValidate.startsAt,\r\n          \"YYYY-MM-DD HH:mm:ss\"\r\n        )\r\n          .add(timedifferenceStartDay * -1, \"hours\")\r\n          .format(\"YYYY-MM-DD HH:mm:ss\");\r\n\r\n        let startsAt = tmpOffsetStartDay;\r\n        str += \" and startsAt gte '\" + startsAt + \"' \";\r\n        this.searchStartDate = startsAt;\r\n      }\r\n      if (this.searchFormValidate.finishesAt !== \"\") {\r\n        let timedifferencefinishDay =\r\n          (new Date().getTimezoneOffset() / 60) * -1 - 8;\r\n        let tmpOffsetFinishDay = moment(\r\n          this.searchFormValidate.finishesAt,\r\n          \"YYYY-MM-DD HH:mm:ss\"\r\n        )\r\n          .add(timedifferencefinishDay * -1, \"hours\")\r\n          .format(\"YYYY-MM-DD HH:mm:ss\");\r\n        let finishesAt = moment(tmpOffsetFinishDay).format(\r\n          \"YYYY-MM-DD HH:mm:ss\"\r\n        );\r\n        str += \" and startsAt lte '\" + finishesAt + \"' \";\r\n        this.searchEndDate = finishesAt;\r\n      }\r\n      if (this.searchFormValidate.stationName !== \"all\") {\r\n        str +=\r\n          \" and sponsorStations.name eq '\" +\r\n          this.searchFormValidate.stationName +\r\n          \"' \";\r\n      }\r\n      if (this.searchFormValidate.deviceType !== \"all\") {\r\n        str +=\r\n          \" and sponsorObjects.devices.type eq '\" +\r\n          this.searchFormValidate.deviceType +\r\n          \"'  \";\r\n      } else {\r\n        str +=\r\n          \" and sponsorObjects.devices.type in \" + allDeviceTypeMenu.join(\",\");\r\n      }\r\n      if (this.searchFormValidate.resourceId !== \"all\") {\r\n        str +=\r\n          \" and eventArgumentKeyId eq 4 and eventArgumentValue eq \" +\r\n          this.searchFormValidate.resourceId +\r\n          \"'  \";\r\n      }\r\n      return { search: str };\r\n    },\r\n    exportHandleSubmit() {\r\n      this.$emit(\"exportConfirm\");\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n/deep/ .ivu-btn-success {\r\n  color: #fff;\r\n  background-color: #31babb;\r\n  border-color: #31babb;\r\n}\r\n\r\n/deep/ .ivu-btn-success:hover {\r\n  color: #fff !important;\r\n  border-color: #31babb;\r\n}\r\n</style>\r\n"]}]}