{"remainingRequest": "E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\src\\components\\administrative\\apps\\sns\\monitoring\\m6\\physiological\\searchModal.vue?vue&type=style&index=0&id=62c313d6&lang=scss&scoped=true&", "dependencies": [{"path": "E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\src\\components\\administrative\\apps\\sns\\monitoring\\m6\\physiological\\searchModal.vue", "mtime": 1754362736680}, {"path": "E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKDQovZGVlcC8gLml2dS1idG4tc3VjY2VzcyB7DQogIGNvbG9yOiAjZmZmOw0KICBiYWNrZ3JvdW5kLWNvbG9yOiAjMzFiYWJiOw0KICBib3JkZXItY29sb3I6ICMzMWJhYmI7DQp9DQoNCi9kZWVwLyAuaXZ1LWJ0bi1zdWNjZXNzOmhvdmVyIHsNCiAgY29sb3I6ICNmZmYgIWltcG9ydGFudDsNCiAgYm9yZGVyLWNvbG9yOiAjMzFiYWJiOw0KfQ0K"}, {"version": 3, "sources": ["searchModal.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4oBA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA", "file": "searchModal.vue", "sourceRoot": "src/components/administrative/apps/sns/monitoring/m6/physiological", "sourcesContent": ["<template>\r\n  <div>\r\n    <Row>\r\n      <Col span=\"21\" style=\"text-align: left\" v-if=\"!searchFirstTime\">\r\n        <span\r\n          style=\"padding-left: 5px\"\r\n          v-if=\"searchFormValidateSearch.deviceType != '' ||\r\n        searchFormValidateSearch.objectName != '' ||\r\n        searchFormValidateSearch.devicePids != '' ||\r\n        searchFormValidateSearch.resourceIds != '' ||\r\n        searchFormValidateSearch.startedTime != '' ||\r\n        searchFormValidateSearch.endedTime != ''\r\n        \"\r\n        >{{ $t(\"LocaleString.L00262\") }}&nbsp;:</span>\r\n        <span\r\n          style=\"padding-left: 10px; font-weight: bolder\"\r\n          v-if=\"searchFormValidateSearch.startedTime != ''\"\r\n        >\r\n          {{\r\n          $t(\"LocaleString.L00141\") + '(' + parseTime(searchFormValidateSearch.startedTime) + ') ' }}\r\n        </span>\r\n        <span\r\n          style=\"padding-left: 10px; font-weight: bolder\"\r\n          v-if=\"searchFormValidateSearch.endedTime != ''\"\r\n        >\r\n          {{\r\n          $t(\"LocaleString.L00142\") + '(' + parseTime(searchFormValidateSearch.endedTime) + ') ' }}\r\n        </span>\r\n        <span\r\n          style=\"padding-left: 10px; font-weight: bolder\"\r\n          v-if=\"searchFormValidateSearch.deviceType != ''\"\r\n        >\r\n          {{\r\n          $t(\"LocaleString.L00223\") + '(' + translateCondition(searchFormValidateSearch.deviceType, 'deviceType') +\r\n          ')' }}\r\n        </span>\r\n        <span\r\n          style=\"padding-left: 10px; font-weight: bolder\"\r\n          v-if=\"searchFormValidateSearch.objectName != ''\"\r\n        >\r\n          {{\r\n          $t(\"LocaleString.L00092\") + '(' + searchFormValidateSearch.objectName + ') ' }}\r\n        </span>\r\n        <span\r\n          style=\"padding-left: 10px; font-weight: bolder\"\r\n          v-if=\"searchFormValidateSearch.devicePids != ''\"\r\n        >\r\n          {{\r\n          $t(\"LocaleString.L00090\") + '(' + searchFormValidateSearch.devicePids + ') ' }}\r\n        </span>\r\n        <span\r\n          style=\"padding-left: 10px; font-weight: bolder\"\r\n          v-if=\"searchFormValidateSearch.resourceIds != ''\"\r\n        >\r\n          {{\r\n          $t(\"LocaleString.L00158\") + '(' + translateCondition(searchFormValidateSearch.resourceIds, 'resourceIds') + ')'}}\r\n        </span>\r\n        <span style=\"padding-left: 10px; font-weight: bolder\">\r\n          {{\r\n          $t(\"LocaleString.L00157\") + '(' + translateCondition(searchFormValidateSearch.lineType, 'lineType') + ')'}}\r\n        </span>\r\n        <span\r\n          v-if=\"searchFormValidateSearch.lineType != 'RAW'\"\r\n          style=\"padding-left: 10px; font-weight: bolder\"\r\n        >{{$t(\"LocaleString.L30150\") + '(' + searchFormValidateSearch.interval + $t(\"LocaleString.L30027\") + ')'}}</span>\r\n\r\n        <!-- <Button\r\n          ghost\r\n          shape=\"circle\"\r\n          style=\"width: 20px; margin-left: 10px\"\r\n          @click=\"handleReset('searchFormValidate')\"\r\n          v-if=\"\r\n            searchFormValidateSearch.deviceType != '' ||\r\n            searchFormValidateSearch.objectName != '' ||\r\n            searchFormValidateSearch.devicePids != '' ||\r\n            searchFormValidateSearch.resourceIds != '' ||\r\n            searchFormValidateSearch.startedTime != '' ||\r\n            searchFormValidateSearch.endedTime != ''\r\n          \"\r\n        >\r\n          <img :src=\"resetIcon\" style=\"width: 30.4px; margin-left: -14.8px\" />\r\n        </Button>-->\r\n      </Col>\r\n      <Col span=\"3\" :offset=\"!searchFirstTime ? 0 : 21\" style=\"text-align: right\">\r\n        <Button ghost shape=\"circle\" style=\"width: 20px\" @click=\"openSearchObject\">\r\n          <img :src=\"searchIcon\" style=\"width: 30.4px; margin-left: -14.8px\" />\r\n        </Button>\r\n        <Button type=\"success\" icon=\"ios-download-outline\" @click=\"exportHandleSubmit()\">\r\n          {{ $t(\"LocaleString.B00009\")\r\n          }}\r\n        </Button>\r\n      </Col>\r\n    </Row>\r\n    <Modal v-model=\"modalSearch\" :closable=\"false\" :mask-closable=\"false\">\r\n      <Form\r\n        ref=\"searchFormValidate\"\r\n        :model=\"searchFormValidate\"\r\n        :rules=\"searchRuleValidate\"\r\n        label-position=\"top\"\r\n      >\r\n        <Row :class=\"rowHeight\" :gutter=\"16\">\r\n          <Col span=\"12\">\r\n            <FormItem\r\n              class=\"search-form-item\"\r\n              :label=\"$t('LocaleString.L00141')\"\r\n              prop=\"startedTime\"\r\n            >\r\n              <DatePicker\r\n                type=\"datetime\"\r\n                v-model.trim=\"searchFormValidate.startedTime\"\r\n                :placeholder=\"$t('LocaleString.M00010', { 0: $t('LocaleString.L00141') })\r\n              \"\r\n                :transfer=\"true\"\r\n                @on-change=\"startedTimeChange()\"\r\n              ></DatePicker>\r\n            </FormItem>\r\n          </Col>\r\n          <Col span=\"12\">\r\n            <FormItem class=\"search-form-item\" :label=\"$t('LocaleString.L00142')\" prop=\"endedTime\">\r\n              <DatePicker\r\n                type=\"datetime\"\r\n                v-model.trim=\"searchFormValidate.endedTime\"\r\n                :placeholder=\"$t('LocaleString.M00010', { 0: $t('LocaleString.L00142') })\r\n              \"\r\n                :transfer=\"true\"\r\n                @on-change=\"endedTimeChange()\"\r\n              ></DatePicker>\r\n            </FormItem>\r\n          </Col>\r\n          <Col span=\"12\">\r\n            <FormItem class=\"search-form-item\" :label=\"$t('LocaleString.L00223')\" prop=\"deviceType\">\r\n              <Select\r\n                :placeholder=\"$t('LocaleString.D00001')\"\r\n                v-model=\"searchFormValidate.deviceType\"\r\n                :transfer=\"true\"\r\n                clearable\r\n                :not-found-text=\"notFoundText\"\r\n                @on-change=\"deviceTypeChange()\"\r\n              >\r\n                <Option\r\n                  v-for=\"item in deviceTypeMenu\"\r\n                  :value=\"item.type\"\r\n                  :key=\"item.type\"\r\n                >{{ item.name }}</Option>\r\n              </Select>\r\n            </FormItem>\r\n          </Col>\r\n          <Col span=\"12\">\r\n            <FormItem class=\"search-form-item\" :label=\"$t('LocaleString.L00090')\" prop=\"devicePids\">\r\n              <Select\r\n                :placeholder=\"$t('LocaleString.D00001')\"\r\n                v-model=\"searchFormValidate.devicePids\"\r\n                :transfer=\"true\"\r\n                filterable\r\n                clearable\r\n                :not-found-text=\"notFoundText\"\r\n              >\r\n                <Option\r\n                  v-for=\"item in objectDeviceMenu\"\r\n                  :value=\"item.pid\"\r\n                  :key=\"item.pid\"\r\n                >{{ item.pid }}</Option>\r\n              </Select>\r\n            </FormItem>\r\n          </Col>\r\n          <Col span=\"12\">\r\n            <FormItem class=\"search-form-item\" :label=\"$t('LocaleString.L00092')\" prop=\"objectName\">\r\n              <Select\r\n                :placeholder=\"$t('LocaleString.D00001')\"\r\n                v-model=\"searchFormValidate.objectName\"\r\n                :transfer=\"true\"\r\n                filterable\r\n                clearable\r\n                :not-found-text=\"notFoundText\"\r\n                @on-change=\"objectNameChange()\"\r\n              >\r\n                <Option\r\n                  v-for=\"item in objectMenu\"\r\n                  :value=\"item.name\"\r\n                  :key=\"item.code\"\r\n                >{{ item.name }}</Option>\r\n              </Select>\r\n            </FormItem>\r\n          </Col>\r\n\r\n          <Col span=\"12\">\r\n            <FormItem\r\n              class=\"search-form-item\"\r\n              :label=\"$t('LocaleString.L00158')\"\r\n              prop=\"resourceIds\"\r\n            >\r\n              <Select\r\n                v-model=\"searchFormValidate.resourceIds\"\r\n                :transfer=\"true\"\r\n                :not-found-text=\"notFoundText\"\r\n              >\r\n                <Option value=\"all\">{{ $t(\"LocaleString.D00002\") }}</Option>\r\n                <Option\r\n                  v-for=\"item in deviceResourceIdMenu\"\r\n                  :value=\"item.id\"\r\n                  :key=\"item.id\"\r\n                >{{ item.name }}</Option>\r\n              </Select>\r\n            </FormItem>\r\n          </Col>\r\n          <Col span=\"12\">\r\n            <FormItem class=\"search-form-item\" :label=\"$t('LocaleString.L00157')\" prop=\"type\">\r\n              <Select\r\n                v-model=\"searchFormValidate.lineType\"\r\n                :transfer=\"true\"\r\n                :not-found-text=\"notFoundText\"\r\n              >\r\n                <Option\r\n                  v-for=\"item in lineTypeList\"\r\n                  :value=\"item.key\"\r\n                  :key=\"item.key\"\r\n                >{{ item.value }}</Option>\r\n              </Select>\r\n            </FormItem>\r\n          </Col>\r\n          <Col span=\"12\" v-if=\"searchFormValidate.lineType !== 'RAW'\">\r\n            <FormItem class=\"search-form-item\" :label=\"$t('LocaleString.L30150')\" prop=\"interval\">\r\n              <Select\r\n                v-model=\"searchFormValidate.interval\"\r\n                :transfer=\"true\"\r\n                :not-found-text=\"notFoundText\"\r\n              >\r\n                <Option v-for=\"item in 10\" :value=\"item\" :key=\"item\">{{ item }}</Option>\r\n              </Select>\r\n            </FormItem>\r\n          </Col>\r\n        </Row>\r\n      </Form>\r\n      <div slot=\"footer\">\r\n        <div class=\"search-submit\">\r\n          <Button\r\n            type=\"success\"\r\n            icon=\"ios-search\"\r\n            @click=\"searchHandleSubmit('searchFormValidate')\"\r\n          >\r\n            {{\r\n            $t(\"LocaleString.B20017\") }}\r\n          </Button>\r\n          <Button @click=\"cancelModal\">{{ $t(\"LocaleString.B00015\") }}</Button>\r\n        </div>\r\n      </div>\r\n    </Modal>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport resetIcon from \"@/assets/images/ic_reset.svg\";\r\nimport searchIcon from \"@/assets/images/ic_search.svg\";\r\nimport Config from \"@/common/config\";\r\nlet moment = require(\"moment\");\r\nexport default {\r\n  props: [\r\n    \"deviceType\",\r\n    \"objectMenu\",\r\n    \"resourceIdMenu\",\r\n    \"targetDeviceType\",\r\n    \"targetDeviceId\"\r\n  ],\r\n  data() {\r\n    return {\r\n      lineTypeList: [\r\n        { key: \"MEDIAN\", value: this.$t(\"LocaleString.L30178\") },\r\n        { key: \"MEAN\", value: this.$t(\"LocaleString.L30179\") },\r\n        { key: \"LAST\", value: this.$t(\"LocaleString.L30180\") },\r\n        { key: \"RAW\", value: this.$t(\"LocaleString.L30181\") }\r\n      ],\r\n      deviceEventTypeList: [],\r\n      searchStartDate: \"\",\r\n      searchEndDate: \"\",\r\n      searchFirstTime: true,\r\n      modalSearch: false,\r\n      resetIcon: resetIcon,\r\n      searchIcon: searchIcon,\r\n      defaultSelection: [],\r\n      deviceTypeMenu: [],\r\n      objectDeviceMenu: [],\r\n      deviceResourceIdMenu: [],\r\n      deviceResourceIdMenuforView: [],\r\n      notFoundText: this.$t(\"LocaleString.L00081\"),\r\n      rowHeight: \"row-height\",\r\n      searchFormValidate: {\r\n        deviceType: this.targetDeviceType != \"\" ? this.targetDeviceType : \"\",\r\n        objectName: \"\",\r\n        devicePids: this.targetDeviceId != \"\" ? this.targetDeviceId : \"\",\r\n        resourceIds: \"all\",\r\n        startedTime: this.getStartedTimeDefault(),\r\n        endedTime: this.getEndedTimeDefault(),\r\n        interval: 1,\r\n        lineType: \"MEDIAN\"\r\n      },\r\n      searchFormValidateSearch: null,\r\n      dateOptions: {\r\n        disabledDate(date) {\r\n          let startedTime = new Date(\r\n            Date.now() - 1000 * 60 * 60 * 24 * Config.SEARCH_ENABLE_DATES\r\n          );\r\n          startedTime.setHours(0, 0, 0, 0);\r\n          let endedTime = new Date(Date.now() + 1000 * 60 * 60 * 24);\r\n          endedTime.setHours(0, 0, 0, 0);\r\n\r\n          return date && (date < startedTime || date > endedTime);\r\n        }\r\n      }\r\n    };\r\n  },\r\n  computed: {\r\n    searchRuleValidate() {\r\n      return {\r\n        deviceType: {\r\n          required: true,\r\n          message: this.$t(\"LocaleString.M00010\", {\r\n            0: this.$t(\"LocaleString.L00223\")\r\n          })\r\n        },\r\n        devicePids: {\r\n          required: true,\r\n          message: this.$t(\"LocaleString.M00010\", {\r\n            0: this.$t(\"LocaleString.L00090\")\r\n          })\r\n        },\r\n        startedTime: [\r\n          {\r\n            required: true,\r\n            message: this.$t(\"LocaleString.M00010\", {\r\n              0: this.$t(\"LocaleString.L00141\")\r\n            })\r\n          },\r\n          {\r\n            type: \"date\",\r\n            validator: (rule, value) =>\r\n              !value ||\r\n              !this.searchFormValidate.endedTime ||\r\n              value <= this.searchFormValidate.endedTime,\r\n            message: this.$t(\"LocaleString.W00036\")\r\n          }\r\n        ],\r\n        endedTime: [\r\n          {\r\n            required: true,\r\n            message: this.$t(\"LocaleString.M00010\", {\r\n              0: this.$t(\"LocaleString.L00142\")\r\n            })\r\n          },\r\n          {\r\n            type: \"date\",\r\n            validator: (rule, value) =>\r\n              !value ||\r\n              !this.searchFormValidate.startedTime ||\r\n              value >= this.searchFormValidate.startedTime,\r\n            message: this.$t(\"LocaleString.W00036\")\r\n          }\r\n        ]\r\n      };\r\n    }\r\n  },\r\n  created() {\r\n    this.searchFormValidateSearch = JSON.parse(\r\n      JSON.stringify(this.searchFormValidate)\r\n    );\r\n  },\r\n  async mounted() {\r\n    await this.getDeviceDefaultList();\r\n    await this.getSelectList();\r\n    this.resetObjectDeviceMenu();\r\n    this.resetResourceIdsMenu();\r\n\r\n    if (this.targetDeviceId != \"\" && this.targetDeviceType != \"\") {\r\n      this.deviceTypeChange();\r\n      this.searchFormValidate.devicePids = this.targetDeviceId;\r\n      this.searchHandleSubmit(\"searchFormValidate\");\r\n    }\r\n  },\r\n  methods: {\r\n    parseTime(day) {\r\n      let timedifference = (new Date().getTimezoneOffset() / 60) * -1;\r\n      let tmpOffsetDay = moment(day, \"YYYY-MM-DD HH:mm:ss\")\r\n        .add(timedifference, \"hours\")\r\n        .format(\"YYYY-MM-DD HH:mm:ss\");\r\n      return tmpOffsetDay;\r\n    },\r\n    translateCondition(item, type) {\r\n      if (item == undefined) {\r\n        return \"\";\r\n      }\r\n      if (item == \"all\") {\r\n        return this.$t(\"LocaleString.D00002\");\r\n      }\r\n\r\n      let translateName = \"\";\r\n      switch (type) {\r\n        case \"lineType\":\r\n          translateName = this.lineTypeList.find(data => data.key == item)\r\n            .value;\r\n          break;\r\n        case \"deviceType\":\r\n          translateName = this.deviceTypeMenu.find(data => data.type == item)\r\n            .name;\r\n          break;\r\n        case \"resourceIds\":\r\n          translateName = this.deviceResourceIdMenuforView.find(\r\n            data => data.id == item\r\n          ).name;\r\n          break;\r\n      }\r\n      return translateName;\r\n    },\r\n    handleReset(name) {\r\n      this.$refs[name].resetFields();\r\n      this.searchFormValidateSearch = this.searchFormValidate;\r\n      this.searchHandleSubmit(\"searchFormValidate\");\r\n    },\r\n    cancelModal() {\r\n      this.$refs[\"searchFormValidate\"].resetFields();\r\n      if (!this.searchFirstTime) {\r\n        let newStartsAt = new Date(this.searchFormValidateSearch.startedTime);\r\n        let newFinishsAt = new Date(this.searchFormValidateSearch.endedTime);\r\n        this.searchFormValidate = JSON.parse(\r\n          JSON.stringify(this.searchFormValidateSearch)\r\n        );\r\n        this.searchFormValidate.startedTime = newStartsAt;\r\n        this.searchFormValidate.endedTime = newFinishsAt;\r\n      }\r\n      this.modalSearch = false;\r\n    },\r\n    openSearchObject() {\r\n      this.modalSearch = true;\r\n    },\r\n    async getDeviceDefaultList() {\r\n      let configParams = {\r\n        inlinecount: true,\r\n        search: \"category in @SNS@Setting\"\r\n      };\r\n      let pocRes = await this.$service.getPOCProperties.send(configParams);\r\n      if (pocRes.count > 0) {\r\n        pocRes.results[0].properties.forEach(res => {\r\n          switch (res.key) {\r\n            case \"deviceSelectList\":\r\n              this.defaultSelection = res.value.includes(\"all\")\r\n                ? [\"all\"]\r\n                : res.value.split(\",\");\r\n              break;\r\n          }\r\n        });\r\n      }\r\n    },\r\n    async getSelectList() {\r\n      let params = {\r\n        inlinecount: true,\r\n        search: \"active eq true\"\r\n      };\r\n      let res = await this.$service.getDeviceEventType.send(params);\r\n      if (res.results && res.results.length > 0) {\r\n        this.deviceEventTypeList = res.results;\r\n        res.results.forEach(item => {\r\n          if (item.isPhysiologic) {\r\n            let data = {\r\n              type: item.type,\r\n              name: this.$t(\"LocaleString.\" + item.deviceTypeNameStringId)\r\n            };\r\n\r\n            // this.deviceTypeMenu.push(data);\r\n            if (this.defaultSelection.includes(\"all\")) {\r\n              this.deviceTypeMenu.push(data);\r\n            } else {\r\n              if (this.defaultSelection.includes(item.type)) {\r\n                this.deviceTypeMenu.push(data);\r\n              }\r\n            }\r\n          }\r\n        });\r\n\r\n        // this.deviceTypeMenu.sort((a, b) =>\r\n        //   a.name.localeCompare(b.name, \"zh-Hant\")\r\n        // );\r\n      }\r\n\r\n      this.resourceIdMenu.forEach(obj => {\r\n        obj.types = Array.from(new Set(obj.types));\r\n      });\r\n    },\r\n    getStartedTimeDefault() {\r\n      let result = new Date(\r\n        Date.now() - 1000 * 3600 * 24 * (Config.SEARCH_INFLUXDB_DATA_DATES - 1)\r\n      );\r\n      // let result = new Date(\r\n      //   Date.now() - 1000 * 3600 * 24 * (Config.SEARCH_INFLUXDB_DATA_DATES - 1)\r\n      // );\r\n      result.setHours(0, 0, 0, 0);\r\n      return result;\r\n    },\r\n    getEndedTimeDefault() {\r\n      let result = new Date(Date.now() + 1000 * 3600 * 24);\r\n      result.setHours(0, 0, 0, 0);\r\n      return result;\r\n    },\r\n    startedTimeChange() {\r\n      this.$refs[\"searchFormValidate\"].validateField(\"endedTime\");\r\n    },\r\n    endedTimeChange() {\r\n      this.$refs[\"searchFormValidate\"].validateField(\"startedTime\");\r\n    },\r\n    deviceTypeChange() {\r\n      if (!this.searchFormValidate.deviceType) {\r\n        this.searchFormValidate.deviceType = \"\";\r\n      }\r\n      this.searchFormValidate.devicePids = \"\";\r\n      this.resetObjectDeviceMenu();\r\n      this.searchFormValidate.resourceIds = \"all\";\r\n      this.resetResourceIdsMenu();\r\n    },\r\n    objectNameChange() {\r\n      if (!this.searchFormValidate.objectName) {\r\n        this.searchFormValidate.objectName = \"\";\r\n      }\r\n      this.searchFormValidate.devicePids = \"\";\r\n      this.resetObjectDeviceMenu();\r\n    },\r\n    resetObjectDeviceMenu() {\r\n      let params = new URLSearchParams();\r\n      params.append(\"active\", true);\r\n      if (this.searchFormValidatedeviceType !== \"\") {\r\n        params.append(\"type\", this.searchFormValidate.deviceType);\r\n      }\r\n      if (this.searchFormValidate.objectName !== \"\") {\r\n        params.append(\"objectName\", this.searchFormValidate.objectName);\r\n      }\r\n\r\n      let _this = this;\r\n      this.$service.getDevicesMenuNoLoad.send(params).then(data => {\r\n        _this.objectDeviceMenu = data;\r\n\r\n        if (data.length == 1) {\r\n          _this.searchFormValidate.devicePids = data[0].pid;\r\n        }\r\n      });\r\n    },\r\n    resetResourceIdsMenu() {\r\n      let resourceIdList = [];\r\n      let deviceData = this.deviceEventTypeList.find(\r\n        item =>\r\n          item.type == this.searchFormValidate.deviceType && item.isPhysiologic\r\n      );\r\n      let resList = deviceData\r\n        ? deviceData.supportDataEvent.filter(item => item.sddResource)\r\n        : [];\r\n      if (resList.length > 0) {\r\n        resList[0].sddResource.forEach(item => {\r\n          if (\r\n            !resourceIdList.some(\r\n              r => r.id == item.id.substring(1, item.id.length)\r\n            )\r\n          ) {\r\n            resourceIdList.push({\r\n              id: item.id.substring(1, item.id.length),\r\n              name: this.$t(\"LocaleString.\" + item.langsId)\r\n            });\r\n          }\r\n        });\r\n      }\r\n      this.deviceResourceIdMenu = resourceIdList;\r\n    },\r\n    joinAllResourceIds() {\r\n      let result = \"\";\r\n      this.deviceResourceIdMenu.forEach(item => {\r\n        result += item.id + \",\";\r\n      });\r\n      return result.substring(0, result.length - 1);\r\n    },\r\n    searchHandleSubmit(name) {\r\n      this.$refs[name].validate(valid => {\r\n        if (valid) {\r\n          this.searchFormValidateSearch = JSON.parse(\r\n            JSON.stringify(this.searchFormValidate)\r\n          );\r\n          this.searchFirstTime = false;\r\n          this.$emit(\"searchRequest\", {\r\n            isUserSubmit: true,\r\n            searchParams: this.getSearchParams()\r\n          });\r\n          this.deviceResourceIdMenuforView = JSON.parse(\r\n            JSON.stringify(this.deviceResourceIdMenu)\r\n          );\r\n          this.modalSearch = false;\r\n        }\r\n      });\r\n    },\r\n    getSearchParams() {\r\n      let result = {};\r\n\r\n      if (this.searchFormValidate.deviceType !== \"\") {\r\n        result[\"deviceType\"] = this.searchFormValidate.deviceType;\r\n      }\r\n      if (this.searchFormValidate.devicePids !== \"\") {\r\n        result[\"devicePidLike\"] = this.searchFormValidate.devicePids;\r\n      }\r\n      if (this.searchFormValidate.resourceIds !== \"all\") {\r\n        result[\"resourceIds\"] = this.searchFormValidate.resourceIds;\r\n      } else {\r\n        result[\"resourceIds\"] = this.joinAllResourceIds();\r\n      }\r\n      if (this.searchFormValidate.startedTime !== \"\") {\r\n        result[\"startedTime\"] = this.searchFormValidate.startedTime.getTime();\r\n        let timedifferenceStartDay =\r\n          (new Date().getTimezoneOffset() / 60) * -1 - 8;\r\n        let tmpOffsetStartDay = moment(\r\n          this.searchFormValidate.startedTime,\r\n          \"YYYY-MM-DD HH:mm:ss\"\r\n        )\r\n          .add(timedifferenceStartDay * -1, \"hours\")\r\n          .format(\"YYYY-MM-DD HH:mm:ss\");\r\n\r\n        let startsAt = tmpOffsetStartDay;\r\n        this.searchStartDate = startsAt;\r\n      }\r\n      if (this.searchFormValidate.endedTime !== \"\") {\r\n        result[\"endedTime\"] = this.searchFormValidate.endedTime.getTime();\r\n        let timedifferencefinishDay =\r\n          (new Date().getTimezoneOffset() / 60) * -1 - 8;\r\n        let tmpOffsetFinishDay = moment(\r\n          this.searchFormValidate.endedTime,\r\n          \"YYYY-MM-DD HH:mm:ss\"\r\n        )\r\n          .add(timedifferencefinishDay * -1, \"hours\")\r\n          .format(\"YYYY-MM-DD HH:mm:ss\");\r\n        let finishesAt = moment(tmpOffsetFinishDay).format(\r\n          \"YYYY-MM-DD HH:mm:ss\"\r\n        );\r\n        this.searchEndDate = finishesAt;\r\n      } else {\r\n        result[\"endedTime\"] = Date.now() + this.getNextDayOffsetTime();\r\n      }\r\n\r\n      result[\"interval\"] = this.searchFormValidate.interval;\r\n      result[\"lineType\"] = this.searchFormValidate.lineType;\r\n      return result;\r\n    },\r\n    getNextDayOffsetTime() {\r\n      return 1 * 24 * 60 * 60 * 1000;\r\n    },\r\n    exportHandleSubmit() {\r\n      this.$emit(\"exportConfirm\");\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n/deep/ .ivu-btn-success {\r\n  color: #fff;\r\n  background-color: #31babb;\r\n  border-color: #31babb;\r\n}\r\n\r\n/deep/ .ivu-btn-success:hover {\r\n  color: #fff !important;\r\n  border-color: #31babb;\r\n}\r\n</style>\r\n"]}]}