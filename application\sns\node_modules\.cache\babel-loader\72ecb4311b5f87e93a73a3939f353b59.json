{"remainingRequest": "E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\node_modules\\babel-loader\\lib\\index.js!E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\node_modules\\string-replace-loader\\index.js??ref--13-0!E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\src\\components\\administrative\\apps\\sns\\planeSetting\\sitePlan\\dragContainNS2.vue?vue&type=script&lang=js&", "dependencies": [{"path": "E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\src\\components\\administrative\\apps\\sns\\planeSetting\\sitePlan\\dragContainNS2.vue", "mtime": 1754362736892}, {"path": "E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\node_modules\\string-replace-loader\\index.js", "mtime": 499162500000}, {"path": "E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["dragContainNS2.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAsFA,OAAA,OAAA,MAAA,4CAAA;AACA,OAAA,KAAA,MAAA,qDAAA;AACA,OAAA,KAAA,MAAA,qDAAA;AAEA,eAAA;AACA,EAAA,KAAA,EAAA,CAAA,WAAA,EAAA,KAAA,EAAA,MAAA,EAAA,mBAAA,CADA;AAEA,EAAA,IAFA,kBAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA,WAAA;AACA,MAAA,QAAA,EAAA,IADA;AAEA,MAAA,MAAA,EAAA,CAFA;AAGA,MAAA,WAAA,EAAA;AACA,QAAA,KAAA,EAAA,KAAA,MAAA,CAAA,SAAA,CAAA,KAAA,CAAA,WAAA,CAAA,QAAA,CAAA,CAAA,GAAA,GAAA,GAAA,KAAA,MAAA,CAAA,SAAA,CAAA,KAAA,CAAA,WAAA,CAAA,SAAA,CAAA,CAAA,GAAA,GAAA,GAAA,GADA;AAEA,QAAA,MAAA,EAAA,CAFA;AAGA,QAAA,IAAA,EAAA,KAAA,MAAA,CAAA,SAAA,CAAA,KAAA,CAAA,WAAA,CAAA,SAAA,CAAA,CAAA,GAAA,GAAA,GAAA,GAHA;AAIA;AACA;AACA;AACA,QAAA,GAAA,EAAA,CAPA;AAQA,mBAAA,CARA;AASA,QAAA,OAAA,EAAA,KAAA,MAAA,CAAA,SAAA,CAAA,MAAA,GAAA,EAAA,GAAA;AATA,OAHA;AAcA,MAAA,KAAA,EAAA,KAdA;AAeA,MAAA,KAAA,EAAA,KAfA;AAgBA,MAAA,UAAA,EAAA;AACA,QAAA,eAAA,EAAA;AADA;AAhBA,KAAA;AAoBA,GApCA;AAqCA,EAAA,KAAA,EAAA;AACA,IAAA,iBADA,+BACA;AACA,WAAA,IAAA;AACA;AAHA,GArCA;AA2CA,EAAA,OA3CA,qBA2CA;AAAA;;AACA,SAAA,WAAA,CAAA,GAAA,GAAA,IAAA;AACA,IAAA,UAAA,CAAA,YAAA;AAEA,MAAA,KAAA,CAAA,IAAA;AAEA,KAJA,EAIA,GAJA,CAAA;AAKA,GAlDA;AAmDA,EAAA,OAAA,EAAA;AACA,IAAA,IADA,kBACA;AACA,UAAA,IAAA,GAAA,QAAA,CAAA,cAAA,CAAA,KAAA,MAAA,CAAA,GAAA,CAAA,CADA,CAEA;AACA;AACA;;AACA,UAAA,CAAA,IAAA,EAAA;AACA;AACA;;AACA,UAAA,KAAA,GAAA,IAAA,CAAA,WAAA;AACA,UAAA,MAAA,GAAA,IAAA,CAAA,YAAA;AACA,WAAA,MAAA,GAAA,MAAA,GAAA,KAAA,CAVA,CAYA;;AACA,UAAA,KAAA,iBAAA,EAAA;AACA,QAAA,OAAA,CAAA,KAAA,MAAA,CAAA,GAAA,EAAA,KAAA,MAAA,CAAA,SAAA,CAAA,KAAA,CAAA,GAAA,EAAA,KAAA,MAAA,CAAA,SAAA,CAAA,EAAA,EAAA,KAAA,MAAA,CAAA,SAAA,CAAA,KAAA,CAAA,GAAA,EAAA,KAAA,SAAA,CAAA;AACA;;AACA,WAAA,WAAA,CAAA,MAAA,GAAA,CAAA,KAAA,MAAA,CAAA,SAAA,CAAA,KAAA,CAAA,WAAA,CAAA,QAAA,CAAA,CAAA,GAAA,KAAA,MAAA,CAAA,SAAA,CAAA,KAAA,CAAA,WAAA,CAAA,SAAA,CAAA,CAAA,IAAA,KAAA,MAAA,GAAA,GAAA,GAAA,GAAA;AACA,WAAA,WAAA,CAAA,GAAA,GAAA,KAAA,MAAA,CAAA,SAAA,CAAA,KAAA,CAAA,WAAA,CAAA,SAAA,CAAA,CAAA,GAAA,KAAA,MAAA,GAAA,GAAA,GAAA,GAAA;AACA,WAAA,UAAA,CAAA,eAAA,GAAA,KAAA,MAAA,CAAA,SAAA,CAAA,KAAA;AACA,KApBA;AAqBA,IAAA,SArBA,qBAqBA,KArBA,EAqBA;AACA;AAEA,UAAA,KAAA,SAAA,CAAA,QAAA,EAAA;AACA,QAAA,KAAA,CAAA,YAAA,GAAA,KAAA,SAAA,CAAA,YAAA;AACA,QAAA,KAAA,CAAA,IAAA,GAAA,KAAA,SAAA,CAAA,IAAA;AACA;;AAEA,MAAA,KAAA,CAAA,QAAA,CAAA,CAAA,GAAA,KAAA,CAAA,QAAA,CAAA,CAAA,GAAA,KAAA,MAAA;AACA,MAAA,KAAA,CAAA,SAAA,CAAA,CAAA,GAAA,KAAA,CAAA,SAAA,CAAA,CAAA,GAAA,KAAA,MAAA;AACA,WAAA,KAAA,CAAA,eAAA,EAAA,KAAA;AACA,KAhCA;AAiCA;AACA,IAAA,UAlCA,sBAkCA,IAlCA,EAkCA;AACA,MAAA,QAAA,CAAA,cAAA,CAAA,IAAA,CAAA,EAAA,EAAA,KAAA,CAAA,KAAA,GAAA,IAAA,CAAA,KAAA,CAAA,cAAA,CAAA,QAAA,CAAA,CAAA,GAAA,GAAA,GAAA,IAAA,CAAA,KAAA,CAAA,cAAA,CAAA,SAAA,CAAA,CAAA,GAAA,GAAA,GAAA,GAAA;AACA,MAAA,QAAA,CAAA,cAAA,CAAA,IAAA,CAAA,EAAA,EAAA,KAAA,CAAA,MAAA,GAAA,CAAA,IAAA,CAAA,KAAA,CAAA,cAAA,CAAA,QAAA,CAAA,CAAA,GAAA,IAAA,CAAA,KAAA,CAAA,cAAA,CAAA,SAAA,CAAA,CAAA,IAAA,KAAA,MAAA,GAAA,GAAA,GAAA,GAAA;AACA,MAAA,QAAA,CAAA,cAAA,CAAA,IAAA,CAAA,EAAA,EAAA,KAAA,CAAA,IAAA,GAAA,IAAA,CAAA,KAAA,CAAA,cAAA,CAAA,SAAA,CAAA,CAAA,GAAA,GAAA,GAAA,GAAA;AACA,MAAA,QAAA,CAAA,cAAA,CAAA,IAAA,CAAA,EAAA,EAAA,KAAA,CAAA,GAAA,GAAA,IAAA,CAAA,KAAA,CAAA,cAAA,CAAA,SAAA,CAAA,CAAA,GAAA,KAAA,MAAA,GAAA,GAAA,GAAA,GAAA;AACA,KAvCA;AAwCA;AACA,IAAA,SAzCA,qBAyCA,IAzCA,EAyCA;AACA,WAAA,KAAA,CAAA,SAAA,EAAA,IAAA;AACA,KA3CA;AA4CA;AACA,IAAA,OA7CA,mBA6CA,IA7CA,EA6CA;AACA,UAAA,SAAA,CAAA,CAAA,CAAA,KAAA,CAAA,EAAA;AACA,QAAA,QAAA,CAAA,cAAA,CAAA,SAAA,CAAA,CAAA,CAAA,EAAA,KAAA,CAAA,OAAA,GAAA,GAAA;AACA;;AACA,MAAA,QAAA,CAAA,cAAA,CAAA,IAAA,CAAA,KAAA,CAAA,GAAA,EAAA,KAAA,CAAA,OAAA,GAAA,IAAA;AACA;AAlDA;AAnDA,CAAA", "sourcesContent": ["<style lang=\"scss\">\r\n.dragContain {\r\n    width: 20px;\r\n    height: 20px;\r\n    position: absolute\r\n}\r\n\r\n.drag {\r\n    opacity: 0.7;\r\n    width: 100%;\r\n    height: 100%;\r\n    border: 1px solid #bbcabf;\r\n    box-shadow: 0 3px 6px rgba(0, 0, 0, .2);\r\n    word-break: break-all;\r\n    overflow: hidden;\r\n    background-color: #e3c1c1;\r\n    text-align: center;\r\n    font-size: 16px;\r\n}\r\n\r\n.circleBase {\r\n    opacity: 0.7;\r\n    border-radius: 50%;\r\n    width: 100%;\r\n    height: 100%;\r\n    border: 1px solid #bbcabf;\r\n    box-shadow: 0 3px 6px rgba(0, 0, 0, .2);\r\n    word-break: break-all;\r\n    overflow: hidden;\r\n    text-align: center;\r\n    background: #00afff !important;\r\n    color: white;\r\n    display: flex;\r\n    align-items: center;\r\n}\r\n\r\n.dragZoom {\r\n    position: absolute;\r\n    width: 10px;\r\n    height: 10px;\r\n    background-color: #c5a2a2;\r\n    right: 0;\r\n    bottom: 0;\r\n}\r\n\r\n.cross {\r\n    position: absolute;\r\n    height: 40px;\r\n    width: 40px;\r\n    top: -20px;\r\n    left: -20px;\r\n}\r\n\r\n.reset {\r\n    height: 24px;\r\n    width: 24px;\r\n    cursor: pointer;\r\n}\r\n\r\n.ivu-poptip-confirm {\r\n    position: absolute;\r\n    top: -12px;\r\n    right: -12px;\r\n}\r\n</style>\r\n<template>\r\n<div :id=\"childDrag.id\" class=\"animated dragContain zoomIn\" v-bind:style=\"styleObject\">\r\n    <div :id=\"childDrag.child.dID\" :class=\"[childDrag.isEntity?'circleBase':'drag']\" v-bind:style=\"styleColor\">\r\n        <p style=\"position: absolute;width: 100%;\">{{childDrag.name}}</p>\r\n    </div>\r\n    <div :id=\"childDrag.child.zID\" class=\"dragZoom\"></div>\r\n    <img class=\"cross\" :src=\"cross\" />\r\n    <!--\r\n    <Poptip\r\n      placement=\"bottom-end\"\r\n      confirm\r\n      @on-ok=\"resetItem(childDrag)\"\r\n      :title=\"$t('lang.common.whetherToReset')\">\r\n      <img class=\"reset\" :src=\"reset\" :title=\"$t('lang.common.reset')\"/>\r\n    </Poptip>\r\n    -->\r\n\r\n</div>\r\n</template>\r\n\r\n<script>\r\nimport dragDiv from '@/components/administrative/common/dragDiv'\r\nimport cross from '@/components/administrative/common/images/cross.png'\r\nimport reset from '@/components/administrative/common/images/reset.png'\r\n\r\nexport default {\r\n    props: ['childDrag', 'box', 'name', 'isDraggableRegion'],\r\n    data() {\r\n        // setTimeout(() => {\r\n        //   let oBox = document.getElementById(this._props.box)\r\n        //   console.log('this._props.box:'+JSON.stringify(this._props.box))\r\n        //   console.log('oBox.clientWidth:'+JSON.stringify(oBox.clientWidth))\r\n        //   console.log('oBox.clientHeight:'+JSON.stringify(oBox.clientHeight))\r\n        //   if (!oBox) {\r\n        //     return\r\n        //   }\r\n        //   let width = oBox.clientWidth\r\n        //   let height = oBox.clientHeight\r\n        //   this.height = height / width\r\n        // }, 300)\r\n\r\n        return {\r\n            position: null,\r\n            height: 0,\r\n            styleObject: {\r\n                width: (this._props.childDrag.child.coordinates.lowRight.x * 100 - this._props.childDrag.child.coordinates.upperLeft.x * 100) + '%',\r\n                height: 0,\r\n                left: this._props.childDrag.child.coordinates.upperLeft.x * 100 + '%',\r\n                //width:'10%',\r\n                //height: '10%',\r\n                //left: '10%',\r\n                top: 0,\r\n                'z-index': 1,\r\n                display: this._props.childDrag.isShow ? '' : 'none'\r\n            },\r\n            cross: cross,\r\n            reset: reset,\r\n            styleColor: {\r\n              backgroundColor: ''\r\n            }\r\n        }\r\n    },\r\n    watch: {\r\n        isDraggableRegion() {\r\n              this.init()         \r\n        }\r\n    },\r\n\r\n    mounted() {\r\n        this.styleObject.top = '5%'\r\n        setTimeout(() => {\r\n\r\n            this.init()\r\n\r\n        }, 500)\r\n    },\r\n    methods: {\r\n        init() {\r\n            let oBox = document.getElementById(this._props.box)\r\n            // console.log('this._props.box:' + JSON.stringify(this._props.box))\r\n            // console.log('oBox.clientWidth:' + JSON.stringify(oBox.clientWidth))\r\n            // console.log('oBox.clientHeight:' + JSON.stringify(oBox.clientHeight))\r\n            if (!oBox) {\r\n                return\r\n            }\r\n            let width = oBox.clientWidth\r\n            let height = oBox.clientHeight\r\n            this.height = height / width\r\n\r\n            // console.log('AAAA' + JSON.stringify(this._props))\r\n            if (this.isDraggableRegion) {\r\n                dragDiv(this._props.box, this._props.childDrag.child.dID, this._props.childDrag.id, this._props.childDrag.child.zID, this.dragValue)\r\n            }\r\n            this.styleObject.height = ((this._props.childDrag.child.coordinates.lowRight.y - this._props.childDrag.child.coordinates.upperLeft.y) / this.height) * 100 + '%'\r\n            this.styleObject.top = (this._props.childDrag.child.coordinates.upperLeft.y / this.height) * 100 + '%'\r\n            this.styleColor.backgroundColor = this._props.childDrag.color\r\n        },\r\n        dragValue(value) {\r\n            // this.position=value // debug\r\n\r\n            if (this.childDrag.isEntity) {\r\n                value.entityNodeId = this.childDrag.entityNodeId\r\n                value.code = this.childDrag.code\r\n            }\r\n\r\n            value.lowRight.y = value.lowRight.y * this.height\r\n            value.upperLeft.y = value.upperLeft.y * this.height\r\n            this.$emit('dragContainer', value)\r\n        },\r\n        // 重置\r\n        itemChange(item) {\r\n            document.getElementById(item.id).style.width = (item.child.oldCoordinates.lowRight.x * 100 - item.child.oldCoordinates.upperLeft.x * 100) + '%'\r\n            document.getElementById(item.id).style.height = ((item.child.oldCoordinates.lowRight.y - item.child.oldCoordinates.upperLeft.y) / this.height) * 100 + '%'\r\n            document.getElementById(item.id).style.left = item.child.oldCoordinates.upperLeft.x * 100 + '%'\r\n            document.getElementById(item.id).style.top = (item.child.oldCoordinates.upperLeft.y / this.height) * 100 + '%'\r\n        },\r\n        // 重置操作\r\n        resetItem(item) {\r\n            this.$emit('okReset', item)\r\n        },\r\n        // 选中\r\n        pitchUp(item) {\r\n            if (arguments[1] !== 0) {\r\n                document.getElementById(arguments[1]).style.opacity = 0.7\r\n            }\r\n            document.getElementById(item.child.dID).style.opacity = 0.95\r\n        }\r\n    }\r\n}\r\n</script>\r\n"], "sourceRoot": "src/components/administrative/apps/sns/planeSetting/sitePlan"}]}