{"remainingRequest": "E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\src\\components\\administrative\\apps\\sns\\planeSetting\\sitePlan\\pointDrag.vue?vue&type=style&index=1&id=04ac45ed&lang=scss&scoped=true&", "dependencies": [{"path": "E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\src\\components\\administrative\\apps\\sns\\planeSetting\\sitePlan\\pointDrag.vue", "mtime": 1754362736894}, {"path": "E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoNCi9kZWVwLyAuaXZ1LXRvb2x0aXAtY29udGVudCB7DQogIG1hcmdpbi1sZWZ0OiAwcHg7DQp9DQoNCi9kZWVwLyAuaXZ1LXRvb2x0aXAtY29udGVudCA+IC5pdnUtdG9vbHRpcC1hcnJvdyB7DQogIGxlZnQ6IDUwJTsNCiAgbWFyZ2luLWxlZnQ6IC01cHg7DQp9DQoNCi9kZWVwLyAuaXZ1LXRvb2x0aXAtY29udGVudCA+IC5pdnUtdG9vbHRpcC1pbm5lciB7DQogIG1hcmdpbi1sZWZ0OiAwcHg7DQp9DQo="}, {"version": 3, "sources": ["pointDrag.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgMA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA", "file": "pointDrag.vue", "sourceRoot": "src/components/administrative/apps/sns/planeSetting/sitePlan", "sourcesContent": ["<style lang=\"scss\">\r\n.dragContainPoint {\r\n  // width: 27px;\r\n  // height: 38px;\r\n  position: absolute;\r\n  top: 0;\r\n  // opacity: 0.7;\r\n  word-break: break-all;\r\n  text-align: center;\r\n  font-size: 16px;\r\n  z-index: 9999;\r\n\r\n  .cross {\r\n    position: absolute;\r\n    height: 20px;\r\n    width: 20px;\r\n    top: -10px;\r\n    left: -10px;\r\n  }\r\n\r\n  .stationText {\r\n    // position: absolute;\r\n    width: 40px;\r\n    height: 40px;\r\n    color: transparent;\r\n  }\r\n}\r\n\r\n.dragContainPointHeight {\r\n  height: 37px !important;\r\n}\r\n</style>\r\n<template>\r\n  <div\r\n    :id=\"item.id\"\r\n    class=\"animated dragContainPoint zoomIn\"\r\n    :class=\"{ dragContainPointHeight: pointType === 'guard' }\"\r\n    :style=\"pointStyle\"\r\n  >\r\n    <Tooltip :content=\"item.name\" placement=\"bottom\" :transfer=\"true\">\r\n      <div class=\"stationText\">1</div>\r\n    </Tooltip>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport pointDrag from \"./pointDrag\";\r\nimport point from \"@/components/administrative/common/images/pointPlane/fill-gps_ok.svg\";\r\nimport pointRed from \"@/components/administrative/common/images/pointPlane/fill-gps_red.svg\";\r\n\r\nexport default {\r\n  props: [\r\n    \"box\",\r\n    \"pointImg\",\r\n    \"item\",\r\n    \"pointActive\",\r\n    \"pointType\",\r\n    \"currentActivePoint\",\r\n    \"pxRate\",\r\n    \"pyRate\",\r\n    \"draggable\",\r\n  ],\r\n  data() {\r\n    setTimeout(() => {\r\n      // console.log('this.item.coordinates.y:'+this.item.coordinates.y)\r\n      // console.log('this.pyRate:'+this.pyRate)\r\n      // console.log('iconHeight:'+this.iconHeight)\r\n      this.pointStyle = {\r\n        background: \"url(\" + point + \") no-repeat\",\r\n        backgroundSize: \"cover\",\r\n        // 圖: 寬=699px, 高=524px\r\n        // 實際公尺： 寬=152.1664， 高=114.1248\r\n        // 比例 :   699/152.1664=4.5936 px/m  , 152.1664/699=0.2176\r\n        // 畫面長寬＝ 實際公尺* 4.5936 (px)\r\n        // 實際座標公尺 = 畫面長寬 * 0.2176 (m)\r\n        // top: (this.item.coordinates.y * this.pxRate)  + 'px',\r\n        // left: (this.item.coordinates.x * this.pxRate) +  'px',\r\n        top:\r\n          this.item.coordinates.y * this.pyRate -\r\n          parseInt(this.iconHeight) +\r\n          \"px\",\r\n        left:\r\n          this.item.coordinates.x * this.pxRate -\r\n          parseInt(this.iconWidth) / 2 +\r\n          1 +\r\n          \"px\", // 7為svg左右留邊px\r\n        width: this.iconWidth + \"px\",\r\n        height: this.iconHeight + \"px\",\r\n      };\r\n      // this.cross = {\r\n      //     background: 'url(' + cross + ') no-repeat',\r\n      //     backgroundSize: 'cover',\r\n      // }\r\n      // if (document.body.clientWidth < 1200) {\r\n      //     this.pointStyle.width = '20px'\r\n      //     this.pointStyle.height = '20px'\r\n      //     this.pointStyle.fontSize = '14px'\r\n      //     // this.cross.height = '10px'\r\n      //     // this.cross.width = '10px'\r\n      //     // this.cross.top = '-5px'\r\n      //     // this.cross.left = '-5px'\r\n      // }\r\n    }, 300);\r\n    return {\r\n      iconWidth: \"40\",\r\n      iconHeight: \"40\",\r\n      pointStyle: {},\r\n      // cross: {}\r\n    };\r\n  },\r\n  mounted() {\r\n    if (this.draggable) {\r\n      pointDrag(this._props.box, this.item.id, (value) => {\r\n        this.$emit(\"pointPosition\", value);\r\n      });\r\n    }\r\n    // console.log('iconSize:'+localStorage.getItem('sns_iconSize'))\r\n    let iconSize = localStorage.getItem(\"sns_iconSize\");\r\n    this.iconWidth = iconSize;\r\n    this.iconHeight = iconSize;\r\n    // let thisitem = document.getElementById('')\r\n  },\r\n  methods: {\r\n    pointDragPitchUp(item) {\r\n      if (arguments[1] !== 0) {\r\n        document.getElementById(arguments[1]).style.background =\r\n          \"url(\" + point + \") 0% 0% / cover no-repeat\";\r\n      }\r\n      document.getElementById(item.id).style.background =\r\n        \"url(\" + pointRed + \") 0% 0% / cover no-repeat\";\r\n    },\r\n    clearObject() {\r\n      // console.log('DDDD:'+this.item.id)\r\n      let container = document.getElementById(this.item.id);\r\n      // console.log('container:'+container)\r\n      container.onmousedown = function () {};\r\n      container.style.cursor = \"\";\r\n    },\r\n  },\r\n  watch: {\r\n    draggable: {\r\n      handler() {\r\n        if (this.draggable) {\r\n          pointDrag(this._props.box, this.item.id, (value) => {\r\n            this.$emit(\"pointPosition\", value);\r\n          });\r\n        } else {\r\n          this.clearObject();\r\n        }\r\n      },\r\n    },\r\n    item: function (newItem, oldItem) {\r\n      this.item = newItem;\r\n      // console.log('this.item.coordinates.x:'+this.item.coordinates.x)\r\n\r\n      document.getElementById(newItem.id).style.left =\r\n        this.item.coordinates.x * this.pxRate -\r\n        parseInt(this.iconWidth) / 2 +\r\n        1 +\r\n        \"px\";\r\n      document.getElementById(newItem.id).style.top =\r\n        this.item.coordinates.y * this.pyRate -\r\n        parseInt(this.iconHeight) +\r\n        \"px\";\r\n      // if (oldItem.id) {\r\n      //     document.getElementById(oldItem.id).style.background = 'url(' + this.pointImg + ') 0% 0% / cover no-repeat'\r\n      // }\r\n      // document.getElementById(newItem.id).style.background = 'url(' + this.pointActive + ') 0% 0% / cover no-repeat'\r\n\r\n      // document.getElementById(newItem.id).style.background = 'url(' + this.pointActive + ') 0% 0% / cover no-repeat'\r\n      // this.$forceUpdate()\r\n      // pointDrag(this._props.box, this.item.id, (value) => {\r\n      //     this.$emit('pointPosition', value)\r\n      // })\r\n    },\r\n    currentActivePoint: function (newItem, oldItem) {\r\n      if (oldItem) {\r\n        document.getElementById(oldItem).style.background =\r\n          \"url(\" + point + \") 0% 0% / cover no-repeat\";\r\n        document.getElementById(oldItem).style.zIndex = \"1\";\r\n      }\r\n      if (newItem && this.pointActive) {\r\n        document.getElementById(newItem).style.background =\r\n          \"url(\" + pointRed + \") 0% 0% / cover no-repeat\";\r\n        document.getElementById(newItem).style.zIndex = \"999\";\r\n      }\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n/deep/ .ivu-tooltip-content {\r\n  margin-left: 0px;\r\n}\r\n\r\n/deep/ .ivu-tooltip-content > .ivu-tooltip-arrow {\r\n  left: 50%;\r\n  margin-left: -5px;\r\n}\r\n\r\n/deep/ .ivu-tooltip-content > .ivu-tooltip-inner {\r\n  margin-left: 0px;\r\n}\r\n</style>"]}]}