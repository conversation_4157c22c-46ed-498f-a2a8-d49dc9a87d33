{"remainingRequest": "E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\node_modules\\babel-loader\\lib\\index.js!E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\node_modules\\string-replace-loader\\index.js??ref--13-0!E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\src\\components\\administrative\\systemManagement\\about.vue?vue&type=script&lang=js&", "dependencies": [{"path": "E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\src\\components\\administrative\\systemManagement\\about.vue", "mtime": 1754362736974}, {"path": "E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\node_modules\\string-replace-loader\\index.js", "mtime": 499162500000}, {"path": "E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["about.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAoCA,OAAA,MAAA,MAAA,iBAAA;;AACA,IAAA,MAAA,GAAA,OAAA,CAAA,QAAA,CAAA;;AACA,eAAA;AACA,EAAA,IADA,kBACA;AACA,WAAA;AACA,MAAA,MAAA,EAAA,KADA;AAEA,MAAA,UAAA,EAAA,EAFA;AAGA,MAAA,UAAA,EAAA,KAAA,EAAA,CAAA,qBAAA,CAHA;AAIA,MAAA,QAAA,EAAA,CACA;AACA,QAAA,KAAA,EAAA,KAAA,EAAA,CAAA,qBAAA,CADA;AAEA,QAAA,GAAA,EAAA,MAFA;AAGA,QAAA,KAAA,EAAA,OAHA;AAIA,QAAA,QAAA,EAAA;AAJA,OADA,EAOA;AACA,QAAA,KAAA,EAAA,KAAA,EAAA,CAAA,qBAAA,CADA;AAEA,QAAA,GAAA,EAAA,SAFA;AAGA,QAAA,QAAA,EAAA;AAHA,OAPA;AAJA,KAAA;AAkBA,GApBA;AAqBA,EAAA,OArBA,qBAqBA;AACA,SAAA,MAAA,GAAA,IAAA;AACA,SAAA,cAAA;AACA,SAAA,WAAA,GAHA,CAIA;AACA,GA1BA;AA2BA,EAAA,OAAA,EAAA;AACA,IAAA,eADA,6BACA;AACA,MAAA,UAAA,CACA,YAAA;AACA,YAAA,KAAA,GAAA,QAAA,CAAA,aAAA,CAAA,0BAAA,CAAA;;AACA,YAAA,KAAA,EAAA;AACA,UAAA,KAAA,CAAA,KAAA,CAAA,MAAA,GAAA,MAAA,CAAA,WAAA,GAAA,GAAA,GAAA,IAAA;AACA;AACA,OALA,CAKA,IALA,CAKA,IALA,CADA,EAOA,EAPA,CAAA;AASA,KAXA;AAYA,IAAA,WAZA,uBAYA,QAZA,EAYA;AACA,UAAA,SAAA,GAAA,CAAA;;AACA,UAAA,QAAA,CAAA,CAAA,CAAA,KAAA,GAAA,EAAA;AACA,QAAA,SAAA,GAAA,CAAA,CAAA;AACA,QAAA,QAAA,GAAA,QAAA,CAAA,MAAA,CAAA,CAAA,CAAA;AACA;;AACA,aAAA,UAAA,CAAA,EAAA,CAAA,EAAA;AACA,YAAA,MAAA,GACA,CAAA,CAAA,QAAA,CAAA,GAAA,CAAA,CAAA,QAAA,CAAA,GAAA,CAAA,CAAA,GAAA,CAAA,CAAA,QAAA,CAAA,GAAA,CAAA,CAAA,QAAA,CAAA,GAAA,CAAA,GAAA,CADA;AAEA,eAAA,MAAA,GAAA,SAAA;AACA,OAJA;AAKA,KAvBA;AAwBA,IAAA,UAxBA,wBAwBA;AACA,WAAA,cAAA;AACA,KA1BA;AA2BA,IAAA,cA3BA,4BA2BA;AACA,UAAA,KAAA,GAAA,IAAA;;AACA,MAAA,KAAA,CAAA,UAAA,CAAA,IAAA,CAAA;AACA,QAAA,IAAA,EAAA,KAAA,EAAA,CAAA,qBAAA,CADA;AAEA,QAAA,OAAA,EAAA,MAAA,CAAA;AAFA,OAAA;AAIA,KAjCA;AAkCA,IAAA,WAlCA,uBAkCA,OAlCA,EAkCA,QAlCA,EAkCA;AAAA;;AACA,UAAA,KAAA,GAAA,IAAA;;AACA,UAAA,GAAA,GAAA,gBAAA;AACA,UAAA,MAAA,GAAA;AACA,QAAA,MAAA,EAAA,GADA;AAEA,QAAA,WAAA,EAAA,IAFA,CAGA;AACA;AACA;;AALA,OAAA,CAHA,CAUA;AACA;;AACA,WAAA,QAAA,CAAA,iBAAA,CAAA,IAAA,CAAA,MAAA,EAAA,IAAA,CAAA,UAAA,IAAA,EAAA;AACA,YAAA,IAAA,CAAA,KAAA,GAAA,CAAA,EAAA;AACA,UAAA,KAAA,CAAA,UAAA,CAAA,IAAA,CAAA;AACA,YAAA,IAAA,EAAA,MAAA,CAAA,EAAA,CAAA,qBAAA,CADA;AAEA,YAAA,OAAA,EAAA,IAAA,CAAA,OAAA,CAAA,IAAA,CAAA,UAAA,IAAA;AAAA,qBAAA,IAAA,CAAA,IAAA,IAAA,YAAA,IAAA,IAAA,CAAA,OAAA,CAAA,SAAA;AAAA,aAAA,IAAA,IAAA,CAAA,OAAA,CAAA,IAAA,CAAA,UAAA,IAAA;AAAA,qBAAA,IAAA,CAAA,IAAA,IAAA,YAAA;AAAA,aAAA,EAAA,OAAA,CAAA,SAAA,GAAA;AAFA,WAAA;AAIA;AACA,OAPA;AAQA,KAtDA;AAuDA,IAAA,YAvDA,wBAuDA,GAvDA,EAuDA;AACA,UAAA,GAAA,EAAA;AACA,eAAA,MAAA,CAAA,GAAA,CAAA,CAAA,MAAA,CAAA,YAAA,CAAA;AACA;;AACA,aAAA,EAAA;AACA,KA5DA;AA6DA,IAAA,gBA7DA,4BA6DA,GA7DA,EA6DA;AACA,UAAA,GAAA,EAAA;AACA,eAAA,MAAA,CAAA,GAAA,CAAA,CAAA,MAAA,CAAA,qBAAA,CAAA;AACA;;AACA,aAAA,EAAA;AACA,KAlEA;AAmEA,IAAA,MAnEA,oBAmEA;AAAA;;AACA,WAAA,MAAA,GAAA,KAAA;AACA,MAAA,UAAA,CAAA,YAAA;AACA,QAAA,MAAA,CAAA,KAAA,CAAA,YAAA;AACA,OAFA,EAEA,GAFA,CAAA;AAGA;AAxEA;AA3BA,CAAA", "sourcesContent": ["<template>\r\n  <div class=\"personal-data\">\r\n    <Modal\r\n      class=\"about-data-modal\"\r\n      :mask-closable=\"false\"\r\n      v-model=\"isShow\"\r\n      class-name=\"vertical-center-modal\"\r\n      @on-cancel=\"cancel\"\r\n      width=\"850\"\r\n    >\r\n      <a slot=\"close\">\r\n        <Icon type=\"md-close\" color=\"#ADADAD\" size=\"24\"></Icon>\r\n      </a>\r\n      <div slot=\"header\" class=\"header\">\r\n        <i class=\"left\"></i>\r\n        <span class=\"title\"></span>\r\n          <span class=\"title\">{{ $t(\"LocaleString.L30052\") }}</span>\r\n        </span>\r\n        <!--\r\n        <Button class=\"es-btn\" type=\"primary\" icon=\"ios-refresh\" @click=\"refreshBtn\">Refresh</Button>\r\n        -->\r\n      </div>\r\n      <div class=\"content\">\r\n        <Table\r\n          class=\"about-table\"\r\n          :columns=\"columns1\"\r\n          :data=\"systemInfo\"\r\n          :no-data-text=\"noDataText\"\r\n        ></Table>\r\n      </div>\r\n      <div slot=\"footer\"></div>\r\n    </Modal>\r\n  </div>\r\n</template>\r\n\r\n<script type='es6'>\r\nimport Config from \"@/common/config\";\r\nlet moment = require(\"moment\");\r\nexport default {\r\n  data() {\r\n    return {\r\n      isShow: false,\r\n      systemInfo: [],\r\n      noDataText: this.$t(\"LocaleString.L00081\"),\r\n      columns1: [\r\n        {\r\n          title: this.$t(\"LocaleString.L30053\"),\r\n          key: \"name\",\r\n          width: \"380px\",\r\n          sortable: true,\r\n        },\r\n        {\r\n          title: this.$t(\"LocaleString.L00071\"),\r\n          key: \"version\",\r\n          sortable: true,\r\n        },\r\n      ],\r\n    };\r\n  },\r\n  mounted() {\r\n    this.isShow = true;\r\n    this.getSystemAbout();\r\n    this.getLincense();\r\n    //this.normalizeHeight();\r\n  },\r\n  methods: {\r\n    normalizeHeight() {\r\n      setTimeout(\r\n        function () {\r\n          const modal = document.querySelector(\".ivu-modal-body .content\");\r\n          if (modal) {\r\n            modal.style.height = window.innerHeight - 200 + \"px\";\r\n          }\r\n        }.bind(this),\r\n        50\r\n      );\r\n    },\r\n    dynamicSort(property) {\r\n      var sortOrder = 1;\r\n      if (property[0] === \"-\") {\r\n        sortOrder = -1;\r\n        property = property.substr(1);\r\n      }\r\n      return function (a, b) {\r\n        var result =\r\n          a[property] < b[property] ? -1 : a[property] > b[property] ? 1 : 0;\r\n        return result * sortOrder;\r\n      };\r\n    },\r\n    refreshBtn() {\r\n      this.getSystemAbout();\r\n    },\r\n    getSystemAbout() {\r\n      let _this = this;\r\n      _this.systemInfo.push({\r\n        name: this.$t(\"LocaleString.S00002\"),\r\n        version: Config.WEB_VERSION,\r\n      });\r\n    },\r\n    getLincense(current, pageSize) {\r\n      let _this = this\r\n      let str = 'active eq true'\r\n      let params = {\r\n          search: str,\r\n          inlinecount: true,\r\n          // sort: _this.sort,\r\n          // page: current,\r\n          // size: pageSize\r\n      }\r\n      // params = new URLSearchParams(params)\r\n      // this.$service.getUsers.requestCommon(_this.queryStr, current, pageSize)\r\n      this.$service.getServiceLicense.send(params).then((data) => {\r\n        if (data.count > 0) { \r\n           _this.systemInfo.push({\r\n            name: this.$t(\"LocaleString.L00103\"),\r\n            version: data.results.some(item=>item.code == \"LossSignal\" && item.license.expiresAt)?  data.results.find(item=>item.code == \"LossSignal\").license.expiresAt : \"-\",\r\n          });\r\n        }\r\n      });\r\n    },\r\n    computedDate(val) {\r\n      if (val) {\r\n        return moment(val).format(\"YYYY-MM-DD\");\r\n      }\r\n      return \"\";\r\n    },\r\n    computedDateTime(val) {\r\n      if (val) {\r\n        return moment(val).format(\"YYYY-MM-DD HH:mm:ss\");\r\n      }\r\n      return \"\";\r\n    },\r\n    cancel() {\r\n      this.isShow = false;\r\n      setTimeout(() => {\r\n        this.$emit(\"closeAbout\");\r\n      }, 500);\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n.about-data-modal {\r\n  // .vertical-center-modal {\r\n  //   display: flex;\r\n  //   align-items: center;\r\n  //   justify-content: center;\r\n  //   .ivu-modal {\r\n  //     top: 0;\r\n  //   }\r\n  // }\r\n  .ivu-modal-close {\r\n    top: 15px;\r\n    right: 20px;\r\n  }\r\n  .content {\r\n    padding: 4px 14px;\r\n    overflow-y: auto;\r\n    //height: 700px;\r\n    //height: 400px;\r\n  }\r\n\r\n  @media only screen and (min-width: 1200px) {\r\n    .ivu-modal-close {\r\n      top: 18px;\r\n    }\r\n  }\r\n  .ivu-modal-header {\r\n    padding: 15px 20px;\r\n    border-bottom: 1px solid #f7f7f7;\r\n    .header {\r\n      overflow: hidden;\r\n      .left {\r\n        float: left;\r\n        width: 6px;\r\n        height: 24px;\r\n        margin-right: 8px;\r\n        background-color: #0ac7c0;\r\n        border-radius: 3px;\r\n      }\r\n      .title {\r\n        float: left;\r\n        height: 24px;\r\n        line-height: 24px;\r\n        font-size: 14px;\r\n        font-weight: bold;\r\n        color: #999;\r\n        span {\r\n          color: #333;\r\n        }\r\n      }\r\n      .es-btn {\r\n        float: right;\r\n        margin-right: 35px;\r\n      }\r\n      @media only screen and (min-width: 1200px) {\r\n        .left {\r\n          height: 30px;\r\n        }\r\n        .title {\r\n          height: 30px;\r\n          line-height: 30px;\r\n          font-size: 18px;\r\n        }\r\n      }\r\n    }\r\n  }\r\n  .ivu-modal-body {\r\n    .content {\r\n      padding: 4px 14px;\r\n      overflow-y: auto;\r\n      .row {\r\n        margin-bottom: 20px;\r\n        .item {\r\n          padding: 15px;\r\n          border: 1px solid #f7f7f7;\r\n          border-radius: 4px;\r\n          label {\r\n            color: #999;\r\n            font-weight: bold;\r\n          }\r\n          p {\r\n            margin-top: 10px;\r\n            color: #333;\r\n          }\r\n        }\r\n      }\r\n      .form-item {\r\n        padding: 15px 15px 22px;\r\n        margin-bottom: 16px;\r\n        border-radius: 4px;\r\n        background-color: #f7f7f7;\r\n      }\r\n    }\r\n  }\r\n  .ivu-modal-footer {\r\n    border-top: none;\r\n    padding: 0;\r\n  }\r\n}\r\n</style>\r\n"], "sourceRoot": "src/components/administrative/systemManagement"}]}