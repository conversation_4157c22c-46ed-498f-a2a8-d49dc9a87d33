{"remainingRequest": "E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\src\\components\\administrative\\apps\\sns\\monitoring\\m6\\physiological\\searchModal.vue?vue&type=template&id=62c313d6&scoped=true&", "dependencies": [{"path": "E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\src\\components\\administrative\\apps\\sns\\monitoring\\m6\\physiological\\searchModal.vue", "mtime": 1754362736680}, {"path": "E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}]}