{"remainingRequest": "E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\node_modules\\babel-loader\\lib\\index.js!E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\node_modules\\string-replace-loader\\index.js??ref--13-0!E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\src\\components\\administrative\\systemManagement\\systemConfig.vue?vue&type=script&lang=js&", "dependencies": [{"path": "E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\src\\components\\administrative\\systemManagement\\systemConfig.vue", "mtime": 1754362736978}, {"path": "E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\node_modules\\string-replace-loader\\index.js", "mtime": 499162500000}, {"path": "E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\GitRepos\\FusionNetProject\\6849\\frontend\\application\\sns\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["systemConfig.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgnCA,OAAA,SAAA,MAAA,8BAAA;AACA,OAAA,QAAA,MAAA,6BAAA;AACA,OAAA,YAAA,MAAA,iCAAA;AACA,OAAA,MAAA,MAAA,iBAAA;AACA,OAAA,aAAA,MAAA,gDAAA;AACA,OAAA,QAAA,MAAA,uEAAA;;AACA,IAAA,UAAA,GAAA,OAAA,CAAA,mBAAA,CAAA,C,CAAA;;;AACA,IAAA,MAAA,GAAA,OAAA,CAAA,WAAA,CAAA;;AACA,IAAA,MAAA,GAAA,OAAA,CAAA,QAAA,CAAA;;AACA,eAAA;AACA,EAAA,IADA,kBACA;AAAA;;AACA,WAAA;AACA,MAAA,cAAA,EAAA;AACA,QAAA,eAAA,EAAA,KADA;AAEA,QAAA,GAAA,EAAA,EAFA;AAGA,QAAA,QAAA,EAAA,EAHA;AAIA,QAAA,MAAA,EAAA;AAJA,OADA;AAOA,MAAA,YAAA,EAAA,CACA;AAAA,QAAA,GAAA,EAAA,QAAA;AAAA,QAAA,KAAA,EAAA,KAAA,EAAA,CAAA,qBAAA;AAAA,OADA,EAEA;AAAA,QAAA,GAAA,EAAA,MAAA;AAAA,QAAA,KAAA,EAAA,KAAA,EAAA,CAAA,qBAAA;AAAA,OAFA,EAGA;AAAA,QAAA,GAAA,EAAA,MAAA;AAAA,QAAA,KAAA,EAAA,KAAA,EAAA,CAAA,qBAAA;AAAA,OAHA,CAPA;AAYA,MAAA,eAAA,EAAA,EAZA;AAaA,MAAA,eAAA,EAAA,EAbA;AAcA,MAAA,QAAA,EAAA,QAdA;AAeA,MAAA,YAAA,EAAA,YAfA;AAgBA,MAAA,YAAA,EAAA,EAhBA;AAiBA,MAAA,aAAA,EAAA,EAjBA;AAkBA,MAAA,KAAA,EAAA,EAlBA;AAmBA,MAAA,OAAA,EAAA,KAnBA;AAoBA,MAAA,gBAAA,EAAA,KApBA;AAqBA,MAAA,WAAA,EAAA,EArBA;AAsBA,MAAA,cAAA,EAAA;AACA,QAAA,KAAA,EAAA;AADA,OAtBA;AAyBA,MAAA,UAAA,EAAA,IAzBA;AA0BA,MAAA,gBAAA,EAAA,MAAA,CAAA,WAAA,GAAA,GA1BA;AA2BA,MAAA,UAAA,EAAA,QA3BA;AA4BA,MAAA,aAAA,EAAA,KA5BA;AA6BA,MAAA,SAAA,EAAA,SA7BA;AA8BA,MAAA,MAAA,EAAA,EA9BA;AA+BA,MAAA,MAAA,EAAA,EA/BA;AAgCA,MAAA,oBAAA,EAAA,EAhCA;AAiCA,MAAA,4BAAA,EAAA,EAjCA;AAkCA,MAAA,aAAA,EAAA,KAlCA;AAmCA,MAAA,iBAAA,EAAA,IAnCA;AAoCA,MAAA,oBAAA,EAAA,EApCA;AAqCA,MAAA,qBAAA,EAAA,EArCA;AAsCA,MAAA,oBAAA,EAAA,EAtCA;AAuCA,MAAA,wBAAA,EAAA,EAvCA;AAwCA,MAAA,UAAA,EAAA,UAxCA;AAyCA,MAAA,OAAA,EAAA,IAzCA;AA0CA,MAAA,UAAA,EAAA,IA1CA;AA2CA,MAAA,aAAA,EAAA,IA3CA;AA4CA,MAAA,OAAA,EAAA,IA5CA;AA6CA,MAAA,UAAA,EAAA,IA7CA;AA8CA,MAAA,OAAA,EAAA,IA9CA;AA+CA,MAAA,IAAA,EAAA,IA/CA;AAgDA,MAAA,MAAA,EAAA,KAhDA;AAiDA,MAAA,QAAA,EAAA,KAjDA;AAkDA,MAAA,SAAA,EAAA,KAlDA;AAmDA,MAAA,SAAA,EAAA,KAnDA;AAoDA,MAAA,SAAA,EAAA,KApDA;AAqDA,MAAA,SAAA,EAAA,KArDA;AAsDA,MAAA,QAAA,EAAA,QAtDA;AAuDA,MAAA,aAAA,EAAA,EAvDA;AAwDA,MAAA,iBAAA,EAAA,EAxDA;AAyDA,MAAA,oBAAA,EAAA,EAzDA;AA0DA,MAAA,eAAA,EAAA,EA1DA;AA2DA,MAAA,cAAA,EAAA,EA3DA;AA4DA,MAAA,kBAAA,EAAA,EA5DA;AA6DA,MAAA,UAAA,EAAA;AACA,QAAA,eAAA,EAAA,EADA;AAEA,QAAA,YAAA,EAAA,KAFA;AAGA,QAAA,WAAA,EAAA,CAAA,YAAA,CAHA;AAIA,QAAA,WAAA,EAAA,KAJA;AAKA,QAAA,UAAA,EAAA,KALA;AAMA,QAAA,cAAA,EAAA,EANA;AAOA,QAAA,SAAA,EAAA,EAPA;AAQA,QAAA,UAAA,EAAA,EARA;AASA,QAAA,OAAA,EAAA,CAAA,IAAA,EAAA,IAAA,EAAA,IAAA,EAAA,IAAA,EAAA,IAAA,EAAA,IAAA,EAAA,IAAA,CATA;AAUA,QAAA,QAAA,EAAA,IAVA;AAWA,QAAA,KAAA,EAAA,KAXA;AAYA,QAAA,WAAA,EAAA,CAZA;AAaA,QAAA,MAAA,EAAA,CACA,aADA,EAEA,WAFA,EAGA,aAHA,EAIA,eAJA,EAKA,MALA,CAbA;AAoBA,QAAA,QAAA,EAAA,IApBA;AAqBA,QAAA,UAAA,EAAA,IArBA;AAsBA,QAAA,aAAA,EAAA,IAtBA;AAuBA,QAAA,QAAA,EAAA,EAvBA;AAwBA,QAAA,MAAA,EAAA,KAxBA;AAyBA,QAAA,cAAA,EAAA,KAzBA;AA0BA,QAAA,aAAA,EAAA,GA1BA;AA2BA,QAAA,WAAA,EAAA,CA3BA;AA4BA,QAAA,UAAA,EAAA,CA5BA;AA6BA,QAAA,WAAA,EAAA,EA7BA;AA8BA,QAAA,UAAA,EAAA,EA9BA;AA+BA,QAAA,iBAAA,EAAA,IA/BA;AAgCA,QAAA,kBAAA,EAAA,EAhCA;AAiCA,QAAA,iBAAA,EAAA,IAjCA;AAkCA,QAAA,eAAA,EAAA,IAlCA;AAmCA,QAAA,SAAA,EAAA,EAnCA;AAoCA,QAAA,gBAAA,EAAA,EApCA;AAqCA,QAAA,OAAA,EAAA,EArCA;AAsCA,QAAA,YAAA,EAAA,KAtCA;AAuCA,QAAA,WAAA,EAAA,EAvCA;AAwCA,QAAA,WAAA,EAAA,EAxCA;AAyCA,QAAA,WAAA,EAAA,EAzCA;AA0CA,QAAA,WAAA,EAAA,EA1CA;AA2CA,QAAA,WAAA,EAAA,EA3CA;AA4CA,QAAA,UAAA,EAAA,SA5CA;AA6CA,QAAA,UAAA,EAAA;AA7CA,OA7DA;AA4GA,MAAA,OAAA,EAAA,EA5GA;AA6GA,MAAA,WAAA,EAAA,EA7GA;AA8GA,MAAA,aAAA,EAAA,EA9GA;AA+GA,MAAA,UAAA,EAAA,EA/GA;AAgHA,MAAA,UAAA,EAAA,EAhHA;AAiHA,MAAA,kBAAA,EAAA,EAjHA;AAkHA,MAAA,iBAAA,EAAA,EAlHA;AAmHA,MAAA,QAAA,EAAA,EAnHA;AAoHA,MAAA,eAAA,EAAA,EApHA;AAqHA,MAAA,cAAA,EAAA,EArHA;AAsHA,MAAA,mBAAA,EAAA,EAtHA;AAuHA,MAAA,MAAA,EAAA,KAvHA;AAwHA,MAAA,MAAA,EAAA,KAxHA;AA0HA,MAAA,UAAA,EAAA,EA1HA;AA2HA,MAAA,gBAAA,EAAA,EA3HA;AA4HA,MAAA,UAAA,EAAA,KAAA,EAAA,CAAA,qBAAA,CA5HA;AA6HA,MAAA,gBAAA,EAAA,CACA;AACA,QAAA,IAAA,EAAA,WADA;AAEA,QAAA,IAAA,EAAA,KAAA,EAAA,CAAA,qBAAA;AAFA,OADA,EAKA;AACA,QAAA,IAAA,EAAA,QADA;AAEA,QAAA,IAAA,EAAA,KAAA,EAAA,CAAA,qBAAA;AAFA,OALA,EASA;AACA,QAAA,IAAA,EAAA,OADA;AAEA,QAAA,IAAA,EAAA,KAAA,EAAA,CAAA,qBAAA;AAFA,OATA,EAaA;AACA,QAAA,IAAA,EAAA,OADA;AAEA,QAAA,IAAA,EAAA,KAAA,EAAA,CAAA,qBAAA;AAFA,OAbA,CA7HA;AA+IA,MAAA,cAAA,EAAA,CACA;AACA,QAAA,IAAA,EAAA,MADA;AAEA,QAAA,IAAA,EAAA,KAAA,EAAA,CAAA,qBAAA;AAFA,OADA,EAKA;AACA,QAAA,IAAA,EAAA,OADA;AAEA,QAAA,IAAA,EAAA,KAAA,EAAA,CAAA,qBAAA;AAFA,OALA,EASA;AACA,QAAA,IAAA,EAAA,OADA;AAEA,QAAA,IAAA,EAAA,KAAA,EAAA,CAAA,qBAAA;AAFA,OATA,EAaA;AACA,QAAA,IAAA,EAAA,MADA;AAEA,QAAA,IAAA,EAAA,KAAA,EAAA,CAAA,qBAAA;AAFA,OAbA,CA/IA;AAiKA,MAAA,iBAAA,EAAA,CACA;AACA,QAAA,KAAA,EAAA,KAAA,EAAA,CAAA,qBAAA,CADA;AAEA,QAAA,IAAA,EAAA,MAFA;AAGA,QAAA,KAAA,EAAA,EAHA;AAIA,QAAA,MAAA,EAAA,gBAAA,CAAA,EAAA,MAAA,EAAA;AACA,iBAAA,CAAA,CAAA,MAAA,EAAA,KAAA,CAAA,aAAA,CAAA,MAAA,CAAA,GAAA,CAAA,IAAA,CAAA,CAAA;AACA;AANA,OADA,EASA;AACA,QAAA,KAAA,EAAA,KAAA,EAAA,CAAA,qBAAA,CADA;AAEA,QAAA,IAAA,EAAA,SAFA;AAGA,QAAA,KAAA,EAAA;AAHA,OATA,EAcA;AACA,QAAA,KAAA,EAAA,KAAA,EAAA,CAAA,qBAAA,CADA;AAEA,QAAA,IAAA,EAAA,cAFA;AAGA,QAAA,KAAA,EAAA;AAHA,OAdA,EAmBA;AACA,QAAA,KAAA,EAAA,KAAA,EAAA,CAAA,qBAAA,CADA;AAEA,QAAA,IAAA,EAAA,cAFA;AAGA,QAAA,KAAA,EAAA;AAHA,OAnBA,EAwBA;AACA,QAAA,KAAA,EAAA,KAAA,EAAA,CAAA,qBAAA,CADA;AAEA,QAAA,IAAA,EAAA;AAFA,OAxBA,EA4BA;AACA,QAAA,KAAA,EAAA,KAAA,EAAA,CAAA,qBAAA,CADA;AAEA,QAAA,IAAA,EAAA,SAFA;AAGA,QAAA,KAAA,EAAA;AAHA,OA5BA,EAiCA;AACA,QAAA,KAAA,EAAA,KAAA,EAAA,CAAA,qBAAA,CADA;AAEA,QAAA,IAAA,EAAA,UAFA;AAGA,QAAA,KAAA,EAAA;AAHA,OAjCA,EAsCA;AACA,QAAA,KAAA,EAAA,KAAA,EAAA,CAAA,qBAAA,CADA;AAEA,QAAA,IAAA,EAAA,UAFA;AAGA,QAAA,KAAA,EAAA;AAHA,OAtCA,EA2CA;AACA,QAAA,KAAA,EAAA,KAAA,EAAA,CAAA,qBAAA,CADA;AAEA,QAAA,IAAA,EAAA,iBAFA;AAGA,QAAA,KAAA,EAAA;AAHA,OA3CA,EAgDA;AACA,QAAA,KAAA,EAAA,GADA;AAEA,QAAA,IAAA,EAAA,QAFA;AAGA,QAAA,KAAA,EAAA;AAHA,OAhDA,CAjKA;AAuNA,MAAA,wBAAA,EAAA,CACA;AACA,QAAA,KAAA,EAAA,KAAA,EAAA,CAAA,qBAAA,CADA;AAEA,QAAA,IAAA,EAAA;AAFA,OADA,EAKA;AACA,QAAA,KAAA,EAAA,KAAA,EAAA,CAAA,qBAAA,CADA;AAEA,QAAA,IAAA,EAAA;AAFA,OALA,EASA;AACA,QAAA,KAAA,EAAA,KAAA,EAAA,CAAA,qBAAA,CADA;AAEA,QAAA,IAAA,EAAA;AAFA,OATA,EAaA;AACA,QAAA,KAAA,EAAA,KAAA,EAAA,CAAA,qBAAA,CADA;AAEA,QAAA,IAAA,EAAA;AAFA,OAbA,CAvNA;AAyOA,MAAA,8BAAA,EAAA,CACA;AACA,QAAA,IAAA,EAAA,KAAA,EAAA,CAAA,qBAAA,CADA;AAEA,QAAA,QAAA,EAAA,CAFA;AAGA,QAAA,QAAA,EAAA,QAHA;AAIA,QAAA,WAAA,EAAA,IAJA;AAKA,QAAA,KAAA,EAAA;AALA,OADA,EAQA;AACA,QAAA,IAAA,EAAA,KAAA,EAAA,CAAA,qBAAA,CADA;AAEA,QAAA,QAAA,EAAA,CAFA;AAGA,QAAA,QAAA,EAAA,QAHA;AAIA,QAAA,WAAA,EAAA,IAJA;AAKA,QAAA,KAAA,EAAA;AALA,OARA,EAeA;AACA,QAAA,IAAA,EAAA,KAAA,EAAA,CAAA,qBAAA,CADA;AAEA,QAAA,QAAA,EAAA,CAFA;AAGA,QAAA,QAAA,EAAA,QAHA;AAIA,QAAA,WAAA,EAAA,IAJA;AAKA,QAAA,KAAA,EAAA;AALA,OAfA,EAsBA;AACA,QAAA,IAAA,EAAA,KAAA,EAAA,CAAA,qBAAA,CADA;AAEA,QAAA,QAAA,EAAA,CAFA;AAGA,QAAA,QAAA,EAAA,QAHA;AAIA,QAAA,WAAA,EAAA,IAJA;AAKA,QAAA,KAAA,EAAA;AALA,OAtBA,CAzOA;AAuQA,MAAA,uBAAA,EAAA,EAvQA;AAwQA,MAAA,oBAAA,EAAA,CACA;AACA,QAAA,IAAA,EAAA,KAAA,EAAA,CAAA,qBAAA,CADA;AAEA,QAAA,OAAA,EAAA,CAFA;AAGA,QAAA,KAAA,EAAA;AAHA,OADA,EAMA;AACA,QAAA,IAAA,EAAA,KAAA,EAAA,CAAA,qBAAA,CADA;AAEA,QAAA,OAAA,EAAA,CAFA;AAGA,QAAA,KAAA,EAAA;AAHA,OANA,EAWA;AACA,QAAA,IAAA,EAAA,UADA;AAEA,QAAA,OAAA,EAAA,CAFA;AAGA,QAAA,KAAA,EAAA;AAHA,OAXA,EAgBA;AACA,QAAA,IAAA,EAAA,kBADA;AAEA,QAAA,OAAA,EAAA,CAFA;AAGA,QAAA,KAAA,EAAA;AAHA,OAhBA,CAxQA;AA8RA,MAAA,uBAAA,EAAA,CACA;AACA,QAAA,IAAA,EAAA,MADA;AAEA,QAAA,OAAA,EAAA,KAFA;AAGA,QAAA,YAAA,EAAA,CAHA;AAIA,QAAA,YAAA,EAAA,CAJA;AAKA,QAAA,eAAA,EAAA,EALA;AAMA,QAAA,OAAA,EAAA,EANA;AAOA,QAAA,QAAA,EAAA,EAPA;AAQA,QAAA,QAAA,EAAA,EARA;AASA,QAAA,eAAA,EAAA;AATA,OADA,EAYA;AACA,QAAA,IAAA,EAAA,OADA;AAEA,QAAA,OAAA,EAAA,KAFA;AAGA,QAAA,YAAA,EAAA,CAHA;AAIA,QAAA,YAAA,EAAA,CAJA;AAKA,QAAA,eAAA,EAAA,EALA;AAMA,QAAA,OAAA,EAAA,EANA;AAOA,QAAA,QAAA,EAAA,EAPA;AAQA,QAAA,QAAA,EAAA,EARA;AASA,QAAA,eAAA,EAAA;AATA,OAZA,EAuBA;AACA,QAAA,IAAA,EAAA,OADA;AAEA,QAAA,OAAA,EAAA,KAFA;AAGA,QAAA,YAAA,EAAA,CAHA;AAIA,QAAA,YAAA,EAAA,CAJA;AAKA,QAAA,eAAA,EAAA,EALA;AAMA,QAAA,OAAA,EAAA,EANA;AAOA,QAAA,QAAA,EAAA,EAPA;AAQA,QAAA,QAAA,EAAA,EARA;AASA,QAAA,eAAA,EAAA;AATA,OAvBA,EAkCA;AACA,QAAA,IAAA,EAAA,MADA;AAEA,QAAA,OAAA,EAAA,KAFA;AAGA,QAAA,YAAA,EAAA,CAHA;AAIA,QAAA,YAAA,EAAA,CAJA;AAKA,QAAA,eAAA,EAAA,EALA;AAMA,QAAA,OAAA,EAAA,EANA;AAOA,QAAA,QAAA,EAAA,EAPA;AAQA,QAAA,QAAA,EAAA,EARA;AASA,QAAA,eAAA,EAAA;AATA,OAlCA,CA9RA;AA4UA,MAAA,gBAAA,EAAA,EA5UA;AA6UA,MAAA,aAAA,EAAA,CACA;AACA,QAAA,KAAA,EAAA,KAAA,EAAA,CAAA,qBAAA,CADA;AAEA,QAAA,GAAA,EAAA,MAFA;AAGA,QAAA,KAAA,EAAA,GAHA;AAIA,QAAA,MAAA,EAAA,gBAAA,CAAA,EAAA,MAAA,EAAA;AACA,iBAAA,CAAA,CAAA,MAAA,EAAA,KAAA,CAAA,aAAA,CAAA,MAAA,CAAA,GAAA,CAAA,IAAA,CAAA,CAAA;AACA;AANA,OADA,EASA;AACA,QAAA,KAAA,EAAA,KAAA,EAAA,CAAA,qBAAA,CADA;AAEA,QAAA,GAAA,EAAA;AAFA,OATA,EAaA;AACA,QAAA,KAAA,EAAA,KAAA,EAAA,CAAA,qBAAA,CADA;AAEA,QAAA,GAAA,EAAA;AAFA,OAbA,EAiBA;AACA,QAAA,KAAA,EAAA,KAAA,EAAA,CAAA,qBAAA,CADA;AAEA,QAAA,GAAA,EAAA;AAFA,OAjBA,EAqBA;AACA,QAAA,KAAA,EAAA,KAAA,EAAA,CAAA,qBAAA,CADA;AAEA,QAAA,GAAA,EAAA;AAFA,OArBA,EAyBA;AACA,QAAA,KAAA,EAAA,KAAA,EAAA,CAAA,qBAAA,CADA;AAEA,QAAA,GAAA,EAAA;AAFA,OAzBA,EA6BA;AACA,QAAA,KAAA,EAAA,KAAA,EAAA,CAAA,qBAAA,CADA;AAEA,QAAA,GAAA,EAAA;AAFA,OA7BA,CA7UA;AA+WA,MAAA,aAAA,EAAA,CACA;AACA,QAAA,KAAA,EAAA,KAAA,EAAA,CAAA,qBAAA,CADA;AAEA,QAAA,IAAA,EAAA,MAFA;AAGA,QAAA,KAAA,EAAA,GAHA;AAIA,QAAA,MAAA,EAAA,gBAAA,CAAA,EAAA,MAAA,EAAA;AACA,iBAAA,CAAA,CAAA,MAAA,EAAA,KAAA,CAAA,aAAA,CAAA,MAAA,CAAA,GAAA,CAAA,IAAA,CAAA,CAAA;AACA;AANA,OADA,EASA;AACA,QAAA,KAAA,EAAA,KAAA,EAAA,CAAA,qBAAA,CADA;AAEA,QAAA,IAAA,EAAA;AAFA,OATA,EAaA;AACA,QAAA,KAAA,EAAA,KAAA,EAAA,CAAA,qBAAA,CADA;AAEA,QAAA,IAAA,EAAA;AAFA,OAbA,EAiBA;AACA,QAAA,KAAA,EAAA,KAAA,EAAA,CAAA,qBAAA,CADA;AAEA,QAAA,IAAA,EAAA;AAFA,OAjBA,EAqBA;AACA,QAAA,KAAA,EAAA,KAAA,EAAA,CAAA,qBAAA,CADA;AAEA,QAAA,IAAA,EAAA;AAFA,OArBA,EAyBA;AACA,QAAA,KAAA,EAAA,KAAA,EAAA,CAAA,qBAAA,CADA;AAEA,QAAA,IAAA,EAAA;AAFA,OAzBA,EA6BA;AACA,QAAA,KAAA,EAAA,KAAA,EAAA,CAAA,qBAAA,CADA;AAEA,QAAA,IAAA,EAAA;AAFA,OA7BA,CA/WA;AAiZA,MAAA,YAAA,EAAA,CACA;AACA,QAAA,IAAA,EAAA,WADA;AAEA,QAAA,aAAA,EAAA,EAFA;AAGA,QAAA,YAAA,EAAA,EAHA;AAIA,QAAA,cAAA,EAAA,EAJA;AAKA,QAAA,aAAA,EAAA,EALA;AAMA,QAAA,eAAA,EAAA,EANA;AAOA,QAAA,cAAA,EAAA;AAPA,OADA,EAUA;AACA,QAAA,IAAA,EAAA,QADA;AAEA,QAAA,aAAA,EAAA,EAFA;AAGA,QAAA,YAAA,EAAA,EAHA;AAIA,QAAA,cAAA,EAAA,EAJA;AAKA,QAAA,aAAA,EAAA,EALA;AAMA,QAAA,eAAA,EAAA,EANA;AAOA,QAAA,cAAA,EAAA;AAPA,OAVA,EAmBA;AACA,QAAA,IAAA,EAAA,OADA;AAEA,QAAA,aAAA,EAAA,EAFA;AAGA,QAAA,YAAA,EAAA,EAHA;AAIA,QAAA,cAAA,EAAA,EAJA;AAKA,QAAA,aAAA,EAAA,EALA;AAMA,QAAA,eAAA,EAAA,EANA;AAOA,QAAA,cAAA,EAAA;AAPA,OAnBA,EA4BA;AACA,QAAA,IAAA,EAAA,OADA;AAEA,QAAA,aAAA,EAAA,EAFA;AAGA,QAAA,YAAA,EAAA,EAHA;AAIA,QAAA,cAAA,EAAA,EAJA;AAKA,QAAA,aAAA,EAAA,EALA;AAMA,QAAA,eAAA,EAAA,EANA;AAOA,QAAA,cAAA,EAAA;AAPA,OA5BA,CAjZA;AAubA,MAAA,gBAAA,EAAA,CACA;AACA,QAAA,IAAA,EAAA,WADA;AAEA,QAAA,aAAA,EAAA,EAFA;AAGA,QAAA,YAAA,EAAA,EAHA;AAIA,QAAA,cAAA,EAAA,EAJA;AAKA,QAAA,aAAA,EAAA,EALA;AAMA,QAAA,eAAA,EAAA,EANA;AAOA,QAAA,cAAA,EAAA;AAPA,OADA,EAUA;AACA,QAAA,IAAA,EAAA,QADA;AAEA,QAAA,aAAA,EAAA,EAFA;AAGA,QAAA,YAAA,EAAA,EAHA;AAIA,QAAA,cAAA,EAAA,EAJA;AAKA,QAAA,aAAA,EAAA,EALA;AAMA,QAAA,eAAA,EAAA,EANA;AAOA,QAAA,cAAA,EAAA;AAPA,OAVA,EAmBA;AACA,QAAA,IAAA,EAAA,OADA;AAEA,QAAA,aAAA,EAAA,EAFA;AAGA,QAAA,YAAA,EAAA,EAHA;AAIA,QAAA,cAAA,EAAA,EAJA;AAKA,QAAA,aAAA,EAAA,EALA;AAMA,QAAA,eAAA,EAAA,EANA;AAOA,QAAA,cAAA,EAAA;AAPA,OAnBA,EA4BA;AACA,QAAA,IAAA,EAAA,OADA;AAEA,QAAA,aAAA,EAAA,EAFA;AAGA,QAAA,YAAA,EAAA,EAHA;AAIA,QAAA,cAAA,EAAA,EAJA;AAKA,QAAA,aAAA,EAAA,EALA;AAMA,QAAA,eAAA,EAAA,EANA;AAOA,QAAA,cAAA,EAAA;AAPA,OA5BA,CAvbA;AA6dA,MAAA,OAAA,EAAA,CACA;AACA,QAAA,KAAA,EAAA,KAAA,EAAA,CAAA,qBAAA,CADA;AAEA,QAAA,IAAA,EAAA,MAFA,CAGA;;AAHA,OADA,EAMA;AACA,QAAA,KAAA,EAAA,KAAA,EAAA,CAAA,qBAAA,CADA;AAEA,QAAA,IAAA,EAAA;AAFA,OANA,CA7dA;AAweA,MAAA,YAAA,EAAA,CACA;AACA,QAAA,IAAA,EAAA,KAAA,EAAA,CAAA,qBAAA,CADA;AAEA,QAAA,OAAA,EAAA,EAFA;AAGA,QAAA,KAAA,EAAA;AAHA,OADA,CAxeA;AA+eA,MAAA,aAAA,EAAA,CACA;AACA,QAAA,IAAA,EAAA,KAAA,EAAA,CAAA,qBAAA,CADA;AAEA,QAAA,OAAA,EAAA,CAFA;AAGA,QAAA,KAAA,EAAA;AAHA,OADA,EAMA;AACA,QAAA,IAAA,EAAA,KAAA,EAAA,CAAA,qBAAA,CADA;AAEA,QAAA,OAAA,EAAA,EAFA;AAGA,QAAA,KAAA,EAAA;AAHA,OANA,EAWA;AACA,QAAA,IAAA,EAAA,KAAA,EAAA,CAAA,qBAAA,CADA;AAEA,QAAA,OAAA,EAAA,EAFA;AAGA,QAAA,KAAA,EAAA;AAHA,OAXA,CA/eA;AAggBA,MAAA,IAAA,EAAA,CACA;AACA,QAAA,IAAA,EAAA,KAAA,EAAA,CAAA,qBAAA,CADA;AAEA,QAAA,OAAA,EAAA,EAFA;AAGA,QAAA,KAAA,EAAA;AAHA,OADA,EAMA;AACA,QAAA,IAAA,EAAA,KAAA,EAAA,CAAA,qBAAA,CADA;AAEA,QAAA,OAAA,EAAA,EAFA;AAGA,QAAA,KAAA,EAAA;AAHA,OANA,EAWA;AACA,QAAA,IAAA,EAAA,KAAA,EAAA,CAAA,qBAAA,CADA;AAEA,QAAA,OAAA,EAAA,CAFA;AAGA,QAAA,KAAA,EAAA;AAHA,OAXA,EAgBA;AACA,QAAA,IAAA,EAAA,KAAA,EAAA,CAAA,qBAAA,CADA;AAEA,QAAA,OAAA,EAAA,CAFA;AAGA,QAAA,KAAA,EAAA;AAHA,OAhBA,EAqBA;AACA,QAAA,IAAA,EAAA,KAAA,EAAA,CAAA,qBAAA,CADA;AAEA,QAAA,OAAA,EAAA,CAFA;AAGA,QAAA,KAAA,EAAA;AAHA,OArBA,EA0BA;AACA,QAAA,IAAA,EAAA,KAAA,EAAA,CAAA,qBAAA,CADA;AAEA,QAAA,OAAA,EAAA,CAFA;AAGA,QAAA,KAAA,EAAA;AAHA,OA1BA,EA+BA;AACA,QAAA,IAAA,EAAA,KAAA,EAAA,CAAA,qBAAA,CADA;AAEA,QAAA,OAAA,EAAA,CAFA;AAGA,QAAA,KAAA,EAAA;AAHA,OA/BA,EAoCA;AACA,QAAA,IAAA,EAAA,KAAA,EAAA,CAAA,qBAAA,CADA;AAEA,QAAA,OAAA,EAAA,CAFA;AAGA,QAAA,KAAA,EAAA;AAHA,OApCA,EAyCA;AACA,QAAA,IAAA,EAAA,KAAA,EAAA,CAAA,qBAAA,CADA;AAEA,QAAA,OAAA,EAAA,CAFA;AAGA,QAAA,KAAA,EAAA;AAHA,OAzCA,EA8CA;AACA,QAAA,IAAA,EAAA,KAAA,EAAA,CAAA,qBAAA,CADA;AAEA,QAAA,OAAA,EAAA,CAFA;AAGA,QAAA,KAAA,EAAA;AAHA,OA9CA,EAmDA;AACA,QAAA,IAAA,EAAA,KAAA,EAAA,CAAA,qBAAA,CADA;AAEA,QAAA,OAAA,EAAA,EAFA;AAGA,QAAA,KAAA,EAAA;AAHA,OAnDA,EAwDA;AACA,QAAA,IAAA,EAAA,KAAA,EAAA,CAAA,qBAAA,CADA;AAEA,QAAA,OAAA,EAAA,EAFA;AAGA,QAAA,KAAA,EAAA,EAHA;AAIA,QAAA,YAAA,EAAA;AAJA,OAxDA,EA8DA;AACA,QAAA,IAAA,EAAA,KAAA,EAAA,CAAA,qBAAA,CADA;AAEA,QAAA,OAAA,EAAA,EAFA;AAGA,QAAA,KAAA,EAAA,EAHA;AAIA,QAAA,YAAA,EAAA;AAJA,OA9DA,EAoEA;AACA,QAAA,IAAA,EAAA,KAAA,EAAA,CAAA,qBAAA,CADA;AAEA,QAAA,OAAA,EAAA,EAFA;AAGA,QAAA,KAAA,EAAA,EAHA;AAIA,QAAA,YAAA,EAAA;AAJA,OApEA,EA0EA;AACA,QAAA,IAAA,EAAA,KAAA,EAAA,CAAA,qBAAA,CADA;AAEA,QAAA,OAAA,EAAA,EAFA;AAGA,QAAA,KAAA,EAAA,EAHA;AAIA,QAAA,YAAA,EAAA;AAJA,OA1EA,EAgFA;AACA,QAAA,IAAA,EAAA,KAAA,EAAA,CAAA,qBAAA,CADA;AAEA,QAAA,OAAA,EAAA,EAFA;AAGA,QAAA,KAAA,EAAA,EAHA;AAIA,QAAA,cAAA,EAAA;AAJA,OAhFA,EAsFA;AACA,QAAA,IAAA,EAAA,KAAA,EAAA,CAAA,qBAAA,CADA;AAEA,QAAA,OAAA,EAAA,EAFA;AAGA,QAAA,KAAA,EAAA,EAHA;AAIA,QAAA,qBAAA,EAAA;AAJA,OAtFA,EA4FA;AACA,QAAA,IAAA,EAAA,KAAA,EAAA,CAAA,qBAAA,CADA;AAEA,QAAA,OAAA,EAAA,EAFA;AAGA,QAAA,KAAA,EAAA,EAHA;AAIA,QAAA,oBAAA,EAAA;AAJA,OA5FA,EAkGA;AACA,QAAA,IAAA,EAAA,KAAA,EAAA,CAAA,qBAAA,CADA;AAEA,QAAA,OAAA,EAAA,EAFA;AAGA,QAAA,KAAA,EAAA;AAHA,OAlGA,EAuGA;AACA,QAAA,IAAA,EAAA,KAAA,EAAA,CAAA,qBAAA,CADA;AAEA,QAAA,OAAA,EAAA,EAFA;AAGA,QAAA,KAAA,EAAA;AAHA,OAvGA,EA4GA;AACA,QAAA,IAAA,EAAA,KAAA,EAAA,CAAA,qBAAA,CADA;AAEA,QAAA,OAAA,EAAA,EAFA;AAGA,QAAA,KAAA,EAAA;AAHA,OA5GA,EAiHA;AACA,QAAA,IAAA,EAAA,KAAA,EAAA,CAAA,qBAAA,CADA;AAEA,QAAA,OAAA,EAAA,EAFA;AAGA,QAAA,KAAA,EAAA;AAHA,OAjHA,EAuHA;AACA,QAAA,IAAA,EAAA,KAAA,EAAA,CAAA,qBAAA,CADA;AAEA,QAAA,OAAA,EAAA,EAFA;AAGA,QAAA,KAAA,EAAA;AAHA,OAvHA,EA4HA;AACA,QAAA,IAAA,EAAA,KAAA,EAAA,CAAA,qBAAA,CADA;AAEA,QAAA,OAAA,EAAA,EAFA;AAGA,QAAA,KAAA,EAAA;AAHA,OA5HA,EAkIA;AACA,QAAA,IAAA,EAAA,KAAA,EAAA,CAAA,qBAAA,CADA;AAEA,QAAA,OAAA,EAAA,EAFA;AAGA,QAAA,KAAA,EAAA;AAHA,OAlIA,EAwIA;AACA,QAAA,IAAA,EAAA,KAAA,EAAA,CAAA,qBAAA,CADA;AAEA,QAAA,OAAA,EAAA,EAFA;AAGA,QAAA,KAAA,EAAA;AAHA,OAxIA,EA6IA;AACA,QAAA,IAAA,EAAA,KAAA,EAAA,CAAA,qBAAA,CADA;AAEA,QAAA,OAAA,EAAA,EAFA;AAGA,QAAA,KAAA,EAAA;AAHA,OA7IA,EAkJA;AACA,QAAA,IAAA,EAAA,KAAA,EAAA,CAAA,qBAAA,CADA;AAEA,QAAA,OAAA,EAAA,CAFA;AAGA,QAAA,KAAA,EAAA;AAHA,OAlJA,EAuJA;AACA,QAAA,IAAA,EAAA,KAAA,EAAA,CAAA,qBAAA,CADA;AAEA,QAAA,OAAA,EAAA,EAFA;AAGA,QAAA,KAAA,EAAA;AAHA,OAvJA,CAhgBA;AA6pBA,MAAA,YAAA,EAAA,CACA;AACA,QAAA,KAAA,EAAA,OADA;AAEA,QAAA,KAAA,EAAA;AAFA,OADA,EAKA;AACA,QAAA,KAAA,EAAA,OADA;AAEA,QAAA,KAAA,EAAA;AAFA,OALA,CA7pBA;AAuqBA,MAAA,gBAAA,EAAA,CACA;AACA,QAAA,KAAA,EAAA,KAAA,EAAA,CAAA,qBAAA,CADA;AAEA,QAAA,KAAA,EAAA;AAFA,OADA,EAKA;AACA,QAAA,KAAA,EAAA,KAAA,EAAA,CAAA,qBAAA,CADA;AAEA,QAAA,KAAA,EAAA;AAFA,OALA,EASA;AACA,QAAA,KAAA,EAAA,KAAA,EAAA,CAAA,qBAAA,CADA;AAEA,QAAA,KAAA,EAAA;AAFA,OATA,EAaA;AACA,QAAA,KAAA,EAAA,KAAA,EAAA,CAAA,qBAAA,CADA;AAEA,QAAA,KAAA,EAAA;AAFA,OAbA,EAiBA;AACA,QAAA,KAAA,EAAA,KAAA,EAAA,CAAA,qBAAA,CADA;AAEA,QAAA,KAAA,EAAA;AAFA,OAjBA,CAvqBA;AA6rBA,MAAA,YAAA,EAAA,CACA;AACA,QAAA,KAAA,EAAA,QAAA,KAAA,EAAA,CAAA,qBAAA,CADA;AAEA,QAAA,KAAA,EAAA;AAFA,OADA,EAKA;AACA,QAAA,KAAA,EAAA,QAAA,KAAA,EAAA,CAAA,qBAAA,CADA;AAEA,QAAA,KAAA,EAAA;AAFA,OALA,EASA;AACA,QAAA,KAAA,EAAA,QAAA,KAAA,EAAA,CAAA,qBAAA,CADA;AAEA,QAAA,KAAA,EAAA;AAFA,OATA,EAaA;AACA,QAAA,KAAA,EAAA,SAAA,KAAA,EAAA,CAAA,qBAAA,CADA;AAEA,QAAA,KAAA,EAAA;AAFA,OAbA,CA7rBA;AA+sBA,MAAA,UAAA,EAAA,CACA;AACA,QAAA,KAAA,EAAA,KAAA,EAAA,CAAA,qBAAA,CADA;AAEA,QAAA,KAAA,EAAA;AAFA,OADA,EAKA;AACA,QAAA,KAAA,EAAA,KAAA,EAAA,CAAA,qBAAA,CADA;AAEA,QAAA,KAAA,EAAA;AAFA,OALA,EASA;AACA,QAAA,KAAA,EAAA,KAAA,EAAA,CAAA,qBAAA,CADA;AAEA,QAAA,KAAA,EAAA;AAFA,OATA,EAaA;AACA,QAAA,KAAA,EAAA,KAAA,EAAA,CAAA,qBAAA,CADA;AAEA,QAAA,KAAA,EAAA;AAFA,OAbA,EAiBA;AACA,QAAA,KAAA,EAAA,KAAA,EAAA,CAAA,qBAAA,CADA;AAEA,QAAA,KAAA,EAAA;AAFA,OAjBA,EAqBA;AACA,QAAA,KAAA,EAAA,KAAA,EAAA,CAAA,qBAAA,CADA;AAEA,QAAA,KAAA,EAAA;AAFA,OArBA,EAyBA;AACA,QAAA,KAAA,EAAA,KAAA,EAAA,CAAA,qBAAA,CADA;AAEA,QAAA,KAAA,EAAA;AAFA,OAzBA,EA6BA;AACA,QAAA,KAAA,EAAA,KAAA,EAAA,CAAA,qBAAA,CADA;AAEA,QAAA,KAAA,EAAA;AAFA,OA7BA,CAiCA;AACA;AACA;AACA;AApCA,OA/sBA;AAqvBA,MAAA,WAAA,EAAA,CACA;AACA,QAAA,KAAA,EAAA,KAAA,EAAA,CAAA,qBAAA,CADA;AAEA,QAAA,KAAA,EAAA;AAFA,OADA,EAKA;AACA,QAAA,KAAA,EAAA,KAAA,EAAA,CAAA,qBAAA,CADA;AAEA,QAAA,KAAA,EAAA;AAFA,OALA,EASA;AACA,QAAA,KAAA,EAAA,KAAA,EAAA,CAAA,qBAAA,CADA;AAEA,QAAA,KAAA,EAAA;AAFA,OATA,EAaA;AACA,QAAA,KAAA,EAAA,KAAA,EAAA,CAAA,qBAAA,CADA;AAEA,QAAA,KAAA,EAAA;AAFA,OAbA,EAiBA;AACA,QAAA,KAAA,EAAA,KAAA,EAAA,CAAA,qBAAA,CADA;AAEA,QAAA,KAAA,EAAA;AAFA,OAjBA,EAqBA;AACA,QAAA,KAAA,EAAA,KAAA,EAAA,CAAA,qBAAA,CADA;AAEA,QAAA,KAAA,EAAA;AAFA,OArBA,CArvBA;AA+wBA,MAAA,cAAA,EAAA,EA/wBA;AAgxBA,MAAA,oBAAA,EAAA,CACA;AACA,QAAA,KAAA,EAAA,GADA;AAEA,QAAA,KAAA,EAAA;AAFA,OADA,EAKA;AACA,QAAA,KAAA,EAAA,IADA;AAEA,QAAA,KAAA,EAAA;AAFA,OALA,EASA;AACA,QAAA,KAAA,EAAA,IADA;AAEA,QAAA,KAAA,EAAA;AAFA,OATA,EAaA;AACA,QAAA,KAAA,EAAA,KADA;AAEA,QAAA,KAAA,EAAA;AAFA,OAbA,CAhxBA;AAkyBA,MAAA,kBAAA,EAAA,CACA;AACA,QAAA,KAAA,EAAA,GADA;AAEA,QAAA,KAAA,EAAA;AAFA,OADA,EAKA;AACA,QAAA,KAAA,EAAA,IADA;AAEA,QAAA,KAAA,EAAA;AAFA,OALA,EASA;AACA,QAAA,KAAA,EAAA,IADA;AAEA,QAAA,KAAA,EAAA;AAFA,OATA,EAaA;AACA,QAAA,KAAA,EAAA,IADA;AAEA,QAAA,KAAA,EAAA;AAFA,OAbA,EAiBA;AACA,QAAA,KAAA,EAAA,IADA;AAEA,QAAA,KAAA,EAAA;AAFA,OAjBA;AAlyBA,KAAA;AAyzBA,GA3zBA;AA4zBA,EAAA,OA5zBA,qBA4zBA,CAAA,CA5zBA;AA6zBA,EAAA,OA7zBA,qBA6zBA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA,cAAA,OAAA,CAAA,GAAA,CAAA,mBAAA;AADA;AAAA,qBAEA,MAAA,CAAA,wBAAA,EAFA;;AAAA;AAAA;AAAA,qBAGA,MAAA,CAAA,kBAAA,EAHA;;AAAA;AAIA,cAAA,MAAA,CAAA,iBAAA;;AACA,cAAA,MAAA,CAAA,UAAA;;AACA,cAAA,MAAA,CAAA,cAAA;;AACA,cAAA,MAAA,CAAA,qBAAA;;AACA,cAAA,MAAA,CAAA,MAAA,GAAA,IAAA;AACA,cAAA,QATA,GASA,YAAA,CAAA,OAAA,CAAA,cAAA,CATA;AAUA,cAAA,MAAA,CAAA,OAAA,GAAA,QAAA,IAAA,MAAA,GAAA,IAAA,GAAA,KAAA;AAEA,cAAA,UAZA,GAYA,YAAA,CAAA,OAAA,CAAA,gBAAA,CAZA;;AAaA,kBAAA,UAAA,CAAA,QAAA,CAAA,kBAAA,KAAA,QAAA,KAAA,MAAA,EAAA;AACA,gBAAA,MAAA,CAAA,IAAA,gCAAA,MAAA,CAAA,aAAA,sBAAA,MAAA,CAAA,IAAA;AACA;;AACA,kBAAA,QAAA,KAAA,MAAA,EAAA;AACA,gBAAA,MAAA,CAAA,IAAA,gCAAA,MAAA,CAAA,YAAA,sBAAA,MAAA,CAAA,IAAA;AACA;;AACA,cAAA,MAAA,CAAA,gBAAA,GAAA,MAAA,CAAA,uBAAA;AACA,cAAA,MAAA,CAAA,uBAAA,GAAA,MAAA,CAAA,8BAAA;AACA,cAAA,MAAA,CAAA,gBAAA,CAAA,QAAA,EAAA,MAAA,CAAA,YAAA,EArBA,CAsBA;AACA;;AAvBA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAwBA,GAr1BA;AAs1BA,EAAA,aAt1BA,2BAs1BA;AACA,IAAA,MAAA,CAAA,mBAAA,CAAA,QAAA,EAAA,KAAA,YAAA;AACA,GAx1BA;AAy1BA,EAAA,OAAA,EAAA;AACA,IAAA,cADA,4BACA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA,gBAAA,QADA,GACA,CACA,MAAA,CAAA,EAAA,CAAA,qBAAA,CADA,EAEA,UAFA,EAGA,kBAHA,CADA;;AAAA,sBAOA,MAAA,CAAA,cAAA,CAAA,GAAA,IAAA,EAAA,IACA,MAAA,CAAA,cAAA,CAAA,QAAA,IAAA,EADA,IAEA,MAAA,CAAA,cAAA,CAAA,MAAA,IAAA,EATA;AAAA;AAAA;AAAA;;AAWA,gBAAA,MAAA,CAAA,OAAA,CAAA,KAAA,CAAA;AACA,kBAAA,KAAA,EAAA,MAAA,CAAA,EAAA,CAAA,qBAAA,CADA;AAEA,kBAAA,IAAA,EAAA,MAAA,CAAA,EAAA,CAAA,qBAAA,EAAA;AAAA,uBAAA,QAAA,CAAA,IAAA,CAAA,GAAA;AAAA,mBAAA,CAFA;AAGA,kBAAA,QAAA,EAAA,MAAA,CAAA;AAHA,iBAAA;;AAXA;;AAAA;AAkBA,gBAAA,MAAA,CAAA,kBAAA,CAAA,cAAA,CAAA,aAAA,CACA,MAAA,CAAA,cAAA,CAAA,GADA;;AAlBA;AAAA,uBAqBA,MAAA,CAAA,kBAAA,CAAA,cAAA,CAAA,IAAA,CACA,IADA,EAEA,IAFA,EAGA;AACA,kBAAA,QAAA,EAAA,MAAA,CAAA,cAAA,CAAA,QADA;AAEA,kBAAA,MAAA,EAAA,MAAA,CAAA,cAAA,CAAA;AAFA,iBAHA,CArBA;;AAAA;AAqBA,gBAAA,MArBA;;AA6BA,oBAAA,MAAA,EAAA;AACA,kBAAA,MAAA,CAAA,OAAA,CAAA,OAAA,CAAA;AACA,oBAAA,KAAA,EAAA,MAAA,CAAA,EAAA,CAAA,qBAAA,CADA;AAEA,oBAAA,IAAA,EACA,MAAA,CAAA,EAAA,CAAA,qBAAA,IACA,GADA,GAEA,MAAA,CAAA,EAAA,CAAA,qBAAA,CALA;AAMA,oBAAA,QAAA,EAAA,MAAA,CAAA;AANA,mBAAA;AAQA;;AAtCA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAuCA,KAxCA;AAyCA,IAAA,iBAzCA,+BAyCA;AACA,UAAA,OAAA,GAAA,KAAA;AAEA,UAAA,UAAA,GAAA,YAAA,CAAA,OAAA,CAAA,gBAAA,CAAA;;AACA,UAAA,UAAA,CAAA,QAAA,CAAA,kBAAA,KAAA,KAAA,OAAA,EAAA;AACA,QAAA,OAAA,GAAA,IAAA;AACA;;AACA,aAAA,OAAA;AACA,KAjDA;AAmDA,IAAA,qBAnDA,iCAmDA,KAnDA,EAmDA;AACA,UAAA,CAAA,KAAA,EAAA;AACA,aAAA,cAAA,CAAA,GAAA,GAAA,EAAA;AACA,aAAA,cAAA,CAAA,QAAA,GAAA,EAAA;AACA,aAAA,cAAA,CAAA,MAAA,GAAA,EAAA;AACA;AACA,KAzDA;AA0DA,IAAA,YA1DA,0BA0DA;AACA,WAAA,gBAAA,GACA,KAAA,UAAA,IAAA,QAAA,GAAA,MAAA,CAAA,WAAA,GAAA,GAAA,GAAA,GADA;AAEA,KA7DA;AA8DA,IAAA,kBA9DA,gCA8DA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA,gBAAA,YADA,GACA;AACA;AACA,kBAAA,QAAA,EAAA;AAFA,iBADA;AAAA;AAAA,uBAKA,MAAA,CAAA,QAAA,CAAA,WAAA,CAAA,IAAA,CACA,YADA,CALA;;AAAA;AAKA,gBAAA,iBALA;AAQA,gBAAA,MAAA,CAAA,eAAA,GAAA,iBAAA,CAAA,OAAA;;AACA,gBAAA,MAAA,CAAA,eAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,sBAAA,SAAA,GAAA,MAAA,CAAA,4BAAA,CAAA,IAAA,CACA,UAAA,IAAA;AAAA,2BAAA,IAAA,CAAA,GAAA,IAAA,IAAA,CAAA,IAAA;AAAA,mBADA,IAGA,MAAA,CAAA,4BAAA,CAAA,IAAA,CACA,UAAA,IAAA;AAAA,2BAAA,IAAA,CAAA,GAAA,IAAA,IAAA,CAAA,IAAA;AAAA,mBADA,EAEA,KALA,GAMA,IAAA,CAAA,IANA;AAQA,kBAAA,IAAA,CAAA,GAAA,GAAA,IAAA,CAAA,IAAA;AACA,kBAAA,IAAA,CAAA,KAAA,GAAA,SAAA;AACA,iBAXA;;AATA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAqBA,KAnFA;AAoFA,IAAA,SApFA,uBAoFA;AACA,MAAA,SAAA,CAAA,SAAA,CAAA,SAAA,CAAA,KAAA,KAAA;AACA,WAAA,OAAA,CAAA,OAAA,CAAA;AACA,QAAA,KAAA,EAAA,KAAA,EAAA,CAAA,qBAAA,CADA;AAEA,QAAA,IAAA,EAAA,KAAA,EAAA,CAAA,qBAAA,CAFA;AAGA,QAAA,QAAA,EAAA,MAAA,CAAA;AAHA,OAAA;AAKA,KA3FA;AA4FA,IAAA,aA5FA,2BA4FA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA,gBAAA,QADA,GACA;AACA,kBAAA,OAAA,EAAA,MAAA,CAAA,YADA;AAEA,kBAAA,QAAA,EAAA,MAAA,CAAA;AAFA,iBADA;AAAA;AAAA,uBAMA,MAAA,CAAA,QAAA,CAAA,kBAAA,CAAA,IAAA,CAAA,QAAA,CANA;;AAAA;AAMA,gBAAA,KANA;;AAAA,qBAOA,KAPA;AAAA;AAAA;AAAA;;AAAA;AAAA,uBAQA,MAAA,CAAA,QAAA,CAAA,aAAA,CAAA,IAAA,CAAA,QAAA,CARA;;AAAA;AAQA,gBAAA,GARA;AASA,gBAAA,MAAA,CAAA,KAAA,GAAA,QAAA,CAAA,MAAA,GAAA,2BAAA,GAAA,GAAA;;AAEA,gBAAA,MAAA,CAAA,OAAA,CAAA,OAAA,CAAA;AACA,kBAAA,KAAA,EAAA,MAAA,CAAA,EAAA,CAAA,qBAAA,CADA;AAEA,kBAAA,IAAA,EAAA,MAAA,CAAA,EAAA,CAAA,qBAAA,CAFA;AAGA,kBAAA,QAAA,EAAA,MAAA,CAAA;AAHA,iBAAA;;AAXA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAiBA,KA7GA;AA8GA,IAAA,oBA9GA,gCA8GA,GA9GA,EA8GA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA,gBAAA,YADA,GACA,EADA;;AAEA,oBAAA,GAAA,CAAA,eAAA,IAAA,EAAA,EAAA;AACA,kBAAA,YAAA,IAAA,MAAA,CAAA,EAAA,CAAA,qBAAA,EAAA;AACA,uBAAA,MAAA,CAAA,EAAA,CAAA,qBAAA;AADA,mBAAA,CAAA;AAGA;;AACA,oBAAA,GAAA,CAAA,OAAA,IAAA,EAAA,EAAA;AACA,kBAAA,YAAA,IAAA,MAAA,CAAA,EAAA,CAAA,qBAAA,EAAA;AACA,uBAAA,MAAA,CAAA,EAAA,CAAA,qBAAA;AADA,mBAAA,CAAA;AAGA;;AACA,oBAAA,GAAA,CAAA,QAAA,IAAA,EAAA,EAAA;AACA,kBAAA,YAAA,IAAA,MAAA,CAAA,EAAA,CAAA,qBAAA,EAAA;AACA,uBAAA,MAAA,CAAA,EAAA,CAAA,qBAAA;AADA,mBAAA,CAAA;AAGA;;AAhBA,sBAiBA,YAAA,IAAA,EAjBA;AAAA;AAAA;AAAA;;AAkBA,gBAAA,MAAA,CAAA,OAAA,CAAA,OAAA,CAAA;AACA,kBAAA,KAAA,EAAA,MAAA,CAAA,EAAA,CAAA,qBAAA,CADA;AAEA,kBAAA,IAAA,EAAA,YAFA;AAGA,kBAAA,QAAA,EAAA,MAAA,CAAA;AAHA,iBAAA;;AAlBA,kDAuBA,KAvBA;;AAAA;AA0BA,gBAAA,MA1BA,GA0BA;AACA,kBAAA,QAAA,EAAA,GAAA,CAAA,eADA;AAEA,kBAAA,QAAA,EAAA,GAAA,CAAA,OAFA;AAGA,kBAAA,QAAA,EAAA,MAAA,CAAA,MAAA,CAAA,GAAA,CAAA,QAAA;AAHA,iBA1BA;AAAA;AAAA,uBAgCA,MAAA,CAAA,QAAA,CAAA,oBAAA,CAAA,IAAA,CAAA,MAAA,CAhCA;;AAAA;AAgCA,gBAAA,YAhCA;;AAiCA,oBAAA,YAAA,IAAA,EAAA,EAAA;AACA,kBAAA,MAAA,CAAA,OAAA,CAAA,OAAA,CAAA;AACA,oBAAA,KAAA,EAAA,MAAA,CAAA,EAAA,CAAA,qBAAA,CADA;AACA;AACA,oBAAA,IAAA,EACA,MAAA,CAAA,EAAA,CAAA,qBAAA,IACA,GADA,GAEA,MAAA,CAAA,EAAA,CAAA,qBAAA,CALA;AAMA,oBAAA,QAAA,EAAA,MAAA,CAAA;AANA,mBAAA;AAQA;;AA1CA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AA2CA,KAzJA;AA0JA,IAAA,kBA1JA,8BA0JA,IA1JA,EA0JA,GA1JA,EA0JA,KA1JA,EA0JA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,+BACA,IADA;AAAA,kDAEA,SAFA,wBAKA,cALA,wBAQA,cARA,wBAWA,iBAXA,wBAcA,SAdA,yBAiBA,UAjBA,yBAoBA,UApBA,yBAuBA,iBAvBA;AAAA;;AAAA;AAGA,gBAAA,MAAA,CAAA,gBAAA,CAAA,KAAA,EAAA,OAAA,GAAA,GAAA,CAAA,OAAA;AAHA;;AAAA;AAMA,gBAAA,MAAA,CAAA,gBAAA,CAAA,KAAA,EAAA,YAAA,GAAA,GAAA,CAAA,YAAA;AANA;;AAAA;AASA,gBAAA,MAAA,CAAA,gBAAA,CAAA,KAAA,EAAA,YAAA,GAAA,GAAA,CAAA,YAAA;AATA;;AAAA;AAYA,gBAAA,MAAA,CAAA,gBAAA,CAAA,KAAA,EAAA,eAAA,GAAA,GAAA,CAAA,eAAA;AAZA;;AAAA;AAeA,gBAAA,MAAA,CAAA,gBAAA,CAAA,KAAA,EAAA,OAAA,GAAA,GAAA,CAAA,OAAA;AAfA;;AAAA;AAkBA,gBAAA,MAAA,CAAA,gBAAA,CAAA,KAAA,EAAA,QAAA,GAAA,GAAA,CAAA,QAAA;AAlBA;;AAAA;AAqBA,gBAAA,MAAA,CAAA,gBAAA,CAAA,KAAA,EAAA,QAAA,GAAA,GAAA,CAAA,QAAA;AArBA;;AAAA;AAwBA,gBAAA,MAAA,CAAA,gBAAA,CAAA,KAAA,EAAA,eAAA,GAAA,GAAA,CAAA,eAAA;AAxBA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AA4BA,KAtLA;AAwLA,IAAA,yBAxLA,qCAwLA,IAxLA,EAwLA,GAxLA,EAwLA,KAxLA,EAwLA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,+BACA,IADA;AAAA,kDAEA,UAFA,wBAKA,UALA,wBAQA,aARA;AAAA;;AAAA;AAGA,gBAAA,MAAA,CAAA,uBAAA,CAAA,KAAA,EAAA,QAAA,GAAA,GAAA,CAAA,QAAA;AAHA;;AAAA;AAMA,gBAAA,MAAA,CAAA,uBAAA,CAAA,KAAA,EAAA,QAAA,GAAA,GAAA,CAAA,QAAA;AANA;;AAAA;AASA,gBAAA,MAAA,CAAA,uBAAA,CAAA,KAAA,EAAA,WAAA,GAAA,GAAA,CAAA,WAAA;AATA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAaA,KArMA;;AAsMA;;;;;;AAMA,IAAA,mBA5MA,+BA4MA,IA5MA,EA4MA;AACA,WAAA,gBAAA,GAAA,IAAA;AACA,KA9MA;AA+MA,IAAA,6BA/MA,yCA+MA,IA/MA,EA+MA;AAAA;;AACA,UAAA,KAAA,gBAAA,EAAA;AACA,YAAA,IAAA,CAAA,MAAA,CAAA,UAAA,IAAA;AAAA,iBAAA,IAAA,IAAA,KAAA;AAAA,SAAA,EAAA,MAAA,GAAA,CAAA,EAAA;AACA,eAAA,UAAA,CAAA,gBAAA,GAAA,CAAA,KAAA,CAAA;AACA,SAFA,MAEA;AACA,eAAA,QAAA,CAAA,wBAAA,CACA,IADA,CACA,IADA,EAEA,IAFA,CAEA,UAAA,WAAA,EAAA;AACA,gBAAA,iBAAA,GAAA,WAAA,CAAA,UAAA,CAAA,KAAA,CAAA,GAAA,CAAA;;AACA,gBAAA,IAAA,CAAA,MAAA,IAAA,CAAA,EAAA;AACA,cAAA,MAAA,CAAA,UAAA,CAAA,gBAAA,GAAA,iBAAA;AACA,aAFA,MAEA;AACA,cAAA,iBAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,oBAAA,CAAA,MAAA,CAAA,UAAA,CAAA,gBAAA,CAAA,QAAA,CAAA,IAAA,CAAA,EAAA;AACA,sBAAA,UAAA,GAAA,EAAA;;AACA,sBACA,MAAA,CAAA,sBAAA,CAAA,IAAA,CAAA,UAAA,IAAA;AAAA,2BACA,IAAA,CAAA,QAAA,CAAA,IAAA,CAAA,GAAA,CADA;AAAA,mBAAA,CADA,EAIA;AACA,oBAAA,UAAA,GAAA,MAAA,CAAA,sBAAA,CAAA,IAAA,CAAA,UAAA,IAAA;AAAA,6BACA,IAAA,CAAA,QAAA,CAAA,IAAA,CAAA,GAAA,CADA;AAAA,qBAAA,EAEA,KAFA;;AAGA,oBAAA,MAAA,CAAA,OAAA,CAAA,KAAA,CAAA;AACA,sBAAA,KAAA,EAAA,MAAA,CAAA,EAAA,CAAA,qBAAA,CADA;AAEA,sBAAA,IAAA,EAAA,MAAA,CAAA,EAAA,CAAA,qBAAA,EAAA;AAAA,2BAAA;AAAA,uBAAA,CAFA;AAGA,sBAAA,QAAA,EAAA,MAAA,CAAA;AAHA,qBAAA;;AAKA,oBAAA,MAAA,CAAA,UAAA,CAAA,gBAAA,CAAA,IAAA,CAAA,IAAA;AACA;AACA;AACA,eAnBA;AAoBA;AACA,WA5BA;AA6BA;AACA;AACA,KAnPA;AAoPA,IAAA,wBApPA,sCAoPA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA,gBAAA,MADA,GACA;AACA,kBAAA,MAAA,EAAA;AADA,iBADA;AAIA,gBAAA,GAJA,GAIA,EAJA;AAAA;AAAA,uBAKA,OAAA,CAAA,QAAA,CAAA,2BAAA,CAAA,IAAA,CAAA,MAAA,CALA;;AAAA;AAKA,gBAAA,GALA;AAMA,gBAAA,GAAA,CAAA,OAAA,CAAA,OAAA,CAAA,UAAA,CAAA,EAAA;AACA,kBAAA,GAAA,CAAA,IAAA,CAAA;AACA,oBAAA,GAAA,EAAA,CAAA,CAAA,IADA;AAEA,oBAAA,KAAA,EAAA,OAAA,CAAA,EAAA,CAAA,kBAAA,CAAA,CAAA,KAAA,CAAA,MAAA;AAFA,mBAAA;AAIA,iBALA;AAMA,gBAAA,OAAA,CAAA,4BAAA,GAAA,IAAA,CAAA,KAAA,CAAA,IAAA,CAAA,SAAA,CAAA,GAAA,CAAA,CAAA;;AAZA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAaA,KAjQA;AAkQA,IAAA,iBAlQA,6BAkQA,GAlQA,EAkQA;AACA,WAAA,UAAA,CAAA,cAAA,GAAA,GAAA;AACA,KApQA;AAqQA,IAAA,iBArQA,6BAqQA,GArQA,EAqQA;AACA,UAAA,CAAA,GAAA,EAAA;AACA,aAAA,UAAA,CAAA,cAAA,GAAA,EAAA;AACA;AACA,KAzQA;AA0QA,IAAA,SA1QA,qBA0QA,CA1QA,EA0QA;AACA,WAAA,UAAA,GAAA,CAAA;AACA,WAAA,YAAA;AACA,KA7QA;AA8QA,IAAA,cA9QA,0BA8QA,WA9QA,EA8QA;AACA;AACA;AACA;AACA,UAAA,SAAA,GAAA,EAAA;;AACA,UACA,KAAA,4BAAA,IACA,KAAA,4BAAA,CAAA,MAAA,GAAA,CAFA,EAGA;AACA,QAAA,SAAA,GAAA,KAAA,4BAAA,CAAA,IAAA,CACA,UAAA,IAAA;AAAA,iBAAA,IAAA,CAAA,GAAA,KAAA,WAAA;AAAA,SADA,EAEA,KAFA;AAGA;;AACA,aAAA,SAAA;AACA,KA5RA;AA6RA,IAAA,+CA7RA,6DA6RA;AAAA;;AACA,UAAA,wBAAA,GAAA,EAAA;AACA,UAAA,yBAAA,GAAA,EAAA;AACA,UAAA,wBAAA,GAAA,EAAA;AACA,UAAA,4BAAA,GAAA,EAAA;AACA,WAAA,iBAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,YAAA,IAAA,CAAA,gBAAA,EAAA;AACA,cACA,IAAA,CAAA,gBAAA,CAAA,IAAA,CAAA,UAAA,CAAA;AAAA,mBAAA,CAAA,CAAA,WAAA,KAAA,YAAA;AAAA,WAAA,KACA,IAAA,CAAA,WAFA,EAGA;AACA,YAAA,wBAAA,CAAA,IAAA,CAAA;AACA,cAAA,GAAA,EAAA,IAAA,CAAA,IADA;AAEA,cAAA,KAAA,EAAA,OAAA,CAAA,EAAA,CAAA,kBAAA,IAAA,CAAA,KAAA,CAAA,MAAA;AAFA,aAAA;AAIA;;AACA,cACA,IAAA,CAAA,gBAAA,CAAA,IAAA,CAAA,UAAA,CAAA;AAAA,mBAAA,CAAA,CAAA,WAAA,KAAA,YAAA;AAAA,WAAA,KACA,CAAA,IAAA,CAAA,WAFA,EAGA;AACA,YAAA,yBAAA,CAAA,IAAA,CAAA;AACA,cAAA,GAAA,EAAA,IAAA,CAAA,IADA;AAEA,cAAA,KAAA,EAAA,OAAA,CAAA,EAAA,CAAA,kBAAA,IAAA,CAAA,KAAA,CAAA,MAAA;AAFA,aAAA;AAIA;;AACA,cAAA,IAAA,CAAA,gBAAA,CAAA,IAAA,CAAA,UAAA,CAAA;AAAA,mBAAA,CAAA,CAAA,WAAA,KAAA,YAAA;AAAA,WAAA,CAAA,EAAA;AACA,YAAA,wBAAA,CAAA,IAAA,CAAA;AACA,cAAA,GAAA,EAAA,IAAA,CAAA,IADA;AAEA,cAAA,KAAA,EAAA,OAAA,CAAA,EAAA,CAAA,kBAAA,IAAA,CAAA,KAAA,CAAA,MAAA;AAFA,aAAA;AAIA;;AACA,cACA,IAAA,CAAA,gBAAA,CAAA,IAAA,CAAA,UAAA,CAAA;AAAA,mBAAA,CAAA,CAAA,WAAA,KAAA,gBAAA;AAAA,WAAA,CADA,EAEA;AACA,YAAA,4BAAA,CAAA,IAAA,CAAA;AACA,cAAA,GAAA,EAAA,IAAA,CAAA,IADA;AAEA,cAAA,KAAA,EAAA,OAAA,CAAA,EAAA,CAAA,kBAAA,IAAA,CAAA,KAAA,CAAA,MAAA;AAFA,aAAA;AAIA;AACA;AACA,OAnCA;AAoCA,WAAA,oBAAA,GAAA,KAAA,CAAA,IAAA,CAAA,IAAA,GAAA,CAAA,wBAAA,CAAA,CAAA;AACA,WAAA,qBAAA,GAAA,KAAA,CAAA,IAAA,CACA,IAAA,GAAA,CAAA,yBAAA,CADA,CAAA;AAGA,WAAA,oBAAA,GAAA,KAAA,CAAA,IAAA,CAAA,IAAA,GAAA,CAAA,wBAAA,CAAA,CAAA;AACA,WAAA,wBAAA,GAAA,KAAA,CAAA,IAAA,CACA,IAAA,GAAA,CAAA,4BAAA,CADA,CAAA;AAGA,KA9UA;AA+UA,IAAA,uBA/UA,qCA+UA;AAAA;;AACA,UAAA,uBAAA,GAAA,EAAA;AACA,WAAA,iBAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,YAAA,IAAA,CAAA,gBAAA,EAAA;AACA,cAAA,MAAA,GAAA,IAAA,CAAA,gBAAA,CAAA,GAAA,CAAA,UAAA,CAAA;AAAA,mBAAA,CAAA,CAAA,WAAA;AAAA,WAAA,CAAA;AACA,UAAA,uBAAA,gCAAA,uBAAA,sBAAA,MAAA,EAAA;AACA;AACA,OALA;AAOA,MAAA,uBAAA,GAAA,KAAA,CAAA,IAAA,CAAA,IAAA,GAAA,CAAA,uBAAA,CAAA,CAAA;AACA,MAAA,uBAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,QAAA,OAAA,CAAA,oBAAA,CAAA,IAAA,CAAA;AACA,UAAA,GAAA,EAAA,IADA;AAEA,UAAA,KAAA,EAAA,OAAA,CAAA,cAAA,CAAA,IAAA;AAFA,SAAA;AAIA,OALA;AAMA,KA/VA;AAgWA,IAAA,iBAhWA,+BAgWA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA,gBAAA,iBADA,GACA,IAAA,eAAA,EADA;AAEA,gBAAA,iBAAA,CAAA,MAAA,CAAA,QAAA,EAAA,IAAA;AAFA;AAAA,uBAGA,OAAA,CAAA,QAAA,CAAA,kBAAA,CAAA,IAAA,CACA,iBADA,CAHA;;AAAA;AAGA,gBAAA,OAAA,CAAA,iBAHA;;AAMA,gBAAA,OAAA,CAAA,mBAAA;;AACA,gBAAA,OAAA,CAAA,uBAAA;;AACA,gBAAA,OAAA,CAAA,+CAAA;;AACA,gBAAA,OAAA,CAAA,QAAA;;AACA,gBAAA,OAAA,CAAA,SAAA;;AACA,gBAAA,OAAA,CAAA,UAAA;;AACA,gBAAA,OAAA,CAAA,cAAA;;AAZA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAaA,KA7WA;AA8WA,IAAA,mBA9WA,iCA8WA;AAAA;;AACA,WAAA,sBAAA,GAAA,KAAA,iBAAA,CAAA,GAAA,CAAA,UAAA,IAAA;AAAA,eAAA;AACA,UAAA,GAAA,EAAA,IAAA,CAAA,IADA;AAEA,UAAA,KAAA,EAAA,OAAA,CAAA,EAAA,CAAA,kBAAA,IAAA,CAAA,KAAA,CAAA,MAAA;AAFA,SAAA;AAAA,OAAA,CAAA;AAIA,WAAA,sBAAA,GACA,CAAA;AAAA,QAAA,GAAA,EAAA,KAAA;AAAA,QAAA,KAAA,EAAA,KAAA,EAAA,CAAA,qBAAA;AAAA,OAAA,CADA,2BAEA,KAAA,sBAFA;AAIA,KAvXA;AAwXA,IAAA,KAxXA,mBAwXA;AACA,UAAA,KAAA,GAAA,KAAA,KAAA,CAAA,KAAA;AACA,WAAA,MAAA,GAAA,KAAA;AACA,MAAA,KAAA,CAAA,KAAA;AACA,MAAA,KAAA,CAAA,WAAA,GAAA,CAAA;AACA,MAAA,QAAA,CAAA,aAAA,CAAA,eAAA,EAAA,SAAA,CAAA,GAAA,CAAA,QAAA;AACA,KA9XA;AA+XA,IAAA,IA/XA,gBA+XA,OA/XA,EA+XA;AACA,UAAA,KAAA,GAAA,KAAA,KAAA,CAAA,KAAA;;AACA,UAAA,OAAA,IAAA,IAAA,IAAA,OAAA,IAAA,SAAA,EAAA;AACA,aAAA,MAAA,GAAA,KAAA;AACA,QAAA,KAAA,CAAA,KAAA;AACA,QAAA,KAAA,CAAA,WAAA,GAAA,CAAA;AACA,QAAA,QAAA,CAAA,aAAA,CAAA,eAAA,EAAA,SAAA,CAAA,GAAA,CAAA,QAAA;AACA;AACA;;AAEA,UACA,KAAA,CAAA,MAAA,IACA,QAAA,CAAA,aAAA,CAAA,eAAA,EAAA,SAAA,CAAA,QAAA,CAAA,QAAA,CAFA,EAGA;AACA,aAAA,MAAA,GAAA,IAAA;AACA,QAAA,QAAA,CAAA,aAAA,CAAA,eAAA,EAAA,SAAA,CAAA,MAAA,CAAA,QAAA;AACA,QAAA,KAAA,CAAA,IAAA;AACA,OAPA,MAOA;AACA,aAAA,MAAA,GAAA,KAAA;AACA,QAAA,KAAA,CAAA,KAAA;AACA,QAAA,KAAA,CAAA,WAAA,GAAA,CAAA;AACA,QAAA,QAAA,CAAA,aAAA,CAAA,eAAA,EAAA,SAAA,CAAA,GAAA,CAAA,QAAA;AACA;AACA,KAtZA;AAuZA,IAAA,SAvZA,uBAuZA;AACA,UAAA,OAAA,GAAA,QAAA,CAAA,aAAA,CAAA,UAAA,CAAA;AACA,MAAA,OAAA,CAAA,GAAA,GAAA,EAAA;AACA,WAAA,aAAA,GAAA,KAAA;AACA,WAAA,UAAA,GAAA,IAAA;AACA,KA5ZA;AA8ZA,IAAA,iBA9ZA,6BA8ZA,OA9ZA,EA8ZA;AACA,UAAA,UAAA,GAAA,IAAA;;AACA,UACA,OAAA,CAAA,IAAA,CAAA,WAAA,GAAA,QAAA,CAAA,KAAA,KACA,OAAA,CAAA,IAAA,CAAA,WAAA,GAAA,QAAA,CAAA,KAAA,CADA,IAEA,OAAA,CAAA,IAAA,CAAA,WAAA,GAAA,QAAA,CAAA,MAAA,CAHA,EAIA;AACA,YAAA,MAAA,GAAA,IAAA,UAAA,EAAA;AACA,YAAA,OAAA,GAAA,QAAA,CAAA,aAAA,CAAA,UAAA,CAAA;AAEA,QAAA,MAAA,CAAA,gBAAA,CACA,MADA,EAEA,YAAA;AACA,UAAA,OAAA,CAAA,GAAA,GAAA,MAAA,CAAA,MAAA;AACA,SAJA,EAKA,KALA;;AAQA,YAAA,OAAA,EAAA;AACA,UAAA,MAAA,CAAA,aAAA,CAAA,OAAA;AACA,SAdA,CAeA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,aAAA,OAAA,GAAA,OAAA;AACA,aAAA,UAAA,GAAA,UAAA;AACA,aAAA,aAAA,GAAA,IAAA;AACA,OA9BA,MA8BA;AACA,aAAA,OAAA,CAAA,KAAA,CAAA;AACA,UAAA,KAAA,EAAA,KAAA,EAAA,CAAA,qBAAA,CADA;AAEA,UAAA,IAAA,EAAA,KAAA,EAAA,CAAA,qBAAA,EAAA;AACA,eAAA,KAAA,EAAA,CAAA,qBAAA;AADA,WAAA,CAFA;AAKA,UAAA,QAAA,EAAA,MAAA,CAAA;AALA,SAAA;AAOA;AACA,KAvcA;AAwcA,IAAA,YAxcA,wBAwcA,IAxcA,EAwcA;AAAA;;AACA,WAAA,MAAA,GAAA,KAAA;AACA,UAAA,KAAA,GAAA,KAAA,KAAA,CAAA,KAAA;AACA,MAAA,KAAA,CAAA,KAAA;AACA,MAAA,QAAA,CAAA,aAAA,CAAA,eAAA,EAAA,SAAA,CAAA,GAAA,CAAA,QAAA;AAEA,UAAA,YAAA,GAAA,IAAA,UAAA,EAAA;AACA,MAAA,YAAA,CAAA,aAAA,CAAA,IAAA;AACA,MAAA,YAAA,CAAA,gBAAA,CAAA,MAAA,EAAA,YAAA;AACA,YAAA,CAAA,YAAA,CAAA,MAAA,CAAA,QAAA,CAAA,aAAA,CAAA,EAAA;AACA,UAAA,OAAA,CAAA,IAAA,GAAA,IAAA;AACA,UAAA,OAAA,CAAA,OAAA,GAAA,OAAA,CAAA,UAAA;;AACA,UAAA,OAAA,CAAA,OAAA,CAAA,KAAA,CAAA;AACA,YAAA,KAAA,EAAA,OAAA,CAAA,EAAA,CAAA,qBAAA,CADA;AAEA,YAAA,IAAA,EAAA,OAAA,CAAA,EAAA,CAAA,qBAAA,EAAA;AACA,iBAAA,OAAA,CAAA,EAAA,CAAA,qBAAA;AADA,aAAA,CAFA;AAKA,YAAA,QAAA,EAAA,MAAA,CAAA;AALA,WAAA;AAOA,SAVA,MAUA;AACA,UAAA,OAAA,CAAA,IAAA,GAAA,IAAA;AACA,UAAA,OAAA,CAAA,OAAA,GAAA,YAAA,CAAA,MAAA;AAEA,UAAA,UAAA,CAAA,YAAA;AACA,gBAAA,KAAA,CAAA,QAAA,GAAA,EAAA,EAAA;AACA,cAAA,OAAA,CAAA,IAAA,GAAA,IAAA;AACA,cAAA,OAAA,CAAA,OAAA,GAAA,OAAA,CAAA,UAAA;;AACA,cAAA,OAAA,CAAA,OAAA,CAAA,KAAA,CAAA;AACA,gBAAA,KAAA,EAAA,OAAA,CAAA,EAAA,CAAA,qBAAA,CADA;AAEA,gBAAA,IAAA,EAAA,OAAA,CAAA,EAAA,CAAA,qBAAA,EAAA;AAAA,qBAAA;AAAA,iBAAA,CAFA;AAGA,gBAAA,QAAA,EAAA,MAAA,CAAA;AAHA,eAAA;AAKA,aARA,MAQA;AACA,cAAA,OAAA,CAAA,IAAA,GAAA,IAAA;AACA,cAAA,OAAA,CAAA,OAAA,GAAA,YAAA,CAAA,MAAA;AACA;AACA,WAbA,EAaA,GAbA,CAAA;AAcA;AACA,OA9BA;AA+BA,KA/eA;AAgfA,IAAA,oBAhfA,gCAgfA,IAhfA,EAgfA;AAAA;;AACA;AACA,UAAA,GAAA,GAAA,EAAA;AACA,MAAA,IAAA,CAAA,OAAA,CAAA,UAAA,OAAA,EAAA;AACA,YAAA,CAAA,GAAA,OAAA,CAAA,UAAA,CAAA,IAAA,CAAA,UAAA,CAAA;AAAA,iBAAA,CAAA,CAAA,KAAA,IAAA,OAAA;AAAA,SAAA,EAAA,KAAA;;AACA,YAAA,CAAA,EAAA;AACA,UAAA,GAAA,CAAA,IAAA,CAAA;AACA,YAAA,KAAA,EAAA,CADA;AAEA,YAAA,KAAA,EAAA;AAFA,WAAA;AAIA;AACA,OARA;AASA,WAAA,cAAA,GAAA,GAAA;AACA,KA7fA;AA8fA,IAAA,QA9fA,sBA8fA;AAAA;;AACA,UAAA,cAAA,GAAA;AACA,QAAA,WAAA,EAAA,IADA;AAEA,QAAA,MAAA,EAAA;AAFA,OAAA;AAIA,WAAA,QAAA,CAAA,sBAAA,CAAA,IAAA,CAAA,cAAA,EAAA,IAAA,CAAA,UAAA,MAAA,EAAA;AACA,YACA,MAAA,CAAA,KAAA,GAAA,CAAA,IACA,MAAA,CAAA,OAAA,CAAA,CAAA,EAAA,KADA,IAEA,MAAA,CAAA,OAAA,CAAA,CAAA,EAAA,KAAA,CAAA,MAAA,GAAA,CAHA,EAIA;AACA,UAAA,OAAA,CAAA,UAAA,GAAA,MAAA,CAAA,OAAA,CAAA,CAAA,EAAA,KAAA,CAAA,CAAA,EAAA,GAAA;AACA,UAAA,OAAA,CAAA,aAAA,GAAA,MAAA,CAAA,OAAA,CAAA,CAAA,EAAA,KAAA,CAAA,CAAA,EAAA,GAAA;AACA,UAAA,OAAA,CAAA,aAAA,GAAA,IAAA;AACA;AACA,OAVA;AAWA,KA9gBA;AA+gBA,IAAA,SA/gBA,uBA+gBA;AAAA;;AACA,UAAA,cAAA,GAAA;AACA,QAAA,WAAA,EAAA,IADA;AAEA,QAAA,MAAA,EAAA;AAFA,OAAA;AAIA,WAAA,QAAA,CAAA,sBAAA,CAAA,IAAA,CAAA,cAAA,EAAA,IAAA,CAAA,UAAA,MAAA,EAAA;AACA,YACA,MAAA,CAAA,KAAA,GAAA,CAAA,IACA,MAAA,CAAA,OAAA,CAAA,CAAA,EAAA,KADA,IAEA,MAAA,CAAA,OAAA,CAAA,CAAA,EAAA,KAAA,CAAA,MAAA,GAAA,CAHA,EAIA;AACA,UAAA,OAAA,CAAA,OAAA,GAAA,MAAA,CAAA,OAAA,CAAA,CAAA,EAAA,KAAA,CAAA,CAAA,EAAA,GAAA;AACA,UAAA,OAAA,CAAA,UAAA,GAAA,MAAA,CAAA,OAAA,CAAA,CAAA,EAAA,KAAA,CAAA,CAAA,EAAA,GAAA;AACA;AACA,OATA;AAUA,KA9hBA;AA+hBA,IAAA,UA/hBA,wBA+hBA;AAAA;;AACA;AAEA,UAAA,MAAA,GAAA;AACA,QAAA,WAAA,EAAA,IADA;AAEA,QAAA,MAAA,EAAA;AAFA,OAAA;AAKA,UAAA,QAAA,GAAA,mCAAA;AACA,UAAA,WAAA,GAAA;AACA,QAAA,MAAA,EAAA,QADA;AAEA,QAAA,WAAA,EAAA,IAFA;AAGA;AACA;AACA,QAAA,IAAA,EAAA,iBALA,CAMA;;AANA,OAAA;AASA,WAAA,QAAA,CAAA,SAAA,CAAA,IAAA,CAAA,WAAA,EAAA,IAAA,CAAA,UAAA,SAAA,EAAA;AACA,YAAA,SAAA,CAAA,OAAA,CAAA,MAAA,GAAA,CAAA,EAAA;AACA,UAAA,OAAA,CAAA,SAAA,GAAA,SAAA,CAAA,OAAA,CAAA,GAAA,CAAA,UAAA,IAAA;AAAA,mBAAA;AACA,cAAA,KAAA,EAAA,IAAA,CAAA,IADA;AAEA,cAAA,KAAA,EAAA,IAAA,CAAA;AAFA,aAAA;AAAA,WAAA,CAAA;AAIA;;AACA,QAAA,OAAA,CAAA,QAAA,CAAA,gBAAA,CAAA,IAAA,CAAA,MAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,cAAA,GAAA,CAAA,KAAA,GAAA,CAAA,IAAA,GAAA,CAAA,OAAA,CAAA,CAAA,EAAA,UAAA,CAAA,MAAA,GAAA,CAAA,EAAA;AACA,YAAA,GAAA,CAAA,OAAA,CAAA,CAAA,EAAA,UAAA,CAAA,OAAA,CAAA,UAAA,GAAA,EAAA;AACA,sBAAA,GAAA,CAAA,GAAA;AACA,qBAAA,SAAA;AACA,kBAAA,OAAA,CAAA,UAAA,CAAA,OAAA,GACA,GAAA,CAAA,KAAA,IAAA,EAAA,GAAA,GAAA,CAAA,KAAA,CAAA,KAAA,CAAA,GAAA,CAAA,GAAA,EADA;AAEA;;AACA,qBAAA,YAAA;AACA,kBAAA,OAAA,CAAA,UAAA,CAAA,UAAA,GAAA,GAAA,CAAA,KAAA;AACA;;AACA,qBAAA,SAAA;AACA,kBAAA,OAAA,CAAA,UAAA,CAAA,OAAA,GAAA,GAAA,CAAA,KAAA,CAAA,KAAA,CAAA,GAAA,CAAA;AACA,sBAAA,GAAA,GAAA,EAAA;;AACA,kBAAA,OAAA,CAAA,UAAA,CAAA,OAAA,CAAA,OAAA,CAAA,UAAA,OAAA,EAAA;AACA,wBAAA,CAAA,GAAA,OAAA,CAAA,UAAA,CAAA,IAAA,CAAA,UAAA,CAAA;AAAA,6BAAA,CAAA,CAAA,KAAA,IAAA,OAAA;AAAA,qBAAA,EAAA,KAAA;;AACA,wBAAA,CAAA,EAAA;AACA,sBAAA,GAAA,CAAA,IAAA,CAAA;AACA,wBAAA,KAAA,EAAA,CADA;AAEA,wBAAA,KAAA,EAAA;AAFA,uBAAA;AAIA;AACA,mBARA;;AASA,kBAAA,OAAA,CAAA,cAAA,GAAA,GAAA;AACA;AACA;AACA;;AACA,qBAAA,QAAA;AACA,kBAAA,OAAA,CAAA,UAAA,CAAA,MAAA,GAAA,GAAA,CAAA,KAAA,CAAA,KAAA,CAAA,GAAA,CAAA;AACA;;AACA,qBAAA,UAAA;AACA,kBAAA,OAAA,CAAA,UAAA,CAAA,QAAA,GAAA,GAAA,CAAA,KAAA;AACA;;AACA,qBAAA,YAAA;AACA,kBAAA,OAAA,CAAA,UAAA,CAAA,UAAA,GAAA,GAAA,CAAA,KAAA;AACA,kBAAA,OAAA,CAAA,MAAA,GAAA,GAAA,CAAA,KAAA;AACA;;AACA,qBAAA,eAAA;AACA,kBAAA,OAAA,CAAA,UAAA,CAAA,aAAA,GAAA,GAAA,CAAA,KAAA;AACA;;AACA,qBAAA,UAAA;AACA,kBAAA,OAAA,CAAA,UAAA,CAAA,QAAA,GAAA,GAAA,CAAA,KAAA,GAAA,CAAA;AACA;;AACA,qBAAA,gBAAA;AACA,kBAAA,OAAA,CAAA,UAAA,CAAA,cAAA,GACA,GAAA,CAAA,KAAA,IAAA,MAAA,GAAA,IAAA,GAAA,KADA;AAEA;;AACA,qBAAA,eAAA;AACA,kBAAA,OAAA,CAAA,UAAA,CAAA,aAAA,GAAA,GAAA,CAAA,KAAA,GAAA,CAAA;AACA;;AACA,qBAAA,aAAA;AACA,kBAAA,OAAA,CAAA,UAAA,CAAA,WAAA,GAAA,GAAA,CAAA,KAAA,GAAA,CAAA;AACA;;AACA,qBAAA,YAAA;AACA,kBAAA,OAAA,CAAA,UAAA,CAAA,UAAA,GAAA,GAAA,CAAA,KAAA,GAAA,CAAA;AACA;;AACA,qBAAA,aAAA;AACA,kBAAA,OAAA,CAAA,UAAA,CAAA,WAAA,GAAA,GAAA,CAAA,KAAA,GAAA,CAAA;AACA;;AACA,qBAAA,YAAA;AACA,kBAAA,OAAA,CAAA,UAAA,CAAA,UAAA,GAAA,GAAA,CAAA,KAAA,GAAA,CAAA;AACA;;AACA,qBAAA,mBAAA;AACA,kBAAA,OAAA,CAAA,UAAA,CAAA,iBAAA,GAAA,GAAA,CAAA,KAAA,CAAA,KAAA,CAAA,GAAA,CAAA;AACA;;AACA,qBAAA,oBAAA;AACA,kBAAA,OAAA,CAAA,UAAA,CAAA,kBAAA,GAAA,GAAA,CAAA,KAAA,CAAA,KAAA,CAAA,GAAA,CAAA;AACA;;AACA,qBAAA,mBAAA;AACA,kBAAA,OAAA,CAAA,UAAA,CAAA,iBAAA,GAAA,GAAA,CAAA,KAAA,CAAA,KAAA,CAAA,GAAA,CAAA;AACA;;AACA,qBAAA,iBAAA;AACA,kBAAA,OAAA,CAAA,UAAA,CAAA,eAAA,GAAA,GAAA,CAAA,KAAA,CAAA,KAAA,CAAA,GAAA,CAAA;AACA;;AACA,qBAAA,WAAA;AACA,kBAAA,OAAA,CAAA,UAAA,CAAA,SAAA,GAAA,GAAA,CAAA,KAAA,CAAA,KAAA,CAAA,GAAA,CAAA;AACA;;AACA,qBAAA,kBAAA;AACA,kBAAA,OAAA,CAAA,UAAA,CAAA,gBAAA,GAAA,GAAA,CAAA,KAAA,CAAA,KAAA,CAAA,GAAA,CAAA;AACA;;AACA,qBAAA,aAAA;AACA,kBAAA,OAAA,CAAA,UAAA,CAAA,WAAA,GACA,GAAA,CAAA,KAAA,IAAA,MAAA,GAAA,IAAA,GAAA,KADA;AAEA;;AACA,qBAAA,gBAAA;AACA,kBAAA,OAAA,CAAA,UAAA,CAAA,cAAA,GAAA,GAAA,CAAA,KAAA,CAAA,KAAA,CAAA,GAAA,CAAA;AACA;;AACA,qBAAA,YAAA;AACA,kBAAA,OAAA,CAAA,UAAA,CAAA,UAAA,GACA,GAAA,CAAA,KAAA,IAAA,MAAA,GAAA,IAAA,GAAA,KADA;AAEA;;AACA,qBAAA,aAAA;AACA,kBAAA,OAAA,CAAA,UAAA,CAAA,WAAA,GAAA,GAAA,CAAA,KAAA,CAAA,KAAA,CAAA,GAAA,CAAA;AACA;;AACA,qBAAA,iBAAA;AACA,kBAAA,OAAA,CAAA,UAAA,CAAA,eAAA,GAAA,GAAA,CAAA,KAAA;AACA;;AACA,qBAAA,cAAA;AACA,kBAAA,OAAA,CAAA,UAAA,CAAA,YAAA,GACA,GAAA,CAAA,KAAA,IAAA,EAAA,GAAA,KAAA,GAAA,GAAA,CAAA,KADA;AAEA;;AACA,qBAAA,cAAA;AACA,kBAAA,OAAA,CAAA,UAAA,CAAA,YAAA,GACA,GAAA,CAAA,KAAA,KAAA,MAAA,GAAA,IAAA,GAAA,KADA;AAEA;;AACA,qBAAA,aAAA;AACA,kBAAA,OAAA,CAAA,UAAA,CAAA,WAAA,GAAA,GAAA,CAAA,KAAA,GAAA,CAAA;AACA;;AACA,qBAAA,aAAA;AACA,kBAAA,OAAA,CAAA,UAAA,CAAA,WAAA,GAAA,GAAA,CAAA,KAAA,GAAA,CAAA;AACA;;AACA,qBAAA,aAAA;AACA,kBAAA,OAAA,CAAA,UAAA,CAAA,WAAA,GAAA,GAAA,CAAA,KAAA,GAAA,CAAA;AACA;;AACA,qBAAA,aAAA;AACA,kBAAA,OAAA,CAAA,UAAA,CAAA,WAAA,GAAA,GAAA,CAAA,KAAA,GAAA,CAAA;AACA;;AACA,qBAAA,aAAA;AACA,kBAAA,OAAA,CAAA,UAAA,CAAA,WAAA,GAAA,GAAA,CAAA,KAAA,GAAA,CAAA;AACA;;AACA,qBAAA,YAAA;AACA,kBAAA,OAAA,CAAA,UAAA,CAAA,UAAA,GAAA,GAAA,CAAA,KAAA;AACA;;AACA,qBAAA,aAAA;AACA,kBAAA,OAAA,CAAA,UAAA,CAAA,WAAA,GAAA,GAAA,CAAA,KAAA,GAAA,CAAA;AACA;;AACA,qBAAA,YAAA;AACA,kBAAA,OAAA,CAAA,UAAA,CAAA,UAAA,GAAA,GAAA,CAAA,KAAA,GAAA,CAAA;AACA;AA7HA;AA+HA,aAhIA;AAiIA,YAAA,OAAA,CAAA,UAAA,CAAA,KAAA,GACA,YAAA,CAAA,OAAA,CAAA,gBAAA,KAAA,MAAA,GAAA,IAAA,GAAA,KADA;AAEA,YAAA,OAAA,CAAA,UAAA,CAAA,MAAA,GACA,YAAA,CAAA,OAAA,CAAA,YAAA,KAAA,MAAA,GAAA,IAAA,GAAA,KADA,CApIA,CAsIA;;AAEA,YAAA,OAAA,CAAA,cAAA,GAAA,IAAA,CAAA,KAAA,CAAA,IAAA,CAAA,SAAA,CAAA,OAAA,CAAA,UAAA,CAAA,CAAA;;AACA,YAAA,OAAA,CAAA,WAAA,CAAA,OAAA,CAAA,cAAA;AACA,WA1IA,MA0IA;AACA,YAAA,IAAA;;AACA,YAAA,OAAA,CAAA,mBAAA,CAAA,KAAA;AACA;AACA,SA/IA;AAgJA,OAvJA;AAwJA,KAzsBA;AA0sBA,IAAA,cA1sBA,4BA0sBA;AAAA;;AACA;AAEA,UAAA,MAAA,GAAA;AACA,QAAA,WAAA,EAAA,IADA;AAEA,QAAA,MAAA,EAAA;AAFA,OAAA;AAKA,WAAA,QAAA,CAAA,gBAAA,CAAA,IAAA,CAAA,MAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,YAAA,GAAA,CAAA,KAAA,GAAA,CAAA,IAAA,GAAA,CAAA,OAAA,CAAA,CAAA,EAAA,UAAA,CAAA,MAAA,GAAA,CAAA,EAAA;AACA,UAAA,GAAA,CAAA,OAAA,CAAA,CAAA,EAAA,UAAA,CAAA,OAAA,CAAA,UAAA,GAAA,EAAA;AACA,oBAAA,GAAA,CAAA,GAAA;AACA,mBAAA,iBAAA;AACA,gBAAA,OAAA,CAAA,cAAA,CAAA,eAAA,GACA,GAAA,CAAA,KAAA,IAAA,MAAA,GAAA,IAAA,GAAA,KADA;AAEA;;AACA,mBAAA,KAAA;AACA,gBAAA,OAAA,CAAA,cAAA,CAAA,GAAA,GAAA,GAAA,CAAA,KAAA;AACA;;AACA,mBAAA,UAAA;AACA,gBAAA,OAAA,CAAA,cAAA,CAAA,QAAA,GAAA,GAAA,CAAA,KAAA;AACA;;AACA,mBAAA,QAAA;AACA,gBAAA,OAAA,CAAA,cAAA,CAAA,MAAA,GAAA,GAAA,CAAA,KAAA;AACA;AAbA;AAeA,WAhBA;AAiBA,UAAA,OAAA,CAAA,kBAAA,GAAA,IAAA,CAAA,KAAA,CACA,IAAA,CAAA,SAAA,CAAA,OAAA,CAAA,cAAA,CADA,CAAA;;AAGA,UAAA,OAAA,CAAA,qBAAA,CAAA,OAAA,CAAA,kBAAA;AACA;AACA,OAxBA;AAyBA,KA3uBA;AA4uBA,IAAA,wBA5uBA,sCA4uBA;AAAA;;AACA;AACA,UAAA,SAAA,GAAA,EAAA;AACA,UAAA,OAAA,GAAA,KAAA,UAAA,CAAA,OAAA;AACA,UAAA,MAAA,GAAA,KAAA,UAAA,CAAA,MAAA;AACA,UAAA,UAAA,GAAA,KAAA,UAAA,CAAA,UAAA;AACA,UAAA,QAAA,GAAA,KAAA,UAAA,CAAA,QAAA;AACA,UAAA,cAAA,GAAA,KAAA,UAAA,CAAA,cAAA;AACA,UAAA,aAAA,GAAA,KAAA,UAAA,CAAA,aAAA;AACA,UAAA,UAAA,GAAA,KAAA,UAAA,CAAA,UAAA;AACA,UAAA,UAAA,GAAA,KAAA,UAAA,CAAA,UAAA;AACA,UAAA,WAAA,GAAA,KAAA,UAAA,CAAA,WAAA;AACA,UAAA,iBAAA,GAAA,KAAA,UAAA,CAAA,iBAAA;AACA,UAAA,iBAAA,GAAA,KAAA,UAAA,CAAA,iBAAA;AACA,UAAA,kBAAA,GAAA,KAAA,UAAA,CAAA,kBAAA;AACA,UAAA,eAAA,GAAA,KAAA,UAAA,CAAA,eAAA;AACA,UAAA,SAAA,GAAA,KAAA,UAAA,CAAA,SAAA;AACA,UAAA,gBAAA,GAAA,KAAA,UAAA,CAAA,gBAAA;AACA,UAAA,WAAA,GAAA,KAAA,UAAA,CAAA,WAAA;AACA,UAAA,cAAA,GAAA,KAAA,UAAA,CAAA,cAAA;AACA,UAAA,UAAA,GAAA,KAAA,UAAA,CAAA,UAAA;AACA,UAAA,WAAA,GAAA,KAAA,UAAA,CAAA,WAAA;AACA,UAAA,YAAA,GAAA,KAAA,UAAA,CAAA,YAAA;AACA,UAAA,WAAA,GAAA,KAAA,UAAA,CAAA,WAAA;AACA,UAAA,WAAA,GAAA,KAAA,UAAA,CAAA,WAAA;AACA,UAAA,WAAA,GAAA,KAAA,UAAA,CAAA,WAAA;AACA,UAAA,WAAA,GAAA,KAAA,UAAA,CAAA,WAAA;AACA,UAAA,WAAA,GAAA,KAAA,UAAA,CAAA,WAAA;AACA,UAAA,UAAA,GAAA,KAAA,UAAA,CAAA,UAAA;AACA,UAAA,UAAA,GAAA,KAAA,UAAA,CAAA,UAAA,CA7BA,CA8BA;;AACA,UAAA,OAAA,CAAA,MAAA,GAAA,CAAA,EAAA;AACA,QAAA,SAAA,IACA,KAAA,EAAA,CAAA,qBAAA,EAAA;AACA,aAAA,KAAA,EAAA,CAAA,qBAAA;AADA,SAAA,IAEA,MAHA;AAIA;;AACA,UAAA,MAAA,CAAA,MAAA,GAAA,CAAA,EAAA;AACA,QAAA,SAAA,IACA,KAAA,EAAA,CAAA,qBAAA,EAAA;AACA,aAAA,KAAA,EAAA,CAAA,qBAAA;AADA,SAAA,IAEA,MAHA;AAIA;;AACA,UAAA,MAAA,CAAA,MAAA,GAAA,CAAA,EAAA;AACA,QAAA,SAAA,IAAA,KAAA,EAAA,CAAA,qBAAA,IAAA,MAAA;AACA;;AACA,UAAA,CAAA,UAAA,EAAA;AACA,QAAA,SAAA,IACA,KAAA,EAAA,CAAA,qBAAA,EAAA;AACA,aAAA,KAAA,EAAA,CAAA,qBAAA;AADA,SAAA,IAEA,MAHA;AAIA;;AACA,UAAA,QAAA,IAAA,IAAA,EAAA;AACA,QAAA,SAAA,IACA,KAAA,EAAA,CAAA,qBAAA,EAAA;AACA,aAAA,KAAA,EAAA,CAAA,qBAAA;AADA,SAAA,IAEA,MAHA;AAIA;;AACA,UAAA,cAAA,IAAA,IAAA,EAAA;AACA,YAAA,aAAA,IAAA,IAAA,EAAA;AACA,UAAA,SAAA,IACA,KAAA,EAAA,CAAA,qBAAA,EAAA;AACA,eAAA,KAAA,EAAA,CAAA,qBAAA;AADA,WAAA,IAEA,MAHA;AAIA;AACA;;AACA,UAAA,UAAA,IAAA,IAAA,EAAA;AACA,QAAA,SAAA,IACA,KAAA,EAAA,CAAA,qBAAA,EAAA;AACA,aAAA,KAAA,EAAA,CAAA,qBAAA;AADA,SAAA,IAEA,MAHA;AAIA,OALA,MAKA;AACA,YAAA,UAAA,GAAA,EAAA,EAAA;AACA,UAAA,SAAA,IACA,KAAA,EAAA,CAAA,qBAAA,IACA,KAAA,EAAA,CAAA,qBAAA,EAAA;AACA,eAAA,EADA;AAEA,eAAA;AAFA,WAAA,CADA,GAKA,MANA;AAOA;AACA;;AACA,UAAA,UAAA,IAAA,IAAA,EAAA;AACA,QAAA,SAAA,IACA,KAAA,EAAA,CAAA,qBAAA,EAAA;AACA,aAAA,KAAA,EAAA,CAAA,qBAAA;AADA,SAAA,IAEA,MAHA;AAIA,OALA,MAKA;AACA,YAAA,UAAA,GAAA,CAAA,EAAA;AACA,UAAA,SAAA,IACA,KAAA,EAAA,CAAA,qBAAA,IACA,KAAA,EAAA,CAAA,qBAAA,EAAA;AACA,eAAA,CADA;AAEA,eAAA;AAFA,WAAA,CADA,GAKA,MANA;AAOA;AACA;;AACA,UAAA,gBAAA,CAAA,MAAA,GAAA,CAAA,EAAA;AACA,QAAA,SAAA,IACA,KAAA,EAAA,CAAA,qBAAA,EAAA;AACA,aAAA,KAAA,EAAA,CAAA,qBAAA;AADA,SAAA,IAEA,MAHA;AAIA;;AACA,UACA,WAAA,KACA,cAAA,CAAA,MAAA,GAAA,CAAA,IACA,cAAA,CAAA,MAAA,IAAA,CAAA,IAAA,cAAA,CAAA,CAAA,CAAA,IAAA,EAFA,CADA,EAIA;AACA,QAAA,SAAA,IACA,KAAA,EAAA,CAAA,qBAAA,EAAA;AACA,aAAA,KAAA,EAAA,CAAA,qBAAA;AADA,SAAA,IAEA,MAHA;AAIA;;AAEA,UAAA,WAAA,IAAA,IAAA,EAAA;AACA,QAAA,SAAA,IACA,KAAA,EAAA,CAAA,qBAAA,EAAA;AACA,aAAA,KAAA,EAAA,CAAA,qBAAA;AADA,SAAA,IAEA,MAHA;AAIA;;AACA,UAAA,WAAA,IAAA,IAAA,EAAA;AACA,QAAA,SAAA,IACA,KAAA,EAAA,CAAA,qBAAA,EAAA;AACA,aAAA,KAAA,EAAA,CAAA,qBAAA;AADA,SAAA,IAEA,MAHA;AAIA;;AACA,UAAA,WAAA,IAAA,IAAA,EAAA;AACA,QAAA,SAAA,IACA,KAAA,EAAA,CAAA,qBAAA,EAAA;AACA,aAAA,KAAA,EAAA,CAAA,qBAAA;AADA,SAAA,IAEA,MAHA;AAIA;;AACA,UAAA,WAAA,IAAA,IAAA,EAAA;AACA,QAAA,SAAA,IACA,KAAA,EAAA,CAAA,qBAAA,EAAA;AACA,aAAA,KAAA,EAAA,CAAA,qBAAA;AADA,SAAA,IAEA,MAHA;AAIA;;AACA,UAAA,WAAA,IAAA,IAAA,EAAA;AACA,QAAA,SAAA,IACA,KAAA,EAAA,CAAA,qBAAA,EAAA;AACA,aAAA,KAAA,EAAA,CAAA,qBAAA;AADA,SAAA,IAEA,MAHA;AAIA;;AACA,UAAA,UAAA,IAAA,EAAA,EAAA;AACA,QAAA,SAAA,IAAA,KAAA,EAAA,CAAA,qBAAA,IAAA,MAAA;AACA;;AACA,UAAA,UAAA,IAAA,IAAA,EAAA;AACA,QAAA,SAAA,IACA,KAAA,EAAA,CAAA,qBAAA,EAAA;AACA,aAAA,KAAA,EAAA,CAAA,qBAAA;AADA,SAAA,IAEA,MAHA;AAIA;;AAEA,WAAA,gBAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,YACA,IAAA,CAAA,OAAA,KACA,IAAA,CAAA,YAAA,IAAA,EAAA,IACA,IAAA,CAAA,YAAA,IAAA,EADA,IAEA,IAAA,CAAA,eAAA,IAAA,EAFA,IAGA,IAAA,CAAA,OAAA,IAAA,EAHA,IAIA,IAAA,CAAA,QAAA,IAAA,EAJA,IAKA,IAAA,CAAA,QAAA,IAAA,EALA,IAMA,IAAA,CAAA,eAAA,IAAA,EAPA,CADA,EASA;AACA,cAAA,IAAA,GAAA,OAAA,CAAA,cAAA,CAAA,IAAA,CAAA,UAAA,KAAA;AAAA,mBAAA,IAAA,CAAA,IAAA,IAAA,KAAA,CAAA,IAAA;AAAA,WAAA,EACA,IADA;;AAEA,UAAA,SAAA,IACA,OAAA,CAAA,EAAA,CAAA,qBAAA,EAAA;AACA,eAAA,OAAA,CAAA,EAAA,CAAA,qBAAA,IAAA,KAAA,GAAA;AADA,WAAA,IAEA,MAHA;AAIA;AACA,OAlBA,EA3JA,CA8KA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,UAAA,SAAA,EAAA;AACA,aAAA,OAAA,CAAA,KAAA,CAAA;AACA,UAAA,KAAA,EAAA,KAAA,EAAA,CAAA,qBAAA,CADA;AAEA,UAAA,IAAA,EAAA,SAFA;AAGA,UAAA,QAAA,EAAA,MAAA,CAAA;AAHA,SAAA;AAKA,eAAA,KAAA;AACA,OAPA,MAOA;AACA,eAAA,IAAA;AACA;AACA,KA76BA;AA86BA,IAAA,mBA96BA,+BA86BA,IA96BA,EA86BA;AAAA;;AACA,UAAA,SAAA,GAAA,KAAA,eAAA,EAAA;;AACA,UAAA,IAAA,KAAA,QAAA,EAAA;AACA,aAAA,aAAA,GAAA,IAAA;AACA,OAJA,CAKA;;;AACA,UAAA,oBAAA,GAAA,EAAA;AACA,UAAA,mBAAA,GAAA,EAAA;AACA,UAAA,oBAAA,GAAA,EAAA;AACA,UAAA,kBAAA,GAAA,EAAA;AACA,UAAA,cAAA,GAAA,EAAA;AACA,UAAA,gBAAA,GAAA,EAAA;AACA,UAAA,KAAA,GAAA,KAAA,wBAAA,EAAA;;AACA,UAAA,KAAA,EAAA;AACA,YAAA,IAAA,KAAA,KAAA,EAAA;AACA,eAAA,UAAA,CAAA,iBAAA,GAAA,KAAA,oBAAA,CAAA,GAAA,CACA,UAAA,IAAA;AAAA,mBAAA,IAAA,CAAA,GAAA;AAAA,WADA,CAAA;AAGA,UAAA,mBAAA,GAAA,KAAA,UAAA,CAAA,iBAAA;AAEA,eAAA,UAAA,CAAA,kBAAA,GAAA,KAAA,qBAAA,CAAA,GAAA,CACA,UAAA,IAAA;AAAA,mBAAA,IAAA,CAAA,GAAA;AAAA,WADA,CAAA;AAGA,UAAA,oBAAA,GAAA,KAAA,UAAA,CAAA,kBAAA;AAEA,eAAA,UAAA,CAAA,iBAAA,GAAA,KAAA,oBAAA,CAAA,GAAA,CACA,UAAA,IAAA;AAAA,mBAAA,IAAA,CAAA,GAAA;AAAA,WADA,CAAA;AAGA,UAAA,oBAAA,GAAA,KAAA,UAAA,CAAA,iBAAA;AAEA,eAAA,UAAA,CAAA,eAAA,GAAA,KAAA,wBAAA,CAAA,GAAA,CACA,UAAA,IAAA;AAAA,mBAAA,IAAA,CAAA,GAAA;AAAA,WADA,CAAA;AAGA,UAAA,kBAAA,GAAA,KAAA,UAAA,CAAA,eAAA;AACA;;AAEA,YAAA,OAAA,GAAA;AACA,UAAA,QAAA,EAAA,cADA;AAEA,UAAA,UAAA,EAAA,CACA;AACA,YAAA,GAAA,EAAA,SADA;AAEA,YAAA,KAAA,EAAA,KAAA,UAAA,CAAA,OAAA,CAAA,IAAA,CAAA,GAAA;AAFA,WADA,EAKA;AACA,YAAA,GAAA,EAAA,YADA;AAEA,YAAA,KAAA,EAAA,KAAA,UAAA,CAAA;AAFA,WALA,EASA;AACA,YAAA,GAAA,EAAA,SADA;AAEA,YAAA,KAAA,EAAA,KAAA,UAAA,CAAA,OAAA,CAAA,IAAA,CAAA,GAAA;AAFA,WATA,EAaA;AACA;AACA;AACA;AACA;AACA,YAAA,GAAA,EAAA,QADA;AAEA,YAAA,KAAA,EAAA,KAAA,UAAA,CAAA,MAAA,CAAA,IAAA,CAAA,GAAA;AAFA,WAjBA,EAqBA;AACA,YAAA,GAAA,EAAA,UADA;AAEA,YAAA,KAAA,EAAA,KAAA,UAAA,CAAA;AAFA,WArBA,EAyBA;AACA,YAAA,GAAA,EAAA,YADA;AAEA,YAAA,KAAA,EAAA,KAAA,UAAA,CAAA;AAFA,WAzBA,EA6BA;AACA,YAAA,GAAA,EAAA,eADA;AAEA,YAAA,KAAA,EAAA,KAAA,UAAA,CAAA;AAFA,WA7BA,EAiCA;AACA,YAAA,GAAA,EAAA,UADA;AAEA,YAAA,KAAA,EAAA,KAAA,UAAA,CAAA;AAFA,WAjCA,EAqCA;AACA,YAAA,GAAA,EAAA,gBADA;AAEA,YAAA,KAAA,EAAA,KAAA,UAAA,CAAA;AAFA,WArCA,EAyCA;AACA,YAAA,GAAA,EAAA,eADA;AAEA,YAAA,KAAA,EAAA,KAAA,UAAA,CAAA;AAFA,WAzCA,EA6CA;AACA,YAAA,GAAA,EAAA,aADA;AAEA,YAAA,KAAA,EAAA,KAAA,UAAA,CAAA;AAFA,WA7CA,EAiDA;AACA,YAAA,GAAA,EAAA,YADA;AAEA,YAAA,KAAA,EAAA,KAAA,UAAA,CAAA;AAFA,WAjDA,EAqDA;AACA,YAAA,GAAA,EAAA,aADA;AAEA,YAAA,KAAA,EAAA,KAAA,UAAA,CAAA;AAFA,WArDA,EAyDA;AACA,YAAA,GAAA,EAAA,YADA;AAEA,YAAA,KAAA,EAAA,KAAA,UAAA,CAAA;AAFA,WAzDA,EA6DA;AACA,YAAA,GAAA,EAAA,mBADA;AAEA,YAAA,KAAA,EAAA,KAAA,UAAA,CAAA,iBAAA,CAAA,IAAA,CAAA,GAAA;AAFA,WA7DA,EAiEA;AACA,YAAA,GAAA,EAAA,oBADA;AAEA,YAAA,KAAA,EAAA,KAAA,UAAA,CAAA,kBAAA,CAAA,IAAA,CAAA,GAAA;AAFA,WAjEA,EAqEA;AACA,YAAA,GAAA,EAAA,mBADA;AAEA,YAAA,KAAA,EAAA,KAAA,UAAA,CAAA,iBAAA,CAAA,IAAA,CAAA,GAAA;AAFA,WArEA,EAyEA;AACA,YAAA,GAAA,EAAA,iBADA;AAEA,YAAA,KAAA,EAAA,KAAA,UAAA,CAAA,eAAA,CAAA,IAAA,CAAA,GAAA;AAFA,WAzEA,EA6EA;AACA,YAAA,GAAA,EAAA,WADA;AAEA,YAAA,KAAA,EAAA,KAAA,UAAA,CAAA,SAAA,CAAA,IAAA,CAAA,GAAA;AAFA,WA7EA,EAiFA;AACA,YAAA,GAAA,EAAA,kBADA;AAEA,YAAA,KAAA,EAAA,KAAA,UAAA,CAAA,gBAAA,CAAA,IAAA,CAAA,GAAA;AAFA,WAjFA,EAqFA;AACA,YAAA,GAAA,EAAA,gBADA;AAEA,YAAA,KAAA,EAAA,KAAA,UAAA,CAAA,cAAA,CAAA,IAAA,CAAA,GAAA;AAFA,WArFA,EAyFA;AACA,YAAA,GAAA,EAAA,aADA;AAEA,YAAA,KAAA,EAAA,KAAA,UAAA,CAAA;AAFA,WAzFA,EA6FA;AACA,YAAA,GAAA,EAAA,YADA;AAEA,YAAA,KAAA,EAAA,KAAA,UAAA,CAAA;AAFA,WA7FA,EAiGA;AACA,YAAA,GAAA,EAAA,aADA;AAEA,YAAA,KAAA,EAAA,KAAA,UAAA,CAAA,WAAA,CAAA,IAAA,CAAA,GAAA;AAFA,WAjGA,EAqGA;AACA,YAAA,GAAA,EAAA,iBADA;AAEA,YAAA,KAAA,EAAA,KAAA,UAAA,CAAA;AAFA,WArGA,EAyGA;AACA,YAAA,GAAA,EAAA,cADA;AAEA,YAAA,KAAA,EAAA,KAAA,UAAA,CAAA;AAFA,WAzGA,EA6GA;AACA,YAAA,GAAA,EAAA,cADA;AAEA,YAAA,KAAA,EAAA,KAAA,UAAA,CAAA;AAFA,WA7GA,EAiHA;AACA,YAAA,GAAA,EAAA,aADA;AAEA,YAAA,KAAA,EAAA,KAAA,UAAA,CAAA;AAFA,WAjHA,EAqHA;AACA,YAAA,GAAA,EAAA,aADA;AAEA,YAAA,KAAA,EAAA,KAAA,UAAA,CAAA;AAFA,WArHA,EAyHA;AACA,YAAA,GAAA,EAAA,aADA;AAEA,YAAA,KAAA,EAAA,KAAA,UAAA,CAAA;AAFA,WAzHA,EA6HA;AACA,YAAA,GAAA,EAAA,aADA;AAEA,YAAA,KAAA,EAAA,KAAA,UAAA,CAAA;AAFA,WA7HA,EAiIA;AACA,YAAA,GAAA,EAAA,aADA;AAEA,YAAA,KAAA,EAAA,KAAA,UAAA,CAAA;AAFA,WAjIA,EAqIA;AACA,YAAA,GAAA,EAAA,YADA;AAEA,YAAA,KAAA,EAAA,KAAA,UAAA,CAAA;AAFA,WArIA,EAyIA;AACA,YAAA,GAAA,EAAA,aADA;AAEA,YAAA,KAAA,EAAA,KAAA,UAAA,CAAA;AAFA,WAzIA,EA6IA;AACA,YAAA,GAAA,EAAA,YADA;AAEA,YAAA,KAAA,EAAA,KAAA,UAAA,CAAA;AAFA,WA7IA;AAFA,SAAA;;AAqJA,YAAA,KAAA,UAAA,CAAA,UAAA,IAAA,EAAA,EAAA;AACA,UAAA,YAAA,CAAA,UAAA,CAAA,iBAAA;AACA,UAAA,QAAA,CAAA,KAAA,GAAA,KAAA,EAAA,CAAA,qBAAA,CAAA;AACA,SAHA,MAGA;AACA,UAAA,YAAA,CAAA,OAAA,CAAA,iBAAA,EAAA,KAAA,UAAA,CAAA,UAAA;AACA,UAAA,QAAA,CAAA,KAAA,GAAA,KAAA,UAAA,CAAA,UAAA;AACA;;AACA,QAAA,YAAA,CAAA,OAAA,CAAA,gBAAA,EAAA,KAAA,UAAA,CAAA,UAAA;AACA,QAAA,YAAA,CAAA,OAAA,CACA,gBADA,EAEA,KAAA,UAAA,CAAA,KAAA,IAAA,IAAA,GAAA,MAAA,GAAA,OAFA;AAIA,QAAA,YAAA,CAAA,OAAA,CACA,YADA,EAEA,KAAA,UAAA,CAAA,MAAA,IAAA,IAAA,GAAA,MAAA,GAAA,OAFA;AAIA,QAAA,YAAA,CAAA,OAAA,CACA,gBADA,EAEA,KAAA,UAAA,CAAA,UAAA,IAAA,IAAA,GAAA,MAAA,GAAA,OAFA;AAIA,QAAA,YAAA,CAAA,OAAA,CAAA,cAAA,EAAA,KAAA,UAAA,CAAA,eAAA;AACA,QAAA,YAAA,CAAA,OAAA,CAAA,gBAAA,EAAA,KAAA,UAAA,CAAA,UAAA;AACA,QAAA,gBAAA,CAAA,IAAA,CAAA,OAAA;AAEA,YAAA,aAAA,GAAA,CACA;AACA,UAAA,IAAA,EAAA,YADA;AAEA,UAAA,IAAA,EAAA,YAFA;AAGA,UAAA,QAAA,EAAA;AAHA,SADA,EAMA;AACA,UAAA,IAAA,EAAA,WADA;AAEA,UAAA,IAAA,EAAA,WAFA;AAGA,UAAA,QAAA,EAAA;AAHA,SANA,CAAA;AAYA,YAAA,WAAA,GAAA;AACA,UAAA,WAAA,EAAA,IADA;AAEA,UAAA,MAAA,EACA;AAHA,SAAA;AAMA,YAAA,YAAA,GAAA;AACA,UAAA,MAAA,EAAA;AADA,SAAA,CAtNA,CA0NA;;AACA,YAAA,SAAA,GAAA;AACA,UAAA,QAAA,EAAA,gBADA;AAEA,UAAA,UAAA,EAAA;AAFA,SAAA;AAIA,aAAA,gBAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,cAAA,aAAA,GAAA,CACA;AACA,YAAA,GAAA,EAAA,IAAA,CAAA,IAAA,GAAA,gBADA;AAEA,YAAA,KAAA,EAAA,IAAA,CAAA;AAFA,WADA,EAKA;AACA,YAAA,GAAA,EAAA,IAAA,CAAA,IAAA,GAAA,eADA;AAEA,YAAA,KAAA,EAAA,IAAA,CAAA;AAFA,WALA,EASA;AACA,YAAA,GAAA,EAAA,IAAA,CAAA,IAAA,GAAA,iBADA;AAEA,YAAA,KAAA,EAAA,IAAA,CAAA;AAFA,WATA,EAaA;AACA,YAAA,GAAA,EAAA,IAAA,CAAA,IAAA,GAAA,gBADA;AAEA,YAAA,KAAA,EAAA,IAAA,CAAA;AAFA,WAbA,EAiBA;AACA,YAAA,GAAA,EAAA,IAAA,CAAA,IAAA,GAAA,kBADA;AAEA,YAAA,KAAA,EAAA,IAAA,CAAA;AAFA,WAjBA,EAqBA;AACA,YAAA,GAAA,EAAA,IAAA,CAAA,IAAA,GAAA,iBADA;AAEA,YAAA,KAAA,EAAA,IAAA,CAAA;AAFA,WArBA,CAAA;AA0BA,UAAA,SAAA,CAAA,UAAA,gCAAA,SAAA,CAAA,UAAA,GAAA,aAAA;AACA,SA5BA;AA6BA,QAAA,gBAAA,CAAA,IAAA,CAAA,SAAA,EA5PA,CA6PA;AAEA;;AACA,YAAA,aAAA,GAAA;AACA,UAAA,QAAA,EAAA,oBADA;AAEA,UAAA,UAAA,EAAA;AAFA,SAAA;AAIA,aAAA,gBAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,cAAA,aAAA,GAAA,CACA;AACA,YAAA,GAAA,EAAA,IAAA,CAAA,IAAA,GAAA,UADA;AAEA,YAAA,KAAA,EAAA,IAAA,CAAA;AAFA,WADA,EAKA;AACA,YAAA,GAAA,EAAA,IAAA,CAAA,IAAA,GAAA,eADA;AAEA,YAAA,KAAA,EAAA,IAAA,CAAA;AAFA,WALA,EASA;AACA,YAAA,GAAA,EAAA,IAAA,CAAA,IAAA,GAAA,eADA;AAEA,YAAA,KAAA,EAAA,IAAA,CAAA;AAFA,WATA,EAaA;AACA,YAAA,GAAA,EAAA,IAAA,CAAA,IAAA,GAAA,kBADA;AAEA,YAAA,KAAA,EAAA,IAAA,CAAA;AAFA,WAbA,EAiBA;AACA,YAAA,GAAA,EAAA,IAAA,CAAA,IAAA,GAAA,UADA;AAEA,YAAA,KAAA,EAAA,IAAA,CAAA;AAFA,WAjBA,EAqBA;AACA,YAAA,GAAA,EAAA,IAAA,CAAA,IAAA,GAAA,WADA;AAEA,YAAA,KAAA,EAAA,IAAA,CAAA;AAFA,WArBA,EAyBA;AACA,YAAA,GAAA,EAAA,IAAA,CAAA,IAAA,GAAA,WADA;AAEA,YAAA,KAAA,EAAA,IAAA,CAAA;AAFA,WAzBA,EA6BA;AACA,YAAA,GAAA,EAAA,IAAA,CAAA,IAAA,GAAA,kBADA;AAEA,YAAA,KAAA,EAAA,IAAA,CAAA;AAFA,WA7BA,CAAA;AAkCA,UAAA,aAAA,CAAA,UAAA,gCACA,aAAA,CAAA,UADA,GAEA,aAFA;AAIA,SAvCA;AAwCA,QAAA,gBAAA,CAAA,IAAA,CAAA,aAAA,EA5SA,CA6SA;AACA;;AACA,YAAA,oBAAA,GAAA;AACA,UAAA,QAAA,EAAA,2BADA;AAEA,UAAA,UAAA,EAAA;AAFA,SAAA;AAIA,aAAA,uBAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,cAAA,aAAA,GAAA,CACA;AACA,YAAA,GAAA,EAAA,IAAA,CAAA,KAAA,GAAA,WADA;AAEA,YAAA,KAAA,EAAA,IAAA,CAAA;AAFA,WADA,EAKA;AACA,YAAA,GAAA,EAAA,IAAA,CAAA,KAAA,GAAA,WADA;AAEA,YAAA,KAAA,EAAA,IAAA,CAAA;AAFA,WALA,EASA;AACA,YAAA,GAAA,EAAA,IAAA,CAAA,KAAA,GAAA,cADA;AAEA,YAAA,KAAA,EAAA,IAAA,CAAA;AAFA,WATA,CAAA;AAcA,UAAA,oBAAA,CAAA,UAAA,gCACA,oBAAA,CAAA,UADA,GAEA,aAFA;AAIA,SAnBA;AAoBA,QAAA,gBAAA,CAAA,IAAA,CAAA,oBAAA,EAvUA,CAwUA;AACA;;AAEA,YAAA,KAAA,cAAA,CAAA,eAAA,EAAA;AACA,cAAA,QAAA,GAAA,CACA,KAAA,EAAA,CAAA,qBAAA,CADA,EAEA,UAFA,EAGA,kBAHA,CAAA;;AAKA,cACA,KAAA,cAAA,CAAA,GAAA,IAAA,EAAA,IACA,KAAA,cAAA,CAAA,QAAA,IAAA,EADA,IAEA,KAAA,cAAA,CAAA,MAAA,IAAA,EAHA,EAIA;AACA,iBAAA,OAAA,CAAA,KAAA,CAAA;AACA,cAAA,KAAA,EAAA,KAAA,EAAA,CAAA,qBAAA,CADA;AAEA,cAAA,IAAA,EAAA,KAAA,EAAA,CAAA,qBAAA,EAAA;AAAA,mBAAA,QAAA,CAAA,IAAA,CAAA,IAAA;AAAA,eAAA,CAFA;AAGA,cAAA,QAAA,EAAA,MAAA,CAAA;AAHA,aAAA;AAKA,iBAAA,aAAA,GAAA,KAAA;AACA;AACA;AACA;;AACA,YAAA,iBAAA,GAAA;AACA,UAAA,QAAA,EAAA,wBADA;AAEA,UAAA,UAAA,EAAA,CACA;AACA,YAAA,GAAA,EAAA,iBADA;AAEA,YAAA,KAAA,EAAA,KAAA,cAAA,CAAA;AAFA,WADA,EAKA;AACA,YAAA,GAAA,EAAA,KADA;AAEA,YAAA,KAAA,EAAA,KAAA,cAAA,CAAA;AAFA,WALA,EASA;AACA,YAAA,GAAA,EAAA,UADA;AAEA,YAAA,KAAA,EAAA,KAAA,cAAA,CAAA;AAFA,WATA,EAaA;AACA,YAAA,GAAA,EAAA,QADA;AAEA,YAAA,KAAA,EAAA,KAAA,cAAA,CAAA;AAFA,WAbA;AAFA,SAAA;AAqBA,QAAA,gBAAA,CAAA,IAAA,CAAA,iBAAA,EApXA,CAqXA;;AACA,YAAA,cAAA,GAAA,EAAA;AACA,YAAA,uBAAA,GAAA,EAAA;AACA,YAAA,wBAAA,GAAA,EAAA;AACA,YAAA,uBAAA,GAAA,EAAA;AACA,aAAA,QAAA,CAAA,gBAAA,CAAA,IAAA,CAAA,YAAA,EAAA,IAAA,CAAA,UAAA,MAAA,EAAA;AACA,UAAA,OAAA,CAAA,QAAA,CAAA,eAAA,CAAA,IAAA,CAAA,WAAA,EAAA,IAAA,CAAA,UAAA,QAAA,EAAA;AACA,YAAA,OAAA,CAAA,QAAA,CAAA,aAAA,CACA,IADA,CACA,aADA,EACA,IADA,EACA;AACA,cAAA,SAAA,EAAA,SADA;AAEA,cAAA,eAAA,EAAA,iBAFA;AAGA,cAAA,aAAA,EAAA;AAHA,aADA,EAMA,IANA,CAMA,UAAA,UAAA,EAAA;AACA,cAAA,OAAA,CAAA,QAAA,CAAA,iBAAA,CACA,IADA,CACA,gBADA,EACA,IADA,EACA;AACA,gBAAA,SAAA,EAAA,SADA;AAEA,gBAAA,eAAA,EAAA,iBAFA;AAGA,gBAAA,aAAA,EAAA;AAHA,eADA,EAMA,IANA,CAMA,UAAA,WAAA,EAAA;AACA,gBAAA,OAAA,CAAA,GAAA,CAAA,WAAA;;AACA,oBAAA,IAAA,IAAA,KAAA,EAAA;AACA,kBAAA,OAAA,CAAA,OAAA,CAAA,OAAA,CAAA;AACA,oBAAA,KAAA,EAAA,OAAA,CAAA,EAAA,CAAA,qBAAA,CADA;AAEA,oBAAA,IAAA,EAAA,OAAA,CAAA,EAAA,CAAA,qBAAA,CAFA;AAGA,oBAAA,QAAA,EAAA,MAAA,CAAA;AAHA,mBAAA;AAKA;;AAEA,gBAAA,YAAA,CAAA,OAAA,CACA,cADA,EAEA,OAAA,CAAA,UAAA,CAAA,QAFA;AAIA,gBAAA,YAAA,CAAA,OAAA,CACA,mBADA,EAEA,OAAA,CAAA,UAAA,CAAA,aAFA;AAKA,gBAAA,OAAA,CAAA,MAAA,GAAA,KAAA;AACA,gBAAA,OAAA,CAAA,cAAA,GAAA,IAAA,CAAA,KAAA,CACA,IAAA,CAAA,SAAA,CAAA,OAAA,CAAA,UAAA,CADA,CAAA;;AAGA,gBAAA,OAAA,CAAA,WAAA,CAAA,OAAA,CAAA,cAAA;;AAEA,gBAAA,OAAA,CAAA,iBAAA,GAAA,IAAA,CAAA,KAAA,CACA,IAAA,CAAA,SAAA,CAAA,OAAA,CAAA,cAAA,CADA,CAAA;;AAGA,gBAAA,OAAA,CAAA,qBAAA,CAAA,OAAA,CAAA,iBAAA;;AAEA,oBAAA,QAAA,CAAA,OAAA,CAAA,MAAA,GAAA,CAAA,EAAA;AACA,kBAAA,QAAA,CAAA,OAAA,CAAA,OAAA,CAAA,UAAA,YAAA,EAAA;AACA,wBAAA,cAAA,GAAA,YAAA,CAAA,SAAA;AACA,oBAAA,cAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,0BAAA,IAAA,CAAA,GAAA,IAAA,WAAA,EAAA;AACA,wBAAA,IAAA,CAAA,KAAA,GAAA,YAAA,CAAA,IAAA,CAAA,QAAA,CACA,YADA,IAGA,OAAA,CAAA,UAAA,CAAA,UAAA,CAAA,QAAA,EAHA,GAIA,YAAA,CAAA,IAAA,CAAA,QAAA,CAAA,aAAA,IACA,OAAA,CAAA,UAAA,CAAA,WAAA,CAAA,QAAA,EADA,GAEA,OAAA,CAAA,UAAA,CAAA,UAAA,CAAA,QAAA,EANA;AAOA;AACA,qBAVA;AAWA,wBAAA,WAAA,GAAA;AACA,sBAAA,IAAA,EAAA,YAAA,CAAA,IADA;AAEA,sBAAA,SAAA,EAAA,cAFA;AAGA;AACA;AACA;AACA;AACA;AACA,sBAAA,aAAA,EAAA;AACA,wBAAA,UAAA,EAAA,YAAA,CAAA,IAAA,CAAA,QAAA,CAAA,YAAA,IACA,OAAA,CAAA,YAAA,CAAA,YAAA,EAAA,MAAA,CADA,GAEA,YAAA,CAAA,IAAA,CAAA,QAAA,CAAA,aAAA,IACA,OAAA,CAAA,YAAA,CAAA,aAAA,EAAA,MAAA,CADA,GAEA,YAAA,CAAA,IAAA,CAAA,QAAA,CAAA,gBAAA,IACA,OAAA,CAAA,YAAA,CAAA,gBAAA,EAAA,MAAA,CADA,GAEA,OAAA,CAAA,YAAA,CAAA,YAAA,EAAA,MAAA,CAPA;AAQA,wBAAA,WAAA,EAAA,YAAA,CAAA,IAAA,CAAA,QAAA,CACA,YADA,IAGA,OAAA,CAAA,UAAA,CAAA,iBAHA,GAIA,YAAA,CAAA,IAAA,CAAA,QAAA,CAAA,aAAA,IACA,OAAA,CAAA,UAAA,CAAA,kBADA,GAEA,YAAA,CAAA,IAAA,CAAA,QAAA,CAAA,gBAAA,IACA,OAAA,CAAA,UAAA,CAAA,eADA,GAEA,OAAA,CAAA,UAAA,CAAA;AAhBA;AARA,qBAAA;AA2BA,oBAAA,cAAA,CAAA,IAAA,CAAA,WAAA;AACA,mBAzCA;;AA0CA,sBACA,CAAA,QAAA,CAAA,OAAA,CAAA,IAAA,CACA,UAAA,IAAA;AAAA,2BAAA,IAAA,CAAA,IAAA,IAAA,iBAAA;AAAA,mBADA,CADA,EAIA;AACA;AACA,wBAAA,eAAA,GAAA,OAAA,CAAA,wBAAA,CACA,mBADA,CAAA;;AAGA,oBAAA,cAAA,CAAA,IAAA,CAAA,eAAA;AACA;;AACA,sBACA,CAAA,QAAA,CAAA,OAAA,CAAA,IAAA,CACA,UAAA,IAAA;AAAA,2BAAA,IAAA,CAAA,IAAA,IAAA,kBAAA;AAAA,mBADA,CADA,EAIA;AACA;AACA,wBAAA,gBAAA,GAAA,OAAA,CAAA,yBAAA,CACA,oBADA,CAAA;;AAGA,oBAAA,cAAA,CAAA,IAAA,CAAA,gBAAA;AACA;;AACA,sBACA,CAAA,QAAA,CAAA,OAAA,CAAA,IAAA,CACA,UAAA,IAAA;AAAA,2BAAA,IAAA,CAAA,IAAA,IAAA,iBAAA;AAAA,mBADA,CADA,EAIA;AACA;AACA,wBAAA,eAAA,GAAA,OAAA,CAAA,wBAAA,CACA,oBADA,CAAA;;AAGA,oBAAA,cAAA,CAAA,IAAA,CAAA,eAAA;AACA;;AACA,sBACA,CAAA,QAAA,CAAA,OAAA,CAAA,IAAA,CACA,UAAA,IAAA;AAAA,2BAAA,IAAA,CAAA,IAAA,IAAA,qBAAA;AAAA,mBADA,CADA,EAIA;AACA;AACA,wBAAA,mBAAA,GAAA,OAAA,CAAA,4BAAA,CACA,kBADA,CAAA;;AAGA,oBAAA,cAAA,CAAA,IAAA,CAAA,mBAAA;AACA,mBAtFA,CAuFA;;;AACA,kBAAA,OAAA,CAAA,QAAA,CAAA,YAAA,CACA,IADA,CACA,cADA,EACA,IADA,EACA;AACA,oBAAA,SAAA,EAAA,SADA;AAEA,oBAAA,eAAA,EAAA,iBAFA;AAGA,oBAAA,aAAA,EAAA;AAHA,mBADA,EAMA,IANA,CAMA,UAAA,OAAA,EAAA;AACA,oBAAA,OAAA,CAAA,QAAA,CAAA,SAAA,CACA,IADA,CACA,cADA,EACA,IADA,EACA;AACA,sBAAA,SAAA,EAAA,SADA;AAEA,sBAAA,eAAA,EAAA,iBAFA;AAGA,sBAAA,aAAA,EAAA;AAHA,qBADA,EAMA,IANA,CAMA,UAAA,QAAA,EAAA;AACA,sBAAA,OAAA,CAAA,UAAA,CAAA,IAAA;AACA,qBARA;AASA,mBAhBA;AAiBA,iBAzGA,MAyGA;AACA;AACA,sBAAA,gBAAA,GAAA,OAAA,CAAA,wBAAA,CACA,oBADA,CAAA,CAFA,CAKA;;;AACA,sBAAA,gBAAA,GAAA,OAAA,CAAA,wBAAA,CACA,mBADA,CAAA,CANA,CASA;;;AACA,sBAAA,iBAAA,GAAA,OAAA,CAAA,yBAAA,CACA,oBADA,CAAA,CAVA,CAaA;;;AACA,sBAAA,oBAAA,GAAA,OAAA,CAAA,4BAAA,CACA,kBADA,CAAA;;AAIA,kBAAA,cAAA,CAAA,IAAA,CAAA,gBAAA;AACA,kBAAA,cAAA,CAAA,IAAA,CAAA,gBAAA;AACA,kBAAA,cAAA,CAAA,IAAA,CAAA,iBAAA;AACA,kBAAA,cAAA,CAAA,IAAA,CAAA,oBAAA;;AACA,kBAAA,OAAA,CAAA,QAAA,CAAA,YAAA,CACA,IADA,CACA,cADA,EACA,IADA,EACA;AACA,oBAAA,SAAA,EAAA,SADA;AAEA,oBAAA,eAAA,EAAA,iBAFA;AAGA,oBAAA,aAAA,EAAA;AAHA,mBADA,EAMA,IANA,CAMA,UAAA,OAAA,EAAA;AACA,oBAAA,OAAA,CAAA,UAAA,CAAA,IAAA;AACA,mBARA;AASA;AACA,eA7KA;AA8KA,aArLA;AAsLA,WAvLA;AAwLA,SAzLA;AA0LA,OApjBA,MAojBA;AACA,YAAA,IAAA,KAAA,QAAA,EAAA;AACA,eAAA,aAAA,GAAA,KAAA;AACA;AACA;AACA,KAp/CA;AAq/CA,IAAA,wBAr/CA,oCAq/CA,WAr/CA,EAq/CA;AACA,UAAA,KAAA,GAAA;AACA,QAAA,IAAA,EAAA,iBADA;AAEA,QAAA,IAAA,EACA,KAAA,EAAA,CAAA,qBAAA,IAAA,GAAA,GAAA,KAAA,EAAA,CAAA,qBAAA,CAHA;AAIA,QAAA,WAAA,EAAA,YAJA;AAKA,QAAA,SAAA,EAAA,CACA;AACA,UAAA,GAAA,EAAA,WADA;AAEA,UAAA,KAAA,EAAA,KAAA,UAAA,CAAA,UAAA,CAAA,QAAA;AAFA,SADA,EAKA;AAAA,UAAA,GAAA,EAAA,aAAA;AAAA,UAAA,KAAA,EAAA;AAAA,SALA,CALA;AAYA,QAAA,aAAA,EAAA;AACA,UAAA,WAAA,EAAA;AADA;AAZA,OAAA;AAgBA,aAAA,KAAA;AACA,KAvgDA;AAwgDA,IAAA,yBAxgDA,qCAwgDA,WAxgDA,EAwgDA;AACA,UAAA,KAAA,GAAA;AACA,QAAA,IAAA,EAAA,kBADA;AAEA,QAAA,IAAA,EACA,KAAA,EAAA,CAAA,qBAAA,IAAA,GAAA,GAAA,KAAA,EAAA,CAAA,qBAAA,CAHA;AAIA,QAAA,WAAA,EAAA,YAJA;AAKA,QAAA,SAAA,EAAA,CACA;AACA,UAAA,GAAA,EAAA,WADA;AAEA,UAAA,KAAA,EAAA,KAAA,UAAA,CAAA,WAAA,CAAA,QAAA;AAFA,SADA,EAKA;AAAA,UAAA,GAAA,EAAA,aAAA;AAAA,UAAA,KAAA,EAAA;AAAA,SALA,CALA;AAYA,QAAA,aAAA,EAAA;AACA,UAAA,WAAA,EAAA;AADA;AAZA,OAAA;AAgBA,aAAA,KAAA;AACA,KA1hDA;AA2hDA,IAAA,wBA3hDA,oCA2hDA,WA3hDA,EA2hDA;AACA,UAAA,KAAA,GAAA;AACA,QAAA,IAAA,EAAA,iBADA;AAEA,QAAA,IAAA,EAAA,KAAA,EAAA,CAAA,qBAAA,CAFA;AAGA,QAAA,WAAA,EAAA,YAHA;AAIA,QAAA,SAAA,EAAA,CACA;AACA,UAAA,GAAA,EAAA,WADA;AAEA,UAAA,KAAA,EAAA,KAAA,UAAA,CAAA,UAAA,CAAA,QAAA;AAFA,SADA,EAKA;AAAA,UAAA,GAAA,EAAA,aAAA;AAAA,UAAA,KAAA,EAAA;AAAA,SALA,CAJA;AAWA,QAAA,aAAA,EAAA;AACA,UAAA,WAAA,EAAA;AADA;AAXA,OAAA;AAeA,aAAA,KAAA;AACA,KA5iDA;AA6iDA,IAAA,4BA7iDA,wCA6iDA,WA7iDA,EA6iDA;AACA,UAAA,KAAA,GAAA;AACA,QAAA,IAAA,EAAA,qBADA;AAEA,QAAA,IAAA,EAAA,KAAA,EAAA,CAAA,qBAAA,CAFA;AAGA,QAAA,WAAA,EAAA,gBAHA;AAIA,QAAA,SAAA,EAAA,CAAA;AAAA,UAAA,GAAA,EAAA,aAAA;AAAA,UAAA,KAAA,EAAA;AAAA,SAAA,CAJA;AAKA,QAAA,aAAA,EAAA;AACA,UAAA,WAAA,EAAA;AADA;AALA,OAAA;AASA,aAAA,KAAA;AACA,KAxjDA;AAyjDA,IAAA,YAzjDA,wBAyjDA,IAzjDA,EAyjDA,cAzjDA,EAyjDA;AAAA;;AACA,UAAA,IAAA,GAAA,EAAA;;AACA,UAAA,cAAA,CAAA,OAAA,CAAA,MAAA,GAAA,CAAA,EAAA;AACA,QAAA,cAAA,CAAA,OAAA,CAAA,OAAA,CAAA,UAAA,GAAA,EAAA;AACA,cAAA,GAAA,CAAA,OAAA,EAAA;AACA,YAAA,GAAA,CAAA,OAAA,CAAA,OAAA,CAAA,UAAA,CAAA,EAAA;AACA,kBACA,IAAA,KAAA,YAAA,IACA,OAAA,CAAA,UAAA,CAAA,iBAAA,CAAA,QAAA,CAAA,CAAA,CAAA,IAAA,CAFA,EAGA;AACA,gBAAA,IAAA,CAAA,IAAA,CAAA,CAAA,CAAA,GAAA;AACA;;AACA,kBACA,IAAA,KAAA,aAAA,IACA,OAAA,CAAA,UAAA,CAAA,kBAAA,CAAA,QAAA,CAAA,CAAA,CAAA,IAAA,CAFA,EAGA;AACA,gBAAA,IAAA,CAAA,IAAA,CAAA,CAAA,CAAA,GAAA;AACA;;AACA,kBACA,IAAA,KAAA,YAAA,IACA,OAAA,CAAA,UAAA,CAAA,iBAAA,CAAA,QAAA,CAAA,CAAA,CAAA,IAAA,CAFA,EAGA;AACA,gBAAA,IAAA,CAAA,IAAA,CAAA,CAAA,CAAA,GAAA;AACA;;AACA,kBACA,IAAA,KAAA,gBAAA,IACA,OAAA,CAAA,UAAA,CAAA,eAAA,CAAA,QAAA,CAAA,CAAA,CAAA,IAAA,CAFA,EAGA;AACA,gBAAA,IAAA,CAAA,IAAA,CAAA,CAAA,CAAA,GAAA;AACA;AACA,aAzBA;AA0BA;AACA,SA7BA;AA8BA;;AACA,aAAA,IAAA;AACA,KA5lDA;AA6lDA,IAAA,UA7lDA,sBA6lDA,IA7lDA,EA6lDA;AAAA;;AAAA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA,sBACA,OAAA,CAAA,OAAA,IAAA,IADA;AAAA;AAAA;AAAA;;AAEA,gBAAA,SAFA,GAEA,CAAA,WAAA,CAFA;AAGA,gBAAA,OAHA,GAGA;AACA,kBAAA,KAAA,EAAA,OAAA,CAAA;AADA,iBAHA;AAAA;AAAA,uBAMA,OAAA,CAAA,QAAA,CAAA,kBAAA,CAAA,UAAA,CACA,OADA,EAEA,SAFA,CANA;;AAAA;AAMA,gBAAA,IANA;AAAA;AAAA;;AAAA;AAAA,sBAWA,CAAA,OAAA,CAAA,aAAA,IAAA,CAAA,OAAA,CAAA,UAXA;AAAA;AAAA;AAAA;;AAYA,gBAAA,MAZA,GAYA,WAZA;AAAA;AAAA,uBAaA,OAAA,CAAA,QAAA,CAAA,oBAAA,CAAA,IAAA,CAAA,MAAA,CAbA;;AAAA;AAaA,gBAAA,GAbA;AAcA,gBAAA,YAAA,CAAA,UAAA,CAAA,aAAA;;AAdA;AAAA,sBAiBA,OAAA,CAAA,IAAA,IAAA,IAjBA;AAAA;AAAA;AAAA;;AAkBA,gBAAA,IAlBA,GAkBA;AACA,kBAAA,KAAA,EAAA,OAAA,CAAA;AADA,iBAlBA;AAqBA,gBAAA,OArBA,GAqBA,CAAA,YAAA,CArBA;AAAA;AAAA,uBAsBA,OAAA,CAAA,QAAA,CAAA,kBAAA,CAAA,UAAA,CACA,IADA,EAEA,OAFA,CAtBA;;AAAA;AAsBA,gBAAA,IAtBA;;AAAA;AA2BA,gBAAA,OAAA,CAAA,aAAA,CAAA,IAAA;;AA3BA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AA4BA,KAznDA;AA0nDA,IAAA,aA1nDA,yBA0nDA,IA1nDA,EA0nDA;AAAA;;AACA,UAAA,KAAA,MAAA,IAAA,KAAA,UAAA,CAAA,UAAA,EAAA;AACA;AACA,aAAA,OAAA,CAAA,IAAA,CAAA;AACA,UAAA,IAAA,EAAA,kBAAA,CACA,8BAAA,KAAA,UAAA,CAAA,UADA,CADA,CAGA;;AAHA,SAAA;AAKA;;AACA,MAAA,UAAA,CAAA,YAAA;AACA,QAAA,OAAA,CAAA,MAAA,CAAA,MAAA,CAAA,qBAAA,EAAA,MAAA,GAAA,OAAA,EAAA;;AACA,YAAA,IAAA,IAAA,KAAA,EAAA;AACA,UAAA,OAAA,CAAA,aAAA,GAAA,KAAA;;AACA,UAAA,OAAA,CAAA,KAAA,CAAA,mBAAA;AACA;AACA,OANA,EAMA,IANA,CAAA;AAOA,KA1oDA;AA2oDA,IAAA,qBA3oDA,iCA2oDA,IA3oDA,EA2oDA;AAAA;;AACA,WAAA,oBAAA,CAAA,OAAA,CAAA,UAAA,CAAA,EAAA;AACA,gBAAA,CAAA,CAAA,OAAA;AACA,eAAA,CAAA;AACA,YAAA,CAAA,CAAA,KAAA,GAAA,IAAA,CAAA,eAAA;AACA,YAAA,OAAA,CAAA,mBAAA,GACA,IAAA,CAAA,eAAA,IAAA,IAAA,GACA,OAAA,CAAA,EAAA,CAAA,qBAAA,CADA,GAEA,OAAA,CAAA,EAAA,CAAA,qBAAA,CAHA;AAIA;;AACA,eAAA,CAAA;AACA,YAAA,CAAA,CAAA,KAAA,GAAA,IAAA,CAAA,GAAA;AACA;;AACA,eAAA,CAAA;AACA,YAAA,CAAA,CAAA,KAAA,GAAA,IAAA,CAAA,QAAA;AACA;;AACA,eAAA,CAAA;AACA,YAAA,CAAA,CAAA,KAAA,GAAA,IAAA,CAAA,MAAA;AACA;AAhBA;AAkBA,OAnBA,EADA,CAsBA;AACA;AACA;AACA;AACA,KArqDA;AAsqDA,IAAA,WAtqDA,uBAsqDA,IAtqDA,EAsqDA;AAAA;;AACA,UAAA,aAAA,GAAA,EAAA;AACA,UAAA,UAAA,GAAA,EAAA;AACA,UAAA,cAAA,GAAA,EAAA;AACA,MAAA,IAAA,CAAA,MAAA,CAAA,OAAA,CAAA,UAAA,CAAA,EAAA;AACA,YAAA,CAAA,GAAA,OAAA,CAAA,WAAA,CAAA,IAAA,CAAA,UAAA,CAAA;AAAA,iBAAA,CAAA,CAAA,KAAA,IAAA,CAAA;AAAA,SAAA,EAAA,KAAA;;AACA,QAAA,aAAA,CAAA,IAAA,CAAA,CAAA;AACA,OAHA;AAIA,WAAA,UAAA,GAAA,aAAA,CAAA,IAAA,CAAA,IAAA,CAAA;AAEA,MAAA,IAAA,CAAA,OAAA,CAAA,OAAA,CAAA,UAAA,CAAA,EAAA;AACA,YAAA,CAAA,GAAA,OAAA,CAAA,UAAA,CAAA,IAAA,CAAA,UAAA,CAAA;AAAA,iBAAA,CAAA,CAAA,KAAA,IAAA,CAAA;AAAA,SAAA,EAAA,KAAA;;AACA,QAAA,UAAA,CAAA,IAAA,CAAA,CAAA;AACA,OAHA;AAKA,MAAA,IAAA,CAAA,OAAA,CAAA,OAAA,CAAA,UAAA,CAAA,EAAA;AACA,YAAA,CAAA,GAAA,OAAA,CAAA,eAAA,CAAA,IAAA,CAAA,UAAA,CAAA;AAAA,iBAAA,CAAA,CAAA,GAAA,IAAA,CAAA;AAAA,SAAA,EAAA,KAAA;;AACA,QAAA,cAAA,CAAA,IAAA,CAAA,CAAA;AACA,OAHA;AAKA,WAAA,aAAA,GAAA,KAAA,YAAA,CAAA,IAAA,CACA,UAAA,CAAA;AAAA,eAAA,CAAA,CAAA,KAAA,IAAA,IAAA,CAAA,QAAA;AAAA,OADA,EAEA,KAFA;AAGA,WAAA,oBAAA,GACA,IAAA,CAAA,eAAA,IAAA,EAAA,GACA,EADA,GAEA,KAAA,YAAA,CAAA,IAAA,CAAA,UAAA,CAAA;AAAA,eAAA,CAAA,CAAA,KAAA,IAAA,IAAA,CAAA,eAAA;AAAA,OAAA,EAAA,KAHA;AAIA,WAAA,iBAAA,GACA,IAAA,CAAA,YAAA,IAAA,EAAA,GACA,EADA,GAEA,KAAA,gBAAA,CAAA,IAAA,CAAA,UAAA,CAAA;AAAA,eAAA,CAAA,CAAA,KAAA,IAAA,IAAA,CAAA,YAAA;AAAA,OAAA,EAAA,KAHA;AAIA,WAAA,eAAA,GAAA,KAAA,UAAA,CAAA,IAAA,CACA,UAAA,CAAA;AAAA,eAAA,CAAA,CAAA,KAAA,IAAA,IAAA,CAAA,UAAA;AAAA,OADA,EAEA,KAFA;AAIA,WAAA,OAAA,GAAA,UAAA,CAAA,IAAA,CAAA,IAAA,CAAA;AACA,WAAA,WAAA,GAAA,cAAA,CAAA,IAAA,CAAA,IAAA,CAAA;AACA,WAAA,aAAA,GACA,YAAA,CAAA,OAAA,CAAA,gBAAA,KAAA,MAAA,GACA,KAAA,EAAA,CAAA,qBAAA,CADA,GAEA,KAAA,EAAA,CAAA,qBAAA,CAHA;AAIA,WAAA,UAAA,GACA,YAAA,CAAA,OAAA,CAAA,YAAA,KAAA,MAAA,GACA,KAAA,EAAA,CAAA,qBAAA,CADA,GAEA,KAAA,EAAA,CAAA,qBAAA,CAHA;AAIA,WAAA,IAAA,CAAA,OAAA,CAAA,UAAA,CAAA,EAAA;AACA,gBAAA,CAAA,CAAA,OAAA;AACA,eAAA,CAAA;AACA,YAAA,CAAA,CAAA,KAAA,GAAA,IAAA,CAAA,UAAA;AACA;;AACA,eAAA,CAAA;AACA,YAAA,CAAA,CAAA,KAAA,GAAA,OAAA,CAAA,OAAA;AACA;;AACA,eAAA,CAAA;AACA,YAAA,CAAA,CAAA,KAAA,GAAA,OAAA,CAAA,aAAA;AACA,YAAA,CAAA,CAAA,SAAA,GAAA,IAAA,CAAA,WAAA;AACA;;AACA,eAAA,CAAA;AACA,YAAA,CAAA,CAAA,KAAA,GAAA,OAAA,CAAA,UAAA;AACA;;AACA,eAAA,CAAA;AACA,YAAA,CAAA,CAAA,KAAA,GAAA,IAAA,CAAA,QAAA;AACA;;AACA,eAAA,CAAA;AACA,YAAA,CAAA,CAAA,KAAA,GAAA,IAAA,CAAA,UAAA;AACA;;AACA,eAAA,CAAA;AACA,YAAA,CAAA,CAAA,KAAA,GAAA,IAAA,CAAA,aAAA;AACA;;AACA,eAAA,CAAA;AACA,YAAA,CAAA,CAAA,KAAA,GAAA,IAAA,CAAA,QAAA,GAAA,CAAA;AACA;;AACA,eAAA,CAAA;AACA,YAAA,CAAA,CAAA,KAAA,GAAA,OAAA,CAAA,UAAA;AACA;;AACA,eAAA,CAAA;AACA,YAAA,CAAA,CAAA,KAAA,GACA,IAAA,CAAA,cAAA,IAAA,IAAA,GACA,OAAA,CAAA,EAAA,CAAA,qBAAA,CADA,GAEA,OAAA,CAAA,EAAA,CAAA,qBAAA,CAHA;AAIA,YAAA,CAAA,CAAA,OAAA,GAAA,IAAA,CAAA,cAAA,IAAA,IAAA,GAAA,IAAA,GAAA,KAAA;AACA;;AACA,eAAA,EAAA;AACA,YAAA,CAAA,CAAA,KAAA,GAAA,IAAA,CAAA,aAAA;AACA;;AACA,eAAA,EAAA;AACA,YAAA,CAAA,CAAA,KAAA,GAAA,IAAA,CAAA,WAAA;AACA;;AACA,eAAA,EAAA;AACA,YAAA,CAAA,CAAA,KAAA,GAAA,IAAA,CAAA,UAAA;AACA,YAAA,CAAA,CAAA,YAAA,GAAA,OAAA,CAAA,aAAA,CACA,IAAA,CAAA,iBADA,EAEA,YAFA,CAAA;AAIA;;AACA,eAAA,EAAA;AACA,YAAA,CAAA,CAAA,KAAA,GAAA,IAAA,CAAA,WAAA;AACA,YAAA,CAAA,CAAA,YAAA,GAAA,OAAA,CAAA,aAAA,CACA,IAAA,CAAA,kBADA,EAEA,aAFA,CAAA;AAIA;;AACA,eAAA,EAAA;AACA,YAAA,CAAA,CAAA,KAAA,GAAA,IAAA,CAAA,UAAA;AACA,YAAA,CAAA,CAAA,YAAA,GAAA,OAAA,CAAA,aAAA,CACA,IAAA,CAAA,iBADA,EAEA,YAFA,CAAA;AAIA;;AACA,eAAA,EAAA;AACA,YAAA,CAAA,CAAA,KAAA,GAAA,IAAA,CAAA,SAAA;AACA,YAAA,CAAA,CAAA,cAAA,GAAA,OAAA,CAAA,aAAA,CAAA,IAAA,CAAA,SAAA,EAAA,WAAA,CAAA;AACA;;AACA,eAAA,EAAA;AACA,YAAA,CAAA,CAAA,KAAA,GAAA,IAAA,CAAA,cAAA;AACA,YAAA,CAAA,CAAA,YAAA,GAAA,OAAA,CAAA,aAAA,CACA,IAAA,CAAA,eADA,EAEA,gBAFA,CAAA;AAIA;;AACA,eAAA,EAAA;AACA,YAAA,CAAA,CAAA,KAAA,GAAA,IAAA,CAAA,gBAAA;AACA,YAAA,CAAA,CAAA,qBAAA,GAAA,OAAA,CAAA,aAAA,CACA,IAAA,CAAA,gBADA,EAEA,kBAFA,CAAA;AAIA;;AACA,eAAA,EAAA;AACA,YAAA,CAAA,CAAA,KAAA,GAAA,OAAA,CAAA,QAAA;AACA;;AACA,eAAA,EAAA;AACA,YAAA,CAAA,CAAA,eAAA,GACA,IAAA,CAAA,WAAA,IAAA,IAAA,GACA,OAAA,CAAA,EAAA,CAAA,qBAAA,CADA,GAEA,OAAA,CAAA,EAAA,CAAA,qBAAA,CAHA;AAIA,YAAA,CAAA,CAAA,WAAA,GAAA,IAAA,CAAA,WAAA,IAAA,IAAA,GAAA,IAAA,GAAA,KAAA;AACA,YAAA,CAAA,CAAA,KAAA,GAAA,IAAA,CAAA,cAAA;AACA,YAAA,CAAA,CAAA,oBAAA,GAAA,OAAA,CAAA,kBAAA,CACA,IAAA,CAAA,cADA,CAAA;AAGA;;AACA,eAAA,EAAA;AACA,YAAA,CAAA,CAAA,KAAA,GAAA,IAAA,CAAA,WAAA;AACA,YAAA,CAAA,CAAA,oBAAA,GAAA,OAAA,CAAA,aAAA,CACA,IAAA,CAAA,WADA,EAEA,aAFA,CAAA;AAIA;;AACA,eAAA,EAAA;AACA,YAAA,CAAA,CAAA,KAAA,GAAA,IAAA,CAAA,eAAA;AACA;;AACA,eAAA,EAAA;AACA,YAAA,CAAA,CAAA,KAAA,GAAA,IAAA,CAAA,YAAA;AACA;;AACA,eAAA,EAAA;AACA,YAAA,CAAA,CAAA,KAAA,GAAA,OAAA,CAAA,WAAA;AACA;;AACA,eAAA,EAAA;AACA,YAAA,CAAA,CAAA,KAAA,GACA,IAAA,CAAA,YAAA,IAAA,IAAA,GACA,OAAA,CAAA,EAAA,CAAA,qBAAA,CADA,GAEA,OAAA,CAAA,EAAA,CAAA,qBAAA,CAHA;AAIA;;AACA,eAAA,EAAA;AACA,YAAA,CAAA,CAAA,KAAA,GAAA,IAAA,CAAA,WAAA;AACA;;AACA,eAAA,EAAA;AACA,YAAA,CAAA,CAAA,KAAA,GAAA,IAAA,CAAA,WAAA;AACA;;AACA,eAAA,EAAA;AACA,YAAA,CAAA,CAAA,KAAA,GAAA,IAAA,CAAA,WAAA;AACA;;AACA,eAAA,EAAA;AACA,YAAA,CAAA,CAAA,KAAA,GAAA,IAAA,CAAA,WAAA;AACA;;AACA,eAAA,EAAA;AACA,YAAA,CAAA,CAAA,KAAA,GAAA,IAAA,CAAA,WAAA;AACA;;AACA,eAAA,EAAA;AACA,YAAA,CAAA,CAAA,cAAA,GACA,IAAA,CAAA,UAAA,IAAA,IAAA,GACA,OAAA,CAAA,EAAA,CAAA,qBAAA,CADA,GAEA,OAAA,CAAA,EAAA,CAAA,qBAAA,CAHA;AAIA,YAAA,CAAA,CAAA,UAAA,GAAA,IAAA,CAAA,UAAA,IAAA,IAAA,GAAA,IAAA,GAAA,KAAA;AACA;;AACA,eAAA,EAAA;AACA,YAAA,CAAA,CAAA,KAAA,GAAA,IAAA,CAAA,UAAA;AACA;;AACA,eAAA,EAAA;AACA,YAAA,CAAA,CAAA,KAAA,GAAA,IAAA,CAAA,UAAA;AACA;AAhJA;AAkJA,OAnJA;AAoJA,KAv2DA;AAw2DA,IAAA,aAx2DA,yBAw2DA,WAx2DA,EAw2DA,IAx2DA,EAw2DA;AAAA;;AACA,UAAA,QAAA,GAAA,EAAA;AACA,UAAA,UAAA,GAAA,EAAA;;AACA,UAAA,WAAA,IAAA,WAAA,CAAA,MAAA,GAAA,CAAA,EAAA;AACA,YAAA,WAAA,CAAA,MAAA,IAAA,CAAA,IAAA,WAAA,CAAA,CAAA,CAAA,KAAA,EAAA,EAAA;AACA,iBAAA,EAAA;AACA;;AACA,gBAAA,IAAA;AACA,eAAA,YAAA;AACA,gBAAA;AACA,cAAA,WAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,gBAAA,UAAA,GAAA,IAAA;;AACA,oBAAA,CAAA,GAAA,OAAA,CAAA,oBAAA,CAAA,IAAA,CAAA,UAAA,IAAA;AAAA,yBAAA,IAAA,CAAA,GAAA,IAAA,IAAA;AAAA,iBAAA,EACA,KADA;;AAEA,gBAAA,QAAA,CAAA,IAAA,CAAA,CAAA;AACA,eALA;AAMA,aAPA,CAOA,OAAA,CAAA,EAAA;AACA,cAAA,QAAA,CAAA,IAAA,CAAA,UAAA;AACA;;AAEA;;AACA,eAAA,aAAA;AACA,gBAAA;AACA,cAAA,WAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,gBAAA,UAAA,GAAA,IAAA;;AACA,oBAAA,CAAA,GAAA,OAAA,CAAA,qBAAA,CAAA,IAAA,CACA,UAAA,IAAA;AAAA,yBAAA,IAAA,CAAA,GAAA,IAAA,IAAA;AAAA,iBADA,EAEA,KAFA;;AAGA,gBAAA,QAAA,CAAA,IAAA,CAAA,CAAA;AACA,eANA;AAOA,aARA,CAQA,OAAA,CAAA,EAAA;AACA,cAAA,QAAA,CAAA,IAAA,CAAA,UAAA;AACA;;AAEA;;AACA,eAAA,YAAA;AACA,gBAAA;AACA,cAAA,WAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,gBAAA,UAAA,GAAA,IAAA;;AACA,oBAAA,CAAA,GAAA,OAAA,CAAA,oBAAA,CAAA,IAAA,CAAA,UAAA,IAAA;AAAA,yBAAA,IAAA,CAAA,GAAA,IAAA,IAAA;AAAA,iBAAA,EACA,KADA;;AAEA,gBAAA,QAAA,CAAA,IAAA,CAAA,CAAA;AACA,eALA;AAMA,aAPA,CAOA,OAAA,CAAA,EAAA;AACA,cAAA,QAAA,CAAA,IAAA,CAAA,UAAA;AACA;;AACA;;AACA,eAAA,gBAAA;AACA,gBAAA;AACA,cAAA,WAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,gBAAA,UAAA,GAAA,IAAA;;AACA,oBAAA,CAAA,GAAA,OAAA,CAAA,wBAAA,CAAA,IAAA,CACA,UAAA,IAAA;AAAA,yBAAA,IAAA,CAAA,GAAA,IAAA,IAAA;AAAA,iBADA,EAEA,KAFA;;AAGA,gBAAA,QAAA,CAAA,IAAA,CAAA,CAAA;AACA,eANA;AAOA,aARA,CAQA,OAAA,CAAA,EAAA;AACA,cAAA,QAAA,CAAA,IAAA,CAAA,UAAA;AACA;;AACA;;AACA,eAAA,WAAA;AACA,YAAA,WAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,kBAAA,CAAA,GAAA,OAAA,CAAA,oBAAA,CAAA,IAAA,CAAA,UAAA,IAAA;AAAA,uBAAA,IAAA,CAAA,GAAA,IAAA,IAAA;AAAA,eAAA,EACA,KADA;;AAEA,cAAA,QAAA,CAAA,IAAA,CAAA,CAAA;AACA,aAJA;AAKA;;AACA,eAAA,kBAAA;AACA,gBAAA;AACA,cAAA,WAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,gBAAA,UAAA,GAAA,IAAA;;AACA,oBAAA,CAAA,GAAA,OAAA,CAAA,sBAAA,CAAA,IAAA,CACA,UAAA,IAAA;AAAA,yBAAA,IAAA,CAAA,GAAA,IAAA,IAAA;AAAA,iBADA,EAEA,KAFA;;AAGA,gBAAA,QAAA,CAAA,IAAA,CAAA,CAAA;AACA,eANA;AAOA,aARA,CAQA,OAAA,CAAA,EAAA;AACA,cAAA,QAAA,CAAA,IAAA,CAAA,UAAA;AACA;;AACA;;AACA,eAAA,aAAA;AACA,gBAAA;AACA,cAAA,WAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,gBAAA,UAAA,GAAA,IAAA;;AACA,oBAAA,CAAA,GAAA,OAAA,CAAA,sBAAA,CAAA,IAAA,CACA,UAAA,IAAA;AAAA,yBAAA,IAAA,CAAA,GAAA,IAAA,IAAA;AAAA,iBADA,EAEA,KAFA;;AAGA,gBAAA,QAAA,CAAA,IAAA,CAAA,CAAA;AACA,eANA;AAOA,aARA,CAQA,OAAA,CAAA,EAAA;AACA,cAAA,QAAA,CAAA,IAAA,CAAA,UAAA;AACA;;AACA;AArFA;;AAuFA,eAAA,QAAA,CAAA,IAAA,CAAA,IAAA,CAAA;AACA;AACA,KAx8DA;AAy8DA,IAAA,kBAz8DA,8BAy8DA,UAz8DA,EAy8DA;AAAA;;AACA,UAAA,QAAA,GAAA,EAAA;;AACA,UAAA,UAAA,CAAA,MAAA,IAAA,CAAA,IAAA,UAAA,CAAA,CAAA,CAAA,KAAA,EAAA,EAAA;AACA,eAAA,EAAA;AACA;;AACA,UAAA,UAAA,IAAA,UAAA,CAAA,MAAA,GAAA,CAAA,IAAA,KAAA,SAAA,CAAA,MAAA,GAAA,CAAA,EAAA;AACA,QAAA,UAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,cAAA,CAAA,GAAA,OAAA,CAAA,SAAA,CAAA,IAAA,CAAA,UAAA,IAAA;AAAA,mBAAA,IAAA,CAAA,KAAA,IAAA,IAAA;AAAA,WAAA,EAAA,KAAA;;AACA,UAAA,QAAA,CAAA,IAAA,CAAA,CAAA;AACA,SAHA;AAIA,eAAA,QAAA,CAAA,IAAA,CAAA,IAAA,CAAA;AACA,OANA,MAMA;AACA,eAAA,EAAA;AACA;AACA,KAv9DA;AAw9DA,IAAA,UAx9DA,wBAw9DA;AAAA;;AACA,UAAA,MAAA,GAAA;AACA,QAAA,WAAA,EAAA,IADA;AAEA,QAAA,MAAA,EAAA;AAFA,OAAA;AAKA,WAAA,QAAA,CAAA,gBAAA,CAAA,IAAA,CAAA,MAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,YAAA,GAAA,CAAA,KAAA,GAAA,CAAA,EAAA;AACA,UAAA,GAAA,CAAA,OAAA,CAAA,CAAA,EAAA,UAAA,CAAA,OAAA,CAAA,UAAA,GAAA,EAAA;AACA,oBAAA,GAAA,CAAA,GAAA;AACA,mBAAA,yBAAA;AACA,gBAAA,OAAA,CAAA,YAAA,CAAA,CAAA,EAAA,aAAA,GAAA,GAAA,CAAA,KAAA;AACA;;AACA,mBAAA,wBAAA;AACA,gBAAA,OAAA,CAAA,YAAA,CAAA,CAAA,EAAA,YAAA,GAAA,GAAA,CAAA,KAAA;AACA;;AACA,mBAAA,0BAAA;AACA,gBAAA,OAAA,CAAA,YAAA,CAAA,CAAA,EAAA,cAAA,GAAA,GAAA,CAAA,KAAA;AACA;;AACA,mBAAA,yBAAA;AACA,gBAAA,OAAA,CAAA,YAAA,CAAA,CAAA,EAAA,aAAA,GAAA,GAAA,CAAA,KAAA;AACA;;AACA,mBAAA,2BAAA;AACA,gBAAA,OAAA,CAAA,YAAA,CAAA,CAAA,EAAA,eAAA,GAAA,GAAA,CAAA,KAAA;AACA;;AACA,mBAAA,0BAAA;AACA,gBAAA,OAAA,CAAA,YAAA,CAAA,CAAA,EAAA,cAAA,GAAA,GAAA,CAAA,KAAA;AACA;;AACA,mBAAA,sBAAA;AACA,gBAAA,OAAA,CAAA,YAAA,CAAA,CAAA,EAAA,aAAA,GAAA,GAAA,CAAA,KAAA;AACA;;AACA,mBAAA,qBAAA;AACA,gBAAA,OAAA,CAAA,YAAA,CAAA,CAAA,EAAA,YAAA,GAAA,GAAA,CAAA,KAAA;AACA;;AACA,mBAAA,uBAAA;AACA,gBAAA,OAAA,CAAA,YAAA,CAAA,CAAA,EAAA,cAAA,GAAA,GAAA,CAAA,KAAA;AACA;;AACA,mBAAA,sBAAA;AACA,gBAAA,OAAA,CAAA,YAAA,CAAA,CAAA,EAAA,aAAA,GAAA,GAAA,CAAA,KAAA;AACA;;AACA,mBAAA,wBAAA;AACA,gBAAA,OAAA,CAAA,YAAA,CAAA,CAAA,EAAA,eAAA,GAAA,GAAA,CAAA,KAAA;AACA;;AACA,mBAAA,uBAAA;AACA,gBAAA,OAAA,CAAA,YAAA,CAAA,CAAA,EAAA,cAAA,GAAA,GAAA,CAAA,KAAA;AACA;;AACA,mBAAA,qBAAA;AACA,gBAAA,OAAA,CAAA,YAAA,CAAA,CAAA,EAAA,aAAA,GAAA,GAAA,CAAA,KAAA;AACA;;AACA,mBAAA,oBAAA;AACA,gBAAA,OAAA,CAAA,YAAA,CAAA,CAAA,EAAA,YAAA,GAAA,GAAA,CAAA,KAAA;AACA;;AACA,mBAAA,sBAAA;AACA,gBAAA,OAAA,CAAA,YAAA,CAAA,CAAA,EAAA,cAAA,GAAA,GAAA,CAAA,KAAA;AACA;;AACA,mBAAA,qBAAA;AACA,gBAAA,OAAA,CAAA,YAAA,CAAA,CAAA,EAAA,aAAA,GAAA,GAAA,CAAA,KAAA;AACA;;AACA,mBAAA,uBAAA;AACA,gBAAA,OAAA,CAAA,YAAA,CAAA,CAAA,EAAA,eAAA,GAAA,GAAA,CAAA,KAAA;AACA;;AACA,mBAAA,sBAAA;AACA,gBAAA,OAAA,CAAA,YAAA,CAAA,CAAA,EAAA,cAAA,GAAA,GAAA,CAAA,KAAA;AACA;;AACA,mBAAA,qBAAA;AACA,gBAAA,OAAA,CAAA,YAAA,CAAA,CAAA,EAAA,aAAA,GAAA,GAAA,CAAA,KAAA;AACA;;AACA,mBAAA,oBAAA;AACA,gBAAA,OAAA,CAAA,YAAA,CAAA,CAAA,EAAA,YAAA,GAAA,GAAA,CAAA,KAAA;AACA;;AACA,mBAAA,sBAAA;AACA,gBAAA,OAAA,CAAA,YAAA,CAAA,CAAA,EAAA,cAAA,GAAA,GAAA,CAAA,KAAA;AACA;;AACA,mBAAA,qBAAA;AACA,gBAAA,OAAA,CAAA,YAAA,CAAA,CAAA,EAAA,aAAA,GAAA,GAAA,CAAA,KAAA;AACA;;AACA,mBAAA,uBAAA;AACA,gBAAA,OAAA,CAAA,YAAA,CAAA,CAAA,EAAA,eAAA,GAAA,GAAA,CAAA,KAAA;AACA;;AACA,mBAAA,sBAAA;AACA,gBAAA,OAAA,CAAA,YAAA,CAAA,CAAA,EAAA,cAAA,GAAA,GAAA,CAAA,KAAA;AACA;AAxEA;AA0EA,WA3EA;AA4EA;;AACA,QAAA,OAAA,CAAA,gBAAA,GAAA,IAAA,CAAA,KAAA,CAAA,IAAA,CAAA,SAAA,CAAA,OAAA,CAAA,YAAA,CAAA,CAAA;AACA,OAhFA;AAiFA,KA/iEA;AAgjEA,IAAA,cAhjEA,4BAgjEA;AAAA;;AACA,UAAA,MAAA,GAAA;AACA,QAAA,WAAA,EAAA,IADA;AAEA,QAAA,MAAA,EAAA;AAFA,OAAA;AAKA,WAAA,QAAA,CAAA,gBAAA,CAAA,IAAA,CAAA,MAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,YAAA,GAAA,CAAA,KAAA,GAAA,CAAA,EAAA;AACA,UAAA,GAAA,CAAA,OAAA,CAAA,CAAA,EAAA,UAAA,CAAA,OAAA,CAAA,UAAA,GAAA,EAAA;AACA,oBAAA,GAAA,CAAA,GAAA;AACA;AACA,mBAAA,cAAA;AACA,gBAAA,OAAA,CAAA,gBAAA,CAAA,CAAA,EAAA,OAAA,GACA,GAAA,CAAA,KAAA,IAAA,MAAA,GAAA,IAAA,GAAA,KADA;AAEA;;AACA,mBAAA,mBAAA;AACA,gBAAA,OAAA,CAAA,gBAAA,CAAA,CAAA,EAAA,YAAA,GAAA,GAAA,CAAA,KAAA,GAAA,CAAA;AACA;;AACA,mBAAA,mBAAA;AACA,gBAAA,OAAA,CAAA,gBAAA,CAAA,CAAA,EAAA,YAAA,GAAA,GAAA,CAAA,KAAA,GAAA,CAAA;AACA;;AACA,mBAAA,sBAAA;AACA,gBAAA,OAAA,CAAA,gBAAA,CAAA,CAAA,EAAA,eAAA,GAAA,GAAA,CAAA,KAAA;AACA;;AACA,mBAAA,cAAA;AACA,gBAAA,OAAA,CAAA,gBAAA,CAAA,CAAA,EAAA,OAAA,GAAA,GAAA,CAAA,KAAA;AACA;;AACA,mBAAA,eAAA;AACA,gBAAA,OAAA,CAAA,gBAAA,CAAA,CAAA,EAAA,QAAA,GAAA,GAAA,CAAA,KAAA;AACA;;AACA,mBAAA,eAAA;AACA,gBAAA,OAAA,CAAA,gBAAA,CAAA,CAAA,EAAA,QAAA,GAAA,GAAA,CAAA,KAAA,GAAA,CAAA;AACA;;AACA,mBAAA,sBAAA;AACA,gBAAA,OAAA,CAAA,gBAAA,CAAA,CAAA,EAAA,eAAA,GAAA,GAAA,CAAA,KAAA,GAAA,CAAA;AACA;AAEA;;AACA,mBAAA,eAAA;AACA,gBAAA,OAAA,CAAA,gBAAA,CAAA,CAAA,EAAA,OAAA,GACA,GAAA,CAAA,KAAA,IAAA,MAAA,GAAA,IAAA,GAAA,KADA;AAEA;;AACA,mBAAA,oBAAA;AACA,gBAAA,OAAA,CAAA,gBAAA,CAAA,CAAA,EAAA,YAAA,GAAA,GAAA,CAAA,KAAA,GAAA,CAAA;AACA;;AACA,mBAAA,oBAAA;AACA,gBAAA,OAAA,CAAA,gBAAA,CAAA,CAAA,EAAA,YAAA,GAAA,GAAA,CAAA,KAAA,GAAA,CAAA;AACA;;AACA,mBAAA,uBAAA;AACA,gBAAA,OAAA,CAAA,gBAAA,CAAA,CAAA,EAAA,eAAA,GAAA,GAAA,CAAA,KAAA;AACA;;AACA,mBAAA,eAAA;AACA,gBAAA,OAAA,CAAA,gBAAA,CAAA,CAAA,EAAA,OAAA,GAAA,GAAA,CAAA,KAAA;AACA;;AACA,mBAAA,gBAAA;AACA,gBAAA,OAAA,CAAA,gBAAA,CAAA,CAAA,EAAA,QAAA,GAAA,GAAA,CAAA,KAAA;AACA;;AACA,mBAAA,gBAAA;AACA,gBAAA,OAAA,CAAA,gBAAA,CAAA,CAAA,EAAA,QAAA,GAAA,GAAA,CAAA,KAAA,GAAA,CAAA;AACA;;AACA,mBAAA,uBAAA;AACA,gBAAA,OAAA,CAAA,gBAAA,CAAA,CAAA,EAAA,eAAA,GAAA,GAAA,CAAA,KAAA,GAAA,CAAA;AACA;AAEA;;AACA,mBAAA,eAAA;AACA,gBAAA,OAAA,CAAA,gBAAA,CAAA,CAAA,EAAA,OAAA,GACA,GAAA,CAAA,KAAA,IAAA,MAAA,GAAA,IAAA,GAAA,KADA;AAEA;;AACA,mBAAA,oBAAA;AACA,gBAAA,OAAA,CAAA,gBAAA,CAAA,CAAA,EAAA,YAAA,GAAA,GAAA,CAAA,KAAA,GAAA,CAAA;AACA;;AACA,mBAAA,oBAAA;AACA,gBAAA,OAAA,CAAA,gBAAA,CAAA,CAAA,EAAA,YAAA,GAAA,GAAA,CAAA,KAAA,GAAA,CAAA;AACA;;AACA,mBAAA,uBAAA;AACA,gBAAA,OAAA,CAAA,gBAAA,CAAA,CAAA,EAAA,eAAA,GAAA,GAAA,CAAA,KAAA;AACA;;AACA,mBAAA,eAAA;AACA,gBAAA,OAAA,CAAA,gBAAA,CAAA,CAAA,EAAA,OAAA,GAAA,GAAA,CAAA,KAAA;AACA;;AACA,mBAAA,gBAAA;AACA,gBAAA,OAAA,CAAA,gBAAA,CAAA,CAAA,EAAA,QAAA,GAAA,GAAA,CAAA,KAAA;AACA;;AACA,mBAAA,gBAAA;AACA,gBAAA,OAAA,CAAA,gBAAA,CAAA,CAAA,EAAA,QAAA,GAAA,GAAA,CAAA,KAAA,GAAA,CAAA;AACA;;AACA,mBAAA,uBAAA;AACA,gBAAA,OAAA,CAAA,gBAAA,CAAA,CAAA,EAAA,eAAA,GAAA,GAAA,CAAA,KAAA,GAAA,CAAA;AACA;AAEA;;AACA,mBAAA,cAAA;AACA,gBAAA,OAAA,CAAA,gBAAA,CAAA,CAAA,EAAA,OAAA,GACA,GAAA,CAAA,KAAA,IAAA,MAAA,GAAA,IAAA,GAAA,KADA;AAEA;;AACA,mBAAA,mBAAA;AACA,gBAAA,OAAA,CAAA,gBAAA,CAAA,CAAA,EAAA,YAAA,GAAA,GAAA,CAAA,KAAA,GAAA,CAAA;AACA;;AACA,mBAAA,mBAAA;AACA,gBAAA,OAAA,CAAA,gBAAA,CAAA,CAAA,EAAA,YAAA,GAAA,GAAA,CAAA,KAAA,GAAA,CAAA;AACA;;AACA,mBAAA,sBAAA;AACA,gBAAA,OAAA,CAAA,gBAAA,CAAA,CAAA,EAAA,eAAA,GAAA,GAAA,CAAA,KAAA;AACA;;AACA,mBAAA,cAAA;AACA,gBAAA,OAAA,CAAA,gBAAA,CAAA,CAAA,EAAA,OAAA,GAAA,GAAA,CAAA,KAAA;AACA;;AACA,mBAAA,eAAA;AACA,gBAAA,OAAA,CAAA,gBAAA,CAAA,CAAA,EAAA,QAAA,GAAA,GAAA,CAAA,KAAA;AACA;;AACA,mBAAA,eAAA;AACA,gBAAA,OAAA,CAAA,gBAAA,CAAA,CAAA,EAAA,QAAA,GAAA,GAAA,CAAA,KAAA,GAAA,CAAA;AACA;;AACA,mBAAA,sBAAA;AACA,gBAAA,OAAA,CAAA,gBAAA,CAAA,CAAA,EAAA,eAAA,GAAA,GAAA,CAAA,KAAA,GAAA,CAAA;AACA;AA3GA;AA6GA,WA9GA;AA+GA;;AACA,QAAA,OAAA,CAAA,gBAAA,GAAA,IAAA,CAAA,KAAA,CAAA,IAAA,CAAA,SAAA,CAAA,OAAA,CAAA,YAAA,CAAA,CAAA;AACA,OAnHA;AAoHA,KA1qEA;AA2qEA,IAAA,qBA3qEA,mCA2qEA;AAAA;;AACA,UAAA,MAAA,GAAA;AACA,QAAA,WAAA,EAAA,IADA;AAEA,QAAA,MAAA,EAAA;AAFA,OAAA;AAIA,WAAA,QAAA,CAAA,gBAAA,CAAA,IAAA,CAAA,MAAA,EAAA,IAAA,CAAA,UAAA,OAAA,EAAA;AACA,YAAA,OAAA,CAAA,KAAA,GAAA,CAAA,EAAA;AACA,UAAA,OAAA,CAAA,OAAA,CAAA,CAAA,EAAA,UAAA,CAAA,OAAA,CAAA,UAAA,GAAA,EAAA;AACA,gBAAA,GAAA,GAAA,GAAA,CAAA,GAAA,CAAA,KAAA,CAAA,GAAA,EAAA,CAAA,CAAA;AACA,gBAAA,IAAA,GAAA,GAAA,CAAA,GAAA,CAAA,KAAA,CAAA,GAAA,EAAA,CAAA,CAAA,CAFA,CAGA;AACA;;AAEA,YAAA,OAAA,CAAA,uBAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,kBAAA,IAAA,CAAA,KAAA,IAAA,GAAA,EAAA;AACA,oBAAA,CAAA,KAAA,CAAA,QAAA,CAAA,GAAA,CAAA,KAAA,CAAA,CAAA,EAAA;AACA,kBAAA,IAAA,CAAA,IAAA,CAAA,GAAA,GAAA,CAAA,KAAA,GAAA,CAAA;AACA,iBAFA,MAEA,IAAA,GAAA,CAAA,KAAA,IAAA,MAAA,IAAA,GAAA,IAAA,OAAA,EAAA;AACA,kBAAA,IAAA,CAAA,IAAA,CAAA,GACA,GAAA,CAAA,KAAA,IAAA,MAAA,GACA,IADA,GAEA,GAAA,CAAA,KAAA,IAAA,OAAA,GACA,KADA,GAEA,KALA;AAMA,iBAPA,MAOA;AACA,kBAAA,IAAA,CAAA,IAAA,CAAA,GAAA,GAAA,CAAA,KAAA;AACA;AACA;AACA,aAfA;AAgBA,WAtBA;AAuBA;AACA,OA1BA;AA2BA,KA3sEA;AA4sEA,IAAA,aA5sEA,yBA4sEA,IA5sEA,EA4sEA;AACA,UAAA,IAAA,IAAA,SAAA,EAAA;AACA,YAAA,QAAA,GAAA,KAAA,cAAA,CAAA,IAAA,CAAA,UAAA,CAAA;AAAA,iBAAA,CAAA,CAAA,IAAA,IAAA,IAAA;AAAA,SAAA,EAAA,IAAA;AACA,eAAA,QAAA;AACA,OAHA,MAGA;AACA,eAAA,EAAA;AACA;AACA,KAntEA;AAotEA,IAAA,aAptEA,yBAotEA,IAptEA,EAotEA;AACA,UAAA,IAAA,IAAA,SAAA,EAAA;AACA,YAAA,QAAA,GAAA,KAAA,gBAAA,CAAA,IAAA,CAAA,UAAA,CAAA;AAAA,iBAAA,CAAA,CAAA,IAAA,IAAA,IAAA;AAAA,SAAA,EAAA,IAAA;AACA,eAAA,QAAA;AACA,OAHA,MAGA;AACA,eAAA,EAAA;AACA;AACA,KA3tEA;AA4tEA,IAAA,gBA5tEA,4BA4tEA,IA5tEA,EA4tEA,GA5tEA,EA4tEA,KA5tEA,EA4tEA;AACA,cAAA,IAAA;AACA,aAAA,eAAA;AACA,eAAA,gBAAA,CAAA,KAAA,EAAA,aAAA,GAAA,GAAA,CAAA,aAAA;AACA;;AACA,aAAA,cAAA;AACA,eAAA,gBAAA,CAAA,KAAA,EAAA,YAAA,GAAA,GAAA,CAAA,YAAA;AACA;;AACA,aAAA,gBAAA;AACA,eAAA,gBAAA,CAAA,KAAA,EAAA,cAAA,GAAA,GAAA,CAAA,cAAA;AACA;;AACA,aAAA,eAAA;AACA,eAAA,gBAAA,CAAA,KAAA,EAAA,aAAA,GAAA,GAAA,CAAA,aAAA;AACA;;AACA,aAAA,iBAAA;AACA,eAAA,gBAAA,CAAA,KAAA,EAAA,eAAA,GAAA,GAAA,CAAA,eAAA;AACA;;AACA,aAAA,gBAAA;AACA,eAAA,gBAAA,CAAA,KAAA,EAAA,cAAA,GAAA,GAAA,CAAA,cAAA;AACA;AAlBA;AAoBA,KAjvEA;AAkvEA,IAAA,eAlvEA,6BAkvEA;AACA,MAAA,UAAA,CACA,YAAA;AACA,YAAA,KAAA,GAAA,QAAA,CAAA,aAAA,CAAA,0BAAA,CAAA;;AACA,YAAA,KAAA,EAAA;AACA,UAAA,KAAA,CAAA,KAAA,CAAA,MAAA,GAAA,MAAA,CAAA,WAAA,GAAA,GAAA,GAAA,IAAA;AACA;;AACA,YAAA,MAAA,GAAA,QAAA,CAAA,aAAA,CAAA,mBAAA,CAAA;;AACA,YAAA,MAAA,EAAA;AACA,UAAA,MAAA,CAAA,KAAA,CAAA,MAAA,GAAA,MAAA;AACA;AACA,OATA,CASA,IATA,CASA,IATA,CADA,EAWA,EAXA,CAAA;AAaA,KAhwEA;AAiwEA,IAAA,WAjwEA,uBAiwEA,QAjwEA,EAiwEA;AACA,UAAA,SAAA,GAAA,CAAA;;AACA,UAAA,QAAA,CAAA,CAAA,CAAA,KAAA,GAAA,EAAA;AACA,QAAA,SAAA,GAAA,CAAA,CAAA;AACA,QAAA,QAAA,GAAA,QAAA,CAAA,MAAA,CAAA,CAAA,CAAA;AACA;;AACA,aAAA,UAAA,CAAA,EAAA,CAAA,EAAA;AACA,YAAA,MAAA,GACA,CAAA,CAAA,QAAA,CAAA,GAAA,CAAA,CAAA,QAAA,CAAA,GAAA,CAAA,CAAA,GAAA,CAAA,CAAA,QAAA,CAAA,GAAA,CAAA,CAAA,QAAA,CAAA,GAAA,CAAA,GAAA,CADA;AAEA,eAAA,MAAA,GAAA,SAAA;AACA,OAJA;AAKA,KA5wEA;AA6wEA,IAAA,IA7wEA,kBA6wEA;AACA,WAAA,MAAA,GAAA,IAAA;AACA,WAAA,IAAA,CAAA,MAAA;AACA,WAAA,QAAA;AACA,WAAA,SAAA,GAJA,CAKA;AACA,KAnxEA;AAoxEA,IAAA,UApxEA,wBAoxEA;AACA,WAAA,IAAA,CAAA,MAAA;AACA,WAAA,IAAA,GAAA,IAAA;AACA,WAAA,OAAA,GAAA,IAAA;AACA,WAAA,MAAA,GAAA,KAAA;AACA,WAAA,UAAA,GAAA,IAAA,CAAA,KAAA,CAAA,IAAA,CAAA,SAAA,CAAA,KAAA,cAAA,CAAA,CAAA;AACA,WAAA,cAAA,GAAA,IAAA,CAAA,KAAA,CAAA,IAAA,CAAA,SAAA,CAAA,KAAA,kBAAA,CAAA,CAAA;AACA,WAAA,gBAAA,GAAA,IAAA,CAAA,KAAA,CACA,IAAA,CAAA,SAAA,CAAA,KAAA,uBAAA,CADA,CAAA;AAGA,WAAA,WAAA,CAAA,KAAA,UAAA;AACA,WAAA,QAAA;AACA,WAAA,SAAA;AACA,WAAA,UAAA;AACA,WAAA,cAAA;AACA,WAAA,cAAA,GAfA,CAgBA;AACA,KAryEA;AAuyEA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AAEA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAA,MA93EA,oBA83EA;AAAA;;AACA,WAAA,MAAA,GAAA,KAAA;AACA,WAAA,UAAA,GAAA,KAAA,cAAA;AACA,WAAA,cAAA,GAAA,KAAA,kBAAA;AACA,MAAA,UAAA,CAAA,YAAA;AACA,QAAA,OAAA,CAAA,KAAA,CAAA,mBAAA;AACA,OAFA,EAEA,GAFA,CAAA;AAGA;AAr4EA;AAz1BA,CAAA", "sourcesContent": ["<template>\r\n  <div class=\"personal-data\">\r\n    <Modal\r\n      class=\"about-data-modal\"\r\n      :closable=\"false\"\r\n      :mask-closable=\"false\"\r\n      v-model=\"isShow\"\r\n      class-name=\"vertical-center-modal\"\r\n      :width=\"modalWidth\"\r\n    >\r\n      <!-- <a slot=\"close\">\r\n        <Icon type=\"md-close\" color=\"#ADADAD\" size=\"24\"></Icon>\r\n      </a>-->\r\n      <div slot=\"header\" class=\"header\">\r\n        <div style=\"position:absolute;\">\r\n          <div :style=\"tabButtonWidth\">\r\n            <div style=\"float: right;margin-top:5px\">\r\n              <Button v-show=\"!isEdit\" type=\"primary\" size=\"small\" class=\"font-small\" @click=\"edit\">\r\n                {{\r\n                $t(\"LocaleString.B20014\") }}\r\n              </Button>\r\n              <Button v-show=\"isEdit\" size=\"small\" class=\"font-small\" @click=\"cancelEdit\">\r\n                {{ $t(\"LocaleString.B00015\")\r\n                }}\r\n              </Button>\r\n              <Button\r\n                v-show=\"isEdit\"\r\n                type=\"primary\"\r\n                size=\"small\"\r\n                class=\"font-small\"\r\n                :loading=\"submitLoading\"\r\n                @click=\"editHandleSubmitNew('update')\"\r\n              >{{ $t(\"LocaleString.B00012\") }}</Button>\r\n            </div>\r\n            <div style=\"clear: both\"></div>\r\n          </div>\r\n        </div>\r\n        <!-- <i class=\"left\"></i> -->\r\n        <span class=\"title\">{{ $t(\"LocaleString.L30054\") }}</span>\r\n      </div>\r\n      <div class=\"content\">\r\n        <Tabs :animated=\"false\" :value=\"currentTab\" @on-click=\"changeTab\">\r\n          <TabPane :label=\"$t('LocaleString.F00037')\" name=\"system\">\r\n            <Table :columns=\"columns\" :data=\"data\" :height=\"modalInnerHeight\">\r\n              <template slot-scope=\"{ row, index }\" slot=\"item\">\r\n                <!-- <Input type=\"text\" v-model=\"row.name\" v-if=\"isEdit\" /> -->\r\n                <div v-if=\"row.section == 10\">\r\n                  <span v-if=\"configData.eventAuthClose == true\">\r\n                    {{\r\n                    row.item\r\n                    }}\r\n                  </span>\r\n                </div>\r\n                <div v-if=\"row.section != 10\">\r\n                  <span>\r\n                    {{ row.item }}\r\n                    <img\r\n                      v-if=\"row.section == 6\"\r\n                      :src=\"InfoIcon\"\r\n                      style=\"vertical-align: middle; cursor: pointer\"\r\n                      @click=\"tipModal = true\"\r\n                    />\r\n                    <img\r\n                      v-if=\"row.section == 7\"\r\n                      :src=\"InfoIcon\"\r\n                      style=\"vertical-align: middle; cursor: pointer\"\r\n                      @click=\"tip2Modal = true\"\r\n                    />\r\n                    <img\r\n                      v-if=\"row.section == 11\"\r\n                      :src=\"InfoIcon\"\r\n                      style=\"vertical-align: middle; cursor: pointer\"\r\n                      @click=\"tip3Modal = true\"\r\n                    />\r\n                    <img\r\n                      v-if=\"row.section == 14\"\r\n                      :src=\"InfoIcon\"\r\n                      style=\"vertical-align: middle; cursor: pointer\"\r\n                      @click=\"tip4Modal = true\"\r\n                    />\r\n                    <img\r\n                      v-if=\"row.section == 16\"\r\n                      :src=\"InfoIcon\"\r\n                      style=\"vertical-align: middle; cursor: pointer\"\r\n                      @click=\"tip5Modal = true\"\r\n                    />\r\n                  </span>\r\n                </div>\r\n              </template>\r\n              <template slot-scope=\"{ row, index }\" slot=\"value\">\r\n                <div v-if=\"row.section == 23\">\r\n                  <Select\r\n                    :placeholder=\"$t('LocaleString.D00001')\"\r\n                    v-model=\"configData.license\"\r\n                    v-if=\"isEdit\"\r\n                    multiple\r\n                    transfer\r\n                  >\r\n                    <Option\r\n                      v-for=\"item in serviceCodeList\"\r\n                      :value=\"item.key\"\r\n                      :key=\"item.key\"\r\n                    >{{ item.value }}</Option>\r\n                  </Select>\r\n                  <span v-else>{{ row.value }}</span>\r\n                </div>\r\n                <div v-if=\"row.section == 0\">\r\n                  <Input\r\n                    maxlength=\"25\"\r\n                    show-word-limit\r\n                    v-if=\"isEdit && getEditPermission()\"\r\n                    v-model=\"configData.systemName\"\r\n                  ></Input>\r\n                  <span v-else>{{ row.value }}</span>\r\n                </div>\r\n                <div v-if=\"row.section == 17\">\r\n                  <div\r\n                    v-show=\"isEdit && getEditPermission()\"\r\n                    class=\"toggle-sound paused\"\r\n                    style=\"display: inline-block\"\r\n                  >\r\n                    <Button\r\n                      ghost\r\n                      shape=\"circle\"\r\n                      style=\"width: 20px; margin-left: 10px\"\r\n                      v-if=\"showCleanIcon\"\r\n                      @click=\"restImage\"\r\n                    >\r\n                      <img :src=\"resetIcon\" style=\"width: 30.4px; margin-left: -14.8px\" />\r\n                    </Button>\r\n                  </div>\r\n                  <Upload\r\n                    v-if=\"isEdit && getEditPermission()\"\r\n                    :before-upload=\"handleUploadImage\"\r\n                    action\r\n                    style=\"display: inline-block\"\r\n                  >\r\n                    <span>\r\n                      <Icon class=\"musicIcon\" type=\"md-cloud-upload\" size=\"30\" />\r\n                    </span>\r\n                  </Upload>\r\n                  <img\r\n                    v-if=\"isEdit && getEditPermission()\"\r\n                    id=\"preview\"\r\n                    :src=\"imgFileURL ? imgFileURL : ''\"\r\n                  />\r\n                  <span v-if=\"!isEdit\">\r\n                    <img :src=\"imgFileURL\" />\r\n                  </span>\r\n                </div>\r\n                <div v-if=\"row.section == 31\">\r\n                  <InputNumber\r\n                    :precision=\"0\"\r\n                    :min=\"0\"\r\n                    :max=\"300\"\r\n                    step=\"1\"\r\n                    v-if=\"isEdit\"\r\n                    v-model=\"configData.logoutTime\"\r\n                  ></InputNumber>\r\n                  <span v-else>{{ row.value }}</span>\r\n                </div>\r\n                <div v-if=\"row.section == 18\">\r\n                  <i-switch\r\n                    @on-change=\"switchMultiPlanes\"\r\n                    v-model=\"configData.multiPlanes\"\r\n                    v-if=\"isEdit\"\r\n                  />\r\n                  <span style=\"margin-right:25px\" v-else>{{ row.viewMultiPlanes }}</span>\r\n                  <Select\r\n                    style=\"margin-left:5px; width:770px\"\r\n                    :placeholder=\"$t('LocaleString.D00001')\"\r\n                    v-model=\"configData.selectedPlanes\"\r\n                    v-if=\"isEdit && configData.multiPlanes\"\r\n                    multiple\r\n                    :max-tag-count=\"8\"\r\n                    transfer\r\n                    @on-change=\"changeMultiPlanes\"\r\n                  >\r\n                    <Option\r\n                      v-for=\"item in planeList\"\r\n                      :value=\"item.value\"\r\n                      :key=\"item.value\"\r\n                    >{{ item.label }}</Option>\r\n                  </Select>\r\n                  <span v-else>\r\n                    <span\r\n                      v-if=\"!isEdit && row.valuePlaneSelectList !== ''\"\r\n                    >{{ row.valuePlaneSelectList }}</span>\r\n                  </span>\r\n                </div>\r\n                <div v-if=\"row.section == 30\">\r\n                  <i-switch v-model=\"configData.stuckPlane\" v-if=\"isEdit\" />\r\n                  <span style=\"margin-right:25px\" v-else>{{ row.viewStuckPlane }}</span>\r\n                </div>\r\n                <div v-if=\"row.section == 1\">\r\n                  <Select\r\n                    :placeholder=\"$t('LocaleString.D00001')\"\r\n                    v-model=\"configData.tabList\"\r\n                    v-if=\"isEdit\"\r\n                    multiple\r\n                    transfer\r\n                    @on-change=\"changeDefaultTabList\"\r\n                  >\r\n                    <Option\r\n                      v-for=\"item in showingTab\"\r\n                      :value=\"item.value\"\r\n                      :key=\"item.value\"\r\n                    >{{ item.label }}</Option>\r\n                  </Select>\r\n                  <span v-else>{{ row.value }}</span>\r\n                </div>\r\n                <div v-if=\"row.section == 2\">\r\n                  <i-switch v-model=\"configData.sound\" v-if=\"isEdit\" />\r\n                  <div\r\n                    v-show=\"isEdit\"\r\n                    @click=\"play()\"\r\n                    class=\"toggle-sound paused\"\r\n                    style=\"display: inline-block\"\r\n                  >\r\n                    <span v-if=\"isPlay\">\r\n                      <Icon class=\"musicIcon\" type=\"md-pause\" size=\"30\" />\r\n                    </span>\r\n                    <span v-if=\"!isPlay\">\r\n                      <Icon class=\"musicIcon\" type=\"md-play\" size=\"30\" />\r\n                    </span>\r\n                  </div>\r\n                  <Upload\r\n                    v-if=\"isEdit\"\r\n                    :before-upload=\"handleUpload\"\r\n                    action\r\n                    style=\"display: inline-block\"\r\n                  >\r\n                    <span>\r\n                      <Icon class=\"musicIcon\" type=\"md-cloud-upload\" size=\"30\" />\r\n                    </span>\r\n                  </Upload>\r\n                  <audio\r\n                    ref=\"audio\"\r\n                    :src=\"fileURL != null ? fileURL : alertSound\"\r\n                    @ended=\"ended\"\r\n                    preload\r\n                    id=\"audio\"\r\n                    muted\r\n                  ></audio>\r\n\r\n                  <span v-if=\"!isEdit\">{{ row.value }}</span>\r\n\r\n                  <div\r\n                    v-show=\"!isEdit && configData.sound\"\r\n                    @click=\"play()\"\r\n                    class=\"toggle-sound paused\"\r\n                    style=\"display: inline-block\"\r\n                  >\r\n                    <span v-if=\"isPlay\">\r\n                      <Icon class=\"musicIcon\" type=\"md-pause\" size=\"30\" />\r\n                    </span>\r\n                    <span v-if=\"!isPlay\">\r\n                      <Icon class=\"musicIcon\" type=\"md-play\" size=\"30\" />\r\n                    </span>\r\n                  </div>\r\n                  <span style=\"margin-left:10px\" v-if=\"!isEdit && configData.sound\">\r\n                    <span>{{$t(\"LocaleString.L30203\")}}</span>\r\n                    {{ row.valueTime }}\r\n                    <span>{{$t(\"LocaleString.L30204\")}}</span>\r\n                  </span>\r\n                  <span style=\"margin-left:10px\" v-if=\"isEdit && configData.sound\">\r\n                    <span>{{$t(\"LocaleString.L30203\")}}</span>\r\n                    <InputNumber\r\n                      style=\"width:60px\"\r\n                      :min=\"0\"\r\n                      :max=\"20\"\r\n                      v-model=\"configData.repeatSound\"\r\n                      :placeholder=\"$t('LocaleString.L00001', { 0: $t('LocaleString.L30027') })\r\n      \"\r\n                    ></InputNumber>\r\n                    <span>{{$t(\"LocaleString.L30204\")}}(0~20)</span>\r\n                  </span>\r\n                </div>\r\n                <div v-if=\"row.section == 3\">\r\n                  <Select\r\n                    :placeholder=\"$t('LocaleString.D00001')\"\r\n                    v-model=\"configData.m2List\"\r\n                    v-if=\"isEdit\"\r\n                    multiple\r\n                    transfer\r\n                  >\r\n                    <Option\r\n                      v-for=\"item in showingType\"\r\n                      v-if=\"item.value != 'wetUrine'\"\r\n                      :value=\"item.value\"\r\n                      :key=\"item.value\"\r\n                    >{{ item.label }}</Option>\r\n                  </Select>\r\n                  <span v-else>{{ row.value }}</span>\r\n                </div>\r\n                <div v-if=\"row.section == 4\">\r\n                  <!-- <i-Input v-if=\"isEdit\" v-model=\"configData.iconSize\" placeholder=\"ex: 40*40\"></i-Input> -->\r\n                  <Select\r\n                    :placeholder=\"$t('LocaleString.D00001')\"\r\n                    v-model=\"configData.iconSize\"\r\n                    v-if=\"isEdit\"\r\n                    transfer\r\n                  >\r\n                    <Option\r\n                      v-for=\"item in iconSizeList\"\r\n                      :value=\"item.value\"\r\n                      :key=\"item.value\"\r\n                    >{{ item.label }}</Option>\r\n                  </Select>\r\n                  <span v-else>{{ iconSizeTitle }}</span>\r\n                </div>\r\n                <div v-if=\"row.section == 5\">\r\n                  <Select\r\n                    :placeholder=\"$t('LocaleString.D00001')\"\r\n                    v-model=\"configData.defaultTab\"\r\n                    v-if=\"isEdit\"\r\n                    transfer\r\n                  >\r\n                    <Option\r\n                      v-for=\"item in defaultTabList\"\r\n                      :value=\"item.value\"\r\n                      :key=\"item.value\"\r\n                    >{{ item.label }}</Option>\r\n                  </Select>\r\n                  <span v-else>{{ defaultTabTitle }}</span>\r\n                </div>\r\n                <div v-if=\"row.section == 6\">\r\n                  <Select\r\n                    :placeholder=\"$t('LocaleString.D00001')\"\r\n                    v-model=\"configData.updateSeconds\"\r\n                    v-if=\"isEdit\"\r\n                    transfer\r\n                  >\r\n                    <Option\r\n                      v-for=\"item in defaultUpdateSeconds\"\r\n                      :value=\"item.value\"\r\n                      :key=\"item.value\"\r\n                    >{{ item.label }}</Option>\r\n                  </Select>\r\n                  <!-- <i-Input\r\n\r\n                v-if=\"isEdit\"\r\n                v-model=\"configData.updateSeconds\"\r\n                placeholder=\"30\"\r\n                  ></i-Input>-->\r\n                  <span v-else>{{ row.value }}</span>\r\n                </div>\r\n                <div v-if=\"row.section == 7\">\r\n                  <InputNumber\r\n                    :min=\"0\"\r\n                    v-if=\"isEdit\"\r\n                    v-model=\"configData.grayTime\"\r\n                    :placeholder=\"$t('LocaleString.L00001', { 0: $t('LocaleString.L30027') })\r\n      \"\r\n                  ></InputNumber>\r\n                  <span v-else>{{ row.value }}</span>\r\n                </div>\r\n                <div v-if=\"row.section == 8\">\r\n                  <i-switch v-model=\"configData.m2Line\" v-if=\"isEdit\" />\r\n                  <span v-else>{{ row.value }}</span>\r\n                </div>\r\n                <div v-if=\"row.section == 11\">\r\n                  <Select\r\n                    :placeholder=\"$t('LocaleString.D00001')\"\r\n                    v-model=\"configData.influxRange\"\r\n                    v-if=\"isEdit\"\r\n                    transfer\r\n                  >\r\n                    <Option\r\n                      v-for=\"item in defaultInfluxRange\"\r\n                      :value=\"item.value\"\r\n                      :key=\"item.value\"\r\n                    >{{ item.label }}</Option>\r\n                  </Select>\r\n                  <span v-else>{{ row.value }}</span>\r\n                </div>\r\n                <div v-if=\"row.section == 12\">\r\n                  <InputNumber\r\n                    :precision=\"0\"\r\n                    :formatter=\"(value) => `${Number(value.toFixed(2))}`\"\r\n                    :min=\"1\"\r\n                    :max=\"60\"\r\n                    v-if=\"isEdit\"\r\n                    v-model=\"configData.lossSignal\"\r\n                    :placeholder=\"$t('LocaleString.L00001', { 0: $t('LocaleString.L30027') })\r\n      \"\r\n                  ></InputNumber>\r\n                  <Select\r\n                    :placeholder=\"$t('LocaleString.D00001')\"\r\n                    multiple\r\n                    :max-tag-count=\"6\"\r\n                    style=\"margin-left: 5px; width: 736px\"\r\n                    v-model=\"configData.lossSignalDevices\"\r\n                    v-if=\"isEdit\"\r\n                    transfer\r\n                  >\r\n                    <Option\r\n                      v-for=\"item in deviceListLossSignal\"\r\n                      :value=\"item.key\"\r\n                      :key=\"item.key\"\r\n                    >{{ item.value }}</Option>\r\n                  </Select>\r\n                  <div v-else>\r\n                    <Row>\r\n                      <Col span=\"3\">{{ row.value }}</Col>\r\n                      <Col span=\"21\" v-if=\"row.valueDevices !== ''\">\r\n                        <span>{{ row.valueDevices }}</span>\r\n                      </Col>\r\n                    </Row>\r\n                  </div>\r\n                </div>\r\n                <div v-if=\"row.section == 22\">\r\n                  <InputNumber\r\n                    :precision=\"0\"\r\n                    :formatter=\"(value) => `${Number(value.toFixed(2))}`\"\r\n                    :min=\"1\"\r\n                    :max=\"300\"\r\n                    v-if=\"isEdit\"\r\n                    v-model=\"configData.lossSignal2\"\r\n                    :placeholder=\"$t('LocaleString.L00001', { 0: $t('LocaleString.L30027') })\r\n      \"\r\n                  ></InputNumber>\r\n                  <Select\r\n                    :placeholder=\"$t('LocaleString.D00001')\"\r\n                    multiple\r\n                    :max-tag-count=\"6\"\r\n                    style=\"margin-left: 5px; width: 736px\"\r\n                    v-model=\"configData.lossSignalDevices2\"\r\n                    v-if=\"isEdit\"\r\n                    transfer\r\n                  >\r\n                    <Option\r\n                      v-for=\"item in deviceListLossSignal2\"\r\n                      :value=\"item.key\"\r\n                      :key=\"item.key\"\r\n                    >{{ item.value }}</Option>\r\n                  </Select>\r\n                  <div v-else>\r\n                    <Row>\r\n                      <Col span=\"3\">{{ row.value }}</Col>\r\n                      <Col span=\"21\" v-if=\"row.valueDevices !== ''\">\r\n                        <span>{{ row.valueDevices }}</span>\r\n                      </Col>\r\n                    </Row>\r\n                  </div>\r\n                </div>\r\n                <div v-if=\"row.section == 13\">\r\n                  <InputNumber\r\n                    :precision=\"0\"\r\n                    :formatter=\"(value) => `${Number(value.toFixed(2))}`\"\r\n                    :min=\"1\"\r\n                    :max=\"60\"\r\n                    v-if=\"isEdit\"\r\n                    v-model=\"configData.lowBattery\"\r\n                    :placeholder=\"$t('LocaleString.L00001', { 0: '%' })\"\r\n                  ></InputNumber>\r\n                  <Select\r\n                    :placeholder=\"$t('LocaleString.D00001')\"\r\n                    style=\"margin-left: 5px; width: 736px\"\r\n                    :max-tag-count=\"6\"\r\n                    multiple\r\n                    v-model=\"configData.lowBatteryDevices\"\r\n                    v-if=\"isEdit\"\r\n                    transfer\r\n                  >\r\n                    <Option\r\n                      v-for=\"item in deviceListLowBattery\"\r\n                      :value=\"item.key\"\r\n                      :key=\"item.key\"\r\n                    >{{ item.value }}</Option>\r\n                  </Select>\r\n                  <div v-else>\r\n                    <Row>\r\n                      <Col span=\"3\">{{ row.value }}</Col>\r\n                      <Col span=\"21\" v-if=\"row.valueDevices !== ''\">\r\n                        <span>{{ row.valueDevices }}</span>\r\n                      </Col>\r\n                    </Row>\r\n                  </div>\r\n                </div>\r\n                <div v-if=\"row.section == 15\">\r\n                  <Select\r\n                    :placeholder=\"$t('LocaleString.D00001')\"\r\n                    :max-tag-count=\"8\"\r\n                    multiple\r\n                    v-model=\"configData.abnormalDevices\"\r\n                    v-if=\"isEdit\"\r\n                    transfer\r\n                  >\r\n                    <Option\r\n                      v-for=\"item in deviceListAbnormalDevice\"\r\n                      :value=\"item.key\"\r\n                      :key=\"item.key\"\r\n                    >{{ item.value }}</Option>\r\n                  </Select>\r\n                  <div v-else>\r\n                    <Row>\r\n                      <Col span=\"3\">{{ row.value }}</Col>\r\n                      <Col span=\"21\" v-if=\"row.valueDevices !== ''\">\r\n                        <span>{{ row.valueDevices }}</span>\r\n                      </Col>\r\n                    </Row>\r\n                  </div>\r\n                </div>\r\n                <div v-if=\"row.section == 20\">\r\n                  <Select\r\n                    :placeholder=\"$t('LocaleString.D00001')\"\r\n                    v-model=\"configData.defaultPageSize\"\r\n                    v-if=\"isEdit\"\r\n                    transfer\r\n                  >\r\n                    <Option\r\n                      v-for=\"item in pageSizeList\"\r\n                      :value=\"item.value\"\r\n                      :key=\"item.value\"\r\n                    >{{ item.label }}</Option>\r\n                  </Select>\r\n                  <span v-else>{{ defaultPageSizeTitle }}</span>\r\n                </div>\r\n                <div v-if=\"row.section == 21\">\r\n                  <Select\r\n                    :placeholder=\"$t('LocaleString.D00001')\"\r\n                    v-model=\"configData.m5ObjectType\"\r\n                    v-if=\"isEdit\"\r\n                    transfer\r\n                  >\r\n                    <Option\r\n                      v-for=\"item in m5ObjectTypeList\"\r\n                      :value=\"item.value\"\r\n                      :key=\"item.value\"\r\n                    >{{ item.label }}</Option>\r\n                  </Select>\r\n                  <span v-else>{{ m5ObjectTypeTitle }}</span>\r\n                </div>\r\n                <div v-if=\"row.section == 9\">\r\n                  <i-switch v-model=\"configData.eventAuthClose\" v-if=\"isEdit\" />\r\n                  <span v-else>{{ row.value }}</span>\r\n                </div>\r\n                <div v-if=\"row.section == 10\">\r\n                  <div v-if=\"configData.eventAuthClose == true\">\r\n                    <InputNumber\r\n                      :precision=\"0\"\r\n                      :formatter=\"(value) => `${Number(value.toFixed(2))}`\"\r\n                      :min=\"1\"\r\n                      :max=\"7\"\r\n                      v-if=\"isEdit\"\r\n                      v-model=\"configData.eventKeepDays\"\r\n                      :placeholder=\"$t('LocaleString.L00001', {\r\n      0: $t('LocaleString.L20150'),\r\n    })\r\n      \"\r\n                    ></InputNumber>\r\n\r\n                    <span v-else>{{ row.value }}</span>\r\n                  </div>\r\n                </div>\r\n                <div v-if=\"row.section == 14\">\r\n                  <Select\r\n                    :placeholder=\"$t('LocaleString.D00001')\"\r\n                    v-model=\"configData.skipEvent\"\r\n                    v-if=\"isEdit\"\r\n                    multiple\r\n                    :max-tag-count=\"10\"\r\n                    transfer\r\n                  >\r\n                    <Option\r\n                      v-for=\"item in eventServiceCodeList\"\r\n                      :value=\"item.key\"\r\n                      :key=\"item.key\"\r\n                    >{{ item.value }}</Option>\r\n                  </Select>\r\n                  <span v-else>\r\n                    <span v-if=\"row.valueSkipEvent !== ''\">{{ row.valueSkipEvent }}</span>\r\n                  </span>\r\n                </div>\r\n                <div v-if=\"row.section == 16\">\r\n                  <Select\r\n                    :placeholder=\"$t('LocaleString.D00001')\"\r\n                    v-model=\"configData.deviceSelectList\"\r\n                    v-if=\"isEdit\"\r\n                    multiple\r\n                    :max-tag-count=\"10\"\r\n                    transfer\r\n                    @on-open-change=\"openDeviceSelection\"\r\n                    @on-change=\"changeDeviceDefaultSelectList\"\r\n                  >\r\n                    <Option\r\n                      v-for=\"item in deviceDefaultSelection\"\r\n                      :value=\"item.key\"\r\n                      :key=\"item.key\"\r\n                    >{{ item.value }}</Option>\r\n                  </Select>\r\n                  <span v-else>\r\n                    <span v-if=\"row.valueDeviceSelectList !== ''\">{{ row.valueDeviceSelectList }}</span>\r\n                  </span>\r\n                </div>\r\n                <div v-if=\"row.section == 19\">\r\n                  <Select\r\n                    :placeholder=\"$t('LocaleString.D00001')\"\r\n                    v-model=\"configData.m1LongPress\"\r\n                    v-if=\"isEdit\"\r\n                    multiple\r\n                    :max-tag-count=\"8\"\r\n                    transfer\r\n                  >\r\n                    <Option\r\n                      v-for=\"item in deviceDefaultSelection.filter((item) => item.key.includes('fusion-km'))\"\r\n                      :value=\"item.key\"\r\n                      :key=\"item.key\"\r\n                    >{{ item.value }}</Option>\r\n                  </Select>\r\n                  <span v-else>\r\n                    <span v-if=\"row.valueM1LongPressList !== ''\">{{ row.valueM1LongPressList }}</span>\r\n                  </span>\r\n                </div>\r\n                <div v-if=\"row.section == 24\">\r\n                  <i-switch v-model=\"configData.showLeaveBed\" v-if=\"isEdit\" />\r\n                  <span v-else>{{ row.value }}</span>\r\n                </div>\r\n                <div v-if=\"row.section == 25\">\r\n                  <InputNumber\r\n                    :precision=\"0\"\r\n                    :formatter=\"(value) => `${Number(value.toFixed(2))}`\"\r\n                    :min=\"2\"\r\n                    :max=\"600\"\r\n                    v-if=\"isEdit\"\r\n                    v-model=\"configData.m2Statistic\"\r\n                    :placeholder=\"$t('LocaleString.L00001', {\r\n      0: $t('LocaleString.L20150'),\r\n    })\r\n      \"\r\n                  ></InputNumber>\r\n                  <span v-else>{{ row.value }}</span>\r\n                </div>\r\n                <div v-if=\"row.section == 26\">\r\n                  <InputNumber\r\n                    :precision=\"0\"\r\n                    :formatter=\"(value) => `${Number(value.toFixed(2))}`\"\r\n                    :min=\"2\"\r\n                    :max=\"600\"\r\n                    v-if=\"isEdit\"\r\n                    v-model=\"configData.m3Statistic\"\r\n                    :placeholder=\"$t('LocaleString.L00001', {\r\n      0: $t('LocaleString.L20150'),\r\n    })\r\n      \"\r\n                  ></InputNumber>\r\n                  <span v-else>{{ row.value }}</span>\r\n                </div>\r\n                <div v-if=\"row.section == 27\">\r\n                  <InputNumber\r\n                    :precision=\"0\"\r\n                    :formatter=\"(value) => `${Number(value.toFixed(2))}`\"\r\n                    :min=\"2\"\r\n                    :max=\"600\"\r\n                    v-if=\"isEdit\"\r\n                    v-model=\"configData.m4Statistic\"\r\n                    :placeholder=\"$t('LocaleString.L00001', {\r\n      0: $t('LocaleString.L20150'),\r\n    })\r\n      \"\r\n                  ></InputNumber>\r\n                  <span v-else>{{ row.value }}</span>\r\n                </div>\r\n                <div v-if=\"row.section == 28\">\r\n                  <InputNumber\r\n                    :precision=\"0\"\r\n                    :formatter=\"(value) => `${Number(value.toFixed(2))}`\"\r\n                    :min=\"2\"\r\n                    :max=\"600\"\r\n                    v-if=\"isEdit\"\r\n                    v-model=\"configData.m8Statistic\"\r\n                    :placeholder=\"$t('LocaleString.L00001', {\r\n      0: $t('LocaleString.L20150'),\r\n    })\r\n      \"\r\n                  ></InputNumber>\r\n                  <span v-else>{{ row.value }}</span>\r\n                </div>\r\n                <div v-if=\"row.section == 29\">\r\n                  <InputNumber\r\n                    :precision=\"0\"\r\n                    :formatter=\"(value) => `${Number(value.toFixed(2))}`\"\r\n                    :min=\"2\"\r\n                    :max=\"600\"\r\n                    v-if=\"isEdit\"\r\n                    v-model=\"configData.m9Statistic\"\r\n                    :placeholder=\"$t('LocaleString.L00001', {\r\n      0: $t('LocaleString.L20150'),\r\n    })\r\n      \"\r\n                  ></InputNumber>\r\n                  <span v-else>{{ row.value }}</span>\r\n                </div>\r\n                <div v-if=\"row.section == 32\">\r\n                  <ColorPicker v-model=\"configData.stateColor\" v-if=\"isEdit\" />\r\n                  <span v-else>\r\n                    <div\r\n                      :style=\"{height:'20px',width:'20px',borderRadius:'2px' , backgroundColor: configData.stateColor}\"\r\n                    ></div>\r\n                  </span>\r\n                </div>\r\n              </template>\r\n            </Table>\r\n\r\n            <Row style=\"margin-top:10px\" v-if=\"isAdmin\">\r\n              <Col\r\n                span=\"4\"\r\n                style=\"padding-left:10px;line-height:32px\"\r\n              >{{ $t('LocaleString.L30155') }}</Col>\r\n              <Col span=\"20\">\r\n                <Input\r\n                  v-model=\"loginAccount\"\r\n                  :placeholder=\"$t('LocaleString.L00001', { 0: $t('LocaleString.L00002') })\r\n      \"\r\n                  style=\"width:200px;margin-right:10px\"\r\n                />\r\n                <Input\r\n                  type=\"password\"\r\n                  v-model=\"loginPassword\"\r\n                  autocomplete=\"new-password\"\r\n                  :placeholder=\"$t('LocaleString.L00001', { 0: $t('LocaleString.L00003') })\r\n      \"\r\n                  style=\"width:200px;margin-right:10px\"\r\n                />\r\n                <Button type=\"text\" @click=\"generateToken\" style=\"width:30px\">\r\n                  <img :src=\"generateIcon\" style=\"margin-left:-15px;width: 30px;;margin-right:5px\" />\r\n                </Button>\r\n                <Input\r\n                  type=\"password\"\r\n                  id=\"generateTokenData\"\r\n                  v-model=\"token\"\r\n                  autocomplete=\"new-password\"\r\n                  style=\"width:420px;margin-right:10px\"\r\n                />\r\n                <Button type=\"text\" @click=\"copyToken\" style=\"width:30px\">\r\n                  <img :src=\"copyIcon\" style=\"margin-left:-15px;width: 30px;\" />\r\n                </Button>\r\n              </Col>\r\n            </Row>\r\n          </TabPane>\r\n          <TabPane :label=\"$t('LocaleString.L30107')\" name=\"km\">\r\n            <Table\r\n              v-if=\"!isEdit\"\r\n              class=\"config-table\"\r\n              :columns=\"kmColumnsList\"\r\n              :data=\"kmConfigList\"\r\n              :no-data-text=\"noDataText\"\r\n            >\r\n              <template slot-scope=\"{ row, index }\" slot=\"type\">\r\n                <span>{{ row.type }}</span>\r\n              </template>\r\n            </Table>\r\n            <Table\r\n              v-if=\"isEdit\"\r\n              class=\"config-table\"\r\n              :columns=\"kmColumnsEdit\"\r\n              :data=\"kmConfigListEdit\"\r\n              :no-data-text=\"noDataText\"\r\n            >\r\n              <template slot-scope=\"{ row, index }\" slot=\"type\">\r\n                <span>{{ getObjectType(row.type) }}</span>\r\n              </template>\r\n              <template slot-scope=\"{ row, index }\" slot=\"longPress_cht\">\r\n                <Input\r\n                  type=\"text\"\r\n                  v-model=\"row.longPress_cht\"\r\n                  maxlength=\"20\"\r\n                  @on-change=\"onEditDataChange('longPress_cht', row, index)\"\r\n                />\r\n              </template>\r\n              <template slot-scope=\"{ row, index }\" slot=\"longPress_en\">\r\n                <Input\r\n                  type=\"text\"\r\n                  v-model=\"row.longPress_en\"\r\n                  maxlength=\"20\"\r\n                  @on-change=\"onEditDataChange('longPress_en', row, index)\"\r\n                />\r\n              </template>\r\n              <template slot-scope=\"{ row, index }\" slot=\"shortClick_cht\">\r\n                <Input\r\n                  type=\"text\"\r\n                  v-model=\"row.shortClick_cht\"\r\n                  maxlength=\"20\"\r\n                  @on-change=\"onEditDataChange('shortClick_cht', row, index)\"\r\n                />\r\n              </template>\r\n              <template slot-scope=\"{ row, index }\" slot=\"shortClick_en\">\r\n                <Input\r\n                  type=\"text\"\r\n                  v-model=\"row.shortClick_en\"\r\n                  maxlength=\"20\"\r\n                  @on-change=\"onEditDataChange('shortClick_en', row, index)\"\r\n                />\r\n              </template>\r\n              <template slot-scope=\"{ row, index }\" slot=\"doubleClick_cht\">\r\n                <Input\r\n                  type=\"text\"\r\n                  v-model=\"row.doubleClick_cht\"\r\n                  maxlength=\"20\"\r\n                  @on-change=\"onEditDataChange('doubleClick_cht', row, index)\"\r\n                />\r\n              </template>\r\n              <template slot-scope=\"{ row, index }\" slot=\"doubleClick_en\">\r\n                <Input\r\n                  type=\"text\"\r\n                  v-model=\"row.doubleClick_en\"\r\n                  maxlength=\"20\"\r\n                  @on-change=\"onEditDataChange('doubleClick_en', row, index)\"\r\n                />\r\n              </template>\r\n            </Table>\r\n          </TabPane>\r\n          <TabPane :label=\"$t('LocaleString.L30139')\" name=\"camera\" v-if=\"isAdmin\">\r\n            <Table\r\n              class=\"config-table\"\r\n              :columns=\"cameraColumnsList\"\r\n              :data=\"cameraConfigList\"\r\n              :no-data-text=\"noDataText\"\r\n            >\r\n              <template slot-scope=\"{ row, index }\" slot=\"capture\">\r\n                <i-switch\r\n                  v-if=\"isAdmin && isEdit\"\r\n                  v-model=\"row.capture\"\r\n                  @on-change=\"onCameraDataChange('capture', row, index)\"\r\n                />\r\n                <span v-if=\"!isEdit || !isAdmin\">\r\n                  {{ row.capture ? $t('LocaleString.D00003') : $t('LocaleString.D00004')\r\n                  }}\r\n                </span>\r\n              </template>\r\n              <template slot-scope=\"{ row, index }\" slot=\"prefixMinute\">\r\n                <InputNumber\r\n                  style=\"width:50px\"\r\n                  v-if=\"row.capture && isAdmin && isEdit\"\r\n                  v-model=\"row.prefixMinute\"\r\n                  :precision=\"0\"\r\n                  :min=\"0\"\r\n                  :max=\"15\"\r\n                  @on-change=\"onCameraDataChange('prefixMinute', row, index)\"\r\n                />\r\n                <span v-if=\"(!isEdit && row.capture) || !isAdmin\">{{ row.prefixMinute }}</span>\r\n              </template>\r\n              <template slot-scope=\"{ row, index }\" slot=\"suffixMinute\">\r\n                <InputNumber\r\n                  style=\"width:50px\"\r\n                  v-if=\"row.capture && isAdmin && isEdit\"\r\n                  v-model=\"row.suffixMinute\"\r\n                  :precision=\"0\"\r\n                  :min=\"0\"\r\n                  :max=\"15\"\r\n                  @on-change=\"onCameraDataChange('suffixMinute', row, index)\"\r\n                />\r\n                <span v-if=\"(!isEdit && row.capture) || !isAdmin\">{{ row.suffixMinute }}</span>\r\n              </template>\r\n              <template slot-scope=\"{ row, index }\" slot=\"backupDirectory\">\r\n                <Input\r\n                  v-model=\"row.backupDirectory\"\r\n                  @on-change=\"onCameraDataChange('backupDirectory', row, index)\"\r\n                  v-if=\"row.capture && isAdmin && isEdit\"\r\n                />\r\n                <span v-if=\"(!isEdit && row.capture) || !isAdmin\">{{ row.backupDirectory }}</span>\r\n              </template>\r\n              <template slot-scope=\"{ row, index }\" slot=\"account\">\r\n                <Input\r\n                  v-if=\"row.capture && isAdmin && isEdit\"\r\n                  v-model=\"row.account\"\r\n                  @on-change=\"onCameraDataChange('account', row, index)\"\r\n                />\r\n                <span v-if=\"(!isEdit && row.capture) || !isAdmin\">{{ row.account }}</span>\r\n              </template>\r\n              <template slot-scope=\"{ row, index }\" slot=\"password\">\r\n                <Input\r\n                  type=\"password\"\r\n                  v-model=\"row.password\"\r\n                  autocomplete=\"new-password\"\r\n                  @on-change=\"onCameraDataChange('password', row, index)\"\r\n                  v-if=\"row.capture && isAdmin && isEdit\"\r\n                />\r\n                <span v-if=\"(!isEdit && row.capture) || !isAdmin\">{{ row.password }}</span>\r\n              </template>\r\n              <template slot-scope=\"{ row, index }\" slot=\"keepDays\">\r\n                <InputNumber\r\n                  style=\"width:70px\"\r\n                  v-if=\"row.capture && isAdmin && isEdit\"\r\n                  v-model=\"row.keepDays\"\r\n                  :precision=\"0\"\r\n                  :min=\"0\"\r\n                  @on-change=\"onCameraDataChange('keepDays', row, index)\"\r\n                />\r\n                <span v-if=\"(!isEdit && row.capture) || !isAdmin\">{{ row.keepDays }}</span>\r\n              </template>\r\n              <template slot-scope=\"{ row, index }\" slot=\"positionCapture\">\r\n                <InputNumber\r\n                  v-if=\"row.capture && isAdmin && isEdit\"\r\n                  :precision=\"0\"\r\n                  :min=\"0\"\r\n                  :max=\"15\"\r\n                  v-model=\"row.positionCapture\"\r\n                  @on-change=\"onCameraDataChange('positionCapture', row, index)\"\r\n                />\r\n                <span v-if=\"(!isEdit && row.capture) || !isAdmin\">{{ row.positionCapture }}</span>\r\n              </template>\r\n              <template slot-scope=\"{ row, index }\" slot=\"action\">\r\n                <Button\r\n                  @click=\"testCameraConnection(row)\"\r\n                  type=\"primary\"\r\n                  class=\"font-small\"\r\n                  v-if=\"row.capture && isAdmin && isEdit\"\r\n                >\r\n                  {{\r\n                  $t(\"LocaleString.B30010\") }}\r\n                </Button>\r\n              </template>\r\n            </Table>\r\n          </TabPane>\r\n          <TabPane :label=\"$t('LocaleString.L30177')\" name=\"statisticLine\">\r\n            <Table\r\n              class=\"config-table\"\r\n              :columns=\"statisticLineColumnsList\"\r\n              :data=\"statisticLineConfigList\"\r\n              :no-data-text=\"noDataText\"\r\n            >\r\n              <template slot-scope=\"{ row }\" slot=\"type\">\r\n                <span>{{ row.type }}</span>\r\n              </template>\r\n              <template slot-scope=\"{ row,index }\" slot=\"interval\">\r\n                <Select\r\n                  :placeholder=\"$t('LocaleString.D00001')\"\r\n                  v-model=\"row.interval\"\r\n                  v-if=\"isEdit\"\r\n                  transfer\r\n                  @on-change=\"onStatisticLineDataChange('interval', row, index)\"\r\n                >\r\n                  <Option v-for=\"item in 10\" :value=\"item\" :key=\"item\">{{ item }}</Option>\r\n                </Select>\r\n                <span v-else>{{ row.interval }}</span>\r\n              </template>\r\n              <template slot-scope=\"{ row,index }\" slot=\"dataType\">\r\n                <Select\r\n                  :placeholder=\"$t('LocaleString.D00001')\"\r\n                  v-model=\"row.dataType\"\r\n                  v-if=\"isEdit\"\r\n                  transfer\r\n                  @on-change=\"onStatisticLineDataChange('dataType', row, index)\"\r\n                >\r\n                  <Option\r\n                    v-for=\"item in dataTypeList\"\r\n                    :value=\"item.key\"\r\n                    :key=\"item.key\"\r\n                  >{{ item.value }}</Option>\r\n                </Select>\r\n                <span v-else>{{ dataTypeList.find(item=> item.key == row.dataType).value }}</span>\r\n              </template>\r\n              <template slot-scope=\"{ row, index }\" slot=\"warningLine\">\r\n                <i-switch\r\n                  v-if=\"isEdit\"\r\n                  v-model=\"row.warningLine\"\r\n                  @on-change=\"onStatisticLineDataChange('warningLine', row, index)\"\r\n                />\r\n                <span\r\n                  v-else\r\n                >{{ row.warningLine == true ? $t('LocaleString.D30029') : $t('LocaleString.D30030') }}</span>\r\n              </template>\r\n            </Table>\r\n          </TabPane>\r\n          <TabPane :label=\"$t('LocaleString.L30194')\" name=\"thirdParty\" v-if=\"isAdmin\">\r\n            <Table\r\n              class=\"config-table\"\r\n              :columns=\"columns\"\r\n              :data=\"thirdPartyConfigList\"\r\n              :no-data-text=\"noDataText\"\r\n            >\r\n              <template slot-scope=\"{ row }\" slot=\"item\">\r\n                <span>{{ row.item }}</span>\r\n              </template>\r\n              <template slot-scope=\"{ row,index }\" slot=\"value\">\r\n                <div v-if=\"row.section == 1\">\r\n                  <i-switch\r\n                    @on-change=\"switchloginThirdParty\"\r\n                    v-model=\"thirdPartyData.loginThirdParty\"\r\n                    v-if=\"isEdit\"\r\n                  />\r\n                  <span\r\n                    style=\"margin-right:25px\"\r\n                    v-else\r\n                  >{{ viewLoginThirdParty == \"\" ? $t(\"LocaleString.B20010\") :viewLoginThirdParty }}</span>\r\n                </div>\r\n                <div v-if=\"row.section == 2\">\r\n                  <Input v-if=\"isEdit\" v-model=\"thirdPartyData.url\"></Input>\r\n                  <span v-else>{{ row.value }}</span>\r\n                </div>\r\n                <div v-if=\"row.section == 3\">\r\n                  <Input v-if=\"isEdit\" v-model=\"thirdPartyData.userName\"></Input>\r\n                  <span v-else>{{ row.value }}</span>\r\n                </div>\r\n                <div v-if=\"row.section == 4\">\r\n                  <Input v-if=\"isEdit\" v-model=\"thirdPartyData.secret\"></Input>\r\n                  <span v-else>{{ row.value }}</span>\r\n                </div>\r\n              </template>\r\n            </Table>\r\n            <Button\r\n              v-if=\"thirdPartyData.loginThirdParty\"\r\n              style=\"margin-top:5px\"\r\n              @click=\"testConnection\"\r\n            >{{ $t(\"LocaleString.B30010\")}}</Button>\r\n          </TabPane>\r\n        </Tabs>\r\n      </div>\r\n      <div slot=\"footer\" style=\"text-align: center; margin-top: 10px\">\r\n        <Button type=\"primary\" class=\"font-small\" @click=\"cancel\" v-show=\"!isEdit\">\r\n          {{ $t(\"LocaleString.B00044\")\r\n          }}\r\n        </Button>\r\n      </div>\r\n    </Modal>\r\n    <Modal\r\n      v-model=\"tipModal\"\r\n      :title=\"$t('LocaleString.L30061')\"\r\n      :mask-closable=\"false\"\r\n      :closable=\"false\"\r\n    >\r\n      <div class=\"message\">\r\n        <div>\r\n          <p>{{ $t(\"LocaleString.L30067\") }}</p>\r\n          <p>\r\n            <B>{{ $t(\"LocaleString.L30106\") }}</B>\r\n          </p>\r\n          <p>\r\n            <B>{{ $t(\"LocaleString.L30068\") }}</B>\r\n          </p>\r\n          <br />\r\n          <p>{{ $t(\"LocaleString.L30066\") }}</p>\r\n        </div>\r\n      </div>\r\n      <div slot=\"footer\" style=\"text-align: center; margin-top: 5px\">\r\n        <Button type=\"primary\" @click=\"tipModal = false\">\r\n          {{\r\n          $t(\"LocaleString.B00044\")\r\n          }}\r\n        </Button>\r\n      </div>\r\n    </Modal>\r\n    <Modal\r\n      v-model=\"tip2Modal\"\r\n      :title=\"$t('LocaleString.L30062')\"\r\n      :mask-closable=\"false\"\r\n      :closable=\"false\"\r\n    >\r\n      <div class=\"message\">\r\n        <div>\r\n          <p>{{ $t(\"LocaleString.L30063\") }}</p>\r\n          <p>\r\n            <B>{{ $t(\"LocaleString.L30064\") }}</B>\r\n          </p>\r\n          <p>\r\n            <B>{{ $t(\"LocaleString.L30065\") }}</B>\r\n          </p>\r\n          <br />\r\n          <p>{{ $t(\"LocaleString.L30066\") }}</p>\r\n        </div>\r\n      </div>\r\n      <div slot=\"footer\" style=\"text-align: center; margin-top: 5px\">\r\n        <Button type=\"primary\" @click=\"tip2Modal = false\">\r\n          {{\r\n          $t(\"LocaleString.B00044\")\r\n          }}\r\n        </Button>\r\n      </div>\r\n    </Modal>\r\n    <Modal\r\n      v-model=\"tip3Modal\"\r\n      :title=\"$t('LocaleString.L30101')\"\r\n      :mask-closable=\"false\"\r\n      :closable=\"false\"\r\n    >\r\n      <div class=\"message\">\r\n        <div>\r\n          <p>\r\n            <B>{{ $t(\"LocaleString.L30102\") }}</B>\r\n          </p>\r\n          <br />\r\n          <p>{{ $t(\"LocaleString.L30066\") }}</p>\r\n        </div>\r\n      </div>\r\n      <div slot=\"footer\" style=\"text-align: center; margin-top: 5px\">\r\n        <Button type=\"primary\" @click=\"tip3Modal = false\">\r\n          {{\r\n          $t(\"LocaleString.B00044\")\r\n          }}\r\n        </Button>\r\n      </div>\r\n    </Modal>\r\n    <Modal\r\n      v-model=\"tip4Modal\"\r\n      :title=\"$t('LocaleString.L30116')\"\r\n      :mask-closable=\"false\"\r\n      :closable=\"false\"\r\n    >\r\n      <div class=\"message\">\r\n        <div>\r\n          <p>{{ $t(\"LocaleString.L30117\") }}</p>\r\n        </div>\r\n      </div>\r\n      <div slot=\"footer\" style=\"text-align: center; margin-top: 5px\">\r\n        <Button type=\"primary\" @click=\"tip4Modal = false\">\r\n          {{\r\n          $t(\"LocaleString.B00044\")\r\n          }}\r\n        </Button>\r\n      </div>\r\n    </Modal>\r\n    <Modal\r\n      v-model=\"tip5Modal\"\r\n      :title=\"$t('LocaleString.L30129')\"\r\n      :mask-closable=\"false\"\r\n      :closable=\"false\"\r\n    >\r\n      <div class=\"message\">\r\n        <div>\r\n          <p>{{ $t(\"LocaleString.L30130\") }}</p>\r\n        </div>\r\n      </div>\r\n      <div slot=\"footer\" style=\"text-align: center; margin-top: 5px\">\r\n        <Button type=\"primary\" @click=\"tip5Modal = false\">\r\n          {{\r\n          $t(\"LocaleString.B00044\")\r\n          }}\r\n        </Button>\r\n      </div>\r\n    </Modal>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport resetIcon from \"@/assets/images/ic_reset.svg\";\r\nimport copyIcon from \"@/assets/images/ic_copy.png\";\r\nimport generateIcon from \"@/assets/images/ic_generate.png\";\r\nimport Config from \"@/common/config\";\r\nimport CommonService from \"@/components/administrative/apps/commonService\";\r\nimport InfoIcon from \"@/components/administrative/common/images/pointPlane/icon-ic-info.svg\";\r\nconst alertSound = require(\"@/assets/beep.mp3\"); // require the sound\r\nlet Base64 = require(\"js-base64\");\r\nlet moment = require(\"moment\");\r\nexport default {\r\n  data() {\r\n    return {\r\n      thirdPartyData: {\r\n        loginThirdParty: false,\r\n        url: \"\",\r\n        userName: \"\",\r\n        secret: \"\"\r\n      },\r\n      dataTypeList: [\r\n        { key: \"MEDIAN\", value: this.$t(\"LocaleString.L30178\") },\r\n        { key: \"MEAN\", value: this.$t(\"LocaleString.L30179\") },\r\n        { key: \"LAST\", value: this.$t(\"LocaleString.L30180\") }\r\n      ],\r\n      serviceCodeList: [],\r\n      serviceNameList: [],\r\n      copyIcon: copyIcon,\r\n      generateIcon: generateIcon,\r\n      loginAccount: \"\",\r\n      loginPassword: \"\",\r\n      token: \"\",\r\n      isAdmin: false,\r\n      openDeviceSelect: false,\r\n      description: \"\",\r\n      tabButtonWidth: {\r\n        width: \"1150px\"\r\n      },\r\n      modalWidth: 1200,\r\n      modalInnerHeight: window.innerHeight - 300,\r\n      currentTab: \"system\",\r\n      showCleanIcon: false,\r\n      resetIcon: resetIcon,\r\n      imgURL: \"\",\r\n      oriTab: \"\",\r\n      eventServiceCodeList: [],\r\n      serviceCodeLocaleStringsList: [],\r\n      submitLoading: false,\r\n      allDeviceTypeMenu: null,\r\n      deviceListLossSignal: [],\r\n      deviceListLossSignal2: [],\r\n      deviceListLowBattery: [],\r\n      deviceListAbnormalDevice: [],\r\n      alertSound: alertSound,\r\n      imgFile: null,\r\n      imgFileURL: null,\r\n      imgFileOriURL: null,\r\n      fileURL: null,\r\n      fileOriURL: null,\r\n      tempURL: null,\r\n      file: null,\r\n      isPlay: false,\r\n      tipModal: false,\r\n      tip2Modal: false,\r\n      tip3Modal: false,\r\n      tip4Modal: false,\r\n      tip5Modal: false,\r\n      InfoIcon: InfoIcon,\r\n      iconSizeTitle: \"\",\r\n      m5ObjectTypeTitle: \"\",\r\n      defaultPageSizeTitle: \"\",\r\n      defaultTabTitle: \"\",\r\n      origConfigData: {},\r\n      origThirdPartyData: {},\r\n      configData: {\r\n        defaultPageSize: \"\",\r\n        m5ObjectType: \"all\",\r\n        m1LongPress: [\"fusion-km7\"],\r\n        multiPlanes: false,\r\n        stuckPlane: false,\r\n        selectedPlanes: [],\r\n        planeList: [],\r\n        systemName: \"\",\r\n        tabList: [\"m1\", \"m2\", \"m3\", \"m4\", \"m5\", \"m6\", \"m8\"],\r\n        soundURL: null,\r\n        sound: false,\r\n        repeatSound: 0,\r\n        m2List: [\r\n          \"temperature\",\r\n          \"heartRate\",\r\n          \"bloodOxygen\",\r\n          \"bloodPressure\",\r\n          \"step\"\r\n        ],\r\n        iconSize: \"40\",\r\n        defaultTab: \"m1\",\r\n        updateSeconds: \"30\",\r\n        grayTime: 15,\r\n        m2Line: false,\r\n        eventAuthClose: false,\r\n        eventKeepDays: \"2\",\r\n        influxRange: 8,\r\n        lossSignal: 1,\r\n        lossSignal2: 60,\r\n        lowBattery: 30,\r\n        lossSignalDevices: null,\r\n        lossSignalDevices2: [],\r\n        lowBatteryDevices: null,\r\n        abnormalDevices: null,\r\n        skipEvent: [],\r\n        deviceSelectList: [],\r\n        license: [],\r\n        showLeaveBed: false,\r\n        m2Statistic: 10,\r\n        m3Statistic: 10,\r\n        m4Statistic: 60,\r\n        m8Statistic: 60,\r\n        m9Statistic: 60,\r\n        stateColor: \"#000000\",\r\n        logoutTime: 0\r\n      },\r\n      viewTab: \"\",\r\n      viewLicense: \"\",\r\n      viewPlaySound: \"\",\r\n      viewM2Line: \"\",\r\n      viewM2List: \"\",\r\n      viewEventAuthClose: \"\",\r\n      viewEventKeepDays: \"\",\r\n      viewLogo: \"\",\r\n      viewMultiPlanes: \"\",\r\n      viewStuckPlane: \"\",\r\n      viewLoginThirdParty: \"\",\r\n      isEdit: false,\r\n      isShow: false,\r\n\r\n      configList: [],\r\n      configListToSave: [],\r\n      noDataText: this.$t(\"LocaleString.L00081\"),\r\n      kmObjectTypeList: [\r\n        {\r\n          type: \"equipment\",\r\n          name: this.$t(\"LocaleString.D00008\")\r\n        },\r\n        {\r\n          type: \"people\",\r\n          name: this.$t(\"LocaleString.D00007\")\r\n        },\r\n        {\r\n          type: \"space\",\r\n          name: this.$t(\"LocaleString.D00009\")\r\n        },\r\n        {\r\n          type: \"other\",\r\n          name: this.$t(\"LocaleString.D00010\")\r\n        }\r\n      ],\r\n      cameraTypeList: [\r\n        {\r\n          type: \"help\",\r\n          name: this.$t(\"LocaleString.D00030\")\r\n        },\r\n        {\r\n          type: \"enter\",\r\n          name: this.$t(\"LocaleString.D00029\")\r\n        },\r\n        {\r\n          type: \"leave\",\r\n          name: this.$t(\"LocaleString.D00031\")\r\n        },\r\n        {\r\n          type: \"fall\",\r\n          name: this.$t(\"LocaleString.D00035\")\r\n        }\r\n      ],\r\n      cameraColumnsList: [\r\n        {\r\n          title: this.$t(\"LocaleString.L00047\"),\r\n          slot: \"type\",\r\n          width: 90,\r\n          render: (h, params) => {\r\n            return h(\"span\", this.getCameraType(params.row.type));\r\n          }\r\n        },\r\n        {\r\n          title: this.$t(\"LocaleString.L30140\"),\r\n          slot: \"capture\",\r\n          width: 70\r\n        },\r\n        {\r\n          title: this.$t(\"LocaleString.L30141\"),\r\n          slot: \"prefixMinute\",\r\n          width: 85\r\n        },\r\n        {\r\n          title: this.$t(\"LocaleString.L30142\"),\r\n          slot: \"suffixMinute\",\r\n          width: 85\r\n        },\r\n        {\r\n          title: this.$t(\"LocaleString.L30143\"),\r\n          slot: \"backupDirectory\"\r\n        },\r\n        {\r\n          title: this.$t(\"LocaleString.L30144\"),\r\n          slot: \"account\",\r\n          width: 110\r\n        },\r\n        {\r\n          title: this.$t(\"LocaleString.L00003\"),\r\n          slot: \"password\",\r\n          width: 110\r\n        },\r\n        {\r\n          title: this.$t(\"LocaleString.L30145\"),\r\n          slot: \"keepDays\",\r\n          width: 90\r\n        },\r\n        {\r\n          title: this.$t(\"LocaleString.L30146\"),\r\n          slot: \"positionCapture\",\r\n          width: 120\r\n        },\r\n        {\r\n          title: \" \",\r\n          slot: \"action\",\r\n          width: 110\r\n        }\r\n      ],\r\n      statisticLineColumnsList: [\r\n        {\r\n          title: this.$t(\"LocaleString.L30057\"),\r\n          slot: \"type\"\r\n        },\r\n        {\r\n          title: this.$t(\"LocaleString.L30150\"),\r\n          slot: \"interval\"\r\n        },\r\n        {\r\n          title: this.$t(\"LocaleString.L00157\"),\r\n          slot: \"dataType\"\r\n        },\r\n        {\r\n          title: this.$t(\"LocaleString.L30151\"),\r\n          slot: \"warningLine\"\r\n        }\r\n      ],\r\n      defaultStatisticLineConfigList: [\r\n        {\r\n          type: this.$t(\"LocaleString.F30002\"),\r\n          interval: 1,\r\n          dataType: \"MEDIAN\",\r\n          warningLine: true,\r\n          entry: \"m2\"\r\n        },\r\n        {\r\n          type: this.$t(\"LocaleString.F30003\"),\r\n          interval: 1,\r\n          dataType: \"MEDIAN\",\r\n          warningLine: true,\r\n          entry: \"m3\"\r\n        },\r\n        {\r\n          type: this.$t(\"LocaleString.F30004\"),\r\n          interval: 1,\r\n          dataType: \"MEDIAN\",\r\n          warningLine: true,\r\n          entry: \"m4\"\r\n        },\r\n        {\r\n          type: this.$t(\"LocaleString.F30016\"),\r\n          interval: 1,\r\n          dataType: \"MEDIAN\",\r\n          warningLine: true,\r\n          entry: \"m8\"\r\n        }\r\n      ],\r\n      statisticLineConfigList: [],\r\n      thirdPartyConfigList: [\r\n        {\r\n          item: this.$t(\"LocaleString.L30195\"),\r\n          section: 1,\r\n          value: false\r\n        },\r\n        {\r\n          item: this.$t(\"LocaleString.L30196\"),\r\n          section: 2,\r\n          value: \"\"\r\n        },\r\n        {\r\n          item: \"UserName\",\r\n          section: 3,\r\n          value: \"\"\r\n        },\r\n        {\r\n          item: \"CredentialSecret\",\r\n          section: 4,\r\n          value: \"\"\r\n        }\r\n      ],\r\n      defaultCameraConfigList: [\r\n        {\r\n          type: \"help\",\r\n          capture: false,\r\n          prefixMinute: 3,\r\n          suffixMinute: 3,\r\n          backupDirectory: \"\",\r\n          account: \"\",\r\n          password: \"\",\r\n          keepDays: 90,\r\n          positionCapture: 3\r\n        },\r\n        {\r\n          type: \"enter\",\r\n          capture: false,\r\n          prefixMinute: 3,\r\n          suffixMinute: 3,\r\n          backupDirectory: \"\",\r\n          account: \"\",\r\n          password: \"\",\r\n          keepDays: 90,\r\n          positionCapture: 3\r\n        },\r\n        {\r\n          type: \"leave\",\r\n          capture: false,\r\n          prefixMinute: 3,\r\n          suffixMinute: 3,\r\n          backupDirectory: \"\",\r\n          account: \"\",\r\n          password: \"\",\r\n          keepDays: 90,\r\n          positionCapture: 3\r\n        },\r\n        {\r\n          type: \"fall\",\r\n          capture: false,\r\n          prefixMinute: 3,\r\n          suffixMinute: 3,\r\n          backupDirectory: \"\",\r\n          account: \"\",\r\n          password: \"\",\r\n          keepDays: 90,\r\n          positionCapture: 3\r\n        }\r\n      ],\r\n      cameraConfigList: [],\r\n      kmColumnsList: [\r\n        {\r\n          title: this.$t(\"LocaleString.L00047\"),\r\n          key: \"type\",\r\n          width: 100,\r\n          render: (h, params) => {\r\n            return h(\"span\", this.getObjectType(params.row.type));\r\n          }\r\n        },\r\n        {\r\n          title: this.$t(\"LocaleString.L30108\"),\r\n          key: \"longPress_cht\"\r\n        },\r\n        {\r\n          title: this.$t(\"LocaleString.L30109\"),\r\n          key: \"longPress_en\"\r\n        },\r\n        {\r\n          title: this.$t(\"LocaleString.L30110\"),\r\n          key: \"shortClick_cht\"\r\n        },\r\n        {\r\n          title: this.$t(\"LocaleString.L30111\"),\r\n          key: \"shortClick_en\"\r\n        },\r\n        {\r\n          title: this.$t(\"LocaleString.L30112\"),\r\n          key: \"doubleClick_cht\"\r\n        },\r\n        {\r\n          title: this.$t(\"LocaleString.L30113\"),\r\n          key: \"doubleClick_en\"\r\n        }\r\n      ],\r\n      kmColumnsEdit: [\r\n        {\r\n          title: this.$t(\"LocaleString.L00047\"),\r\n          slot: \"type\",\r\n          width: 100,\r\n          render: (h, params) => {\r\n            return h(\"span\", this.getObjectType(params.row.type));\r\n          }\r\n        },\r\n        {\r\n          title: this.$t(\"LocaleString.L30108\"),\r\n          slot: \"longPress_cht\"\r\n        },\r\n        {\r\n          title: this.$t(\"LocaleString.L30109\"),\r\n          slot: \"longPress_en\"\r\n        },\r\n        {\r\n          title: this.$t(\"LocaleString.L30110\"),\r\n          slot: \"shortClick_cht\"\r\n        },\r\n        {\r\n          title: this.$t(\"LocaleString.L30111\"),\r\n          slot: \"shortClick_en\"\r\n        },\r\n        {\r\n          title: this.$t(\"LocaleString.L30112\"),\r\n          slot: \"doubleClick_cht\"\r\n        },\r\n        {\r\n          title: this.$t(\"LocaleString.L30113\"),\r\n          slot: \"doubleClick_en\"\r\n        }\r\n      ],\r\n      kmConfigList: [\r\n        {\r\n          type: \"equipment\",\r\n          longPress_cht: \"\",\r\n          longPress_en: \"\",\r\n          shortClick_cht: \"\",\r\n          shortClick_en: \"\",\r\n          doubleClick_cht: \"\",\r\n          doubleClick_en: \"\"\r\n        },\r\n        {\r\n          type: \"people\",\r\n          longPress_cht: \"\",\r\n          longPress_en: \"\",\r\n          shortClick_cht: \"\",\r\n          shortClick_en: \"\",\r\n          doubleClick_cht: \"\",\r\n          doubleClick_en: \"\"\r\n        },\r\n        {\r\n          type: \"space\",\r\n          longPress_cht: \"\",\r\n          longPress_en: \"\",\r\n          shortClick_cht: \"\",\r\n          shortClick_en: \"\",\r\n          doubleClick_cht: \"\",\r\n          doubleClick_en: \"\"\r\n        },\r\n        {\r\n          type: \"other\",\r\n          longPress_cht: \"\",\r\n          longPress_en: \"\",\r\n          shortClick_cht: \"\",\r\n          shortClick_en: \"\",\r\n          doubleClick_cht: \"\",\r\n          doubleClick_en: \"\"\r\n        }\r\n      ],\r\n      kmConfigListEdit: [\r\n        {\r\n          type: \"equipment\",\r\n          longPress_cht: \"\",\r\n          longPress_en: \"\",\r\n          shortClick_cht: \"\",\r\n          shortClick_en: \"\",\r\n          doubleClick_cht: \"\",\r\n          doubleClick_en: \"\"\r\n        },\r\n        {\r\n          type: \"people\",\r\n          longPress_cht: \"\",\r\n          longPress_en: \"\",\r\n          shortClick_cht: \"\",\r\n          shortClick_en: \"\",\r\n          doubleClick_cht: \"\",\r\n          doubleClick_en: \"\"\r\n        },\r\n        {\r\n          type: \"space\",\r\n          longPress_cht: \"\",\r\n          longPress_en: \"\",\r\n          shortClick_cht: \"\",\r\n          shortClick_en: \"\",\r\n          doubleClick_cht: \"\",\r\n          doubleClick_en: \"\"\r\n        },\r\n        {\r\n          type: \"other\",\r\n          longPress_cht: \"\",\r\n          longPress_en: \"\",\r\n          shortClick_cht: \"\",\r\n          shortClick_en: \"\",\r\n          doubleClick_cht: \"\",\r\n          doubleClick_en: \"\"\r\n        }\r\n      ],\r\n      columns: [\r\n        {\r\n          title: this.$t(\"LocaleString.L00183\"),\r\n          slot: \"item\"\r\n          // width: 360\r\n        },\r\n        {\r\n          title: this.$t(\"LocaleString.L00184\"),\r\n          slot: \"value\"\r\n        }\r\n      ],\r\n      dataSection0: [\r\n        {\r\n          item: this.$t(\"LocaleString.F00004\"),\r\n          section: 23,\r\n          value: \"\"\r\n        }\r\n      ],\r\n      dataSection01: [\r\n        {\r\n          item: this.$t(\"LocaleString.L30118\"),\r\n          section: 0,\r\n          value: \"\"\r\n        },\r\n        {\r\n          item: this.$t(\"LocaleString.L30134\"),\r\n          section: 17,\r\n          value: \"\"\r\n        },\r\n        {\r\n          item: this.$t(\"LocaleString.L00446\"),\r\n          section: 31,\r\n          value: \"\"\r\n        }\r\n      ],\r\n      data: [\r\n        {\r\n          item: this.$t(\"LocaleString.L30138\"),\r\n          section: 18,\r\n          value: \"\"\r\n        },\r\n        {\r\n          item: this.$t(\"LocaleString.L30188\"),\r\n          section: 30,\r\n          value: \"\"\r\n        },\r\n        {\r\n          item: this.$t(\"LocaleString.L30057\"),\r\n          section: 1,\r\n          value: \"\"\r\n        },\r\n        {\r\n          item: this.$t(\"LocaleString.L30055\"),\r\n          section: 2,\r\n          value: \"\"\r\n        },\r\n        {\r\n          item: this.$t(\"LocaleString.L30056\"),\r\n          section: 3,\r\n          value: \"\"\r\n        },\r\n        {\r\n          item: this.$t(\"LocaleString.L30058\"),\r\n          section: 4,\r\n          value: \"\"\r\n        },\r\n        {\r\n          item: this.$t(\"LocaleString.L30060\"),\r\n          section: 5,\r\n          value: \"\"\r\n        },\r\n        {\r\n          item: this.$t(\"LocaleString.L30061\"),\r\n          section: 6,\r\n          value: \"\"\r\n        },\r\n        {\r\n          item: this.$t(\"LocaleString.L30062\"),\r\n          section: 7,\r\n          value: 1\r\n        },\r\n        {\r\n          item: this.$t(\"LocaleString.L30085\"),\r\n          section: 8,\r\n          value: \"\"\r\n        },\r\n        {\r\n          item: this.$t(\"LocaleString.L30101\"),\r\n          section: 11,\r\n          value: \"\"\r\n        },\r\n        {\r\n          item: this.$t(\"LocaleString.L30103\"),\r\n          section: 12,\r\n          value: \"\",\r\n          valueDevices: []\r\n        },\r\n        {\r\n          item: this.$t(\"LocaleString.L30156\"),\r\n          section: 22,\r\n          value: \"\",\r\n          valueDevices: []\r\n        },\r\n        {\r\n          item: this.$t(\"LocaleString.L30104\"),\r\n          section: 13,\r\n          value: \"\",\r\n          valueDevices: []\r\n        },\r\n        {\r\n          item: this.$t(\"LocaleString.L30115\"),\r\n          section: 15,\r\n          value: \"\",\r\n          valueDevices: []\r\n        },\r\n        {\r\n          item: this.$t(\"LocaleString.L30116\"),\r\n          section: 14,\r\n          value: \"\",\r\n          valueSkipEvent: []\r\n        },\r\n        {\r\n          item: this.$t(\"LocaleString.L30129\"),\r\n          section: 16,\r\n          value: \"\",\r\n          valueDeviceSelectList: []\r\n        },\r\n        {\r\n          item: this.$t(\"LocaleString.L30136\"),\r\n          section: 19,\r\n          value: \"\",\r\n          valueM1LongPressList: []\r\n        },\r\n        {\r\n          item: this.$t(\"LocaleString.L30159\"),\r\n          section: 24,\r\n          value: \"\"\r\n        },\r\n        {\r\n          item: this.$t(\"LocaleString.L30153\"),\r\n          section: 20,\r\n          value: \"\"\r\n        },\r\n        {\r\n          item: this.$t(\"LocaleString.L30154\"),\r\n          section: 21,\r\n          value: \"\"\r\n        },\r\n        {\r\n          item: this.$t(\"LocaleString.L30182\"),\r\n          section: 25,\r\n          value: \"\"\r\n        },\r\n\r\n        {\r\n          item: this.$t(\"LocaleString.L30183\"),\r\n          section: 26,\r\n          value: \"\"\r\n        },\r\n        {\r\n          item: this.$t(\"LocaleString.L30184\"),\r\n          section: 27,\r\n          value: \"\"\r\n        },\r\n\r\n        {\r\n          item: this.$t(\"LocaleString.L30185\"),\r\n          section: 28,\r\n          value: \"\"\r\n        },\r\n\r\n        {\r\n          item: this.$t(\"LocaleString.L30186\"),\r\n          section: 29,\r\n          value: \"\"\r\n        },\r\n        {\r\n          item: this.$t(\"LocaleString.L30214\"),\r\n          section: 32,\r\n          value: \"\"\r\n        },\r\n        {\r\n          item: this.$t(\"LocaleString.L30082\"),\r\n          section: 9,\r\n          value: \"\"\r\n        },\r\n        {\r\n          item: this.$t(\"LocaleString.L30083\"),\r\n          section: 10,\r\n          value: \"\"\r\n        }\r\n      ],\r\n      iconSizeList: [\r\n        {\r\n          label: \"32*32\",\r\n          value: \"32\"\r\n        },\r\n        {\r\n          label: \"40*40\",\r\n          value: \"40\"\r\n        }\r\n      ],\r\n      m5ObjectTypeList: [\r\n        {\r\n          label: this.$t(\"LocaleString.D00002\"),\r\n          value: \"all\"\r\n        },\r\n        {\r\n          label: this.$t(\"LocaleString.D00007\"),\r\n          value: \"people\"\r\n        },\r\n        {\r\n          label: this.$t(\"LocaleString.D00008\"),\r\n          value: \"equipment\"\r\n        },\r\n        {\r\n          label: this.$t(\"LocaleString.D00009\"),\r\n          value: \"space\"\r\n        },\r\n        {\r\n          label: this.$t(\"LocaleString.D00010\"),\r\n          value: \"other\"\r\n        }\r\n      ],\r\n      pageSizeList: [\r\n        {\r\n          label: \"10/\" + this.$t(\"LocaleString.L00020\"),\r\n          value: \"10\"\r\n        },\r\n        {\r\n          label: \"25/\" + this.$t(\"LocaleString.L00020\"),\r\n          value: \"25\"\r\n        },\r\n        {\r\n          label: \"50/\" + this.$t(\"LocaleString.L00020\"),\r\n          value: \"50\"\r\n        },\r\n        {\r\n          label: \"100/\" + this.$t(\"LocaleString.L00020\"),\r\n          value: \"100\"\r\n        }\r\n      ],\r\n      showingTab: [\r\n        {\r\n          label: this.$t(\"LocaleString.D30031\"),\r\n          value: \"m1\"\r\n        },\r\n        {\r\n          label: this.$t(\"LocaleString.D30032\"),\r\n          value: \"m2\"\r\n        },\r\n        {\r\n          label: this.$t(\"LocaleString.D30033\"),\r\n          value: \"m3\"\r\n        },\r\n        {\r\n          label: this.$t(\"LocaleString.D30034\"),\r\n          value: \"m4\"\r\n        },\r\n        {\r\n          label: this.$t(\"LocaleString.D30035\"),\r\n          value: \"m5\"\r\n        },\r\n        {\r\n          label: this.$t(\"LocaleString.D30036\"),\r\n          value: \"m6\"\r\n        },\r\n        {\r\n          label: this.$t(\"LocaleString.F30016\"),\r\n          value: \"m8\"\r\n        },\r\n        {\r\n          label: this.$t(\"LocaleString.D30044\"),\r\n          value: \"m9\"\r\n        }\r\n        // {\r\n        //   label: \"軌跡監控\",\r\n        //   value: \"m7\",\r\n        // },\r\n      ],\r\n      showingType: [\r\n        {\r\n          label: this.$t(\"LocaleString.L30059\"),\r\n          value: \"temperature\"\r\n        },\r\n        {\r\n          label: this.$t(\"LocaleString.L30010\"),\r\n          value: \"heartRate\"\r\n        },\r\n        {\r\n          label: this.$t(\"LocaleString.L30013\"),\r\n          value: \"bloodOxygen\"\r\n        },\r\n        {\r\n          label: this.$t(\"LocaleString.L30037\"),\r\n          value: \"bloodPressure\"\r\n        },\r\n        {\r\n          label: this.$t(\"LocaleString.L30208\"),\r\n          value: \"step\"\r\n        },\r\n        {\r\n          label: this.$t(\"LocaleString.L30105\"),\r\n          value: \"breathe\"\r\n        }\r\n      ],\r\n      defaultTabList: [],\r\n      defaultUpdateSeconds: [\r\n        {\r\n          label: \"0\",\r\n          value: \"0\"\r\n        },\r\n        {\r\n          label: \"30\",\r\n          value: \"30\"\r\n        },\r\n        {\r\n          label: \"60\",\r\n          value: \"60\"\r\n        },\r\n        {\r\n          label: \"120\",\r\n          value: \"120\"\r\n        }\r\n      ],\r\n      defaultInfluxRange: [\r\n        {\r\n          label: \"8\",\r\n          value: 8\r\n        },\r\n        {\r\n          label: \"12\",\r\n          value: 12\r\n        },\r\n        {\r\n          label: \"24\",\r\n          value: 24\r\n        },\r\n        {\r\n          label: \"48\",\r\n          value: 48\r\n        },\r\n        {\r\n          label: \"72\",\r\n          value: 72\r\n        }\r\n      ]\r\n    };\r\n  },\r\n  created() {},\r\n  async mounted() {\r\n    console.log(\"load systemConfig\");\r\n    await this.getEventLocaleStringList();\r\n    await this.getServiceCodeMenu();\r\n    this.getDeviceTypeMenu();\r\n    this.loadKMData();\r\n    this.loadCameraData();\r\n    this.loadStatisticLineData();\r\n    this.isShow = true;\r\n    let identity = localStorage.getItem(\"sns_identity\");\r\n    this.isAdmin = identity == \"true\" ? true : false;\r\n\r\n    let permission = localStorage.getItem(\"sns_permission\");\r\n    if (permission.includes(\"[sns]-[1]-[view]\") || identity === \"true\") {\r\n      this.data = [...this.dataSection01, ...this.data];\r\n    }\r\n    if (identity === \"true\") {\r\n      this.data = [...this.dataSection0, ...this.data];\r\n    }\r\n    this.cameraConfigList = this.defaultCameraConfigList;\r\n    this.statisticLineConfigList = this.defaultStatisticLineConfigList;\r\n    window.addEventListener(\"resize\", this.resizeHeight);\r\n    //this.getSystemConfig();\r\n    // this.normalizeHeight();\r\n  },\r\n  beforeDestroy() {\r\n    window.removeEventListener(\"resize\", this.resizeHeight);\r\n  },\r\n  methods: {\r\n    async testConnection() {\r\n      let errorMsg = [\r\n        this.$t(\"LocaleString.L30196\"),\r\n        \"UserName\",\r\n        \"CredentialSecret\"\r\n      ];\r\n      if (\r\n        this.thirdPartyData.url == \"\" ||\r\n        this.thirdPartyData.userName == \"\" ||\r\n        this.thirdPartyData.secret == \"\"\r\n      ) {\r\n        this.$Notice.error({\r\n          title: this.$t(\"LocaleString.E00037\"),\r\n          desc: this.$t(\"LocaleString.W00038\", { 0: errorMsg.join(\",\") }),\r\n          duration: Config.WARNING_DURATION\r\n        });\r\n        return;\r\n      }\r\n      this.$serviceThirdParty.testConnection.requestCommon(\r\n        this.thirdPartyData.url\r\n      );\r\n      let result = await this.$serviceThirdParty.testConnection.send(\r\n        null,\r\n        null,\r\n        {\r\n          userName: this.thirdPartyData.userName,\r\n          secret: this.thirdPartyData.secret\r\n        }\r\n      );\r\n      if (result) {\r\n        this.$Notice.success({\r\n          title: this.$t(\"LocaleString.M00004\"),\r\n          desc:\r\n            this.$t(\"LocaleString.D00063\") +\r\n            \" \" +\r\n            this.$t(\"LocaleString.D00167\"),\r\n          duration: Config.SUCCESS_DURATION\r\n        });\r\n      }\r\n    },\r\n    getEditPermission() {\r\n      let isAllow = false;\r\n\r\n      let permission = localStorage.getItem(\"sns_permission\");\r\n      if (permission.includes(\"[sns]-[1]-[edit]\") || this.isAdmin) {\r\n        isAllow = true;\r\n      }\r\n      return isAllow;\r\n    },\r\n\r\n    switchloginThirdParty(entry) {\r\n      if (!entry) {\r\n        this.thirdPartyData.url = \"\";\r\n        this.thirdPartyData.userName = \"\";\r\n        this.thirdPartyData.secret = \"\";\r\n      }\r\n    },\r\n    resizeHeight() {\r\n      this.modalInnerHeight =\r\n        this.currentTab == \"system\" ? window.innerHeight - 300 : 250;\r\n    },\r\n    async getServiceCodeMenu() {\r\n      let configParams = {\r\n        // hasLicense: true,\r\n        category: \"event\"\r\n      };\r\n      let tmperviceCodeList = await this.$service.getServices.send(\r\n        configParams\r\n      );\r\n      this.serviceCodeList = tmperviceCodeList.results;\r\n      this.serviceCodeList.forEach(item => {\r\n        let transName = this.serviceCodeLocaleStringsList.some(\r\n          data => data.key == item.code\r\n        )\r\n          ? this.serviceCodeLocaleStringsList.find(\r\n              data => data.key == item.code\r\n            ).value\r\n          : item.code;\r\n\r\n        item.key = item.code;\r\n        item.value = transName;\r\n      });\r\n    },\r\n    copyToken() {\r\n      navigator.clipboard.writeText(this.token);\r\n      this.$Notice.success({\r\n        title: this.$t(\"LocaleString.M00004\"),\r\n        desc: this.$t(\"LocaleString.M30006\"),\r\n        duration: Config.SUCCESS_DURATION\r\n      });\r\n    },\r\n    async generateToken() {\r\n      let postData = {\r\n        account: this.loginAccount,\r\n        password: this.loginPassword\r\n      };\r\n\r\n      let valid = await this.$service.getAuthTokenNoload.send(postData);\r\n      if (valid) {\r\n        let res = await this.$service.generateToken.send(postData);\r\n        this.token = location.origin + \"/sns/#/user/login?direct=\" + res;\r\n\r\n        this.$Notice.success({\r\n          title: this.$t(\"LocaleString.M00004\"),\r\n          desc: this.$t(\"LocaleString.M30007\"),\r\n          duration: Config.SUCCESS_DURATION\r\n        });\r\n      }\r\n    },\r\n    async testCameraConnection(row) {\r\n      let errorMessage = \"\";\r\n      if (row.backupDirectory == \"\") {\r\n        errorMessage += this.$t(\"LocaleString.W00038\", {\r\n          0: this.$t(\"LocaleString.L30143\")\r\n        });\r\n      }\r\n      if (row.account == \"\") {\r\n        errorMessage += this.$t(\"LocaleString.W00038\", {\r\n          0: this.$t(\"LocaleString.L30144\")\r\n        });\r\n      }\r\n      if (row.password == \"\") {\r\n        errorMessage += this.$t(\"LocaleString.W00038\", {\r\n          0: this.$t(\"LocaleString.L00003\")\r\n        });\r\n      }\r\n      if (errorMessage != \"\") {\r\n        this.$Notice.warning({\r\n          title: this.$t(\"LocaleString.W00005\"),\r\n          desc: errorMessage,\r\n          duration: Config.WARNING_DURATION\r\n        });\r\n        return false;\r\n      }\r\n\r\n      let params = {\r\n        filepath: row.backupDirectory,\r\n        username: row.account,\r\n        userAuth: Base64.encode(row.password)\r\n      };\r\n\r\n      let connetResult = await this.$service.testCameraConnection.send(params);\r\n      if (connetResult == \"\") {\r\n        this.$Notice.success({\r\n          title: this.$t(\"LocaleString.M00004\"), //成功\r\n          desc:\r\n            this.$t(\"LocaleString.B30010\") +\r\n            \" \" +\r\n            this.$t(\"LocaleString.M00004\"),\r\n          duration: Config.WARNING_DURATION\r\n        });\r\n      }\r\n    },\r\n    async onCameraDataChange(type, row, index) {\r\n      switch (type) {\r\n        case \"capture\":\r\n          this.cameraConfigList[index].capture = row.capture;\r\n          break;\r\n        case \"prefixMinute\":\r\n          this.cameraConfigList[index].prefixMinute = row.prefixMinute;\r\n          break;\r\n        case \"suffixMinute\":\r\n          this.cameraConfigList[index].suffixMinute = row.suffixMinute;\r\n          break;\r\n        case \"backupDirectory\":\r\n          this.cameraConfigList[index].backupDirectory = row.backupDirectory;\r\n          break;\r\n        case \"account\":\r\n          this.cameraConfigList[index].account = row.account;\r\n          break;\r\n        case \"password\":\r\n          this.cameraConfigList[index].password = row.password;\r\n          break;\r\n        case \"keepDays\":\r\n          this.cameraConfigList[index].keepDays = row.keepDays;\r\n          break;\r\n        case \"positionCapture\":\r\n          this.cameraConfigList[index].positionCapture = row.positionCapture;\r\n          break;\r\n      }\r\n      // console.log(this.cameraConfigList);\r\n    },\r\n\r\n    async onStatisticLineDataChange(type, row, index) {\r\n      switch (type) {\r\n        case \"interval\":\r\n          this.statisticLineConfigList[index].interval = row.interval;\r\n          break;\r\n        case \"dataType\":\r\n          this.statisticLineConfigList[index].dataType = row.dataType;\r\n          break;\r\n        case \"warningLine\":\r\n          this.statisticLineConfigList[index].warningLine = row.warningLine;\r\n          break;\r\n      }\r\n      // console.log(this.cameraConfigList);\r\n    },\r\n    /**\r\n      when change device default select List action\r\n      1. default is all\r\n      2. when disable all, auto insert device type which exist from console\r\n      3. when diable a device type which is included in DefaultDeviceTypeList(from API) , it will show alert dialog and reinsert to selection\r\n     **/\r\n    openDeviceSelection(data) {\r\n      this.openDeviceSelect = data;\r\n    },\r\n    changeDeviceDefaultSelectList(item) {\r\n      if (this.openDeviceSelect) {\r\n        if (item.filter(data => data == \"all\").length > 0) {\r\n          this.configData.deviceSelectList = [\"all\"];\r\n        } else {\r\n          this.$service.getDefaultDeviceTypeList\r\n            .send(null)\r\n            .then(defaultList => {\r\n              let defaultSelectList = defaultList.deviceList.split(\",\");\r\n              if (item.length == 0) {\r\n                this.configData.deviceSelectList = defaultSelectList;\r\n              } else {\r\n                defaultSelectList.forEach(item => {\r\n                  if (!this.configData.deviceSelectList.includes(item)) {\r\n                    let deviceName = \"\";\r\n                    if (\r\n                      this.deviceDefaultSelection.some(data =>\r\n                        item.includes(data.key)\r\n                      )\r\n                    ) {\r\n                      deviceName = this.deviceDefaultSelection.find(data =>\r\n                        item.includes(data.key)\r\n                      ).value;\r\n                      this.$Notice.error({\r\n                        title: this.$t(\"LocaleString.E00037\"),\r\n                        desc: this.$t(\"LocaleString.W30011\", { 0: deviceName }),\r\n                        duration: Config.errorDuration\r\n                      });\r\n                      this.configData.deviceSelectList.push(item);\r\n                    }\r\n                  }\r\n                });\r\n              }\r\n            });\r\n        }\r\n      }\r\n    },\r\n    async getEventLocaleStringList() {\r\n      let params = {\r\n        search: \"\"\r\n      };\r\n      let tmp = [];\r\n      let res = await this.$service.getServiceCodeLocaleStrings.send(params);\r\n      res.results.forEach(r => {\r\n        tmp.push({\r\n          key: r.code,\r\n          value: this.$t(\"LocaleString.\" + r.langs.nameId)\r\n        });\r\n      });\r\n      this.serviceCodeLocaleStringsList = JSON.parse(JSON.stringify(tmp));\r\n    },\r\n    changeMultiPlanes(val) {\r\n      this.configData.selectedPlanes = val;\r\n    },\r\n    switchMultiPlanes(val) {\r\n      if (!val) {\r\n        this.configData.selectedPlanes = [];\r\n      }\r\n    },\r\n    changeTab(e) {\r\n      this.currentTab = e;\r\n      this.resizeHeight();\r\n    },\r\n    transEventName(serviceCode) {\r\n      // console.log(\r\n      //   \"transEventName: \" + JSON.stringify(this.serviceCodeLocaleStringsList)\r\n      // );\r\n      let rtnString = \"\";\r\n      if (\r\n        this.serviceCodeLocaleStringsList &&\r\n        this.serviceCodeLocaleStringsList.length > 0\r\n      ) {\r\n        rtnString = this.serviceCodeLocaleStringsList.find(\r\n          item => item.key === serviceCode\r\n        ).value;\r\n      }\r\n      return rtnString;\r\n    },\r\n    getDeviceListLossSignalLowBatteryAbnormalDevice() {\r\n      let tempDeviceListLossSignal = [];\r\n      let tempDeviceListLossSignal2 = [];\r\n      let tempDeviceListLowBattery = [];\r\n      let tempDeviceListAbnormalDevice = [];\r\n      this.allDeviceTypeMenu.forEach(item => {\r\n        if (item.supportDataEvent) {\r\n          if (\r\n            item.supportDataEvent.some(i => i.serviceCode === \"LossSignal\") &&\r\n            item.isBleDevice\r\n          ) {\r\n            tempDeviceListLossSignal.push({\r\n              key: item.type,\r\n              value: this.$t(\"LocaleString.\" + item.langs.nameId)\r\n            });\r\n          }\r\n          if (\r\n            item.supportDataEvent.some(i => i.serviceCode === \"LossSignal\") &&\r\n            !item.isBleDevice\r\n          ) {\r\n            tempDeviceListLossSignal2.push({\r\n              key: item.type,\r\n              value: this.$t(\"LocaleString.\" + item.langs.nameId)\r\n            });\r\n          }\r\n          if (item.supportDataEvent.some(i => i.serviceCode === \"LowBattery\")) {\r\n            tempDeviceListLowBattery.push({\r\n              key: item.type,\r\n              value: this.$t(\"LocaleString.\" + item.langs.nameId)\r\n            });\r\n          }\r\n          if (\r\n            item.supportDataEvent.some(i => i.serviceCode === \"AbnormalDevice\")\r\n          ) {\r\n            tempDeviceListAbnormalDevice.push({\r\n              key: item.type,\r\n              value: this.$t(\"LocaleString.\" + item.langs.nameId)\r\n            });\r\n          }\r\n        }\r\n      });\r\n      this.deviceListLossSignal = Array.from(new Set(tempDeviceListLossSignal));\r\n      this.deviceListLossSignal2 = Array.from(\r\n        new Set(tempDeviceListLossSignal2)\r\n      );\r\n      this.deviceListLowBattery = Array.from(new Set(tempDeviceListLowBattery));\r\n      this.deviceListAbnormalDevice = Array.from(\r\n        new Set(tempDeviceListAbnormalDevice)\r\n      );\r\n    },\r\n    getEventServiceCodeList() {\r\n      let tmpEventServiceCodeList = [];\r\n      this.allDeviceTypeMenu.forEach(item => {\r\n        if (item.supportDataEvent) {\r\n          let scList = item.supportDataEvent.map(e => e.serviceCode);\r\n          tmpEventServiceCodeList = [...tmpEventServiceCodeList, ...scList];\r\n        }\r\n      });\r\n\r\n      tmpEventServiceCodeList = Array.from(new Set(tmpEventServiceCodeList));\r\n      tmpEventServiceCodeList.forEach(item => {\r\n        this.eventServiceCodeList.push({\r\n          key: item,\r\n          value: this.transEventName(item)\r\n        });\r\n      });\r\n    },\r\n    async getDeviceTypeMenu() {\r\n      let deviceTypesParams = new URLSearchParams();\r\n      deviceTypesParams.append(\"active\", true);\r\n      this.allDeviceTypeMenu = await this.$service.getDeviceTypesmenu.send(\r\n        deviceTypesParams\r\n      );\r\n      this.getDeviceSelectList();\r\n      this.getEventServiceCodeList();\r\n      this.getDeviceListLossSignalLowBatteryAbnormalDevice();\r\n      this.loadLogo();\r\n      this.loadSound();\r\n      this.loadConfig();\r\n      this.loadThirdParty();\r\n    },\r\n    getDeviceSelectList() {\r\n      this.deviceDefaultSelection = this.allDeviceTypeMenu.map(item => ({\r\n        key: item.type,\r\n        value: this.$t(\"LocaleString.\" + item.langs.nameId)\r\n      }));\r\n      this.deviceDefaultSelection = [\r\n        ...[{ key: \"all\", value: this.$t(\"LocaleString.D00002\") }],\r\n        ...this.deviceDefaultSelection\r\n      ];\r\n    },\r\n    ended() {\r\n      let audio = this.$refs.audio;\r\n      this.isPlay = false;\r\n      audio.pause();\r\n      audio.currentTime = 0;\r\n      document.querySelector(\".toggle-sound\").classList.add(\"paused\");\r\n    },\r\n    play(control) {\r\n      let audio = this.$refs.audio;\r\n      if (control != null || control != undefined) {\r\n        this.isPlay = false;\r\n        audio.pause();\r\n        audio.currentTime = 0;\r\n        document.querySelector(\".toggle-sound\").classList.add(\"paused\");\r\n        return;\r\n      }\r\n\r\n      if (\r\n        audio.paused &&\r\n        document.querySelector(\".toggle-sound\").classList.contains(\"paused\")\r\n      ) {\r\n        this.isPlay = true;\r\n        document.querySelector(\".toggle-sound\").classList.remove(\"paused\");\r\n        audio.play();\r\n      } else {\r\n        this.isPlay = false;\r\n        audio.pause();\r\n        audio.currentTime = 0;\r\n        document.querySelector(\".toggle-sound\").classList.add(\"paused\");\r\n      }\r\n    },\r\n    restImage() {\r\n      const preview = document.querySelector(\"#preview\");\r\n      preview.src = \"\";\r\n      this.showCleanIcon = false;\r\n      this.imgFileURL = null;\r\n    },\r\n\r\n    handleUploadImage(imgFile) {\r\n      let imgFileurl = null;\r\n      if (\r\n        imgFile.type.toLowerCase().includes(\"png\") ||\r\n        imgFile.type.toLowerCase().includes(\"jpg\") ||\r\n        imgFile.type.toLowerCase().includes(\"jpeg\")\r\n      ) {\r\n        const reader = new FileReader();\r\n        const preview = document.querySelector(\"#preview\");\r\n\r\n        reader.addEventListener(\r\n          \"load\",\r\n          function() {\r\n            preview.src = reader.result;\r\n          },\r\n          false\r\n        );\r\n\r\n        if (imgFile) {\r\n          reader.readAsDataURL(imgFile);\r\n        }\r\n        //\r\n        // if (window.createObjectURL != undefined) {\r\n        //   imgFileurl = window.createObjectURL(imgFile);\r\n        // } else if (window.URL != undefined) {\r\n        //   imgFileurl = window.URL.createObjectURL(imgFile);\r\n        // } else if (window.webkitURL != undefined) {\r\n        //   imgFileurl = window.webitURL.createObjectURL(imgFile);\r\n        // }\r\n        this.imgFile = imgFile;\r\n        this.imgFileURL = imgFileurl;\r\n        this.showCleanIcon = true;\r\n      } else {\r\n        this.$Notice.error({\r\n          title: this.$t(\"LocaleString.E00037\"),\r\n          desc: this.$t(\"LocaleString.E00040\", {\r\n            0: this.$t(\"LocaleString.L30134\")\r\n          }),\r\n          duration: Config.errorDuration\r\n        });\r\n      }\r\n    },\r\n    handleUpload(file) {\r\n      this.isPlay = false;\r\n      let audio = this.$refs.audio;\r\n      audio.pause();\r\n      document.querySelector(\".toggle-sound\").classList.add(\"paused\");\r\n\r\n      let audioElement = new FileReader();\r\n      audioElement.readAsDataURL(file);\r\n      audioElement.addEventListener(\"load\", () => {\r\n        if (!audioElement.result.includes(\"data:audio/\")) {\r\n          this.file = null;\r\n          this.fileURL = this.fileOriURL;\r\n          this.$Notice.error({\r\n            title: this.$t(\"LocaleString.E00037\"),\r\n            desc: this.$t(\"LocaleString.E00040\", {\r\n              0: this.$t(\"LocaleString.L30055\")\r\n            }),\r\n            duration: Config.errorDuration\r\n          });\r\n        } else {\r\n          this.file = file;\r\n          this.fileURL = audioElement.result;\r\n\r\n          setTimeout(() => {\r\n            if (audio.duration > 15) {\r\n              this.file = null;\r\n              this.fileURL = this.fileOriURL;\r\n              this.$Notice.error({\r\n                title: this.$t(\"LocaleString.E00037\"),\r\n                desc: this.$t(\"LocaleString.W30008\", { 0: \"15\" }),\r\n                duration: Config.errorDuration\r\n              });\r\n            } else {\r\n              this.file = file;\r\n              this.fileURL = audioElement.result;\r\n            }\r\n          }, 100);\r\n        }\r\n      });\r\n    },\r\n    changeDefaultTabList(item) {\r\n      // console.log('Sitem:'+JSON.stringify(item))\r\n      let arr = [];\r\n      item.forEach(element => {\r\n        let v = this.showingTab.find(t => t.value == element).label;\r\n        if (v) {\r\n          arr.push({\r\n            label: v,\r\n            value: element\r\n          });\r\n        }\r\n      });\r\n      this.defaultTabList = arr;\r\n    },\r\n    loadLogo() {\r\n      let pocGroupParams = {\r\n        inlinecount: true,\r\n        search: \"active eq true and category eq @SNS@Logo\"\r\n      };\r\n      this.$service.getPOCGroupsByCategory.send(pocGroupParams).then(pocRes => {\r\n        if (\r\n          pocRes.count > 0 &&\r\n          pocRes.results[0].files &&\r\n          pocRes.results[0].files.length > 0\r\n        ) {\r\n          this.imgFileURL = pocRes.results[0].files[0].url;\r\n          this.imgFileOriURL = pocRes.results[0].files[0].url;\r\n          this.showCleanIcon = true;\r\n        }\r\n      });\r\n    },\r\n    loadSound() {\r\n      let pocGroupParams = {\r\n        inlinecount: true,\r\n        search: \"active eq true and category eq @SNS@Sound\"\r\n      };\r\n      this.$service.getPOCGroupsByCategory.send(pocGroupParams).then(pocRes => {\r\n        if (\r\n          pocRes.count > 0 &&\r\n          pocRes.results[0].files &&\r\n          pocRes.results[0].files.length > 0\r\n        ) {\r\n          this.fileURL = pocRes.results[0].files[0].url;\r\n          this.fileOriURL = pocRes.results[0].files[0].url;\r\n        }\r\n      });\r\n    },\r\n    loadConfig() {\r\n      //let loginUserName = localStorage.getItem(\"sns_loginUser\");\r\n\r\n      let params = {\r\n        inlinecount: true,\r\n        search: \"category eq @SNS@Setting\"\r\n      };\r\n\r\n      let planeStr = \"active eq true and enable eq true\";\r\n      let planeParams = {\r\n        search: planeStr,\r\n        inlinecount: true,\r\n        // page: 0,\r\n        // size: 1,\r\n        sort: \"modifiesAt,desc\"\r\n        // active: true,\r\n      };\r\n\r\n      this.$service.getPlanes.send(planeParams).then(planeData => {\r\n        if (planeData.results.length > 0) {\r\n          this.planeList = planeData.results.map(item => ({\r\n            value: item.code,\r\n            label: item.name\r\n          }));\r\n        }\r\n        this.$service.getPOCProperties.send(params).then(res => {\r\n          if (res.count > 0 && res.results[0].properties.length > 0) {\r\n            res.results[0].properties.forEach(res => {\r\n              switch (res.key) {\r\n                case \"license\":\r\n                  this.configData.license =\r\n                    res.value != \"\" ? res.value.split(\",\") : [];\r\n                  break;\r\n                case \"systemName\":\r\n                  this.configData.systemName = res.value;\r\n                  break;\r\n                case \"tabList\":\r\n                  this.configData.tabList = res.value.split(\",\");\r\n                  let arr = [];\r\n                  this.configData.tabList.forEach(element => {\r\n                    let v = this.showingTab.find(t => t.value == element).label;\r\n                    if (v) {\r\n                      arr.push({\r\n                        label: v,\r\n                        value: element\r\n                      });\r\n                    }\r\n                  });\r\n                  this.defaultTabList = arr;\r\n                  break;\r\n                // case \"sound\":\r\n                //   break;\r\n                case \"m2List\":\r\n                  this.configData.m2List = res.value.split(\",\");\r\n                  break;\r\n                case \"iconSize\":\r\n                  this.configData.iconSize = res.value;\r\n                  break;\r\n                case \"defaultTab\":\r\n                  this.configData.defaultTab = res.value;\r\n                  this.oriTab = res.value;\r\n                  break;\r\n                case \"updateSeconds\":\r\n                  this.configData.updateSeconds = res.value;\r\n                  break;\r\n                case \"grayTime\":\r\n                  this.configData.grayTime = res.value * 1;\r\n                  break;\r\n                case \"EventAuthClose\":\r\n                  this.configData.eventAuthClose =\r\n                    res.value == \"true\" ? true : false;\r\n                  break;\r\n                case \"EventKeepDays\":\r\n                  this.configData.eventKeepDays = res.value * 1;\r\n                  break;\r\n                case \"InfluxRange\":\r\n                  this.configData.influxRange = res.value * 1;\r\n                  break;\r\n                case \"lossSignal\":\r\n                  this.configData.lossSignal = res.value * 1;\r\n                  break;\r\n                case \"lossSignal2\":\r\n                  this.configData.lossSignal2 = res.value * 1;\r\n                  break;\r\n                case \"lowBattery\":\r\n                  this.configData.lowBattery = res.value * 1;\r\n                  break;\r\n                case \"lossSignalDevices\":\r\n                  this.configData.lossSignalDevices = res.value.split(\",\");\r\n                  break;\r\n                case \"lossSignalDevices2\":\r\n                  this.configData.lossSignalDevices2 = res.value.split(\",\");\r\n                  break;\r\n                case \"lowBatteryDevices\":\r\n                  this.configData.lowBatteryDevices = res.value.split(\",\");\r\n                  break;\r\n                case \"abnormalDevices\":\r\n                  this.configData.abnormalDevices = res.value.split(\",\");\r\n                  break;\r\n                case \"skipEvent\":\r\n                  this.configData.skipEvent = res.value.split(\",\");\r\n                  break;\r\n                case \"deviceSelectList\":\r\n                  this.configData.deviceSelectList = res.value.split(\",\");\r\n                  break;\r\n                case \"multiPlanes\":\r\n                  this.configData.multiPlanes =\r\n                    res.value == \"true\" ? true : false;\r\n                  break;\r\n                case \"selectedPlanes\":\r\n                  this.configData.selectedPlanes = res.value.split(\",\");\r\n                  break;\r\n                case \"stuckPlane\":\r\n                  this.configData.stuckPlane =\r\n                    res.value == \"true\" ? true : false;\r\n                  break;\r\n                case \"m1LongPress\":\r\n                  this.configData.m1LongPress = res.value.split(\",\");\r\n                  break;\r\n                case \"defaultPageSize\":\r\n                  this.configData.defaultPageSize = res.value;\r\n                  break;\r\n                case \"m5ObjectType\":\r\n                  this.configData.m5ObjectType =\r\n                    res.value == \"\" ? \"all\" : res.value;\r\n                  break;\r\n                case \"showLeaveBed\":\r\n                  this.configData.showLeaveBed =\r\n                    res.value === \"true\" ? true : false;\r\n                  break;\r\n                case \"m2Statistic\":\r\n                  this.configData.m2Statistic = res.value * 1;\r\n                  break;\r\n                case \"m3Statistic\":\r\n                  this.configData.m3Statistic = res.value * 1;\r\n                  break;\r\n                case \"m4Statistic\":\r\n                  this.configData.m4Statistic = res.value * 1;\r\n                  break;\r\n                case \"m8Statistic\":\r\n                  this.configData.m8Statistic = res.value * 1;\r\n                  break;\r\n                case \"m9Statistic\":\r\n                  this.configData.m9Statistic = res.value * 1;\r\n                  break;\r\n                case \"stateColor\":\r\n                  this.configData.stateColor = res.value;\r\n                  break;\r\n                case \"repeatSound\":\r\n                  this.configData.repeatSound = res.value * 1;\r\n                  break;\r\n                case \"logoutTime\":\r\n                  this.configData.logoutTime = res.value * 1;\r\n                  break;\r\n              }\r\n            });\r\n            this.configData.sound =\r\n              localStorage.getItem(\"sns_alertSound\") == \"true\" ? true : false;\r\n            this.configData.m2Line =\r\n              localStorage.getItem(\"sns_m2Line\") == \"true\" ? true : false;\r\n            //\r\n\r\n            this.origConfigData = JSON.parse(JSON.stringify(this.configData));\r\n            this.updateValue(this.origConfigData);\r\n          } else {\r\n            tgus;\r\n            this.editHandleSubmitNew(\"new\");\r\n          }\r\n        });\r\n      });\r\n    },\r\n    loadThirdParty() {\r\n      //let loginUserName = localStorage.getItem(\"sns_loginUser\");\r\n\r\n      let params = {\r\n        inlinecount: true,\r\n        search: \"category eq @SNS@SettingThirdParty\"\r\n      };\r\n\r\n      this.$service.getPOCProperties.send(params).then(res => {\r\n        if (res.count > 0 && res.results[0].properties.length > 0) {\r\n          res.results[0].properties.forEach(res => {\r\n            switch (res.key) {\r\n              case \"loginThirdParty\":\r\n                this.thirdPartyData.loginThirdParty =\r\n                  res.value == \"true\" ? true : false;\r\n                break;\r\n              case \"url\":\r\n                this.thirdPartyData.url = res.value;\r\n                break;\r\n              case \"userName\":\r\n                this.thirdPartyData.userName = res.value;\r\n                break;\r\n              case \"secret\":\r\n                this.thirdPartyData.secret = res.value;\r\n                break;\r\n            }\r\n          });\r\n          this.origThirdPartyData = JSON.parse(\r\n            JSON.stringify(this.thirdPartyData)\r\n          );\r\n          this.updateThirdPartyValue(this.origThirdPartyData);\r\n        }\r\n      });\r\n    },\r\n    checkEditHandleSubmitNew() {\r\n      //\r\n      let errorDesc = \"\";\r\n      let tabList = this.configData.tabList;\r\n      let m2List = this.configData.m2List;\r\n      let defaultTab = this.configData.defaultTab;\r\n      let grayTime = this.configData.grayTime;\r\n      let eventAuthClose = this.configData.eventAuthClose;\r\n      let eventKeepDays = this.configData.eventKeepDays;\r\n      let lowBattery = this.configData.lowBattery;\r\n      let lossSignal = this.configData.lossSignal;\r\n      let lossSignal2 = this.configData.lossSignal2;\r\n      let lowBatteryDevices = this.configData.lowBatteryDevices;\r\n      let lossSignalDevices = this.configData.lossSignalDevices;\r\n      let lossSignalDevices2 = this.configData.lossSignalDevices2;\r\n      let abnormalDevices = this.configData.abnormalDevices;\r\n      let skipEvent = this.configData.skipEvent;\r\n      let deviceSelectList = this.configData.deviceSelectList;\r\n      let multiPlanes = this.configData.multiPlanes;\r\n      let selectedPlanes = this.configData.selectedPlanes;\r\n      let stuckPlane = this.configData.stuckPlane;\r\n      let m1LognPress = this.configData.m1LongPress;\r\n      let showLeaveBed = this.configData.showLeaveBed;\r\n      let m2Statistic = this.configData.m2Statistic;\r\n      let m3Statistic = this.configData.m3Statistic;\r\n      let m4Statistic = this.configData.m4Statistic;\r\n      let m8Statistic = this.configData.m8Statistic;\r\n      let m9Statistic = this.configData.m9Statistic;\r\n      let stateColor = this.configData.stateColor;\r\n      let logoutTime = this.configData.logoutTime;\r\n      // let iconSize = this.configData.iconSize\r\n      if (tabList.length < 1) {\r\n        errorDesc +=\r\n          this.$t(\"LocaleString.M00010\", {\r\n            0: this.$t(\"LocaleString.L30057\")\r\n          }) + \"<br>\";\r\n      }\r\n      if (m2List.length < 1) {\r\n        errorDesc +=\r\n          this.$t(\"LocaleString.M00010\", {\r\n            0: this.$t(\"LocaleString.L30056\")\r\n          }) + \"<br>\";\r\n      }\r\n      if (m2List.length > 4) {\r\n        errorDesc += this.$t(\"LocaleString.W30002\") + \"<br>\";\r\n      }\r\n      if (!defaultTab) {\r\n        errorDesc +=\r\n          this.$t(\"LocaleString.M00010\", {\r\n            0: this.$t(\"LocaleString.L30060\")\r\n          }) + \"<br>\";\r\n      }\r\n      if (grayTime == null) {\r\n        errorDesc +=\r\n          this.$t(\"LocaleString.L00001\", {\r\n            0: this.$t(\"LocaleString.L30062\")\r\n          }) + \"<br>\";\r\n      }\r\n      if (eventAuthClose == true) {\r\n        if (eventKeepDays == null) {\r\n          errorDesc +=\r\n            this.$t(\"LocaleString.L00001\", {\r\n              0: this.$t(\"LocaleString.L30083\")\r\n            }) + \"<br>\";\r\n        }\r\n      }\r\n      if (lowBattery == null) {\r\n        errorDesc +=\r\n          this.$t(\"LocaleString.L00001\", {\r\n            0: this.$t(\"LocaleString.L30104\")\r\n          }) + \"<br>\";\r\n      } else {\r\n        if (lowBattery < 10) {\r\n          errorDesc +=\r\n            this.$t(\"LocaleString.L30104\") +\r\n            this.$t(\"LocaleString.W30009\", {\r\n              0: 10,\r\n              1: 60\r\n            }) +\r\n            \"<br>\";\r\n        }\r\n      }\r\n      if (lossSignal == null) {\r\n        errorDesc +=\r\n          this.$t(\"LocaleString.L00001\", {\r\n            0: this.$t(\"LocaleString.L30103\")\r\n          }) + \"<br>\";\r\n      } else {\r\n        if (lossSignal < 1) {\r\n          errorDesc +=\r\n            this.$t(\"LocaleString.L30103\") +\r\n            this.$t(\"LocaleString.W30009\", {\r\n              0: 1,\r\n              1: 60\r\n            }) +\r\n            \"<br>\";\r\n        }\r\n      }\r\n      if (deviceSelectList.length < 1) {\r\n        errorDesc +=\r\n          this.$t(\"LocaleString.M00010\", {\r\n            0: this.$t(\"LocaleString.L30129\")\r\n          }) + \"<br>\";\r\n      }\r\n      if (\r\n        multiPlanes &&\r\n        (selectedPlanes.length < 1 ||\r\n          (selectedPlanes.length == 1 && selectedPlanes[0] == \"\"))\r\n      ) {\r\n        errorDesc +=\r\n          this.$t(\"LocaleString.M00010\", {\r\n            0: this.$t(\"LocaleString.L30138\")\r\n          }) + \"<br>\";\r\n      }\r\n\r\n      if (m2Statistic == null) {\r\n        errorDesc +=\r\n          this.$t(\"LocaleString.L00001\", {\r\n            0: this.$t(\"LocaleString.L30182\")\r\n          }) + \"<br>\";\r\n      }\r\n      if (m3Statistic == null) {\r\n        errorDesc +=\r\n          this.$t(\"LocaleString.L00001\", {\r\n            0: this.$t(\"LocaleString.L30183\")\r\n          }) + \"<br>\";\r\n      }\r\n      if (m4Statistic == null) {\r\n        errorDesc +=\r\n          this.$t(\"LocaleString.L00001\", {\r\n            0: this.$t(\"LocaleString.L30184\")\r\n          }) + \"<br>\";\r\n      }\r\n      if (m8Statistic == null) {\r\n        errorDesc +=\r\n          this.$t(\"LocaleString.L00001\", {\r\n            0: this.$t(\"LocaleString.L30185\")\r\n          }) + \"<br>\";\r\n      }\r\n      if (m9Statistic == null) {\r\n        errorDesc +=\r\n          this.$t(\"LocaleString.L00001\", {\r\n            0: this.$t(\"LocaleString.L30186\")\r\n          }) + \"<br>\";\r\n      }\r\n      if (stateColor == \"\") {\r\n        errorDesc += this.$t(\"LocaleString.W30019\") + \"<br>\";\r\n      }\r\n      if (logoutTime == null) {\r\n        errorDesc +=\r\n          this.$t(\"LocaleString.L00001\", {\r\n            0: this.$t(\"LocaleString.L00446\")\r\n          }) + \"<br>\";\r\n      }\r\n\r\n      this.cameraConfigList.forEach(item => {\r\n        if (\r\n          item.capture &&\r\n          (item.prefixMinute == \"\" ||\r\n            item.suffixMinute == \"\" ||\r\n            item.backupDirectory == \"\" ||\r\n            item.account == \"\" ||\r\n            item.password == \"\" ||\r\n            item.keepDays == \"\" ||\r\n            item.positionCapture == \"\")\r\n        ) {\r\n          let name = this.cameraTypeList.find(item2 => item.type == item2.type)\r\n            .name;\r\n          errorDesc +=\r\n            this.$t(\"LocaleString.L00001\", {\r\n              0: this.$t(\"LocaleString.L30139\") + \" - \" + name\r\n            }) + \"<br>\";\r\n        }\r\n      });\r\n      // if (iconSize == \"\") {\r\n      //     errorDesc += '請輸入平面圖標大小<br>'\r\n      // }else {\r\n      //   let re = /^[0-9]{1,}\\*[0-9]{1,}$/\r\n      //   if (!re.test(iconSize)) {\r\n      //     errorDesc += '平面圖標大小格式錯誤<br>'\r\n      //   }\r\n      // }\r\n      //\r\n      if (errorDesc) {\r\n        this.$Notice.error({\r\n          title: this.$t(\"LocaleString.E00037\"),\r\n          desc: errorDesc,\r\n          duration: Config.errorDuration\r\n        });\r\n        return false;\r\n      } else {\r\n        return true;\r\n      }\r\n    },\r\n    editHandleSubmitNew(type) {\r\n      let requestId = this.createRequestID();\r\n      if (type === \"update\") {\r\n        this.submitLoading = true;\r\n      }\r\n      //let loginUserName = localStorage.getItem(\"sns_loginUser\");\r\n      let newLowBatteryDevices = [];\r\n      let newLossignalDevices = [];\r\n      let newLossignalDevices2 = [];\r\n      let newAbnormalDevices = [];\r\n      let submitEventArr = [];\r\n      let putPropertyDatas = [];\r\n      let valid = this.checkEditHandleSubmitNew();\r\n      if (valid) {\r\n        if (type === \"new\") {\r\n          this.configData.lossSignalDevices = this.deviceListLossSignal.map(\r\n            item => item.key\r\n          );\r\n          newLossignalDevices = this.configData.lossSignalDevices;\r\n\r\n          this.configData.lossSignalDevices2 = this.deviceListLossSignal2.map(\r\n            item => item.key\r\n          );\r\n          newLossignalDevices2 = this.configData.lossSignalDevices2;\r\n\r\n          this.configData.lowBatteryDevices = this.deviceListLowBattery.map(\r\n            item => item.key\r\n          );\r\n          newLowBatteryDevices = this.configData.lowBatteryDevices;\r\n\r\n          this.configData.abnormalDevices = this.deviceListAbnormalDevice.map(\r\n            item => item.key\r\n          );\r\n          newAbnormalDevices = this.configData.abnormalDevices;\r\n        }\r\n\r\n        let putData = {\r\n          category: \"@SNS@Setting\",\r\n          properties: [\r\n            {\r\n              key: \"license\",\r\n              value: this.configData.license.join(\",\")\r\n            },\r\n            {\r\n              key: \"systemName\",\r\n              value: this.configData.systemName\r\n            },\r\n            {\r\n              key: \"tabList\",\r\n              value: this.configData.tabList.join(\",\")\r\n            },\r\n            // {\r\n            //   key: \"sound\",\r\n            //   value: this.configData.sound == true ? \"true\" : \"false\",\r\n            // },\r\n            {\r\n              key: \"m2List\",\r\n              value: this.configData.m2List.join(\",\")\r\n            },\r\n            {\r\n              key: \"iconSize\",\r\n              value: this.configData.iconSize\r\n            },\r\n            {\r\n              key: \"defaultTab\",\r\n              value: this.configData.defaultTab\r\n            },\r\n            {\r\n              key: \"updateSeconds\",\r\n              value: this.configData.updateSeconds\r\n            },\r\n            {\r\n              key: \"grayTime\",\r\n              value: this.configData.grayTime\r\n            },\r\n            {\r\n              key: \"EventAuthClose\",\r\n              value: this.configData.eventAuthClose\r\n            },\r\n            {\r\n              key: \"EventKeepDays\",\r\n              value: this.configData.eventKeepDays\r\n            },\r\n            {\r\n              key: \"InfluxRange\",\r\n              value: this.configData.influxRange\r\n            },\r\n            {\r\n              key: \"lossSignal\",\r\n              value: this.configData.lossSignal\r\n            },\r\n            {\r\n              key: \"lossSignal2\",\r\n              value: this.configData.lossSignal2\r\n            },\r\n            {\r\n              key: \"lowBattery\",\r\n              value: this.configData.lowBattery\r\n            },\r\n            {\r\n              key: \"lossSignalDevices\",\r\n              value: this.configData.lossSignalDevices.join(\",\")\r\n            },\r\n            {\r\n              key: \"lossSignalDevices2\",\r\n              value: this.configData.lossSignalDevices2.join(\",\")\r\n            },\r\n            {\r\n              key: \"lowBatteryDevices\",\r\n              value: this.configData.lowBatteryDevices.join(\",\")\r\n            },\r\n            {\r\n              key: \"abnormalDevices\",\r\n              value: this.configData.abnormalDevices.join(\",\")\r\n            },\r\n            {\r\n              key: \"skipEvent\",\r\n              value: this.configData.skipEvent.join(\",\")\r\n            },\r\n            {\r\n              key: \"deviceSelectList\",\r\n              value: this.configData.deviceSelectList.join(\",\")\r\n            },\r\n            {\r\n              key: \"selectedPlanes\",\r\n              value: this.configData.selectedPlanes.join(\",\")\r\n            },\r\n            {\r\n              key: \"multiPlanes\",\r\n              value: this.configData.multiPlanes\r\n            },\r\n            {\r\n              key: \"stuckPlane\",\r\n              value: this.configData.stuckPlane\r\n            },\r\n            {\r\n              key: \"m1LongPress\",\r\n              value: this.configData.m1LongPress.join(\",\")\r\n            },\r\n            {\r\n              key: \"defaultPageSize\",\r\n              value: this.configData.defaultPageSize\r\n            },\r\n            {\r\n              key: \"m5ObjectType\",\r\n              value: this.configData.m5ObjectType\r\n            },\r\n            {\r\n              key: \"showLeaveBed\",\r\n              value: this.configData.showLeaveBed\r\n            },\r\n            {\r\n              key: \"m2Statistic\",\r\n              value: this.configData.m2Statistic\r\n            },\r\n            {\r\n              key: \"m3Statistic\",\r\n              value: this.configData.m3Statistic\r\n            },\r\n            {\r\n              key: \"m4Statistic\",\r\n              value: this.configData.m4Statistic\r\n            },\r\n            {\r\n              key: \"m8Statistic\",\r\n              value: this.configData.m8Statistic\r\n            },\r\n            {\r\n              key: \"m9Statistic\",\r\n              value: this.configData.m9Statistic\r\n            },\r\n            {\r\n              key: \"stateColor\",\r\n              value: this.configData.stateColor\r\n            },\r\n            {\r\n              key: \"repeatSound\",\r\n              value: this.configData.repeatSound\r\n            },\r\n            {\r\n              key: \"logoutTime\",\r\n              value: this.configData.logoutTime\r\n            }\r\n          ]\r\n        };\r\n        if (this.configData.systemName == \"\") {\r\n          localStorage.removeItem(\"sns_systemTitle\");\r\n          document.title = this.$t(\"LocaleString.S00002\");\r\n        } else {\r\n          localStorage.setItem(\"sns_systemTitle\", this.configData.systemName);\r\n          document.title = this.configData.systemName;\r\n        }\r\n        localStorage.setItem(\"sns_defaultTab\", this.configData.defaultTab);\r\n        localStorage.setItem(\r\n          \"sns_alertSound\",\r\n          this.configData.sound == true ? \"true\" : \"false\"\r\n        );\r\n        localStorage.setItem(\r\n          \"sns_m2Line\",\r\n          this.configData.m2Line == true ? \"true\" : \"false\"\r\n        );\r\n        localStorage.setItem(\r\n          \"sns_stuckPlane\",\r\n          this.configData.stuckPlane == true ? \"true\" : \"false\"\r\n        );\r\n        localStorage.setItem(\"sns_pageSize\", this.configData.defaultPageSize);\r\n        localStorage.setItem(\"sns_logoutTime\", this.configData.logoutTime);\r\n        putPropertyDatas.push(putData);\r\n\r\n        let editPOCGroups = [\r\n          {\r\n            code: \"@SNS@Sound\",\r\n            name: \"@SNS@Sound\",\r\n            category: \"@SNS@Sound\"\r\n          },\r\n          {\r\n            code: \"@SNS@Logo\",\r\n            name: \"@SNS@Logo\",\r\n            category: \"@SNS@Logo\"\r\n          }\r\n        ];\r\n        let paramsEvent = {\r\n          inlinecount: true,\r\n          search:\r\n            \"active eq true and code in @SNS@LowBattery,@SNS@LossSignal,@SNS@LossSignal2,@SNS@AbnormalDevice\"\r\n        };\r\n\r\n        let objectParams = {\r\n          search: \"active eq true and foreignKeys.key1 eq @SNS@\"\r\n        };\r\n\r\n        /////// construct km data ///////\r\n        let putKMData = {\r\n          category: \"@SNS@SettingKM\",\r\n          properties: []\r\n        };\r\n        this.kmConfigListEdit.forEach(item => {\r\n          let keyValuePairs = [\r\n            {\r\n              key: item.type + \"_longPress_cht\",\r\n              value: item.longPress_cht\r\n            },\r\n            {\r\n              key: item.type + \"_longPress_en\",\r\n              value: item.longPress_en\r\n            },\r\n            {\r\n              key: item.type + \"_shortClick_cht\",\r\n              value: item.shortClick_cht\r\n            },\r\n            {\r\n              key: item.type + \"_shortClick_en\",\r\n              value: item.shortClick_en\r\n            },\r\n            {\r\n              key: item.type + \"_doubleClick_cht\",\r\n              value: item.doubleClick_cht\r\n            },\r\n            {\r\n              key: item.type + \"_doubleClick_en\",\r\n              value: item.doubleClick_en\r\n            }\r\n          ];\r\n          putKMData.properties = [...putKMData.properties, ...keyValuePairs];\r\n        });\r\n        putPropertyDatas.push(putKMData);\r\n        /////// construct km data ///////\r\n\r\n        /////// construct camera data ///////\r\n        let putCameraData = {\r\n          category: \"@SNS@SettingCamera\",\r\n          properties: []\r\n        };\r\n        this.cameraConfigList.forEach(item => {\r\n          let keyValuePairs = [\r\n            {\r\n              key: item.type + \"_capture\",\r\n              value: item.capture\r\n            },\r\n            {\r\n              key: item.type + \"_prefixMinute\",\r\n              value: item.prefixMinute\r\n            },\r\n            {\r\n              key: item.type + \"_suffixMinute\",\r\n              value: item.suffixMinute\r\n            },\r\n            {\r\n              key: item.type + \"_backupDirectory\",\r\n              value: item.backupDirectory\r\n            },\r\n            {\r\n              key: item.type + \"_account\",\r\n              value: item.account\r\n            },\r\n            {\r\n              key: item.type + \"_password\",\r\n              value: item.password\r\n            },\r\n            {\r\n              key: item.type + \"_keepDays\",\r\n              value: item.keepDays\r\n            },\r\n            {\r\n              key: item.type + \"_positionCapture\",\r\n              value: item.positionCapture\r\n            }\r\n          ];\r\n          putCameraData.properties = [\r\n            ...putCameraData.properties,\r\n            ...keyValuePairs\r\n          ];\r\n        });\r\n        putPropertyDatas.push(putCameraData);\r\n        /////// construct camera data ///////\r\n        /////// construct statistic Line data ///////\r\n        let putStatisticLineData = {\r\n          category: \"@SNS@SettingStatisticLine\",\r\n          properties: []\r\n        };\r\n        this.statisticLineConfigList.forEach(item => {\r\n          let keyValuePairs = [\r\n            {\r\n              key: item.entry + \"_interval\",\r\n              value: item.interval\r\n            },\r\n            {\r\n              key: item.entry + \"_dataType\",\r\n              value: item.dataType\r\n            },\r\n            {\r\n              key: item.entry + \"_warningLine\",\r\n              value: item.warningLine\r\n            }\r\n          ];\r\n          putStatisticLineData.properties = [\r\n            ...putStatisticLineData.properties,\r\n            ...keyValuePairs\r\n          ];\r\n        });\r\n        putPropertyDatas.push(putStatisticLineData);\r\n        /////// construct statistic Line data ///////\r\n        /////// construct thirdParty data ///////\r\n\r\n        if (this.thirdPartyData.loginThirdParty) {\r\n          let errorMsg = [\r\n            this.$t(\"LocaleString.L30196\"),\r\n            \"UserName\",\r\n            \"CredentialSecret\"\r\n          ];\r\n          if (\r\n            this.thirdPartyData.url == \"\" ||\r\n            this.thirdPartyData.userName == \"\" ||\r\n            this.thirdPartyData.secret == \"\"\r\n          ) {\r\n            this.$Notice.error({\r\n              title: this.$t(\"LocaleString.E00037\"),\r\n              desc: this.$t(\"LocaleString.W00038\", { 0: errorMsg.join(\", \") }),\r\n              duration: Config.WARNING_DURATION\r\n            });\r\n            this.submitLoading = false;\r\n            return;\r\n          }\r\n        }\r\n        let putThirdPartyData = {\r\n          category: \"@SNS@SettingThirdParty\",\r\n          properties: [\r\n            {\r\n              key: \"loginThirdParty\",\r\n              value: this.thirdPartyData.loginThirdParty\r\n            },\r\n            {\r\n              key: \"url\",\r\n              value: this.thirdPartyData.url\r\n            },\r\n            {\r\n              key: \"userName\",\r\n              value: this.thirdPartyData.userName\r\n            },\r\n            {\r\n              key: \"secret\",\r\n              value: this.thirdPartyData.secret\r\n            }\r\n          ]\r\n        };\r\n        putPropertyDatas.push(putThirdPartyData);\r\n        /////// construct thirdParty data ///////\r\n        let createEventArr = [];\r\n        let objectDevicesLossSignal = [];\r\n        let objectDevicesLossSignal2 = [];\r\n        let objectDevicesLowBattery = [];\r\n        this.$service.getObjectsNoLoad.send(objectParams).then(objRes => {\r\n          this.$service.getEventsNoLoad.send(paramsEvent).then(eventRes => {\r\n            this.$service.editPOCGroups\r\n              .send(editPOCGroups, null, {\r\n                requestID: requestId,\r\n                requestFunction: \"systemParameter\",\r\n                requestAction: \"Update\"\r\n              })\r\n              .then(editPOCRes => {\r\n                this.$service.editPOCProperties\r\n                  .send(putPropertyDatas, null, {\r\n                    requestID: requestId,\r\n                    requestFunction: \"systemParameter\",\r\n                    requestAction: \"Update\"\r\n                  })\r\n                  .then(propertyRes => {\r\n                    console.log(propertyRes);\r\n                    if (type != \"new\") {\r\n                      this.$Notice.success({\r\n                        title: this.$t(\"LocaleString.M00004\"),\r\n                        desc: this.$t(\"LocaleString.M00002\"),\r\n                        duration: Config.SUCCESS_DURATION\r\n                      });\r\n                    }\r\n\r\n                    localStorage.setItem(\r\n                      \"sns_iconSize\",\r\n                      this.configData.iconSize\r\n                    );\r\n                    localStorage.setItem(\r\n                      \"sns_updateSeconds\",\r\n                      this.configData.updateSeconds\r\n                    );\r\n\r\n                    this.isEdit = false;\r\n                    this.origConfigData = JSON.parse(\r\n                      JSON.stringify(this.configData)\r\n                    );\r\n                    this.updateValue(this.origConfigData);\r\n\r\n                    this.oriThirdPartyData = JSON.parse(\r\n                      JSON.stringify(this.thirdPartyData)\r\n                    );\r\n                    this.updateThirdPartyValue(this.oriThirdPartyData);\r\n\r\n                    if (eventRes.results.length > 0) {\r\n                      eventRes.results.forEach(currentEvent => {\r\n                        let submitArgument = currentEvent.arguments;\r\n                        submitArgument.forEach(item => {\r\n                          if (item.key == \"threshold\") {\r\n                            item.value = currentEvent.code.includes(\r\n                              \"LowBattery\"\r\n                            )\r\n                              ? this.configData.lowBattery.toString()\r\n                              : currentEvent.code.includes(\"LossSignal2\")\r\n                              ? this.configData.lossSignal2.toString()\r\n                              : this.configData.lossSignal.toString();\r\n                          }\r\n                        });\r\n                        let updateEvent = {\r\n                          code: currentEvent.code,\r\n                          arguments: submitArgument,\r\n                          // sponsorObject: {\r\n                          //   objectCodes: total_objCodes.length\r\n                          //     ? total_objCodes\r\n                          //     : [],\r\n                          // },\r\n                          sponsorDevice: {\r\n                            devicePids: currentEvent.code.includes(\"LowBattery\")\r\n                              ? this.getEventPids(\"LowBattery\", objRes)\r\n                              : currentEvent.code.includes(\"LossSignal2\")\r\n                              ? this.getEventPids(\"LossSignal2\", objRes)\r\n                              : currentEvent.code.includes(\"AbnormalDevice\")\r\n                              ? this.getEventPids(\"AbnormalDevice\", objRes)\r\n                              : this.getEventPids(\"LossSignal\", objRes),\r\n                            deviceTypes: currentEvent.code.includes(\r\n                              \"LowBattery\"\r\n                            )\r\n                              ? this.configData.lowBatteryDevices\r\n                              : currentEvent.code.includes(\"LossSignal2\")\r\n                              ? this.configData.lossSignalDevices2\r\n                              : currentEvent.code.includes(\"AbnormalDevice\")\r\n                              ? this.configData.abnormalDevices\r\n                              : this.configData.lossSignalDevices\r\n                          }\r\n                        };\r\n                        submitEventArr.push(updateEvent);\r\n                      });\r\n                      if (\r\n                        !eventRes.results.some(\r\n                          item => item.code == \"@SNS@LossSignal\"\r\n                        )\r\n                      ) {\r\n                        // create lossSignal event\r\n                        let lossSignalEvent = this.lossSignalCreateTemplate(\r\n                          newLossignalDevices\r\n                        );\r\n                        createEventArr.push(lossSignalEvent);\r\n                      }\r\n                      if (\r\n                        !eventRes.results.some(\r\n                          item => item.code == \"@SNS@LossSignal2\"\r\n                        )\r\n                      ) {\r\n                        // create lossSignal2 event\r\n                        let lossSignalEvent2 = this.lossSignalCreateTemplate2(\r\n                          newLossignalDevices2\r\n                        );\r\n                        createEventArr.push(lossSignalEvent2);\r\n                      }\r\n                      if (\r\n                        !eventRes.results.some(\r\n                          item => item.code == \"@SNS@LowBattery\"\r\n                        )\r\n                      ) {\r\n                        // create lowBattery event\r\n                        let lowBetteryEvent = this.lowBatteryCreateTemplate(\r\n                          newLowBatteryDevices\r\n                        );\r\n                        createEventArr.push(lowBetteryEvent);\r\n                      }\r\n                      if (\r\n                        !eventRes.results.some(\r\n                          item => item.code == \"@SNS@AbnormalDevice\"\r\n                        )\r\n                      ) {\r\n                        // create abnormalDevice event\r\n                        let abnormalDeviceEvent = this.abnormalDeviceCreateTemplate(\r\n                          newAbnormalDevices\r\n                        );\r\n                        createEventArr.push(abnormalDeviceEvent);\r\n                      }\r\n                      //edit lowBattery event\r\n                      this.$service.postNewEvent\r\n                        .send(createEventArr, null, {\r\n                          requestID: requestId,\r\n                          requestFunction: \"systemParameter\",\r\n                          requestAction: \"Update\"\r\n                        })\r\n                        .then(postRes => {\r\n                          this.$service.editEvent\r\n                            .send(submitEventArr, null, {\r\n                              requestID: requestId,\r\n                              requestFunction: \"systemParameter\",\r\n                              requestAction: \"Update\"\r\n                            })\r\n                            .then(patchRes => {\r\n                              this.fileUpload(type);\r\n                            });\r\n                        });\r\n                    } else {\r\n                      // create lowBattery event\r\n                      let lowBetteryEvent = this.lowBatteryCreateTemplate(\r\n                        newLowBatteryDevices\r\n                      );\r\n                      // create lossSignal event\r\n                      let lossSignalEvent = this.lossSignalCreateTemplate(\r\n                        newLossignalDevices\r\n                      );\r\n                      // create lossSignal event\r\n                      let lossSignalEvent2 = this.lossSignalCreateTemplate2(\r\n                        newLossignalDevices2\r\n                      );\r\n                      // create lossSignal event\r\n                      let abnormalDeviceEvent = this.abnormalDeviceCreateTemplate(\r\n                        newAbnormalDevices\r\n                      );\r\n\r\n                      createEventArr.push(lowBetteryEvent);\r\n                      createEventArr.push(lossSignalEvent);\r\n                      createEventArr.push(lossSignalEvent2);\r\n                      createEventArr.push(abnormalDeviceEvent);\r\n                      this.$service.postNewEvent\r\n                        .send(createEventArr, null, {\r\n                          requestID: requestId,\r\n                          requestFunction: \"systemParameter\",\r\n                          requestAction: \"Update\"\r\n                        })\r\n                        .then(editRes => {\r\n                          this.fileUpload(type);\r\n                        });\r\n                    }\r\n                  });\r\n              });\r\n          });\r\n        });\r\n      } else {\r\n        if (type === \"update\") {\r\n          this.submitLoading = false;\r\n        }\r\n      }\r\n    },\r\n    lossSignalCreateTemplate(deviceTypes) {\r\n      let event = {\r\n        code: \"@SNS@LossSignal\",\r\n        name:\r\n          this.$t(\"LocaleString.D00033\") + \"_\" + this.$t(\"LocaleString.L30158\"),\r\n        serviceCode: \"LossSignal\",\r\n        arguments: [\r\n          {\r\n            key: \"threshold\",\r\n            value: this.configData.lossSignal.toString()\r\n          },\r\n          { key: \"autoTreated\", value: true }\r\n        ],\r\n        sponsorDevice: {\r\n          deviceTypes: deviceTypes\r\n        }\r\n      };\r\n      return event;\r\n    },\r\n    lossSignalCreateTemplate2(deviceTypes) {\r\n      let event = {\r\n        code: \"@SNS@LossSignal2\",\r\n        name:\r\n          this.$t(\"LocaleString.D00033\") + \"_\" + this.$t(\"LocaleString.L30157\"),\r\n        serviceCode: \"LossSignal\",\r\n        arguments: [\r\n          {\r\n            key: \"threshold\",\r\n            value: this.configData.lossSignal2.toString()\r\n          },\r\n          { key: \"autoTreated\", value: true }\r\n        ],\r\n        sponsorDevice: {\r\n          deviceTypes: deviceTypes\r\n        }\r\n      };\r\n      return event;\r\n    },\r\n    lowBatteryCreateTemplate(deviceTypes) {\r\n      let event = {\r\n        code: \"@SNS@LowBattery\",\r\n        name: this.$t(\"LocaleString.D00034\"),\r\n        serviceCode: \"LowBattery\",\r\n        arguments: [\r\n          {\r\n            key: \"threshold\",\r\n            value: this.configData.lowBattery.toString()\r\n          },\r\n          { key: \"autoTreated\", value: true }\r\n        ],\r\n        sponsorDevice: {\r\n          deviceTypes: deviceTypes\r\n        }\r\n      };\r\n      return event;\r\n    },\r\n    abnormalDeviceCreateTemplate(deviceTypes) {\r\n      let event = {\r\n        code: \"@SNS@AbnormalDevice\",\r\n        name: this.$t(\"LocaleString.D00026\"),\r\n        serviceCode: \"AbnormalDevice\",\r\n        arguments: [{ key: \"autoTreated\", value: true }],\r\n        sponsorDevice: {\r\n          deviceTypes: deviceTypes\r\n        }\r\n      };\r\n      return event;\r\n    },\r\n    getEventPids(type, objectResponse) {\r\n      let pids = [];\r\n      if (objectResponse.results.length > 0) {\r\n        objectResponse.results.forEach(obj => {\r\n          if (obj.devices) {\r\n            obj.devices.forEach(d => {\r\n              if (\r\n                type === \"LossSignal\" &&\r\n                this.configData.lossSignalDevices.includes(d.type)\r\n              ) {\r\n                pids.push(d.pid);\r\n              }\r\n              if (\r\n                type === \"LossSignal2\" &&\r\n                this.configData.lossSignalDevices2.includes(d.type)\r\n              ) {\r\n                pids.push(d.pid);\r\n              }\r\n              if (\r\n                type === \"LowBattery\" &&\r\n                this.configData.lowBatteryDevices.includes(d.type)\r\n              ) {\r\n                pids.push(d.pid);\r\n              }\r\n              if (\r\n                type === \"AbnormalDevice\" &&\r\n                this.configData.abnormalDevices.includes(d.type)\r\n              ) {\r\n                pids.push(d.pid);\r\n              }\r\n            });\r\n          }\r\n        });\r\n      }\r\n      return pids;\r\n    },\r\n    async fileUpload(type) {\r\n      if (this.imgFile != null) {\r\n        let imgParams = [\"@SNS@Logo\"];\r\n        let imgData = {\r\n          image: this.imgFile\r\n        };\r\n        let res2 = await this.$service.editPOCGroupsImage.fileUpload(\r\n          imgData,\r\n          imgParams\r\n        );\r\n      } else {\r\n        if (!this.showCleanIcon && !this.imgFileURL) {\r\n          let params = \"@SNS@Logo\";\r\n          let res = await this.$service.deletePOCGroupsImage.send(params);\r\n          localStorage.removeItem(\"sns_logoURL\");\r\n        }\r\n      }\r\n      if (this.file != null) {\r\n        let data = {\r\n          image: this.file\r\n        };\r\n        let params = [\"@SNS@Sound\"];\r\n        let res = await this.$service.editPOCGroupsMusic.fileUpload(\r\n          data,\r\n          params\r\n        );\r\n      }\r\n      this.goDefaultPage(type);\r\n    },\r\n    goDefaultPage(type) {\r\n      if (this.oriTab != this.configData.defaultTab) {\r\n        //重導向\r\n        this.$router.push({\r\n          path: decodeURIComponent(\r\n            \"/administrative/apps/sns/\" + this.configData.defaultTab\r\n          ) // 導頁至預設頁面\r\n        });\r\n      }\r\n      setTimeout(() => {\r\n        this.$store.commit(\"setPOCConfigChanged\", moment().valueOf());\r\n        if (type != \"new\") {\r\n          this.submitLoading = false;\r\n          this.$emit(\"closeSystemConfig\");\r\n        }\r\n      }, 1100);\r\n    },\r\n    updateThirdPartyValue(data) {\r\n      this.thirdPartyConfigList.forEach(d => {\r\n        switch (d.section) {\r\n          case 1:\r\n            d.value = data.loginThirdParty;\r\n            this.viewLoginThirdParty =\r\n              data.loginThirdParty == true\r\n                ? this.$t(\"LocaleString.B20009\")\r\n                : this.$t(\"LocaleString.B20010\");\r\n            break;\r\n          case 2:\r\n            d.value = data.url;\r\n            break;\r\n          case 3:\r\n            d.value = data.userName;\r\n            break;\r\n          case 4:\r\n            d.value = data.secret;\r\n            break;\r\n        }\r\n      });\r\n\r\n      // d.viewStuckPlane =\r\n      //         data.stuckPlane == true\r\n      //           ? this.$t(\"LocaleString.B20009\")\r\n      //           : this.$t(\"LocaleString.B20010\");\r\n    },\r\n    updateValue(data) {\r\n      let viewM2ListArr = [];\r\n      let viewTabArr = [];\r\n      let viewLicenseArr = [];\r\n      data.m2List.forEach(d => {\r\n        let v = this.showingType.find(t => t.value == d).label;\r\n        viewM2ListArr.push(v);\r\n      });\r\n      this.viewM2List = viewM2ListArr.join(\", \");\r\n\r\n      data.tabList.forEach(d => {\r\n        let v = this.showingTab.find(t => t.value == d).label;\r\n        viewTabArr.push(v);\r\n      });\r\n\r\n      data.license.forEach(d => {\r\n        let v = this.serviceCodeList.find(t => t.key == d).value;\r\n        viewLicenseArr.push(v);\r\n      });\r\n\r\n      this.iconSizeTitle = this.iconSizeList.find(\r\n        t => t.value == data.iconSize\r\n      ).label;\r\n      this.defaultPageSizeTitle =\r\n        data.defaultPageSize == \"\"\r\n          ? \"\"\r\n          : this.pageSizeList.find(t => t.value == data.defaultPageSize).label;\r\n      this.m5ObjectTypeTitle =\r\n        data.m5ObjectType == \"\"\r\n          ? \"\"\r\n          : this.m5ObjectTypeList.find(t => t.value == data.m5ObjectType).label;\r\n      this.defaultTabTitle = this.showingTab.find(\r\n        t => t.value == data.defaultTab\r\n      ).label;\r\n\r\n      this.viewTab = viewTabArr.join(\", \");\r\n      this.viewLicense = viewLicenseArr.join(\", \");\r\n      this.viewPlaySound =\r\n        localStorage.getItem(\"sns_alertSound\") == \"true\"\r\n          ? this.$t(\"LocaleString.B20009\")\r\n          : this.$t(\"LocaleString.B20010\");\r\n      this.viewM2Line =\r\n        localStorage.getItem(\"sns_m2Line\") == \"true\"\r\n          ? this.$t(\"LocaleString.B20009\")\r\n          : this.$t(\"LocaleString.B20010\");\r\n      this.data.forEach(d => {\r\n        switch (d.section) {\r\n          case 0:\r\n            d.value = data.systemName;\r\n            break;\r\n          case 1:\r\n            d.value = this.viewTab;\r\n            break;\r\n          case 2:\r\n            d.value = this.viewPlaySound;\r\n            d.valueTime = data.repeatSound;\r\n            break;\r\n          case 3:\r\n            d.value = this.viewM2List;\r\n            break;\r\n          case 4:\r\n            d.value = data.iconSize;\r\n            break;\r\n          case 5:\r\n            d.value = data.defaultTab;\r\n            break;\r\n          case 6:\r\n            d.value = data.updateSeconds;\r\n            break;\r\n          case 7:\r\n            d.value = data.grayTime * 1;\r\n            break;\r\n          case 8:\r\n            d.value = this.viewM2Line;\r\n            break;\r\n          case 9:\r\n            d.value =\r\n              data.eventAuthClose == true\r\n                ? this.$t(\"LocaleString.B20009\")\r\n                : this.$t(\"LocaleString.B20010\");\r\n            d.boolean = data.eventAuthClose == true ? true : false;\r\n            break;\r\n          case 10:\r\n            d.value = data.eventKeepDays;\r\n            break;\r\n          case 11:\r\n            d.value = data.influxRange;\r\n            break;\r\n          case 12:\r\n            d.value = data.lossSignal;\r\n            d.valueDevices = this.transformName(\r\n              data.lossSignalDevices,\r\n              \"lossSignal\"\r\n            );\r\n            break;\r\n          case 22:\r\n            d.value = data.lossSignal2;\r\n            d.valueDevices = this.transformName(\r\n              data.lossSignalDevices2,\r\n              \"lossSignal2\"\r\n            );\r\n            break;\r\n          case 13:\r\n            d.value = data.lowBattery;\r\n            d.valueDevices = this.transformName(\r\n              data.lowBatteryDevices,\r\n              \"lowBattery\"\r\n            );\r\n            break;\r\n          case 14:\r\n            d.value = data.skipEvent;\r\n            d.valueSkipEvent = this.transformName(data.skipEvent, \"skipEvent\");\r\n            break;\r\n          case 15:\r\n            d.value = data.abnormalDevice;\r\n            d.valueDevices = this.transformName(\r\n              data.abnormalDevices,\r\n              \"abnormalDevice\"\r\n            );\r\n            break;\r\n          case 16:\r\n            d.value = data.deviceSelectList;\r\n            d.valueDeviceSelectList = this.transformName(\r\n              data.deviceSelectList,\r\n              \"deviceSelectList\"\r\n            );\r\n            break;\r\n          case 17:\r\n            d.value = this.viewLogo;\r\n            break;\r\n          case 18:\r\n            d.viewMultiPlanes =\r\n              data.multiPlanes == true\r\n                ? this.$t(\"LocaleString.B20009\")\r\n                : this.$t(\"LocaleString.B20010\");\r\n            d.multiPlanes = data.multiPlanes == true ? true : false;\r\n            d.value = data.selectedPlanes;\r\n            d.valuePlaneSelectList = this.transformPlaneName(\r\n              data.selectedPlanes\r\n            );\r\n            break;\r\n          case 19:\r\n            d.value = data.m1LongPress;\r\n            d.valueM1LongPressList = this.transformName(\r\n              data.m1LongPress,\r\n              \"m1LongPress\"\r\n            );\r\n            break;\r\n          case 20:\r\n            d.value = data.defaultPageSize;\r\n            break;\r\n          case 21:\r\n            d.value = data.m5ObjectType;\r\n            break;\r\n          case 23:\r\n            d.value = this.viewLicense;\r\n            break;\r\n          case 24:\r\n            d.value =\r\n              data.showLeaveBed == true\r\n                ? this.$t(\"LocaleString.B20009\")\r\n                : this.$t(\"LocaleString.B20010\");\r\n            break;\r\n          case 25:\r\n            d.value = data.m2Statistic;\r\n            break;\r\n          case 26:\r\n            d.value = data.m3Statistic;\r\n            break;\r\n          case 27:\r\n            d.value = data.m4Statistic;\r\n            break;\r\n          case 28:\r\n            d.value = data.m8Statistic;\r\n            break;\r\n          case 29:\r\n            d.value = data.m9Statistic;\r\n            break;\r\n          case 30:\r\n            d.viewStuckPlane =\r\n              data.stuckPlane == true\r\n                ? this.$t(\"LocaleString.B20009\")\r\n                : this.$t(\"LocaleString.B20010\");\r\n            d.stuckPlane = data.stuckPlane == true ? true : false;\r\n            break;\r\n          case 31:\r\n            d.value = data.logoutTime;\r\n            break;\r\n          case 32:\r\n            d.value = data.stateColor;\r\n            break;\r\n        }\r\n      });\r\n    },\r\n    transformName(deviceArray, type) {\r\n      let tempData = [];\r\n      let deviceType = \"\";\r\n      if (deviceArray && deviceArray.length > 0) {\r\n        if (deviceArray.length == 1 && deviceArray[0] === \"\") {\r\n          return \"\";\r\n        }\r\n        switch (type) {\r\n          case \"lossSignal\":\r\n            try {\r\n              deviceArray.forEach(item => {\r\n                deviceType = item;\r\n                let d = this.deviceListLossSignal.find(data => data.key == item)\r\n                  .value;\r\n                tempData.push(d);\r\n              });\r\n            } catch (e) {\r\n              tempData.push(deviceType);\r\n            }\r\n\r\n            break;\r\n          case \"lossSignal2\":\r\n            try {\r\n              deviceArray.forEach(item => {\r\n                deviceType = item;\r\n                let d = this.deviceListLossSignal2.find(\r\n                  data => data.key == item\r\n                ).value;\r\n                tempData.push(d);\r\n              });\r\n            } catch (e) {\r\n              tempData.push(deviceType);\r\n            }\r\n\r\n            break;\r\n          case \"lowBattery\":\r\n            try {\r\n              deviceArray.forEach(item => {\r\n                deviceType = item;\r\n                let d = this.deviceListLowBattery.find(data => data.key == item)\r\n                  .value;\r\n                tempData.push(d);\r\n              });\r\n            } catch (e) {\r\n              tempData.push(deviceType);\r\n            }\r\n            break;\r\n          case \"abnormalDevice\":\r\n            try {\r\n              deviceArray.forEach(item => {\r\n                deviceType = item;\r\n                let d = this.deviceListAbnormalDevice.find(\r\n                  data => data.key == item\r\n                ).value;\r\n                tempData.push(d);\r\n              });\r\n            } catch (e) {\r\n              tempData.push(deviceType);\r\n            }\r\n            break;\r\n          case \"skipEvent\":\r\n            deviceArray.forEach(item => {\r\n              let d = this.eventServiceCodeList.find(data => data.key == item)\r\n                .value;\r\n              tempData.push(d);\r\n            });\r\n            break;\r\n          case \"deviceSelectList\":\r\n            try {\r\n              deviceArray.forEach(item => {\r\n                deviceType = item;\r\n                let d = this.deviceDefaultSelection.find(\r\n                  data => data.key == item\r\n                ).value;\r\n                tempData.push(d);\r\n              });\r\n            } catch (e) {\r\n              tempData.push(deviceType);\r\n            }\r\n            break;\r\n          case \"m1LongPress\":\r\n            try {\r\n              deviceArray.forEach(item => {\r\n                deviceType = item;\r\n                let d = this.deviceDefaultSelection.find(\r\n                  data => data.key == item\r\n                ).value;\r\n                tempData.push(d);\r\n              });\r\n            } catch (e) {\r\n              tempData.push(deviceType);\r\n            }\r\n            break;\r\n        }\r\n        return tempData.join(\", \");\r\n      }\r\n    },\r\n    transformPlaneName(planeArray) {\r\n      let tempData = [];\r\n      if (planeArray.length == 1 && planeArray[0] === \"\") {\r\n        return \"\";\r\n      }\r\n      if (planeArray && planeArray.length > 0 && this.planeList.length > 0) {\r\n        planeArray.forEach(item => {\r\n          let d = this.planeList.find(data => data.value == item).label;\r\n          tempData.push(d);\r\n        });\r\n        return tempData.join(\", \");\r\n      } else {\r\n        return \"\";\r\n      }\r\n    },\r\n    loadKMData() {\r\n      let params = {\r\n        inlinecount: true,\r\n        search: \"category eq @SNS@SettingKM\"\r\n      };\r\n\r\n      this.$service.getPOCProperties.send(params).then(res => {\r\n        if (res.count > 0) {\r\n          res.results[0].properties.forEach(res => {\r\n            switch (res.key) {\r\n              case \"equipment_longPress_cht\":\r\n                this.kmConfigList[0].longPress_cht = res.value;\r\n                break;\r\n              case \"equipment_longPress_en\":\r\n                this.kmConfigList[0].longPress_en = res.value;\r\n                break;\r\n              case \"equipment_shortClick_cht\":\r\n                this.kmConfigList[0].shortClick_cht = res.value;\r\n                break;\r\n              case \"equipment_shortClick_en\":\r\n                this.kmConfigList[0].shortClick_en = res.value;\r\n                break;\r\n              case \"equipment_doubleClick_cht\":\r\n                this.kmConfigList[0].doubleClick_cht = res.value;\r\n                break;\r\n              case \"equipment_doubleClick_en\":\r\n                this.kmConfigList[0].doubleClick_en = res.value;\r\n                break;\r\n              case \"people_longPress_cht\":\r\n                this.kmConfigList[1].longPress_cht = res.value;\r\n                break;\r\n              case \"people_longPress_en\":\r\n                this.kmConfigList[1].longPress_en = res.value;\r\n                break;\r\n              case \"people_shortClick_cht\":\r\n                this.kmConfigList[1].shortClick_cht = res.value;\r\n                break;\r\n              case \"people_shortClick_en\":\r\n                this.kmConfigList[1].shortClick_en = res.value;\r\n                break;\r\n              case \"people_doubleClick_cht\":\r\n                this.kmConfigList[1].doubleClick_cht = res.value;\r\n                break;\r\n              case \"people_doubleClick_en\":\r\n                this.kmConfigList[1].doubleClick_en = res.value;\r\n                break;\r\n              case \"space_longPress_cht\":\r\n                this.kmConfigList[2].longPress_cht = res.value;\r\n                break;\r\n              case \"space_longPress_en\":\r\n                this.kmConfigList[2].longPress_en = res.value;\r\n                break;\r\n              case \"space_shortClick_cht\":\r\n                this.kmConfigList[2].shortClick_cht = res.value;\r\n                break;\r\n              case \"space_shortClick_en\":\r\n                this.kmConfigList[2].shortClick_en = res.value;\r\n                break;\r\n              case \"space_doubleClick_cht\":\r\n                this.kmConfigList[2].doubleClick_cht = res.value;\r\n                break;\r\n              case \"space_doubleClick_en\":\r\n                this.kmConfigList[2].doubleClick_en = res.value;\r\n                break;\r\n              case \"other_longPress_cht\":\r\n                this.kmConfigList[3].longPress_cht = res.value;\r\n                break;\r\n              case \"other_longPress_en\":\r\n                this.kmConfigList[3].longPress_en = res.value;\r\n                break;\r\n              case \"other_shortClick_cht\":\r\n                this.kmConfigList[3].shortClick_cht = res.value;\r\n                break;\r\n              case \"other_shortClick_en\":\r\n                this.kmConfigList[3].shortClick_en = res.value;\r\n                break;\r\n              case \"other_doubleClick_cht\":\r\n                this.kmConfigList[3].doubleClick_cht = res.value;\r\n                break;\r\n              case \"other_doubleClick_en\":\r\n                this.kmConfigList[3].doubleClick_en = res.value;\r\n                break;\r\n            }\r\n          });\r\n        }\r\n        this.kmConfigListEdit = JSON.parse(JSON.stringify(this.kmConfigList));\r\n      });\r\n    },\r\n    loadCameraData() {\r\n      let params = {\r\n        inlinecount: true,\r\n        search: \"category eq @SNS@SettingCamera\"\r\n      };\r\n\r\n      this.$service.getPOCProperties.send(params).then(res => {\r\n        if (res.count > 0) {\r\n          res.results[0].properties.forEach(res => {\r\n            switch (res.key) {\r\n              //help\r\n              case \"help_capture\":\r\n                this.cameraConfigList[0].capture =\r\n                  res.value == \"true\" ? true : false;\r\n                break;\r\n              case \"help_prefixMinute\":\r\n                this.cameraConfigList[0].prefixMinute = res.value * 1;\r\n                break;\r\n              case \"help_suffixMinute\":\r\n                this.cameraConfigList[0].suffixMinute = res.value * 1;\r\n                break;\r\n              case \"help_backupDirectory\":\r\n                this.cameraConfigList[0].backupDirectory = res.value;\r\n                break;\r\n              case \"help_account\":\r\n                this.cameraConfigList[0].account = res.value;\r\n                break;\r\n              case \"help_password\":\r\n                this.cameraConfigList[0].password = res.value;\r\n                break;\r\n              case \"help_keepDays\":\r\n                this.cameraConfigList[0].keepDays = res.value * 1;\r\n                break;\r\n              case \"help_positionCapture\":\r\n                this.cameraConfigList[0].positionCapture = res.value * 1;\r\n                break;\r\n\r\n              //Enter\r\n              case \"enter_capture\":\r\n                this.cameraConfigList[1].capture =\r\n                  res.value == \"true\" ? true : false;\r\n                break;\r\n              case \"enter_prefixMinute\":\r\n                this.cameraConfigList[1].prefixMinute = res.value * 1;\r\n                break;\r\n              case \"enter_suffixMinute\":\r\n                this.cameraConfigList[1].suffixMinute = res.value * 1;\r\n                break;\r\n              case \"enter_backupDirectory\":\r\n                this.cameraConfigList[1].backupDirectory = res.value;\r\n                break;\r\n              case \"enter_account\":\r\n                this.cameraConfigList[1].account = res.value;\r\n                break;\r\n              case \"enter_password\":\r\n                this.cameraConfigList[1].password = res.value;\r\n                break;\r\n              case \"enter_keepDays\":\r\n                this.cameraConfigList[1].keepDays = res.value * 1;\r\n                break;\r\n              case \"enter_positionCapture\":\r\n                this.cameraConfigList[1].positionCapture = res.value * 1;\r\n                break;\r\n\r\n              //Leave\r\n              case \"leave_capture\":\r\n                this.cameraConfigList[2].capture =\r\n                  res.value == \"true\" ? true : false;\r\n                break;\r\n              case \"leave_prefixMinute\":\r\n                this.cameraConfigList[2].prefixMinute = res.value * 1;\r\n                break;\r\n              case \"leave_suffixMinute\":\r\n                this.cameraConfigList[2].suffixMinute = res.value * 1;\r\n                break;\r\n              case \"leave_backupDirectory\":\r\n                this.cameraConfigList[2].backupDirectory = res.value;\r\n                break;\r\n              case \"leave_account\":\r\n                this.cameraConfigList[2].account = res.value;\r\n                break;\r\n              case \"leave_password\":\r\n                this.cameraConfigList[2].password = res.value;\r\n                break;\r\n              case \"leave_keepDays\":\r\n                this.cameraConfigList[2].keepDays = res.value * 1;\r\n                break;\r\n              case \"leave_positionCapture\":\r\n                this.cameraConfigList[2].positionCapture = res.value * 1;\r\n                break;\r\n\r\n              //fall\r\n              case \"fall_capture\":\r\n                this.cameraConfigList[3].capture =\r\n                  res.value == \"true\" ? true : false;\r\n                break;\r\n              case \"fall_prefixMinute\":\r\n                this.cameraConfigList[3].prefixMinute = res.value * 1;\r\n                break;\r\n              case \"fall_suffixMinute\":\r\n                this.cameraConfigList[3].suffixMinute = res.value * 1;\r\n                break;\r\n              case \"fall_backupDirectory\":\r\n                this.cameraConfigList[3].backupDirectory = res.value;\r\n                break;\r\n              case \"fall_account\":\r\n                this.cameraConfigList[3].account = res.value;\r\n                break;\r\n              case \"fall_password\":\r\n                this.cameraConfigList[3].password = res.value;\r\n                break;\r\n              case \"fall_keepDays\":\r\n                this.cameraConfigList[3].keepDays = res.value * 1;\r\n                break;\r\n              case \"fall_positionCapture\":\r\n                this.cameraConfigList[3].positionCapture = res.value * 1;\r\n                break;\r\n            }\r\n          });\r\n        }\r\n        this.kmConfigListEdit = JSON.parse(JSON.stringify(this.kmConfigList));\r\n      });\r\n    },\r\n    loadStatisticLineData() {\r\n      let params = {\r\n        inlinecount: true,\r\n        search: \"category eq @SNS@SettingStatisticLine\"\r\n      };\r\n      this.$service.getPOCProperties.send(params).then(resData => {\r\n        if (resData.count > 0) {\r\n          resData.results[0].properties.forEach(res => {\r\n            let key = res.key.split(\"_\")[0];\r\n            let data = res.key.split(\"_\")[1];\r\n            // if(req)\r\n            //     this.cameraConfigList[0].capture = res.value == \"true\" ? true : false;\r\n\r\n            this.statisticLineConfigList.forEach(item => {\r\n              if (item.entry == key) {\r\n                if (!isNaN(parseInt(res.value))) {\r\n                  item[data] = res.value * 1;\r\n                } else if (res.value == \"true\" || res == \"false\") {\r\n                  item[data] =\r\n                    res.value == \"true\"\r\n                      ? true\r\n                      : res.value == \"false\"\r\n                      ? false\r\n                      : false;\r\n                } else {\r\n                  item[data] = res.value;\r\n                }\r\n              }\r\n            });\r\n          });\r\n        }\r\n      });\r\n    },\r\n    getCameraType(type) {\r\n      if (type != undefined) {\r\n        let typeData = this.cameraTypeList.find(t => t.type == type).name;\r\n        return typeData;\r\n      } else {\r\n        return \"\";\r\n      }\r\n    },\r\n    getObjectType(type) {\r\n      if (type != undefined) {\r\n        let typeData = this.kmObjectTypeList.find(t => t.type == type).name;\r\n        return typeData;\r\n      } else {\r\n        return \"\";\r\n      }\r\n    },\r\n    onEditDataChange(type, row, index) {\r\n      switch (type) {\r\n        case \"longPress_cht\":\r\n          this.kmConfigListEdit[index].longPress_cht = row.longPress_cht;\r\n          break;\r\n        case \"longPress_en\":\r\n          this.kmConfigListEdit[index].longPress_en = row.longPress_en;\r\n          break;\r\n        case \"shortClick_cht\":\r\n          this.kmConfigListEdit[index].shortClick_cht = row.shortClick_cht;\r\n          break;\r\n        case \"shortClick_en\":\r\n          this.kmConfigListEdit[index].shortClick_en = row.shortClick_en;\r\n          break;\r\n        case \"doubleClick_cht\":\r\n          this.kmConfigListEdit[index].doubleClick_cht = row.doubleClick_cht;\r\n          break;\r\n        case \"doubleClick_en\":\r\n          this.kmConfigListEdit[index].doubleClick_en = row.doubleClick_en;\r\n          break;\r\n      }\r\n    },\r\n    normalizeHeight() {\r\n      setTimeout(\r\n        function() {\r\n          const modal = document.querySelector(\".ivu-modal-body .content\");\r\n          if (modal) {\r\n            modal.style.height = window.innerHeight - 400 + \"px\";\r\n          }\r\n          const modal2 = document.querySelector(\".ivu-modal-footer\");\r\n          if (modal2) {\r\n            modal2.style.height = \"30px\";\r\n          }\r\n        }.bind(this),\r\n        50\r\n      );\r\n    },\r\n    dynamicSort(property) {\r\n      var sortOrder = 1;\r\n      if (property[0] === \"-\") {\r\n        sortOrder = -1;\r\n        property = property.substr(1);\r\n      }\r\n      return function(a, b) {\r\n        var result =\r\n          a[property] < b[property] ? -1 : a[property] > b[property] ? 1 : 0;\r\n        return result * sortOrder;\r\n      };\r\n    },\r\n    edit() {\r\n      this.isEdit = true;\r\n      this.play(\"stop\");\r\n      this.loadLogo();\r\n      this.loadSound();\r\n      //this.getSystemConfig()\r\n    },\r\n    cancelEdit() {\r\n      this.play(\"stop\");\r\n      this.file = null;\r\n      this.fileURL = null;\r\n      this.isEdit = false;\r\n      this.configData = JSON.parse(JSON.stringify(this.origConfigData));\r\n      this.thirdPartyData = JSON.parse(JSON.stringify(this.origThirdPartyData));\r\n      this.cameraConfigList = JSON.parse(\r\n        JSON.stringify(this.defaultCameraConfigList)\r\n      );\r\n      this.updateValue(this.configData);\r\n      this.loadLogo();\r\n      this.loadSound();\r\n      this.loadKMData();\r\n      this.loadCameraData();\r\n      this.loadThirdParty();\r\n      //this.getSystemConfig();\r\n    },\r\n\r\n    // editHandleSubmit() {\r\n    //   console.log(\"editHandleSubmit\");\r\n    //   console.log(this.configList);\r\n    //   console.log(this.configListToSave);\r\n    //   console.log(\"LoginUser: \" + localStorage.getItem(\"sns_loginUser\"));\r\n\r\n    //   let putdata = [];\r\n    //   let properties = [];\r\n    //   let loginUserName = localStorage.getItem(\"sns_loginUser\");\r\n    //   let appEnableAccountKey = \"appsEnable-\" + loginUserName;\r\n    //   let appEditAccountKey = \"appsEdit-\" + loginUserName;\r\n    //   let appEnableForUser = \"\";\r\n    //   let appEditForUser = \"\";\r\n\r\n    //   this.configList.forEach((x) => {\r\n    //     if (x.key == appEnableAccountKey) appEnableForUser = x.value.toString();\r\n    //     else if (x.key == appEditAccountKey)\r\n    //       appEditForUser = x.value.toString();\r\n    //   });\r\n    //   this.configListToSave.forEach((y) => {\r\n    //     switch (y.schema.type) {\r\n    //       case \"string\":\r\n    //         properties.push({\r\n    //           key: y.key,\r\n    //           value: y.value,\r\n    //         });\r\n    //         break;\r\n    //       case \"select\":\r\n    //         if (y.key != appEnableAccountKey)\r\n    //           properties.push({\r\n    //             key: y.key,\r\n    //             value: y.value.toString(),\r\n    //           });\r\n    //         break;\r\n    //       case \"boolean\":\r\n    //         properties.push({\r\n    //           key: y.key,\r\n    //           value: y.value.toString(),\r\n    //         });\r\n    //         break;\r\n    //     }\r\n    //   });\r\n    //   if (loginUserName) {\r\n    //     properties.push({\r\n    //       key: appEnableAccountKey,\r\n    //       value: appEnableForUser,\r\n    //     });\r\n    //     properties.push({\r\n    //       key: appEditAccountKey,\r\n    //       value: appEditForUser,\r\n    //     });\r\n    //   }\r\n\r\n    //   putdata.push({\r\n    //     category: \"pocConfig\",\r\n    //     properties: properties,\r\n    //   });\r\n    //   console.log(\"properties=\");\r\n    //   console.log(putdata);\r\n\r\n    //   this.$service.editPOCProperties.send(putdata).then((res) => {\r\n    //     console.log(res);\r\n    //     this.$Notice.success({\r\n    //       title: this.$t(\"LocaleString.M00004\"),\r\n    //       desc: this.$t(\"LocaleString.M00002\"),\r\n    //       duration: Config.SUCCESS_DURATION,\r\n    //     });\r\n\r\n    //     this.$store.commit(\"setPOCConfigChanged\", moment().valueOf());\r\n\r\n    //     this.isShow = false;\r\n    //     this.$emit(\"closeSystemConfig\");\r\n    //   });\r\n    // },\r\n\r\n    // computedDate(val) {\r\n    //   if (val) {\r\n    //     return moment(val).format(\"YYYY-MM-DD\");\r\n    //   }\r\n    //   return \"\";\r\n    // },\r\n    // computedDateTime(val) {\r\n    //   if (val) {\r\n    //     return moment(val).format(\"YYYY-MM-DD HH:mm:ss\");\r\n    //   }\r\n    //   return \"\";\r\n    // },\r\n    cancel() {\r\n      this.isShow = false;\r\n      this.configData = this.origConfigData;\r\n      this.thirdPartyData = this.origThirdPartyData;\r\n      setTimeout(() => {\r\n        this.$emit(\"closeSystemConfig\");\r\n      }, 500);\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n.about-data-modal {\r\n  .vertical-center-modal {\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n\r\n    .ivu-modal {\r\n      top: 0;\r\n    }\r\n  }\r\n\r\n  .ivu-modal-close {\r\n    top: 15px;\r\n    right: 20px;\r\n  }\r\n\r\n  .content {\r\n    padding: 4px 14px;\r\n    overflow-y: auto;\r\n    //height: 700px;\r\n    //height: 400px;\r\n  }\r\n\r\n  @media only screen and (min-width: 1200px) {\r\n    .ivu-modal-close {\r\n      top: 18px;\r\n    }\r\n  }\r\n\r\n  .ivu-modal-header {\r\n    padding: 15px 20px;\r\n    border-bottom: 1px solid #f7f7f7;\r\n\r\n    .header {\r\n      overflow: hidden;\r\n\r\n      .left {\r\n        float: left;\r\n        width: 6px;\r\n        height: 24px;\r\n        margin-right: 8px;\r\n        background-color: #0ac7c0;\r\n        border-radius: 3px;\r\n      }\r\n\r\n      .title {\r\n        float: left;\r\n        height: 24px;\r\n        line-height: 24px;\r\n        font-size: 14px;\r\n        font-weight: bold;\r\n        color: #999;\r\n\r\n        span {\r\n          color: #333;\r\n        }\r\n      }\r\n\r\n      .es-btn {\r\n        float: right;\r\n        margin-right: 35px;\r\n      }\r\n\r\n      @media only screen and (min-width: 1200px) {\r\n        .left {\r\n          height: 30px;\r\n        }\r\n\r\n        .title {\r\n          height: 30px;\r\n          line-height: 30px;\r\n          font-size: 18px;\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .ivu-modal-body {\r\n    .content {\r\n      padding: 4px 14px;\r\n      overflow-y: auto;\r\n\r\n      .row {\r\n        margin-bottom: 20px;\r\n\r\n        .item {\r\n          padding: 15px;\r\n          border: 1px solid #f7f7f7;\r\n          border-radius: 4px;\r\n\r\n          label {\r\n            color: #999;\r\n            font-weight: bold;\r\n          }\r\n\r\n          p {\r\n            margin-top: 10px;\r\n            color: #333;\r\n          }\r\n        }\r\n      }\r\n\r\n      .form-item {\r\n        padding: 15px 15px 22px;\r\n        margin-bottom: 16px;\r\n        border-radius: 4px;\r\n        background-color: #f7f7f7;\r\n      }\r\n    }\r\n  }\r\n\r\n  .ivu-modal-footer {\r\n    height: 50px;\r\n    border-top: none;\r\n    padding: 0;\r\n  }\r\n}\r\n</style>\r\n\r\n<style lang=\"less\" scoped>\r\n/deep/ .ivu-btn-primary {\r\n  color: #fff;\r\n  background-color: #31babb;\r\n  border-color: #31babb;\r\n}\r\n\r\n/deep/ .ivu-btn-primary:hover {\r\n  color: #fff !important;\r\n  border-color: #31babb;\r\n}\r\n\r\n/deep/ .ivu-switch-checked {\r\n  background-color: #31babb;\r\n  border-color: #31babb;\r\n}\r\n\r\n.musicIcon {\r\n  vertical-align: middle;\r\n  margin-left: 5px;\r\n  color: #31babb;\r\n  cursor: pointer;\r\n}\r\n\r\n/deep/ .ivu-tag .ivu-icon-ios-close {\r\n  display: none;\r\n}\r\n\r\n/deep/ .ivu-select-multiple .ivu-tag span:not(.ivu-select-max-tag) {\r\n  margin-right: 0px;\r\n}\r\n</style>\r\n"], "sourceRoot": "src/components/administrative/systemManagement"}]}